import { useState, useEffect } from 'react'
import { imageGenerationService } from '@/api/services/image-generation'

interface HealthStatus {
  apiV2Available: boolean
  status: string
  lastChecked: Date | null
  error?: string
}

/**
 * 图片生成服务健康状态 Hook
 * 定期检查 API v2 服务状态
 */
export function useImageGenerationHealth(checkInterval = 60000) { // 默认1分钟检查一次
  const [health, setHealth] = useState<HealthStatus>({
    apiV2Available: true, // 默认假设可用
    status: 'unknown',
    lastChecked: null
  })

  const checkHealth = async () => {
    try {
      const healthResult = await imageGenerationService.checkApiV2Health()
      setHealth({
        apiV2Available: healthResult.available,
        status: healthResult.status,
        lastChecked: new Date(),
        error: healthResult.error
      })
    } catch (error) {
      setHealth(prev => ({
        ...prev,
        apiV2Available: false,
        status: 'error',
        lastChecked: new Date(),
        error: error instanceof Error ? error.message : String(error)
      }))
    }
  }

  useEffect(() => {
    // 立即检查一次
    checkHealth()

    // 设置定期检查
    const interval = setInterval(checkHealth, checkInterval)

    return () => clearInterval(interval)
  }, [checkInterval])

  return {
    health,
    checkHealth,
    refresh: checkHealth
  }
}
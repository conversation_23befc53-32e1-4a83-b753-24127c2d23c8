import { useEffect, type ReactNode } from 'react'
import { useMobileViewport } from '@/hooks/use-mobile-viewport'
import { Capacitor } from '@capacitor/core'
import { StatusBar, Style } from '@capacitor/status-bar'

interface MobileViewportManagerProps {
  children: ReactNode
}

/**
 * 移动视口管理组件
 * 处理移动设备的视口适配、状态栏和触摸手势设置
 */
export function MobileViewportManager({ children }: MobileViewportManagerProps) {
  // 使用移动端视口适配钩子
  const viewport = useMobileViewport()

  useEffect(() => {
    // 添加视口尺寸到CSS变量，供全局样式使用
    document.documentElement.style.setProperty('--viewport-width', `${viewport.width}px`)
    document.documentElement.style.setProperty('--viewport-height', `${viewport.height}px`)

    // 如果是移动设备，添加特定的类名
    if (viewport.isNative) {
      document.body.classList.add('capacitor-app')
      document.body.classList.add(`platform-${viewport.platform}`)
    }

    // 设置状态栏
    const setupStatusBar = async () => {
      if (Capacitor.isNativePlatform()) {
        try {
          if (Capacitor.getPlatform() === 'android') {
            // Android特定设置
            await StatusBar.setOverlaysWebView({ overlay: false })
            await StatusBar.setStyle({ style: Style.Dark })
            // 明确设置状态栏背景色
            await StatusBar.setBackgroundColor({ color: '#0B0A1E' })
            console.log('安卓状态栏已设置，背景色: #0B0A1E')
          } else if (Capacitor.getPlatform() === 'ios') {
            // iOS特定设置
            await StatusBar.setStyle({ style: Style.Dark })
            console.log('iOS状态栏已设置为深色模式')
          }
        } catch (error) {
          console.error('设置状态栏失败:', error)
        }
      }
    }

    setupStatusBar()

    // 仅防止多点触控缩放，不阻止正常滚动
    const preventPinchZoom = (e: TouchEvent) => {
      // 只有当有多个触摸点时才阻止默认行为（防止缩放）
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }

    // 防止双击缩放，但允许单击和滚动事件
    const preventDoubleTapZoom = (() => {
      let lastTap = 0
      return (e: TouchEvent) => {
        // 只在非输入元素上防止双击缩放
        const target = e.target as HTMLElement
        if (
          target.tagName !== 'INPUT' &&
          target.tagName !== 'TEXTAREA' &&
          !target.isContentEditable
        ) {
          const now = Date.now()
          const DOUBLE_TAP_THRESHOLD = 300
          if (now - lastTap < DOUBLE_TAP_THRESHOLD) {
            e.preventDefault()
          }
          lastTap = now
        }
      }
    })()

    // 注意：touchmove事件使用passive=false可能会影响滚动性能
    // 只在非输入元素上阻止多点触控
    document.addEventListener('touchmove', preventPinchZoom, {
      passive: false
    })
    document.addEventListener('touchend', preventDoubleTapZoom, {
      passive: false
    })

    return () => {
      document.removeEventListener('touchmove', preventPinchZoom)
      document.removeEventListener('touchend', preventDoubleTapZoom)
    }
  }, [viewport])

  return <>{children}</>
}

// 换脸队列任务类型
export interface FaceSwapTask {
  taskId: string;
  messageId: string;
  chatId: string;
  userId: string;
  inputImageUrl: string; // InstantID 生成的图片URL
  swapImageUrl: string; // 角色头像URL (characterAvatar)
  timestamp: number;
  metadata?: {
    mediaGenerationId?: string; // 媒体生成记录ID
    pointsUsed?: number; // 消费的积分
    originalPrompt?: string; // 原始提示词
    characterName?: string; // 角色名称
    [key: string]: any;
  };
}

// 换脸生成结果类型
export interface FaceSwapResult {
  taskId: string;
  success: boolean;
  imageUrl?: string;
  error?: string;
  processingTime: number;
  metadata?: {
    replicatePredictionId?: string;
    finalImageUrl?: string;
  };
}

// 换脸生成状态类型
export type FaceSwapStatus = 'pending' | 'starting' | 'processing' | 'completed' | 'failed';

// 换脸生成进度更新类型
export interface FaceSwapProgress {
  messageId: string;
  status: FaceSwapStatus;
  progress: number;
  message: string;
  timestamp: string;
}

// Replicate API 相关类型
export interface ReplicatePredictionRequest {
  version: string;
  input: {
    swap_image: string; // 角色头像URL
    input_image: string; // 要换脸的图片URL
  };
}

export interface ReplicatePredictionResponse {
  id: string;
  status: string;
  input: {
    swap_image: string;
    input_image: string;
  };
  output?: string | string[];
  error?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

export interface ReplicatePredictionStatus {
  id: string;
  status: 'starting' | 'processing' | 'succeeded' | 'failed' | 'canceled';
  input: {
    swap_image: string;
    input_image: string;
  };
  output?: string | string[];
  error?: string;
  logs?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
}

// 换脸生成请求类型
export interface FaceSwapRequest {
  messageId: string;
  chatId: string;
  inputImageUrl: string;
  swapImageUrl: string;
  metadata?: {
    originalPrompt?: string;
    characterName?: string;
  };
}

// 换脸生成响应类型
export interface FaceSwapResponse {
  success: boolean;
  taskId: string;
  message?: string;
  error?: string;
}

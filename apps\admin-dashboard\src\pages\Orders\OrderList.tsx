import React, { useState, useEffect, useCallback } from 'react'
import { 
  Table, 
  Card, 
  Input, 
  Button, 
  Space, 
  Tag, 
  Select, 
  DatePicker, 
  message,
  Typography,
  Tooltip,
  Statistic,
  Row,
  Col,
  Modal
} from 'antd'
import { 
  SearchOutlined, 
  ExportOutlined, 
  EyeOutlined,
  DollarOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { PaymentOrder, OrderListParams, OrderDetailResponse, OrderStatsResponse } from '@/services/orders'
import { orderService } from '@/services/orders'
import { ORDER_STATUS_OPTIONS, TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { Title } = Typography

const OrderList: React.FC = () => {
  const [orders, setOrders] = useState<PaymentOrder[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [stats, setStats] = useState<OrderStatsResponse | null>(null)
  const [selectedOrder, setSelectedOrder] = useState<OrderDetailResponse | null>(null)
  const [orderDetailVisible, setOrderDetailVisible] = useState(false)
  const [detailLoading, setDetailLoading] = useState(false)
  
  // 搜索条件
  const [searchParams, setSearchParams] = useState<OrderListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  const loadOrders = useCallback(async () => {
    try {
      setLoading(true)
      
      const params: OrderListParams = {
        page: currentPage,
        pageSize,
        ...searchParams
      }
      
      const response = await orderService.getOrders(params)
      
      if (response.success && response.data) {
        setOrders(response.data.data || [])
        setTotal(response.data.total || 0)
      } else {
        message.error(response.message || '获取订单列表失败')
      }
    } catch (error) {
      console.error('获取订单列表失败:', error)
      message.error('获取订单列表失败')
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchParams])

  const loadStats = async () => {
    try {
      const response = await orderService.getOrderStats()
      if (response.success && response.data) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('获取订单统计失败:', error)
    }
  }

  useEffect(() => {
    loadOrders()
    loadStats()
  }, [loadOrders])

  const handleSearch = () => {
    setCurrentPage(1)
    // loadOrders will be called automatically due to useEffect dependency
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
    // loadOrders will be called automatically due to useEffect dependency
  }

  const getStatusColor = (status: string) => {
    const option = ORDER_STATUS_OPTIONS.find(o => o.value === status)
    return option?.color || 'default'
  }

  const getStatusText = (status: string) => {
    const option = ORDER_STATUS_OPTIONS.find(o => o.value === status)
    return option?.label || status
  }

  const handleViewDetail = async (order: PaymentOrder) => {
    setDetailLoading(true)
    setOrderDetailVisible(true)
    setSelectedOrder(null)
    
    try {
      const response = await orderService.getOrderDetail(order.id)
      if (response.success && response.data) {
        setSelectedOrder(response.data)
      } else {
        message.error('获取订单详情失败')
        setOrderDetailVisible(false)
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
      message.error('获取订单详情失败')
      setOrderDetailVisible(false)
    } finally {
      setDetailLoading(false)
    }
  }

  const handleExport = async () => {
    try {
      const blob = await orderService.exportOrders(searchParams)
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `orders_${dayjs().format('YYYY-MM-DD')}.xlsx`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
      message.success('导出成功')
    } catch (error) {
      console.error('导出失败:', error)
      message.error('导出失败')
    }
  }

  const columns: ColumnsType<PaymentOrder> = [
    {
      title: '订单号',
      dataIndex: 'id',
      width: 180,
      render: (id) => (
        <span style={{ fontFamily: 'monospace', fontSize: '12px' }}>
          {id}
        </span>
      ),
    },
    {
      title: '用户邮箱',
      dataIndex: 'userEmail',
      width: 200,
      render: (userEmail) => (
        <span style={{ fontSize: '12px' }}>
          {userEmail || '未知用户'}
        </span>
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'itemName',
      width: 150,
      render: (itemName) => (
        <span style={{ fontSize: '12px' }}>
          {itemName || '未知商品'}
        </span>
      ),
    },
    {
      title: '订单类型',
      dataIndex: 'type',
      render: (type) => (
        <Tag color={type === 'membership' ? 'gold' : 'blue'}>
          {type === 'membership' ? '会员套餐' : '积分包'}
        </Tag>
      ),
    },
    {
      title: '订单金额',
      dataIndex: 'amount',
      render: (amount) => (
        <span style={{ color: '#f50', fontWeight: 500 }}>
          ¥{amount.toFixed(2)}
        </span>
      ),
    },
    {
      title: '支付方式',
      dataIndex: 'paymentMethod',
      render: (method) => (
        <Tag color={method === 'alipay' ? 'blue' : 'green'}>
          {method === 'alipay' ? '支付宝' : '微信支付'}
        </Tag>
      ),
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {getStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        订单管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日订单"
              value={stats?.todayOrders || 0}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已完成订单"
              value={stats?.completedOrders || 0}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日收入"
              value={stats?.todayRevenue || 0}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={total}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索订单号或用户邮箱"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />
          
          <Select
            placeholder="订单类型"
            style={{ width: 120 }}
            allowClear
            value={searchParams.type}
            onChange={(value) => setSearchParams({ ...searchParams, type: value })}
          >
            <Select.Option value="membership">会员套餐</Select.Option>
            <Select.Option value="points">积分包</Select.Option>
          </Select>

          <Select
            placeholder="订单状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.status}
            onChange={(value) => setSearchParams({ ...searchParams, status: value })}
          >
            {ORDER_STATUS_OPTIONS.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>

          <Select
            placeholder="支付方式"
            style={{ width: 120 }}
            allowClear
            value={searchParams.paymentMethod}
            onChange={(value) => setSearchParams({ ...searchParams, paymentMethod: value })}
          >
            <Select.Option value="alipay">支付宝</Select.Option>
            <Select.Option value="wechat">微信支付</Select.Option>
          </Select>

          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={(dates) => {
              if (dates) {
                setSearchParams({
                  ...searchParams,
                  startDate: dates[0]?.toISOString(),
                  endDate: dates[1]?.toISOString()
                })
              } else {
                setSearchParams({
                  ...searchParams,
                  startDate: undefined,
                  endDate: undefined
                })
              }
            }}
          />

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          
          <Button onClick={handleReset}>
            重置
          </Button>
          
          <Button icon={<ExportOutlined />} onClick={handleExport}>
            导出
          </Button>
        </Space>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 订单详情弹窗 */}
      <Modal
        title={<div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
          <EyeOutlined />
          <span>订单详情</span>
        </div>}
        open={orderDetailVisible}
        onCancel={() => {
          setOrderDetailVisible(false)
          setSelectedOrder(null)
        }}
        footer={[
          <Button key="close" onClick={() => {
            setOrderDetailVisible(false)
            setSelectedOrder(null)
          }}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {detailLoading ? (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            padding: '40px',
            color: '#999'
          }}>
            加载中...
          </div>
        ) : selectedOrder ? (
          <div style={{ padding: '16px 0' }}>
            {/* 订单基本信息 */}
            <Card 
              title="订单信息" 
              size="small" 
              style={{ marginBottom: 16 }}
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <div><strong>订单号：</strong></div>
                  <code style={{ 
                    fontSize: '12px', 
                    padding: '4px 8px',
                    backgroundColor: '#f5f5f5',
                    borderRadius: 4
                  }}>
                    {selectedOrder.id}
                  </code>
                </Col>
                
                <Col span={12}>
                  <div><strong>用户邮箱：</strong></div>
                  <span>{selectedOrder.userEmail}</span>
                </Col>
                
                <Col span={12}>
                  <div><strong>订单类型：</strong></div>
                  <Tag color={selectedOrder.type === 'membership' ? 'gold' : 'blue'}>
                    {selectedOrder.type === 'membership' ? '会员套餐' : '积分包'}
                  </Tag>
                </Col>
                
                <Col span={12}>
                  <div><strong>订单金额：</strong></div>
                  <span style={{ color: '#f50', fontWeight: 500 }}>
                    ¥{selectedOrder.amount.toFixed(2)}
                  </span>
                </Col>
                
                <Col span={12}>
                  <div><strong>支付方式：</strong></div>
                  <Tag color={selectedOrder.paymentMethod === 'alipay' ? 'blue' : 'green'}>
                    {selectedOrder.paymentMethod === 'alipay' ? '支付宝' : '微信支付'}
                  </Tag>
                </Col>
                
                <Col span={12}>
                  <div><strong>订单状态：</strong></div>
                  <Tag color={getStatusColor(selectedOrder.status)}>
                    {getStatusText(selectedOrder.status)}
                  </Tag>
                </Col>
                
                <Col span={12}>
                  <div><strong>创建时间：</strong></div>
                  <span>{dayjs(selectedOrder.createdAt).format('YYYY-MM-DD HH:mm:ss')}</span>
                </Col>
                
                <Col span={12}>
                  <div><strong>支付时间：</strong></div>
                  <span>{selectedOrder.paidAt ? 
                    dayjs(selectedOrder.paidAt).format('YYYY-MM-DD HH:mm:ss') : 
                    '未支付'
                  }</span>
                </Col>
                
                {selectedOrder.tradeNo && (
                  <Col span={24}>
                    <div><strong>交易号：</strong></div>
                    <code style={{ 
                      fontSize: '12px', 
                      padding: '4px 8px',
                      backgroundColor: '#f5f5f5',
                      borderRadius: 4
                    }}>
                      {selectedOrder.tradeNo}
                    </code>
                  </Col>
                )}
                
                {selectedOrder.description && (
                  <Col span={24}>
                    <div><strong>订单描述：</strong></div>
                    <span>{selectedOrder.description}</span>
                  </Col>
                )}
              </Row>
            </Card>
            
            {/* 商品信息 */}
            {selectedOrder.itemInfo && (
              <Card 
                title="商品信息" 
                size="small"
              >
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div><strong>商品名称：</strong></div>
                    <span>{selectedOrder.itemInfo.name}</span>
                  </Col>
                  
                  <Col span={12}>
                    <div><strong>商品价格：</strong></div>
                    <span style={{ color: '#f50', fontWeight: 500 }}>
                      ¥{selectedOrder.itemInfo.price.toFixed(2)}
                    </span>
                  </Col>
                  
                  {selectedOrder.itemInfo.description && (
                    <Col span={24}>
                      <div><strong>商品描述：</strong></div>
                      <span>{selectedOrder.itemInfo.description}</span>
                    </Col>
                  )}
                  
                  {selectedOrder.itemInfo.type === 'membership' && (
                    <>
                      <Col span={12}>
                        <div><strong>有效期：</strong></div>
                        <span>{selectedOrder.itemInfo.durationDays}天</span>
                      </Col>
                      
                      <Col span={12}>
                        <div><strong>包含积分：</strong></div>
                        <span>{selectedOrder.itemInfo.pointsIncluded}</span>
                      </Col>
                    </>
                  )}
                  
                  {selectedOrder.itemInfo.type === 'points' && (
                    <>
                      <Col span={12}>
                        <div><strong>基础积分：</strong></div>
                        <span>{selectedOrder.itemInfo.points}</span>
                      </Col>
                      
                      <Col span={12}>
                        <div><strong>赠送积分：</strong></div>
                        <span>{selectedOrder.itemInfo.bonusPoints || 0}</span>
                      </Col>
                    </>
                  )}
                </Row>
              </Card>
            )}
          </div>
        ) : (
          <div style={{ 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            padding: '40px',
            color: '#999'
          }}>
            加载失败，请重试
          </div>
        )}
      </Modal>
    </div>
  )
}

export default OrderList
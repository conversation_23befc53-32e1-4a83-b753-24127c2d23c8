import { useEffect, useRef, useCallback, useState } from 'react'
import { useAuth, getAccessToken } from '@/contexts/auth-context'

// SSE事件类型定义
interface SSEEvent {
  id?: string
  event?: string
  data?: any
  retry?: number
}

// 媒体完成事件
interface MediaCompletionEvent {
  type: 'media_completed'
  messageId: string
  chatId: string
  mediaType: 'audio' | 'image' | 'video'
  mediaUrl: string
  status: 'completed' | 'failed'
  errorMessage?: string
  metadata?: any
}

// 媒体进度事件
interface MediaProgressEvent {
  type: 'media_progress'
  messageId: string
  chatId: string
  mediaType: 'audio' | 'image' | 'video'
  status: 'starting' | 'processing' | 'completed' | 'failed'
  progress: number
  message: string
  errorMessage?: string
  timestamp: string
}

// SSE客户端配置
interface SSEClientConfig {
  enabled?: boolean
  autoReconnect?: boolean
  maxReconnectAttempts?: number
  reconnectInterval?: number
  heartbeatTimeout?: number
}

// 事件处理器
interface SSEEventHandlers {
  onMediaCompletion?: (event: MediaCompletionEvent) => void
  onMediaProgress?: (event: MediaProgressEvent) => void
  onConnectionChange?: (connected: boolean) => void
  onError?: (error: Error) => void
  onCustomEvent?: (eventType: string, data: any) => void
}

// SSE连接状态
interface SSEConnectionState {
  connected: boolean
  connecting: boolean
  reconnectAttempts: number
  lastError?: string
  connectionId?: string
}

/**
 * SSE客户端Hook
 * 管理与后端的实时事件连接
 */
export function useSSEClient(config: SSEClientConfig = {}, eventHandlers: SSEEventHandlers = {}) {
  const { user } = useAuth()
  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectTimeoutRef = useRef<number | null>(null)
  const heartbeatTimeoutRef = useRef<number | null>(null)

  // 默认配置
  const {
    enabled = true,
    autoReconnect = true,
    maxReconnectAttempts = 5,
    reconnectInterval = 3000,
    heartbeatTimeout = 60000 // 60秒心跳超时
  } = config

  // 连接状态
  const [connectionState, setConnectionState] = useState<SSEConnectionState>({
    connected: false,
    connecting: false,
    reconnectAttempts: 0
  })

  // SSE端点URL
  const sseUrl = `${import.meta.env.VITE_APP_API_URL || ''}/api/sse/events`

  /**
   * 清理重连定时器
   */
  const clearReconnectTimeout = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
      reconnectTimeoutRef.current = null
    }
  }, [])

  /**
   * 清理心跳定时器
   */
  const clearHeartbeatTimeout = useCallback(() => {
    if (heartbeatTimeoutRef.current) {
      clearTimeout(heartbeatTimeoutRef.current)
      heartbeatTimeoutRef.current = null
    }
  }, [])

  /**
   * 重置心跳定时器
   */
  const resetHeartbeatTimeout = useCallback(() => {
    clearHeartbeatTimeout()

    if (heartbeatTimeout > 0) {
      heartbeatTimeoutRef.current = window.setTimeout(() => {
        console.warn('📡 [SSE] 心跳超时，断开连接')
        disconnect()

        if (autoReconnect && connectionState.reconnectAttempts < maxReconnectAttempts) {
          scheduleReconnect()
        }
      }, heartbeatTimeout)
    }
  }, [heartbeatTimeout, autoReconnect, connectionState.reconnectAttempts, maxReconnectAttempts])

  /**
   * 处理SSE事件
   */
  const handleSSEEvent = useCallback(
    (event: MessageEvent) => {
      try {
        const eventData = JSON.parse(event.data)

        // 重置心跳定时器（收到任何事件都表示连接正常）
        resetHeartbeatTimeout()

        console.log('📡 [SSE] 收到事件:', event.type, eventData)

        switch (event.type) {
          case 'connected':
            setConnectionState(prev => ({
              ...prev,
              connected: true,
              connecting: false,
              reconnectAttempts: 0,
              connectionId: eventData.connectionId
            }))
            eventHandlers.onConnectionChange?.(true)
            break

          case 'media_completed':
            eventHandlers.onMediaCompletion?.(eventData as MediaCompletionEvent)
            break

          case 'media_progress':
            eventHandlers.onMediaProgress?.(eventData as MediaProgressEvent)
            break

          case 'heartbeat':
            // 心跳事件，仅用于保持连接
            console.log('📡 [SSE] 收到心跳:', eventData.timestamp)
            break

          default:
            // 自定义事件
            eventHandlers.onCustomEvent?.(event.type, eventData)
            break
        }
      } catch (error) {
        console.error('📡 [SSE] 解析事件数据失败:', error)
        eventHandlers.onError?.(error as Error)
      }
    },
    [eventHandlers, resetHeartbeatTimeout]
  )

  /**
   * 处理连接错误
   */
  const handleConnectionError = useCallback(
    (error: Event) => {
      console.error('📡 [SSE] 连接错误:', JSON.stringify(error))

      setConnectionState(prev => ({
        ...prev,
        connected: false,
        connecting: false,
        lastError: '连接错误'
      }))

      eventHandlers.onConnectionChange?.(false)
      eventHandlers.onError?.(new Error('SSE连接错误'))

      clearHeartbeatTimeout()
    },
    [eventHandlers, clearHeartbeatTimeout]
  )

  /**
   * 处理连接关闭
   */
  const handleConnectionClose = useCallback(() => {
    console.log('📡 [SSE] 连接已关闭')

    setConnectionState(prev => ({
      ...prev,
      connected: false,
      connecting: false
    }))

    eventHandlers.onConnectionChange?.(false)
    clearHeartbeatTimeout()
  }, [eventHandlers, clearHeartbeatTimeout])

  /**
   * 安排重连
   */
  const scheduleReconnect = useCallback(() => {
    if (!autoReconnect) return

    setConnectionState(prev => {
      if (prev.reconnectAttempts >= maxReconnectAttempts) {
        console.error('📡 [SSE] 重连次数已达上限')
        eventHandlers.onError?.(new Error('重连失败，已达最大重试次数'))
        return prev
      }

      const nextAttempt = prev.reconnectAttempts + 1
      const delay = reconnectInterval * Math.pow(2, Math.min(nextAttempt - 1, 3)) // 指数退避，最大8倍延迟

      console.log(`📡 [SSE] 安排第${nextAttempt}次重连，延迟${delay}ms`)

      clearReconnectTimeout()
      reconnectTimeoutRef.current = window.setTimeout(() => {
        connect()
      }, delay)

      return {
        ...prev,
        reconnectAttempts: nextAttempt
      }
    })
  }, [autoReconnect, maxReconnectAttempts, reconnectInterval, clearReconnectTimeout])

  /**
   * 建立SSE连接
   */
  const connect = useCallback(() => {
    const token = getAccessToken()

    if (!user || !token || !enabled) {
      console.log('📡 [SSE] 跳过连接：用户未认证或未启用')
      return
    }

    // 使用 ref 读取最新状态，避免依赖数组包含状态
    setConnectionState(prev => {
      if (prev.connected || prev.connecting) {
        console.log('📡 [SSE] 跳过连接：已连接或正在连接')
        return prev
      }

      console.log('📡 [SSE] 开始建立连接...')

      try {
        // 🔧 修复：EventSource不支持自定义头部，通过URL查询参数传递token
        // 后端已修改为支持查询参数认证
        const eventSource = new EventSource(`${sseUrl}?token=${token}`, {
          withCredentials: true
        })

        eventSourceRef.current = eventSource

        // 通用消息处理器
        eventSource.onmessage = handleSSEEvent

        // 连接打开
        eventSource.onopen = () => {
          console.log('📡 [SSE] 连接已建立')
          resetHeartbeatTimeout()
        }

        // 连接错误
        eventSource.onerror = error => {
          handleConnectionError(error)

          // EventSource会自动重连，但我们需要控制重连逻辑
          if (eventSource.readyState === EventSource.CLOSED) {
            handleConnectionClose()

            // 使用当前状态进行重连判断
            setConnectionState(currentState => {
              if (autoReconnect && currentState.reconnectAttempts < maxReconnectAttempts) {
                scheduleReconnect()
              }
              return currentState
            })
          }
        }

        // 监听特定事件类型
        eventSource.addEventListener('connected', handleSSEEvent)
        eventSource.addEventListener('media_completed', handleSSEEvent)
        eventSource.addEventListener('media_progress', handleSSEEvent)
        eventSource.addEventListener('heartbeat', handleSSEEvent)
      } catch (error) {
        console.error('📡 [SSE] 创建连接失败:', error)
        // 在错误处理中使用内联的错误处理，避免依赖 eventHandlers
        setTimeout(() => {
          // 延迟调用，确保状态已更新
          if (eventHandlers.onError) {
            eventHandlers.onError(error as Error)
          }
        }, 0)

        return {
          ...prev,
          connecting: false,
          lastError: '创建连接失败'
        }
      }

      return {
        ...prev,
        connecting: true,
        lastError: undefined
      }
    })
  }, [
    user,
    enabled,
    sseUrl,
    handleSSEEvent,
    handleConnectionError,
    handleConnectionClose,
    resetHeartbeatTimeout,
    autoReconnect,
    maxReconnectAttempts,
    scheduleReconnect
    // 🔧 移除 connectionState 相关依赖和 eventHandlers
  ])

  /**
   * 断开SSE连接
   */
  const disconnect = useCallback(() => {
    console.log('📡 [SSE] 开始断开连接')

    clearReconnectTimeout()
    clearHeartbeatTimeout()

    if (eventSourceRef.current) {
      console.log('📡 [SSE] 关闭EventSource连接')

      // 🔧 移除所有事件监听器
      const eventSource = eventSourceRef.current
      eventSource.onmessage = null
      eventSource.onopen = null
      eventSource.onerror = null

      // 移除自定义事件监听器
      eventSource.removeEventListener('connected', handleSSEEvent)
      eventSource.removeEventListener('media_completed', handleSSEEvent)
      eventSource.removeEventListener('media_progress', handleSSEEvent)
      eventSource.removeEventListener('heartbeat', handleSSEEvent)

      // 关闭连接
      eventSource.close()
      eventSourceRef.current = null

      console.log('📡 [SSE] EventSource已关闭并清理')
    }

    setConnectionState({
      connected: false,
      connecting: false,
      reconnectAttempts: 0
    })

    eventHandlers.onConnectionChange?.(false)
    console.log('📡 [SSE] 断开连接完成')
  }, [clearReconnectTimeout, clearHeartbeatTimeout, handleSSEEvent, eventHandlers])

  /**
   * 手动重连
   */
  const reconnect = useCallback(() => {
    console.log('📡 [SSE] 手动重连')
    disconnect()
    setTimeout(() => {
      setConnectionState(prev => ({ ...prev, reconnectAttempts: 0 }))
      connect()
    }, 100)
  }, [disconnect, connect])

  // 🔧 使用稳定的引用，避免重复连接
  const stableConnect = useRef(connect)
  const stableDisconnect = useRef(disconnect)

  // 更新稳定引用
  useEffect(() => {
    stableConnect.current = connect
    stableDisconnect.current = disconnect
  }, [connect, disconnect])

  // 🔧 优化自动连接逻辑，避免重复连接
  useEffect(() => {
    const token = getAccessToken()

    if (enabled && user && token) {
      console.log('📡 [SSE] 条件满足，建立连接')
      stableConnect.current()
    } else {
      console.log('📡 [SSE] 条件不满足，断开连接')
      stableDisconnect.current()
    }

    return () => {
      console.log('📡 [SSE] useEffect清理，断开连接')
      stableDisconnect.current()
    }
  }, [enabled, user?.id]) // 🔧 只依赖用户ID，不依赖整个user对象

  // 🔧 优化页面可见性处理，移除不稳定的状态依赖
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('📡 [SSE] 页面隐藏，保持连接')
        // 页面隐藏时不断开连接，但可以考虑降低心跳频率
      } else {
        console.log('📡 [SSE] 页面可见，检查连接状态')
        const token = getAccessToken()

        // 🔧 使用ref获取最新状态，避免依赖state
        if (enabled && user && token) {
          // 检查是否需要重新连接（这里简化处理）
          setTimeout(() => {
            // 延迟检查，避免重复连接
            if (
              !eventSourceRef.current ||
              eventSourceRef.current.readyState === EventSource.CLOSED
            ) {
              console.log('📡 [SSE] 页面可见时重新连接')
              stableConnect.current()
            }
          }, 100)
        }
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [enabled, user?.id]) // 🔧 只依赖基本属性

  // 🔧 页面卸载时强制断开连接
  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log('📡 [SSE] 页面卸载，强制断开连接')
      stableDisconnect.current()
    }

    const handlePageHide = () => {
      console.log('📡 [SSE] 页面隐藏，强制断开连接')
      stableDisconnect.current()
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('pagehide', handlePageHide)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('pagehide', handlePageHide)
      // 组件卸载时也断开连接
      stableDisconnect.current()
    }
  }, [])

  return {
    connectionState,
    connect,
    disconnect,
    reconnect,

    // 辅助方法
    isConnected: connectionState.connected,
    isConnecting: connectionState.connecting,
    canReconnect: connectionState.reconnectAttempts < maxReconnectAttempts
  }
}

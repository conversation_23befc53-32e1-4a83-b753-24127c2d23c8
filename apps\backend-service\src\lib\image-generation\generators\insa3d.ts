import type { Env } from '@/types/env'
import type {
  GenerateImageRequest,
  ImageGenerationTask,
  Insa3DRunTaskResponse,
  Insa3DTaskStatusResponse,
  TaskStatus
} from '@/types/image-generation'
import { downloadAndUploadImage } from '../upload'

type Insa3DVersion = 'v2' | 'v3' | 'v4'

const CONFIG = {
  v2: {
    endpoint: 'kf1frub667e411',
    token: 'olho4vqVN4MOLs3RoRDDWQ'
  },
  v3: {
    endpoint: 'ozinqcn3qpfsu4',
    token: 'sGHj0uCtD5P8A_9MLX3eVw'
  },
  v4: {
    endpoint: 'nizuhoxrees7jv',
    token: 'sEYZ9719krdppRaYSoTxnw'
  }
}

/**
 * Insa3D图片生成器
 * 特点：异步生成，需要轮询任务状态
 * 使用原来Next.js backend的API格式
 */
export class Insa3DGenerator {
  private baseUrl: string
  private token: string
  private version: Insa3DVersion

  constructor(private env: Env, version: Insa3DVersion = 'v2') {
    const config = CONFIG[version]
    this.version = version
    this.baseUrl = `https://api.instasd.com/api_endpoints/${config.endpoint}`
    this.token = config.token

    if (!this.token) {
      throw new Error('Insa3D API密钥未配置')
    }
  }

  private getRequestBody(prompt: string, imageUrl?: string) {
    const version = this.version

    switch (version) {
      case 'v2':
        return {
          inputs: {
            '96b9fc105f305520': {
              title: 'width',
              value: 720
            },
            '577750b1e716b191': {
              title: 'height',
              value: 1280
            },
            '6307d2aa9851566f': {
              title: 'prompt',
              value: prompt
            },
            '4bee95674812740b': {
              title: 'Load Image',
              value: imageUrl
            }
          }
        }
      case 'v3':
        return {
          inputs: {
            cfe0f97f7f619003: {
              title: 'width',
              value: 720
            },
            '83fff7381441a174': {
              title: 'height',
              value: 1280
            },
            a3c313ef566759f7: {
              title: 'prompt',
              value: prompt
            }
          }
        }
      case 'v4':
        return {
          inputs: {
            acf316b99d40ca0a: {
              title: 'prompt',
              value: prompt
            },
            e66b86632f418b48: {
              title: 'seed',
              value: 4124214324244234
            },
            da9850330bee2a7b: {
              title: 'width',
              value: 1024
            },
            '144baa72b0139eb9': {
              title: 'height',
              value: 1440
            },
            a5f1cde189946aba: {
              title: 'load image',
              value: imageUrl
            }
          }
        }
    }
  }

  /**
   * 启动图片生成任务
   */
  async startGenerationTask(request: GenerateImageRequest): Promise<ImageGenerationTask> {
    const { prompt, imageUrl } = request

    try {
      // 根据版本构建不同的请求体
      let requestBody: any

      requestBody = this.getRequestBody(prompt, imageUrl)

      const response = await fetch(`${this.baseUrl}/run_task`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.token}`
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Insa3D API请求失败:', response.status, errorText)
        throw new Error(`Insa3D API请求失败: ${response.statusText}`)
      }

      // const data: Insa3DRunTaskResponse = await response.json()
      const rawText = await response.text()
      console.log('📄 [DEBUG] 原始响应:', rawText.slice(0, 300))

      let data: Insa3DRunTaskResponse
      try {
        data = JSON.parse(rawText)
      } catch (e) {
        console.error('❌ [ERROR] JSON 解析失败:', e)
        throw new Error('响应非合法 JSON')
      }

      return {
        taskId: data.task_id,
        status: 'pending',
        estimatedSteps: data.estimated_steps,
        completedSteps: 0,
        prompt: prompt,
        inputImageUrl: imageUrl
      }
    } catch (error) {
      console.error('启动Insa3D生成任务失败:', error)
      throw new Error('启动生成任务失败')
    }
  }

  /**
   * 查询任务状态
   */
  async getTaskStatus(taskId: string): Promise<ImageGenerationTask> {
    if (!taskId) {
      throw new Error('任务ID不能为空')
    }

    try {
      const response = await fetch(`${this.baseUrl}/task_status/${taskId}`, {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${this.token}`
        }
      })

      if (!response.ok) {
        console.error('Insa3D API状态查询失败:', response.status)
        throw new Error(`查询任务状态失败: ${response.statusText}`)
      }

      const data: Insa3DTaskStatusResponse = await response.json()

      // 转换状态
      let status: TaskStatus
      switch (data.status) {
        case 'PENDING':
          status = 'pending'
          break
        case 'IN_PROGRESS':
        case 'EXECUTING':
          status = 'processing'
          break
        case 'COMPLETED':
          status = 'completed'
          break
        case 'FAILED':
          status = 'failed'
          break
        default:
          status = 'pending'
      }

      // 计算进度
      let progress = 0
      if (data.estimated_steps > 0 && data.completed_steps >= 0) {
        progress = Math.round((data.completed_steps / data.estimated_steps) * 100)
        progress = Math.min(progress, status === 'completed' ? 100 : 99)
      }

      const task: ImageGenerationTask = {
        taskId: data.task_id,
        status: status,
        progress: progress,
        estimatedSteps: data.estimated_steps,
        completedSteps: data.completed_steps,
        prompt: '', // 这里需要从缓存或数据库获取
        errorMessage: data.error_message
      }

      // 如果任务完成且有图片URL，下载并上传到R2
      if (status === 'completed' && data.image_urls?.[0]) {
        try {
          task.imageUrl = await downloadAndUploadImage(this.env, data.image_urls[0], 'characters')
        } catch (error) {
          console.error('下载生成的图片失败:', error)
          // 如果上传失败，仍然返回临时URL
          task.imageUrl = data.image_urls[0]
        }
      }

      return task
    } catch (error) {
      console.error('查询Insa3D任务状态失败:', error)
      throw new Error('查询任务状态失败')
    }
  }

  /**
   * 等待任务完成（轮询）
   * @param taskId 任务ID
   * @param maxWaitTime 最大等待时间（毫秒）
   * @param pollInterval 轮询间隔（毫秒）
   */
  async waitForCompletion(
    taskId: string,
    maxWaitTime = 300000, // 5分钟
    pollInterval = 3000 // 3秒
  ): Promise<ImageGenerationTask> {
    const startTime = Date.now()

    while (Date.now() - startTime < maxWaitTime) {
      const task = await this.getTaskStatus(taskId)

      if (task.status === 'completed' || task.status === 'failed') {
        return task
      }

      // 等待下次轮询
      await new Promise(resolve => setTimeout(resolve, pollInterval))
    }

    throw new Error('任务等待超时')
  }
}

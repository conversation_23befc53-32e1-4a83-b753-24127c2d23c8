{"connection": {"title": "Conectar dispositivo", "failed": "Error de conexión del dispositivo", "disconnect_failed": "Error al desconectar dispositivo", "bluetooth_device": "Dispositivo Bluetooth"}, "connection_form": {"input_placeholder": "Ingresa el código del dispositivo", "invalid_code": "Código de dispositivo inválido, verifica e inténtalo de nuevo", "connect_failed": "Error al conectar dispositivo, inténtalo más tarde", "qr_not_supported": "El dispositivo actual no soporta escaneo de código QR", "invalid_qr_code": "El código de dispositivo escaneado es inválido", "qr_unrecognized": "No se pudo reconocer el contenido del código QR", "camera_permission_denied": "Permiso de c<PERSON><PERSON> denegado, habilita el acceso a la cámara en configuración", "scan_not_supported": "El dispositivo actual no soporta la función de escaneo de código QR", "scan_failed": "Error de escaneo, inténtalo de nuevo", "input_label": "Ingresar código del dispositivo", "input_example": "Códigos de ejemplo: 1583, 6842, 2137", "connecting": "Conectando...", "connect_button": "Conectar dispositivo", "or": "o", "scanning": "Escaneando...", "scan_button": "Escanear código QR del dispositivo", "scan_instruction": "Coloca el código QR del dispositivo frente a la cámara"}, "control": {"title": "Panel de control del dispositivo", "connected": "Conectado", "bluetooth_error": "<PERSON><PERSON><PERSON>", "bluetooth_ready": "Bluetooth listo", "bluetooth_initializing": "Inicializando Bluetooth...", "bluetooth_not_initialized": "Bluetooth no inicializado", "wait_bluetooth_init": "Espera a que se complete la inicialización de Bluetooth", "bluetooth_connection_error": "<PERSON><PERSON>r de conexión Bluetooth", "mode_not_found": "ID de modo no encontrado", "select_classic_mode": "Seleccionar modo clásico", "send_classic_mode_failed": "Error al enviar comando de modo clásico", "device_function_set": "Función del dispositivo {{functionKey}} intensidad configurada a {{statusText}} (valor: {{intensity}})", "debounce_delay": "Enviando comando Bluetooth después del retraso de rebote", "intensity_zero": "Intensidad es cero, enviando instrucción de parada", "stop_command_sent": "Comando de parada enviado", "stop_command_not_found": "Comando de parada no encontrado para la función {{functionKey}}", "intensity": "Intensidad", "off": "<PERSON><PERSON><PERSON>", "level": "Nivel {{level}}", "classic_mode": "Modo clásico", "disconnect_failed": "Error al desconectar", "disconnect_button": "Desconectar"}}
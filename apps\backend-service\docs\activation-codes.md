# 激活码系统实现文档

## 概述

激活码系统允许管理员生成激活码，用户通过激活码可以获得会员套餐或积分包。系统采用简化的冲突处理策略，支持一次性使用的激活码。

## 数据库设计

### 1. ActivationCode 表（激活码主表）

```sql
CREATE TABLE "ActivationCode" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  code VARCHAR(32) UNIQUE NOT NULL,           -- 激活码字符串
  type VARCHAR CHECK (type IN ('membership', 'points')) NOT NULL,
  
  -- 关联产品
  membership_plan_id UUID REFERENCES "MembershipPlan"(id),
  points_package_id UUID REFERENCES "PointsPackage"(id),
  
  -- 使用状态（简化为一次性使用）
  is_used BOOLEAN DEFAULT FALSE,
  used_at TIMESTAMP,
  used_by UUID REFERENCES "User"(id),
  expires_at TIMESTAMP,                       -- 过期时间
  
  -- 管理字段
  is_active BOOLEAN DEFAULT TRUE,
  description TEXT,
  batch_id VARCHAR(32),                       -- 批次ID
  created_by UUID NOT NULL REFERENCES "User"(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### 2. ActivationCodeUsage 表（使用记录表）

```sql
CREATE TABLE "ActivationCodeUsage" (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  activation_code_id UUID NOT NULL REFERENCES "ActivationCode"(id),
  user_id UUID NOT NULL REFERENCES "User"(id),
  
  -- 激活结果
  result_type VARCHAR CHECK (result_type IN (
    'membership_created', 'membership_extended', 
    'membership_upgraded', 'points_added'
  )) NOT NULL,
  result_id UUID,                             -- 结果ID（订阅ID或积分交易ID）
  
  -- 冲突处理记录
  original_membership_id UUID,                -- 原有会员订阅ID
  conflict_resolution VARCHAR CHECK (conflict_resolution IN (
    'no_conflict', 'extended', 'upgraded', 'replaced', 'queued', 'rejected'
  )),
  
  -- 审计信息
  metadata JSONB DEFAULT '{}',
  ip_address VARCHAR(45),
  user_agent TEXT,
  used_at TIMESTAMP DEFAULT NOW(),
  
  -- 防重复激活
  UNIQUE(user_id, activation_code_id)
);
```

## 业务逻辑

### 冲突处理策略（简化版）

1. **无会员用户**：直接激活
2. **有会员用户**：直接替换（用户自主选择，自负责任）
   - 激活码等级 > 当前等级 → 升级
   - 激活码等级 = 当前等级 → 延长
   - 激活码等级 < 当前等级 → 替换
3. **积分包**：直接累加，无冲突

### 激活码生成规则

- 使用 nanoid 生成 16 位字符串
- 只包含大写字母和数字
- 排除容易混淆的字符（0、O、1、I、L）
- 全局唯一性检查

## API 接口

### 用户端接口

#### 1. 验证激活码
```
GET /api/activation-codes/validate/:code
```

#### 2. 使用激活码
```
POST /api/activation-codes/use
Body: { "code": "ABCD1234EFGH5678" }
```

#### 3. 获取激活历史
```
GET /api/activation-codes/history?limit=20&offset=0
```

### 管理员接口

#### 1. 创建会员激活码
```
POST /api/activation-codes/admin/membership
Body: {
  "membershipPlanId": "uuid",
  "description": "活动奖励",
  "expiresAt": "2024-12-31T23:59:59Z",
  "count": 10
}
```

#### 2. 创建积分包激活码
```
POST /api/activation-codes/admin/points
Body: {
  "pointsPackageId": "uuid",
  "description": "新用户礼包",
  "count": 100
}
```

#### 3. 获取激活码列表
```
GET /api/activation-codes/admin/list?type=membership&isUsed=false&limit=50
```

#### 4. 获取统计信息
```
GET /api/activation-codes/admin/stats?type=membership&dateFrom=2024-01-01
```

#### 5. 禁用/启用激活码
```
POST /api/activation-codes/admin/:id/disable
POST /api/activation-codes/admin/:id/enable
```

## 核心函数

### 1. 激活码生成
```typescript
// 生成唯一激活码
generateActivationCode(): string

// 创建会员激活码
createMembershipActivationCode(env, params): Promise<ActivationCode>

// 创建积分包激活码
createPointsActivationCode(env, params): Promise<ActivationCode>

// 批量创建激活码
createActivationCodesBatch(env, params): Promise<ActivationCode[]>
```

### 2. 激活码验证和使用
```typescript
// 验证激活码有效性
validateActivationCode(env, code): Promise<ValidationResult>

// 使用激活码（主要入口）
useActivationCode(env, params): Promise<UsageResult>
```

### 3. 查询和管理
```typescript
// 获取激活码列表
getActivationCodes(env, params): Promise<ActivationCode[]>

// 获取用户激活历史
getUserActivationHistory(env, userId): Promise<ActivationCodeUsage[]>

// 获取统计信息
getActivationCodeStats(env, params): Promise<Stats>

// 禁用/启用激活码
disableActivationCode(env, codeId, adminUserId): Promise<void>
enableActivationCode(env, codeId, adminUserId): Promise<void>
```

## 使用示例

### 管理员创建激活码

```typescript
// 创建会员激活码
const membershipCode = await createMembershipActivationCode(env, {
  membershipPlanId: 'plan-uuid',
  description: '双十一活动奖励',
  expiresAt: new Date('2024-12-31'),
  createdBy: 'admin-uuid'
})

// 批量创建积分包激活码
const pointsCodes = await createActivationCodesBatch(env, {
  type: 'points',
  pointsPackageId: 'package-uuid',
  count: 100,
  description: '新用户注册礼包',
  createdBy: 'admin-uuid'
})
```

### 用户使用激活码

```typescript
// 验证激活码
const validation = await validateActivationCode(env, 'ABCD1234EFGH5678')
if (!validation.valid) {
  console.log('激活码无效:', validation.reason)
  return
}

// 使用激活码
const result = await useActivationCode(env, {
  code: 'ABCD1234EFGH5678',
  userId: 'user-uuid',
  ipAddress: '***********',
  userAgent: 'Mozilla/5.0...'
})

if (result.success) {
  console.log('激活成功:', result.message)
} else {
  console.log('激活失败:', result.message)
}
```

## 安全考虑

1. **防重复使用**：数据库唯一约束确保同一用户不能重复使用同一激活码
2. **有效期控制**：支持设置激活码过期时间
3. **状态管理**：支持禁用激活码
4. **审计日志**：记录详细的使用历史和IP地址
5. **权限控制**：管理员接口需要权限验证

## 扩展性

系统设计考虑了未来的扩展需求：

1. **批次管理**：支持批量生成和管理
2. **统计分析**：提供详细的使用统计
3. **灵活配置**：支持不同类型的激活码
4. **历史追踪**：完整的使用记录

## 测试

使用提供的测试脚本验证功能：

```bash
# 设置环境变量
export SUPABASE_URL="your-supabase-url"
export SUPABASE_ANON_KEY="your-anon-key"
export SUPABASE_SERVICE_ROLE_KEY="your-service-key"

# 运行测试
npm run test:activation-codes
```

## 注意事项

1. 激活码为一次性使用，使用后立即失效
2. 会员冲突采用直接替换策略，用户需要承担选择责任
3. 积分包激活无冲突，直接累加到用户账户
4. 管理员接口需要适当的权限控制
5. 生产环境中应该设置合理的激活码有效期

import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'
import type {
  DeviceMode,
  CreateModeRequest,
  ModePreview,
  ModeTemplate,
  DeviceModePatternStep
} from '@/types/device-modes'

// Re-export types for backward compatibility
export type { DeviceMode, CreateModeRequest, ModePreview, ModeTemplate }
export type PatternStep = DeviceModePatternStep

export class DeviceModeService {
  // 获取设备模式列表
  async getModes(params: {
    page?: number
    pageSize?: number
    keyword?: string
    isActive?: boolean
  }): Promise<ApiResponse<PaginatedResponse<DeviceMode>>> {
    return await apiService.get<PaginatedResponse<DeviceMode>>('/admin/device-modes', { params })
  }

  // 获取设备模式详情
  async getMode(id: string): Promise<ApiResponse<DeviceMode>> {
    return await apiService.get<DeviceMode>(`/admin/device-modes/${id}`)
  }

  // 创建设备模式
  async createMode(data: CreateModeRequest): Promise<ApiResponse<DeviceMode>> {
    return await apiService.post<DeviceMode>('/admin/device-modes', data)
  }

  // 更新设备模式
  async updateMode(id: string, data: Partial<CreateModeRequest>): Promise<ApiResponse<DeviceMode>> {
    return await apiService.put<DeviceMode>(`/admin/device-modes/${id}`, data)
  }

  // 删除设备模式
  async deleteMode(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/device-modes/${id}`)
  }

  // 切换模式状态
  async toggleStatus(id: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/device-modes/${id}/toggle-status`, { isActive })
  }

  // 获取模式预览
  async getModePreview(id: string): Promise<ApiResponse<ModePreview>> {
    return await apiService.get<ModePreview>(`/admin/device-modes/${id}/preview`)
  }

  // 获取模式模板
  async getTemplates(): Promise<ApiResponse<ModeTemplate[]>> {
    return await apiService.get<ModeTemplate[]>('/admin/device-modes/templates/list')
  }

}

export const modeService = new DeviceModeService()
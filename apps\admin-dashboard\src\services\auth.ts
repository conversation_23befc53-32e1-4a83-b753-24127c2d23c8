import { apiService } from './api'
import type { ApiResponse } from '@/types/api'

export interface LoginParams {
  email: string
  password: string
}

export interface LoginResponse {
  user: {
    id: string
    email: string
    isAdmin: boolean
  }
  token: string
}

export interface AdminProfile {
  id: string
  email: string
  isAdmin: boolean
  createdAt: string
}

// 认证服务
export class AuthService {
  // 管理员登录
  async login(params: LoginParams): Promise<ApiResponse<LoginResponse>> {
    const response = await apiService.post<LoginResponse>('/auth/admin/login', params)
    
    if (response.success && response.data?.token) {
      // 保存token到localStorage
      localStorage.setItem('admin_token', response.data.token)
    }
    
    return response
  }

  // 获取当前管理员信息
  async getProfile(): Promise<ApiResponse<AdminProfile>> {
    return await apiService.get<AdminProfile>('/auth/admin/profile')
  }

  // 退出登录
  async logout(): Promise<void> {
    try {
      await apiService.post('/auth/admin/logout')
    } catch (error) {
      // 即使接口调用失败，也要清除本地token
      console.error('Logout failed:', error)
    } finally {
      localStorage.removeItem('admin_token')
    }
  }

  // 检查是否已登录
  isAuthenticated(): boolean {
    return !!localStorage.getItem('admin_token')
  }

  // 获取token
  getToken(): string | null {
    return localStorage.getItem('admin_token')
  }
}

export const authService = new AuthService()
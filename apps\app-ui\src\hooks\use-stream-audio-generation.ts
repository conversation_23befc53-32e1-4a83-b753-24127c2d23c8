import { useState, useCallback, useRef } from 'react'
import { tts3Service } from '@/api/services/tts3'
import { useRoleStore } from '@/stores/role-store'
import { charactersService } from '@/api/services/characters'

export type StreamAudioStatus = 'idle' | 'generating' | 'streaming' | 'completed' | 'failed'

export interface StreamAudioState {
  status: StreamAudioStatus
  audioUrl: string | null
  error: string | null
  progress: number // 流式传输进度 0-100
}

export const useStreamAudioGeneration = () => {
  const [state, setState] = useState<StreamAudioState>({
    status: 'idle',
    audioUrl: null,
    error: null,
    progress: 0
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const audioChunksRef = useRef<Uint8Array[]>([])

  // 获取当前角色信息
  const { currentRole } = useRoleStore()

  // 重置状态
  const reset = useCallback(() => {
    // 取消正在进行的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    // 清理音频块
    audioChunksRef.current = []

    setState({
      status: 'idle',
      audioUrl: null,
      error: null,
      progress: 0
    })
  }, [])

  // 获取当前角色的声音模型ID
  const getCurrentVoice = useCallback(async (): Promise<string | undefined> => {
    if (!currentRole) {
      console.warn('当前没有选择角色，使用默认声音')
      return undefined
    }

    try {
      // UUID 格式正则，用于判断是否为自定义角色
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

      if (uuidPattern.test(currentRole.id)) {
        // 自定义角色，需要获取详细信息
        const response = await charactersService.getCharacter(currentRole.id)
        // 返回voiceModelId，如果没有则返回voice作为fallback
        return response.character.voiceModelId || response.character.voice
      } else {
        // 系统角色，使用默认声音映射
        const systemVoiceMap: Record<string, string> = {
          ruyun: 'soft',
          jinlin: 'sweet'
          // 可以添加更多系统角色的声音映射
        }
        return systemVoiceMap[currentRole.id] || 'soft'
      }
    } catch (error) {
      console.error('获取角色声音信息失败:', error)
      return undefined
    }
  }, [currentRole])

  // 流式生成音频
  const generateStreamAudio = useCallback(
    async (text: string, messageId?: string, chatId?: string, voiceOverride?: string) => {
      try {
        // 重置状态
        reset()

        // 开始生成
        setState({
          status: 'generating',
          audioUrl: null,
          error: null,
          progress: 0
        })

        // 获取声音参数
        let voiceModelId = voiceOverride
        if (!voiceModelId) {
          voiceModelId = await getCurrentVoice()
        }

        console.log('🎵 使用 TTS3 流式生成，声音模型ID:', voiceModelId)

        // 创建 AbortController
        const abortController = new AbortController()
        abortControllerRef.current = abortController

        // 更新状态为流式传输中
        setState(prev => ({
          ...prev,
          status: 'streaming'
        }))

        // 使用 TTS3 服务生成音频
        const audioUrl = await tts3Service.generateAudioBlob(
          {
            text,
            messageId,
            chatId,
            voiceModelId
          },
          // 进度回调
          (progress) => {
            setState(prev => ({
              ...prev,
              progress
            }))
          },
          // 取消信号
          abortController.signal
        )

        // 完成
        setState({
          status: 'completed',
          audioUrl,
          error: null,
          progress: 100
        })

        return audioUrl
      } catch (error) {
        console.error('TTS3 流式音频生成失败:', error)

        // 如果是用户取消，不更新为失败状态
        if (error instanceof Error && error.name === 'AbortError') {
          return null
        }

        setState({
          status: 'failed',
          audioUrl: null,
          error: error instanceof Error ? error.message : '生成音频失败',
          progress: 0
        })
        throw error
      } finally {
        abortControllerRef.current = null
      }
    },
    [getCurrentVoice, reset]
  )

  // 取消生成
  const cancelGeneration = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }
    
    setState(prev => ({
      ...prev,
      status: 'idle',
      progress: 0
    }))
  }, [])

  return {
    state,
    generateStreamAudio,
    cancelGeneration,
    reset,
    currentVoice: currentRole?.name
  }
}
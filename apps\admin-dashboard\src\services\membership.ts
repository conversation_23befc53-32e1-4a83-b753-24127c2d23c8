import { apiService } from './api'
import type { ApiResponse, PaginatedResponse, MembershipPlan, UserSubscription } from '@/types/api'

export interface MembershipPlanParams {
  name: string
  description?: string
  price: number
  durationDays: number // 天数
  pointsIncluded: number
  isActive: boolean
  sortOrder?: number
  features?: {
    maxCharacters?: number
    canCreatePublicCharacters?: boolean
    canUseCustomVoices?: boolean
    canAccessPremiumTemplates?: boolean
  }
}

export interface SubscriptionListParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: string
  planId?: string
  startDate?: string
  endDate?: string
}

// 会员管理服务
export class MembershipService {
  // 获取会员套餐列表
  async getPlans(params?: { isActive?: boolean }): Promise<ApiResponse<MembershipPlan[]>> {
    return await apiService.get<MembershipPlan[]>('/admin/membership/plans', { params })
  }

  // 创建会员套餐
  async createPlan(plan: MembershipPlanParams): Promise<ApiResponse<MembershipPlan>> {
    return await apiService.post<MembershipPlan>('/admin/membership/plans', plan)
  }

  // 更新会员套餐
  async updatePlan(id: string, plan: Partial<MembershipPlanParams>): Promise<ApiResponse<MembershipPlan>> {
    return await apiService.put<MembershipPlan>(`/admin/membership/plans/${id}`, plan)
  }

  // 删除会员套餐
  async deletePlan(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/membership/plans/${id}`)
  }

  // 获取用户订阅列表
  async getSubscriptions(params: SubscriptionListParams): Promise<ApiResponse<PaginatedResponse<UserSubscription>>> {
    return await apiService.get<PaginatedResponse<UserSubscription>>('/admin/membership/subscriptions', { params })
  }

  // 获取订阅详情
  async getSubscriptionDetail(id: string): Promise<ApiResponse<UserSubscription>> {
    return await apiService.get<UserSubscription>(`/admin/membership/subscriptions/${id}`)
  }

  // 取消用户订阅
  async cancelSubscription(id: string): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/membership/subscriptions/${id}/cancel`)
  }

  // 延长用户订阅
  async extendSubscription(id: string, days: number): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/membership/subscriptions/${id}/extend`, { days })
  }

  // 获取会员统计数据
  async getMembershipStats(): Promise<ApiResponse<{
    totalPlans: number
    activePlans: number
    totalSubscriptions: number
    activeSubscriptions: number
    monthlyRevenue: number
    todayRevenue: number
  }>> {
    return await apiService.get<any>('/admin/membership/stats')
  }
}

export const membershipService = new MembershipService()
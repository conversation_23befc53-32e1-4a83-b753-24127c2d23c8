import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  InputNumber,
  Switch,
  Image,
  Tooltip,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Badge
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  UserOutlined,
  SearchOutlined,
  ReloadOutlined,
  PictureOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import { templateService } from '@/services/template'
import type { Template, TemplateListParams, TemplateCreateParams, TemplateStats } from '@/types/template'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

const TemplateManagement: React.FC = () => {
  const [templates, setTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState<Template | null>(null)
  const [viewingTemplate, setViewingTemplate] = useState<Template | null>(null)
  const [viewModalVisible, setViewModalVisible] = useState(false)
  const [form] = Form.useForm()
  
  // 搜索条件
  const [searchParams, setSearchParams] = useState<TemplateListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState<TemplateStats>({
    totalTemplates: 0,
    publicTemplates: 0,
    privateTemplates: 0,
    activeTemplates: 0,
    premiumTemplates: 0,
    totalUsage: 0
  })

  // 分类选项
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    loadStats()
    loadCategories()
  }, [])

  useEffect(() => {
    loadTemplates()
  }, [currentPage, pageSize, searchParams])

  const loadTemplates = async () => {
    try {
      setLoading(true)
      
      const params = {
        page: currentPage,
        pageSize,
        ...searchParams
      }
      
      const response = await templateService.getTemplates(params)
      
      if (response.success && response.data) {
        setTemplates(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取模板列表失败')
      }
      setLoading(false)
    } catch (error: any) {
      console.error('获取模板列表失败:', error)
      
      // 更细致的错误处理
      if (error.code === 'TIMEOUT') {
        message.error('请求超时，请重试')
      } else if (error.code === 'UNAUTHORIZED') {
        message.error('认证失效，正在跳转到登录页...')
      } else if (error.code === 'NETWORK_ERROR') {
        message.error('网络连接失败，请检查网络')
      } else {
        message.error(error.message || '获取模板列表失败')
      }
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await templateService.getStats()
      
      if (response.success && response.data) {
        setStats(response.data)
      } else {
        console.error('获取统计数据失败:', response.message)
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error)
      // 统计数据失败不显示错误信息，避免干扰用户体验
    }
  }

  const loadCategories = async () => {
    try {
      const response = await templateService.getCategories()
      
      if (response.success && response.data) {
        setCategories(response.data)
      } else {
        console.error('获取分类失败:', response.message)
      }
    } catch (error: any) {
      console.error('获取分类失败:', error)
      // 分类获取失败也不显示错误，使用默认分类
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    setSearchParams({
      ...searchParams,
      page: 1
    })
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingTemplate(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleView = async (template: Template) => {
    try {
      setLoading(true)
      // 获取完整的模板数据
      const response = await templateService.getTemplate(template.id)
      
      if (response.success && response.data) {
        const fullTemplate = response.data
        setViewingTemplate(fullTemplate)
        setViewModalVisible(true)
      } else {
        message.error(response.message || '获取模板详情失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取模板详情失败:', error)
      message.error('获取模板详情失败')
      setLoading(false)
    }
  }

  const handleEdit = async (template: Template) => {
    try {
      setLoading(true)
      // 获取完整的模板数据
      const response = await templateService.getTemplate(template.id)
      
      if (response.success && response.data) {
        const fullTemplate = response.data
        setEditingTemplate(fullTemplate)
        form.setFieldsValue({
          ...fullTemplate,
          tags: fullTemplate.tags.join(', ')
        })
        setModalVisible(true)
      } else {
        message.error(response.message || '获取模板详情失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取模板详情失败:', error)
      message.error('获取模板详情失败')
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await templateService.deleteTemplate(id)
      
      if (response.success) {
        message.success('删除成功')
        loadTemplates()
        loadStats()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleToggleStatus = async (id: string, field: 'isActive' | 'isPublic', value: boolean) => {
    try {
      let response
      if (field === 'isActive') {
        response = await templateService.toggleTemplateStatus(id, value)
      } else {
        response = await templateService.toggleTemplatePublic(id, value)
      }
      
      if (response.success) {
        message.success(response.message || '状态更新成功')
        loadTemplates()
        loadStats()
      } else {
        message.error(response.message || '状态更新失败')
      }
    } catch (error) {
      console.error('状态更新失败:', error)
      message.error('状态更新失败')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      const submitData: TemplateCreateParams = {
        ...values,
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [],
        settings: values.settings || {}
      }

      let response
      if (editingTemplate) {
        response = await templateService.updateTemplate({
          id: editingTemplate.id,
          ...submitData
        })
      } else {
        response = await templateService.createTemplate(submitData)
      }
      
      if (response.success) {
        message.success(response.message || (editingTemplate ? '更新成功' : '创建成功'))
        setModalVisible(false)
        loadTemplates()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('操作失败:', error)
      message.error('操作失败')
    }
  }

  const columns: ColumnsType<Template> = [
    {
      title: '预览图',
      dataIndex: 'previewImage',
      width: 80,
      render: (previewImage) => (
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          {previewImage ? (
            <Image
              src={previewImage}
              width={60}
              height={60}
              style={{ objectFit: 'cover', borderRadius: 4 }}
              fallback="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0zMiAyMEMzNS4zMTM3IDIwIDM4IDIyLjY4NjMgMzggMjZWMzhDMzggNDEuMzEzNyAzNS4zMTM3IDQ0IDMyIDQ0QzI4LjY4NjMgNDQgMjYgNDEuMzEzNyAyNiAzOFYyNkMyNiAyMi42ODYzIDI4LjY4NjMgMjAgMzIgMjBaIiBmaWxsPSIjQ0NDIi8+CjxwYXRoIGQ9Ik0yMCAzMkMyMCAyNi40NzcyIDI0LjQ3NzIgMjIgMzAgMjJINDAuNUg0MkM0Ny41MjI4IDIyIDUyIDI2LjQ3NzIgNTIgMzJWNDBDNTIgNDUuNTIyOCA0Ny41MjI4IDUwIDQyIDUwSDMwQzI0LjQ3NzIgNTAgMjAgNDUuNTIyOCAyMCA0MFYzMloiIGZpbGw9IiNEREQiLz4KPC9zdmc+"
            />
          ) : (
            <div 
              style={{ 
                width: 60, 
                height: 60, 
                backgroundColor: '#f5f5f5', 
                borderRadius: 4, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px solid #e8e8e8'
              }}
            >
              <PictureOutlined style={{ fontSize: 24, color: '#ccc' }} />
            </div>
          )}
        </div>
      )
    },
    {
      title: '模板信息',
      key: 'info',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            {record.name}
          </div>
          <div style={{ color: '#666', fontSize: '12px', marginBottom: 4 }}>
            {record.description && record.description.length > 50 
              ? `${record.description.substring(0, 50)}...` 
              : record.description || '无描述'}
          </div>
          <Space size="small">
            <Tag color="blue">
              {record.category}
            </Tag>
            <Tag color="orange">
              {record.pointsCost} 积分
            </Tag>
          </Space>
        </div>
      )
    },
    {
      title: '提示词',
      dataIndex: 'prompt',
      width: 200,
      render: (prompt) => (
        <Tooltip title={prompt}>
          <Text ellipsis style={{ maxWidth: 180, display: 'block' }}>
            {prompt}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <div>
          {tags.map(tag => (
            <Tag key={tag} style={{ margin: '2px', fontSize: '12px' }}>
              {tag}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Badge 
              status={record.isActive ? 'success' : 'error'} 
              text={record.isActive ? '启用' : '禁用'}
            />
          </div>
          <div style={{ marginBottom: 4 }}>
            <Badge 
              status={record.isPublic ? 'processing' : 'default'} 
              text={record.isPublic ? '公开' : '私有'}
            />
          </div>
          {record.isPremium && (
            <Tag color="gold" style={{ fontSize: '12px' }}>
              会员专享
            </Tag>
          )}
        </div>
      )
    },
    {
      title: '使用次数',
      dataIndex: 'usageCount',
      width: 100,
      render: (count) => (
        <div style={{ textAlign: 'center' }}>
          <UserOutlined /> {count}
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 120,
      render: (date) => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '禁用' : '启用'}>
            <Button 
              type="link" 
              icon={record.isActive ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              size="small"
              onClick={() => handleToggleStatus(record.id, 'isActive', !record.isActive)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个模板吗？"
            onConfirm={() => handleDelete(record.id)}
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        写真集模板管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="总模板数"
              value={stats.totalTemplates}
              prefix={<PictureOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="公开模板"
              value={stats.publicTemplates}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="启用模板"
              value={stats.activeTemplates}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="会员专享"
              value={stats.premiumTemplates}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="总使用次数"
              value={stats.totalUsage}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索模板名称或描述"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />
          
          <Select
            placeholder="选择分类"
            style={{ width: 120 }}
            allowClear
            value={searchParams.category}
            onChange={(value) => setSearchParams({ ...searchParams, category: value })}
          >
            {categories.map(category => (
              <Select.Option key={category} value={category}>
                {category}
              </Select.Option>
            ))}
          </Select>

          <Select
            placeholder="公开状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isPublic}
            onChange={(value) => setSearchParams({ ...searchParams, isPublic: value })}
          >
            <Select.Option value={true}>公开</Select.Option>
            <Select.Option value={false}>私有</Select.Option>
          </Select>

          <Select
            placeholder="启用状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={(value) => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          
          <Button icon={<ReloadOutlined />} onClick={handleReset}>
            重置
          </Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增模板
          </Button>
        </Space>
      </Card>

      {/* 模板列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={templates}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 新增/编辑模板模态框 */}
      <Modal
        title={editingTemplate ? '编辑模板' : '新增模板'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={() => form.submit()}>
            {editingTemplate ? '更新' : '创建'}
          </Button>
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="模板名称"
                rules={[{ required: true, message: '请输入模板名称' }]}
              >
                <Input placeholder="请输入模板名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="模板分类"
                rules={[{ required: true, message: '请选择模板分类' }]}
              >
                <Select placeholder="请选择模板分类" showSearch allowClear>
                  {/* 默认分类选项 */}
                  <Select.Option value="写真">写真</Select.Option>
                  <Select.Option value="人像">人像</Select.Option>
                  <Select.Option value="风景">风景</Select.Option>
                  <Select.Option value="抽象">抽象</Select.Option>
                  <Select.Option value="动物">动物</Select.Option>
                  <Select.Option value="建筑">建筑</Select.Option>
                  <Select.Option value="二次元">二次元</Select.Option>
                  <Select.Option value="其他">其他</Select.Option>
                  {/* 从数据库加载的分类 */}
                  {categories.map(category => (
                    <Select.Option key={`db-${category}`} value={category}>
                      {category}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="模板描述"
          >
            <TextArea rows={3} placeholder="请输入模板描述" />
          </Form.Item>

          <Form.Item
            name="prompt"
            label="提示词"
            rules={[{ required: true, message: '请输入提示词' }]}
          >
            <TextArea rows={4} placeholder="请输入生成图片的提示词" />
          </Form.Item>

          <Form.Item
            name="negativePrompt"
            label="负面提示词"
          >
            <TextArea rows={2} placeholder="请输入负面提示词（可选）" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="tags"
                label="标签"
                extra="多个标签用逗号分隔"
              >
                <Input placeholder="如：写真,人像,美女" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="pointsCost"
                label="积分消耗"
                initialValue={1}
                rules={[{ required: true, message: '请设置积分消耗' }]}
              >
                <InputNumber 
                  min={0} 
                  style={{ width: '100%' }}
                  placeholder="生成一次所需积分"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="previewImage"
            label="预览图片URL"
          >
            <Input placeholder="请输入预览图片URL（可选）" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="isPublic"
                label="公开状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="公开" unCheckedChildren="私有" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="isPremium"
                label="会员专享"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="isActive"
                label="启用状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 查看详情模态框 */}
      <Modal
        title="查看模板详情"
        open={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>
            关闭
          </Button>
        ]}
      >
        {viewingTemplate && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>模板名称：</Text>
                  <Text>{viewingTemplate.name}</Text>
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>分类：</Text>
                  <Tag color="blue">{viewingTemplate.category}</Tag>
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>积分消耗：</Text>
                  <Tag color="orange">{viewingTemplate.pointsCost} 积分</Tag>
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>状态：</Text>
                  <Space>
                    <Badge 
                      status={viewingTemplate.isActive ? 'success' : 'error'} 
                      text={viewingTemplate.isActive ? '启用' : '禁用'}
                    />
                    <Badge 
                      status={viewingTemplate.isPublic ? 'processing' : 'default'} 
                      text={viewingTemplate.isPublic ? '公开' : '私有'}
                    />
                    {viewingTemplate.isPremium && (
                      <Tag color="gold">会员专享</Tag>
                    )}
                  </Space>
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>使用次数：</Text>
                  <Text>{viewingTemplate.usageCount}</Text>
                </div>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>创建时间：</Text>
                  <Text>{dayjs(viewingTemplate.createdAt).format('YYYY-MM-DD HH:mm:ss')}</Text>
                </div>
              </Col>
              <Col span={12}>
                <div style={{ marginBottom: 16 }}>
                  <Text strong>预览图：</Text>
                  <div style={{ marginTop: 8 }}>
                    {viewingTemplate.previewImage ? (
                      <Image
                        src={viewingTemplate.previewImage}
                        width={200}
                        height={200}
                        style={{ objectFit: 'cover', borderRadius: 8 }}
                      />
                    ) : (
                      <div 
                        style={{ 
                          width: 200, 
                          height: 200, 
                          backgroundColor: '#f5f5f5', 
                          borderRadius: 8, 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'center',
                          border: '1px solid #e8e8e8'
                        }}
                      >
                        <PictureOutlined style={{ fontSize: 48, color: '#ccc' }} />
                      </div>
                    )}
                  </div>
                </div>
              </Col>
            </Row>
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>描述：</Text>
              <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f9f9f9', borderRadius: 4 }}>
                <Text>{viewingTemplate.description || '无描述'}</Text>
              </div>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>提示词：</Text>
              <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f9f9f9', borderRadius: 4 }}>
                <Text>{viewingTemplate.prompt}</Text>
              </div>
            </div>

            {viewingTemplate.negativePrompt && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>负面提示词：</Text>
                <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f9f9f9', borderRadius: 4 }}>
                  <Text>{viewingTemplate.negativePrompt}</Text>
                </div>
              </div>
            )}

            <div style={{ marginBottom: 16 }}>
              <Text strong>标签：</Text>
              <div style={{ marginTop: 8 }}>
                {viewingTemplate.tags.length > 0 ? (
                  viewingTemplate.tags.map(tag => (
                    <Tag key={tag} style={{ margin: '2px' }}>
                      {tag}
                    </Tag>
                  ))
                ) : (
                  <Text type="secondary">无标签</Text>
                )}
              </div>
            </div>

            {viewingTemplate.settings && Object.keys(viewingTemplate.settings).length > 0 && (
              <div style={{ marginBottom: 16 }}>
                <Text strong>生成设置：</Text>
                <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f9f9f9', borderRadius: 4 }}>
                  <pre style={{ margin: 0 }}>
                    {JSON.stringify(viewingTemplate.settings, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  )
}

export default TemplateManagement
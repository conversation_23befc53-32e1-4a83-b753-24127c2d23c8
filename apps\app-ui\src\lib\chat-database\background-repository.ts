import { ChatDatabaseError } from './types'

/**
 * 背景图仓储类
 * 负责会话背景图相关的数据库操作
 */
export class BackgroundRepository {
  private db: any

  constructor(database: any) {
    this.db = database
  }

  /**
   * 更新会话背景图
   */
  async updateSessionBackground(
    sessionId: string,
    backgroundImageUrl: string,
    backgroundImagePath: string,
    sceneDescription: string
  ): Promise<boolean> {
    try {
      const now = new Date().toISOString()

      const result = await this.db.run(
        `UPDATE chat_sessions 
         SET backgroundImagePath = ?, backgroundImageUrl = ?, backgroundSceneDescription = ?, backgroundUpdatedAt = ?, updatedAt = ?
         WHERE id = ?`,
        [backgroundImagePath, backgroundImageUrl, sceneDescription, now, now, sessionId]
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [BgRepo] 会话背景图已更新: ${sessionId}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `更新会话背景图失败: ${sessionId}`,
        'UPDATE_BACKGROUND_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取会话背景图
   */
  async getSessionBackground(sessionId: string): Promise<{
    backgroundImagePath: string
    backgroundImageUrl: string
    backgroundSceneDescription: string
    backgroundUpdatedAt: string
  } | null> {
    try {
      const result = await this.db.query(
        'SELECT backgroundImagePath, backgroundImageUrl, backgroundSceneDescription, backgroundUpdatedAt FROM chat_sessions WHERE id = ?',
        [sessionId]
      )

      if (result.values?.length > 0) {
        const row = result.values[0]
        if (row.backgroundImagePath || row.backgroundImageUrl) {
          return {
            backgroundImagePath: row.backgroundImagePath || '',
            backgroundImageUrl: row.backgroundImageUrl || '',
            backgroundSceneDescription: row.backgroundSceneDescription || '',
            backgroundUpdatedAt: row.backgroundUpdatedAt || new Date().toISOString()
          }
        }
      }

      return null
    } catch (error) {
      throw new ChatDatabaseError(
        `获取会话背景图失败: ${sessionId}`,
        'GET_BACKGROUND_FAILED',
        error as Error
      )
    }
  }

  /**
   * 清除会话背景图
   */
  async clearSessionBackground(sessionId: string): Promise<boolean> {
    try {
      const result = await this.db.run(
        `UPDATE chat_sessions 
         SET backgroundImagePath = NULL, backgroundImageUrl = NULL, backgroundSceneDescription = NULL, backgroundUpdatedAt = NULL, updatedAt = ?
         WHERE id = ?`,
        [new Date().toISOString(), sessionId]
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`🗑️ [BgRepo] 会话背景图已清除: ${sessionId}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `清除会话背景图失败: ${sessionId}`,
        'CLEAR_BACKGROUND_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取所有有背景图的会话
   */
  async getSessionsWithBackground(): Promise<
    Array<{
      id: string
      roleId: string
      title: string
      backgroundImagePath: string
      backgroundImageUrl: string
      backgroundSceneDescription: string
      backgroundUpdatedAt: string
    }>
  > {
    try {
      const result = await this.db.query(
        `SELECT id, roleId, title, backgroundImagePath, backgroundImageUrl, backgroundSceneDescription, backgroundUpdatedAt 
         FROM chat_sessions 
         WHERE (backgroundImagePath IS NOT NULL OR backgroundImageUrl IS NOT NULL)
         ORDER BY backgroundUpdatedAt DESC`
      )

      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        '获取有背景图的会话失败',
        'GET_SESSIONS_WITH_BACKGROUND_FAILED',
        error as Error
      )
    }
  }

  /**
   * 根据场景描述搜索背景图
   */
  async searchBackgroundsByScene(
    keyword: string,
    limit: number = 20
  ): Promise<
    Array<{
      id: string
      title: string
      backgroundImagePath: string
      backgroundImageUrl: string
      backgroundSceneDescription: string
      backgroundUpdatedAt: string
    }>
  > {
    try {
      const result = await this.db.query(
        `SELECT id, title, backgroundImagePath, backgroundImageUrl, backgroundSceneDescription, backgroundUpdatedAt 
         FROM chat_sessions 
         WHERE backgroundSceneDescription LIKE ? AND (backgroundImagePath IS NOT NULL OR backgroundImageUrl IS NOT NULL)
         ORDER BY backgroundUpdatedAt DESC
         LIMIT ?`,
        [`%${keyword}%`, limit]
      )

      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        `搜索背景图失败: ${keyword}`,
        'SEARCH_BACKGROUNDS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 更新背景图本地路径
   */
  async updateBackgroundPath(sessionId: string, localPath: string): Promise<boolean> {
    try {
      const result = await this.db.run(
        'UPDATE chat_sessions SET backgroundImagePath = ?, updatedAt = ? WHERE id = ?',
        [localPath, new Date().toISOString(), sessionId]
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [BgRepo] 背景图本地路径已更新: ${sessionId}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `更新背景图路径失败: ${sessionId}`,
        'UPDATE_BACKGROUND_PATH_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取背景图统计信息
   */
  async getBackgroundStats(): Promise<{
    totalSessions: number
    sessionsWithBackground: number
    backgroundPercentage: number
    latestBackgroundUpdate: string | null
  }> {
    try {
      // 获取总会话数
      const totalResult = await this.db.query('SELECT COUNT(*) as count FROM chat_sessions')
      const totalSessions = totalResult.values?.[0]?.count || 0

      // 获取有背景图的会话数
      const bgResult = await this.db.query(
        'SELECT COUNT(*) as count FROM chat_sessions WHERE (backgroundImagePath IS NOT NULL OR backgroundImageUrl IS NOT NULL)'
      )
      const sessionsWithBackground = bgResult.values?.[0]?.count || 0

      // 获取最新背景图更新时间
      const latestResult = await this.db.query(
        'SELECT backgroundUpdatedAt FROM chat_sessions WHERE backgroundUpdatedAt IS NOT NULL ORDER BY backgroundUpdatedAt DESC LIMIT 1'
      )
      const latestBackgroundUpdate = latestResult.values?.[0]?.backgroundUpdatedAt || null

      return {
        totalSessions,
        sessionsWithBackground,
        backgroundPercentage:
          totalSessions > 0 ? (sessionsWithBackground / totalSessions) * 100 : 0,
        latestBackgroundUpdate
      }
    } catch (error) {
      throw new ChatDatabaseError(
        '获取背景图统计失败',
        'GET_BACKGROUND_STATS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 清理孤儿背景图路径（会话已删除但背景图路径仍存在的情况）
   */
  async cleanupOrphanBackgrounds(): Promise<number> {
    try {
      // 这个在当前设计中不太可能发生，因为背景图数据直接存储在会话表中
      // 但保留此方法以备将来可能的独立背景图表设计
      console.log('🧹 [BgRepo] 背景图数据与会话表一体，无需清理孤儿数据')
      return 0
    } catch (error) {
      throw new ChatDatabaseError(
        '清理孤儿背景图失败',
        'CLEANUP_ORPHAN_BACKGROUNDS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 批量清除多个会话的背景图
   */
  async clearMultipleSessionBackgrounds(sessionIds: string[]): Promise<number> {
    try {
      if (sessionIds.length === 0) return 0

      const placeholders = sessionIds.map(() => '?').join(',')
      const result = await this.db.run(
        `UPDATE chat_sessions 
         SET backgroundImagePath = NULL, backgroundImageUrl = NULL, backgroundSceneDescription = NULL, backgroundUpdatedAt = NULL, updatedAt = ?
         WHERE id IN (${placeholders})`,
        [new Date().toISOString(), ...sessionIds]
      )

      const clearedCount = result.changes?.changes || 0
      if (clearedCount > 0) {
        console.log(`🗑️ [BgRepo] 批量清除背景图完成: ${clearedCount}个会话`)
      }
      return clearedCount
    } catch (error) {
      throw new ChatDatabaseError(
        '批量清除背景图失败',
        'CLEAR_MULTIPLE_BACKGROUNDS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 检查会话是否有背景图
   */
  async hasBackground(sessionId: string): Promise<boolean> {
    try {
      const result = await this.db.query(
        'SELECT 1 FROM chat_sessions WHERE id = ? AND (backgroundImagePath IS NOT NULL OR backgroundImageUrl IS NOT NULL)',
        [sessionId]
      )

      return result.values && result.values.length > 0
    } catch (error) {
      throw new ChatDatabaseError(
        `检查背景图存在性失败: ${sessionId}`,
        'CHECK_BACKGROUND_EXISTS_FAILED',
        error as Error
      )
    }
  }
}

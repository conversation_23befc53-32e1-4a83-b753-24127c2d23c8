import type { LangChainMessage } from './type'
type ChatStatus = 'idle' | 'streaming' | 'submitted' | 'error'
import { MessageV2 } from './message'
import { useScrollToBottom } from '../use-scroll-to-bottom'
import { memo, useEffect, useRef } from 'react'
import { Spinner } from '@heroui/react'
import equal from 'fast-deep-equal'
// 移除 AI SDK 依赖
import { ThinkingIndicator } from '../thinking-indicator'

interface MessagesV2Props {
  status: ChatStatus
  messages: Array<LangChainMessage>
  chatId?: string
  onScene?: (sceneDescription: string) => void
}

function PureMessagesV2({ status, messages, chatId, onScene }: MessagesV2Props) {
  const [messagesContainerRef, messagesEndRef] = useScrollToBottom<HTMLDivElement>()

  // 检查是否应该显示思考指示器
  // 当状态为submitted且最后一条消息是用户消息时显示
  const shouldShowThinking =
    status === 'submitted' && messages.length > 0 && messages[messages.length - 1]?.role === 'user'

  // 手动滚动到底部的函数
  const scrollToBottom = (delay = 50, smooth = true) => {
    if (messagesEndRef.current) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({
          behavior: smooth ? 'smooth' : 'instant',
          block: 'end'
        })
      }, delay)
    }
  }

  // 当ThinkingIndicator显示时，确保滚动到底部
  useEffect(() => {
    if (shouldShowThinking) {
      scrollToBottom(100)
    }
  }, [shouldShowThinking])

  // 只在真正有新消息时滚动到底部，避免音频播放等操作触发滚动
  const prevMessagesLengthRef = useRef(messages.length)
  useEffect(() => {
    // 只有当消息数量真正增加时才滚动
    if (messages.length > prevMessagesLengthRef.current) {
      scrollToBottom()
    }
    prevMessagesLengthRef.current = messages.length
  }, [messages.length])

  // 监听流式输出状态变化，确保流式输出时也能滚动
  useEffect(() => {
    if (status === 'streaming') {
      // 流式输出时适度滚动到底部，避免过于频繁
      const interval = setInterval(() => {
        scrollToBottom(10)
      }, 2000) // 每2秒滚动一次，减少干扰

      return () => clearInterval(interval)
    }
  }, [status])

  // 页面初始加载时直接跳转到底部（无动画）
  useEffect(() => {
    if (messages.length > 0) {
      scrollToBottom(0, false) // 无延迟，无动画
    }
  }, []) // 只在组件挂载时执行一次

  // 监听输入框聚焦事件，滚动到底部
  useEffect(() => {
    const handleFocusIn = (event: FocusEvent) => {
      const target = event.target as Element
      // 如果是输入相关的元素获得焦点，滚动到底部
      if (target.matches('input, textarea, [contenteditable]')) {
        scrollToBottom(150) // 减少延迟时间
      }
    }

    document.addEventListener('focusin', handleFocusIn)
    return () => document.removeEventListener('focusin', handleFocusIn)
  }, [])

  // 监听窗口大小变化，确保在键盘弹出/收起时滚动到底部
  useEffect(() => {
    const handleResize = () => {
      scrollToBottom(50) // 减少延迟时间
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div
      ref={messagesContainerRef}
      className="flex flex-1 flex-col w-full h-full gap-6 overflow-y-auto px-2 pt-4"
    >
      {/* {messages.length === 0 && <Greeting />} */}
      {messages.map((message, index) => (
        <MessageV2
          key={message.id}
          message={message}
          isLoading={status === 'streaming' && messages.length - 1 === index}
          chatId={chatId}
          onScene={onScene}
        />
      ))}

      {/* 显示思考指示器 */}
      {shouldShowThinking && <ThinkingIndicator />}
      <div ref={messagesEndRef} className="shrink-0 min-w-[24px] min-h-[24px]" />
    </div>
  )
}

export const MessagesV2 = memo(PureMessagesV2, (prevProps, nextProps) => {
  if (prevProps.status !== nextProps.status) return false
  if (prevProps.messages.length !== nextProps.messages.length) return false
  if (!equal(prevProps.messages, nextProps.messages)) return false
  if (prevProps.chatId !== nextProps.chatId) return false
  if (prevProps.onScene !== nextProps.onScene) return false

  return true
})

import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware } from '@/middleware/auth';
import type { Env } from '@/types/env';
import { getSupabase } from '@/lib/db/queries/base';
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types';
import { createSupabaseServiceClient } from '@/lib/supabase';

const adminPoints = new Hono<{ Bindings: Env }>();

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user');
    if (!supabaseUser) {
      return false;
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {};
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true;

    if (isAdmin) {
      return true;
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>',
      // 在这里添加其他管理员邮箱
    ];

    if (adminEmails.includes(supabaseUser.email)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('检查管理员权限失败:', error);
    return false;
  }
}

// ==================== 积分套餐管理 ====================

// 创建积分套餐验证schema
const createPackageSchema = z.object({
  name: z.string().min(1, '套餐名称不能为空'),
  description: z.string().optional(),
  price: z.number().min(0, '价格不能小于0'),
  points: z.number().min(1, '积分数量至少为1'),
  bonusPoints: z.number().min(0, '奖励积分不能小于0').default(0),
  isActive: z.boolean().default(true),
  sortOrder: z.number().optional()
});

// 创建积分套餐
adminPoints.post('/packages', authMiddleware, zValidator('json', createPackageSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const packageData = c.req.valid('json');
    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 创建积分套餐:', packageData);

    // 使用Supabase创建套餐
    const supabase = getSupabase(env);

    const result = await supabase
      .from(TABLE_NAMES.pointsPackage)
      .insert({
        name: packageData.name,
        description: packageData.description,
        price: packageData.price.toString(),
        points: packageData.points,
        bonus_points: packageData.bonusPoints,
        is_active: packageData.isActive,
        sort_order: packageData.sortOrder || 0,
      })
      .select()
      .single();

    const { data: newPackage, error } = result;
    if (error) throw error;

    console.log('✅ [ADMIN-POINTS] 积分套餐创建成功:', newPackage.id);

    return c.json({
      success: true,
      data: {
        id: newPackage.id,
        name: newPackage.name,
        description: newPackage.description,
        price: newPackage.price,
        points: newPackage.points,
        bonusPoints: newPackage.bonus_points,
        isActive: newPackage.is_active,
        sortOrder: newPackage.sort_order,
        createdAt: newPackage.created_at,
        updatedAt: newPackage.updated_at,
      },
      message: '积分套餐创建成功'
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 创建积分套餐失败:', error);
    return c.json(
      {
        success: false,
        message: '创建积分套餐失败',
      },
      500
    );
  }
});

// 更新积分套餐
adminPoints.put('/packages/:id', authMiddleware, zValidator('json', createPackageSchema.partial()), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const packageId = c.req.param('id');
    const updateData = c.req.valid('json');
    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 更新积分套餐:', packageId, updateData);

    const supabase = getSupabase(env);

    // 构建更新数据
    const updateFields: any = {};
    if (updateData.name !== undefined) updateFields.name = updateData.name;
    if (updateData.description !== undefined) updateFields.description = updateData.description;
    if (updateData.price !== undefined) updateFields.price = updateData.price.toString();
    if (updateData.points !== undefined) updateFields.points = updateData.points;
    if (updateData.bonusPoints !== undefined) updateFields.bonus_points = updateData.bonusPoints;
    if (updateData.isActive !== undefined) updateFields.is_active = updateData.isActive;
    if (updateData.sortOrder !== undefined) updateFields.sort_order = updateData.sortOrder;

    const result = await supabase
      .from(TABLE_NAMES.pointsPackage)
      .update(updateFields)
      .eq('id', packageId)
      .select()
      .single();

    const { data: updatedPackage, error } = result;

    if (error || !updatedPackage) {
      return c.json({ success: false, message: '积分套餐不存在' }, 404);
    }

    console.log('✅ [ADMIN-POINTS] 积分套餐更新成功:', updatedPackage.id);

    return c.json({
      success: true,
      data: {
        id: updatedPackage.id,
        name: updatedPackage.name,
        description: updatedPackage.description,
        price: updatedPackage.price,
        points: updatedPackage.points,
        bonusPoints: updatedPackage.bonus_points,
        isActive: updatedPackage.is_active,
        sortOrder: updatedPackage.sort_order,
        createdAt: updatedPackage.created_at,
        updatedAt: updatedPackage.updated_at,
      },
      message: '积分套餐更新成功'
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 更新积分套餐失败:', error);
    return c.json(
      {
        success: false,
        message: '更新积分套餐失败',
      },
      500
    );
  }
});

// 删除积分套餐
adminPoints.delete('/packages/:id', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const packageId = c.req.param('id');
    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 删除积分套餐:', packageId);

    const supabase = getSupabase(env);

    // 检查是否有用户购买过此套餐
    const existingOrdersResult = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('id')
      .eq('points_package_id', packageId)
      .limit(1);

    if (existingOrdersResult.data && existingOrdersResult.data.length > 0) {
      return c.json({
        success: false,
        message: '该积分套餐已有用户购买，无法删除'
      }, 400);
    }

    const result = await supabase
      .from(TABLE_NAMES.pointsPackage)
      .delete()
      .eq('id', packageId)
      .select()
      .single();

    const { data: deletedPackage, error } = result;

    if (error || !deletedPackage) {
      return c.json({ success: false, message: '积分套餐不存在' }, 404);
    }

    console.log('✅ [ADMIN-POINTS] 积分套餐删除成功:', deletedPackage.id);

    return c.json({
      success: true,
      message: '积分套餐删除成功'
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 删除积分套餐失败:', error);
    return c.json(
      {
        success: false,
        message: '删除积分套餐失败',
      },
      500
    );
  }
});

// 获取所有积分套餐（管理员）
adminPoints.get('/packages', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const env = c.env;

    console.log('📊 [ADMIN-POINTS] 获取积分套餐列表');

    const supabase = getSupabase(env);

    const result = await supabase
      .from(TABLE_NAMES.pointsPackage)
      .select('*')
      .order('sort_order', { ascending: false })
      .order('created_at', { ascending: false });

    const { data: packages, error } = handleSupabaseResult(result);
    if (error) throw error;

    // 添加统计信息
    const packageStats = await Promise.all(
      (packages || []).map(async (pkg: any) => {
        // TODO: 实现每个套餐的购买统计
        return {
          id: pkg.id,
          name: pkg.name,
          description: pkg.description,
          price: Number(pkg.price), // 转换为数字类型
          points: pkg.points,
          bonusPoints: pkg.bonus_points,
          isActive: pkg.is_active,
          sortOrder: pkg.sort_order,
          createdAt: pkg.created_at,
          updatedAt: pkg.updated_at,
          salesCount: 0, // TODO: 实现销售数量统计
          revenue: 0, // TODO: 实现收入统计
        };
      })
    );

    return c.json({
      success: true,
      data: packageStats,
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 获取积分套餐失败:', error);
    return c.json(
      {
        success: false,
        message: '获取积分套餐失败',
      },
      500
    );
  }
});

// 获取积分套餐详情
adminPoints.get('/packages/:id', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const packageId = c.req.param('id');
    const env = c.env;

    const supabase = getSupabase(env);

    const result = await supabase
      .from(TABLE_NAMES.pointsPackage)
      .select('*')
      .eq('id', packageId)
      .single();

    const { data: pointsPackage, error } = result;
    if (error || !pointsPackage) {
      return c.json(
        {
          success: false,
          message: '积分套餐不存在',
        },
        404
      );
    }

    return c.json({
      success: true,
      data: {
        id: pointsPackage.id,
        name: pointsPackage.name,
        description: pointsPackage.description,
        price: Number(pointsPackage.price), // 转换为数字类型
        points: pointsPackage.points,
        bonusPoints: pointsPackage.bonus_points,
        isActive: pointsPackage.is_active,
        sortOrder: pointsPackage.sort_order,
        createdAt: pointsPackage.created_at,
        updatedAt: pointsPackage.updated_at,
      },
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 获取积分套餐详情失败:', error);
    return c.json(
      {
        success: false,
        message: '获取积分套餐详情失败',
      },
      500
    );
  }
});

// ==================== 用户积分管理 ====================

// 获取用户积分列表
const userPointsListSchema = z.object({
  page: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  minBalance: z
    .string()
    .transform((val) => Number.parseInt(val))
    .optional(),
  maxBalance: z
    .string()
    .transform((val) => Number.parseInt(val))
    .optional(),
});

adminPoints.get(
  '/users',
  authMiddleware,
  zValidator('query', userPointsListSchema),
  async (c) => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c);
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403);
      }

      const { page, pageSize, keyword, minBalance, maxBalance } = c.req.valid('query');
      const env = c.env;

      console.log('🔍 [ADMIN-POINTS] 获取用户积分列表:', { page, pageSize, keyword, minBalance, maxBalance });

      // 从数据库查询用户积分信息
      const supabase = getSupabase(env);
      const { createSupabaseServiceClient } = await import('@/lib/supabase');
      
      // 构建基础查询
      let query = supabase
        .from(TABLE_NAMES.userPoints)
        .select(`
          id,
          user_id,
          total_points,
          used_points,
          available_points,
          cycle_start_date,
          cycle_end_date,
          last_updated
        `)
        .order('available_points', { ascending: false });

      // 应用积分余额筛选
      if (minBalance !== undefined) {
        query = query.gte('available_points', minBalance);
      }

      if (maxBalance !== undefined) {
        query = query.lte('available_points', maxBalance);
      }

      // 处理关键字搜索
      let matchedUserIds: string[] = [];
      if (keyword) {
        const supabaseService = createSupabaseServiceClient(env);
        const { data: allUsers } = await supabaseService.auth.admin.listUsers();
        
        if (allUsers?.users) {
          // 筛选匹配keyword的用户
          matchedUserIds = allUsers.users
            .filter(user => 
              user.email?.toLowerCase().includes(keyword.toLowerCase()) ||
              user.id.includes(keyword)
            )
            .map(user => user.id);

          if (matchedUserIds.length > 0) {
            query = query.in('user_id', matchedUserIds);
          } else {
            // 没有匹配的用户，返回空列表
            return c.json({
              success: true,
              data: {
                data: [],
                total: 0,
                page,
                pageSize,
                totalPages: 0,
              },
            });
          }
        }
      }

      // 计算总数
      const countQuery = supabase
        .from(TABLE_NAMES.userPoints)
        .select('*', { count: 'exact', head: true });
      
      // 应用相同的筛选条件到计数查询
      let countResult;
      if (keyword && matchedUserIds.length > 0) {
        countResult = await countQuery.in('user_id', matchedUserIds);
      } else {
        countResult = await countQuery;
      }
      
      const total = countResult.count || 0;

      // 分页查询
      const offset = (page - 1) * pageSize;
      const result = await query.range(offset, offset + pageSize - 1);
      const { data: userPointsData, error } = handleSupabaseResult(result);
      if (error) throw error;

      console.log(`🔍 [ADMIN-POINTS] 查询到 ${userPointsData?.length || 0} 条用户积分，共 ${total} 条`);

      // 获取用户邮箱信息
      const userPointsWithEmail = await Promise.all(
        (userPointsData || []).map(async (up: any) => {
          let userEmail = '';
          try {
            const supabaseService = createSupabaseServiceClient(env);
            const { data: authUser } = await supabaseService.auth.admin.getUserById(up.user_id);
            userEmail = authUser?.user?.email || '';
          } catch (error) {
            console.warn('获取用户邮箱失败:', error);
          }
          
          return {
            id: up.id,
            userId: up.user_id,
            userEmail,
            balance: up.available_points || 0, // 为了兼容前端接口
            totalPoints: up.total_points || 0,
            usedPoints: up.used_points || 0,
            availablePoints: up.available_points || 0,
            cycleStartDate: up.cycle_start_date || null,
            cycleEndDate: up.cycle_end_date || null,
            createdAt: up.last_updated || new Date().toISOString(),
          };
        })
      );

      const formattedUserPoints = userPointsWithEmail;

      return c.json({
        success: true,
        data: {
          data: formattedUserPoints,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize),
        },
      });
    } catch (error) {
      console.error('❌ [ADMIN-POINTS] 获取用户积分列表失败:', error);
      return c.json(
        {
          success: false,
          message: '获取用户积分列表失败',
        },
        500
      );
    }
  }
);

// 调整用户积分
const adjustPointsSchema = z.object({
  userId: z.string().min(1, '用户ID不能为空'),
  points: z.number().min(1, '积分数量必须大于0'),
  type: z.enum(['add', 'deduct'], { required_error: '请选择操作类型' }),
  reason: z.string().min(1, '操作原因不能为空')
});

adminPoints.post('/adjust', authMiddleware, zValidator('json', adjustPointsSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const { userId, points, type, reason } = c.req.valid('json');
    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 调整用户积分:', { userId, points, type, reason });

    // 使用 Supabase 进行积分调整
    const supabase = getSupabase(env);
    
    // 获取当前用户积分
    const { data: currentPoints } = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .eq('user_id', userId)
      .single();

    let newBalance: number;
    
    if (!currentPoints) {
      // 用户积分记录不存在，创建一个
      if (type === 'deduct') {
        throw new Error('用户积分余额不足');
      }
      
      const now = new Date();
      const cycleEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30天后
      
      const { error: createError } = await supabase
        .from(TABLE_NAMES.userPoints)
        .insert({
          user_id: userId,
          total_points: points,
          used_points: 0,
          available_points: points,
          cycle_start_date: now.toISOString(),
          cycle_end_date: cycleEnd.toISOString(),
          monthly_allocation: 0,
          cycle_consumed: 0,
          cycle_gifted: 0,
          cycle_received: 0,
          last_cycle_check: now.toISOString(),
          last_updated: now.toISOString(),
        });
      
      if (createError) throw createError;
      newBalance = points;
    } else {
      // 更新现有积分记录
      if (type === 'add') {
        newBalance = currentPoints.available_points + points;
        const { error: updateError } = await supabase
          .from(TABLE_NAMES.userPoints)
          .update({
            total_points: currentPoints.total_points + points,
            available_points: newBalance,
            last_updated: new Date().toISOString(),
          })
          .eq('user_id', userId);
        
        if (updateError) throw updateError;
      } else {
        // 扣除积分，检查余额是否足够
        if (currentPoints.available_points < points) {
          throw new Error('用户积分余额不足');
        }
        
        newBalance = currentPoints.available_points - points;
        const { error: updateError } = await supabase
          .from(TABLE_NAMES.userPoints)
          .update({
            used_points: currentPoints.used_points + points,
            available_points: newBalance,
            last_updated: new Date().toISOString(),
          })
          .eq('user_id', userId);
        
        if (updateError) throw updateError;
      }
    }

    // 记录积分交易
    const { error: transactionError } = await supabase
      .from(TABLE_NAMES.pointsTransaction)
      .insert({
        user_id: userId,
        transaction_type: type === 'add' ? 'earn' : 'spend',
        amount: type === 'add' ? points : -points,
        source: 'admin',
        description: reason,
        balance_after: newBalance,
      });
    
    if (transactionError) throw transactionError;

    console.log('✅ [ADMIN-POINTS] 用户积分调整成功');

    return c.json({
      success: true,
      message: `积分${type === 'add' ? '增加' : '扣除'}成功`
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 调整用户积分失败:', error);
    return c.json(
      {
        success: false,
        message: error instanceof Error ? error.message : '调整积分失败',
      },
      500
    );
  }
});

// ==================== 积分交易记录 ====================

// 获取积分交易记录
const transactionListSchema = z.object({
  page: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('20'),
  userId: z.string().optional(),
  type: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
});

adminPoints.get(
  '/transactions',
  authMiddleware,
  zValidator('query', transactionListSchema),
  async (c) => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c);
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403);
      }

      const { page, pageSize, userId, type, startDate, endDate } = c.req.valid('query');
      const env = c.env;

      console.log('🔍 [ADMIN-POINTS] 获取积分交易记录:', { page, pageSize, userId, type, startDate, endDate });

      // 使用 Supabase 查询积分交易记录
      const supabase = getSupabase(env);
      const { createSupabaseServiceClient } = await import('@/lib/supabase');
      
      // 构建基础查询
      let query = supabase
        .from(TABLE_NAMES.pointsTransaction)
        .select(`
          id,
          user_id,
          transaction_type,
          amount,
          source,
          description,
          balance_after,
          created_at
        `)
        .order('created_at', { ascending: false });

      // 应用筛选条件
      if (userId) {
        query = query.eq('user_id', userId);
      }

      if (type) {
        // 类型转换以匹配enum
        const validTypes = ['earn', 'spend', 'refund', 'bonus', 'cycle_grant', 'cycle_reset', 'gift_sent', 'gift_received', 'upgrade_bonus'] as const;
        if (validTypes.includes(type as any)) {
          query = query.eq('transaction_type', type);
        }
      }

      if (startDate) {
        query = query.gte('created_at', new Date(startDate).toISOString());
      }

      if (endDate) {
        query = query.lte('created_at', new Date(endDate).toISOString());
      }

      // 计算总数
      const countQuery = supabase
        .from(TABLE_NAMES.pointsTransaction)
        .select('*', { count: 'exact', head: true });
      
      // 应用相同筛选条件
      let countResult = countQuery;
      if (userId) {
        countResult = countResult.eq('user_id', userId);
      }
      if (type) {
        const validTypes = ['earn', 'spend', 'refund', 'bonus', 'cycle_grant', 'cycle_reset', 'gift_sent', 'gift_received', 'upgrade_bonus'] as const;
        if (validTypes.includes(type as any)) {
          countResult = countResult.eq('transaction_type', type);
        }
      }
      if (startDate) {
        countResult = countResult.gte('created_at', new Date(startDate).toISOString());
      }
      if (endDate) {
        countResult = countResult.lte('created_at', new Date(endDate).toISOString());
      }
      
      const { count: total } = await countResult;

      // 分页查询
      const offset = (page - 1) * pageSize;
      const result = await query.range(offset, offset + pageSize - 1);
      const { data: transactions, error } = handleSupabaseResult(result);
      if (error) throw error;

      console.log(`🔍 [ADMIN-POINTS] 查询到 ${transactions?.length || 0} 条交易记录，共 ${total} 条`);

      // 获取用户邮箱信息
      const transactionsWithEmail = await Promise.all(
        (transactions || []).map(async (transaction: any) => {
          let userEmail = '';
          try {
            const supabaseService = createSupabaseServiceClient(env);
            const { data: authUser } = await supabaseService.auth.admin.getUserById(transaction.user_id);
            userEmail = authUser?.user?.email || '';
          } catch (error) {
            console.warn('获取用户邮箱失败:', error);
          }
          
          return {
            id: transaction.id,
            userId: transaction.user_id,
            userEmail,
            type: transaction.transaction_type,
            amount: transaction.amount,
            source: transaction.source,
            description: transaction.description,
            balanceAfter: transaction.balance_after,
            createdAt: transaction.created_at,
          };
        })
      );

      // 格式化数据
      const formattedTransactions = transactionsWithEmail.map((tx) => ({
        id: tx.id,
        userId: tx.userId,
        userEmail: tx.userEmail,
        type: tx.type === 'earn' ? 'add' : 'deduct',
        points: Math.abs(tx.amount),
        reason: tx.description,
        balanceAfter: tx.balanceAfter,
        createdAt: tx.createdAt,
      }));

      return c.json({
        success: true,
        data: {
          data: formattedTransactions,
          total: total || 0,
          page,
          pageSize,
          totalPages: Math.ceil((total || 0) / pageSize),
        },
      });
    } catch (error) {
      console.error('❌ [ADMIN-POINTS] 获取积分交易记录失败:', error);
      return c.json(
        {
          success: false,
          message: '获取积分交易记录失败',
        },
        500
      );
    }
  }
);

// ==================== 数据修复工具 ====================

// 修复用户积分记录中的空周期信息
adminPoints.post('/fix-cycle-dates', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const env = c.env;

    console.log('🔧 [ADMIN-POINTS] 开始修复用户积分周期信息');

    // 使用 Supabase 进行数据修复
    const supabase = getSupabase(env);

    // 查找所有周期信息为空的记录
    const { data: recordsToFix, error: queryError } = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .or('cycle_start_date.is.null,cycle_end_date.is.null');

    if (queryError) throw queryError;

    console.log(`🔍 发现 ${recordsToFix?.length || 0} 条需要修复的记录`);

    if (!recordsToFix || recordsToFix.length === 0) {
      return c.json({
        success: true,
        message: '没有需要修复的记录',
        data: { fixed: 0 }
      });
    }

    let fixedCount = 0;

    // 分批修复记录
    for (const record of recordsToFix) {
      try {
        const now = new Date();
        const cycleEnd = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30天后

        const updateData: any = {};
        if (!record.cycle_start_date) updateData.cycle_start_date = now.toISOString();
        if (!record.cycle_end_date) updateData.cycle_end_date = cycleEnd.toISOString();
        if (!record.last_cycle_check) updateData.last_cycle_check = now.toISOString();

        if (Object.keys(updateData).length > 0) {
          const { error: updateError } = await supabase
            .from(TABLE_NAMES.userPoints)
            .update(updateData)
            .eq('id', record.id);

          if (updateError) {
            console.error(`❌ 修复记录 ${record.id} 失败:`, updateError);
            continue;
          }

          fixedCount++;
        }
      } catch (error) {
        console.error(`❌ 修复记录 ${record.id} 失败:`, error);
      }
    }

    console.log(`✅ [ADMIN-POINTS] 成功修复 ${fixedCount} 条记录`);

    return c.json({
      success: true,
      message: `成功修复 ${fixedCount} 条用户积分记录的周期信息`,
      data: { 
        total: recordsToFix.length,
        fixed: fixedCount 
      }
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 修复周期信息失败:', error);
    return c.json(
      {
        success: false,
        message: '修复周期信息失败',
      },
      500
    );
  }
});

// ==================== 积分配置管理 ====================

// 获取所有积分配置
adminPoints.get('/config', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 获取积分配置');

    const { createPointsConfigManager } = await import('@/lib/membership/config');
    const configManager = createPointsConfigManager(env);

    // 获取所有积分配置
    const pointsConfig = await configManager.getAllPointsCosts();

    return c.json({
      success: true,
      data: pointsConfig,
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 获取积分配置失败:', error);
    return c.json(
      {
        success: false,
        message: '获取积分配置失败',
      },
      500
    );
  }
});

// 更新积分配置
const updateConfigSchema = z.object({
  feature: z.string().min(1, '功能名称不能为空'),
  cost: z.number().min(0, '积分消费不能小于0'),
});

adminPoints.put('/config', authMiddleware, zValidator('json', updateConfigSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const { feature, cost } = c.req.valid('json');
    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 更新积分配置:', { feature, cost });

    const { createPointsConfigManager } = await import('@/lib/membership/config');
    const configManager = createPointsConfigManager(env);

    // 更新积分配置
    const success = await configManager.updatePointsCost(feature, cost);

    if (success) {
      return c.json({
        success: true,
        message: '积分配置更新成功',
      });
    } else {
      return c.json({
        success: false,
        message: '更新积分配置失败',
      }, 500);
    }
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 更新积分配置失败:', error);
    return c.json(
      {
        success: false,
        message: '更新积分配置失败',
      },
      500
    );
  }
});

// 批量更新积分配置
const batchUpdateConfigSchema = z.object({
  configs: z.array(z.object({
    feature: z.string().min(1, '功能名称不能为空'),
    cost: z.number().min(0, '积分消费不能小于0'),
  })).min(1, '配置列表不能为空')
});

adminPoints.put('/config/batch', authMiddleware, zValidator('json', batchUpdateConfigSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const { configs } = c.req.valid('json');
    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 批量更新积分配置:', configs);

    const { createPointsConfigManager } = await import('@/lib/membership/config');
    const configManager = createPointsConfigManager(env);

    // 批量更新配置
    let successCount = 0;
    for (const config of configs) {
      const success = await configManager.updatePointsCost(config.feature, config.cost);
      if (success) successCount++;
    }

    if (successCount === configs.length) {
      return c.json({
        success: true,
        message: `成功更新 ${successCount} 个配置`,
      });
    } else {
      return c.json({
        success: false,
        message: `更新了 ${successCount}/${configs.length} 个配置，部分失败`,
      }, 500);
    }
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 批量更新积分配置失败:', error);
    return c.json(
      {
        success: false,
        message: '批量更新积分配置失败',
      },
      500
    );
  }
});

// 重置积分配置为默认值
adminPoints.post('/config/reset', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const env = c.env;

    console.log('🔍 [ADMIN-POINTS] 重置积分配置为默认值');

    const { createPointsConfigManager } = await import('@/lib/membership/config');
    const configManager = createPointsConfigManager(env);

    // 获取默认配置
    const defaultConfig = configManager.getDefaultConfig();
    const pointsConfig = defaultConfig.points;

    // 重置所有配置为默认值
    let successCount = 0;
    for (const [feature, cost] of Object.entries(pointsConfig)) {
      const success = await configManager.updatePointsCost(feature, cost);
      if (success) successCount++;
    }

    const totalFeatures = Object.keys(pointsConfig).length;

    if (successCount === totalFeatures) {
      return c.json({
        success: true,
        message: `成功重置 ${successCount} 个配置为默认值`,
      });
    } else {
      return c.json({
        success: false,
        message: `重置了 ${successCount}/${totalFeatures} 个配置，部分失败`,
      }, 500);
    }
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 重置积分配置失败:', error);
    return c.json(
      {
        success: false,
        message: '重置积分配置失败',
      },
      500
    );
  }
});

// ==================== 积分统计数据 ====================

adminPoints.get('/stats', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const env = c.env;

    console.log('📊 [ADMIN-POINTS] 获取积分统计');

    // 添加快速返回选项，避免长时间查询
    const quickMode = c.req.query('quick') === 'true';
    
    if (quickMode) {
      console.log('📊 使用快速模式返回基本统计');
      return c.json({
        success: true,
        data: {
          totalPackages: 0,
          activePackages: 0,
          totalUsers: 0,
          totalPoints: 0,
          monthlyTransactions: 0,
          todayTransactions: 0,
          monthlyRevenue: 0,
        },
      });
    }

    // 使用 Supabase 查询统计数据
    const supabase = getSupabase(env);

    // 今日时间范围
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 逐步查询统计数据，避免超时
    const stats = {
      totalPackages: 0,
      activePackages: 0,
      totalUsers: 0,
      totalPoints: 0,
      monthlyTransactions: 0,
      todayTransactions: 0,
      monthlyRevenue: 0,
    };

    try {
      // 查询积分套餐统计
      const totalPackagesResult = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .select('*', { count: 'exact', head: true });
      stats.totalPackages = totalPackagesResult.count || 0;

      const activePackagesResult = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .select('*', { count: 'exact', head: true })
        .eq('is_active', true);
      stats.activePackages = activePackagesResult.count || 0;

      console.log('📊 积分套餐统计完成:', { totalPackages: stats.totalPackages, activePackages: stats.activePackages });
    } catch (error) {
      console.error('积分套餐统计失败:', error);
    }

    try {
      // 查询用户积分统计
      const totalUsersResult = await supabase
        .from(TABLE_NAMES.userPoints)
        .select('*', { count: 'exact', head: true });
      stats.totalUsers = totalUsersResult.count || 0;

      // 查询总积分（使用JavaScript计算而非数据库聚合）
      const { data: userPointsData } = await supabase
        .from(TABLE_NAMES.userPoints)
        .select('available_points');
      
      stats.totalPoints = (userPointsData || []).reduce((sum, item) => sum + (Number.parseFloat(item.available_points) || 0), 0);

      console.log('📊 用户积分统计完成:', { totalUsers: stats.totalUsers, totalPoints: stats.totalPoints });
    } catch (error) {
      console.error('用户积分统计失败:', error);
    }

    try {
      // 查询今日交易统计
      const todayTransactionsResult = await supabase
        .from(TABLE_NAMES.pointsTransaction)
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString());
      stats.todayTransactions = todayTransactionsResult.count || 0;

      console.log('📊 今日交易统计完成:', { todayTransactions: stats.todayTransactions });
    } catch (error) {
      console.error('今日交易统计失败:', error);
    }

    try {
      // 月度收入统计
      const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
      
      const { data: monthlyOrdersData } = await supabase
        .from(TABLE_NAMES.paymentOrder)
        .select('amount')
        .eq('status', 'paid')
        .gte('created_at', thirtyDaysAgo.toISOString())
        .not('points_package_id', 'is', null);
      
      stats.monthlyRevenue = (monthlyOrdersData || []).reduce((sum, item) => sum + (Number.parseFloat(item.amount) || 0), 0);

      console.log('📊 月度收入统计完成:', { monthlyRevenue: stats.monthlyRevenue });
    } catch (error) {
      console.error('月度收入统计失败:', error);
      // 如果月度收入查询失败，跳过，不影响其他统计
    }


    console.log('📊 [ADMIN-POINTS] 统计结果:', stats);

    return c.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('❌ [ADMIN-POINTS] 获取积分统计失败:', error);
    return c.json(
      {
        success: false,
        message: '获取统计数据失败',
      },
      500
    );
  }
});

export default adminPoints;
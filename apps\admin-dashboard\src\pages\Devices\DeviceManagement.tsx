import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Badge,
  Upload,
  Image,
  Divider
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  TabletOutlined,
  SearchOutlined,
  ExportOutlined,
  WifiOutlined,
  HistoryOutlined,
  SettingOutlined,
  UploadOutlined,
  PlayCircleOutlined,
  ThunderboltOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { Device, DeviceFunctionBinding } from '@/services/devices'
import { deviceService } from '@/services/devices'
import type { DeviceFunction } from '@/services/device-functions'
import { functionService } from '@/services/device-functions'
import type { SimpleDeviceMode } from '@/services/simple-device-modes'
import { simpleModeService } from '@/services/simple-device-modes'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

const DeviceManagement: React.FC = () => {
  const [devices, setDevices] = useState<Device[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [functionModalVisible, setFunctionModalVisible] = useState(false)
  const [modeModalVisible, setModeModalVisible] = useState(false)
  const [editingDevice, setEditingDevice] = useState<Device | null>(null)
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null)
  const [deviceFunctionBindings, setDeviceFunctionBindings] = useState<DeviceFunctionBinding[]>([])
  const [availableFunctions, setAvailableFunctions] = useState<DeviceFunction[]>([])
  const [availableModes, setAvailableModes] = useState<SimpleDeviceMode[]>([])
  const [deviceModes, setDeviceModes] = useState<string[]>([])
  const [form] = Form.useForm()
  const [modeForm] = Form.useForm()
  
  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    category: undefined as string | undefined,
    isActive: undefined as boolean | undefined,
    userId: undefined as string | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    totalDevices: 0,
    activeDevices: 0,
    onlineDevices: 0,
    todayUsage: 0,
    monthlyUsage: 0
  })

  useEffect(() => {
    loadDevices()
    loadStats()
    loadAvailableModes()
    loadAvailableFunctions()
  }, [currentPage, pageSize, searchParams]) // eslint-disable-line react-hooks/exhaustive-deps

  const loadDevices = async () => {
    try {
      setLoading(true)
      
      const response = await deviceService.getDevices({
        page: currentPage,
        pageSize,
        ...searchParams
      })
      
      if (response.success && response.data) {
        setDevices(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取设备列表失败')
      }
    } catch (error) {
      console.error('获取设备列表失败:', error)
      message.error('获取设备列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await deviceService.getDeviceStats()
      
      if (response.success && response.data) {
        setStats(response.data)
      } else {
        // API不存在时使用默认值
        console.warn('统计API不可用，使用默认统计数据')
        setStats({
          totalDevices: devices.length,
          activeDevices: devices.filter(d => d.isActive).length,
          onlineDevices: 0,
          todayUsage: 0,
          monthlyUsage: 0
        })
      }
    } catch (error) {
      console.warn('获取统计数据失败，使用默认值:', error)
      // 使用基于当前设备列表的统计
      setStats({
        totalDevices: devices.length,
        activeDevices: devices.filter(d => d.isActive).length,
        onlineDevices: 0,
        todayUsage: 0,
        monthlyUsage: 0
      })
    }
  }

  const loadAvailableModes = async () => {
    try {
      const response = await simpleModeService.getModes({ page: 1, pageSize: 100, isActive: true })
      
      if (response.success && response.data) {
        setAvailableModes(response.data.data)
      }
    } catch (error) {
      console.error('获取模式列表失败:', error)
    }
  }

  const loadAvailableFunctions = async () => {
    try {
      const response = await functionService.getFunctions({ page: 1, pageSize: 100, isActive: true })
      
      if (response.success && response.data) {
        setAvailableFunctions(response.data.data)
      }
    } catch (error) {
      console.error('获取可用功能列表失败:', error)
    }
  }

  const loadDeviceFunctions = async (deviceId: string) => {
    try {
      const response = await deviceService.getDeviceFunctions(deviceId)
      
      if (response.success && response.data) {
        setDeviceFunctionBindings(response.data)
      } else {
        message.error(response.message || '获取设备功能失败')
      }
    } catch (error) {
      console.error('获取设备功能失败:', error)
      message.error('获取设备功能失败')
    }
  }

  const loadDeviceModes = async () => {
    try {
      // TODO: 实现获取设备绑定模式的API
      // const response = await deviceService.getDeviceModes(deviceId)
      // if (response.success && response.data) {
      //   setDeviceModes(response.data.map(mode => mode.id))
      // }
      
      // 临时模拟数据
      setDeviceModes([])
    } catch (error) {
      console.error('获取设备模式失败:', error)
      message.error('获取设备模式失败')
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      category: undefined,
      isActive: undefined,
      userId: undefined
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingDevice(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (device: Device) => {
    setEditingDevice(device)
    form.setFieldsValue({
      deviceCode: device.deviceCode,
      name: device.name,
      brand: device.brand,
      model: device.model,
      category: device.category,
      description: device.description,
      isActive: device.isActive
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingDevice) {
        const response = await deviceService.updateDevice(editingDevice.id, values)
        if (response.success) {
          message.success('设备更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await deviceService.createDevice(values)
        if (response.success) {
          message.success('设备创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }

      setModalVisible(false)
      loadDevices()
      loadStats()
    } catch (error) {
      console.error('操作失败:', error)
      message.error(editingDevice ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await deviceService.deleteDevice(id)
      if (response.success) {
        message.success('设备删除成功')
        loadDevices()
        loadStats()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleViewFunctions = async (device: Device) => {
    setSelectedDevice(device)
    await loadDeviceFunctions(device.id)
    setFunctionModalVisible(true)
  }

  const handleViewModes = async (device: Device) => {
    setSelectedDevice(device)
    await loadDeviceModes()
    setModeModalVisible(true)
  }

  const handleBindFunction = async (functionId: string) => {
    if (!selectedDevice) return

    try {
      const response = await deviceService.bindDeviceFunction(selectedDevice.id, functionId)
      if (response.success) {
        message.success('功能绑定成功')
        // 刷新模态框中的功能列表
        await loadDeviceFunctions(selectedDevice.id)
        // 刷新主设备列表以更新功能数量显示
        await loadDevices()
      } else {
        message.error(response.message || '绑定失败')
      }
    } catch (error) {
      console.error('绑定功能失败:', error)
      message.error('绑定功能失败')
    }
  }

  const handleUnbindFunction = async (bindingId: string) => {
    if (!selectedDevice) return

    try {
      const response = await deviceService.unbindDeviceFunction(selectedDevice.id, bindingId)
      
      if (response.success) {
        message.success('功能解绑成功')
        // 刷新模态框中的功能列表
        await loadDeviceFunctions(selectedDevice.id)
        // 刷新主设备列表以更新功能数量显示
        await loadDevices()
      } else {
        message.error(response.message || '解绑失败')
      }
    } catch (error) {
      console.error('解绑功能失败:', error)
      message.error('解绑功能失败')
    }
  }

  const handleBindModes = async (values: { modes?: string[] }) => {
    if (!selectedDevice) return

    try {
      // TODO: 实现设备模式绑定的API
      // const response = await deviceService.bindDeviceModes(selectedDevice.id, values.modes || [])
      // if (response.success) {
      //   message.success('模式绑定成功')
      //   setModeModalVisible(false)
      //   loadDevices()
      // } else {
      //   message.error(response.message || '绑定失败')
      // }
      
      console.log('绑定模式:', values.modes)
      message.success('模式绑定成功（模拟）')
      setModeModalVisible(false)
    } catch (error) {
      console.error('模式绑定失败:', error)
      message.error('模式绑定失败')
    }
  }

  const handleViewDetail = (device: Device) => {
    Modal.info({
      title: '设备详情',
      width: 700,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <div>
              <strong>基本信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div><strong>设备码:</strong> {device.deviceCode}</div>
                <div><strong>设备名称:</strong> {device.name}</div>
                <div><strong>品牌:</strong> {device.brand || '未设置'}</div>
                <div><strong>型号:</strong> {device.model || '未设置'}</div>
                <div><strong>分类:</strong> {device.category || '未设置'}</div>
                <div><strong>描述:</strong> {device.description || '无'}</div>
              </div>
            </div>
            
            <div>
              <strong>状态信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div><strong>启用状态:</strong> {
                  device.isActive ? 
                    <Tag color="green">启用</Tag> : 
                    <Tag color="red">禁用</Tag>
                }</div>
                <div><strong>拥有者:</strong> {device.user?.email || '系统设备'}</div>
                <div><strong>最后连接:</strong> {
                  device.lastConnectedAt ? 
                    dayjs(device.lastConnectedAt).format('YYYY-MM-DD HH:mm:ss') : 
                    '从未连接'
                }</div>
              </div>
            </div>

            <div>
              <strong>时间信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div><strong>创建时间:</strong> {dayjs(device.createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
                <div><strong>更新时间:</strong> {dayjs(device.updatedAt).format('YYYY-MM-DD HH:mm:ss')}</div>
              </div>
            </div>
          </Space>
        </div>
      )
    })
  }

  const getCategoryColor = (category?: string) => {
    const colorMap: Record<string, string> = {
      '震动类': 'magenta',
      '加热类': 'orange', 
      '按摩类': 'green',
      '智能类': 'blue',
      '组合类': 'purple'
    }
    return colorMap[category || ''] || 'default'
  }

  const deviceColumns: ColumnsType<Device> = [
    {
      title: '设备信息',
      key: 'deviceInfo',
      render: (_, record) => (
        <Space>
          {record.pic ? (
            <Image
              width={40}
              height={40}
              src={record.pic}
              style={{ borderRadius: 4 }}
              fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RUG8G+2RhEJSJwLaIg"
            />
          ) : (
            <TabletOutlined style={{ fontSize: '32px', color: '#1890ff' }} />
          )}
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              码: {record.deviceCode}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              {record.brand} {record.model}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '分类',
      dataIndex: 'category',
      render: (category) => (
        <Tag color={getCategoryColor(category)}>
          {category || '未分类'}
        </Tag>
      ),
    },
    {
      title: '拥有者',
      key: 'owner',
      render: (_, record) => (
        record.user ? (
          <div>
            <div>{record.user.email}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              用户设备
            </div>
          </div>
        ) : (
          <Tag color="blue">系统设备</Tag>
        )
      ),
    },
    {
      title: '功能数量',
      key: 'functionCount',
      render: (_, record) => (
        <Badge 
          count={record.functions?.length || 0} 
          style={{ backgroundColor: '#52c41a' }}
        />
      ),
    },
    {
      title: '启用状态',
      dataIndex: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '最后连接',
      dataIndex: 'lastConnectedAt',
      render: (date) => date ? dayjs(date).format('MM-DD HH:mm') : '从未连接',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="功能管理">
            <Button 
              type="link" 
              icon={<SettingOutlined />}
              onClick={() => handleViewFunctions(record)}
            />
          </Tooltip>
          <Tooltip title="模式管理">
            <Button 
              type="link" 
              icon={<PlayCircleOutlined />}
              onClick={() => handleViewModes(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个设备吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const functionBindingColumns: ColumnsType<DeviceFunctionBinding> = [
    {
      title: '功能名称',
      key: 'functionName',
      render: (_, record) => (
        <Space>
          <ThunderboltOutlined />
          <div>
            <div style={{ fontWeight: 500 }}>{record.function.name}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              键: {record.function.key}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '最大强度',
      key: 'maxIntensity',
      render: (_, record) => (
        <Tag color="orange">{record.function.max_intensity} 级</Tag>
      ),
    },
    {
      title: '状态',
      key: 'isActive',
      render: (_, record) => (
        <Tag color={record.isActive ? 'green' : 'red'}>
          {record.isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '描述',
      key: 'description',
      render: (_, record) => record.function.description || '无描述',
    },
    {
      title: '绑定时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Popconfirm
          title="确定解绑这个功能吗？"
          onConfirm={() => handleUnbindFunction(record.id)}
          okText="确定"
          cancelText="取消"
        >
          <Button type="link" danger size="small">
            解绑
          </Button>
        </Popconfirm>
      ),
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        玩具设备管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={5}>
          <Card>
            <Statistic
              title="总设备数"
              value={stats.totalDevices}
              prefix={<TabletOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="启用设备"
              value={stats.activeDevices}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="在线设备"
              value={stats.onlineDevices}
              prefix={<WifiOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="今日使用"
              value={stats.todayUsage}
              prefix={<HistoryOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="本月使用"
              value={stats.monthlyUsage}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索设备名称/设备码/品牌"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />
          
          <Select
            placeholder="设备分类"
            style={{ width: 120 }}
            allowClear
            value={searchParams.category}
            onChange={(value) => setSearchParams({ ...searchParams, category: value })}
          >
            <Select.Option value="震动类">震动类</Select.Option>
            <Select.Option value="加热类">加热类</Select.Option>
            <Select.Option value="按摩类">按摩类</Select.Option>
            <Select.Option value="智能类">智能类</Select.Option>
            <Select.Option value="组合类">组合类</Select.Option>
          </Select>

          <Select
            placeholder="启用状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={(value) => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          
          <Button onClick={handleReset}>
            重置
          </Button>
          
          <Button icon={<ExportOutlined />}>
            导出
          </Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增设备
          </Button>
        </Space>
      </Card>

      {/* 设备列表 */}
      <Card>
        <Table
          columns={deviceColumns}
          dataSource={devices}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 创建/编辑设备模态框 */}
      <Modal
        title={editingDevice ? '编辑设备' : '新增设备'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="deviceCode"
            label="设备码"
            rules={[{ required: true, message: '请输入设备码' }]}
          >
            <Input placeholder="例如：TOY001" disabled={!!editingDevice} />
          </Form.Item>

          <Form.Item
            name="name"
            label="设备名称"
            rules={[{ required: true, message: '请输入设备名称' }]}
          >
            <Input placeholder="例如：智能震动器 Pro" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="brand" label="品牌">
                <Input placeholder="例如：LELO" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="model" label="型号">
                <Input placeholder="例如：V2.0" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="category" label="设备分类">
            <Select placeholder="选择设备分类">
              <Select.Option value="震动类">震动类</Select.Option>
              <Select.Option value="加热类">加热类</Select.Option>
              <Select.Option value="按摩类">按摩类</Select.Option>
              <Select.Option value="智能类">智能类</Select.Option>
              <Select.Option value="组合类">组合类</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item name="description" label="设备描述">
            <TextArea 
              rows={3}
              placeholder="详细描述设备的功能和特点..."
            />
          </Form.Item>

          <Form.Item name="pic" label="设备图片">
            <Upload
              name="pic"
              listType="picture-card"
              maxCount={1}
              showUploadList={{ showPreviewIcon: true }}
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            </Upload>
          </Form.Item>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingDevice ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 设备功能管理模态框 */}
      <Modal
        title={`设备功能管理 - ${selectedDevice?.name}`}
        open={functionModalVisible}
        onCancel={() => setFunctionModalVisible(false)}
        footer={null}
        width={800}
      >
        <div>
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5}>绑定功能</Title>
            <div style={{ marginBottom: 16 }}>
              <Text type="secondary">从下列可用功能中选择要绑定的功能：</Text>
            </div>
            <Row gutter={[8, 8]}>
              {availableFunctions
                .filter(func => !deviceFunctionBindings.some(binding => binding.functionId === func.id))
                .map(func => (
                <Col key={func.id}>
                  <Button
                    size="small"
                    onClick={() => handleBindFunction(func.id)}
                    icon={<PlusOutlined />}
                  >
                    {func.name} ({func.key}) - 最大{func.maxIntensity}级
                  </Button>
                </Col>
              ))}
            </Row>
            {availableFunctions.filter(func => !deviceFunctionBindings.some(binding => binding.functionId === func.id)).length === 0 && (
              <Text type="secondary">所有可用功能已绑定</Text>
            )}
          </Card>

          <Divider />

          <Table
            columns={functionBindingColumns}
            dataSource={deviceFunctionBindings}
            rowKey="id"
            size="small"
            pagination={false}
          />
        </div>
      </Modal>

      {/* 设备模式管理模态框 */}
      <Modal
        title={`设备模式管理 - ${selectedDevice?.name}`}
        open={modeModalVisible}
        onCancel={() => setModeModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={modeForm}
          layout="vertical"
          onFinish={handleBindModes}
          initialValues={{
            modes: deviceModes
          }}
        >
          <Form.Item
            name="modes"
            label="绑定模式"
          >
            <Select
              mode="multiple"
              placeholder="选择设备支持的模式"
              style={{ width: '100%' }}
              optionFilterProp="children"
            >
              {availableModes.map(mode => (
                <Select.Option key={mode.id} value={mode.id}>
                  <Space>
                    <PlayCircleOutlined />
                    {mode.name}
                    {mode.description && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>- {mode.description}</Text>
                    )}
                  </Space>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Divider />
          
          <div style={{ marginBottom: 16 }}>
            <Title level={5}>当前绑定模式</Title>
            <Card size="small">
              {deviceModes.length > 0 ? (
                deviceModes.map(modeId => {
                  const mode = availableModes.find(m => m.id === modeId)
                  return mode ? (
                    <Tag key={modeId} style={{ marginBottom: 4 }}>
                      {mode.name}
                    </Tag>
                  ) : null
                })
              ) : (
                <Text type="secondary">暂无绑定模式</Text>
              )}
            </Card>
          </div>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModeModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存绑定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default DeviceManagement
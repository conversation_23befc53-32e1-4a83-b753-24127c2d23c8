// 模板查询参数
export interface TemplateQuery {
  category?: string;
  premium?: boolean;
  showPremium?: boolean;
  limit?: number;
  offset?: number;
}

// 模板搜索参数
export interface TemplateSearchQuery {
  q?: string; // 搜索关键词
  category?: string;
  tags?: string[];
  premium?: boolean;
  sort?: 'created' | 'name' | 'cost' | 'popular';
  order?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

// 创建模板请求
export interface CreateTemplateRequest {
  name: string;
  description?: string;
  category: string;
  previewImage?: string;
  prompt: string;
  negativePrompt?: string;
  pointsCost: number;
  isPremium?: boolean;
  isActive?: boolean;
  tags?: string[];
  settings?: {
    width?: number;
    height?: number;
    steps?: number;
    guidance?: number;
  };
}

// 更新模板请求
export interface UpdateTemplateRequest {
  name?: string;
  description?: string;
  category?: string;
  previewImage?: string;
  prompt?: string;
  negativePrompt?: string;
  pointsCost?: number;
  isPremium?: boolean;
  isActive?: boolean;
  tags?: string[];
  settings?: {
    width?: number;
    height?: number;
    steps?: number;
    guidance?: number;
  };
}

// 模板生成请求
export interface GenerateWithTemplateRequest {
  templateId: string;
  originalImageUrl?: string;
  customPrompt?: string;
}

// 模板响应（带访问权限信息）
export interface TemplateWithAccess {
  id: string;
  name: string;
  description?: string;
  category?: string;
  previewImage?: string;
  prompt: string;
  negativePrompt?: string;
  pointsCost: number;
  isPremium: boolean;
  isPublic: boolean;
  isActive: boolean;
  tags?: string[];
  settings?: any;
  usageCount: number;
  createdBy?: string;
  createdAt: Date;
  updatedAt: Date;
  // 访问权限信息
  hasAccess: boolean;
  requiresMembership: boolean;
}

// 模板列表响应
export interface TemplateListResponse {
  templates: TemplateWithAccess[];
  userMembership: {
    isMember: boolean;
    canAccessPremium: boolean;
  };
}

// 模板搜索响应
export interface TemplateSearchResponse {
  templates: TemplateWithAccess[];
  total: number;
  pagination: {
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  filters: {
    query?: string;
    category?: string;
    tags?: string[];
    premium?: boolean;
    sort: string;
    order: string;
  };
  userMembership: {
    isMember: boolean;
    canAccessPremium: boolean;
  };
}

// 分类统计信息
export interface CategoryStats {
  name: string;
  totalTemplates: number;
  premiumTemplates: number;
  regularTemplates: number;
  totalUsage: number;
  averageRating: number;
}

// 模板统计信息
export interface TemplateStats {
  overview: {
    totalTemplates: number;
    activeTemplates: number;
    inactiveTemplates: number;
    premiumTemplates: number;
    regularTemplates: number;
    totalCategories: number;
    averageCost: number;
  };
  categoryStats: CategoryStats[];
  costDistribution: {
    low: number; // <= 5 points
    medium: number; // 6-15 points
    high: number; // > 15 points
  };
  recentActivity: {
    newTemplatesLast30Days: number;
    newTemplatesLast7Days: number;
  };
  usageStats: {
    totalGenerations: number;
    totalRevenue: number;
    averageGenerationsPerTemplate: number;
    mostPopularTemplates: any[];
    leastUsedTemplates: any[];
  };
  trends: {
    newTemplates: number;
    generationTrend: any[];
    revenueTrend: any[];
  };
  topTemplates: {
    byUsage: any[];
    byRevenue: any[];
    newest: any[];
  };
}

// 生成历史
export interface GenerationHistory {
  id: string;
  userId: string;
  templateId?: string;
  originalImageUrl?: string;
  generatedImageUrl?: string;
  prompt?: string;
  negativePrompt?: string;
  pointsUsed: number;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  errorMessage?: string;
  generationTime?: number;
  metadata?: any;
  createdAt: Date;
  completedAt?: Date;
}

// 生成响应
export interface GenerationResponse {
  generationId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  template: {
    id: string;
    name: string;
    pointsCost: number;
  };
  pointsUsed: number;
  remainingPoints: number;
  generatedImageUrl?: string;
  errorMessage?: string;
}

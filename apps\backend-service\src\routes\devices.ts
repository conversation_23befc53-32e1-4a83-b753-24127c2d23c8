import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { languageMiddleware } from '@/middleware/language';
import type { Env } from '@/types/env';
import { getDeviceByCode, getSystemDevices } from '@/lib/db/queries/devices';

const app = new Hono<{
  Bindings: Env;
}>();

// ==================== 设备信息查询 ====================

// 根据设备码获取设备信息（用于前端连接设备）
const getDeviceSchema = z.object({
  deviceCode: z.string().min(1).max(20),
});

app.post('/connect', languageMiddleware, zValidator('json', getDeviceSchema), async (c) => {
  try {
    const { deviceCode } = c.req.valid('json');
    const t = c.get('t');

    // 验证设备码
    if (!deviceCode || deviceCode.trim().length === 0) {
      return c.json({ error: t('device_code_required') }, 400);
    }
    if (deviceCode.length > 20) {
      return c.json({ error: t('device_code_too_long') }, 400);
    }

    // 查找设备
    const deviceInfo = await getDeviceByCode(c.env, deviceCode);

    if (!deviceInfo) {
      return c.json({ error: t('device_code_invalid') }, 404);
    }

    // 转换为前端需要的格式
    const frontendDevice = {
      pic: deviceInfo.pic || '',
      name: deviceInfo.name,
      func: deviceInfo.functions.map((func: any) => ({
        name: func.name,
        key: func.key,
        commands: func.commands.map((cmd: any) => ({
          intensity: cmd.intensity,
          command: cmd.command,
        })),
      })),
    };

    return c.json({
      success: true,
      device: frontendDevice,
      message: t('device_info_success'),
    });
  } catch (error) {
    const t = c.get('t');
    console.error('获取设备信息失败:', error);
    return c.json({ error: t('device_info_failed') }, 500);
  }
});

// 获取系统支持的设备列表
app.get('/supported', languageMiddleware, async (c) => {
  try {
    const t = c.get('t');
    const devices = await getSystemDevices(c.env);

    // 转换为前端格式
    const supportedDevices = devices.map((device: any) => ({
      deviceCode: device.deviceCode,
      name: device.name,
      pic: device.pic || '',
      brand: device.brand,
      model: device.model,
      category: device.category,
      description: device.description,
      func: device.functions.map((func: any) => ({
        name: func.name,
        key: func.key,
        commands: func.commands.map((cmd: any) => ({
          intensity: cmd.intensity,
          command: cmd.command,
        })),
      })),
    }));

    return c.json({
      success: true,
      devices: supportedDevices,
      message: t('supported_devices_success'),
    });
  } catch (error) {
    const t = c.get('t');
    console.error('获取支持设备列表失败:', error);
    return c.json({ error: t('supported_devices_failed') }, 500);
  }
});

export default app;

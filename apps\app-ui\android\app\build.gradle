apply plugin: 'com.android.application'

android {
    namespace "com.pleasurehub.app"
    compileSdk rootProject.ext.compileSdkVersion
    
    // 添加buildFeatures配置来解决BuildConfig警告
    buildFeatures {
        buildConfig = true
    }
    
    defaultConfig {
        applicationId "com.pleasurehub.app"
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        aaptOptions {
             // Files and dirs to omit from the packaged assets dir, modified to accommodate modern web apps.
             // Default: https://android.googlesource.com/platform/frameworks/base/+/282e181b58cf72b6ca770dc7ca5f91f135444502/tools/aapt/AaptAssets.cpp#61
            ignoreAssetsPattern '!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~'
        }
        
        // 启用多 dex 支持
        multiDexEnabled true
    }
    
    buildTypes {
        debug {
            minifyEnabled false
            shrinkResources false
            // 调试版本不使用混淆，保留所有日志
            debuggable true
            
            // 调试版本的构建配置
            buildConfigField "boolean", "ENABLE_SECURITY_LOG", "true"
            buildConfigField "boolean", "ENABLE_VERBOSE_LOG", "true"
        }
        
        release {
            // 启用代码混淆和资源压缩
            minifyEnabled true
            shrinkResources true
            // 使用 R8 优化器（默认行为，无需显式设置）
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            
            // 禁用调试
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            
            // 启用 ZIP 对齐和签名
            zipAlignEnabled true
            
            // 发布版本的构建配置
            buildConfigField "boolean", "ENABLE_SECURITY_LOG", "false"
            buildConfigField "boolean", "ENABLE_VERBOSE_LOG", "false"
        }
        
        // 添加一个更安全的发布配置
        secureRelease {
            initWith release
            minifyEnabled true
            shrinkResources true
            // 使用 R8 优化器（默认行为，无需显式设置）
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro', 'proguard-security.pro'
            
            // 更严格的安全设置
            debuggable false
            jniDebuggable false
            renderscriptDebuggable false
            zipAlignEnabled true
            
            // 高安全版本的构建配置（完全禁用日志）
            buildConfigField "boolean", "ENABLE_SECURITY_LOG", "false"
            buildConfigField "boolean", "ENABLE_VERBOSE_LOG", "false"
        }
    }
    
    // 编译选项
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    
    // 打包选项
    packagingOptions {
        // 排除不必要的文件
        excludes += [
            'META-INF/DEPENDENCIES',
            'META-INF/LICENSE',
            'META-INF/LICENSE.txt',
            'META-INF/NOTICE',
            'META-INF/NOTICE.txt'
        ]
    }
}

dependencies {
    implementation "androidx.appcompat:appcompat:$androidxAppCompatVersion"
    implementation "androidx.coordinatorlayout:coordinatorlayout:$androidxCoordinatorLayoutVersion"
    implementation "androidx.core:core-splashscreen:$coreSplashScreenVersion"
    implementation project(':capacitor-android')
    testImplementation "junit:junit:$junitVersion"
    androidTestImplementation "androidx.test.ext:junit:$androidxJunitVersion"
    androidTestImplementation "androidx.test.espresso:espresso-core:$androidxEspressoCoreVersion"
    implementation project(':capacitor-cordova-android-plugins')
}

apply from: 'capacitor.build.gradle'

try {
    def servicesJSON = file('google-services.json')
    if (servicesJSON.text) {
        apply plugin: 'com.google.gms.google-services'
    }
} catch(Exception e) {
    logger.info("google-services.json not found, google-services plugin not applied. Push Notifications won't work")
}

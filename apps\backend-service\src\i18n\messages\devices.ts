// 设备相关消息
export const deviceMessages = {
  zh: {
    device_code_required: '设备码不能为空',
    device_code_too_long: '设备码长度不能超过20位',
    device_code_invalid: '设备码无效，请检查后重试',
    device_info_success: '设备信息获取成功',
    device_info_failed: '获取设备信息失败，请稍后重试',
    supported_devices_success: '获取支持设备列表成功',
    supported_devices_failed: '获取设备列表失败'
  },
  'zh-TW': {
    device_code_required: '設備碼不能為空',
    device_code_too_long: '設備碼長度不能超過20位',
    device_code_invalid: '設備碼無效，請檢查後重試',
    device_info_success: '設備資訊取得成功',
    device_info_failed: '取得設備資訊失敗，請稍後重試',
    supported_devices_success: '取得支援設備列表成功',
    supported_devices_failed: '取得設備列表失敗'
  },
  ja: {
    device_code_required: 'デバイスコードを入力してください',
    device_code_too_long: 'デバイスコードの長さは20文字以下である必要があります',
    device_code_invalid: 'デバイスコードが無効です。確認してから再試行してください',
    device_info_success: 'デバイス情報の取得に成功しました',
    device_info_failed: 'デバイス情報の取得に失敗しました。しばらく後に再試行してください',
    supported_devices_success: 'サポートデバイスリストの取得に成功しました',
    supported_devices_failed: 'デバイスリストの取得に失敗しました'
  },
  es: {
    device_code_required: 'El código del dispositivo no puede estar vacío',
    device_code_too_long:
      'La longitud del código del dispositivo no puede exceder los 20 caracteres',
    device_code_invalid: 'Código de dispositivo inválido, por favor verifique y vuelva a intentar',
    device_info_success: 'Información del dispositivo obtenida correctamente',
    device_info_failed:
      'Error al obtener la información del dispositivo, por favor inténtelo más tarde',
    supported_devices_success: 'Lista de dispositivos compatibles obtenida correctamente',
    supported_devices_failed: 'Error al obtener la lista de dispositivos'
  },
  en: {
    device_code_required: 'Device code cannot be empty',
    device_code_too_long: 'Device code length cannot exceed 20 characters',
    device_code_invalid: 'Invalid device code, please check and try again',
    device_info_success: 'Device information retrieved successfully',
    device_info_failed: 'Failed to get device information, please try again later',
    supported_devices_success: 'Supported device list retrieved successfully',
    supported_devices_failed: 'Failed to get device list'
  }
}

import { useEffect, useState } from 'react';
import { authApi, type Session } from '@/api/auth';
import { Button } from './ui/button';

export function AuthDebug() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSession = async () => {
    try {
      setIsLoading(true);
      setError(null);
      console.log('正在获取会话...');
      const userSession = await authApi.getSession();
      console.log('获取到会话:', userSession);
      setSession(userSession);
    } catch (err) {
      console.error('获取会话出错:', err);
      setError(err instanceof Error ? err.message : '获取会话失败');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSession();
  }, []);

  return (
    <div className="p-4 my-4 border rounded-md">
      <h2 className="text-lg font-bold mb-2">认证状态调试</h2>

      {isLoading ? (
        <div>加载中...</div>
      ) : (
        <div className="space-y-4">
          <div>
            <h3 className="font-semibold">会话状态:</h3>
            <pre className="p-2 mt-1 bg-muted rounded-md overflow-auto text-xs">
              {session ? JSON.stringify(session, null, 2) : '未登录'}
            </pre>
          </div>

          {error && (
            <div className="text-red-500">
              <h3 className="font-semibold">错误:</h3>
              <p>{error}</p>
            </div>
          )}

          <Button onClick={fetchSession}>刷新会话状态</Button>
        </div>
      )}
    </div>
  );
}

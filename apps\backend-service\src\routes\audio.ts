import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import type { Env } from '@/types/env';
import {
  getAudioEffects,
  getAudioEffectById,
  createAudioEffect,
  updateAudioEffect,
  deleteAudioEffect,
  incrementAudioUsage,
  getAudioCategories,
  getAudioCategoryById,
  createAudioCategory,
  updateAudioCategory,
  deleteAudioCategory,
  searchAudioByTags,
  getRandomAudioEffects,
  getAllAudioTags,
  getAudioStatistics,
  batchCreateAudioEffects,
} from '@/lib/db/queries/audio';
import { authMiddleware } from '@/middleware/auth';

const app = new Hono<{ Bindings: Env }>();

// ==================== 音频效果路由 ====================

// 音频效果查询验证
const audioEffectQuerySchema = z.object({
  page: z
    .string()
    .optional()
    .transform((val) => (val ? Number.parseInt(val) : 1)),
  pageSize: z
    .string()
    .optional()
    .transform((val) => (val ? Number.parseInt(val) : 20)),
  tags: z
    .string()
    .optional()
    .transform((val) => (val ? val.split(',').filter(Boolean) : [])),
  search: z.string().optional(),
  sortBy: z
    .enum(['id', 'duration', 'avgPitch', 'avgLoudness', 'createdAt', 'usageCount'])
    .optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  isActive: z
    .string()
    .optional()
    .transform((val) => val !== 'false'),
  categoryId: z.string().optional(),
});

// 音频效果创建验证
const createAudioEffectSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()),
  url: z.string().url(),
  duration: z.number().positive(),
  avgPitch: z.number().optional(),
  avgLoudness: z.number().optional(),
  energyVariation: z.number().optional(),
  isPublic: z.boolean().optional(),
});

// 音频效果更新验证
const updateAudioEffectSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  categoryId: z.string().optional(),
  tags: z.array(z.string()).optional(),
  url: z.string().url().optional(),
  duration: z.number().positive().optional(),
  avgPitch: z.number().optional(),
  avgLoudness: z.number().optional(),
  energyVariation: z.number().optional(),
  isPublic: z.boolean().optional(),
  isActive: z.boolean().optional(),
});

/**
 * GET /api/audio
 * 获取音频效果列表
 */
app.get('/', zValidator('query', audioEffectQuerySchema), async (c) => {
  try {
    const query = c.req.valid('query');
    const result = await getAudioEffects(c.env, query);

    return c.json(result);
  } catch (error) {
    console.error('获取音频效果列表失败:', error);
    return c.json({ error: '获取音频效果列表失败' }, 500);
  }
});

/**
 * POST /api/audio
 * 创建音频效果
 */
app.post('/', authMiddleware, zValidator('json', createAudioEffectSchema), async (c) => {
  try {
    const data = c.req.valid('json');
    const audioEffect = await createAudioEffect(c.env, data);

    return c.json({ audioEffect }, 201);
  } catch (error) {
    console.error('创建音频效果失败:', error);
    return c.json({ error: '创建音频效果失败' }, 500);
  }
});

/**
 * GET /api/audio/:id
 * 获取音频效果详情
 */
app.get('/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const audioEffect = await getAudioEffectById(c.env, id);

    if (!audioEffect) {
      return c.json({ error: '音频效果不存在' }, 404);
    }

    // 增加使用次数
    await incrementAudioUsage(c.env, id);

    return c.json({ audioEffect });
  } catch (error) {
    console.error('获取音频效果详情失败:', error);
    return c.json({ error: '获取音频效果详情失败' }, 500);
  }
});

/**
 * PUT /api/audio/:id
 * 更新音频效果
 */
app.put('/:id', authMiddleware, zValidator('json', updateAudioEffectSchema), async (c) => {
  try {
    const id = c.req.param('id');
    const data = c.req.valid('json');

    const audioEffect = await updateAudioEffect(c.env, id, data);

    if (!audioEffect) {
      return c.json({ error: '音频效果不存在' }, 404);
    }

    return c.json({ audioEffect });
  } catch (error) {
    console.error('更新音频效果失败:', error);
    return c.json({ error: '更新音频效果失败' }, 500);
  }
});

/**
 * DELETE /api/audio/:id
 * 删除音频效果（软删除）
 */
app.delete('/:id', authMiddleware, async (c) => {
  try {
    const id = c.req.param('id');
    const success = await deleteAudioEffect(c.env, id);

    if (!success) {
      return c.json({ error: '音频效果不存在' }, 404);
    }

    return c.json({ message: '音频效果已删除' });
  } catch (error) {
    console.error('删除音频效果失败:', error);
    return c.json({ error: '删除音频效果失败' }, 500);
  }
});

// ==================== 音频分类路由 ====================

// 音频分类查询验证
const audioCategoryQuerySchema = z.object({
  parentId: z
    .string()
    .optional()
    .transform((val) => (val === 'null' ? null : val)),
  isActive: z
    .string()
    .optional()
    .transform((val) => val !== 'false'),
});

// 音频分类创建验证
const createAudioCategorySchema = z.object({
  name: z.string().min(1),
  displayName: z.string().optional(),
  description: z.string().optional(),
  parentId: z.string().optional(),
  sortOrder: z.number().optional(),
});

// 音频分类更新验证
const updateAudioCategorySchema = z.object({
  name: z.string().min(1).optional(),
  displayName: z.string().optional(),
  description: z.string().optional(),
  parentId: z.string().optional(),
  sortOrder: z.number().optional(),
  isActive: z.boolean().optional(),
});

/**
 * GET /api/audio/categories
 * 获取音频分类列表
 */
app.get('/categories', zValidator('query', audioCategoryQuerySchema), async (c) => {
  try {
    const query = c.req.valid('query');
    const categories = await getAudioCategories(c.env, query);

    return c.json({ categories });
  } catch (error) {
    console.error('获取音频分类列表失败:', error);
    return c.json({ error: '获取音频分类列表失败' }, 500);
  }
});

/**
 * POST /api/audio/categories
 * 创建音频分类
 */
app.post(
  '/categories',
  authMiddleware,
  zValidator('json', createAudioCategorySchema),
  async (c) => {
    try {
      const data = c.req.valid('json');
      const category = await createAudioCategory(c.env, data);

      return c.json({ category }, 201);
    } catch (error) {
      console.error('创建音频分类失败:', error);
      return c.json({ error: '创建音频分类失败' }, 500);
    }
  }
);

/**
 * GET /api/audio/categories/:id
 * 获取音频分类详情
 */
app.get('/categories/:id', async (c) => {
  try {
    const id = c.req.param('id');
    const category = await getAudioCategoryById(c.env, id);

    if (!category) {
      return c.json({ error: '音频分类不存在' }, 404);
    }

    return c.json({ category });
  } catch (error) {
    console.error('获取音频分类详情失败:', error);
    return c.json({ error: '获取音频分类详情失败' }, 500);
  }
});

/**
 * PUT /api/audio/categories/:id
 * 更新音频分类
 */
app.put(
  '/categories/:id',
  authMiddleware,
  zValidator('json', updateAudioCategorySchema),
  async (c) => {
    try {
      const id = c.req.param('id');
      const data = c.req.valid('json');

      const category = await updateAudioCategory(c.env, id, data);

      if (!category) {
        return c.json({ error: '音频分类不存在' }, 404);
      }

      return c.json({ category });
    } catch (error) {
      console.error('更新音频分类失败:', error);
      return c.json({ error: '更新音频分类失败' }, 500);
    }
  }
);

/**
 * DELETE /api/audio/categories/:id
 * 删除音频分类（软删除）
 */
app.delete('/categories/:id', authMiddleware, async (c) => {
  try {
    const id = c.req.param('id');
    const success = await deleteAudioCategory(c.env, id);

    if (!success) {
      return c.json({ error: '音频分类不存在' }, 404);
    }

    return c.json({ message: '音频分类已删除' });
  } catch (error) {
    console.error('删除音频分类失败:', error);
    return c.json({ error: '删除音频分类失败' }, 500);
  }
});

// ==================== 搜索和其他功能路由 ====================

/**
 * GET /api/audio/search
 * 根据标签搜索音频
 */
app.get(
  '/search',
  zValidator(
    'query',
    z.object({
      tags: z
        .string()
        .min(1)
        .transform((val) => val.split(',').filter(Boolean)),
      limit: z
        .string()
        .optional()
        .transform((val) => (val ? Number.parseInt(val) : 20)),
    })
  ),
  async (c) => {
    try {
      const { tags, limit } = c.req.valid('query');
      const result = await searchAudioByTags(c.env, tags, { limit });

      return c.json(result);
    } catch (error) {
      console.error('搜索音频失败:', error);
      return c.json({ error: '搜索音频失败' }, 500);
    }
  }
);

/**
 * GET /api/audio/random
 * 获取随机音频
 */
app.get(
  '/random',
  zValidator(
    'query',
    z.object({
      limit: z
        .string()
        .optional()
        .transform((val) => (val ? Number.parseInt(val) : 10)),
    })
  ),
  async (c) => {
    try {
      const { limit } = c.req.valid('query');
      const result = await getRandomAudioEffects(c.env, { limit });

      return c.json(result);
    } catch (error) {
      console.error('获取随机音频失败:', error);
      return c.json({ error: '获取随机音频失败' }, 500);
    }
  }
);

/**
 * GET /api/audio/tags
 * 获取所有标签
 */
app.get('/tags', async (c) => {
  try {
    const tags = await getAllAudioTags(c.env);

    return c.json({ tags });
  } catch (error) {
    console.error('获取标签列表失败:', error);
    return c.json({ error: '获取标签列表失败' }, 500);
  }
});

/**
 * GET /api/audio/stats
 * 获取音频统计信息
 */
app.get('/stats', async (c) => {
  try {
    const stats = await getAudioStatistics(c.env);

    return c.json({ stats });
  } catch (error) {
    console.error('获取音频统计信息失败:', error);
    return c.json({ error: '获取音频统计信息失败' }, 500);
  }
});

/**
 * POST /api/audio/batch
 * 批量创建音频效果
 */
app.post(
  '/batch',
  authMiddleware,
  zValidator(
    'json',
    z.object({
      audioEffects: z.array(createAudioEffectSchema),
    })
  ),
  async (c) => {
    try {
      const { audioEffects } = c.req.valid('json');
      const result = await batchCreateAudioEffects(c.env, audioEffects);

      return c.json(
        {
          audioEffects: result,
          count: result.length,
        },
        201
      );
    } catch (error) {
      console.error('批量创建音频效果失败:', error);
      return c.json({ error: '批量创建音频效果失败' }, 500);
    }
  }
);

export default app;

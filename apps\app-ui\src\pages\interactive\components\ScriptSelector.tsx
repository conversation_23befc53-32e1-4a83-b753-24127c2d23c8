import React, { useState, useEffect, useCallback } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import confetti from 'canvas-confetti'
import { type Script, scriptService } from '@/api/services/scripts'
import { useScriptStore } from '@/stores/script-store'
import { useScriptDownloadStore } from '@/stores/script-download-store'
import { scriptPurchaseService } from '@/api/services/script-purchase'
import { PullToRefresh } from '@/components/ui/PullToRefresh'
import { ScriptDetailModal } from './ScriptDetailModal'
import { PurchaseSuccessModal } from '@/components/ui/PurchaseSuccessModal'
import { addToast, Progress } from '@heroui/react'
import { useDeviceStore } from '@/stores/device-store'

interface ScriptSelectorProps {
  onScriptSelect?: (scriptId: string) => void
}

/**
 * 剧本选择组件
 * 支持浏览和选择不同的剧本，集成缓存和下拉刷新功能
 */
export const ScriptSelector: React.FC<ScriptSelectorProps> = ({ onScriptSelect }) => {
  const navigate = useNavigate()
  const { t } = useTranslation('interactive')
  const [scripts, setScripts] = useState<Script[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [isModalOpen, setIsModalOpen] = useState(false)

  // 使用 Zustand store
  const { getScripts, refreshScripts, getLoadingState } = useScriptStore()
  const { downloadScript, isScriptDownloaded, getDownloadProgress } = useScriptDownloadStore()

  // 获取设备连接状态
  const { connectedDevice } = useDeviceStore()

  // 购买相关状态
  const [purchasedScriptIds, setPurchasedScriptIds] = useState<Set<string>>(new Set())
  const [showPurchaseConfirm, setShowPurchaseConfirm] = useState(false)
  const [purchasingScript, setPurchasingScript] = useState<Script | null>(null)
  const [purchasingScriptId, setPurchasingScriptId] = useState<string | null>(null)
  const [showPurchaseSuccess, setShowPurchaseSuccess] = useState(false)

  const { isLoading, error } = getLoadingState()

  // 加载购买状态
  const loadPurchaseStatus = useCallback(async () => {
    try {
      const response = await scriptPurchaseService.getPurchasedScriptIds()
      if (response.success) {
        setPurchasedScriptIds(new Set(response.data.scriptIds))
      }
    } catch (err) {
      console.error(`${t('errors.purchaseScriptFailed')}`, err)
    }
  }, [])

  // 初始化加载剧本数据
  useEffect(() => {
    const loadScripts = async () => {
      try {
        const scriptList = await getScripts()
        setScripts(scriptList)
        // 同时加载购买状态
        await loadPurchaseStatus()
      } catch (err) {
        console.error(`${t('errors.getScriptsFailed')}`, err)
      }
    }

    loadScripts()
  }, [getScripts, loadPurchaseStatus])

  // 下拉刷新处理函数
  const handleRefresh = useCallback(async () => {
    try {
      const scriptList = await refreshScripts()
      setScripts(scriptList)
      // 同时刷新购买状态
      await loadPurchaseStatus()
    } catch (err) {
      console.error(`${t('errors.refreshScriptsFailed')}`, err)
      // 刷新失败时不显示错误，因为可能还有缓存数据可用
    }
  }, [refreshScripts, loadPurchaseStatus])

  // 处理剧本卡片点击 - 显示详情模态框
  const handleScriptCardClick = (index: number) => {
    setSelectedIndex(index)
    setIsModalOpen(true)
  }

  // 开始剧情（下载内容并导航）
  const handleStartScript = useCallback(
    async (script: Script) => {
      try {
        // 检查是否已下载
        if (!(await isScriptDownloaded(script.id))) {
          // 下载剧本内容
          await downloadScript(script.id, script.title)
        }

        // 如果已连接设备，显示设备连接提示
        if (connectedDevice) {
          addToast({
            title: t('toast.deviceConnected.title'),
            description: t('toast.deviceConnected.description', {
              deviceName: connectedDevice.name,
              scriptTitle: script.title
            }),
            color: 'success'
          })
        }

        // 关闭弹窗
        setIsModalOpen(false)

        // 导航或调用回调
        if (onScriptSelect) {
          onScriptSelect(script.id)
        } else {
          // 在实际跳转路由之前，异步更新剧本使用次数
          scriptService.useScript(script.id).catch(error => {
            console.error(`${t('errors.updateScriptUsageFailed')}`, error)
          })

          navigate(`/interactive/player/${script.id}`)
        }
      } catch (err) {
        console.error(`${t('errors.startScriptFailed')}`, err)
        // 错误处理已在ScriptDetailModal中处理
      }
    },
    [isScriptDownloaded, downloadScript, navigate, onScriptSelect, connectedDevice]
  )

  // 判断剧本的按钮状态
  const getScriptButtonState = useCallback(
    (script: Script) => {
      const isPurchased = purchasedScriptIds.has(script.id)
      const downloadProgress = getDownloadProgress(script.id)
      const isDownloading =
        downloadProgress &&
        (downloadProgress.status === 'downloading' || downloadProgress.status === 'pending')
      const isPurchasing = purchasingScriptId === script.id

      if (script.pointsCost === 0) {
        // 免费剧本
        if (isDownloading) {
          const progressText = downloadProgress?.progress
            ? `${Math.round(downloadProgress.progress)}%`
            : '下载中...'
          return {
            type: 'downloading',
            text: progressText,
            disabled: true,
            progress: downloadProgress
          }
        }
        return { type: 'start', text: '开始剧情', disabled: false }
      }

      if (!isPurchased) {
        // 未购买的付费剧本
        if (isPurchasing) {
          return { type: 'purchasing', text: '购买中...', disabled: true }
        }
        return { type: 'purchase', text: `${script.pointsCost} 积分`, disabled: false }
      }

      // 已购买的剧本
      if (isDownloading) {
        const progressText = downloadProgress?.progress
          ? `${Math.round(downloadProgress.progress)}%`
          : '下载中...'
        return {
          type: 'downloading',
          text: progressText,
          disabled: true,
          progress: downloadProgress
        }
      }
      return { type: 'start', text: '开始剧情', disabled: false }
    },
    [purchasedScriptIds, getDownloadProgress, purchasingScriptId]
  )

  // 处理剧本选择确认（从模态框中调用）
  const handleScriptSelect = (scriptId: string) => {
    // 导航或调用回调
    if (onScriptSelect) {
      onScriptSelect(scriptId)
    } else {
      // 在实际跳转路由之前，异步更新剧本使用次数
      scriptService.useScript(scriptId).catch(error => {
        console.error('更新剧本使用次数失败:', error)
      })

      navigate(`/interactive/player/${scriptId}`)
    }
  }

  // 处理列表中按钮点击
  const handleListButtonClick = (script: Script) => {
    const buttonState = getScriptButtonState(script)

    if (buttonState.type === 'purchase') {
      // 未购买的付费剧本 - 直接弹出购买确认弹窗
      setPurchasingScript(script)
      setShowPurchaseConfirm(true)
    } else if (buttonState.type === 'start') {
      // 免费剧本或已购买的剧本 - 直接开始
      handleStartScript(script)
    }
    // downloading 状态下按钮是禁用的，不需要处理
  }

  // 购买剧本
  const handlePurchaseScript = useCallback(
    async (script: Script) => {
      if (purchasingScriptId) return // 防止重复购买

      try {
        setPurchasingScriptId(script.id)

        const response = await scriptPurchaseService.purchaseScript(script.id)

        if (response.success) {
          // 购买成功
          setPurchasedScriptIds(prev => new Set([...prev, script.id]))
          setShowPurchaseConfirm(false)

          // 触发纸屑特效
          confetti({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 },
            colors: ['#ff2d97', '#892fff', '#ffd700', '#ff6b35', '#00d4ff']
          })

          // 延迟一点再发射第二波
          setTimeout(() => {
            confetti({
              particleCount: 50,
              spread: 60,
              origin: { y: 0.7 },
              colors: ['#ff2d97', '#892fff', '#ffd700', '#ff6b35', '#00d4ff']
            })
          }, 250)

          // 显示购买成功弹窗
          setShowPurchaseSuccess(true)

          addToast({
            title: t('toast.purchaseSuccess.title'),
            description: t('toast.purchaseSuccess.description', { scriptTitle: script.title }),
            color: 'success'
          })
        }
      } catch (err) {
        console.error(`${t('errors.purchaseScriptFailed')}`, err)

        // 根据错误类型显示不同的提示
        let errorMessage = t('errors.unknown')
        if (err instanceof Error) {
          if (err.message.includes('积分不足') || err.message.includes('insufficient')) {
            errorMessage = t('toast.insufficientPoints.description')
          } else if (err.message.includes('网络') || err.message.includes('network')) {
            errorMessage = t('toast.networkError.description')
          } else {
            errorMessage = err.message
          }
        }

        addToast({
          title: t('toast.purchaseFailed.title'),
          description: errorMessage,
          color: 'danger'
        })

        // 购买失败时关闭确认弹窗
        setShowPurchaseConfirm(false)
        setPurchasingScript(null)
      } finally {
        setPurchasingScriptId(null)
      }
    },
    [purchasingScriptId]
  )

  // 处理购买成功（从详情弹窗调用）
  const handlePurchaseSuccess = (scriptId: string) => {
    setPurchasedScriptIds(prev => new Set([...prev, scriptId]))
  }

  if (isLoading && scripts.length === 0) {
    return (
      <div className="flex justify-center items-center h-60">
        <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-pink-500" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-60 text-center">
        <p className="text-red-500 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 bg-pink-500 text-white rounded hover:bg-pink-600"
        >
          重试
        </button>
      </div>
    )
  }

  return (
    <>
      <PullToRefresh
        onRefresh={handleRefresh}
        className="min-h-screen"
        pullText={t('scriptSelector.pullToRefresh.pullDown')}
        releaseText={t('scriptSelector.pullToRefresh.release')}
        refreshingText={t('scriptSelector.pullToRefresh.refreshing')}
        data-swipe-disabled="true"
      >
        <div className="flex flex-col mx-auto">
          <div className="mx-2 mb-4">
            <h2 className="text-2xl font-bold text-white mb-2">{t('scriptSelector.title')}</h2>
            <p className="text-gray-400">{t('scriptSelector.subtitle')}</p>
          </div>

          {/* 剧本列表 - 使用flexbox布局代替absolute定位 */}
          <div className="space-y-0">
            {scripts.map((script, index) => (
              <motion.div
                key={script.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div
                  className="w-full bg-background cursor-pointer hover:bg-[#1a1e2e] transition-colors duration-200 flex items-start p-4"
                  onClick={() => handleScriptCardClick(index)}
                >
                  {/* 左侧：剧本封面图片 - 添加共享元素动画 */}
                  <motion.div
                    className="w-20 h-[107px] bg-neutral-100 rounded-xl overflow-hidden flex-shrink-0"
                    layoutId={`script-image-${script.id}`}
                  >
                    <img
                      src={script.coverImage}
                      alt={script.title}
                      className="w-full h-full object-cover object-top"
                    />
                  </motion.div>

                  {/* 中间：剧本信息区域 */}
                  <div className="flex-1 ml-4 mr-4 flex flex-col justify-between h-full py-1">
                    {/* 上半部分：标题和描述 */}
                    <div>
                      <motion.h3
                        className="text-white text-[16px] font-semibold leading-normal mb-1 line-clamp-1"
                        layoutId={`script-title-${script.id}`}
                      >
                        {script.title}
                      </motion.h3>
                      <p className="text-[#7c85b6] text-[12px] font-normal leading-normal line-clamp-2">
                        {script.description}
                      </p>
                    </div>

                    {/* 下半部分：标签 */}
                    <div className="flex items-center gap-1 mt-2">
                      {script.tags?.slice(0, 3).map((tag: string) => (
                        <div key={tag} className="bg-[#33395c] rounded-xl px-2 py-px">
                          <span className="text-[#7c85b6] text-[12px] font-normal whitespace-nowrap">
                            {tag}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* 右侧：按钮 */}
                  <div className="flex-shrink-0">
                    {(() => {
                      const buttonState = getScriptButtonState(script)
                      return (
                        <div
                          className={`h-8 rounded-xl flex items-center justify-center cursor-pointer px-3 min-w-[68px] relative overflow-hidden ${
                            buttonState.disabled ? 'opacity-50 cursor-not-allowed' : ''
                          }`}
                          style={{
                            backgroundImage:
                              'linear-gradient(123.69deg, rgb(255, 45, 151) 78.526%, rgb(137, 47, 255) 95.522%)'
                          }}
                          onClick={e => {
                            e.stopPropagation()
                            if (!buttonState.disabled) {
                              handleListButtonClick(script)
                            }
                          }}
                        >
                          {/* 下载进度条 */}
                          {buttonState.type === 'downloading' && buttonState.progress && (
                            <Progress
                              value={buttonState.progress.progress || 0}
                              className="absolute inset-0 w-full h-full"
                              classNames={{
                                base: 'max-w-none w-full h-full',
                                track: 'bg-transparent',
                                indicator: 'bg-white/20'
                              }}
                              radius="lg"
                            />
                          )}

                          {/* 按钮文字 */}
                          <span className="text-white text-[12px] font-normal whitespace-nowrap relative z-10">
                            {buttonState.text}
                          </span>

                          {/* 下载中的loading动画 */}
                          {buttonState.type === 'downloading' && (
                            <div className="ml-1 relative z-10">
                              <div className="w-3 h-3 border border-white/30 border-t-white rounded-full animate-spin" />
                            </div>
                          )}
                        </div>
                      )
                    })()}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {scripts.length === 0 && !isLoading && (
            <div className="text-center py-10">
              <p className="text-gray-400">
                {error
                  ? `${error}，${t('scriptSelector.pullToRefreshRetry')}`
                  : t('scriptSelector.noScripts')}
              </p>
            </div>
          )}
        </div>
      </PullToRefresh>

      {/* 剧本详情模态框 - 使用新的组件 */}
      <ScriptDetailModal
        isOpen={isModalOpen}
        onOpenChange={() => setIsModalOpen(false)}
        scripts={scripts}
        selectedIndex={selectedIndex}
        onScriptSelect={handleScriptSelect}
        onIndexChange={setSelectedIndex}
        shouldCloseOnSelect={!onScriptSelect} // 如果没有外部回调，则在选择后关闭模态框
        purchasedScriptIds={purchasedScriptIds}
        onPurchaseSuccess={handlePurchaseSuccess}
      />

      {/* 购买确认弹窗 */}
      {showPurchaseConfirm && purchasingScript && (
        <div
          className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
          onClick={e => {
            // 点击背景时，如果不在购买中，则关闭弹窗
            if (e.target === e.currentTarget && !purchasingScriptId) {
              setShowPurchaseConfirm(false)
              setPurchasingScript(null)
            }
          }}
        >
          <div className="bg-[#1a1e2e] rounded-2xl p-6 max-w-sm w-full mx-4">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto mb-4 rounded-xl overflow-hidden">
                <img
                  src={purchasingScript.coverImage}
                  alt={purchasingScript.title}
                  className="w-full h-full object-cover object-top"
                />
              </div>
              <h3 className="text-white text-lg font-semibold mb-2">
                {t('purchaseConfirmModal.title')}
              </h3>
              <p className="text-gray-400 text-sm mb-2">《{purchasingScript.title}》</p>
              <p className="text-gray-400 text-sm mb-6">
                {t('purchaseConfirmModal.pointsCost', {
                  pointsCost: (
                    <span className="text-[#ff2d97] font-semibold">
                      {purchasingScript.pointsCost}
                    </span>
                  )
                })}
              </p>
              <p className="text-gray-500 text-xs mb-6">
                {t('purchaseConfirmModal.validityPeriod')}
              </p>
              <div className="flex gap-3">
                <button
                  className="flex-1 py-3 px-4 bg-gray-600 text-white rounded-xl hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={!!purchasingScriptId} // 购买中时禁用取消按钮
                  onClick={() => {
                    if (!purchasingScriptId) {
                      setShowPurchaseConfirm(false)
                      setPurchasingScript(null)
                    }
                  }}
                >
                  {t('purchaseConfirmModal.cancelButton')}
                </button>
                <button
                  className="flex-1 py-3 px-4 text-white rounded-xl transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    backgroundImage:
                      'linear-gradient(123.69deg, rgb(255, 45, 151) 78.526%, rgb(137, 47, 255) 95.522%)'
                  }}
                  disabled={!!purchasingScriptId}
                  onClick={() => {
                    if (!purchasingScriptId) {
                      handlePurchaseScript(purchasingScript)
                    }
                  }}
                >
                  {purchasingScriptId === purchasingScript.id
                    ? t('purchaseConfirmModal.purchasingButton')
                    : t('purchaseConfirmModal.confirmButton')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 购买成功弹窗 */}
      {showPurchaseSuccess && purchasingScript && (
        <PurchaseSuccessModal
          isOpen={showPurchaseSuccess}
          onClose={() => {
            setShowPurchaseSuccess(false)
            setPurchasingScript(null)
          }}
          onStartScript={() => {
            setShowPurchaseSuccess(false)
            handleStartScript(purchasingScript)
            setPurchasingScript(null)
          }}
          scriptTitle={purchasingScript.title}
          pointsCost={purchasingScript.pointsCost}
        />
      )}
    </>
  )
}

import { useState, useEffect, useRef } from 'react';
import type { Dialogue, Stage } from '../types';
import { timeToSeconds } from '../utils/timeUtils';

interface UseDialogueManagerProps {
  currentStage: Stage | null;
  currentTime: number;
}

interface UseDialogueManagerReturn {
  currentDialogues: Dialogue[];
  activeDialogue: Dialogue | null;
  nextDialogue: Dialogue | null;
  timeToNextDialogue: number | null;
}

/**
 * 对话管理Hook
 * 根据当前时间管理显示哪些对话
 */
export const useDialogueManager = ({
  currentStage,
  currentTime,
}: UseDialogueManagerProps): UseDialogueManagerReturn => {
  const [currentDialogues, setCurrentDialogues] = useState<Dialogue[]>([]);
  const [activeDialogue, setActiveDialogue] = useState<Dialogue | null>(null);
  const [nextDialogue, setNextDialogue] = useState<Dialogue | null>(null);
  const [timeToNextDialogue, setTimeToNextDialogue] = useState<number | null>(
    null,
  );

  // 添加强制刷新标志，用于解决对话不更新问题
  const [forceRefresh, setForceRefresh] = useState(0);

  // 添加上一次时间的引用，用于检测大幅度时间变化
  const lastTimeRef = useRef<number>(currentTime);
  // 添加上一个阶段的引用，用于检测阶段变化
  const lastStageRef = useRef<Stage | null>(currentStage);

  // 监听对话重置事件 - 当阶段切换时强制重置对话状态
  useEffect(() => {
    const handleDialogueReset = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail) {
        // 提取事件详情
        const { time, reason, stageChange } = customEvent.detail;

        // 清空当前对话列表
        setCurrentDialogues([]);
        setActiveDialogue(null);

        // 强制刷新对话状态
        setForceRefresh((prev) => prev + 1);

        // 如果是由于seek操作导致的重置，更新lastTimeRef以防止再次触发
        if (reason === 'seek' && time !== undefined) {
          lastTimeRef.current = time;
        }

        // 如果是阶段变化，更新阶段引用
        if (stageChange && currentStage) {
          lastStageRef.current = currentStage;
        }
      }
    };

    document.addEventListener('dialogueReset', handleDialogueReset);

    return () => {
      document.removeEventListener('dialogueReset', handleDialogueReset);
    };
  }, [currentStage]);

  // 检测大幅度的时间变化或阶段变化
  useEffect(() => {
    // 检查时间是否有大幅跳跃（超过2秒）或阶段变化
    const timeDiff = Math.abs(currentTime - lastTimeRef.current);
    const isStageChanged = currentStage !== lastStageRef.current;

    // 添加强力刷新机制，如果没有活跃对话，也触发刷新
    const needsRefresh = timeDiff > 2 || isStageChanged || !activeDialogue;

    if (needsRefresh) {
      // 清空对话状态
      setCurrentDialogues([]);
      setActiveDialogue(null);

      // 强制刷新对话状态
      setForceRefresh((prev) => prev + 1);

      // 发出对话重置事件，确保其他组件也能响应
      const resetEvent = new CustomEvent('dialogueReset', {
        detail: { time: currentTime, stageChange: isStageChanged },
      });
      document.dispatchEvent(resetEvent);
    }

    // 更新引用值
    lastTimeRef.current = currentTime;
    lastStageRef.current = currentStage;
  }, [currentTime, currentStage, activeDialogue]);

  // 根据当前时间更新对话 - 直接使用forceRefresh作为依赖项，确保强制刷新能生效
  useEffect(() => {
    if (!currentStage) {
      setCurrentDialogues([]);
      setActiveDialogue(null);
      setNextDialogue(null);
      setTimeToNextDialogue(null);
      return;
    }

    // 直接获取当前时间之前的所有对话
    const dialoguesBeforeCurrentTime = [...currentStage.dialogues].filter(
      (dialogue) => timeToSeconds(dialogue.time) <= currentTime,
    );

    // 按时间降序排序，以便获取最近的对话
    dialoguesBeforeCurrentTime.sort(
      (a, b) => timeToSeconds(b.time) - timeToSeconds(a.time),
    );

    // 获取最新的对话（时间最近的）
    const latestDialogue =
      dialoguesBeforeCurrentTime.length > 0
        ? dialoguesBeforeCurrentTime[0]
        : null;

    // 获取要显示的对话（当前最新对话及之前的几个对话，形成对话历史）
    let visibleDialogues: Dialogue[] = [];
    if (latestDialogue) {
      // 找到最新对话在原始数组中的索引
      const originalIndex = currentStage.dialogues.findIndex(
        (d) => d === latestDialogue,
      );

      // 获取最近的3个对话作为历史记录
      if (originalIndex >= 0) {
        const startIndex = Math.max(0, originalIndex - 2); // 最多显示3个对话
        visibleDialogues = currentStage.dialogues.slice(
          startIndex,
          originalIndex + 1,
        );
      } else {
        visibleDialogues = [latestDialogue];
      }
    }

    // 查找下一个对话
    const next =
      currentStage.dialogues.find(
        (dialogue) => timeToSeconds(dialogue.time) > currentTime,
      ) || null;

    // 计算到下一个对话的时间
    const timeToNext = next ? timeToSeconds(next.time) - currentTime : null;

    // 更新状态
    setCurrentDialogues(visibleDialogues);
    setActiveDialogue(latestDialogue);
    setNextDialogue(next);
    setTimeToNextDialogue(timeToNext);
  }, [currentStage, currentTime, forceRefresh]); // 添加forceRefresh作为依赖

  return {
    currentDialogues,
    activeDialogue,
    nextDialogue,
    timeToNextDialogue,
  };
};

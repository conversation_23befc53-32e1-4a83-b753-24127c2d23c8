{"id": "cd763d17-0bc4-4bcc-8afe-4b904294b0e1", "prevId": "ca708346-d516-4a2f-bf17-4096a44c8ada", "version": "7", "dialect": "postgresql", "tables": {"public.ActivationCode": {"name": "ActivationCode", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "membership_plan_id": {"name": "membership_plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "points_package_id": {"name": "points_package_id", "type": "uuid", "primaryKey": false, "notNull": false}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "used_by": {"name": "used_by", "type": "uuid", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "batch_id": {"name": "batch_id", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_activation_code_code": {"name": "idx_activation_code_code", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_type": {"name": "idx_activation_code_type", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_active": {"name": "idx_activation_code_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"ActivationCode\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_used": {"name": "idx_activation_code_used", "columns": [{"expression": "is_used", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_expires_at": {"name": "idx_activation_code_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_batch_id": {"name": "idx_activation_code_batch_id", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_created_by": {"name": "idx_activation_code_created_by", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_created_at": {"name": "idx_activation_code_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_used_by": {"name": "idx_activation_code_used_by", "columns": [{"expression": "used_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_membership_plan": {"name": "idx_activation_code_membership_plan", "columns": [{"expression": "membership_plan_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_points_package": {"name": "idx_activation_code_points_package", "columns": [{"expression": "points_package_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ActivationCode_membership_plan_id_MembershipPlan_id_fk": {"name": "ActivationCode_membership_plan_id_MembershipPlan_id_fk", "tableFrom": "ActivationCode", "tableTo": "MembershipPlan", "columnsFrom": ["membership_plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ActivationCode_points_package_id_PointsPackage_id_fk": {"name": "ActivationCode_points_package_id_PointsPackage_id_fk", "tableFrom": "ActivationCode", "tableTo": "PointsPackage", "columnsFrom": ["points_package_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ActivationCode_used_by_User_id_fk": {"name": "ActivationCode_used_by_User_id_fk", "tableFrom": "ActivationCode", "tableTo": "User", "columnsFrom": ["used_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ActivationCode_created_by_User_id_fk": {"name": "ActivationCode_created_by_User_id_fk", "tableFrom": "ActivationCode", "tableTo": "User", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"ActivationCode_code_unique": {"name": "ActivationCode_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}}, "public.ActivationCodeUsage": {"name": "ActivationCodeUsage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "activation_code_id": {"name": "activation_code_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "result_type": {"name": "result_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "result_id": {"name": "result_id", "type": "uuid", "primaryKey": false, "notNull": false}, "original_membership_id": {"name": "original_membership_id", "type": "uuid", "primaryKey": false, "notNull": false}, "conflict_resolution": {"name": "conflict_resolution", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_activation_code_usage_code_id": {"name": "idx_activation_code_usage_code_id", "columns": [{"expression": "activation_code_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_usage_user_id": {"name": "idx_activation_code_usage_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_usage_result_type": {"name": "idx_activation_code_usage_result_type", "columns": [{"expression": "result_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_usage_used_at": {"name": "idx_activation_code_usage_used_at", "columns": [{"expression": "used_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_usage_code_user": {"name": "idx_activation_code_usage_code_user", "columns": [{"expression": "activation_code_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_usage_user_used_at": {"name": "idx_activation_code_usage_user_used_at", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "used_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_activation_code_usage_unique_user_code": {"name": "idx_activation_code_usage_unique_user_code", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "activation_code_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ActivationCodeUsage_activation_code_id_ActivationCode_id_fk": {"name": "ActivationCodeUsage_activation_code_id_ActivationCode_id_fk", "tableFrom": "ActivationCodeUsage", "tableTo": "ActivationCode", "columnsFrom": ["activation_code_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ActivationCodeUsage_user_id_User_id_fk": {"name": "ActivationCodeUsage_user_id_User_id_fk", "tableFrom": "ActivationCodeUsage", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ApiUsage": {"name": "ApiUsage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "status_code": {"name": "status_code", "type": "integer", "primaryKey": false, "notNull": true}, "response_time": {"name": "response_time", "type": "integer", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_api_usage_user_id": {"name": "idx_api_usage_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_api_usage_endpoint": {"name": "idx_api_usage_endpoint", "columns": [{"expression": "endpoint", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_api_usage_created_at": {"name": "idx_api_usage_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_api_usage_status": {"name": "idx_api_usage_status", "columns": [{"expression": "status_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ApiUsage_user_id_User_id_fk": {"name": "ApiUsage_user_id_User_id_fk", "tableFrom": "ApiUsage", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AppUpdateLog": {"name": "AppUpdateLog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "device_id": {"name": "device_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "current_version": {"name": "current_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "target_version": {"name": "target_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "update_type": {"name": "update_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "update_status": {"name": "update_status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_app_update_log_user_id": {"name": "idx_app_update_log_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_update_log_device_id": {"name": "idx_app_update_log_device_id", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_update_log_update_type": {"name": "idx_app_update_log_update_type", "columns": [{"expression": "update_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_update_log_status": {"name": "idx_app_update_log_status", "columns": [{"expression": "update_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_update_log_created_at": {"name": "idx_app_update_log_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"AppUpdateLog_user_id_User_id_fk": {"name": "AppUpdateLog_user_id_User_id_fk", "tableFrom": "AppUpdateLog", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AppUpdatePolicy": {"name": "AppUpdatePolicy", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version_id": {"name": "version_id", "type": "uuid", "primaryKey": false, "notNull": true}, "channel": {"name": "channel", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'production'"}, "update_strategy": {"name": "update_strategy", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "target_version_min": {"name": "target_version_min", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "target_version_max": {"name": "target_version_max", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "rollout_percentage": {"name": "rollout_percentage", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_app_update_policy_version_id": {"name": "idx_app_update_policy_version_id", "columns": [{"expression": "version_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_update_policy_channel": {"name": "idx_app_update_policy_channel", "columns": [{"expression": "channel", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_update_policy_strategy": {"name": "idx_app_update_policy_strategy", "columns": [{"expression": "update_strategy", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_update_policy_active": {"name": "idx_app_update_policy_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"AppUpdatePolicy_version_id_AppVersion_id_fk": {"name": "AppUpdatePolicy_version_id_AppVersion_id_fk", "tableFrom": "AppUpdatePolicy", "tableTo": "AppVersion", "columnsFrom": ["version_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AppVersion": {"name": "AppVersion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "version_name": {"name": "version_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "version_code": {"name": "version_code", "type": "integer", "primaryKey": false, "notNull": true}, "version_type": {"name": "version_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "bigint", "primaryKey": false, "notNull": false}, "file_hash": {"name": "file_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "min_compatible_version": {"name": "min_compatible_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "release_notes": {"name": "release_notes", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_app_version_code": {"name": "idx_app_version_code", "columns": [{"expression": "version_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_version_type": {"name": "idx_app_version_type", "columns": [{"expression": "version_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_version_active": {"name": "idx_app_version_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_app_version_created_at": {"name": "idx_app_version_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.AudioCategory": {"name": "AudioCategory", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "parent_id": {"name": "parent_id", "type": "uuid", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_audio_category_active": {"name": "idx_audio_category_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"AudioCategory\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_audio_category_parent": {"name": "idx_audio_category_parent", "columns": [{"expression": "parent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"AudioCategory_name_unique": {"name": "AudioCategory_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}}, "public.AudioEffect": {"name": "AudioEffect", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "uuid", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'"}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "avg_pitch": {"name": "avg_pitch", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "avg_loudness": {"name": "avg_loudness", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "energy_variation": {"name": "energy_variation", "type": "numeric(10, 4)", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_audio_category": {"name": "idx_audio_category", "columns": [{"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_audio_public": {"name": "idx_audio_public", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"AudioEffect\".\"is_public\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_audio_active": {"name": "idx_audio_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"AudioEffect\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_audio_created_by": {"name": "idx_audio_created_by", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"AudioEffect_category_id_AudioCategory_id_fk": {"name": "AudioEffect_category_id_AudioCategory_id_fk", "tableFrom": "AudioEffect", "tableTo": "AudioCategory", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "AudioEffect_created_by_User_id_fk": {"name": "AudioEffect_created_by_User_id_fk", "tableFrom": "AudioEffect", "tableTo": "User", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Character": {"name": "Character", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "relationship": {"name": "relationship", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "ethnicity": {"name": "ethnicity", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "age": {"name": "age", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "eye_color": {"name": "eye_color", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "hair_style": {"name": "hair_style", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "hair_color": {"name": "hair_color", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "body_type": {"name": "body_type", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "breast_size": {"name": "breast_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "butt_size": {"name": "butt_size", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "personality": {"name": "personality", "type": "text", "primaryKey": false, "notNull": false}, "clothing": {"name": "clothing", "type": "text", "primaryKey": false, "notNull": false}, "voice": {"name": "voice", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "voice_model_id": {"name": "voice_model_id", "type": "uuid", "primaryKey": false, "notNull": false}, "keywords": {"name": "keywords", "type": "text", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_character_user_id": {"name": "idx_character_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_character_public": {"name": "idx_character_public", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"Character\".\"is_public\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_character_category": {"name": "idx_character_category", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_character_created_at": {"name": "idx_character_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_character_user_created_at": {"name": "idx_character_user_created_at", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_character_public_category": {"name": "idx_character_public_category", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"Character\".\"is_public\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Character_user_id_User_id_fk": {"name": "Character_user_id_User_id_fk", "tableFrom": "Character", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "Character_voice_model_id_VoiceModel_id_fk": {"name": "Character_voice_model_id_VoiceModel_id_fk", "tableFrom": "Character", "tableTo": "VoiceModel", "columnsFrom": ["voice_model_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Chat": {"name": "Cha<PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "visibility": {"name": "visibility", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'private'"}, "character_id": {"name": "character_id", "type": "uuid", "primaryKey": false, "notNull": false}, "background_image_url": {"name": "background_image_url", "type": "text", "primaryKey": false, "notNull": false}, "background_scene_description": {"name": "background_scene_description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_chat_user_id": {"name": "idx_chat_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_chat_created_at": {"name": "idx_chat_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_chat_updated_at": {"name": "idx_chat_updated_at", "columns": [{"expression": "updated_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_chat_user_created": {"name": "idx_chat_user_created", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Chat_user_id_User_id_fk": {"name": "Chat_user_id_User_id_fk", "tableFrom": "Cha<PERSON>", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.CommissionAccount": {"name": "CommissionAccount", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "total_earned": {"name": "total_earned", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "available_balance": {"name": "available_balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "frozen_balance": {"name": "frozen_balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "total_withdrawn": {"name": "total_withdrawn", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_commission_account_user_id": {"name": "idx_commission_account_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"CommissionAccount_user_id_User_id_fk": {"name": "CommissionAccount_user_id_User_id_fk", "tableFrom": "CommissionAccount", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"CommissionAccount_user_id_unique": {"name": "CommissionAccount_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}}, "public.CommissionRecord": {"name": "CommissionRecord", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "inviter_id": {"name": "inviter_id", "type": "uuid", "primaryKey": false, "notNull": true}, "invitee_id": {"name": "invitee_id", "type": "uuid", "primaryKey": false, "notNull": true}, "order_id": {"name": "order_id", "type": "uuid", "primaryKey": false, "notNull": true}, "commission_amount": {"name": "commission_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "source_type": {"name": "source_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "source_amount": {"name": "source_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "commission_rate": {"name": "commission_rate", "type": "numeric(5, 4)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "settled_at": {"name": "settled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_commission_record_inviter_id": {"name": "idx_commission_record_inviter_id", "columns": [{"expression": "inviter_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_commission_record_invitee_id": {"name": "idx_commission_record_invitee_id", "columns": [{"expression": "invitee_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_commission_record_order_id": {"name": "idx_commission_record_order_id", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_commission_record_status": {"name": "idx_commission_record_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_commission_record_created_at": {"name": "idx_commission_record_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"CommissionRecord_inviter_id_User_id_fk": {"name": "CommissionRecord_inviter_id_User_id_fk", "tableFrom": "CommissionRecord", "tableTo": "User", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "CommissionRecord_invitee_id_User_id_fk": {"name": "CommissionRecord_invitee_id_User_id_fk", "tableFrom": "CommissionRecord", "tableTo": "User", "columnsFrom": ["invitee_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "CommissionRecord_order_id_PaymentOrder_id_fk": {"name": "CommissionRecord_order_id_PaymentOrder_id_fk", "tableFrom": "CommissionRecord", "tableTo": "PaymentOrder", "columnsFrom": ["order_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Device": {"name": "<PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "device_code": {"name": "device_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "pic": {"name": "pic", "type": "text", "primaryKey": false, "notNull": false}, "brand": {"name": "brand", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_connected_at": {"name": "last_connected_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_device_user_id": {"name": "idx_device_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_code": {"name": "idx_device_code", "columns": [{"expression": "device_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_category": {"name": "idx_device_category", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_active": {"name": "idx_device_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_user_active": {"name": "idx_device_user_active", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Device_user_id_User_id_fk": {"name": "Device_user_id_User_id_fk", "tableFrom": "<PERSON><PERSON>", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"Device_device_code_unique": {"name": "Device_device_code_unique", "nullsNotDistinct": false, "columns": ["device_code"]}}}, "public.DeviceCommandSet": {"name": "DeviceCommandSet", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "command": {"name": "command", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "broadcast": {"name": "broadcast", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_device_command_set_name": {"name": "idx_device_command_set_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_command_set_active": {"name": "idx_device_command_set_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.DeviceConnection": {"name": "DeviceConnection", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "device_id": {"name": "device_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'connected'"}, "connected_at": {"name": "connected_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "disconnected_at": {"name": "disconnected_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}}, "indexes": {"idx_device_connection_user_id": {"name": "idx_device_connection_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_connection_device_id": {"name": "idx_device_connection_device_id", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_connection_status": {"name": "idx_device_connection_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_connection_connected_at": {"name": "idx_device_connection_connected_at", "columns": [{"expression": "connected_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_connection_user_device": {"name": "idx_device_connection_user_device", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"DeviceConnection_user_id_User_id_fk": {"name": "DeviceConnection_user_id_User_id_fk", "tableFrom": "DeviceConnection", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "DeviceConnection_device_id_Device_id_fk": {"name": "DeviceConnection_device_id_Device_id_fk", "tableFrom": "DeviceConnection", "tableTo": "<PERSON><PERSON>", "columnsFrom": ["device_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.DeviceFunction": {"name": "DeviceFunction", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "max_intensity": {"name": "max_intensity", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_device_function_key": {"name": "idx_device_function_key", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_function_active": {"name": "idx_device_function_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"DeviceFunction_key_unique": {"name": "DeviceFunction_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}}, "public.DeviceFunctionCommand": {"name": "DeviceFunctionCommand", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "function_id": {"name": "function_id", "type": "uuid", "primaryKey": false, "notNull": true}, "command_set_id": {"name": "command_set_id", "type": "uuid", "primaryKey": false, "notNull": true}, "intensity": {"name": "intensity", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_device_function_command_function_id": {"name": "idx_device_function_command_function_id", "columns": [{"expression": "function_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_function_command_command_set_id": {"name": "idx_device_function_command_command_set_id", "columns": [{"expression": "command_set_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_function_command_function_intensity": {"name": "idx_device_function_command_function_intensity", "columns": [{"expression": "function_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "intensity", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"DeviceFunctionCommand_function_id_DeviceFunction_id_fk": {"name": "DeviceFunctionCommand_function_id_DeviceFunction_id_fk", "tableFrom": "DeviceFunctionCommand", "tableTo": "DeviceFunction", "columnsFrom": ["function_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "DeviceFunctionCommand_command_set_id_DeviceCommandSet_id_fk": {"name": "DeviceFunctionCommand_command_set_id_DeviceCommandSet_id_fk", "tableFrom": "DeviceFunctionCommand", "tableTo": "DeviceCommandSet", "columnsFrom": ["command_set_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.DeviceMode": {"name": "DeviceMode", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "command_set_id": {"name": "command_set_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_device_mode_name": {"name": "idx_device_mode_name", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_mode_active": {"name": "idx_device_mode_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_mode_command_set": {"name": "idx_device_mode_command_set", "columns": [{"expression": "command_set_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"DeviceMode_command_set_id_DeviceCommandSet_id_fk": {"name": "DeviceMode_command_set_id_DeviceCommandSet_id_fk", "tableFrom": "DeviceMode", "tableTo": "DeviceCommandSet", "columnsFrom": ["command_set_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.DeviceSupportedFunction": {"name": "DeviceSupportedFunction", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "device_id": {"name": "device_id", "type": "uuid", "primaryKey": false, "notNull": true}, "function_id": {"name": "function_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_device_supported_function_device_id": {"name": "idx_device_supported_function_device_id", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_supported_function_function_id": {"name": "idx_device_supported_function_function_id", "columns": [{"expression": "function_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_supported_function_device_function": {"name": "idx_device_supported_function_device_function", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "function_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"DeviceSupportedFunction_device_id_Device_id_fk": {"name": "DeviceSupportedFunction_device_id_Device_id_fk", "tableFrom": "DeviceSupportedFunction", "tableTo": "<PERSON><PERSON>", "columnsFrom": ["device_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "DeviceSupportedFunction_function_id_DeviceFunction_id_fk": {"name": "DeviceSupportedFunction_function_id_DeviceFunction_id_fk", "tableFrom": "DeviceSupportedFunction", "tableTo": "DeviceFunction", "columnsFrom": ["function_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.DeviceSupportedMode": {"name": "DeviceSupportedMode", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "device_id": {"name": "device_id", "type": "uuid", "primaryKey": false, "notNull": true}, "mode_id": {"name": "mode_id", "type": "uuid", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_device_supported_mode_device_id": {"name": "idx_device_supported_mode_device_id", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_supported_mode_mode_id": {"name": "idx_device_supported_mode_mode_id", "columns": [{"expression": "mode_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_supported_mode_device_mode": {"name": "idx_device_supported_mode_device_mode", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "mode_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"DeviceSupportedMode_device_id_Device_id_fk": {"name": "DeviceSupportedMode_device_id_Device_id_fk", "tableFrom": "DeviceSupportedMode", "tableTo": "<PERSON><PERSON>", "columnsFrom": ["device_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "DeviceSupportedMode_mode_id_DeviceMode_id_fk": {"name": "DeviceSupportedMode_mode_id_DeviceMode_id_fk", "tableFrom": "DeviceSupportedMode", "tableTo": "DeviceMode", "columnsFrom": ["mode_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.DeviceUsage": {"name": "DeviceUsage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "device_id": {"name": "device_id", "type": "uuid", "primaryKey": false, "notNull": true}, "script_id": {"name": "script_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "function_key": {"name": "function_key", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": true}, "intensity": {"name": "intensity", "type": "integer", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}}, "indexes": {"idx_device_usage_user_id": {"name": "idx_device_usage_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_usage_device_id": {"name": "idx_device_usage_device_id", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_usage_script_id": {"name": "idx_device_usage_script_id", "columns": [{"expression": "script_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_usage_started_at": {"name": "idx_device_usage_started_at", "columns": [{"expression": "started_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_device_usage_user_device": {"name": "idx_device_usage_user_device", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"DeviceUsage_user_id_User_id_fk": {"name": "DeviceUsage_user_id_User_id_fk", "tableFrom": "DeviceUsage", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "DeviceUsage_device_id_Device_id_fk": {"name": "DeviceUsage_device_id_Device_id_fk", "tableFrom": "DeviceUsage", "tableTo": "<PERSON><PERSON>", "columnsFrom": ["device_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "DeviceUsage_script_id_Script_id_fk": {"name": "DeviceUsage_script_id_Script_id_fk", "tableFrom": "DeviceUsage", "tableTo": "<PERSON><PERSON><PERSON>", "columnsFrom": ["script_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.InviteCode": {"name": "InviteCode", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": false}, "used_count": {"name": "used_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_invite_code_user_id": {"name": "idx_invite_code_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invite_code_code": {"name": "idx_invite_code_code", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invite_code_active": {"name": "idx_invite_code_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_invite_code_expires_at": {"name": "idx_invite_code_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"InviteCode_user_id_User_id_fk": {"name": "InviteCode_user_id_User_id_fk", "tableFrom": "InviteCode", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"InviteCode_code_unique": {"name": "InviteCode_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}}, "public.MediaGeneration": {"name": "MediaGeneration", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "character_id": {"name": "character_id", "type": "uuid", "primaryKey": false, "notNull": false}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": false}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "media_type": {"name": "media_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "generation_type": {"name": "generation_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": false}, "negative_prompt": {"name": "negative_prompt", "type": "text", "primaryKey": false, "notNull": false}, "input_image_url": {"name": "input_image_url", "type": "text", "primaryKey": false, "notNull": false}, "output_urls": {"name": "output_urls", "type": "json", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "points_used": {"name": "points_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "generation_time": {"name": "generation_time", "type": "integer", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_media_gen_user_id": {"name": "idx_media_gen_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_character_id": {"name": "idx_media_gen_character_id", "columns": [{"expression": "character_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_chat_id": {"name": "idx_media_gen_chat_id", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_media_type": {"name": "idx_media_gen_media_type", "columns": [{"expression": "media_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_generation_type": {"name": "idx_media_gen_generation_type", "columns": [{"expression": "generation_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_status": {"name": "idx_media_gen_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_created_at": {"name": "idx_media_gen_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_user_character": {"name": "idx_media_gen_user_character", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "character_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_media_gen_user_media_type": {"name": "idx_media_gen_user_media_type", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "media_type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"MediaGeneration_user_id_User_id_fk": {"name": "MediaGeneration_user_id_User_id_fk", "tableFrom": "MediaGeneration", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "MediaGeneration_character_id_Character_id_fk": {"name": "MediaGeneration_character_id_Character_id_fk", "tableFrom": "MediaGeneration", "tableTo": "Character", "columnsFrom": ["character_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "MediaGeneration_chat_id_Chat_id_fk": {"name": "MediaGeneration_chat_id_Chat_id_fk", "tableFrom": "MediaGeneration", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.MembershipPlan": {"name": "MembershipPlan", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "duration_days": {"name": "duration_days", "type": "integer", "primaryKey": false, "notNull": true}, "points_included": {"name": "points_included", "type": "integer", "primaryKey": false, "notNull": true}, "features": {"name": "features", "type": "json", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_membership_active": {"name": "idx_membership_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"MembershipPlan\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Message": {"name": "Message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "parts": {"name": "parts", "type": "json", "primaryKey": false, "notNull": true}, "attachments": {"name": "attachments", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_message_chat_id": {"name": "idx_message_chat_id", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_message_created_at": {"name": "idx_message_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_message_chat_created": {"name": "idx_message_chat_created", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Message_chat_id_Chat_id_fk": {"name": "Message_chat_id_Chat_id_fk", "tableFrom": "Message", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.PaymentOrder": {"name": "PaymentOrder", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": false}, "points_package_id": {"name": "points_package_id", "type": "uuid", "primaryKey": false, "notNull": false}, "order_no": {"name": "order_no", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "external_order_id": {"name": "external_order_id", "type": "<PERSON><PERSON><PERSON>(128)", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'CNY'"}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "is_upgrade": {"name": "is_upgrade", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "original_amount": {"name": "original_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "current_subscription_id": {"name": "current_subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "callback_url": {"name": "callback_url", "type": "text", "primaryKey": false, "notNull": false}, "return_url": {"name": "return_url", "type": "text", "primaryKey": false, "notNull": false}, "notify_url": {"name": "notify_url", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'"}}, "indexes": {"idx_payment_order_user_id": {"name": "idx_payment_order_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payment_order_order_no": {"name": "idx_payment_order_order_no", "columns": [{"expression": "order_no", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payment_order_status": {"name": "idx_payment_order_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payment_order_external_id": {"name": "idx_payment_order_external_id", "columns": [{"expression": "external_order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payment_order_expires_at": {"name": "idx_payment_order_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payment_order_payment_method": {"name": "idx_payment_order_payment_method", "columns": [{"expression": "payment_method", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payment_order_created_at": {"name": "idx_payment_order_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_payment_order_user_created": {"name": "idx_payment_order_user_created", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"PaymentOrder_user_id_User_id_fk": {"name": "PaymentOrder_user_id_User_id_fk", "tableFrom": "PaymentOrder", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "PaymentOrder_plan_id_MembershipPlan_id_fk": {"name": "PaymentOrder_plan_id_MembershipPlan_id_fk", "tableFrom": "PaymentOrder", "tableTo": "MembershipPlan", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "PaymentOrder_points_package_id_PointsPackage_id_fk": {"name": "PaymentOrder_points_package_id_PointsPackage_id_fk", "tableFrom": "PaymentOrder", "tableTo": "PointsPackage", "columnsFrom": ["points_package_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"PaymentOrder_order_no_unique": {"name": "PaymentOrder_order_no_unique", "nullsNotDistinct": false, "columns": ["order_no"]}}}, "public.PointsPackage": {"name": "PointsPackage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "points": {"name": "points", "type": "integer", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "bonus_points": {"name": "bonus_points", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_points_package_active": {"name": "idx_points_package_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"PointsPackage\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.PointsTransaction": {"name": "PointsTransaction", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "transaction_type": {"name": "transaction_type", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "source_id": {"name": "source_id", "type": "uuid", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "balance_after": {"name": "balance_after", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_points_transaction_user": {"name": "idx_points_transaction_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_points_transaction_created": {"name": "idx_points_transaction_created", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_points_transaction_type": {"name": "idx_points_transaction_type", "columns": [{"expression": "transaction_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_points_transaction_user_created": {"name": "idx_points_transaction_user_created", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"PointsTransaction_user_id_User_id_fk": {"name": "PointsTransaction_user_id_User_id_fk", "tableFrom": "PointsTransaction", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ReferralRelation": {"name": "ReferralRelation", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "inviter_id": {"name": "inviter_id", "type": "uuid", "primaryKey": false, "notNull": true}, "invitee_id": {"name": "invitee_id", "type": "uuid", "primaryKey": false, "notNull": true}, "invite_code_id": {"name": "invite_code_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_referral_relation_inviter_id": {"name": "idx_referral_relation_inviter_id", "columns": [{"expression": "inviter_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_referral_relation_invitee_id": {"name": "idx_referral_relation_invitee_id", "columns": [{"expression": "invitee_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_referral_relation_invite_code_id": {"name": "idx_referral_relation_invite_code_id", "columns": [{"expression": "invite_code_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_referral_relation_invitee_unique": {"name": "idx_referral_relation_invitee_unique", "columns": [{"expression": "invitee_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ReferralRelation_inviter_id_User_id_fk": {"name": "ReferralRelation_inviter_id_User_id_fk", "tableFrom": "ReferralRelation", "tableTo": "User", "columnsFrom": ["inviter_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ReferralRelation_invitee_id_User_id_fk": {"name": "ReferralRelation_invitee_id_User_id_fk", "tableFrom": "ReferralRelation", "tableTo": "User", "columnsFrom": ["invitee_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ReferralRelation_invite_code_id_InviteCode_id_fk": {"name": "ReferralRelation_invite_code_id_InviteCode_id_fk", "tableFrom": "ReferralRelation", "tableTo": "InviteCode", "columnsFrom": ["invite_code_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Script": {"name": "<PERSON><PERSON><PERSON>", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "cover_image": {"name": "cover_image", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": true, "default": "'[]'"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "json", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "text", "primaryKey": false, "notNull": false}, "total_duration": {"name": "total_duration", "type": "integer", "primaryKey": false, "notNull": false}, "stage_count": {"name": "stage_count", "type": "integer", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_premium": {"name": "is_premium", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "points_cost": {"name": "points_cost", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rating": {"name": "rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "rating_count": {"name": "rating_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_script_public": {"name": "idx_script_public", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"Script\".\"is_public\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_script_active": {"name": "idx_script_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"Script\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_script_category": {"name": "idx_script_category", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_premium": {"name": "idx_script_premium", "columns": [{"expression": "is_premium", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_rating": {"name": "idx_script_rating", "columns": [{"expression": "rating", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_usage": {"name": "idx_script_usage", "columns": [{"expression": "usage_count", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_created_at": {"name": "idx_script_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_public_category": {"name": "idx_script_public_category", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"Script\".\"is_public\" = true AND \"Script\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Script_created_by_User_id_fk": {"name": "Script_created_by_User_id_fk", "tableFrom": "<PERSON><PERSON><PERSON>", "tableTo": "User", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ScriptPurchase": {"name": "ScriptPurchase", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "script_id": {"name": "script_id", "type": "uuid", "primaryKey": false, "notNull": true}, "points_cost": {"name": "points_cost", "type": "integer", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "uuid", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'completed'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_downloaded": {"name": "is_downloaded", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "downloaded_at": {"name": "downloaded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_script_purchase_user_id": {"name": "idx_script_purchase_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_purchase_script_id": {"name": "idx_script_purchase_script_id", "columns": [{"expression": "script_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_purchase_status": {"name": "idx_script_purchase_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_purchase_expires_at": {"name": "idx_script_purchase_expires_at", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_purchase_created_at": {"name": "idx_script_purchase_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_purchase_user_script": {"name": "idx_script_purchase_user_script", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "script_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_purchase_unique_user_script": {"name": "idx_script_purchase_unique_user_script", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "script_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ScriptPurchase_user_id_User_id_fk": {"name": "ScriptPurchase_user_id_User_id_fk", "tableFrom": "ScriptPurchase", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ScriptPurchase_script_id_Script_id_fk": {"name": "ScriptPurchase_script_id_Script_id_fk", "tableFrom": "ScriptPurchase", "tableTo": "<PERSON><PERSON><PERSON>", "columnsFrom": ["script_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ScriptPurchase_transaction_id_PointsTransaction_id_fk": {"name": "ScriptPurchase_transaction_id_PointsTransaction_id_fk", "tableFrom": "ScriptPurchase", "tableTo": "PointsTransaction", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.ScriptUsage": {"name": "ScriptUsage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "script_id": {"name": "script_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_script_usage_user_id": {"name": "idx_script_usage_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_usage_script_id": {"name": "idx_script_usage_script_id", "columns": [{"expression": "script_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_usage_chat_id": {"name": "idx_script_usage_chat_id", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_usage_created_at": {"name": "idx_script_usage_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_script_usage_user_script": {"name": "idx_script_usage_user_script", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "script_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ScriptUsage_user_id_User_id_fk": {"name": "ScriptUsage_user_id_User_id_fk", "tableFrom": "ScriptUsage", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ScriptUsage_script_id_Script_id_fk": {"name": "ScriptUsage_script_id_Script_id_fk", "tableFrom": "ScriptUsage", "tableTo": "<PERSON><PERSON><PERSON>", "columnsFrom": ["script_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "ScriptUsage_chat_id_Chat_id_fk": {"name": "ScriptUsage_chat_id_Chat_id_fk", "tableFrom": "ScriptUsage", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.SubscriptionHistory": {"name": "SubscriptionHistory", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subscription_id": {"name": "subscription_id", "type": "uuid", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "points_granted": {"name": "points_granted", "type": "integer", "primaryKey": false, "notNull": false}, "payment_id": {"name": "payment_id", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_subscription_history_user": {"name": "idx_subscription_history_user", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_subscription_history_created": {"name": "idx_subscription_history_created", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"SubscriptionHistory_user_id_User_id_fk": {"name": "SubscriptionHistory_user_id_User_id_fk", "tableFrom": "SubscriptionHistory", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "SubscriptionHistory_plan_id_MembershipPlan_id_fk": {"name": "SubscriptionHistory_plan_id_MembershipPlan_id_fk", "tableFrom": "SubscriptionHistory", "tableTo": "MembershipPlan", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "SubscriptionHistory_subscription_id_UserSubscription_id_fk": {"name": "SubscriptionHistory_subscription_id_UserSubscription_id_fk", "tableFrom": "SubscriptionHistory", "tableTo": "UserSubscription", "columnsFrom": ["subscription_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.SystemConfig": {"name": "SystemConfig", "schema": "", "columns": {"key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": true, "notNull": true}, "value": {"name": "value", "type": "json", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.Template": {"name": "Template", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "preview_image": {"name": "preview_image", "type": "text", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "text", "primaryKey": false, "notNull": true}, "negative_prompt": {"name": "negative_prompt", "type": "text", "primaryKey": false, "notNull": false}, "points_cost": {"name": "points_cost", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "is_premium": {"name": "is_premium", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "json", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_template_public": {"name": "idx_template_public", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"Template\".\"is_public\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_template_category": {"name": "idx_template_category", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_template_premium": {"name": "idx_template_premium", "columns": [{"expression": "is_premium", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_template_public_category": {"name": "idx_template_public_category", "columns": [{"expression": "is_public", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"Template\".\"is_public\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"Template_created_by_User_id_fk": {"name": "Template_created_by_User_id_fk", "tableFrom": "Template", "tableTo": "User", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.TTSTask": {"name": "TTSTask", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "voice": {"name": "voice", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "audio_url": {"name": "audio_url", "type": "text", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "progress": {"name": "progress", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_tts_user_id": {"name": "idx_tts_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_tts_status": {"name": "idx_tts_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_tts_message_id": {"name": "idx_tts_message_id", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_tts_chat_id": {"name": "idx_tts_chat_id", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_tts_created_at": {"name": "idx_tts_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"TTSTask_user_id_User_id_fk": {"name": "TTSTask_user_id_User_id_fk", "tableFrom": "TTSTask", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "TTSTask_message_id_Message_id_fk": {"name": "TTSTask_message_id_Message_id_fk", "tableFrom": "TTSTask", "tableTo": "Message", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "TTSTask_chat_id_Chat_id_fk": {"name": "TTSTask_chat_id_Chat_id_fk", "tableFrom": "TTSTask", "tableTo": "Cha<PERSON>", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.User": {"name": "User", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "supabase_user_id": {"name": "supabase_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_user_supabase_id": {"name": "idx_user_supabase_id", "columns": [{"expression": "supabase_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_email": {"name": "idx_user_email", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"User_email_unique": {"name": "User_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "User_supabase_user_id_unique": {"name": "User_supabase_user_id_unique", "nullsNotDistinct": false, "columns": ["supabase_user_id"]}}}, "public.UserPoints": {"name": "UserPoints", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "total_points": {"name": "total_points", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "used_points": {"name": "used_points", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "available_points": {"name": "available_points", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "cycle_start_date": {"name": "cycle_start_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "cycle_end_date": {"name": "cycle_end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "membership_level": {"name": "membership_level", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "monthly_allocation": {"name": "monthly_allocation", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cycle_consumed": {"name": "cycle_consumed", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cycle_gifted": {"name": "cycle_gifted", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "cycle_received": {"name": "cycle_received", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "last_cycle_check": {"name": "last_cycle_check", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_user_points_user_id": {"name": "idx_user_points_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_points_cycle_end": {"name": "idx_user_points_cycle_end", "columns": [{"expression": "cycle_end_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"UserPoints_user_id_User_id_fk": {"name": "UserPoints_user_id_User_id_fk", "tableFrom": "UserPoints", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"UserPoints_user_id_unique": {"name": "UserPoints_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}}}, "public.UserProfile": {"name": "UserProfile", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "nickname": {"name": "nickname", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_userprofile_user_id": {"name": "idx_userprofile_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"UserProfile_user_id_User_id_fk": {"name": "UserProfile_user_id_User_id_fk", "tableFrom": "UserProfile", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.UserSession": {"name": "UserSession", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "device_info": {"name": "device_info", "type": "json", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "last_active": {"name": "last_active", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_user_session_user_id": {"name": "idx_user_session_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_session_last_active": {"name": "idx_user_session_last_active", "columns": [{"expression": "last_active", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_user_session_active": {"name": "idx_user_session_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"UserSession\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"UserSession_user_id_User_id_fk": {"name": "UserSession_user_id_User_id_fk", "tableFrom": "UserSession", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.UserSubscription": {"name": "UserSubscription", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "uuid", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "auto_renew": {"name": "auto_renew", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "payment_id": {"name": "payment_id", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_subscription_user_id": {"name": "idx_subscription_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_subscription_status": {"name": "idx_subscription_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_subscription_expires": {"name": "idx_subscription_expires", "columns": [{"expression": "end_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"UserSubscription_user_id_User_id_fk": {"name": "UserSubscription_user_id_User_id_fk", "tableFrom": "UserSubscription", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "UserSubscription_plan_id_MembershipPlan_id_fk": {"name": "UserSubscription_plan_id_MembershipPlan_id_fk", "tableFrom": "UserSubscription", "tableTo": "MembershipPlan", "columnsFrom": ["plan_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.VoiceModel": {"name": "VoiceModel", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "model_id": {"name": "model_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'zh-CN'"}, "supported_languages": {"name": "supported_languages", "type": "json", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "json", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_premium": {"name": "is_premium", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_voice_model_id": {"name": "idx_voice_model_id", "columns": [{"expression": "model_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voice_active": {"name": "idx_voice_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"VoiceModel\".\"is_active\" = true", "concurrently": false, "method": "btree", "with": {}}, "idx_voice_gender": {"name": "idx_voice_gender", "columns": [{"expression": "gender", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"VoiceModel_model_id_unique": {"name": "VoiceModel_model_id_unique", "nullsNotDistinct": false, "columns": ["model_id"]}}}, "public.VoiceSample": {"name": "VoiceSample", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "voice_model_id": {"name": "voice_model_id", "type": "uuid", "primaryKey": false, "notNull": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "sample_text": {"name": "sample_text", "type": "text", "primaryKey": false, "notNull": true}, "audio_url": {"name": "audio_url", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_voice_sample_model": {"name": "idx_voice_sample_model", "columns": [{"expression": "voice_model_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voice_sample_language": {"name": "idx_voice_sample_language", "columns": [{"expression": "language", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voice_sample_default": {"name": "idx_voice_sample_default", "columns": [{"expression": "is_default", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"VoiceSample\".\"is_default\" = true", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"VoiceSample_voice_model_id_VoiceModel_id_fk": {"name": "VoiceSample_voice_model_id_VoiceModel_id_fk", "tableFrom": "VoiceSample", "tableTo": "VoiceModel", "columnsFrom": ["voice_model_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}, "public.WithdrawRequest": {"name": "WithdrawRequest", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "fee_amount": {"name": "fee_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "actual_amount": {"name": "actual_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true, "default": "'pending'"}, "bank_info": {"name": "bank_info", "type": "json", "primaryKey": false, "notNull": false}, "admin_note": {"name": "admin_note", "type": "text", "primaryKey": false, "notNull": false}, "processed_by": {"name": "processed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_withdraw_request_user_id": {"name": "idx_withdraw_request_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_withdraw_request_status": {"name": "idx_withdraw_request_status", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_withdraw_request_processed_by": {"name": "idx_withdraw_request_processed_by", "columns": [{"expression": "processed_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_withdraw_request_created_at": {"name": "idx_withdraw_request_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": false, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"WithdrawRequest_user_id_User_id_fk": {"name": "WithdrawRequest_user_id_User_id_fk", "tableFrom": "WithdrawRequest", "tableTo": "User", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "WithdrawRequest_processed_by_User_id_fk": {"name": "WithdrawRequest_processed_by_User_id_fk", "tableFrom": "WithdrawRequest", "tableTo": "User", "columnsFrom": ["processed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}}}, "enums": {}, "schemas": {}, "sequences": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
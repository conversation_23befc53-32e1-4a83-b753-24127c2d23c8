import React, { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON>, <PERSON>er<PERSON>ontent, DrawerHeader, Drawer<PERSON><PERSON> } from '@heroui/react'
import { DeviceConnectCore } from '@/components/device'
import { useDeviceStore } from '../../../stores/device-store'
import type { SupportedDevicesResponse } from '@/api/services/devices'

interface DeviceConnectDrawerProps {
  isOpen: boolean
  onClose: () => void
  onDeviceConnect: (device: any) => void
  onSkip: () => void
  supportedDevices: SupportedDevicesResponse['devices']
  isLoadingDevices: boolean
}

export const DeviceConnectDrawer: React.FC<DeviceConnectDrawerProps> = ({
  isOpen,
  onClose,
  onDeviceConnect,
  onSkip,
  supportedDevices,
  isLoadingDevices
}) => {
  const { t } = useTranslation('interactive')
  const [isConnecting, setIsConnecting] = useState(false)

  const handleDeviceConnect = (device: any) => {
    onDeviceConnect(device)
  }

  const handleSkip = () => {
    onSkip()
  }

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      placement="bottom"
      size="lg"
      backdrop="blur"
      classNames={{
        base: 'max-h-[70vh]',
        backdrop: 'bg-black/50',
        wrapper: 'items-end'
      }}
    >
      <DrawerContent className="bg-gray-900 text-white rounded-t-2xl border-t border-gray-700">
        <DrawerHeader className="flex flex-col gap-1 bg-gray-900 text-white border-b border-gray-700 px-6 py-4">
          <h2 className="text-xl font-bold">{t('deviceConnect.title')}</h2>
          <p className="text-sm text-gray-400">{t('deviceConnect.subtitle')}</p>
        </DrawerHeader>

        <DrawerBody className="bg-gray-900 text-white p-6">
          <DeviceConnectCore
            onDeviceConnect={handleDeviceConnect}
            onSkip={handleSkip}
            showSkipButton={true}
            connectButtonText={t('deviceConnect.connectButton')}
            skipButtonText={t('deviceConnect.skipButton')}
            isConnecting={isConnecting}
            onConnectingChange={setIsConnecting}
            supportedDevices={supportedDevices}
            isLoadingDevices={isLoadingDevices}
          />
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  )
}

import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { uploadToR2, getR2ConfigFromEnv, deleteFromR2 } from '@/lib/utils/r2-upload'
import {
  getLatestAppVersion,
  checkForUpdates,
  logUpdateEvent,
  getAppVersions,
  createAppVersion,
  createUpdatePolicy,
  getUpdateStats
} from '@/lib/db/queries/app-update'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'

const appUpdate = new Hono<{ Bindings: Env }>()

// ==================== 验证模式 ====================

const checkUpdateSchema = z.object({
  platform: z.enum(['android', 'ios']).default('android'),
  currentVersion: z.string().min(1, '当前版本不能为空'),
  versionCode: z.number().int().positive('版本代码必须为正整数'),
  channel: z.string().default('production'),
  deviceId: z.string().optional()
})

const logUpdateSchema = z.object({
  deviceId: z.string().optional(),
  currentVersion: z.string().optional(),
  targetVersion: z.string().optional(),
  updateType: z.enum(['apk', 'hotfix']),
  updateStatus: z.enum([
    'started',
    'downloading',
    'downloaded',
    'installing',
    'installed',
    'failed',
    'cancelled'
  ]),
  errorMessage: z.string().optional(),
  metadata: z.record(z.any()).optional()
})

const createVersionSchema = z.object({
  versionName: z.string().min(1, '版本名称不能为空'),
  versionCode: z.number().int().positive('版本代码必须为正整数'),
  versionType: z.enum(['apk', 'hotfix']),
  fileUrl: z.string().url('文件URL格式不正确'),
  fileSize: z.number().int().positive().optional(),
  fileHash: z.string().optional(),
  minCompatibleVersion: z.string().optional(),
  releaseNotes: z.string().optional()
})

const createPolicySchema = z.object({
  versionId: z.string().uuid('版本ID格式不正确'),
  channel: z.string().default('production'),
  updateStrategy: z.enum(['force', 'optional', 'silent']),
  targetVersionMin: z.string().optional(),
  targetVersionMax: z.string().optional(),
  rolloutPercentage: z.number().int().min(1).max(100).default(100)
})

// ==================== 客户端API ====================

/**
 * 检查应用更新
 */
appUpdate.get('/check', zValidator('query', checkUpdateSchema), async c => {
  try {
    const { platform, currentVersion, versionCode, channel, deviceId } = c.req.valid('query')
    const env = c.env

    // 记录检查更新的事件
    if (deviceId) {
      await logUpdateEvent(env, {
        deviceId,
        currentVersion,
        updateType: 'apk', // 默认检查APK更新
        updateStatus: 'started'
      })
    }

    // 检查APK更新
    const apkUpdate = await checkForUpdates(env, currentVersion, 'apk', channel)

    // 检查热更新
    const hotfixUpdate = await checkForUpdates(env, currentVersion, 'hotfix', channel)

    return c.json({
      success: true,
      data: {
        hasApkUpdate: apkUpdate.hasUpdate,
        hasHotfixUpdate: hotfixUpdate.hasUpdate,
        apkUpdate: apkUpdate.hasUpdate
          ? {
              version: apkUpdate.version,
              policy: apkUpdate.policy
            }
          : null,
        hotfixUpdate: hotfixUpdate.hasUpdate
          ? {
              version: hotfixUpdate.version,
              policy: hotfixUpdate.policy
            }
          : null,
        currentVersion,
        platform,
        channel
      }
    })
  } catch (error) {
    console.error('检查更新失败:', error)
    return c.json(
      {
        success: false,
        message: '检查更新失败'
      },
      500
    )
  }
})

/**
 * 记录更新事件
 */
appUpdate.post('/log', zValidator('json', logUpdateSchema), async c => {
  try {
    const logData = c.req.valid('json')
    const env = c.env

    // 尝试获取用户ID（如果已登录）
    const authHeader = c.req.header('Authorization')
    let userId: string | undefined

    if (authHeader?.startsWith('Bearer ')) {
      try {
        // 这里可以解析token获取用户ID，但由于是日志记录，失败了也没关系
        // 暂时跳过用户ID解析
      } catch (error) {
        // 忽略认证错误
      }
    }

    await logUpdateEvent(env, {
      userId,
      ...logData
    })

    return c.json({
      success: true,
      message: '更新日志记录成功'
    })
  } catch (error) {
    console.error('记录更新日志失败:', error)
    return c.json(
      {
        success: false,
        message: '记录更新日志失败'
      },
      500
    )
  }
})

// ==================== 管理员API ====================

/**
 * 上传应用文件 (APK 或热更新包)
 */
appUpdate.post('/admin/upload', authMiddleware, async c => {
  try {
    const env = c.env

    // 获取R2配置
    const r2Config = getR2ConfigFromEnv(env)
    if (!r2Config) {
      return c.json(
        {
          success: false,
          message: 'R2存储配置未找到，请检查环境变量配置'
        },
        500
      )
    }

    // 解析表单数据
    const formData = await c.req.formData()
    const file = formData.get('file') as File | null
    const versionName = formData.get('versionName') as string
    const versionCode = Number.parseInt(formData.get('versionCode') as string)
    const versionType = formData.get('versionType') as 'apk' | 'hotfix'
    const releaseNotes = (formData.get('releaseNotes') as string) || ''
    const minCompatibleVersion = (formData.get('minCompatibleVersion') as string) || ''

    // 验证必要参数
    if (!file || !versionName || !versionCode || !versionType) {
      return c.json(
        {
          success: false,
          message: '缺少必要参数：file, versionName, versionCode, versionType'
        },
        400
      )
    }

    // 文件大小和类型验证
    const maxSize = versionType === 'apk' ? 200 * 1024 * 1024 : 50 * 1024 * 1024 // APK最大200MB，热更新包最大50MB
    const allowedTypes =
      versionType === 'apk'
        ? ['application/vnd.android.package-archive', 'application/octet-stream']
        : ['application/zip', 'application/octet-stream']

    if (file.size > maxSize) {
      return c.json(
        {
          success: false,
          message: `文件过大，${versionType === 'apk' ? 'APK' : '热更新包'}最大允许${Math.round(
            maxSize / 1024 / 1024
          )}MB`
        },
        400
      )
    }

    // 上传到R2
    const uploadOptions = {
      fileName: `${versionType === 'apk' ? 'app' : 'bundle'}_${versionName}_${versionCode}.${
        versionType === 'apk' ? 'apk' : 'zip'
      }`,
      folder: versionType === 'apk' ? 'apk-updates' : 'hotfix-updates',
      maxSize,
      allowedTypes,
      makePublic: true
    }

    const uploadResult = await uploadToR2(file, r2Config, uploadOptions)

    if (!uploadResult.success) {
      return c.json(
        {
          success: false,
          message: `文件上传失败: ${uploadResult.error}`
        },
        500
      )
    }

    // 计算文件哈希 (简单实现，生产环境可能需要更强的哈希算法)
    const fileBuffer = await file.arrayBuffer()
    const hashBuffer = await crypto.subtle.digest('SHA-256', fileBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    const fileHash = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')

    // 创建版本记录
    const versionData = {
      versionName,
      versionCode,
      versionType,
      fileUrl: uploadResult.url!,
      fileSize: uploadResult.size,
      fileHash,
      minCompatibleVersion: minCompatibleVersion || undefined,
      releaseNotes: releaseNotes || undefined
    }

    const newVersion = await createAppVersion(env, versionData)

    return c.json({
      success: true,
      data: {
        ...newVersion,
        uploadInfo: {
          key: uploadResult.key,
          url: uploadResult.url,
          size: uploadResult.size
        }
      },
      message: '文件上传并创建版本成功'
    })
  } catch (error) {
    console.error('上传文件失败:', error)
    return c.json(
      {
        success: false,
        message: '上传文件失败'
      },
      500
    )
  }
})

/**
 * 删除应用版本和相关文件
 */
appUpdate.delete('/admin/versions/:versionId', authMiddleware, async c => {
  try {
    const env = c.env
    const versionId = c.req.param('versionId')

    // 获取版本信息 (直接从数据库查询)
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.appVersion)
      .select('*')
      .eq('id', versionId)
      .single()

    const { data: version, error } = handleSupabaseSingleResult(result)
    if (error || !version) {
      return c.json(
        {
          success: false,
          message: '版本不存在'
        },
        404
      )
    }

    // 获取R2配置并删除文件
    const r2Config = getR2ConfigFromEnv(env)
    if (r2Config && version.fileUrl) {
      try {
        // 从URL中提取文件key
        const url = new URL(version.fileUrl)
        const fileKey = url.pathname.substring(1) // 移除开头的'/'

        await deleteFromR2(fileKey, r2Config)
      } catch (error) {
        console.warn('删除R2文件失败:', error)
        // 继续删除数据库记录，即使文件删除失败
      }
    }

    // 删除数据库记录 (这里需要实现deleteAppVersion函数)
    // await deleteAppVersion(env, versionId)

    return c.json({
      success: true,
      message: '版本删除成功'
    })
  } catch (error) {
    console.error('删除版本失败:', error)
    return c.json(
      {
        success: false,
        message: '删除版本失败'
      },
      500
    )
  }
})

/**
 * 获取应用版本列表
 */
appUpdate.get('/admin/versions', authMiddleware, async c => {
  try {
    const env = c.env
    const { versionType, limit, offset } = c.req.query()

    const versions = await getAppVersions(env, {
      versionType: versionType as 'apk' | 'hotfix' | undefined,
      limit: limit ? Number.parseInt(limit) : undefined,
      offset: offset ? Number.parseInt(offset) : undefined
    })

    return c.json({
      success: true,
      data: versions
    })
  } catch (error) {
    console.error('获取版本列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取版本列表失败'
      },
      500
    )
  }
})

/**
 * 创建新版本
 */
appUpdate.post(
  '/admin/versions',
  authMiddleware,
  zValidator('json', createVersionSchema),
  async c => {
    try {
      const versionData = c.req.valid('json')
      const env = c.env

      const newVersion = await createAppVersion(env, versionData)

      return c.json({
        success: true,
        data: newVersion,
        message: '版本创建成功'
      })
    } catch (error) {
      console.error('创建版本失败:', error)
      return c.json(
        {
          success: false,
          message: '创建版本失败'
        },
        500
      )
    }
  }
)

/**
 * 创建更新策略
 */
appUpdate.post(
  '/admin/policies',
  authMiddleware,
  zValidator('json', createPolicySchema),
  async c => {
    try {
      const policyData = c.req.valid('json')
      const env = c.env

      const newPolicy = await createUpdatePolicy(env, policyData)

      return c.json({
        success: true,
        data: newPolicy,
        message: '更新策略创建成功'
      })
    } catch (error) {
      console.error('创建更新策略失败:', error)
      return c.json(
        {
          success: false,
          message: '创建更新策略失败'
        },
        500
      )
    }
  }
)

/**
 * 获取更新统计
 */
appUpdate.get('/admin/stats', authMiddleware, async c => {
  try {
    const env = c.env
    const { versionId, updateType, dateFrom, dateTo } = c.req.query()

    const stats = await getUpdateStats(env, {
      versionId,
      updateType: updateType as 'apk' | 'hotfix' | undefined,
      dateFrom,
      dateTo
    })

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('获取更新统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取更新统计失败'
      },
      500
    )
  }
})

export default appUpdate

import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { Chat, Message } from '../schema';
import type { Env } from '@/types/env';

// ==================== 聊天操作 ====================

export async function saveChat(
  env: Env,
  {
    id,
    userId,
    title,
    characterId,
  }: {
    id: string;
    userId: string;
    title: string;
    characterId?: string;
  }
): Promise<Chat[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.chat)
      .insert({
        id,
        user_id: userId,
        title,
        character_id: characterId,
        created_at: new Date().toISOString(),
      })
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to save chat in database', error);
    throw error;
  }
}

export async function deleteChatById(env: Env, { id }: { id: string }): Promise<Chat | null> {
  try {
    const supabase = getSupabase(env);

    // 先删除消息
    await supabase.from(TABLE_NAMES.message).delete().eq('chat_id', id);

    // 再删除聊天
    const result = await supabase.from(TABLE_NAMES.chat).delete().eq('id', id).select().single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to delete chat by id from database', error);
    throw error;
  }
}

export async function getChatsByUserId(
  env: Env,
  {
    id,
    limit,
    startingAfter,
    endingBefore,
  }: {
    id: string;
    limit: number;
    startingAfter: string | null;
    endingBefore: string | null;
  }
): Promise<{ chats: Chat[]; hasMore: boolean }> {
  try {
    const supabase = getSupabase(env);
    const extendedLimit = limit + 1;

    let query = supabase
      .from(TABLE_NAMES.chat)
      .select('*')
      .eq('user_id', id)
      .order('updated_at', { ascending: false })
      .limit(extendedLimit);

    if (startingAfter) {
      // 获取参考聊天的时间戳
      const refResult = await supabase
        .from(TABLE_NAMES.chat)
        .select('updated_at')
        .eq('id', startingAfter)
        .single();

      const { data: refChat, error: refError } = handleSupabaseSingleResult(refResult);
      if (refError || !refChat) {
        throw new Error(`Chat with id ${startingAfter} not found`);
      }

      query = query.gt('updated_at', refChat.updatedAt);
    } else if (endingBefore) {
      // 获取参考聊天的时间戳
      const refResult = await supabase
        .from(TABLE_NAMES.chat)
        .select('updated_at')
        .eq('id', endingBefore)
        .single();

      const { data: refChat, error: refError } = handleSupabaseSingleResult(refResult);
      if (refError || !refChat) {
        throw new Error(`Chat with id ${endingBefore} not found`);
      }

      query = query.lt('updated_at', refChat.updatedAt);
    }

    const result = await query;
    const { data: filteredChats, error } = handleSupabaseResult(result);
    if (error) throw error;

    const hasMore = (filteredChats || []).length > limit;

    return {
      chats: hasMore ? filteredChats.slice(0, limit) : filteredChats,
      hasMore,
    };
  } catch (error) {
    console.error('Failed to get chats by user from database', error);
    throw error;
  }
}

export async function getChatById(env: Env, { id }: { id: string }): Promise<Chat | null> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase.from(TABLE_NAMES.chat).select('*').eq('id', id).single();

    const { data, error } = handleSupabaseSingleResult(result);

    // 如果是"没有找到记录"的错误，返回null而不是抛出异常
    if (error && error.code === 'PGRST116') {
      console.log(`Chat with id ${id} not found`);
      return null;
    }

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Failed to get chat by id from database', error);
    throw error;
  }
}

export async function updateChatVisiblityById(
  env: Env,
  {
    chatId,
    visibility,
  }: {
    chatId: string;
    visibility: 'private' | 'public';
  }
): Promise<Chat[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.chat)
      .update({ visibility })
      .eq('id', chatId)
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to update chat visibility in database', error);
    throw error;
  }
}

export async function updateChatTitle(
  env: Env,
  {
    chatId,
    title,
  }: {
    chatId: string;
    title: string;
  }
): Promise<Chat[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.chat)
      .update({
        title,
        updated_at: new Date().toISOString(),
      })
      .eq('id', chatId)
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to update chat title in database', error);
    throw error;
  }
}

/**
 * 更新聊天的最后活动时间
 * 这是一个非关键操作，失败不应影响主业务流程
 */
export async function updateChatLastActivity(
  env: Env,
  {
    chatId,
  }: {
    chatId: string;
  }
): Promise<Chat[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.chat)
      .update({
        updated_at: new Date().toISOString(),
      })
      .eq('id', chatId)
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    // 如果没有更新任何记录，说明 chatId 不存在
    if (!data || data.length === 0) {
      console.warn(`尝试更新不存在的聊天活动时间: ${chatId}`);
    }

    return data || [];
  } catch (error) {
    console.error('更新聊天最后活动时间失败:', {
      chatId,
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  }
}

export async function getChatsByCharacterAndUserId(
  env: Env,
  {
    userId,
    characterId,
    limit = 5,
  }: {
    userId: string;
    characterId: string;
    limit?: number;
  }
): Promise<Chat[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.chat)
      .select('*')
      .eq('user_id', userId)
      .eq('character_id', characterId)
      .order('created_at', { ascending: false })
      .limit(limit);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('获取用户和角色的聊天记录失败', error);
    throw error;
  }
}

/**
 * 根据用户ID和角色ID获取聊天记录
 */
export async function getChatsByRoleAndUserId(
  env: Env,
  {
    userId,
    roleId,
    limit = 5,
  }: {
    userId: string;
    roleId: string;
    limit?: number;
  }
): Promise<Chat[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.chat)
      .select('*')
      .eq('user_id', userId)
      .eq('character_id', roleId)
      .order('updated_at', { ascending: false })
      .limit(limit);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('获取用户和角色的聊天记录失败', error);
    throw error;
  }
}

// ==================== 消息操作 ====================

export async function saveMessages(
  env: Env,
  {
    messages,
  }: {
    messages: Array<{
      id?: string;
      chatId: string;
      role: 'user' | 'assistant' | 'system';
      parts: any;
      attachments?: any[];
    }>;
  }
): Promise<Message[]> {
  try {
    const supabase = getSupabase(env);

    // 使用基准时间戳，为每条消息添加微小递增时间差以确保排序
    const baseTimestamp = new Date();

    const messagesToInsert = messages.map((msg, index) => {
      // 为每条消息添加微小时间差（毫秒级），确保时间戳不完全相同
      const timestamp = new Date(baseTimestamp.getTime() + index);

      return {
        id: msg.id,
        chat_id: msg.chatId,
        role: msg.role,
        parts: msg.parts,
        attachments: msg.attachments || [],
        created_at: timestamp.toISOString(),
      };
    });

    console.log(
      '🔍 [DEBUG] 准备保存消息:',
      messagesToInsert.map((m) => ({
        id: m.id,
        role: m.role,
        chat_id: m.chat_id,
        created_at: m.created_at,
      }))
    );

    // 使用 upsert 避免重复插入问题
    const result = await supabase
      .from(TABLE_NAMES.message)
      .upsert(messagesToInsert, { onConflict: 'id' })
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    // 异步更新聊天的最后活动时间，不阻塞主流程
    if (messages.length > 0) {
      const chatId = messages[0].chatId;
      // 异步执行，不等待结果，避免影响消息保存
      updateChatLastActivity(env, { chatId }).catch((error) => {
        console.warn('更新聊天活动时间失败，但消息已成功保存:', {
          chatId,
          error: error instanceof Error ? error.message : String(error),
        });
      });
    }

    console.log('🔍 [DEBUG] 消息保存完成');
    return data || [];
  } catch (error) {
    console.error('Failed to save messages in database', error);
    throw error;
  }
}

export async function updateMessageAttachments(
  env: Env,
  {
    messageId,
    attachments,
  }: {
    messageId: string;
    attachments: any[];
  }
): Promise<Message[]> {
  try {
    const supabase = getSupabase(env);

    // 先查询消息是否存在
    const existingResult = await supabase
      .from(TABLE_NAMES.message)
      .select('*')
      .eq('id', messageId)
      .single();

    const { data: existingMessage, error: queryError } = handleSupabaseSingleResult(existingResult);

    console.log('🔍 [DEBUG] 查询消息ID:', messageId);
    console.log('🔍 [DEBUG] 找到的消息:', existingMessage ? '存在' : '不存在');

    if (existingMessage) {
      console.log('🔍 [DEBUG] 消息详情:', {
        id: existingMessage.id,
        role: existingMessage.role,
        chatId: existingMessage.chatId,
        createdAt: existingMessage.createdAt,
      });
    }

    const result = await supabase
      .from(TABLE_NAMES.message)
      .update({
        attachments: attachments,
      })
      .eq('id', messageId)
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    console.log('🔍 [DEBUG] 更新结果:', data && data.length > 0 ? '成功' : '失败');
    return data || [];
  } catch (error) {
    console.error('Failed to update message attachments in database', error);
    throw error;
  }
}

export async function getMessagesByChatId(env: Env, { id }: { id: string }): Promise<Message[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.message)
      .select('*')
      .eq('chat_id', id)
      .order('created_at', { ascending: true })
      .order('id', { ascending: true });

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to get messages by chat id from database', error);
    throw error;
  }
}

export async function getMessageById(env: Env, { id }: { id: string }): Promise<Message[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase.from(TABLE_NAMES.message).select('*').eq('id', id);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Failed to get message by id from database', error);
    throw error;
  }
}

export async function deleteMessagesByChatIdAfterTimestamp(
  env: Env,
  {
    chatId,
    timestamp,
  }: {
    chatId: string;
    timestamp: Date;
  }
) {
  try {
    const supabase = getSupabase(env);

    // 先查询要删除的消息
    const messagesToDeleteResult = await supabase
      .from(TABLE_NAMES.message)
      .select('id')
      .eq('chat_id', chatId)
      .gte('created_at', timestamp.toISOString());

    const { data: messagesToDelete, error: queryError } =
      handleSupabaseResult(messagesToDeleteResult);
    if (queryError) throw queryError;

    const messageIds = (messagesToDelete || []).map((msg: any) => msg.id);

    if (messageIds.length > 0) {
      const result = await supabase.from(TABLE_NAMES.message).delete().in('id', messageIds);

      if (result.error) throw result.error;
      return result;
    }
  } catch (error) {
    console.error('Failed to delete messages by id after timestamp from database', error);
    throw error;
  }
}

export async function getMessageCountByUserId(
  env: Env,
  {
    id,
    differenceInHours,
  }: {
    id: string;
    differenceInHours: number;
  }
): Promise<number> {
  try {
    const supabase = getSupabase(env);
    const hoursAgo = new Date(Date.now() - differenceInHours * 60 * 60 * 1000);

    // 先获取用户的聊天ID列表
    const { data: userChats, error: chatError } = await supabase
      .from(TABLE_NAMES.chat)
      .select('id')
      .eq('user_id', id);

    if (chatError) throw chatError;

    const chatIds = (userChats || []).map((chat: any) => chat.id);

    if (chatIds.length === 0) {
      return 0;
    }

    // 然后统计这些聊天中的用户消息数量
    const { count, error } = await supabase
      .from(TABLE_NAMES.message)
      .select('id', { count: 'exact', head: true })
      .eq('role', 'user')
      .gte('created_at', hoursAgo.toISOString())
      .in('chat_id', chatIds);

    if (error) throw error;
    return count ?? 0;
  } catch (error) {
    console.error('Failed to get message count by user id from database', error);
    throw error;
  }
}

import { motion, AnimatePresence } from 'framer-motion'
import { But<PERSON> } from '@heroui/react'
import { Icon } from '@iconify/react'
import { TFunction } from 'i18next'
import type {
  PhotoTemplate,
  GenerationTask,
  GenerationStatus
} from '@/stores/photo-generation-store'

interface PhotoGenerationModalProps {
  isVisible: boolean
  template: PhotoTemplate | null
  currentRole: any
  generationStatus: GenerationStatus
  currentTask: GenerationTask | null
  onBackgroundGeneration: () => void
  onReturn: () => void
  onRetry: () => void
  t: TFunction<'photo-album', undefined>
}

export function PhotoGenerationModal({
  isVisible,
  template,
  currentRole,
  generationStatus,
  currentTask,
  onBackgroundGeneration,
  onReturn,
  onRetry,
  t
}: PhotoGenerationModalProps) {
  if (!template || !currentRole) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex flex-col"
          style={{
            background:
              'radial-gradient(ellipse at center, rgba(147, 51, 234, 0.1) 0%, rgba(0, 0, 0, 0.95) 70%)'
          }}
        >
          {/* 生成进度区域 */}
          <div className="flex-1 flex items-center justify-center px-4 py-8">
            <motion.div
              className="relative w-full max-w-sm"
              layoutId={`template-container-${template.id}`}
            >
              {/* 抽卡背景效果 */}
              <div className="absolute inset-0 -m-8">
                {/* 外层光环 */}
                <motion.div
                  className="absolute inset-0 rounded-full opacity-30"
                  style={{
                    background:
                      'conic-gradient(from 0deg, #9333ea, #ec4899, #06b6d4, #10b981, #f59e0b, #9333ea)',
                    filter: 'blur(20px)'
                  }}
                  animate={{
                    rotate: generationStatus === 'generating' ? 360 : 0
                  }}
                  transition={{
                    duration: 4,
                    repeat: generationStatus === 'generating' ? Infinity : 0,
                    ease: 'linear'
                  }}
                />

                {/* 内层光环 */}
                <motion.div
                  className="absolute inset-4 rounded-full opacity-20"
                  style={{
                    background: 'conic-gradient(from 180deg, #ec4899, #9333ea, #06b6d4, #ec4899)',
                    filter: 'blur(15px)'
                  }}
                  animate={{
                    rotate: generationStatus === 'generating' ? -360 : 0
                  }}
                  transition={{
                    duration: 6,
                    repeat: generationStatus === 'generating' ? Infinity : 0,
                    ease: 'linear'
                  }}
                />
              </div>

              {/* 主卡片容器 */}
              <motion.div
                className="relative z-10"
                animate={{
                  scale: generationStatus === 'generating' ? [1, 1.02, 1] : 1,
                  rotateY: generationStatus === 'completed' ? [0, 180, 360] : 0
                }}
                transition={{
                  scale: {
                    duration: 2,
                    repeat: generationStatus === 'generating' ? Infinity : 0,
                    ease: 'easeInOut'
                  },
                  rotateY: {
                    duration: 1.5,
                    ease: 'easeInOut'
                  }
                }}
              >
                {/* 卡片边框发光效果 */}
                <div className="absolute -inset-1 rounded-3xl opacity-75">
                  <motion.div
                    className="w-full h-full rounded-3xl"
                    style={{
                      background:
                        generationStatus === 'generating'
                          ? 'linear-gradient(45deg, #9333ea, #ec4899, #06b6d4, #10b981)'
                          : generationStatus === 'completed'
                          ? 'linear-gradient(45deg, #10b981, #06b6d4, #9333ea)'
                          : 'linear-gradient(45deg, #ef4444, #f97316)',
                      filter: 'blur(8px)'
                    }}
                    animate={{
                      opacity: generationStatus === 'generating' ? [0.3, 0.8, 0.3] : 0.5
                    }}
                    transition={{
                      duration: 2,
                      repeat: generationStatus === 'generating' ? Infinity : 0,
                      ease: 'easeInOut'
                    }}
                  />
                </div>

                {/* 主图片 - 占满屏幕宽度 */}
                <motion.img
                  layoutId={`template-image-${template.id}`}
                  src={
                    generationStatus === 'completed' && currentTask?.imageUrl
                      ? currentTask.imageUrl
                      : template.previewImage
                  }
                  alt={generationStatus === 'completed' ? '生成结果' : template.name}
                  className="w-full aspect-[3/4] object-cover rounded-3xl relative z-10 shadow-2xl"
                  animate={{
                    filter:
                      generationStatus === 'generating'
                        ? 'brightness(1.1) saturate(1.2)'
                        : 'brightness(1) saturate(1)'
                  }}
                  transition={{ duration: 0.5 }}
                />

                {/* 生成中的粒子效果 */}
                {generationStatus === 'generating' && (
                  <div className="absolute inset-0 pointer-events-none">
                    {[...Array(12)].map((_, i) => (
                      <motion.div
                        key={i}
                        className="absolute w-2 h-2 bg-white rounded-full opacity-60"
                        style={{
                          left: `${Math.random() * 100}%`,
                          top: `${Math.random() * 100}%`
                        }}
                        animate={{
                          scale: [0, 1, 0],
                          opacity: [0, 1, 0],
                          x: [0, (Math.random() - 0.5) * 100],
                          y: [0, (Math.random() - 0.5) * 100]
                        }}
                        transition={{
                          duration: 2 + Math.random() * 2,
                          repeat: Infinity,
                          delay: Math.random() * 2
                        }}
                      />
                    ))}
                  </div>
                )}
              </motion.div>

              {/* 生成状态文字 */}
              <motion.div
                className="mt-8 text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {generationStatus === 'generating' && (
                  <div className="space-y-6">
                    <motion.div
                      className="text-white text-xl font-bold"
                      animate={{ opacity: [0.7, 1, 0.7] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      {t('generation.generating.title')}
                    </motion.div>

                    {/* 自定义进度条 */}
                    <div className="relative w-64 h-2 mx-auto bg-gray-800 rounded-full overflow-hidden">
                      <motion.div
                        className="absolute left-0 top-0 h-full rounded-full"
                        style={{
                          background: 'linear-gradient(90deg, #9333ea, #ec4899, #06b6d4)'
                        }}
                        initial={{ width: '0%' }}
                        animate={{ width: `${currentTask?.progress || 0}%` }}
                        transition={{ duration: 0.5, ease: 'easeOut' }}
                      />

                      {/* 进度条光效 */}
                      <motion.div
                        className="absolute top-0 h-full w-8 opacity-60"
                        style={{
                          background:
                            'linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent)',
                          left: `${Math.max(0, (currentTask?.progress || 0) - 8)}%`
                        }}
                        animate={{
                          x: [-20, 20, -20]
                        }}
                        transition={{
                          duration: 2,
                          repeat: Infinity,
                          ease: 'easeInOut'
                        }}
                      />
                    </div>

                    <div className="text-purple-300 text-lg font-medium">
                      {t('generation.generating.progress', {
                        progress: Math.round(currentTask?.progress || 0)
                      })}
                    </div>
                  </div>
                )}

                {generationStatus === 'completed' && (
                  <motion.div
                    className="space-y-4"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    transition={{ duration: 0.6, ease: 'backOut' }}
                  >
                    <motion.div
                      className="text-green-400 text-2xl font-bold"
                      animate={{ scale: [1, 1.1, 1] }}
                      transition={{ duration: 0.6 }}
                    >
                      {t('generation.completed.title')}
                    </motion.div>
                    <div className="text-gray-300 text-lg">
                      {t('generation.completed.subtitle')}
                    </div>
                  </motion.div>
                )}

                {generationStatus === 'failed' && (
                  <motion.div
                    className="space-y-4"
                    initial={{ scale: 0.8, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                  >
                    <div className="text-red-400 text-xl font-bold">
                      {t('generation.failed.title')}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {currentTask?.errorMessage || t('generation.failed.defaultError')}
                    </div>
                  </motion.div>
                )}
              </motion.div>
            </motion.div>
          </div>

          {/* 底部按钮 */}
          <motion.div
            className="p-6 flex justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            {generationStatus === 'generating' && (
              <Button
                variant="ghost"
                className="text-white/60 text-lg px-8 py-3 border border-white/20 rounded-2xl backdrop-blur-sm"
                onPress={onBackgroundGeneration}
              >
                <Icon icon="solar:minimalistic-magnifer-zoom-out-linear" className="w-5 h-5 mr-2" />
                {t('generation.generating.background')}
              </Button>
            )}

            {(generationStatus === 'completed' || generationStatus === 'failed') && (
              <div className="flex space-x-4">
                <Button
                  variant="ghost"
                  className="text-white/60 text-lg px-6 py-3"
                  onPress={onReturn}
                >
                  {t('generation.completed.return')}
                </Button>
                {generationStatus === 'failed' && (
                  <Button
                    className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-3 text-lg font-bold rounded-2xl shadow-lg"
                    onPress={onRetry}
                  >
                    <Icon icon="solar:refresh-bold" className="w-5 h-5 mr-2" />
                    {t('generation.failed.retry')}
                  </Button>
                )}
              </div>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

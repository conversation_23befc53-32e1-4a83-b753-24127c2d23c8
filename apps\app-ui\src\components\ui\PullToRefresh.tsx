import React, { useRef, useState, useCallback, useEffect } from 'react'
import { motion, useMotionValue, useTransform, animate } from 'framer-motion'
import { useTranslation } from 'react-i18next'

interface PullToRefreshProps {
  children: React.ReactNode
  onRefresh: () => Promise<void>
  disabled?: boolean
  pullThreshold?: number
  maxPullDistance?: number
  refreshingText?: string
  pullText?: string
  releaseText?: string
  className?: string
}

/**
 * 自定义下拉刷新组件
 * 支持移动端触摸手势，提供原生般的下拉刷新体验
 */
export const PullToRefresh: React.FC<PullToRefreshProps> = ({
  children,
  onRefresh,
  disabled = false,
  pullThreshold = 80,
  maxPullDistance = 120,
  refreshingText,
  pullText,
  releaseText,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isPulling, setIsPulling] = useState(false)
  const [canRefresh, setCanRefresh] = useState(false)

  // 使用 Framer Motion 的 motion values 来控制动画
  const y = useMotionValue(0)
  const opacity = useTransform(y, [0, pullThreshold], [0, 1])
  const rotate = useTransform(y, [0, pullThreshold], [0, 180])

  // 触摸状态
  const touchStartY = useRef(0)
  const touchCurrentY = useRef(0)
  const startScrollTop = useRef(0)

  // 检查是否可以开始下拉（页面滚动到顶部）
  const canStartPull = useCallback(() => {
    if (disabled || isRefreshing) return false

    const container = containerRef.current
    if (!container) return false

    // 检查页面是否滚动到顶部
    return window.scrollY === 0 || document.documentElement.scrollTop === 0
  }, [disabled, isRefreshing])

  // 处理触摸开始
  const handleTouchStart = useCallback(
    (e: TouchEvent) => {
      if (!canStartPull()) return

      touchStartY.current = e.touches[0].clientY
      startScrollTop.current = window.scrollY
    },
    [canStartPull]
  )

  // 处理触摸移动
  const handleTouchMove = useCallback(
    (e: TouchEvent) => {
      if (!canStartPull()) return

      touchCurrentY.current = e.touches[0].clientY
      const deltaY = touchCurrentY.current - touchStartY.current

      // 只有向下拉且在页面顶部时才处理
      if (deltaY > 0 && window.scrollY === 0) {
        e.preventDefault() // 阻止默认的滚动行为

        // 计算拉动距离，添加阻尼效果
        const pullDistance = Math.min(deltaY * 0.5, maxPullDistance)

        y.set(pullDistance)
        setIsPulling(pullDistance > 0)
        setCanRefresh(pullDistance >= pullThreshold)
      }
    },
    [canStartPull, pullThreshold, maxPullDistance, y]
  )

  // 处理触摸结束
  const handleTouchEnd = useCallback(async () => {
    if (!isPulling) return

    const currentY = y.get()

    if (canRefresh && currentY >= pullThreshold) {
      // 触发刷新
      setIsRefreshing(true)
      setCanRefresh(false)

      // 保持在刷新位置
      animate(y, pullThreshold * 0.6, {
        type: 'spring',
        stiffness: 300,
        damping: 30
      })

      try {
        await onRefresh()
      } catch (error) {
        console.error('Refresh failed:', error)
      } finally {
        setIsRefreshing(false)

        // 动画回到原位
        animate(y, 0, {
          type: 'spring',
          stiffness: 300,
          damping: 30
        }).then(() => {
          setIsPulling(false)
        })
      }
    } else {
      // 未达到刷新阈值，回弹
      animate(y, 0, {
        type: 'spring',
        stiffness: 400,
        damping: 30
      }).then(() => {
        setIsPulling(false)
        setCanRefresh(false)
      })
    }
  }, [isPulling, canRefresh, pullThreshold, y, onRefresh])

  // 绑定触摸事件
  useEffect(() => {
    const container = containerRef.current
    if (!container) return

    // 添加触摸事件监听器，使用 passive: false 以便可以阻止默认行为
    container.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.addEventListener('touchend', handleTouchEnd, { passive: false })

    return () => {
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchmove', handleTouchMove)
      container.removeEventListener('touchend', handleTouchEnd)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd])

  // 获取默认文本和刷新指示器文本
  const { t } = useTranslation('photo-album')
  const defaultRefreshingText = t('pullToRefresh.refreshing')
  const defaultPullText = t('pullToRefresh.pull')
  const defaultReleaseText = t('pullToRefresh.release')
  
  const getIndicatorText = () => {
    if (isRefreshing) return refreshingText || defaultRefreshingText
    if (canRefresh) return releaseText || defaultReleaseText
    return pullText || defaultPullText
  }

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* 下拉刷新指示器 */}
      <motion.div
        className="absolute top-0 left-0 right-0 flex items-center justify-center bg-[#121521] z-10"
        style={{
          height: y,
          opacity
        }}
      >
        <div className="flex items-center space-x-2 text-white">
          {/* 刷新图标 */}
          {isRefreshing ? (
            <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
          ) : (
            <motion.div className="w-5 h-5 flex items-center justify-center" style={{ rotate }}>
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M12 5v14M5 12l7 7 7-7" />
              </svg>
            </motion.div>
          )}

          {/* 刷新文本 */}
          <span className="text-sm text-[#7c85b6]">{getIndicatorText()}</span>
        </div>
      </motion.div>

      {/* 内容区域 */}
      <motion.div style={{ y }} className="relative z-0">
        {children}
      </motion.div>
    </div>
  )
}

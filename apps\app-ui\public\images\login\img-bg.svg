<svg width="375" height="259" viewBox="0 0 375 259" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_752_2340" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="375" height="259">
<rect width="375" height="259" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_752_2340)">
<g opacity="0.5" filter="url(#filter0_f_752_2340)">
<path d="M458.387 -62.7799C469.249 40.5834 371.302 95.0915 271.747 105.553C172.192 116.014 3.36128 234.743 -7.5 131.38C-18.3613 28.0167 140.704 -183.649 240.259 -194.11C339.814 -204.571 447.526 -166.143 458.387 -62.7799Z" fill="#892FFF"/>
</g>
<g opacity="0.4" filter="url(#filter1_f_752_2340)">
<ellipse cx="-24.9749" cy="130.566" rx="94.2418" ry="85.1896" transform="rotate(-42.9448 -24.9749 130.566)" fill="#FF2D97"/>
</g>
<g opacity="0.4" filter="url(#filter2_f_752_2340)">
<path d="M-103.574 121.722C-68.4287 163.72 12.6962 123.873 94.8583 84.9648C197.459 55.7004 232.931 -12.0326 197.786 -54.0307C162.641 -96.0288 42.0314 -129.422 -32.4144 -67.1239C-106.86 -4.82534 -138.719 79.7239 -103.574 121.722Z" fill="#66FFDE"/>
</g>
</g>
<defs>
<filter id="filter0_f_752_2340" x="-80.0284" y="-267.77" width="611.243" height="513.131" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_752_2340"/>
</filter>
<filter id="filter1_f_752_2340" x="-187.13" y="-30.9393" width="324.31" height="323.01" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_752_2340"/>
</filter>
<filter id="filter2_f_752_2340" x="-189.875" y="-173.525" width="472.834" height="385.184" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_752_2340"/>
</filter>
</defs>
</svg>

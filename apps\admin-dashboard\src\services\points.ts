import { apiService } from './api'
import type { ApiResponse, PaginatedResponse, PointsPackage, UserPoints } from '@/types/api'

export interface PointsPackageParams {
  name: string
  points: number
  price: number
  bonusPoints?: number
  isActive: boolean
  description?: string
  sortOrder?: number
}

export interface UserPointsListParams {
  page?: number
  pageSize?: number
  keyword?: string
  minBalance?: number
  maxBalance?: number
  startDate?: string
  endDate?: string
}

export interface PointsTransactionParams {
  userId: string
  points: number
  type: 'add' | 'deduct'
  reason: string
}

// 积分管理服务
export class PointsService {
  // 获取积分套餐列表
  async getPackages(params?: { isActive?: boolean }): Promise<ApiResponse<PointsPackage[]>> {
    return await apiService.get<PointsPackage[]>('/admin/points/packages', { params })
  }

  // 创建积分套餐
  async createPackage(pkg: PointsPackageParams): Promise<ApiResponse<PointsPackage>> {
    return await apiService.post<PointsPackage>('/admin/points/packages', pkg)
  }

  // 更新积分套餐
  async updatePackage(id: string, pkg: Partial<PointsPackageParams>): Promise<ApiResponse<PointsPackage>> {
    return await apiService.put<PointsPackage>(`/admin/points/packages/${id}`, pkg)
  }

  // 删除积分套餐
  async deletePackage(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/points/packages/${id}`)
  }

  // 获取用户积分列表
  async getUserPoints(params: UserPointsListParams): Promise<ApiResponse<PaginatedResponse<UserPoints>>> {
    return await apiService.get<PaginatedResponse<UserPoints>>('/admin/points/users', { params })
  }

  // 调整用户积分
  async adjustUserPoints(transaction: PointsTransactionParams): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/points/adjust', transaction)
  }

  // 获取积分交易记录
  async getTransactions(params: {
    page?: number
    pageSize?: number
    userId?: string
    type?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PaginatedResponse<any>>> {
    return await apiService.get<PaginatedResponse<any>>('/admin/points/transactions', { params })
  }

  // 获取积分统计数据
  async getPointsStats(quick = false): Promise<ApiResponse<{
    totalPackages: number
    activePackages: number
    totalPoints: number
    totalUsers: number
    monthlyTransactions: number
    todayTransactions: number
    monthlyRevenue: number
  }>> {
    const params = quick ? { quick: 'true' } : {};
    return await apiService.get<any>('/admin/points/stats', { params })
  }

  // 修复用户积分记录中的空周期信息
  async fixCycleDates(): Promise<ApiResponse<{ total: number; fixed: number }>> {
    return await apiService.post<{ total: number; fixed: number }>('/admin/points/fix-cycle-dates')
  }

  // ==================== 积分配置管理 ====================

  // 获取积分配置
  async getPointsConfig(): Promise<ApiResponse<Record<string, number>>> {
    return await apiService.get<Record<string, number>>('/admin/points/config')
  }

  // 更新单个积分配置
  async updatePointsConfig(feature: string, cost: number): Promise<ApiResponse<void>> {
    return await apiService.put<void>('/admin/points/config', { feature, cost })
  }

  // 批量更新积分配置
  async batchUpdatePointsConfig(configs: Array<{ feature: string; cost: number }>): Promise<ApiResponse<void>> {
    return await apiService.put<void>('/admin/points/config/batch', { configs })
  }

  // 重置积分配置为默认值
  async resetPointsConfig(): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/points/config/reset')
  }
}

export const pointsService = new PointsService()
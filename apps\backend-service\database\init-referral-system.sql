-- ==========================================
-- 邀请码营销系统初始化 SQL
-- 执行环境: Supabase Dashboard > SQL Editor
-- 创建时间: 2024-06-30
-- ==========================================

-- ==========================================
-- 1. 系统配置初始化
-- ==========================================

-- 邀请码营销系统配置
INSERT INTO "SystemConfig" ("key", "value", "description", "is_public") VALUES 
('COMMISSION_RATE_MEMBERSHIP', '0.05', '会员套餐佣金比例（5%）', false),
('COMMISSION_RATE_POINTS', '0.10', '积分包佣金比例（10%）', false),
('MIN_WITHDRAW_AMOUNT', '100', '最低提现金额（元）', false),
('WITHDRAW_FEE_RATE', '0.03', '提现手续费比例（3%）', false),
('COMMISSION_FREEZE_DAYS', '7', '佣金冻结天数', false),
('INVITE_CODE_LENGTH', '6', '邀请码长度', false),
('INVITE_CODE_ENABLED', 'true', '邀请码功能是否启用', true)
ON CONFLICT ("key") DO UPDATE SET 
    "value" = EXCLUDED."value",
    "description" = EXCLUDED."description",
    "is_public" = EXCLUDED."is_public",
    "updated_at" = NOW();

-- ==========================================
-- 2. 创建邀请码生成函数
-- ==========================================

-- 生成随机邀请码的函数
CREATE OR REPLACE FUNCTION generate_invite_code()
RETURNS TEXT AS $$
DECLARE
    chars TEXT := 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INTEGER := 0;
    code_length INTEGER := 6;
BEGIN
    -- 从系统配置获取邀请码长度
    SELECT CAST("value"::text AS INTEGER) INTO code_length
    FROM "SystemConfig"
    WHERE "key" = 'INVITE_CODE_LENGTH';
    
    -- 如果没有配置，使用默认长度6
    IF code_length IS NULL THEN
        code_length := 6;
    END IF;
    
    -- 生成随机字符串
    FOR i IN 1..code_length LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::integer, 1);
    END LOOP;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- 3. 创建佣金计算函数
-- ==========================================

-- 计算佣金金额的函数
CREATE OR REPLACE FUNCTION calculate_commission(
    source_amount DECIMAL(10,2),
    source_type TEXT
)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    commission_rate DECIMAL(5,4);
    commission_amount DECIMAL(10,2);
BEGIN
    -- 根据来源类型获取佣金比例
    IF source_type = 'membership' THEN
        SELECT CAST("value"::text AS DECIMAL(5,4)) INTO commission_rate
        FROM "SystemConfig"
        WHERE "key" = 'COMMISSION_RATE_MEMBERSHIP';
    ELSIF source_type = 'points_package' THEN
        SELECT CAST("value"::text AS DECIMAL(5,4)) INTO commission_rate
        FROM "SystemConfig"
        WHERE "key" = 'COMMISSION_RATE_POINTS';
    ELSE
        commission_rate := 0;
    END IF;
    
    -- 如果没有找到配置，使用默认值
    IF commission_rate IS NULL THEN
        commission_rate := 0;
    END IF;
    
    -- 计算佣金金额
    commission_amount := source_amount * commission_rate;
    
    -- 保留两位小数
    RETURN ROUND(commission_amount, 2);
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- 4. 创建提现手续费计算函数
-- ==========================================

-- 计算提现手续费的函数
CREATE OR REPLACE FUNCTION calculate_withdraw_fee(
    withdraw_amount DECIMAL(10,2)
)
RETURNS DECIMAL(10,2) AS $$
DECLARE
    fee_rate DECIMAL(5,4);
    fee_amount DECIMAL(10,2);
BEGIN
    -- 获取提现手续费比例
    SELECT CAST("value"::text AS DECIMAL(5,4)) INTO fee_rate
    FROM "SystemConfig"
    WHERE "key" = 'WITHDRAW_FEE_RATE';
    
    -- 如果没有配置，使用默认值3%
    IF fee_rate IS NULL THEN
        fee_rate := 0.03;
    END IF;
    
    -- 计算手续费
    fee_amount := withdraw_amount * fee_rate;
    
    -- 保留两位小数
    RETURN ROUND(fee_amount, 2);
END;
$$ LANGUAGE plpgsql;

-- ==========================================
-- 5. 创建触发器函数
-- ==========================================

-- 更新佣金账户余额的触发器函数
CREATE OR REPLACE FUNCTION update_commission_account_balance()
RETURNS TRIGGER AS $$
BEGIN
    -- 当佣金记录状态变为settled时，更新账户余额
    IF NEW.status = 'settled' AND OLD.status != 'settled' THEN
        INSERT INTO "CommissionAccount" (
            "user_id", 
            "total_earned", 
            "available_balance"
        ) VALUES (
            NEW.inviter_id, 
            NEW.commission_amount, 
            NEW.commission_amount
        )
        ON CONFLICT ("user_id") DO UPDATE SET
            "total_earned" = "CommissionAccount"."total_earned" + NEW.commission_amount,
            "available_balance" = "CommissionAccount"."available_balance" + NEW.commission_amount,
            "updated_at" = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
DROP TRIGGER IF EXISTS trigger_update_commission_account ON "CommissionRecord";
CREATE TRIGGER trigger_update_commission_account
    AFTER UPDATE ON "CommissionRecord"
    FOR EACH ROW
    EXECUTE FUNCTION update_commission_account_balance();

-- ==========================================
-- 6. 验证数据插入
-- ==========================================

-- 查看插入的系统配置
SELECT 
    "key",
    "value",
    "description",
    "is_public"
FROM "SystemConfig"
WHERE "key" LIKE 'COMMISSION_%' OR "key" LIKE 'WITHDRAW_%' OR "key" LIKE 'INVITE_%'
ORDER BY "key";

-- 测试邀请码生成函数
SELECT generate_invite_code() as sample_invite_code;

-- 测试佣金计算函数
SELECT 
    calculate_commission(100.00, 'membership') as membership_commission,
    calculate_commission(100.00, 'points_package') as points_commission;

-- 测试提现手续费计算函数
SELECT 
    calculate_withdraw_fee(100.00) as withdraw_fee_100,
    calculate_withdraw_fee(500.00) as withdraw_fee_500;

-- ==========================================
-- 执行完成提示
-- ==========================================

SELECT 
    '✅ 邀请码营销系统初始化完成！' as status,
    '系统配置和函数已成功创建' as message,
    NOW() as executed_at;

-- ==========================================
-- 后续步骤提醒
-- ==========================================

/*
🎯 后续步骤：
1. 在 Supabase Dashboard 中执行此 SQL 文件
2. 验证系统配置是否正确插入
3. 测试邀请码生成和佣金计算函数
4. 启动应用测试邀请码功能

🔧 配置调整示例：
-- 修改会员套餐佣金比例为8%
UPDATE "SystemConfig" 
SET "value" = '0.08' 
WHERE "key" = 'COMMISSION_RATE_MEMBERSHIP';

-- 修改最低提现金额为200元
UPDATE "SystemConfig" 
SET "value" = '200' 
WHERE "key" = 'MIN_WITHDRAW_AMOUNT';
*/

/**
 * 缓存相关的类型定义
 */

// 缓存前缀枚举
export enum CachePrefix {
  USER_MAPPING = 'cache:user:supabase',
  VOICE_MODEL = 'cache:voice:model',
  USER_POINTS = 'cache:points:user',
  PERMISSION = 'cache:permission',
  MEMBERSHIP = 'cache:membership:user',
  VOICE_MODELS_LIST = 'cache:voice:models:list',
  VOICE_MODELS_GENDER = 'cache:voice:models:gender',
  TEMPLATE = 'cache:template:data'
}

// 缓存TTL配置（秒）
export enum CacheTTL {
  USER_MAPPING = 2592000, // 30天 - 用户映射基本不变，接近永不过期
  VOICE_MODEL = 2592000, // 30天 - 声音模型基本不变，接近永不过期
  USER_POINTS = 600, // 10分钟 - 积分需要相对实时
  PERMISSION = 43200, // 12小时 - 权限信息
  MEMBERSHIP = 43200, // 12小时 - 会员状态需要相对实时，但不用太频繁
  VOICE_MODELS_LIST = 2592000, // 30天 - 声音模型列表变化不频繁
  TEMPLATE = 2592000 // 30天 - 模板数据相对稳定，变化不频繁
}

// 缓存数据接口
export interface CachedUserMapping {
  dbUserId: string
  timestamp: number
}

export interface CachedVoiceModel {
  realVoiceId: string
  displayName?: string
  timestamp: number
}

export interface CachedUserPoints {
  totalPoints: number
  usedPoints: number
  remainingPoints: number
  timestamp: number
}

export interface CachedPermission {
  hasPermission: boolean
  reason?: string
  timestamp: number
}

// 新增：会员状态缓存接口
export interface CachedMembership {
  isMember: boolean
  subscriptionId?: string
  planId?: string
  endDate?: string // ISO string
  status?: string
  timestamp: number
}

// 新增：声音模型列表缓存接口
export interface CachedVoiceModelsList {
  models: any[] // 声音模型数组
  type: 'all' | 'gender' | 'withSamples' // 缓存类型
  filter?: string // 筛选条件（如gender值）
  timestamp: number
}

// 新增：模板数据缓存接口
export interface CachedTemplate {
  id: string
  name: string
  prompt?: string
  pointsCost: number
  isActive: boolean
  category?: string
  tags?: string[]
  imageUrl?: string
  timestamp: number
}

// 缓存操作结果
export interface CacheResult<T> {
  success: boolean
  data?: T
  cached: boolean // 是否来自缓存
  error?: string
}

// 缓存统计信息
export interface CacheStats {
  hits: number
  misses: number
  errors: number
  hitRate: number
}

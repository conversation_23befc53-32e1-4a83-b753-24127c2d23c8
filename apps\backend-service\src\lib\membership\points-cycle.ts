import type { Env } from '@/types/env';
import { getSupabase } from '@/lib/db/queries/base';
import {
  handleSupabaseResult,
  handleSupabaseSingleResult,
  TABLE_NAMES,
} from '@/lib/db/supabase-types';

/**
 * 积分周期管理服务
 * 核心功能：个人会员周期管理、积分自动重置、会员升级补差
 */

// 会员等级积分配置
const MEMBERSHIP_POINTS_ALLOCATION = {
  FREE: 5,
  PRO: 400,
  ELITE: 1200,
  ULTRA: 3000,
} as const;

type MembershipLevel = keyof typeof MEMBERSHIP_POINTS_ALLOCATION;

/**
 * 用户积分周期信息
 */
export interface UserPointsCycleInfo {
  userId: string;
  availablePoints: number;
  cycleStartDate: Date | null;
  cycleEndDate: Date | null;
  membershipLevel: string | null;
  monthlyAllocation: number;
  cycleConsumed: number;
  daysRemaining: number;
  isExpired: boolean;
  needsReset: boolean;
}

/**
 * 积分周期管理器
 */
export class PointsCycleManager {
  constructor(private env: Env) {}

  /**
   * 获取用户积分周期信息
   */
  async getUserPointsCycle(userId: string): Promise<UserPointsCycleInfo> {
    const supabase = getSupabase(this.env);

    // 获取用户积分信息
    const result = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .eq('user_id', userId)
      .limit(1)
      .single();

    const { data: pointsRecord } = handleSupabaseSingleResult(result);

    if (!pointsRecord) {
      // 用户没有积分记录，需要初始化
      return {
        userId,
        availablePoints: 0,
        cycleStartDate: null,
        cycleEndDate: null,
        membershipLevel: null,
        monthlyAllocation: 0,
        cycleConsumed: 0,
        daysRemaining: 0,
        isExpired: true,
        needsReset: true,
      };
    }

    // 计算周期剩余天数
    const now = new Date();
    const cycleEndDate = pointsRecord.cycleEndDate ? new Date(pointsRecord.cycleEndDate) : null;
    const daysRemaining = cycleEndDate
      ? Math.max(0, Math.ceil((cycleEndDate.getTime() - now.getTime()) / (24 * 60 * 60 * 1000)))
      : 0;

    const isExpired = cycleEndDate ? now > cycleEndDate : true;
    const needsReset = isExpired && pointsRecord.membershipLevel;

    return {
      userId,
      availablePoints: pointsRecord.availablePoints || 0,
      cycleStartDate: pointsRecord.cycleStartDate ? new Date(pointsRecord.cycleStartDate) : null,
      cycleEndDate,
      membershipLevel: pointsRecord.membershipLevel,
      monthlyAllocation: pointsRecord.monthlyAllocation || 0,
      cycleConsumed: pointsRecord.cycleConsumed || 0,
      daysRemaining,
      isExpired,
      needsReset,
    };
  }

  /**
   * 初始化用户积分周期（新用户或首次会员）
   */
  async initializeUserPointsCycle(
    userId: string,
    membershipLevel: MembershipLevel,
    startDate?: Date
  ): Promise<void> {
    const supabase = getSupabase(this.env);
    const now = startDate || new Date();
    const cycleStartDate = now;
    const cycleEndDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后

    const monthlyAllocation = MEMBERSHIP_POINTS_ALLOCATION[membershipLevel];

    // 由于Supabase不支持事务，改为顺序操作
    try {
      // 检查是否已存在记录
      const existingResult = await supabase
        .from(TABLE_NAMES.userPoints)
        .select('*')
        .eq('user_id', userId)
        .limit(1)
        .single();

      const { data: existingRecord } = handleSupabaseSingleResult(existingResult);

      if (existingRecord) {
        // 更新现有记录
        await supabase
          .from(TABLE_NAMES.userPoints)
          .update({
            total_points:
              existingRecord.totalPoints - existingRecord.availablePoints + monthlyAllocation,
            available_points: monthlyAllocation,
            cycle_start_date: cycleStartDate.toISOString(),
            cycle_end_date: cycleEndDate.toISOString(),
            membership_level: membershipLevel,
            monthly_allocation: monthlyAllocation,
            cycle_consumed: 0,
            cycle_gifted: 0,
            cycle_received: 0,
            last_cycle_check: now.toISOString(),
            last_updated: now.toISOString(),
          })
          .eq('user_id', userId);
      } else {
        // 创建新记录
        await supabase.from(TABLE_NAMES.userPoints).insert({
          user_id: userId,
          total_points: monthlyAllocation,
          used_points: 0,
          available_points: monthlyAllocation,
          cycle_start_date: cycleStartDate.toISOString(),
          cycle_end_date: cycleEndDate.toISOString(),
          membership_level: membershipLevel,
          monthly_allocation: monthlyAllocation,
          cycle_consumed: 0,
          cycle_gifted: 0,
          cycle_received: 0,
          last_cycle_check: now.toISOString(),
          last_updated: now.toISOString(),
        });
      }

      // 记录积分发放交易
      await supabase.from(TABLE_NAMES.pointsTransaction).insert({
        user_id: userId,
        transaction_type: 'cycle_grant',
        amount: monthlyAllocation,
        source: 'cycle_grant',
        source_id: null,
        description: `${membershipLevel}会员周期积分发放`,
        balance_after: monthlyAllocation,
        metadata: null,
        created_at: now.toISOString(),
      });
    } catch (error) {
      console.error('初始化用户积分周期失败:', error);
      throw error;
    }
  }

  /**
   * 重置用户积分周期（周期到期时调用）
   */
  async resetUserPointsCycle(userId: string): Promise<void> {
    const supabase = getSupabase(this.env);

    // 获取用户当前会员状态
    const membershipInfo = await this.getCurrentMembershipLevel(userId);

    if (!membershipInfo.isMember) {
      // 用户不是会员，清零积分
      await this.clearUserPoints(userId);
      return;
    }

    const now = new Date();
    const cycleStartDate = now;
    const cycleEndDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    const monthlyAllocation = MEMBERSHIP_POINTS_ALLOCATION[membershipInfo.level as MembershipLevel];

    // 由于Supabase不支持事务，改为顺序操作
    try {
      // 获取当前积分记录
      const currentResult = await supabase
        .from(TABLE_NAMES.userPoints)
        .select('*')
        .eq('user_id', userId)
        .limit(1)
        .single();

      const { data: currentRecord } = handleSupabaseSingleResult(currentResult);

      if (!currentRecord) {
        throw new Error('用户积分记录不存在');
      }

      // 重置积分周期
      await supabase
        .from(TABLE_NAMES.userPoints)
        .update({
          total_points:
            currentRecord.totalPoints - currentRecord.availablePoints + monthlyAllocation,
          available_points: monthlyAllocation,
          cycle_start_date: cycleStartDate.toISOString(),
          cycle_end_date: cycleEndDate.toISOString(),
          membership_level: membershipInfo.level,
          monthly_allocation: monthlyAllocation,
          cycle_consumed: 0,
          cycle_gifted: 0,
          cycle_received: 0,
          last_cycle_check: now.toISOString(),
          last_updated: now.toISOString(),
        })
        .eq('user_id', userId);

      // 记录周期重置交易
      await supabase.from(TABLE_NAMES.pointsTransaction).insert({
        user_id: userId,
        transaction_type: 'cycle_reset',
        amount: monthlyAllocation,
        source: 'cycle_reset',
        source_id: null,
        description: `积分周期重置 - ${membershipInfo.level}会员`,
        balance_after: monthlyAllocation,
        metadata: null,
        created_at: now.toISOString(),
      });
    } catch (error) {
      console.error('重置用户积分周期失败:', error);
      throw error;
    }
  }

  /**
   * 清零用户积分（非会员时调用）
   */
  async clearUserPoints(userId: string): Promise<void> {
    const supabase = getSupabase(this.env);
    const now = new Date();

    // 由于Supabase不支持事务，改为顺序操作
    try {
      // 获取当前积分记录
      const currentResult = await supabase
        .from(TABLE_NAMES.userPoints)
        .select('*')
        .eq('user_id', userId)
        .limit(1)
        .single();

      const { data: currentRecord } = handleSupabaseSingleResult(currentResult);

      if (!currentRecord) {
        throw new Error('用户积分记录不存在');
      }

      await supabase
        .from(TABLE_NAMES.userPoints)
        .update({
          available_points: 0,
          cycle_start_date: null,
          cycle_end_date: null,
          membership_level: null,
          monthly_allocation: 0,
          cycle_consumed: 0,
          last_cycle_check: now.toISOString(),
          last_updated: now.toISOString(),
        })
        .eq('user_id', userId);

      // 记录积分清零交易
      await supabase.from(TABLE_NAMES.pointsTransaction).insert({
        user_id: userId,
        transaction_type: 'cycle_reset',
        amount: 0,
        source: 'cycle_reset',
        source_id: null,
        description: '非会员积分清零',
        balance_after: 0,
        metadata: null,
        created_at: now.toISOString(),
      });
    } catch (error) {
      console.error('清零用户积分失败:', error);
      throw error;
    }
  }

  /**
   * 会员升级积分补差处理（完整补差逻辑）
   */
  async handleMembershipUpgrade(
    userId: string,
    fromLevel: MembershipLevel,
    toLevel: MembershipLevel,
    remainingDays: number
  ): Promise<{ bonusPoints: number }> {
    const supabase = getSupabase(this.env);

    // 计算积分补差（完整补差，不考虑剩余天数）
    const fromAllocation = MEMBERSHIP_POINTS_ALLOCATION[fromLevel];
    const toAllocation = MEMBERSHIP_POINTS_ALLOCATION[toLevel];
    const bonusPoints = toAllocation - fromAllocation;

    console.log('🔍 [UPGRADE] 积分补差计算:', {
      fromLevel,
      toLevel,
      fromAllocation,
      toAllocation,
      bonusPoints,
      remainingDays,
    });

    if (bonusPoints <= 0) {
      console.log('⚠️ [UPGRADE] 无需补差或降级，跳过积分调整');
      return { bonusPoints: 0 };
    }

    // 由于Supabase不支持事务，改为顺序操作，但要注意数据一致性
    try {
      // 获取当前积分记录
      const currentResult = await supabase
        .from(TABLE_NAMES.userPoints)
        .select('*')
        .eq('user_id', userId)
        .limit(1)
        .single();

      const { data: currentRecord } = handleSupabaseSingleResult(currentResult);

      if (!currentRecord) {
        throw new Error('用户积分记录不存在');
      }

      console.log('🔍 [UPGRADE] 当前积分记录:', currentRecord);

      // 更新积分：将补差积分加到可用积分中
      const newAvailablePoints = currentRecord.availablePoints + bonusPoints;
      const newTotalPoints = currentRecord.totalPoints + bonusPoints;

      console.log('🔍 [UPGRADE] 积分更新:', {
        原可用积分: currentRecord.availablePoints,
        原总积分: currentRecord.totalPoints,
        补差积分: bonusPoints,
        新可用积分: newAvailablePoints,
        新总积分: newTotalPoints,
      });

      await supabase
        .from(TABLE_NAMES.userPoints)
        .update({
          total_points: newTotalPoints,
          available_points: newAvailablePoints,
          membership_level: toLevel, // 更新会员等级
          monthly_allocation: toAllocation, // 更新月度配额
          last_updated: new Date().toISOString(),
        })
        .eq('user_id', userId);

      // 记录升级积分奖励交易
      await supabase.from(TABLE_NAMES.pointsTransaction).insert({
        user_id: userId,
        transaction_type: 'upgrade_bonus',
        amount: bonusPoints,
        source: 'upgrade',
        source_id: null,
        description: `会员升级补差：${fromLevel} -> ${toLevel}`,
        balance_after: newAvailablePoints,
        metadata: {
          fromLevel,
          toLevel,
          fromAllocation,
          toAllocation,
          upgradeType: 'full_bonus',
        },
        created_at: new Date().toISOString(),
      });

      console.log('✅ [UPGRADE] 积分补差完成:', {
        userId,
        bonusPoints,
        newAvailablePoints,
        newTotalPoints,
      });

      return { bonusPoints };
    } catch (error) {
      console.error('❌ [UPGRADE] 处理会员升级积分补差失败:', error);
      throw error;
    }
  }

  /**
   * 检查并处理过期积分（管理员工具）
   */
  async checkAndHandleExpiredPoints(
    userId?: string
  ): Promise<{
    processedUsers: number;
    totalPointsReset: number;
    wasExpired: boolean;
    newCycle?: any;
  }> {
    const supabase = getSupabase(this.env);
    const now = new Date();

    try {
      // 构建查询：查找过期的积分记录
      let query = supabase
        .from(TABLE_NAMES.userPoints)
        .select('*')
        .not('cycle_end_date', 'is', null)
        .lt('cycle_end_date', now.toISOString());

      if (userId) {
        query = query.eq('user_id', userId);
      }

      const result = await query;
      const { data: expiredRecords, error } = handleSupabaseResult(result);
      if (error) throw error;

      let processedUsers = 0;
      let totalPointsReset = 0;
      let wasExpired = false;
      let newCycleInfo = null;

      // 逐个处理过期用户
      for (const record of expiredRecords || []) {
        try {
          const cycleInfo = await this.getUserPointsCycle(record.userId);
          if (cycleInfo.needsReset) {
            await this.resetUserPointsCycle(record.userId);
            processedUsers++;
            totalPointsReset += cycleInfo.availablePoints;
            wasExpired = true;

            // 如果是指定用户，获取新的周期信息
            if (userId && record.userId === userId) {
              newCycleInfo = await this.getUserPointsCycle(record.userId);
            }
          }
        } catch (error) {
          console.error(`处理用户 ${record.userId} 过期积分失败:`, error);
        }
      }

      return {
        processedUsers,
        totalPointsReset,
        wasExpired,
        newCycle: newCycleInfo,
      };
    } catch (error) {
      console.error('检查过期积分失败:', error);
      throw error;
    }
  }

  /**
   * 批量处理过期积分（定时任务使用）
   */
  async batchProcessExpiredPoints(): Promise<{ processedCount: number; errors: string[] }> {
    const supabase = getSupabase(this.env);
    const now = new Date();

    try {
      // 查找所有过期的积分记录
      const result = await supabase
        .from(TABLE_NAMES.userPoints)
        .select('user_id, cycle_end_date')
        .not('cycle_end_date', 'is', null)
        .lt('cycle_end_date', now.toISOString());

      const { data: expiredRecords, error } = handleSupabaseResult(result);
      if (error) throw error;

      const errors: string[] = [];
      let processedCount = 0;

      // 逐个处理（避免并发问题）
      for (const record of expiredRecords || []) {
        try {
          await this.resetUserPointsCycle(record.userId);
          processedCount++;
        } catch (error) {
          const errorMsg = `处理用户 ${record.userId} 失败: ${error}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      return { processedCount, errors };
    } catch (error) {
      console.error('批量处理过期积分失败:', error);
      throw error;
    }
  }

  /**
   * 获取当前会员等级
   */
  private async getCurrentMembershipLevel(
    userId: string
  ): Promise<{ isMember: boolean; level: string | null; plan: any }> {
    const supabase = getSupabase(this.env);

    try {
      // 查询用户当前有效订阅
      const result = await supabase
        .from(TABLE_NAMES.userSubscription)
        .select(`
          *,
          plan:${TABLE_NAMES.membershipPlan}(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'active')
        .gt('end_date', new Date().toISOString())
        .order('end_date', { ascending: false })
        .limit(1)
        .single();

      const { data: subscription } = handleSupabaseSingleResult(result);

      if (subscription && subscription.plan) {
        return {
          isMember: true,
          level: subscription.plan.name?.toUpperCase() || 'FREE',
          plan: subscription.plan,
        };
      }

      return { isMember: false, level: null, plan: null };
    } catch (error) {
      console.error('获取会员等级失败:', error);
      return { isMember: false, level: null, plan: null };
    }
  }

  /**
   * 记录积分交易
   */
  private async recordPointsTransaction(
    userId: string,
    type: string,
    amount: number,
    source: string,
    description: string,
    balanceAfter: number
  ): Promise<void> {
    const supabase = getSupabase(this.env);

    await supabase.from(TABLE_NAMES.pointsTransaction).insert({
      user_id: userId,
      transaction_type: type,
      amount,
      source,
      source_id: null,
      description,
      balance_after: balanceAfter,
      metadata: null,
      created_at: new Date().toISOString(),
    });
  }
}

/**
 * 创建积分周期管理器实例
 */
export function createPointsCycleManager(env: Env): PointsCycleManager {
  return new PointsCycleManager(env);
}

/**
 * 便捷函数：获取用户积分周期信息
 */
export async function getUserPointsCycleInfo(
  env: Env,
  userId: string
): Promise<UserPointsCycleInfo> {
  const cycleManager = createPointsCycleManager(env);
  return cycleManager.getUserPointsCycle(userId);
}

/**
 * 便捷函数：初始化用户积分周期
 */
export async function initializeUserPointsCycle(
  env: Env,
  userId: string,
  membershipLevel: MembershipLevel,
  startDate?: Date
): Promise<void> {
  const cycleManager = createPointsCycleManager(env);
  return cycleManager.initializeUserPointsCycle(userId, membershipLevel, startDate);
}

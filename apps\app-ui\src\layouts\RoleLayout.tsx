import { Outlet } from 'react-router'
import { RoleHeader } from '@/components/role-header'
import { StreamingProvider } from '@/components/data-stream-handler'
import { AnimatePresence } from 'framer-motion'
import { useDeviceSafeArea } from '@/hooks/use-mobile-viewport'

/**
 * 角色上下文布局
 * 适用于需要角色信息的页面
 * 使用全局角色状态管理，不再创建本地 Provider
 */
export function RoleLayout() {
  const safeArea = useDeviceSafeArea()
  return (
    <StreamingProvider>
      <div className="flex flex-col overflow-hidden min-h-screen">
        <RoleHeader />
        <main
          className="flex-1 overflow-hidden"
          style={{ minHeight: `calc(100vh - ${safeArea.top}px - 4rem)` }}
        >
          <AnimatePresence mode="wait">
            <Outlet />
          </AnimatePresence>
        </main>
      </div>
    </StreamingProvider>
  )
}

import React from 'react'
import { Button } from '@heroui/react'
import { useNavigate } from 'react-router'

export default function Android10Test() {
  const navigate = useNavigate()

  return (
    <div className="min-h-screen p-4">
      {/* 页面标题 */}
      <div className="mb-6">
        <Button variant="light" onPress={() => navigate(-1)} className="mb-4">
          ← 返回
        </Button>
        <h1 className="text-2xl font-bold">Android 10 渐变兼容性测试</h1>
      </div>

      {/* 测试区域 */}
      <div className="space-y-6">
        {/* 测试 1: 原始的 HeroUI 主题颜色渐变 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">测试 1: HeroUI 主题颜色渐变</h2>
          <div className="h-32 bg-gradient-to-b from-background via-content1 to-content2 rounded-lg flex items-center justify-center">
            <p className="text-foreground font-medium">from-background via-content1 to-content2</p>
          </div>
        </div>

        {/* 测试 2: 其他 HeroUI 颜色组合 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">测试 2: 其他 HeroUI 颜色组合</h2>
          <div className="h-32 bg-gradient-to-r from-content1 to-content3 rounded-lg flex items-center justify-center">
            <p className="text-foreground font-medium">from-content1 to-content3</p>
          </div>
        </div>

        {/* 测试 3: 混合标准 Tailwind 颜色 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">测试 3: 混合标准 Tailwind 颜色</h2>
          <div className="h-32 bg-gradient-to-br from-primary via-purple-500 to-secondary rounded-lg flex items-center justify-center">
            <p className="text-white font-medium">from-primary via-purple-500 to-secondary</p>
          </div>
        </div>

        {/* 测试 4: 标准 Tailwind 颜色 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">测试 4: 标准 Tailwind 颜色</h2>
          <div className="h-32 bg-gradient-to-r from-blue-500 to-green-500 rounded-lg flex items-center justify-center">
            <p className="text-white font-medium">from-blue-500 to-green-500</p>
          </div>
        </div>

        {/* 测试 5: 复杂渐变 */}
        <div className="space-y-2">
          <h2 className="text-lg font-semibold">测试 5: 复杂渐变</h2>
          <div className="h-32 bg-gradient-to-br from-pink-500 via-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
            <p className="text-white font-medium">from-pink-500 via-purple-500 to-indigo-600</p>
          </div>
        </div>

        {/* 测试信息 */}
        <div className="mt-8 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <h3 className="font-semibold mb-2">测试说明</h3>
          <ul className="text-sm space-y-1 text-gray-600 dark:text-gray-300">
            <li>• 如果你在 Android 10 设备上看到渐变效果，说明修复生效了</li>
            <li>• 如果只看到纯色背景，说明渐变未生效</li>
            <li>• 在现代浏览器中，渐变应该正常显示</li>
            <li>• 可以通过开发者工具检查 CSS 变量是否正确设置</li>
          </ul>
        </div>

        {/* 调试信息 */}
        <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <h3 className="font-semibold mb-2">调试信息</h3>
          <div className="text-sm font-mono">
            <p>User Agent: {navigator.userAgent}</p>
            <p>
              支持 color-mix:{' '}
              {CSS.supports('color', 'color-mix(in srgb, red 50%, blue)') ? '是' : '否'}
            </p>
            <p>支持 CSS 变量: {CSS.supports('color', 'var(--test)') ? '是' : '否'}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

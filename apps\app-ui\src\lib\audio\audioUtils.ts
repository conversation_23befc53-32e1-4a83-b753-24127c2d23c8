/**
 * 音频处理工具模块
 * 提供音频格式检测、转换、压缩等功能
 */

// 支持的音频格式
export const SUPPORTED_AUDIO_FORMATS = {
  webm: 'audio/webm',
  mp4: 'audio/mp4',
  wav: 'audio/wav',
  m4a: 'audio/mp4'
} as const

export type AudioFormat = keyof typeof SUPPORTED_AUDIO_FORMATS

// 音频质量配置
export interface AudioQualityConfig {
  sampleRate: number // 采样率
  bitRate: number // 比特率
  channels: number // 声道数
}

export const AUDIO_QUALITY_PRESETS = {
  high: { sampleRate: 48000, bitRate: 128000, channels: 1 },
  medium: { sampleRate: 24000, bitRate: 64000, channels: 1 },
  low: { sampleRate: 16000, bitRate: 32000, channels: 1 }
} as const

/**
 * 检测浏览器支持的音频格式
 */
export function detectSupportedAudioFormat(): AudioFormat {
  const mediaRecorder = window.MediaRecorder

  if (!mediaRecorder) {
    throw new Error('浏览器不支持音频录制')
  }

  // 按优先级检测支持的格式
  const formatPriority: AudioFormat[] = ['webm', 'mp4', 'wav']

  for (const format of formatPriority) {
    if (mediaRecorder.isTypeSupported(SUPPORTED_AUDIO_FORMATS[format])) {
      return format
    }
  }

  throw new Error('浏览器不支持任何音频格式')
}

/**
 * 获取音频文件信息
 */
export async function getAudioInfo(audioBlob: Blob): Promise<{
  duration: number
  size: number
  format: string
}> {
  return new Promise((resolve, reject) => {
    const audio = new Audio()
    const url = URL.createObjectURL(audioBlob)

    audio.onloadedmetadata = () => {
      URL.revokeObjectURL(url)
      resolve({
        duration: audio.duration,
        size: audioBlob.size,
        format: audioBlob.type
      })
    }

    audio.onerror = () => {
      URL.revokeObjectURL(url)
      reject(new Error('无法获取音频信息'))
    }

    audio.src = url
  })
}

/**
 * 压缩音频文件（通过重新编码）
 */
export async function compressAudio(
  audioBlob: Blob,
  quality: keyof typeof AUDIO_QUALITY_PRESETS = 'medium'
): Promise<Blob> {
  try {
    // 创建AudioContext
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

    // 解码音频数据
    const arrayBuffer = await audioBlob.arrayBuffer()
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

    // 获取质量配置
    const config = AUDIO_QUALITY_PRESETS[quality]

    // 重采样到目标采样率
    const resampledBuffer = resampleAudioBuffer(audioBuffer, config.sampleRate)

    // 转换为Blob（这里简化处理，实际项目中可能需要更复杂的编码）
    const compressedBlob = audioBufferToBlob(resampledBuffer, audioBlob.type)

    audioContext.close()
    return compressedBlob
  } catch (error) {
    console.warn('音频压缩失败，返回原始文件:', error)
    return audioBlob
  }
}

/**
 * 重采样音频缓冲区
 */
function resampleAudioBuffer(audioBuffer: AudioBuffer, targetSampleRate: number): AudioBuffer {
  if (audioBuffer.sampleRate === targetSampleRate) {
    return audioBuffer
  }

  const ratio = audioBuffer.sampleRate / targetSampleRate
  const newLength = Math.round(audioBuffer.length / ratio)

  // 创建新的AudioBuffer
  const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
  const newBuffer = audioContext.createBuffer(
    audioBuffer.numberOfChannels,
    newLength,
    targetSampleRate
  )

  // 简单的线性插值重采样
  for (let channel = 0; channel < audioBuffer.numberOfChannels; channel++) {
    const oldData = audioBuffer.getChannelData(channel)
    const newData = newBuffer.getChannelData(channel)

    for (let i = 0; i < newLength; i++) {
      const oldIndex = i * ratio
      const index = Math.floor(oldIndex)
      const fraction = oldIndex - index

      if (index + 1 < oldData.length) {
        newData[i] = oldData[index] * (1 - fraction) + oldData[index + 1] * fraction
      } else {
        newData[i] = oldData[index] || 0
      }
    }
  }

  return newBuffer
}

/**
 * 将AudioBuffer转换为Blob
 */
function audioBufferToBlob(audioBuffer: AudioBuffer, mimeType: string): Blob {
  // 简化实现：将音频数据转换为WAV格式
  const numberOfChannels = audioBuffer.numberOfChannels
  const length = audioBuffer.length * numberOfChannels * 2
  const buffer = new ArrayBuffer(44 + length)
  const view = new DataView(buffer)

  // WAV文件头
  const writeString = (offset: number, string: string) => {
    for (let i = 0; i < string.length; i++) {
      view.setUint8(offset + i, string.charCodeAt(i))
    }
  }

  // RIFF chunk descriptor
  writeString(0, 'RIFF')
  view.setUint32(4, 36 + length, true)
  writeString(8, 'WAVE')

  // FMT sub-chunk
  writeString(12, 'fmt ')
  view.setUint32(16, 16, true) // SubChunk1Size
  view.setUint16(20, 1, true) // AudioFormat (PCM)
  view.setUint16(22, numberOfChannels, true)
  view.setUint32(24, audioBuffer.sampleRate, true)
  view.setUint32(28, audioBuffer.sampleRate * numberOfChannels * 2, true) // ByteRate
  view.setUint16(32, numberOfChannels * 2, true) // BlockAlign
  view.setUint16(34, 16, true) // BitsPerSample

  // Data sub-chunk
  writeString(36, 'data')
  view.setUint32(40, length, true)

  // 写入音频数据
  let offset = 44
  for (let i = 0; i < audioBuffer.length; i++) {
    for (let channel = 0; channel < numberOfChannels; channel++) {
      const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]))
      view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7fff, true)
      offset += 2
    }
  }

  return new Blob([buffer], { type: mimeType })
}

/**
 * 检查浏览器兼容性
 */
export function checkBrowserCompatibility(): {
  mediaRecorder: boolean
  audioContext: boolean
  getUserMedia: boolean
  fullSupport: boolean
} {
  const mediaRecorder = !!window.MediaRecorder
  const audioContext = !!(window.AudioContext || (window as any).webkitAudioContext)
  const getUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)

  return {
    mediaRecorder,
    audioContext,
    getUserMedia,
    fullSupport: mediaRecorder && audioContext && getUserMedia
  }
}

/**
 * 生成音频波形数据（用于可视化）
 */
export async function generateWaveformData(
  audioBlob: Blob,
  samples: number = 100
): Promise<number[]> {
  try {
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
    const arrayBuffer = await audioBlob.arrayBuffer()
    const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

    const channelData = audioBuffer.getChannelData(0) // 使用第一个声道
    const blockSize = Math.floor(channelData.length / samples)
    const waveformData: number[] = []

    for (let i = 0; i < samples; i++) {
      const start = i * blockSize
      const end = start + blockSize
      let sum = 0

      for (let j = start; j < end && j < channelData.length; j++) {
        sum += Math.abs(channelData[j])
      }

      waveformData.push(sum / blockSize)
    }

    audioContext.close()
    return waveformData
  } catch (error) {
    console.error('生成波形数据失败:', error)
    return new Array(samples).fill(0)
  }
}

/**
 * 检查文件大小限制
 */
export function validateAudioFile(
  audioBlob: Blob,
  maxSizeMB: number = 10
): {
  valid: boolean
  error?: string
  sizeMB: number
} {
  const sizeMB = audioBlob.size / (1024 * 1024)

  if (sizeMB > maxSizeMB) {
    return {
      valid: false,
      error: `文件大小超过限制 (${sizeMB.toFixed(1)}MB > ${maxSizeMB}MB)`,
      sizeMB
    }
  }

  return { valid: true, sizeMB }
}

import { Hono } from 'hono'
import { cors } from 'hono/cors'
import { logger } from 'hono/logger'
import { prettyJSON } from 'hono/pretty-json'
import type { Env } from '@/types/env'
import { ApiError } from '@/types/api'
import type { Message<PERSON><PERSON>, ScheduledController, ExecutionContext } from '@cloudflare/workers-types'
import authRoutes from '@/routes/auth'
import userRoutes from '@/routes/users'
import chatRoutes from '@/routes/chat'
import chatv2Routes from '@/routes/chatv2'
import characterRoutes from '@/routes/characters'
import dialogueRoutes from '@/routes/dialogue'
import imageGenerationRoutes from '@/routes/image-generation'
import audioRoutes from '@/routes/audio'
import historyRoutes from '@/routes/history'
import templateRoutes from '@/routes/templates'
import photoAlbumGenerationRoutes from '@/routes/photo-album-generation'
import uploadRoutes from '@/routes/upload'
import membershipRoutes from '@/routes/membership'
import ttsRoutes from '@/routes/tts'
import tts2Routes from '@/routes/tts2'
import tts3Routes from '@/routes/tts3'
import scriptRoutes from '@/routes/scripts'
import deviceRoutes from '@/routes/devices'
import voicesRoutes from '@/routes/voices'
import speechToTextRoutes from '@/routes/speech-to-text'
import adminConfigRoutes from '@/routes/admin/admin-config'
import pointsCycleRoutes from '@/routes/points-cycle'
import pointsRoutes from '@/routes/points'
import { paymentRoutes } from '@/routes/payment'
import backgroundRoutes from '@/routes/background'
import imageGenerationV2Routes from '@/routes/image-generation-v2'
import multimodalVideoGenerationRoutes from '@/routes/multimodal-video-generation'
import referralRoutes from '@/routes/referral'
import adminReferralRoutes from '@/routes/admin/admin-referral'
import activationCodeRoutes from '@/routes/activation-codes'
import adminUsersRoutes from '@/routes/admin/admin-users'
import adminMembershipRoutes from '@/routes/admin/admin-membership'
import adminMarketingRoutes from '@/routes/admin/admin-marketing'
import adminDevicesRoutes from '@/routes/admin/admin-devices'
import adminDeviceCommandSetsRoutes from '@/routes/admin/admin-device-command-sets'
import adminDeviceFunctionsRoutes from '@/routes/admin/admin-device-functions'
import adminDeviceModesRoutes from '@/routes/admin/admin-device-modes'
import adminOrdersRoutes from '@/routes/admin/admin-orders'
import adminPointsRoutes from '@/routes/admin/admin-points'
import adminScriptsRoutes from '@/routes/admin/admin-scripts'
import adminTemplatesRoutes from '@/routes/admin/admin-templates'
import adminCharactersRoutes from '@/routes/admin/admin-characters'
import appUpdateRoutes from '@/routes/app-update'
import sseRoutes from '@/routes/sse'
import characterMediaRoutes from '@/routes/character-media'
import { createQueueHandlers } from '@/queues'
import { languageMiddleware } from '@/middleware/language'

// 创建 Hono 应用实例
const app = new Hono<{ Bindings: Env }>()

// 全局中间件
app.use('*', logger())
app.use('*', prettyJSON())
app.use('*', languageMiddleware)
app.use(
  '*',
  cors({
    origin: origin => {
      // 允许的源列表
      const allowedOrigins = [
        'http://localhost:5173',
        'http://localhost:3000',
        'https://localhost',
        'https://localhost:5173',
        'http://*************:5173',
        'https://*************:5173',
        'http://*************:5173',
        'https://*************:5173',
        'https://localhost:3000',
        'capacitor://localhost',
        'ionic://localhost',
        'file://',
        // 生产环境域名
        'https://pleasurehub.app',
        'https://www.pleasurehub.app',
        'https://api.pleasurehub.app',
        'https://demo.pleasurehub.app'
      ]

      // 如果没有 origin（比如直接访问），允许
      if (!origin) return null

      // 检查是否在允许列表中
      if (allowedOrigins.includes(origin)) return origin

      // 开发环境允许 localhost 的任何端口
      if (origin.match(/^https?:\/\/localhost(:\d+)?$/)) return origin

      // 默认拒绝
      return null
    },
    allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization'],
    credentials: true // 支持 credentials
  })
)

// 健康检查端点
app.get('/health', c => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'pleasurehub-backend-service'
  })
})

// 路由定义 (先定义再使用)
const adminRoutes = new Hono<{ Bindings: Env }>()

// 挂载管理后台配置路由
adminRoutes.route('/config', adminConfigRoutes)
adminRoutes.route('/referral', adminReferralRoutes)
adminRoutes.route('/users', adminUsersRoutes)
adminRoutes.route('/membership', adminMembershipRoutes)
adminRoutes.route('/marketing', adminMarketingRoutes)
adminRoutes.route('/devices', adminDevicesRoutes)
adminRoutes.route('/devices/command-sets', adminDeviceCommandSetsRoutes)
adminRoutes.route('/devices/functions', adminDeviceFunctionsRoutes)
adminRoutes.route('/devices/modes', adminDeviceModesRoutes)
adminRoutes.route('/orders', adminOrdersRoutes)
adminRoutes.route('/points', adminPointsRoutes)
adminRoutes.route('/content/scripts', adminScriptsRoutes)
adminRoutes.route('/content/templates', adminTemplatesRoutes)
adminRoutes.route('/characters', adminCharactersRoutes)

// API 路由组
const api = new Hono<{ Bindings: Env }>()

// 挂载路由
api.route('/auth', authRoutes)
api.route('/users', userRoutes)
api.route('/chat', chatRoutes)
api.route('/chatv2', chatv2Routes)
api.route('/characters', characterRoutes)
api.route('/dialogue', dialogueRoutes)
api.route('/image-generation', imageGenerationRoutes)
api.route('/audio', audioRoutes)
api.route('/history', historyRoutes)
api.route('/upload', uploadRoutes)
api.route('/membership', membershipRoutes)
api.route('/payment', paymentRoutes)
api.route('/admin', adminRoutes)
api.route('/templates', templateRoutes)
api.route('/photo-album-generation', photoAlbumGenerationRoutes)
api.route('/tts', ttsRoutes)
api.route('/tts2', tts2Routes)
api.route('/tts3', tts3Routes)
api.route('/scripts', scriptRoutes)
api.route('/devices', deviceRoutes)
api.route('/voices', voicesRoutes)
api.route('/speech-to-text', speechToTextRoutes)
api.route('/points-cycle', pointsCycleRoutes)
api.route('/points', pointsRoutes)
api.route('/background', backgroundRoutes)
api.route('/image-generation-v2', imageGenerationV2Routes)
api.route('/multimodal-video-generation', multimodalVideoGenerationRoutes)
api.route('/referral', referralRoutes)
api.route('/activation-codes', activationCodeRoutes)
api.route('/app-update', appUpdateRoutes)
api.route('/sse', sseRoutes)
api.route('/character-media', characterMediaRoutes)

// 原来的图片生成路由（保持兼容性）
// 直接挂载到 api 根路径下
api.route('/', imageGenerationRoutes)

// 挂载 API 路由
app.route('/api', api)

// 404 处理
app.notFound(c => {
  return c.json(
    {
      error: 'Not Found',
      message: `Route ${c.req.method} ${c.req.path} not found`
    },
    404
  )
})

// 全局错误处理
app.onError((err, c) => {
  console.error('Global error handler:', err)

  if (err instanceof ApiError) {
    return c.json(
      {
        error: err.message,
        code: err.code
      },
      err.statusCode as any
    )
  }

  return c.json(
    {
      error: 'Internal Server Error'
    },
    500
  )
})

// Queue Consumer - 使用新的队列架构
// 统一队列处理函数
async function queueHandler(
  batch: MessageBatch<any>,
  env: Env,
  ctx: ExecutionContext
): Promise<void> {
  // 根据队列名称判断处理类型
  const queueName = batch.queue

  console.log(`🔄 队列处理: ${queueName}，任务数量: ${batch.messages.length}`)

  try {
    // 创建队列处理器
    const queueHandlers = createQueueHandlers(env)

    if (queueName.includes('audio-processing')) {
      // 音频处理队列
      await queueHandlers.audioQueueHandler(batch, env, ctx)
    } else if (queueName.includes('image-generation')) {
      // 图片生成队列
      await queueHandlers.imageQueueHandler(batch, env, ctx)
    } else if (queueName.includes('video-generation')) {
      // 视频生成队列
      await queueHandlers.videoQueueHandler(batch, env, ctx)
    } else if (queueName.includes('photo-album-generation')) {
      // 写真集生成队列
      await queueHandlers.photoAlbumQueueHandler(batch, env, ctx)
    } else if (queueName.includes('face-swap')) {
      // 换脸处理队列
      await queueHandlers.faceSwapQueueHandler(batch, env, ctx)
    } else {
      console.warn(`⚠️ 未知队列类型: ${queueName}`)
    }
  } catch (error) {
    console.error(`❌ 队列 ${queueName} 处理失败:`, error)
    throw error
  }
}

/**
 * Cron Trigger Handler - 定时任务处理器
 * 处理积分周期自动重置任务
 */
async function scheduledHandler(
  controller: ScheduledController,
  env: Env,
  ctx: ExecutionContext
): Promise<void> {
  const cronTime = new Date(controller.scheduledTime)
  console.log(`🕐 积分周期定时任务开始执行 - ${cronTime.toISOString()}`)

  try {
    // 创建积分周期管理器
    const { createPointsCycleManager } = await import('@/lib/membership/points-cycle')
    const cycleManager = createPointsCycleManager(env)

    // 批量处理过期积分
    const result = await cycleManager.batchProcessExpiredPoints()

    console.log(`✅ 积分周期定时任务执行完成:`)
    console.log(`   - 处理用户数量: ${result.processedCount}`)
    console.log(`   - 错误数量: ${result.errors.length}`)

    if (result.errors.length > 0) {
      console.error('❌ 处理过程中的错误:')
      result.errors.forEach((error, index) => {
        console.error(`   ${index + 1}. ${error}`)
      })
    }

    // 记录任务完成状态
    console.log(`🎯 积分周期定时任务完成 - 耗时: ${Date.now() - cronTime.getTime()}ms`)
  } catch (error) {
    console.error('❌ 积分周期定时任务执行失败:', error)

    // 重新抛出错误，让 Cloudflare 知道任务失败
    throw error
  }
}

// 按照 Cloudflare Workers 要求的格式导出
const worker = {
  fetch: app.fetch.bind(app),
  queue: queueHandler,
  scheduled: scheduledHandler
}

export default worker

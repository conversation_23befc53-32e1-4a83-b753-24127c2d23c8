import type { CharacterData as CharacterType } from './index'

export const genderMapping = {
  female: '女性',
  male: '男性'
}

export const characterMapping = {
  relationship: {
    friend: '朋友',
    lover: '恋人',
    mentor: '导师',
    family: '家人',
    colleague: '同事',
    classmate: '同学'
  },

  ethnicity: {
    caucasian: '白种人',
    latina: '拉丁裔',
    asian: '亚洲人',
    arab: '阿拉伯人',
    black: '黑人'
  },

  age: {
    teen: '18+',
    '20s': '20多岁',
    '30s': '30多岁',
    '40-55': '40-55岁'
  },

  eyeColor: {
    brown: '棕色',
    blue: '蓝色',
    green: '绿色'
  },

  hairStyle: {
    female: {
      straight: '直发',
      curly: '自然卷发',
      braids: '编发',
      'high-bun': '高丸子头',
      ponytail: '马尾辫',
      'short-bangs': '齐刘海短发',
      pixie: '超短发',
      'long-waves': '长波浪卷',
      'twin-tails': '双马尾'
    },
    male: {
      'buzz-cut': '短寸',
      pompadour: '庞巴度',
      'side-part': '三七侧分',
      'messy-quiff': '凌乱蓬松短发',
      'long-straight': '长直发',
      curly: '自然卷发',
      'man-bun': '丸子头',
      'textured-crop': '渐变短寸',
      dreadlocks: '脏辫'
    }
  },

  hairColor: {
    black: '黑色',
    brown: '棕色',
    blonde: '金色',
    red: '红色',
    white: '白色/银色',
    blue: '蓝色',
    purple: '紫色',
    pink: '粉色'
  },

  bodyType: {
    female: {
      slim: '纤细',
      rectangle: '直筒',
      athletic: '健美',
      hourglass: '沙漏',
      curvy: '丰腴'
    },
    male: {
      slim: '瘦小',
      athletic: '健美',
      average: '普通',
      heavy: '肥胖'
    }
  },

  breastSize: {
    flat: '扁平的',
    petite: '小巧的',
    moderate: '中等的',
    full: '丰满的',
    busty: '胸部丰盈的'
  },

  buttSize: {
    toned: '紧实小巧',
    slim: '纤细',
    medium: '中等挺翘',
    full: '丰满圆润',
    athletic: '健康运动'
  },

  voice: {
    soft: '柔和的',
    deep: '低沉的',
    sweet: '甜美的',
    husky: '沙哑的',
    energetic: '有活力的',
    seductive: '诱惑的',
    childish: '稚嫩的',
    mature: '成熟的'
  }
}

export const personalityOptions = [
  {
    value: 'caregiver',
    label: '照顾者',
    description: '关怀、保护，随时提供安慰与支持',
    emoji: '🤗'
  },
  {
    value: 'jester',
    label: '逗趣者',
    description: '幽默、玩闹，总是能让你开怀大笑',
    emoji: '😂'
  },
  {
    value: 'submissive',
    label: '顺从者',
    description: '服从、温顺，乐于跟随他人',
    emoji: '🙇'
  },
  {
    value: 'mean',
    label: '刻薄者',
    description: '冷漠、轻蔑，经常带有讽刺意味',
    emoji: '😒'
  },
  {
    value: 'sage',
    label: '智者',
    description: '睿智、深思熟虑，是指导的来源',
    emoji: '🧠'
  },
  {
    value: 'temptress',
    label: '诱惑者',
    description: '调情、顽皮，总让你欲罢不能',
    emoji: '😏'
  },
  {
    value: 'lover',
    label: '恋人',
    description: '浪漫、深情，珍视深厚的情感连接',
    emoji: '💝'
  },
  {
    value: 'confidant',
    label: '知己',
    description: '值得信赖，善于倾听，随时提供建议',
    emoji: '🤝'
  },
  {
    value: 'innocent',
    label: '纯真者',
    description: '乐观、天真，以好奇之心看世界',
    emoji: '🌻'
  },
  {
    value: 'dominant',
    label: '支配者',
    description: '自信、掌控，喜欢发号施令',
    emoji: '👑'
  },
  {
    value: 'nympho',
    label: '热情者',
    description: '难以满足，热情如火，渴望亲密接触',
    emoji: '🔥'
  },
  {
    value: 'experimenter',
    label: '探索者',
    description: '好奇心强，愿意尝试各种新鲜事物',
    emoji: '🧪'
  }
]

export const getDescription = (data: CharacterType) => {
  // const ethnicity =
  //   characterMapping.ethnicity[
  //     data.ethnicity?.split('-')[1] as keyof typeof characterMapping.ethnicity
  //   ] || '';
  const relationship =
    characterMapping.relationship[
      data.relationship as keyof typeof characterMapping.relationship
    ] || data.relationship

  const personality =
    personalityOptions.find(p => p.value === data.personality)?.description || data.personality

  return `${data.name}是你的${relationship}, ${personality}`
}

// 获取标签函数
export const getLabel = (key: string, value: string): string => {
  if (!value) return '未设置'

  // 渲染不同选项的标签
  switch (key) {
    case 'relationship': {
      return (
        characterMapping.relationship[value as keyof typeof characterMapping.relationship] || value
      )
    }

    case 'ethnicity': {
      // 处理复合值，例如 male-caucasian
      const ethnicity = value.split('-')[1] || value
      return (
        characterMapping.ethnicity[ethnicity as keyof typeof characterMapping.ethnicity] ||
        ethnicity
      )
    }

    case 'age': {
      return characterMapping.age[value as keyof typeof characterMapping.age] || value
    }

    case 'eyeColor': {
      return characterMapping.eyeColor[value as keyof typeof characterMapping.eyeColor] || value
    }

    case 'hairColor': {
      return characterMapping.hairColor[value as keyof typeof characterMapping.hairColor] || value
    }

    case 'personality': {
      return personalityOptions.find(p => p.value === value)?.label || value
    }

    case 'voice': {
      return characterMapping.voice[value as keyof typeof characterMapping.voice] || value
    }

    default:
      return value
  }
}

// 获取图标的颜色
export const getColorForField = (key: string, value: string): string => {
  if (!value || value === '未设置') return 'text-muted-foreground'

  switch (key) {
    case 'eyeColor':
      return (
        {
          brown: 'text-amber-700 dark:text-amber-500',
          blue: 'text-blue-600 dark:text-blue-400',
          green: 'text-green-600 dark:text-green-400'
        }[value] || 'text-primary'
      )

    case 'hairColor':
      return (
        {
          black: 'text-gray-900 dark:text-gray-300',
          brown: 'text-amber-800 dark:text-amber-500',
          blonde: 'text-yellow-400 dark:text-yellow-300',
          red: 'text-red-600 dark:text-red-400',
          white: 'text-gray-200 dark:text-gray-100',
          blue: 'text-blue-500 dark:text-blue-400',
          purple: 'text-purple-600 dark:text-purple-400',
          pink: 'text-pink-500 dark:text-pink-400'
        }[value] || 'text-primary'
      )

    case 'personality':
      return 'text-indigo-600 dark:text-indigo-400'

    default:
      return 'text-primary'
  }
}

// 将角色数据转换为关键词字符串
export const generateKeywords = (gender: string, data: CharacterType): string => {
  const keywords = []

  // 基本信息
  if (data.ethnicity) {
    const ethnicityParts = data.ethnicity.split('-')
    const ethnicityType = ethnicityParts[1] || data.ethnicity
    keywords.push(
      characterMapping.ethnicity[ethnicityType as keyof typeof characterMapping.ethnicity] ||
        ethnicityType
    )
  }

  if (data.age) {
    keywords.push(characterMapping.age[data.age as keyof typeof characterMapping.age] || data.age)
  }

  if (data.hairStyle) keywords.push(data.hairStyle)

  if (data.hairColor) {
    keywords.push(
      `${
        characterMapping.hairColor[data.hairColor as keyof typeof characterMapping.hairColor] ||
        data.hairColor
      }头发`
    )
  }

  if (data.eyeColor) {
    keywords.push(
      `${
        characterMapping.eyeColor[data.eyeColor as keyof typeof characterMapping.eyeColor] ||
        data.eyeColor
      }眼睛`
    )
  }

  if (data.bodyType) keywords.push(data.bodyType)

  // 女性特有属性
  if (gender === 'female') {
    if (data.breastSize) keywords.push(data.breastSize)
    if (data.buttSize) keywords.push(data.buttSize)
  }

  if (data.personality) {
    keywords.push(
      personalityOptions.find(p => p.value === data.personality)?.label || data.personality
    )
  }

  if (data.clothing) keywords.push(data.clothing)

  if (data.voice) {
    keywords.push(
      characterMapping.voice[data.voice as keyof typeof characterMapping.voice] || data.voice
    )
  }

  // 添加性别信息
  keywords.push(gender === 'male' ? '男性' : '女性')

  return keywords.join('，')
}

export const getRoleDescription = (data: CharacterType & { gender: string }) => {
  const name = data.name
  const relationship =
    characterMapping.relationship[
      data.relationship as keyof typeof characterMapping.relationship
    ] || data.relationship
  const age = characterMapping.age[data.age as keyof typeof characterMapping.age] || data.age
  const ethnicity =
    characterMapping.ethnicity[data.ethnicity as keyof typeof characterMapping.ethnicity] ||
    data.ethnicity
  const eyeColor =
    characterMapping.eyeColor[data.eyeColor as keyof typeof characterMapping.eyeColor] ||
    data.eyeColor
  const hairStyle =
    characterMapping.hairStyle[data.hairStyle as keyof typeof characterMapping.hairStyle] ||
    data.hairStyle
  const hairColor =
    characterMapping.hairColor[data.hairColor as keyof typeof characterMapping.hairColor] ||
    data.hairColor
  let bodyType = ''
  let breastSize = ''
  let buttSize = ''
  if (data.gender === 'female') {
    bodyType =
      characterMapping.bodyType.female[
        data.bodyType as keyof typeof characterMapping.bodyType.female
      ] ||
      data.bodyType ||
      ''
    breastSize =
      characterMapping.breastSize[data.breastSize as keyof typeof characterMapping.breastSize] ||
      data.breastSize ||
      ''
    buttSize =
      characterMapping.buttSize[data.buttSize as keyof typeof characterMapping.buttSize] ||
      data.buttSize ||
      ''
  } else {
    bodyType =
      characterMapping.bodyType.male[
        data.bodyType as keyof typeof characterMapping.bodyType.male
      ] || data.bodyType
  }
  const personality =
    personalityOptions.find(p => p.value === data.personality)?.description || data.personality
  const voice =
    characterMapping.voice[data.voice as keyof typeof characterMapping.voice] || data.voice

  if (data.gender === 'female') {
    return `你是叫${name}, 是我的${relationship}, 今年${age}, 是${ethnicity}人, 有着${eyeColor}色的眼睛, 留着${hairStyle}的发型, 头发颜色是${hairColor}, 有着${bodyType}的身材, 有着${breastSize}的胸部, 有着${buttSize}的臀部, 有着${personality}的性格, 有着${voice}的声音`
  } else {
    return `你是叫${name}, 是我的${relationship}, 今年${age}, 是${ethnicity}人, 有着${eyeColor}色的眼睛, 留着${hairStyle}的发型, 头发颜色是${hairColor}, 有着${bodyType}的身材, 有着${personality}的性格, 有着${voice}的声音`
  }
}

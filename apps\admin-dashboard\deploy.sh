#!/bin/bash

# admin-dashboard 部署脚本
# 使用方法: ./deploy.sh [preview|production]

set -e

ENV=${1:-production}

echo "🚀 部署 admin-dashboard 到 ${ENV} 环境..."

# 检查登录
if ! pnpm wrangler whoami > /dev/null 2>&1; then
    echo "❌ 请先登录: pnpm wrangler login"
    exit 1
fi

# 构建项目
echo "📦 构建项目..."
pnpm build

# 部署到 Cloudflare Pages
if [ "$ENV" = "preview" ]; then
    echo "🔄 部署到预览环境..."
    pnpm wrangler pages deploy dist --env preview
else
    echo "🔄 部署到生产环境..."
    pnpm wrangler pages deploy dist
fi

echo "✅ 部署完成！"

# 获取部署 URL
echo "📋 获取部署信息..."
pnpm wrangler pages deployment list --project-name admin-dashboard | head -5

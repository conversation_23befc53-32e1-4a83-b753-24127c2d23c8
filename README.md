# 项目说明

> ⚠️ 本项目要求 Node.js 版本 >= 22，请确保本地开发环境已安装对应版本。

## 项目简介

本项目为 AI 女友聊天应用的多端一体化解决方案，采用 monorepo 架构，涵盖 Web 前端、后台服务、移动端、支付系统、自研插件及第三方服务 worker，便于统一管理和协同开发。

---

## 目录结构

```
rc-demo-app/
├── apps/                # 应用层，包含前后端、管理后台、支付、边缘函数等
│   ├── admin-dashboard/     # 后台管理面板（React/Vite）
│   ├── app-ui/              # 主前端应用（React/Vite/Capacitor）
│   ├── backend-service/     # 后端服务（API/数据库/队列）
│   ├── pay-web/             # 支付前端
│   ├── edge-functions/      # 边缘函数（如 Supabase Functions）
│   └── elevenlabs-worker/   # 第三方服务 worker（如语音处理）
├── packages/            # 公共包与插件
│   └── capacitor-ble-advertiser/ # 自研 BLE 广播插件（iOS/Android）
├── package.json         # 根依赖管理
├── pnpm-workspace.yaml  # pnpm 工作区配置
└── README.md            # 项目说明
```

---

## 子项目功能与启动方式

### 1. admin-dashboard

- 后台管理面板，基于 React/Vite。
- 启动：
  ```bash
  cd apps/admin-dashboard
  pnpm install
  pnpm dev
  ```

### 2. app-ui

- 主前端应用，支持 Web 和移动端（Capacitor）。
- 启动：
  ```bash
  cd apps/app-ui
  pnpm install
  pnpm dev
  ```
- 构建移动端：
  ```bash
  pnpm build
  # 见 Capacitor 相关命令
  ```

### 3. backend-service

- 后端服务，包含 API、数据库、队列等。
- 启动：
  ```bash
  cd apps/backend-service
  pnpm install
  pnpm dev
  ```

### 4. pay-web

- 支付相关 Web 前端。
- 启动：
  ```bash
  cd apps/pay-web
  pnpm install
  pnpm dev
  ```

### 5. edge-functions

- 边缘函数，适配 Supabase 等云服务。
- 参考 `apps/edge-functions/README.md`。

### 6. elevenlabs-worker

- 第三方服务 worker，处理语音等功能。
- 启动：
  ```bash
  cd apps/elevenlabs-worker
  pnpm install
  pnpm dev
  ```

### 7. capacitor-ble-advertiser

- 自研 Capacitor 插件，支持 BLE 广播。
- 见 `packages/capacitor-ble-advertiser/INSTALLATION.md`。

---

## 依赖管理与开发流程

- 采用 pnpm 作为包管理工具，统一管理所有子项目依赖。
- 根目录下执行 `pnpm install` 可安装所有依赖。
- 各子项目可独立开发、调试和构建。
- 统一的 `pnpm-workspace.yaml` 管理依赖和包引用。

---

## 常见开发命令

```bash
# 安装所有依赖
pnpm install

# 启动指定子项目
cd apps/app-ui && pnpm dev
cd apps/backend-service && pnpm dev
# 其他子项目同理

# 构建前端
cd apps/app-ui && pnpm build

# 构建后端
cd apps/backend-service && pnpm build

# 移动端相关（需先构建前端）
cd apps/app-ui && pnpm build
cd apps/app-ui && pnpm sync
cd apps/app-ui && pnpm open:android  # 打开 Android Studio
cd apps/app-ui && pnpm open:ios      # 打开 Xcode
```

---

## 真机测试流程

调整 capacitor.config.ts 文件

```
// 改为你本机的 ip
url: 'http://192.168.1.xxx:5173'

// 执行(首次)
pnpm run cap:android

// 后续设计到原生代码或者配置改动，手动执行
pnpm run sync
```

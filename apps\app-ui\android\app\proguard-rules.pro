# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# =============================================
# Capacitor APP 安全混淆规则
# =============================================

# 基础混淆设置
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# 保持行号信息用于调试（生产环境可注释掉）
-keepattributes SourceFile,LineNumberTable
-renamesourcefileattribute SourceFile

# =============================================
# Capacitor 框架保护规则
# =============================================

# 保持 Capacitor 核心类
-keep class com.getcapacitor.** { *; }
-keep class com.capacitorjs.** { *; }

# 保持 WebView JavaScript 接口
-keepclassmembers class * {
    @android.webkit.JavascriptInterface <methods>;
}

# 保持 WebView 相关类
-keep class android.webkit.** { *; }
-keepclassmembers class * extends android.webkit.WebViewClient {
    public void *(android.webkit.WebView, java.lang.String, android.graphics.Bitmap);
    public boolean *(android.webkit.WebView, java.lang.String);
}

# =============================================
# 自定义插件保护
# =============================================

# 保持 BLE 广播插件
-keep class com.rcapp.bleadvertiser.** { *; }

# 保持所有插件的公共方法
-keepclassmembers class * extends com.getcapacitor.Plugin {
    @com.getcapacitor.annotation.CapacitorPlugin <methods>;
    @com.getcapacitor.PluginMethod <methods>;
}

# =============================================
# Android 系统类保护
# =============================================

# 保持 Activity 和 Service
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# 保持 Parcelable 实现
-keepclassmembers class * implements android.os.Parcelable {
    public static final android.os.Parcelable$Creator CREATOR;
}

# 保持序列化类
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# =============================================
# 第三方库保护规则
# =============================================

# Gson 序列化库
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# OkHttp 网络库
-dontwarn okhttp3.**
-keep class okhttp3.** { *; }
-dontwarn okio.**
-keep class okio.** { *; }

# Google Error Prone 注解（编译时注解，运行时不需要）
-dontwarn com.google.errorprone.annotations.CanIgnoreReturnValue
-dontwarn com.google.errorprone.annotations.CheckReturnValue
-dontwarn com.google.errorprone.annotations.Immutable
-dontwarn com.google.errorprone.annotations.RestrictedApi

# Google Tink 加密库（如果使用了的话）
-dontwarn com.google.crypto.tink.**
-keep class com.google.crypto.tink.** { *; }

# =============================================
# 安全加固规则
# =============================================

# 在发布版本中移除调试日志（调试版本保留）
# 注意：这个规则只在 release 构建中生效
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int d(...);
    # 保留重要的日志级别
    # public static int i(...);
    # public static int w(...);
    # public static int e(...);
}

# 移除调试断言
-assumenosideeffects class java.lang.System {
    public static void setProperty(java.lang.String, java.lang.String);
    public static java.lang.String getProperty(java.lang.String);
}

# 字符串混淆（高级保护）
-adaptclassstrings
-adaptresourcefilenames **.properties,**.xml,**.js,**.html,**.css
-adaptresourcefilecontents **.properties,**.xml,**.js,**.html,**.css

# 类名和方法名混淆
-repackageclasses 'a'
-allowaccessmodification
-mergeinterfacesaggressively

# =============================================
# 反调试和反篡改保护
# =============================================

# 保持应用签名验证相关的类（如果有的话）
-keep class com.pleasurehub.app.security.** { *; }

# 混淆异常信息
-keepattributes !LocalVariableTable,!LocalVariableTypeTable

# =============================================
# 性能优化
# =============================================

# 启用优化
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5

# 预验证（加快加载速度）
-dontpreverify

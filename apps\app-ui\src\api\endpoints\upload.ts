import type { UploadResponse } from '../services'
import { apiClient } from '../client'

/**
 * 将文件转换为 base64 字符串
 */
function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => {
      const result = reader.result as string
      resolve(result)
    }
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsDataURL(file)
  })
}

/**
 * 上传文件到后端服务
 * 使用 JSON 格式和 base64 编码，匹配后端服务的期望格式
 */
export async function uploadFile(
  file: File,
  fileType: 'avatar' | 'character' = 'avatar'
): Promise<UploadResponse> {
  console.log(`[上传文件] 准备上传${fileType}文件:`, {
    fileName: file.name,
    fileSize: `${(file.size / 1024).toFixed(2)}KB`,
    fileType: file.type
  })

  try {
    // 将文件转换为 base64
    const base64Data = await fileToBase64(file)

    // 准备上传数据 - 匹配后端服务期望的格式
    const uploadData = {
      file: base64Data,
      fileName: file.name,
      fileType: file.type as 'image/jpeg' | 'image/jpg' | 'image/png' | 'image/webp',
      uploadType: fileType === 'avatar' ? ('avatar' as const) : ('character-image' as const)
    }

    // 根据文件类型选择不同的上传端点
    const endpoint = fileType === 'avatar' ? '/api/upload/avatar' : '/api/upload/character-image'
    console.log(`[上传文件] 使用端点:`, endpoint)

    // 使用 apiClient 发送请求
    const response = await apiClient.post<UploadResponse>(endpoint, uploadData)

    console.log(`[上传文件] 上传成功:`, response)
    return response
  } catch (error) {
    console.error(`[上传文件] 捕获到错误:`, error)
    throw error
  }
}

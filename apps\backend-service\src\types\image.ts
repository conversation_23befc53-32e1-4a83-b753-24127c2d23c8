// 图片生成队列任务类型
export interface ImageGenerationTask {
  taskId: string;
  messageId: string;
  chatId: string;
  prompt: string;
  characterAvatar?: string;
  userId: string;
  timestamp: number;
  metadata?: {
    width?: number;
    height?: number;
    steps?: number;
    cfg?: number;
    negativePrompt?: string;
    mediaGenerationId?: string; // 媒体生成记录ID
    pointsUsed?: number; // 消费的积分
  };
}

// 图片生成结果类型
export interface ImageGenerationResult {
  taskId: string;
  success: boolean;
  imageUrl?: string;
  error?: string;
  processingTime: number;
  metadata?: {
    insa3dTaskId?: string;
    finalImageUrl?: string;
  };
}

// 图片生成状态类型
export type ImageGenerationStatus = 'pending' | 'starting' | 'processing' | 'completed' | 'failed';

// 图片生成进度更新类型
export interface ImageGenerationProgress {
  messageId: string;
  status: ImageGenerationStatus;
  progress: number;
  message: string;
  timestamp: string;
}

// Insa3D API 相关类型
export interface Insa3DTaskRequest {
  inputs: {
    [key: string]: {
      title: string;
      value: string | number;
    };
  };
}

export interface Insa3DTaskResponse {
  task_id: string;
  status: string;
  error_message?: string;
}

export interface Insa3DTaskStatus {
  task_id: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'EXECUTING' | 'COMPLETED' | 'FAILED';
  image_urls?: string[];
  error_message?: string;
}

// 图片生成请求类型
export interface ImageGenerationRequest {
  messageId: string;
  chatId: string;
  prompt: string;
  characterAvatar?: string;
  metadata?: {
    width?: number;
    height?: number;
    steps?: number;
    cfg?: number;
    negativePrompt?: string;
  };
}

// 图片生成响应类型
export interface ImageGenerationResponse {
  success: boolean;
  taskId: string;
  message?: string;
  error?: string;
}

// 图片附件类型
export interface ImageAttachment {
  url: string;
  name: string;
  contentType: string;
  metadata?: {
    status?: ImageGenerationStatus;
    progress?: number;
    timestamp?: string;
    taskId?: string;
  };
}

// 生成状态附件类型（用于进度追踪）
export interface GeneratingStatusAttachment extends ImageAttachment {
  contentType: 'image/generating';
  metadata: {
    status: ImageGenerationStatus;
    progress: number;
    timestamp: string;
    taskId: string;
  };
}

// 完成的图片附件类型
export interface CompletedImageAttachment extends ImageAttachment {
  contentType: 'image/png' | 'image/jpeg' | 'image/webp';
  metadata?: {
    taskId?: string;
    generatedAt?: string;
    originalPrompt?: string;
  };
}

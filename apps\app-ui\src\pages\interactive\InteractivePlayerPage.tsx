import { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router'
import { PlayerProvider } from './context/PlayerContext'
import { InteractivePlayer } from './components/InteractivePlayer'
import type { Script, Stage, Dialogue, Command } from './types'
import { But<PERSON> } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useDeviceSafeArea } from '@/hooks/use-mobile-viewport'
import { useDeviceStore } from '../../stores/device-store'
import { useDeviceLifecycle } from '../../hooks/use-device-lifecycle'
import { useScriptDownloadStore } from '@/stores/script-download-store'
import { resourceManager } from '@/services/resource-manager'

interface PlayerPageState {
  scriptId: string
}

interface LoadedPlayerData {
  script: Script
  commands: Command[]
  audioSrc: string
  scriptId: string
}

// 新剧本数据结构
interface NewScriptData {
  commands: Array<{
    command: string
    time: string
  }>
  stages: Array<{
    stage: number
    stageTitle: string
    pics: Array<{
      name: string
      pic: string
    }>
    intensity: Record<
      // 时间点
      string,
      {
        // 抽插
        thrust?: number
        // 吮吸
        suction?: number
        // 震动
        vibrate?: number
      }
    >
    stageTime?: string // 新增的 stageTime 字段
  }>
}

/**
 * 将新的剧本数据格式转换为播放器期望的格式
 */
function convertNewScriptToOldFormat(newScriptData: NewScriptData): Script {
  // 如果没有stages，返回空数组
  if (!newScriptData.stages || newScriptData.stages.length === 0) {
    return []
  }

  return newScriptData.stages.map(stage => {
    // 优先用 stageTime 作为对白锚点，没有就用 intensity 的第一个时间点
    const intensityTimes = Object.keys(stage.intensity).sort((a, b) => {
      return timeToSeconds(a) - timeToSeconds(b)
    })
    const hasStageTime = typeof stage.stageTime === 'string' && stage.stageTime.length > 0
    const firstTime = hasStageTime ? stage.stageTime! : intensityTimes[0] || '00:00:00'
    const safeFirstTime = firstTime || '00:00:00'

    // 生成对白数组，先加锚点对白
    const dialogues: Dialogue[] = [
      {
        role: 'system',
        time: safeFirstTime,
        dialogue: '', // 没有对白内容
        intensity: stage.intensity[safeFirstTime] || {}
      }
    ]

    // 其余 intensity 时间点（排除 safeFirstTime），也生成空对白
    intensityTimes
      .filter(timeKey => timeKey !== safeFirstTime)
      .forEach(timeKey => {
        dialogues.push({
          role: 'system',
          time: timeKey,
          dialogue: '',
          intensity: stage.intensity[timeKey] || {}
        })
      })

    // 如果有commands，将匹配时间范围的命令也添加进来（可选，保留原逻辑）
    if (newScriptData.commands && newScriptData.commands.length > 0) {
      // 计算当前阶段的时间范围
      const stageStartSeconds = timeToSeconds(safeFirstTime)
      const nextStageIndex = stage.stage // stage是从1开始的
      const nextStage = newScriptData.stages.find(s => s.stage === nextStageIndex + 1)
      let nextStageStartTime: string | undefined = undefined
      if (nextStage) {
        if (typeof nextStage.stageTime === 'string' && nextStage.stageTime.length > 0) {
          nextStageStartTime = nextStage.stageTime
        } else {
          const nextIntensityTimes = Object.keys(nextStage.intensity).sort(
            (a, b) => timeToSeconds(a) - timeToSeconds(b)
          )
          nextStageStartTime = nextIntensityTimes[0] || '00:00:00'
        }
      }
      const stageEndSeconds = nextStageStartTime
        ? timeToSeconds(nextStageStartTime)
        : Number.MAX_SAFE_INTEGER

      // 找到属于当前阶段时间范围内的命令
      const stageCommands = newScriptData.commands.filter(cmd => {
        const cmdSeconds = timeToSeconds(cmd.time)
        return cmdSeconds >= stageStartSeconds && cmdSeconds < stageEndSeconds
      })

      // 为每个命令创建对白（如果该时间点没有对白）
      stageCommands.forEach(cmd => {
        const existingDialogue = dialogues.find(d => d.time === cmd.time)
        if (!existingDialogue) {
          dialogues.push({
            role: 'system',
            time: cmd.time,
            dialogue: cmd.command,
            intensity: findIntensityByTime(stage.intensity, cmd.time)
          })
        } else {
          // 如果已存在，更新对白内容
          existingDialogue.dialogue = cmd.command
        }
      })
    }

    // 按时间排序对白
    dialogues.sort((a, b) => timeToSeconds(a.time) - timeToSeconds(b.time))

    const convertedStage: Stage = {
      stage: stage.stage,
      stageTitle: stage.stageTitle,
      pics:
        stage.pics?.map(pic => ({
          name: pic.name,
          pic: pic.pic,
          type: 'image' as const
        })) || [],
      dialogues
    }

    return convertedStage
  })
}

/**
 * 根据时间查找对应的强度配置
 */
function findIntensityByTime(
  intensityConfig: Record<string, any>,
  targetTime: string
): Record<string, number> {
  const targetSeconds = timeToSeconds(targetTime)
  let closestTime = ''
  let closestDiff = Infinity

  // 找到最接近的时间点
  Object.keys(intensityConfig).forEach(timeKey => {
    const keySeconds = timeToSeconds(timeKey)
    const diff = Math.abs(targetSeconds - keySeconds)
    if (diff < closestDiff) {
      closestDiff = diff
      closestTime = timeKey
    }
  })

  return closestTime ? intensityConfig[closestTime] || {} : {}
}

/**
 * 将时间字符串转换为秒数
 */
function timeToSeconds(timeStr: string): number {
  const parts = timeStr.split(':')
  if (parts.length === 3) {
    const hours = parseInt(parts[0], 10)
    const minutes = parseInt(parts[1], 10)
    const seconds = parseInt(parts[2], 10)
    return hours * 3600 + minutes * 60 + seconds
  }
  return 0
}

export default function InteractivePlayerPage() {
  const location = useLocation()
  const navigate = useNavigate()
  const [playerData, setPlayerData] = useState<LoadedPlayerData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const safeArea = useDeviceSafeArea()

  // 使用全局设备状态
  const { connectedDevice } = useDeviceStore()

  // 使用设备生命周期管理
  useDeviceLifecycle('script')

  // 使用剧本下载store
  const { getDownloadedScript } = useScriptDownloadStore()

  // 组件卸载时清理资源
  useEffect(() => {
    return () => {
      // 清理blob URL缓存
      resourceManager.clearBlobUrlCache()
    }
  }, [])

  useEffect(() => {
    const loadScriptData = async () => {
      try {
        // 从路由状态中获取数据
        const state = location.state as PlayerPageState

        if (!state || !state.scriptId) {
          // 如果没有必要的数据，返回到剧本选择页面
          navigate('/interactive', { replace: true })
          return
        }

        setLoading(true)
        setError(null)

        // 从本地存储获取剧本内容
        const scriptContentData = await getDownloadedScript(state.scriptId)

        if (!scriptContentData) {
          throw new Error('剧本内容未找到，请先下载剧本')
        }

        // 检查数据格式并转换
        let convertedScript: Script = []
        let extractedCommands: Command[] = []

        if (scriptContentData.content) {
          // 判断是新格式还是旧格式
          if (isNewScriptFormat(scriptContentData.content)) {
            const newScriptData = scriptContentData.content as NewScriptData
            // 新格式：转换数据
            convertedScript = convertNewScriptToOldFormat(newScriptData)
            // 提取commands
            extractedCommands = newScriptData.commands || []
          } else if (Array.isArray(scriptContentData.content)) {
            // 旧格式：直接使用
            convertedScript = scriptContentData.content as Script
            // 旧格式没有commands
            extractedCommands = []
          } else {
            throw new Error('不支持的剧本数据格式')
          }
        }

        // 🔄 预处理资源，将网络URL替换为本地blob URL
        console.log('🔄 开始预处理剧本资源...')
        const { processedContent, processedAudioUrl } = await resourceManager.preprocessScriptData(
          convertedScript,
          scriptContentData.audioUrl
        )

        const loadedData: LoadedPlayerData = {
          script: processedContent,
          commands: extractedCommands,
          audioSrc: processedAudioUrl,
          scriptId: state.scriptId
        }

        setPlayerData(loadedData)
      } catch (err) {
        console.error('加载剧本数据失败:', err)
        setError(err instanceof Error ? err.message : '加载剧本数据失败')
      } finally {
        setLoading(false)
      }
    }

    loadScriptData()
  }, [location.state, navigate, getDownloadedScript])

  /**
   * 判断是否为新的剧本数据格式
   */
  function isNewScriptFormat(data: any): boolean {
    return (
      data &&
      typeof data === 'object' &&
      'stages' in data &&
      Array.isArray(data.stages) &&
      // commands 字段可能不存在，或者是空数组
      (!('commands' in data) || Array.isArray(data.commands))
    )
  }

  const handleBackToScriptSelect = () => {
    navigate(-1)
  }

  // 如果正在加载，显示加载状态
  if (loading) {
    return (
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="size-full min-h-screen bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto mb-4" />
            <p className="text-white">正在加载剧本数据...</p>
          </div>
        </div>
      </div>
    )
  }

  // 如果加载出错，显示错误状态
  if (error) {
    return (
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="size-full min-h-screen bg-gray-900 flex items-center justify-center">
          <div className="text-center max-w-md p-4 bg-red-900/20 rounded-lg border border-red-800/50">
            <svg
              className="w-10 h-10 text-red-500 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <h3 className="text-red-200 text-lg font-bold mb-2">加载失败</h3>
            <p className="text-red-300 mb-4">{error}</p>
            <button
              type="button"
              onClick={handleBackToScriptSelect}
              className="px-4 py-2 bg-red-700 hover:bg-red-600 text-white rounded"
            >
              返回选择剧本
            </button>
          </div>
        </div>
      </div>
    )
  }

  // 如果数据还没有加载完成，显示加载状态
  if (!playerData) {
    return (
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="size-full min-h-screen bg-gray-900 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto mb-4" />
            <p className="text-white">正在初始化播放器...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div
      className="flex-1 flex flex-col overflow-hidden h-full"
      style={{ minHeight: `calc(100vh - ${safeArea.top}px - 4rem)` }}
    >
      <div className="size-full bg-gray-900 relative">
        {/* 返回按钮 */}
        <div className="absolute top-20 left-4 z-50">
          <Button
            isIconOnly
            variant="flat"
            className="bg-black/50 text-white"
            onPress={handleBackToScriptSelect}
            aria-label="返回剧本选择"
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </Button>
        </div>

        {/* 播放器内容 */}
        <PlayerProvider
          key={`player-${playerData.scriptId}-${Date.now()}`}
          initialScript={playerData.script}
          initialCommands={playerData.commands}
          initialDevice={connectedDevice}
          audioSrc={playerData.audioSrc}
        >
          <InteractivePlayer onBackToScriptSelect={handleBackToScriptSelect} />
        </PlayerProvider>
      </div>
    </div>
  )
}

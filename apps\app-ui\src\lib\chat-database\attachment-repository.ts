import type { MessageAttachment } from './types'
import { ChatDatabaseError } from './types'

/**
 * 附件仓储类
 * 负责附件相关的数据库操作
 */
export class AttachmentRepository {
  private db: any

  constructor(database: any) {
    this.db = database
  }

  /**
   * 创建附件
   */
  async createAttachment(
    attachment: Omit<MessageAttachment, 'createdAt' | 'updatedAt'>
  ): Promise<MessageAttachment> {
    const now = new Date().toISOString()
    const newAttachment: MessageAttachment = {
      ...attachment,
      createdAt: now,
      updatedAt: now
    }

    try {
      await this.db.run(
        `INSERT INTO message_attachments (id, messageId, type, name, contentType, originalUrl, localPath, status, fileSize, metadata, createdAt, updatedAt)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          newAttachment.id,
          newAttachment.messageId,
          newAttachment.type,
          newAttachment.name,
          newAttachment.contentType,
          newAttachment.originalUrl,
          newAttachment.localPath || null,
          newAttachment.status,
          newAttachment.fileSize || null,
          newAttachment.metadata,
          newAttachment.createdAt,
          newAttachment.updatedAt
        ]
      )

      console.log(`✅ [AttachmentRepo] 附件已创建: ${newAttachment.id}`)
      return newAttachment
    } catch (error) {
      throw new ChatDatabaseError(
        `创建附件失败: ${newAttachment.id}`,
        'CREATE_ATTACHMENT_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取附件
   */
  async getAttachment(id: string): Promise<MessageAttachment | null> {
    try {
      const result = await this.db.query('SELECT * FROM message_attachments WHERE id = ?', [id])
      return result.values?.length > 0 ? result.values[0] : null
    } catch (error) {
      throw new ChatDatabaseError(`获取附件失败: ${id}`, 'GET_ATTACHMENT_FAILED', error as Error)
    }
  }

  /**
   * 根据消息ID获取附件
   */
  async getAttachmentsByMessage(messageId: string): Promise<MessageAttachment[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM message_attachments WHERE messageId = ? ORDER BY createdAt ASC',
        [messageId]
      )
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        `获取消息附件失败: ${messageId}`,
        'GET_ATTACHMENTS_BY_MESSAGE_FAILED',
        error as Error
      )
    }
  }

  /**
   * 根据聊天ID获取所有附件
   */
  async getAttachmentsByChat(chatId: string): Promise<MessageAttachment[]> {
    try {
      const result = await this.db.query(
        `SELECT ma.* FROM message_attachments ma
         INNER JOIN chat_messages cm ON ma.messageId = cm.id
         WHERE cm.chatId = ?
         ORDER BY ma.createdAt ASC`,
        [chatId]
      )
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        `获取聊天附件失败: ${chatId}`,
        'GET_ATTACHMENTS_BY_CHAT_FAILED',
        error as Error
      )
    }
  }

  /**
   * 根据类型获取附件
   */
  async getAttachmentsByType(
    type: 'audio' | 'image' | 'video',
    limit: number = 50
  ): Promise<MessageAttachment[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM message_attachments WHERE type = ? ORDER BY createdAt DESC LIMIT ?',
        [type, limit]
      )
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        `获取类型附件失败: ${type}`,
        'GET_ATTACHMENTS_BY_TYPE_FAILED',
        error as Error
      )
    }
  }

  /**
   * 根据状态获取附件
   */
  async getAttachmentsByStatus(
    status: 'generating' | 'downloading' | 'completed' | 'failed'
  ): Promise<MessageAttachment[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM message_attachments WHERE status = ? ORDER BY createdAt ASC',
        [status]
      )
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        `获取状态附件失败: ${status}`,
        'GET_ATTACHMENTS_BY_STATUS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 更新附件
   */
  async updateAttachment(id: string, updates: Partial<MessageAttachment>): Promise<boolean> {
    try {
      const setClause = Object.keys(updates)
        .filter(key => key !== 'id')
        .map(key => `${key} = ?`)
        .join(', ')

      if (!setClause) return false

      const values = Object.entries(updates)
        .filter(([key]) => key !== 'id')
        .map(([_, value]) => value)

      values.push(new Date().toISOString()) // updatedAt
      values.push(id) // WHERE 条件

      const result = await this.db.run(
        `UPDATE message_attachments SET ${setClause}, updatedAt = ? WHERE id = ?`,
        values
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [AttachmentRepo] 附件已更新: ${id}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(`更新附件失败: ${id}`, 'UPDATE_ATTACHMENT_FAILED', error as Error)
    }
  }

  /**
   * 更新附件状态
   */
  async updateAttachmentStatus(
    id: string,
    status: 'generating' | 'downloading' | 'completed' | 'failed'
  ): Promise<boolean> {
    try {
      const result = await this.db.run(
        'UPDATE message_attachments SET status = ?, updatedAt = ? WHERE id = ?',
        [status, new Date().toISOString(), id]
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [AttachmentRepo] 附件状态已更新: ${id} -> ${status}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `更新附件状态失败: ${id}`,
        'UPDATE_ATTACHMENT_STATUS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 更新附件本地路径
   */
  async updateAttachmentLocalPath(id: string, localPath: string): Promise<boolean> {
    try {
      const result = await this.db.run(
        'UPDATE message_attachments SET localPath = ?, status = ?, updatedAt = ? WHERE id = ?',
        [localPath, 'completed', new Date().toISOString(), id]
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [AttachmentRepo] 附件本地路径已更新: ${id}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `更新附件本地路径失败: ${id}`,
        'UPDATE_ATTACHMENT_LOCAL_PATH_FAILED',
        error as Error
      )
    }
  }

  /**
   * 删除附件
   */
  async deleteAttachment(id: string): Promise<boolean> {
    try {
      const result = await this.db.run('DELETE FROM message_attachments WHERE id = ?', [id])
      const success = result.changes?.changes > 0
      if (success) {
        console.log(`🗑️ [AttachmentRepo] 附件已删除: ${id}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(`删除附件失败: ${id}`, 'DELETE_ATTACHMENT_FAILED', error as Error)
    }
  }

  /**
   * 删除消息的所有附件
   */
  async deleteAttachmentsByMessage(messageId: string): Promise<number> {
    try {
      const result = await this.db.run('DELETE FROM message_attachments WHERE messageId = ?', [
        messageId
      ])
      const deletedCount = result.changes?.changes || 0
      if (deletedCount > 0) {
        console.log(
          `🗑️ [AttachmentRepo] 已删除消息的所有附件: ${messageId}, 删除数量: ${deletedCount}`
        )
      }
      return deletedCount
    } catch (error) {
      throw new ChatDatabaseError(
        `删除消息附件失败: ${messageId}`,
        'DELETE_ATTACHMENTS_BY_MESSAGE_FAILED',
        error as Error
      )
    }
  }

  /**
   * 删除聊天的所有附件
   */
  async deleteAttachmentsByChat(chatId: string): Promise<number> {
    try {
      const result = await this.db.run(
        `DELETE FROM message_attachments 
         WHERE messageId IN (
           SELECT id FROM chat_messages WHERE chatId = ?
         )`,
        [chatId]
      )
      const deletedCount = result.changes?.changes || 0
      if (deletedCount > 0) {
        console.log(
          `🗑️ [AttachmentRepo] 已删除聊天的所有附件: ${chatId}, 删除数量: ${deletedCount}`
        )
      }
      return deletedCount
    } catch (error) {
      throw new ChatDatabaseError(
        `删除聊天附件失败: ${chatId}`,
        'DELETE_ATTACHMENTS_BY_CHAT_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取附件统计信息
   */
  async getAttachmentStats(): Promise<{
    totalAttachments: number
    totalSize: number
    byType: Record<string, number>
    byStatus: Record<string, number>
  }> {
    try {
      // 获取总数和总大小
      const totalResult = await this.db.query(
        'SELECT COUNT(*) as count, SUM(COALESCE(fileSize, 0)) as totalSize FROM message_attachments'
      )
      const totalAttachments = totalResult.values?.[0]?.count || 0
      const totalSize = totalResult.values?.[0]?.totalSize || 0

      // 按类型统计
      const typeResult = await this.db.query(
        'SELECT type, COUNT(*) as count FROM message_attachments GROUP BY type'
      )
      const byType: Record<string, number> = {}
      typeResult.values?.forEach((row: any) => {
        byType[row.type] = row.count
      })

      // 按状态统计
      const statusResult = await this.db.query(
        'SELECT status, COUNT(*) as count FROM message_attachments GROUP BY status'
      )
      const byStatus: Record<string, number> = {}
      statusResult.values?.forEach((row: any) => {
        byStatus[row.status] = row.count
      })

      return {
        totalAttachments,
        totalSize,
        byType,
        byStatus
      }
    } catch (error) {
      throw new ChatDatabaseError('获取附件统计失败', 'GET_ATTACHMENT_STATS_FAILED', error as Error)
    }
  }

  /**
   * 清理失败的附件
   */
  async cleanupFailedAttachments(): Promise<number> {
    try {
      const result = await this.db.run("DELETE FROM message_attachments WHERE status = 'failed'")
      const deletedCount = result.changes?.changes || 0
      if (deletedCount > 0) {
        console.log(`🧹 [AttachmentRepo] 已清理失败附件: ${deletedCount}个`)
      }
      return deletedCount
    } catch (error) {
      throw new ChatDatabaseError(
        '清理失败附件失败',
        'CLEANUP_FAILED_ATTACHMENTS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取需要下载的附件
   */
  async getPendingDownloads(): Promise<MessageAttachment[]> {
    try {
      const result = await this.db.query(
        "SELECT * FROM message_attachments WHERE status IN ('generating', 'downloading') ORDER BY createdAt ASC"
      )
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        '获取待下载附件失败',
        'GET_PENDING_DOWNLOADS_FAILED',
        error as Error
      )
    }
  }
}

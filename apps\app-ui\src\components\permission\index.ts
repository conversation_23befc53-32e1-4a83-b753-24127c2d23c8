import React from 'react'
import type { FeatureType } from '@/hooks/use-permission-guard'

// 权限系统统一导出
export { PermissionGuard } from './PermissionGuard'
export { PermissionModal } from './PermissionModal'
export { showPermissionToast } from './PermissionToast'

// Hooks
export { usePermissionGuard, useFeatureAccess } from '@/hooks/use-permission-guard'
export type { PermissionResult, FeatureType, PermissionGuardConfig } from '@/hooks/use-permission-guard'

// 便捷的高阶组件
export function withPermission<T extends object>(
  Component: React.ComponentType<T>, 
  feature: FeatureType,
  options?: {
    uiStrategy?: 'modal' | 'toast' | 'inline' | 'silent'
    featureName?: string
    fallback?: React.ReactNode
  }
) {
  return function WrappedComponent(props: T) {
    return React.createElement(
      require('./PermissionGuard').PermissionGuard,
      {
        feature,
        uiStrategy: options?.uiStrategy,
        featureName: options?.featureName,
        fallback: options?.fallback
      },
      React.createElement(Component, props)
    )
  }
}
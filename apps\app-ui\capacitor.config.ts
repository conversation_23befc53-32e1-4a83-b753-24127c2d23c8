import type { CapacitorConfig } from '@capacitor/cli'

// 根据环境变量确定应用版本配置
const APP_VERSION = (globalThis as any).process?.env?.APP_VERSION || 'domestic' // 默认国内版

const getAppConfig = (version: string) => {
  switch (version) {
    case 'international':
      return {
        appId: 'com.pleasurehubv2.app',
        appName: 'PleasureHub'
      }
    case 'domestic':
    default:
      return {
        appId: 'com.pleasurehub.app',
        appName: '灵犀秘境'
      }
  }
}

const appConfig = getAppConfig(APP_VERSION)

const config: CapacitorConfig = {
  appId: appConfig.appId,
  appName: appConfig.appName,
  webDir: 'dist',

  // 允许导航到外部域名，包括你的 R2 域名
  server: {
    // 生产环境下注释掉 url，使用打包后的本地文件
    // url: 'http://*************:5173',
    // hostname: 'localhost',
    // iosScheme: 'https',
    // androidScheme: 'https',
    allowNavigation: ['*.r2.cloudflarestorage.com', '*.r2.dev', '*.pleasurehub.app'],
    // 允许混合内容和媒体访问
    cleartext: true
  },

  plugins: {
    // 禁用 CapacitorHttp 以支持流式响应
    // 这样可以使用标准的 web fetch API 而不是 Capacitor 的原生 HTTP 插件
    CapacitorHttp: {
      enabled: false
    },
    BleAdvertiser: {
      enabled: true
    },
    // 条形码扫描器插件配置
    BarcodeScanner: {
      enabled: true
    },
    StatusBar: {
      overlaysWebView: false,
      style: 'DARK',
      backgroundColor: '#0B0A1E'
    },
    // 音频会话管理插件配置
    AudioSession: {
      enabled: true
    },
    // 媒体会话插件配置
    MediaSession: {
      enabled: true
    },
    // 隐私屏幕插件配置
    PrivacyScreen: {
      enabled: true
    },
    // SQLite 数据库插件配置
    CapacitorSQLite: {
      iosDatabaseLocation: 'Library/CapacitorDatabase',
      iosIsEncryption: false,
      iosKeychainPrefix: 'pleasurehub',
      iosBiometric: {
        biometricAuth: false,
        biometricTitle: 'Biometric login for capacitor sqlite'
      },
      androidIsEncryption: false,
      androidBiometric: {
        biometricAuth: false,
        biometricTitle: 'Biometric login for capacitor sqlite',
        biometricSubTitle: 'Log in using your biometric'
      },
      electronIsEncryption: false,
      electronWindowsLocation: 'C:\\ProgramData\\CapacitorDatabases',
      electronMacLocation: '/Volumes/Development_Lacie/Development/Databases',
      electronLinuxLocation: 'Databases'
    },
    // @capacitor-community/media 插件配置
    Media: {
      androidGalleryMode: true // 设为 true 允许访问系统相册，确保有可用相册
    }
  },
  android: {
    buildOptions: {
      keystorePath: 'app/key.jks',
      keystorePassword: 'rc.demo',
      keystoreAlias: 'rckey',
      keystoreAliasPassword: 'rc.demo',
      releaseType: 'APK'
    },
    // 允许混合内容
    allowMixedContent: true
  },
  ios: {
    // iOS 特定配置
    allowsLinkPreview: false
  }
}

export default config

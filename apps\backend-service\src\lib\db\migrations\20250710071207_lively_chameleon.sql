CREATE TABLE IF NOT EXISTS "AppUpdateLog" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid,
	"device_id" varchar(100),
	"current_version" varchar(50),
	"target_version" varchar(50),
	"update_type" varchar NOT NULL,
	"update_status" varchar NOT NULL,
	"error_message" text,
	"metadata" json DEFAULT '{}',
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AppUpdatePolicy" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"version_id" uuid NOT NULL,
	"channel" varchar(50) DEFAULT 'production' NOT NULL,
	"update_strategy" varchar NOT NULL,
	"target_version_min" varchar(50),
	"target_version_max" varchar(50),
	"rollout_percentage" integer DEFAULT 100 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AppVersion" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"version_name" varchar(50) NOT NULL,
	"version_code" integer NOT NULL,
	"version_type" varchar NOT NULL,
	"file_url" text NOT NULL,
	"file_size" bigint,
	"file_hash" varchar(64),
	"min_compatible_version" varchar(50),
	"release_notes" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "archive_metadata" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"total_chats" integer DEFAULT 0 NOT NULL,
	"total_messages" integer DEFAULT 0 NOT NULL,
	"archive_date" timestamp NOT NULL,
	"compression_ratio" numeric(5, 4) DEFAULT '1.0000',
	"file_size_bytes" bigint DEFAULT 0 NOT NULL,
	"r2_key" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AppUpdateLog" ADD CONSTRAINT "AppUpdateLog_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AppUpdatePolicy" ADD CONSTRAINT "AppUpdatePolicy_version_id_AppVersion_id_fk" FOREIGN KEY ("version_id") REFERENCES "public"."AppVersion"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "archive_metadata" ADD CONSTRAINT "archive_metadata_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_log_user_id" ON "AppUpdateLog" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_log_device_id" ON "AppUpdateLog" USING btree ("device_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_log_update_type" ON "AppUpdateLog" USING btree ("update_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_log_status" ON "AppUpdateLog" USING btree ("update_status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_log_created_at" ON "AppUpdateLog" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_policy_version_id" ON "AppUpdatePolicy" USING btree ("version_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_policy_channel" ON "AppUpdatePolicy" USING btree ("channel");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_policy_strategy" ON "AppUpdatePolicy" USING btree ("update_strategy");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_update_policy_active" ON "AppUpdatePolicy" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_version_code" ON "AppVersion" USING btree ("version_code");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_version_type" ON "AppVersion" USING btree ("version_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_version_active" ON "AppVersion" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_app_version_created_at" ON "AppVersion" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_archive_metadata_user_id" ON "archive_metadata" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_archive_metadata_archive_date" ON "archive_metadata" USING btree ("archive_date" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_archive_metadata_user_archive_date" ON "archive_metadata" USING btree ("user_id","archive_date" DESC NULLS LAST);
{"name": "pleasurehub-app-ui", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "dev:international": "APP_VERSION=international vite --host 0.0.0.0", "build": "tsc -b && vite build", "build:domestic": "APP_VERSION=domestic tsc -b && APP_VERSION=domestic vite build", "build:international": "APP_VERSION=international tsc -b && APP_VERSION=international vite build", "lint": "eslint .", "preview": "vite preview", "sync": "pnpm run build && npx cap sync", "sync:domestic": "pnpm run build:domestic && APP_VERSION=domestic npx cap sync", "sync:international": "pnpm run build:international && APP_VERSION=international npx cap sync", "cap": "pnpm run build && npx cap sync && npx cap open ios", "cap:android": "pnpm run build && npx cap sync && npx cap open android", "cap:domestic": "pnpm run sync:domestic && npx cap open ios", "cap:international": "pnpm run sync:international && npx cap open ios", "cap:android:domestic": "pnpm run sync:domestic && npx cap open android", "cap:android:international": "pnpm run sync:international && npx cap open android", "build:android": "pnpm run build && npx cap sync && npx cap build android", "build:android:domestic": "pnpm run sync:domestic && npx cap build android", "build:android:international": "pnpm run sync:international && npx cap build android", "build:ios": "pnpm run build && npx cap sync && npx cap build ios", "build:ios:domestic": "pnpm run sync:domestic && npx cap build ios", "build:ios:international": "pnpm run sync:international && npx cap build ios", "build:web": "pnpm run build", "build:web:domestic": "pnpm run build:domestic", "build:web:international": "pnpm run build:international", "build:all": "pnpm run build:web && pnpm run build:android && pnpm run build:ios", "build:all:domestic": "pnpm run build:web:domestic && pnpm run build:android:domestic && pnpm run build:ios:domestic", "build:all:international": "pnpm run build:web:international && pnpm run build:android:international && pnpm run build:ios:international"}, "dependencies": {"@ai-sdk/react": "^1.2.12", "@capacitor-community/bluetooth-le": "^7.0.0", "@capacitor-community/media": "^8.0.1", "@capacitor-community/sqlite": "^7.0.1", "@capacitor/android": "^7.2.0", "@capacitor/app": "^7.0.1", "@capacitor/barcode-scanner": "^2.0.3", "@capacitor/cli": "^7.2.0", "@capacitor/core": "^7.2.0", "@capacitor/device": "^7.0.1", "@capacitor/filesystem": "^7.1.2", "@capacitor/ios": "^7.2.0", "@capacitor/keyboard": "^7.0.1", "@capacitor/network": "^7.0.1", "@capacitor/preferences": "^7.0.1", "@capacitor/privacy-screen": "^1.1.0", "@capacitor/status-bar": "^7.0.1", "@capawesome/capacitor-live-update": "^7.2.0", "@heroui/react": "2.8.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@radix-ui/react-visually-hidden": "^1.2.2", "@supabase/supabase-js": "^2.49.9", "@tailwindcss/vite": "^4.1.8", "@types/canvas-confetti": "^1.9.0", "ai": "^4.3.15", "canvas-confetti": "^1.9.3", "capacitor-ble-advertiser": "workspace:^", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dexie": "^4.0.11", "embla-carousel-fade": "^8.6.0", "embla-carousel-react": "^8.6.0", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.11.3", "i18next": "^25.2.0", "i18next-browser-languagedetector": "^8.1.0", "keepalive-for-react-router": "^2.0.2", "lucide-react": "^0.523.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.3", "react-i18next": "^15.5.1", "react-markdown": "^10.1.0", "react-player": "^3.1.0", "react-router": "^7.6.2", "remark-gfm": "^4.0.1", "sonner": "^2.0.3", "swr": "^2.3.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.10", "tw-animate-css": "^1.3.4", "usehooks-ts": "^3.1.1", "zod": "^3.24.4", "zustand": "^5.0.6"}, "devDependencies": {"@csstools/postcss-cascade-layers": "^5.0.2", "@eslint/js": "^9.25.0", "@iconify/react": "^6.0.0", "@parcel/css": "^1.14.0", "@types/node": "^22.15.17", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@types/react-router": "^5.1.20", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "eslint-plugin-tailwindcss": "^3.18.0", "globals": "^16.0.0", "lightningcss": "^1.30.1", "postcss": "^8.5.6", "postcss-calc": "^10.1.1", "postcss-color-mix": "^1.1.0", "postcss-custom-properties": "^14.0.6", "postcss-import": "^16.1.1", "postcss-nested": "^7.0.2", "postcss-preset-env": "^10.2.4", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4"}}
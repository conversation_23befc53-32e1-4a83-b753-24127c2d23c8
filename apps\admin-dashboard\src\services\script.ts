import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'
import type {
  Script,
  ScriptListParams,
  ScriptCreateParams,
  ScriptUpdateParams,
  ScriptStats,
  ScriptContent
} from '@/types/script'

// 剧本管理服务
export class ScriptService {
  // ==================== 剧本CRUD ====================

  // 获取剧本列表
  async getScripts(params?: ScriptListParams): Promise<ApiResponse<PaginatedResponse<Script>>> {
    return await apiService.get<PaginatedResponse<Script>>('/admin/content/scripts', { params })
  }

  // 获取剧本详情
  async getScript(id: string): Promise<ApiResponse<Script>> {
    return await apiService.get<Script>(`/admin/content/scripts/${id}`)
  }

  // 创建剧本
  async createScript(params: ScriptCreateParams): Promise<ApiResponse<Script>> {
    return await apiService.post<Script>('/admin/content/scripts', params)
  }

  // 更新剧本
  async updateScript(params: ScriptUpdateParams): Promise<ApiResponse<Script>> {
    const { id, ...updateData } = params
    return await apiService.put<Script>(`/admin/content/scripts/${id}`, updateData)
  }

  // 删除剧本
  async deleteScript(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/content/scripts/${id}`)
  }

  // ==================== 剧本状态管理 ====================

  // 启用/禁用剧本
  async toggleScriptStatus(id: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/scripts/${id}/toggle-status`, { isActive })
  }

  // 设置剧本公开状态
  async toggleScriptPublic(id: string, isPublic: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/scripts/${id}/toggle-public`, { isPublic })
  }

  // 设置会员专享
  async toggleScriptPremium(id: string, isPremium: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/scripts/${id}/toggle-premium`, { isPremium })
  }

  // ==================== 剧本内容管理 ====================

  // 获取剧本内容
  async getScriptContent(id: string): Promise<ApiResponse<ScriptContent>> {
    return await apiService.get<ScriptContent>(`/admin/content/scripts/${id}/content`)
  }

  // 更新剧本内容
  async updateScriptContent(id: string, content: ScriptContent): Promise<ApiResponse<void>> {
    return await apiService.put<void>(`/admin/content/scripts/${id}/content`, content)
  }

  // 上传剧本封面图片
  async uploadCoverImage(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)
    return await apiService.post<{ url: string }>('/admin/content/scripts/upload-cover', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // 上传剧本音频文件
  async uploadAudioFile(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)
    return await apiService.post<{ url: string }>('/admin/content/scripts/upload-audio', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // 上传阶段图片
  async uploadStageImage(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)
    return await apiService.post<{ url: string }>('/admin/content/scripts/upload-stage-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // ==================== 统计和分析 ====================

  // 获取剧本统计数据
  async getStats(): Promise<ApiResponse<ScriptStats>> {
    return await apiService.get<ScriptStats>('/admin/content/scripts/stats/summary')
  }

  // 获取剧本分类列表
  async getCategories(): Promise<ApiResponse<string[]>> {
    return await apiService.get<string[]>('/admin/content/scripts/categories')
  }

  // 获取剧本使用记录
  async getUsageRecords(scriptId: string, params?: {
    page?: number
    pageSize?: number
  }): Promise<ApiResponse<PaginatedResponse<any>>> {
    return await apiService.get<PaginatedResponse<any>>(`/admin/content/scripts/${scriptId}/usage`, { params })
  }

  // ==================== 批量操作 ====================

  // 批量删除剧本
  async batchDeleteScripts(ids: string[]): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/scripts/batch-delete', { ids })
  }

  // 批量设置状态
  async batchToggleStatus(ids: string[], isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/scripts/batch-toggle-status', { ids, isActive })
  }

  // 批量设置公开状态
  async batchTogglePublic(ids: string[], isPublic: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/scripts/batch-toggle-public', { ids, isPublic })
  }

  // ==================== 导入导出 ====================

  // 导出剧本数据
  async exportScripts(params?: {
    format?: 'json' | 'csv'
    ids?: string[]
  }): Promise<ApiResponse<{ downloadUrl: string }>> {
    return await apiService.post<{ downloadUrl: string }>('/admin/content/scripts/export', params)
  }

  // 导入剧本数据
  async importScripts(file: File): Promise<ApiResponse<{ 
    success: number
    failed: number
    errors?: string[]
  }>> {
    const formData = new FormData()
    formData.append('file', file)
    return await apiService.post<{
      success: number
      failed: number
      errors?: string[]
    }>('/admin/content/scripts/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // ==================== 剧本预览 ====================

  // 生成剧本预览
  async generatePreview(id: string): Promise<ApiResponse<{
    previewUrl: string
    thumbnails: string[]
  }>> {
    return await apiService.post<{
      previewUrl: string
      thumbnails: string[]
    }>(`/admin/content/scripts/${id}/generate-preview`)
  }

  // 获取剧本预览
  async getPreview(id: string): Promise<ApiResponse<{
    previewUrl?: string
    thumbnails?: string[]
  }>> {
    return await apiService.get<{
      previewUrl?: string
      thumbnails?: string[]
    }>(`/admin/content/scripts/${id}/preview`)
  }
}

export const scriptService = new ScriptService()
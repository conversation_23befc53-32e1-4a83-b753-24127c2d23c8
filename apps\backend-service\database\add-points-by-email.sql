-- ==========================================
-- 为指定邮箱增加积分 SQL 脚本
-- 执行环境: Supabase Dashboard > SQL Editor
-- 创建时间: 2025-01-22
-- ==========================================

-- 使用说明：
-- 1. 修改下面的变量值为实际需要的邮箱和积分数量
-- 2. 在 Supabase SQL Editor 中执行此脚本
-- 3. 脚本会自动查找用户并增加积分

-- ==========================================
-- 配置变量 (请修改这些值)
-- ==========================================

DO $$
DECLARE
    -- 🔧 请在这里修改配置参数 🔧
    target_email TEXT := '<EMAIL>';           -- 目标用户邮箱
    points_amount INTEGER := 1000;                     -- 要增加的积分数量
    points_description TEXT := '管理员手动发放积分';      -- 积分来源说明
    points_source TEXT := 'admin';                     -- 积分来源类型 ('bonus', 'admin', 'purchase', 'subscription')
    
    -- 内部变量
    user_record RECORD;
    current_points INTEGER;
    new_balance INTEGER;
    transaction_id UUID;
    points_to_add INTEGER;
BEGIN
    -- 获取要添加的积分数量
    points_to_add := points_amount;
    
    -- 验证积分数量
    IF points_to_add <= 0 THEN
        RAISE EXCEPTION '积分数量必须大于0，当前值: %', points_to_add;
    END IF;
    
    IF points_to_add > 100000 THEN
        RAISE EXCEPTION '单次添加积分不能超过100,000，当前值: %', points_to_add;
    END IF;
    
    -- 1. 查找目标用户
    SELECT * INTO user_record
    FROM "User" 
    WHERE email = target_email;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION '用户不存在: %', target_email;
    END IF;
    
    RAISE NOTICE '找到用户: % (ID: %)', user_record.email, user_record.id;
    
    -- 2. 确保用户积分记录存在
    INSERT INTO "UserPoints" (
        id,
        user_id,
        total_points,
        used_points,
        available_points,
        last_updated
    ) 
    VALUES (
        gen_random_uuid(),
        user_record.id,
        0,
        0,
        0,
        NOW()
    )
    ON CONFLICT (user_id) DO NOTHING;
    
    -- 3. 获取当前积分余额
    SELECT available_points INTO current_points
    FROM "UserPoints" 
    WHERE user_id = user_record.id;
    
    RAISE NOTICE '当前积分余额: %', current_points;
    
    -- 4. 计算新的积分余额
    new_balance := current_points + points_to_add;
    
    -- 检查余额是否会超过系统限制
    IF new_balance > 1000000 THEN
        RAISE EXCEPTION '积分余额不能超过1,000,000，当前: %，添加后: %', current_points, new_balance;
    END IF;
    
    -- 5. 更新用户积分
    UPDATE "UserPoints" 
    SET 
        total_points = total_points + points_to_add,
        available_points = available_points + points_to_add,
        last_updated = NOW()
    WHERE user_id = user_record.id;
    
    -- 6. 创建积分交易记录
    transaction_id := gen_random_uuid();
    
    INSERT INTO "PointsTransaction" (
        id,
        user_id,
        transaction_type,
        amount,
        source,
        source_id,
        description,
        balance_after,
        metadata,
        created_at
    ) VALUES (
        transaction_id,
        user_record.id,
        'earn',
        points_to_add,
        points_source,
        NULL, -- 管理员操作没有特定来源ID
        points_description,
        new_balance,
        jsonb_build_object(
            'source', 'admin_script',
            'granted_by', 'database_admin',
            'previous_balance', current_points,
            'added_amount', points_to_add,
            'operation_time', NOW()
        ),
        NOW()
    );
    
    -- 7. 输出结果摘要
    RAISE NOTICE '========================================';
    RAISE NOTICE '积分添加完成！';
    RAISE NOTICE '用户邮箱: %', user_record.email;
    RAISE NOTICE '添加积分: %', points_to_add;
    RAISE NOTICE '原有积分: %', current_points;
    RAISE NOTICE '新增积分: %', new_balance;
    RAISE NOTICE '积分来源: %', points_source;
    RAISE NOTICE '交易ID: %', transaction_id;
    RAISE NOTICE '========================================';
    
END $$;

-- ==========================================
-- 验证结果
-- ==========================================

-- 查询用户当前积分状态
SELECT 
    u.email as "用户邮箱",
    up.total_points as "总获得积分",
    up.used_points as "已使用积分",
    up.available_points as "可用积分",
    up.membership_level as "会员等级",
    up.last_updated as "最后更新时间"
FROM "User" u
JOIN "UserPoints" up ON u.id = up.user_id
WHERE u.email = '<EMAIL>';  -- 🔧 请修改为实际邮箱

-- 查询最近的积分交易记录
SELECT 
    u.email as "用户邮箱",
    pt.transaction_type as "交易类型",
    pt.amount as "积分数量",
    pt.source as "来源",
    pt.description as "描述",
    pt.balance_after as "交易后余额",
    pt.created_at as "交易时间"
FROM "User" u
JOIN "PointsTransaction" pt ON u.id = pt.user_id
WHERE u.email = '<EMAIL>'  -- 🔧 请修改为实际邮箱
ORDER BY pt.created_at DESC
LIMIT 5;

-- ==========================================
-- 批量操作示例
-- ==========================================

-- 如果需要为多个用户批量添加积分，可以使用以下模板：

/*
-- 批量添加积分示例
DO $$
DECLARE
    email_list TEXT[] := ARRAY['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    target_email TEXT;
    points_to_add INTEGER := 500;
    user_id UUID;
BEGIN
    FOREACH target_email IN ARRAY email_list LOOP
        -- 查找用户ID
        SELECT id INTO user_id FROM "User" WHERE email = target_email;
        
        IF FOUND THEN
            -- 确保积分记录存在
            INSERT INTO "UserPoints" (id, user_id, total_points, used_points, available_points, last_updated) 
            VALUES (gen_random_uuid(), user_id, 0, 0, 0, NOW())
            ON CONFLICT (user_id) DO NOTHING;
            
            -- 更新积分
            UPDATE "UserPoints" 
            SET 
                total_points = total_points + points_to_add,
                available_points = available_points + points_to_add,
                last_updated = NOW()
            WHERE user_id = user_id;
            
            -- 记录交易
            INSERT INTO "PointsTransaction" (
                id, user_id, transaction_type, amount, source, description, 
                balance_after, created_at
            ) VALUES (
                gen_random_uuid(), user_id, 'earn', points_to_add, 'admin',
                '批量发放积分',
                (SELECT available_points FROM "UserPoints" WHERE user_id = user_id),
                NOW()
            );
            
            RAISE NOTICE '为用户 % 添加了 % 积分', target_email, points_to_add;
        ELSE
            RAISE NOTICE '用户不存在: %', target_email;
        END IF;
    END LOOP;
END $$;
*/

-- ==========================================
-- 使用示例
-- ==========================================

/*
-- 使用示例：直接修改上面DO块中的变量值

-- 示例1: 为用户添加1000积分
target_email TEXT := '<EMAIL>';
points_amount INTEGER := 1000;
points_description TEXT := '活动奖励积分';
points_source TEXT := 'bonus';

-- 示例2: 为VIP用户添加5000积分
target_email TEXT := '<EMAIL>';
points_amount INTEGER := 5000;
points_description TEXT := 'VIP用户专属奖励';
points_source TEXT := 'admin';

-- 示例3: 补偿积分
target_email TEXT := '<EMAIL>';
points_amount INTEGER := 200;
points_description TEXT := '系统故障补偿积分';
points_source TEXT := 'admin';
*/ 
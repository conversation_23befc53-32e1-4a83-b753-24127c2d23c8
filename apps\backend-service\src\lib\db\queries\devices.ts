import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type {
  Device,
  DeviceFunction,
  DeviceCommandSet,
  DeviceFunctionCommand,
  DeviceConnection,
  DeviceUsage,
} from '../schema';
import type { Env } from '@/types/env';

// ==================== 设备管理 ====================

/**
 * 根据设备码获取设备信息（包含功能和指令）
 */
export async function getDeviceByCode(env: Env, deviceCode: string) {
  const supabase = getSupabase(env);

  // 获取设备信息
  const deviceResult = await supabase
    .from(TABLE_NAMES.device)
    .select('*')
    .eq('device_code', deviceCode)
    .eq('is_active', true)
    .single();

  const { data: deviceData, error: deviceError } = handleSupabaseSingleResult(deviceResult);
  if (deviceError || !deviceData) {
    return null;
  }

  // 获取设备支持的功能
  const supportedFunctionsResult = await supabase
    .from('DeviceSupportedFunction')
    .select(`
      is_active,
      function:DeviceFunction(
        id,
        name,
        key,
        description,
        max_intensity,
        is_active
      )
    `)
    .eq('device_id', deviceData.id)
    .eq('is_active', true);

  let { data: supportedFunctions, error: functionsError } = handleSupabaseResult(supportedFunctionsResult);
  if (functionsError) {
    console.error('❌ [DEVICE] 功能查询错误:', functionsError);
    throw functionsError;
  }

  // 如果没有功能数据，尝试自动修复
  if (!supportedFunctions || supportedFunctions.length === 0) {
    console.log('⚠️ [DEVICE] 没有找到功能数据，尝试检查是否需要自动创建关联');
    
    // 查询所有可用的功能
    const allFunctionsResult = await supabase
      .from('DeviceFunction')
      .select('*')
      .eq('is_active', true);
    
    console.log('📋 [DEVICE] 所有可用功能:', allFunctionsResult.data?.length || 0);
    
    if (allFunctionsResult.data && allFunctionsResult.data.length > 0) {
      console.log('🔧 [DEVICE] 自动为设备创建功能关联');
      
      // 为这个设备创建所有功能的关联
      const insertData = allFunctionsResult.data.map(func => ({
        device_id: deviceData.id,
        function_id: func.id,
        is_active: true
      }));
      
      const insertResult = await supabase
        .from('DeviceSupportedFunction')
        .insert(insertData)
        .select();
      
      console.log('✅ [DEVICE] 创建功能关联结果:', insertResult.data?.length || 0);
      
      // 重新查询功能数据
      const retryResult = await supabase
        .from('DeviceSupportedFunction')
        .select(`
          is_active,
          function:DeviceFunction(
            id,
            name,
            key,
            description,
            max_intensity,
            is_active
          )
        `)
        .eq('device_id', deviceData.id)
        .eq('is_active', true);
      
      const { data: retrySupportedFunctions } = handleSupabaseResult(retryResult);
      console.log('🔄 [DEVICE] 重新查询后的功能数据:', retrySupportedFunctions?.length || 0);
      
      // 更新功能数据
      if (retrySupportedFunctions && retrySupportedFunctions.length > 0) {
        supportedFunctions = retrySupportedFunctions;
      }
    }
  }

  // 获取每个功能的指令
  const functionsWithCommands = await Promise.all(
    (supportedFunctions || []).map(async (supportedFunc: any) => {
      const func = supportedFunc.function;
      if (!func) return null;

      // 获取功能的指令映射
      const commandMappingsResult = await supabase
        .from('DeviceFunctionCommand')
        .select(`
          intensity,
          description,
          commandSet:DeviceCommandSet(
            id,
            name,
            command,
            broadcast
          )
        `)
        .eq('function_id', func.id)
        .order('intensity');

      const { data: commandMappings } = handleSupabaseResult(commandMappingsResult);

      // 转换为旧格式的commands
      const commands = (commandMappings || []).map((mapping: any) => ({
        intensity: mapping.intensity,
        command: mapping.commandSet?.command || '',
        description: mapping.description || mapping.commandSet?.description
      }));

      return {
        id: func.id,
        name: func.name,
        key: func.key,
        description: func.description,
        maxIntensity: func.max_intensity,
        isActive: func.is_active,
        commands: commands,
      };
    })
  );

  // 过滤掉空的功能
  const validFunctions = functionsWithCommands.filter(func => func !== null);

  return {
    ...deviceData,
    functions: validFunctions,
  };
}

/**
 * 获取用户的设备列表
 */
export async function getUserDevices(env: Env, userId: string): Promise<Device[]> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.device)
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .order('last_connected_at', { ascending: false })
    .order('created_at', { ascending: false });

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

/**
 * 获取系统预设设备列表（包含功能和指令）
 */
export async function getSystemDevices(env: Env) {
  const supabase = getSupabase(env);

  // 获取系统预设设备（user_id 为 null）
  const devicesResult = await supabase
    .from(TABLE_NAMES.device)
    .select('*')
    .is('user_id', null)
    .eq('is_active', true)
    .order('category')
    .order('name');

  const { data: devices, error: devicesError } = handleSupabaseResult(devicesResult);
  if (devicesError) throw devicesError;

  // 为每个设备获取功能和指令
  const devicesWithFunctions = await Promise.all(
    (devices || []).map(async (deviceData: any) => {
      // 获取设备支持的功能
      console.log('🔍 [SYSTEM-DEVICE] 查询设备功能，设备ID:', deviceData.id, '设备名称:', deviceData.name);
      
      const supportedFunctionsResult = await supabase
        .from('DeviceSupportedFunction')
        .select(`
          is_active,
          function:DeviceFunction(
            id,
            name,
            key,
            description,
            max_intensity,
            is_active
          )
        `)
        .eq('device_id', deviceData.id)
        .eq('is_active', true);

      console.log('📋 [SYSTEM-DEVICE] Supabase查询结果:', supportedFunctionsResult);

      let { data: supportedFunctions } = handleSupabaseResult(supportedFunctionsResult);
      
      console.log('📊 [SYSTEM-DEVICE] 处理后的功能数据:', supportedFunctions);

      // 如果没有功能数据，尝试自动修复
      if (!supportedFunctions || supportedFunctions.length === 0) {
        console.log('⚠️ [SYSTEM-DEVICE] 设备', deviceData.name, '没有功能数据，尝试自动创建关联');
        
        // 查询所有可用的功能
        const allFunctionsResult = await supabase
          .from('DeviceFunction')
          .select('*')
          .eq('is_active', true);
        
        if (allFunctionsResult.data && allFunctionsResult.data.length > 0) {
          console.log('🔧 [SYSTEM-DEVICE] 自动为设备', deviceData.name, '创建功能关联');
          
          // 为这个设备创建所有功能的关联
          const insertData = allFunctionsResult.data.map(func => ({
            device_id: deviceData.id,
            function_id: func.id,
            is_active: true
          }));
          
          const insertResult = await supabase
            .from('DeviceSupportedFunction')
            .insert(insertData)
            .select();
          
          console.log('✅ [SYSTEM-DEVICE] 创建功能关联结果:', insertResult.data?.length || 0);
          
          // 重新查询功能数据
          const retryResult = await supabase
            .from('DeviceSupportedFunction')
            .select(`
              is_active,
              function:DeviceFunction(
                id,
                name,
                key,
                description,
                max_intensity,
                is_active
              )
            `)
            .eq('device_id', deviceData.id)
            .eq('is_active', true);
          
          const { data: retrySupportedFunctions } = handleSupabaseResult(retryResult);
          console.log('🔄 [SYSTEM-DEVICE] 重新查询后的功能数据:', retrySupportedFunctions?.length || 0);
          
          // 更新功能数据
          if (retrySupportedFunctions && retrySupportedFunctions.length > 0) {
            supportedFunctions = retrySupportedFunctions;
          }
        }
      }

      // 获取每个功能的指令
      const functionsWithCommands = await Promise.all(
        (supportedFunctions || []).map(async (supportedFunc: any) => {
          const func = supportedFunc.function;
          if (!func) return null;

          // 获取功能的指令映射
          const commandMappingsResult = await supabase
            .from('DeviceFunctionCommand')
            .select(`
              intensity,
              description,
              commandSet:DeviceCommandSet(
                id,
                name,
                command,
                broadcast
              )
            `)
            .eq('function_id', func.id)
            .order('intensity');

          const { data: commandMappings } = handleSupabaseResult(commandMappingsResult);

          // 转换为旧格式的commands
          const commands = (commandMappings || []).map((mapping: any) => ({
            intensity: mapping.intensity,
            command: mapping.commandSet?.command || '',
            description: mapping.description || mapping.commandSet?.description
          }));

          return {
            id: func.id,
            name: func.name,
            key: func.key,
            description: func.description,
            maxIntensity: func.max_intensity,
            isActive: func.is_active,
            commands: commands,
          };
        })
      );

      // 过滤掉空的功能
      const validFunctions = functionsWithCommands.filter(func => func !== null);

      return {
        ...deviceData,
        functions: validFunctions,
      };
    })
  );

  return devicesWithFunctions;
}

/**
 * 创建新设备
 */
export async function createDevice(
  env: Env,
  deviceData: {
    userId: string;
    deviceCode: string;
    name: string;
    pic?: string;
    brand?: string;
    model?: string;
    category?: string;
    description?: string;
  }
): Promise<Device | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.device)
    .insert({
      user_id: deviceData.userId,
      device_code: deviceData.deviceCode,
      name: deviceData.name,
      pic: deviceData.pic,
      brand: deviceData.brand,
      model: deviceData.model,
      category: deviceData.category,
      description: deviceData.description,
    })
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

/**
 * 更新设备信息
 */
export async function updateDevice(
  env: Env,
  deviceId: string,
  updates: Partial<Device>
): Promise<Device | null> {
  const supabase = getSupabase(env);

  // 转换字段名为 snake_case
  const updateData: any = {};
  if (updates.name !== undefined) updateData.name = updates.name;
  if (updates.pic !== undefined) updateData.pic = updates.pic;
  if (updates.brand !== undefined) updateData.brand = updates.brand;
  if (updates.model !== undefined) updateData.model = updates.model;
  if (updates.category !== undefined) updateData.category = updates.category;
  if (updates.description !== undefined) updateData.description = updates.description;
  if (updates.isActive !== undefined) updateData.is_active = updates.isActive;
  if (updates.lastConnectedAt !== undefined)
    updateData.last_connected_at = updates.lastConnectedAt?.toISOString();

  updateData.updated_at = new Date().toISOString();

  const result = await supabase
    .from(TABLE_NAMES.device)
    .update(updateData)
    .eq('id', deviceId)
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

/**
 * 删除设备（软删除）
 */
export async function deleteDevice(
  env: Env,
  deviceId: string,
  userId: string
): Promise<Device | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.device)
    .update({
      is_active: false,
      updated_at: new Date().toISOString(),
    })
    .eq('id', deviceId)
    .eq('user_id', userId)
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

// ==================== 设备功能管理 ====================

/**
 * 为设备添加功能支持
 */
export async function createDeviceFunction(
  env: Env,
  functionData: {
    deviceId: string;
    functionId: string;
    isActive?: boolean;
  }
): Promise<any | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from('DeviceSupportedFunction')
    .insert({
      device_id: functionData.deviceId,
      function_id: functionData.functionId,
      is_active: functionData.isActive ?? true,
    })
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

/**
 * 获取设备功能列表
 */
export async function getDeviceFunctions(env: Env, deviceId: string): Promise<any[]> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from('DeviceSupportedFunction')
    .select(`
      *,
      function:DeviceFunction(*)
    `)
    .eq('device_id', deviceId)
    .eq('is_active', true);

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

/**
 * 更新设备功能支持状态
 */
export async function updateDeviceFunction(
  env: Env,
  deviceId: string,
  functionId: string,
  updates: { isActive?: boolean }
): Promise<any | null> {
  const supabase = getSupabase(env);

  const updateData: any = {};
  if (updates.isActive !== undefined) updateData.is_active = updates.isActive;

  const result = await supabase
    .from('DeviceSupportedFunction')
    .update(updateData)
    .eq('device_id', deviceId)
    .eq('function_id', functionId)
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

// ==================== 设备指令管理 ====================

/**
 * 为功能添加指令映射
 */
export async function createDeviceCommand(
  env: Env,
  commandData: {
    functionId: string;
    commandSetId: string;
    intensity: number;
    description?: string;
  }
): Promise<DeviceFunctionCommand | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from('DeviceFunctionCommand')
    .insert({
      function_id: commandData.functionId,
      command_set_id: commandData.commandSetId,
      intensity: commandData.intensity,
      description: commandData.description,
    })
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

/**
 * 获取功能的指令列表
 */
export async function getFunctionCommands(env: Env, functionId: string): Promise<any[]> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from('DeviceFunctionCommand')
    .select(`
      *,
      commandSet:DeviceCommandSet(*)
    `)
    .eq('function_id', functionId)
    .order('intensity');

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

/**
 * 批量创建设备指令映射
 */
export async function createDeviceCommands(
  env: Env,
  commands: Array<{
    functionId: string;
    commandSetId: string;
    intensity: number;
    description?: string;
  }>
): Promise<DeviceFunctionCommand[]> {
  const supabase = getSupabase(env);

  const insertData = commands.map((cmd) => ({
    function_id: cmd.functionId,
    command_set_id: cmd.commandSetId,
    intensity: cmd.intensity,
    description: cmd.description,
  }));

  const result = await supabase.from('DeviceFunctionCommand').insert(insertData).select();

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

// ==================== 设备连接管理 ====================

/**
 * 记录设备连接
 */
export async function createDeviceConnection(
  env: Env,
  connectionData: {
    userId: string;
    deviceId: string;
    sessionId?: string;
    metadata?: any;
  }
): Promise<DeviceConnection | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.deviceConnection)
    .insert({
      user_id: connectionData.userId,
      device_id: connectionData.deviceId,
      session_id: connectionData.sessionId,
      metadata: connectionData.metadata || {},
    })
    .select()
    .single();

  const { data: connection, error } = handleSupabaseSingleResult(result);
  if (error) throw error;

  // 更新设备最后连接时间
  await supabase
    .from(TABLE_NAMES.device)
    .update({
      last_connected_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', connectionData.deviceId);

  return connection;
}

/**
 * 更新连接状态
 */
export async function updateDeviceConnection(
  env: Env,
  connectionId: string,
  updates: {
    status?: 'connected' | 'disconnected' | 'error';
    disconnectedAt?: Date;
    errorMessage?: string;
  }
): Promise<DeviceConnection | null> {
  const supabase = getSupabase(env);

  // 转换字段名为 snake_case
  const updateData: any = {};
  if (updates.status !== undefined) updateData.status = updates.status;
  if (updates.disconnectedAt !== undefined)
    updateData.disconnected_at = updates.disconnectedAt.toISOString();
  if (updates.errorMessage !== undefined) updateData.error_message = updates.errorMessage;

  const result = await supabase
    .from(TABLE_NAMES.deviceConnection)
    .update(updateData)
    .eq('id', connectionId)
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

/**
 * 获取用户的设备连接记录
 */
export async function getUserDeviceConnections(env: Env, userId: string, limit = 50) {
  const supabase = getSupabase(env);

  // 由于 Supabase 不支持复杂的 JOIN 查询，我们分别查询
  const connectionsResult = await supabase
    .from(TABLE_NAMES.deviceConnection)
    .select('*')
    .eq('user_id', userId)
    .order('connected_at', { ascending: false })
    .limit(limit);

  const { data: connections, error } = handleSupabaseResult(connectionsResult);
  if (error) throw error;

  // 获取设备信息并组合结果
  const results = await Promise.all(
    (connections || []).map(async (connection: any) => {
      const deviceResult = await supabase
        .from(TABLE_NAMES.device)
        .select('name')
        .eq('id', connection.deviceId)
        .single();

      const { data: device } = handleSupabaseSingleResult(deviceResult);

      return {
        id: connection.id,
        deviceId: connection.deviceId,
        deviceName: device?.name || 'Unknown Device',
        status: connection.status,
        connectedAt: connection.connectedAt,
        disconnectedAt: connection.disconnectedAt,
        sessionId: connection.sessionId,
      };
    })
  );

  return results;
}

// ==================== 设备使用记录 ====================

/**
 * 记录设备使用
 */
export async function createDeviceUsage(
  env: Env,
  usageData: {
    userId: string;
    deviceId: string;
    scriptId?: string;
    sessionId?: string;
    functionKey: string;
    intensity: number;
    metadata?: any;
  }
): Promise<DeviceUsage | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.deviceUsage)
    .insert({
      user_id: usageData.userId,
      device_id: usageData.deviceId,
      script_id: usageData.scriptId,
      session_id: usageData.sessionId,
      function_key: usageData.functionKey,
      intensity: usageData.intensity,
      metadata: usageData.metadata || {},
    })
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

/**
 * 结束设备使用记录
 */
export async function endDeviceUsage(
  env: Env,
  usageId: string,
  duration?: number
): Promise<DeviceUsage | null> {
  const supabase = getSupabase(env);

  const updateData: any = {
    ended_at: new Date().toISOString(),
  };

  if (duration !== undefined) {
    updateData.duration = duration.toString();
  }

  const result = await supabase
    .from(TABLE_NAMES.deviceUsage)
    .update(updateData)
    .eq('id', usageId)
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data;
}

/**
 * 获取用户设备使用统计
 */
export async function getUserDeviceUsageStats(env: Env, userId: string, deviceId?: string) {
  const supabase = getSupabase(env);

  let query = supabase
    .from(TABLE_NAMES.deviceUsage)
    .select('function_key, intensity, duration, started_at')
    .eq('user_id', userId);

  if (deviceId) {
    query = query.eq('device_id', deviceId);
  }

  const result = await query;
  const { data: usageData, error } = handleSupabaseResult(result);
  if (error) throw error;

  // 手动计算统计信息
  const stats = (usageData || []).reduce(
    (acc: any, usage: any) => {
      const key = usage.functionKey;
      if (!acc[key]) {
        acc[key] = {
          functionKey: key,
          totalUsage: 0,
          totalDuration: 0,
          intensitySum: 0,
          lastUsedAt: null,
        };
      }

      acc[key].totalUsage++;
      acc[key].totalDuration += Number(usage.duration) || 0;
      acc[key].intensitySum += usage.intensity;

      const usedAt = new Date(usage.startedAt);
      if (!acc[key].lastUsedAt || usedAt > acc[key].lastUsedAt) {
        acc[key].lastUsedAt = usedAt;
      }

      return acc;
    },
    {} as Record<string, any>
  );

  return Object.values(stats).map((stat: any) => ({
    functionKey: stat.functionKey,
    totalUsage: stat.totalUsage,
    totalDuration: stat.totalDuration,
    avgIntensity: stat.totalUsage > 0 ? stat.intensitySum / stat.totalUsage : 0,
    lastUsedAt: stat.lastUsedAt,
  }));
}

/**
 * 获取设备使用历史
 */
export async function getDeviceUsageHistory(
  env: Env,
  userId: string,
  deviceId?: string,
  limit = 100
) {
  const supabase = getSupabase(env);

  let query = supabase.from(TABLE_NAMES.deviceUsage).select('*').eq('user_id', userId);

  if (deviceId) {
    query = query.eq('device_id', deviceId);
  }

  const result = await query.order('started_at', { ascending: false }).limit(limit);

  const { data: usageData, error } = handleSupabaseResult(result);
  if (error) throw error;

  // 获取设备信息并组合结果
  const results = await Promise.all(
    (usageData || []).map(async (usage: any) => {
      const deviceResult = await supabase
        .from(TABLE_NAMES.device)
        .select('name')
        .eq('id', usage.deviceId)
        .single();

      const { data: device } = handleSupabaseSingleResult(deviceResult);

      return {
        id: usage.id,
        deviceId: usage.deviceId,
        deviceName: device?.name || 'Unknown Device',
        functionKey: usage.functionKey,
        intensity: usage.intensity,
        duration: Number(usage.duration) || 0,
        startedAt: usage.startedAt,
        endedAt: usage.endedAt,
        sessionId: usage.sessionId,
      };
    })
  );

  return results;
}

# 设备控制组件

## 组件概述

设备控制组件提供了完整的设备连接和控制功能，支持多种设备类型和功能。

## 组件列表

### DeviceConnectCore

通用的设备连接核心组件，可在不同场景中复用。

**特性：**

- 设备码输入连接
- 二维码扫描连接
- 键盘避让功能
- 错误处理和用户反馈
- 支持外部状态管理

**使用示例：**

```tsx
import { DeviceConnectCore } from '@/components/device'
;<DeviceConnectCore
  onDeviceConnect={device => {
    console.log('设备连接成功:', device)
  }}
  showSkipButton={false}
  connectButtonText="连接我的设备"
/>
```

### DeviceConnectionForm

保持特定设计风格的设备连接表单组件。

**特性：**

- Framer Motion 动画效果
- 设计稿样式复原
- 集成真实 API 调用

### DeviceControl

动态设备控制组件，根据设备功能自动渲染控制界面。

**特性：**

- 自适应布局（单功能占满，多功能网格）
- 0-3 档强度控制（0 档为关闭状态）
- 动态颜色主题
- 经典模式选择
- 断开连接功能
- **🆕 完整蓝牙控制集成**
- **🆕 实时设备命令发送**
- **🆕 蓝牙状态指示器**
- **🆕 错误处理和用户反馈**

**强度等级说明：**

- 0 档：关闭状态（灰色显示）
- 1 档：低强度
- 2 档：中强度
- 3 档：高强度

**使用示例：**

```tsx
import { DeviceControl } from '@/components/device'
;<DeviceControl
  device={connectedDevice}
  onBack={() => {
    // 处理断开连接
  }}
/>
```

## 设备数据结构

```typescript
interface Device {
  pic: string // 设备图片URL
  name: string // 设备名称
  func: DeviceFunction[] // 设备功能列表
}

interface DeviceFunction {
  name: string // 功能名称（如"振动"、"吮吸"）
  key: string // 功能唯一标识
  commands: DeviceCommand[] // 命令列表
}

interface DeviceCommand {
  intensity: number // 强度值（-1表示停止）
  command: string // 命令字符串
}
```

## 布局适配

### 单功能设备

当设备只有 1 个功能时，控制卡片会占满整个宽度（grid-cols-1）。

### 多功能设备

当设备有 2 个或更多功能时，使用 2 列网格布局（grid-cols-2）。

## 颜色主题

系统为不同的设备功能分配不同的颜色主题：

- 功能 1：粉色 (#ff2d97)
- 功能 2：紫色 (#892fff)
- 功能 3：蓝色 (#00d4ff)
- 功能 4：橙色 (#ff9500)
- 功能 5：绿色 (#00ff88)

颜色会循环使用，支持任意数量的设备功能。

## API 集成

组件集成了以下 API：

- `deviceService.connectDevice(deviceCode)` - 连接设备
- `deviceService.getSupportedDevices()` - 获取支持的设备列表

## 注意事项

1. 设备强度范围固定为 0-3 档，0 档表示关闭状态
2. 单功能设备会自动占满控制区域宽度
3. 所有组件都支持移动端响应式设计
4. 断开连接功能通过 onBack 回调实现

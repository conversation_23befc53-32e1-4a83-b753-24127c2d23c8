import { Hono } from 'hono'
import { authMiddleware } from '@/middleware/auth'
import { verifySupabaseToken } from '@/lib/supabase'
import type { Env } from '@/types/env'

const sse = new Hono<{ Bindings: Env }>()

// SSE连接管理器
class SSEConnectionManager {
  private connections = new Map<string, WritableStreamDefaultWriter>()
  private userConnections = new Map<string, Set<string>>() // userId -> Set<connectionId>
  private heartbeatInterval: number | null = null // 心跳定时器ID
  private heartbeatInitialized = false // 是否已初始化心跳
  private connectionTimeouts = new Map<string, any>() // 连接超时管理

  constructor() {
    // 不在构造函数中启动心跳，避免全局作用域异步操作
  }

  /**
   * 初始化心跳机制
   */
  private initHeartbeat() {
    if (!this.heartbeatInitialized) {
      this.startHeartbeat()
      this.heartbeatInitialized = true
      console.log('📡 [SSE] 心跳机制已启动')
    }
  }

  /**
   * 添加新连接
   */
  addConnection(userId: string, connectionId: string, writer: WritableStreamDefaultWriter) {
    this.connections.set(connectionId, writer)

    if (!this.userConnections.has(userId)) {
      this.userConnections.set(userId, new Set())
    }
    this.userConnections.get(userId)!.add(connectionId)

    // 🔧 设置连接超时 (5分钟)
    const timeout = setTimeout(() => {
      console.log(`📡 [SSE] 连接超时，自动清理: ${connectionId}`)
      this.removeConnection(userId, connectionId)
    }, 300000) // 5分钟超时
    this.connectionTimeouts.set(connectionId, timeout)

    // 当有第一个连接时，初始化心跳
    if (this.connections.size === 1) {
      this.initHeartbeat()
    }

    console.log(`📡 [SSE] 新连接: ${connectionId}, 用户: ${userId}`)
    console.log(`📊 [SSE] 当前连接数: ${this.connections.size}`)
  }

  /**
   * 移除连接
   */
  removeConnection(userId: string, connectionId: string) {
    // 🔧 清理连接超时
    const timeout = this.connectionTimeouts.get(connectionId)
    if (timeout) {
      clearTimeout(timeout)
      this.connectionTimeouts.delete(connectionId)
    }

    this.connections.delete(connectionId)

    const userConns = this.userConnections.get(userId)
    if (userConns) {
      userConns.delete(connectionId)
      if (userConns.size === 0) {
        this.userConnections.delete(userId)
      }
    }

    // 当没有连接时，停止心跳
    if (this.connections.size === 0 && this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
      this.heartbeatInitialized = false
      console.log('📡 [SSE] 心跳机制已停止')
    }

    console.log(`📡 [SSE] 连接断开: ${connectionId}, 用户: ${userId}`)
    console.log(`📊 [SSE] 剩余连接数: ${this.connections.size}`)
  }

  /**
   * 向特定用户发送事件
   */
  async sendToUser(userId: string, event: SSEEvent) {
    const userConnections = this.userConnections.get(userId)
    if (!userConnections || userConnections.size === 0) {
      console.log(`📡 [SSE] 用户 ${userId} 无活动连接`)
      return false
    }

    const message = this.formatSSEMessage(event)
    let successCount = 0
    const disconnectedConnections: string[] = []

    for (const connectionId of userConnections) {
      const writer = this.connections.get(connectionId)
      if (writer) {
        try {
          await writer.write(new TextEncoder().encode(message))
          successCount++

          // 🔧 重置连接超时 (活跃连接延长存活时间)
          this.resetConnectionTimeout(userId, connectionId)
        } catch (error) {
          console.error(`📡 [SSE] 发送失败: ${connectionId}`, error)
          disconnectedConnections.push(connectionId)
        }
      }
    }

    // 清理断开的连接
    for (const connectionId of disconnectedConnections) {
      this.removeConnection(userId, connectionId)
    }

    console.log(
      `📡 [SSE] 向用户 ${userId} 发送事件，成功: ${successCount}, 失败: ${disconnectedConnections.length}`
    )
    return successCount > 0
  }

  /**
   * 🔧 重置连接超时
   */
  private resetConnectionTimeout(userId: string, connectionId: string) {
    const oldTimeout = this.connectionTimeouts.get(connectionId)
    if (oldTimeout) {
      clearTimeout(oldTimeout)
    }

    const newTimeout = setTimeout(() => {
      console.log(`📡 [SSE] 连接超时，自动清理: ${connectionId}`)
      this.removeConnection(userId, connectionId)
    }, 300000) // 5分钟超时

    this.connectionTimeouts.set(connectionId, newTimeout)
  }

  /**
   * 广播事件到所有连接
   */
  async broadcast(event: SSEEvent) {
    const message = this.formatSSEMessage(event)
    let successCount = 0
    const disconnectedConnections: { connectionId: string; userId: string }[] = []

    // 🔧 需要同时跟踪 userId 以便正确清理
    for (const [userId, connectionIds] of this.userConnections) {
      for (const connectionId of connectionIds) {
        const writer = this.connections.get(connectionId)
        if (writer) {
          try {
            await writer.write(new TextEncoder().encode(message))
            successCount++
            this.resetConnectionTimeout(userId, connectionId)
          } catch (error) {
            console.error(`📡 [SSE] 广播失败: ${connectionId}`, error)
            disconnectedConnections.push({ connectionId, userId })
          }
        }
      }
    }

    // 清理断开的连接
    for (const { connectionId, userId } of disconnectedConnections) {
      this.removeConnection(userId, connectionId)
    }

    console.log(`📡 [SSE] 广播事件，成功: ${successCount}, 失败: ${disconnectedConnections.length}`)
    return successCount
  }

  /**
   * 格式化SSE消息
   */
  private formatSSEMessage(event: SSEEvent): string {
    let message = ''

    if (event.id) {
      message += `id: ${event.id}\n`
    }

    if (event.event) {
      message += `event: ${event.event}\n`
    }

    if (event.data) {
      message += `data: ${JSON.stringify(event.data)}\n`
    }

    if (event.retry) {
      message += `retry: ${event.retry}\n`
    }

    message += '\n'
    return message
  }

  /**
   * 发送心跳到所有连接
   */
  private async sendHeartbeat() {
    const heartbeatEvent: SSEEvent = {
      event: 'heartbeat',
      data: { timestamp: new Date().toISOString() }
    }

    const sentCount = await this.broadcast(heartbeatEvent)
    console.log(`📡 [SSE] 心跳发送完成，活跃连接: ${sentCount}`)
  }

  /**
   * 启动心跳检查
   */
  private startHeartbeat() {
    // 🔧 缩短心跳间隔到15秒，更快检测断开连接
    this.heartbeatInterval = setInterval(() => {
      this.sendHeartbeat().catch(error => {
        console.error('📡 [SSE] 心跳发送失败:', error)
      })
    }, 15000) as any // 15秒间隔
  }

  /**
   * 获取连接统计信息
   */
  getStats() {
    return {
      totalConnections: this.connections.size,
      totalUsers: this.userConnections.size,
      connectionsPerUser: Array.from(this.userConnections.entries()).map(
        ([userId, connections]) => ({
          userId,
          connectionCount: connections.size
        })
      )
    }
  }
}

// 全局SSE连接管理器实例
const sseManager = new SSEConnectionManager()

// SSE事件类型定义
interface SSEEvent {
  id?: string
  event?: string
  data?: any
  retry?: number
}

// 媒体生成完成事件类型
interface MediaCompletionEvent {
  type: 'media_completed'
  messageId: string
  chatId: string
  mediaType: 'audio' | 'image' | 'video'
  mediaUrl: string
  status: 'completed' | 'failed'
  errorMessage?: string
  metadata?: any
}

/**
 * SSE事件推送 - 建立EventSource连接
 * 注意：EventSource不支持自定义头部，需要特殊的认证处理
 */
sse.get('/events', async c => {
  // 🔧 修复：SSE专用认证逻辑（支持查询参数）
  let user = null

  // 先尝试标准的Authorization头部认证
  const authHeader = c.req.header('Authorization')
  if (authHeader) {
    const token = authHeader.replace('Bearer ', '')
    if (token) {
      try {
        const { user: authUser, error } = await verifySupabaseToken(c.env, token)
        if (!error && authUser) {
          user = authUser
        }
      } catch (error) {
        console.warn('📡 [SSE] 头部认证失败:', error)
      }
    }
  }

  // 如果头部认证失败，尝试查询参数认证
  if (!user) {
    const tokenParam = c.req.query('token')
    if (tokenParam) {
      try {
        const { user: authUser, error } = await verifySupabaseToken(c.env, tokenParam)
        if (!error && authUser) {
          user = authUser
        }
      } catch (error) {
        console.warn('📡 [SSE] 查询参数认证失败:', error)
      }
    }
  }

  if (!user) {
    return c.json({ error: '用户未认证' }, 401)
  }

  try {
    // 生成唯一连接ID
    const connectionId = `sse_${user.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 创建可读流
    const { readable, writable } = new TransformStream()
    const writer = writable.getWriter()

    // 添加到连接管理器
    sseManager.addConnection(user.id, connectionId, writer)

    // 发送初始连接确认事件
    const welcomeEvent: SSEEvent = {
      id: connectionId,
      event: 'connected',
      data: {
        message: 'SSE连接已建立',
        connectionId,
        userId: user.id,
        timestamp: new Date().toISOString()
      }
    }

    const welcomeMessage = `id: ${welcomeEvent.id}\nevent: ${
      welcomeEvent.event
    }\ndata: ${JSON.stringify(welcomeEvent.data)}\n\n`
    await writer.write(new TextEncoder().encode(welcomeMessage))

    // 设置SSE响应头
    const headers = new Headers({
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'Access-Control-Allow-Credentials': 'true'
    })

    // 处理连接关闭
    const response = new Response(readable, { headers })

    // 🔧 改进连接关闭处理
    c.executionCtx.waitUntil(
      new Promise<void>(resolve => {
        // 🔧 监听writer关闭状态的方法
        const checkWriterClosed = async () => {
          try {
            while (true) {
              // 定期检查writer状态
              await new Promise(resolve => setTimeout(resolve, 5000)) // 每5秒检查一次

              try {
                // 尝试写入一个空的心跳来检测连接状态
                await writer.write(new TextEncoder().encode(': keepalive\n\n'))
              } catch (error) {
                // 写入失败说明连接已断开
                console.log(`📡 [SSE] 连接检测失败，自动清理: ${connectionId}`, error)
                sseManager.removeConnection(user.id, connectionId)
                writer.close().catch(() => {})
                resolve()
                return
              }
            }
          } catch (error) {
            console.log(`📡 [SSE] 连接状态检查异常: ${connectionId}`, error)
            sseManager.removeConnection(user.id, connectionId)
            writer.close().catch(() => {})
            resolve()
          }
        }

        // 启动连接状态检查
        checkWriterClosed()

        // 🔧 缩短兜底超时到3分钟，更快清理僵尸连接
        setTimeout(() => {
          console.log(`📡 [SSE] 兜底超时清理: ${connectionId}`)
          sseManager.removeConnection(user.id, connectionId)
          writer.close().catch(() => {})
          resolve()
        }, 180000) // 3分钟兜底超时
      })
    )

    return response
  } catch (error) {
    console.error('📡 [SSE] 建立连接失败:', error)
    return c.json({ error: '建立SSE连接失败' }, 500)
  }
})

/**
 * 手动触发事件推送 - 用于测试
 */
sse.post('/trigger', authMiddleware, async c => {
  const user = c.get('user')
  if (!user) {
    return c.json({ error: '用户未认证' }, 401)
  }

  try {
    const body = await c.req.json()
    const { event, data, targetUserId } = body

    const testEvent: SSEEvent = {
      id: `test_${Date.now()}`,
      event: event || 'test',
      data: data || { message: '这是一个测试事件', timestamp: new Date().toISOString() }
    }

    let success = false
    if (targetUserId) {
      success = await sseManager.sendToUser(targetUserId, testEvent)
    } else {
      const sentCount = await sseManager.broadcast(testEvent)
      success = sentCount > 0
    }

    return c.json({
      success,
      message: success ? '事件发送成功' : '没有活动连接',
      stats: sseManager.getStats()
    })
  } catch (error) {
    console.error('📡 [SSE] 手动触发事件失败:', error)
    return c.json({ error: '触发事件失败' }, 500)
  }
})

/**
 * 获取SSE连接统计信息
 */
sse.get('/stats', authMiddleware, async c => {
  const user = c.get('user')
  if (!user) {
    return c.json({ error: '用户未认证' }, 401)
  }

  try {
    const stats = sseManager.getStats()
    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('📡 [SSE] 获取统计信息失败:', error)
    return c.json({ error: '获取统计信息失败' }, 500)
  }
})

// 导出SSE管理器供队列消费者使用
export { sseManager, type MediaCompletionEvent }

export default sse

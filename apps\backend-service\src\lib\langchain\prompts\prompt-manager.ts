// 提示词管理器

import { PromptTemplate } from '@langchain/core/prompts';
import type { CharacterType } from '../types';
import type { LangChainMessage } from '../types/chat';
import {
  SYSTEM_TEMPLATE_STRING,
  generateCharacterPrompt,
  generateRoleDescription,
  CONVERSATION_CONTEXT_TEMPLATE,
  MEMORY_CONTEXT_TEMPLATE,
} from './templates';

export interface PromptOptions {
  username: string;
  userGender?: string; // 用户性别（可选）
  character?: CharacterType;
  conversationHistory?: LangChainMessage[];
  memories?: string[]; // 预留记忆内容
}

export class PromptManager {
  private systemTemplate: PromptTemplate;
  private conversationTemplate: PromptTemplate;
  private memoryTemplate: PromptTemplate;

  constructor() {
    // 初始化提示词模板
    this.systemTemplate = PromptTemplate.fromTemplate(SYSTEM_TEMPLATE_STRING);
    this.conversationTemplate = PromptTemplate.fromTemplate(CONVERSATION_CONTEXT_TEMPLATE);
    this.memoryTemplate = PromptTemplate.fromTemplate(MEMORY_CONTEXT_TEMPLATE);
  }

  /**
   * 生成系统提示词
   */
  async generateSystemPrompt(options: PromptOptions): Promise<string> {
    const characterPrompt = generateCharacterPrompt(options.character);
    const characterName = options.character?.name || 'AI助手';
    const roleDescription = options.character
      ? generateRoleDescription(options.character)
      : '你正在与AI助手进行对话';

    return await this.systemTemplate.format({
      username: options.username,
      userGender: options.userGender || '未知', // 添加用户性别参数
      characterName,
      roleDescription,
      characterPrompt,
    });
  }

  /**
   * 生成对话历史上下文
   */
  async generateConversationContext(messages: LangChainMessage[]): Promise<string> {
    if (!messages || messages.length === 0) {
      return '';
    }

    // 格式化对话历史
    const conversationHistory = messages
      .slice(-10) // 只取最近10条消息
      .map((msg) => {
        const role = msg.role === 'user' ? '用户' : 'AI';
        return `${role}: ${msg.content}`;
      })
      .join('\n');

    return await this.conversationTemplate.format({
      conversationHistory,
    });
  }

  /**
   * 生成记忆上下文（预留）
   */
  async generateMemoryContext(memories: string[]): Promise<string> {
    if (!memories || memories.length === 0) {
      return '';
    }

    const memoriesText = memories.join('\n- ');

    return await this.memoryTemplate.format({
      memories: `- ${memoriesText}`,
    });
  }

  /**
   * 生成完整的系统提示词（包含所有上下文）
   */
  async generateFullSystemPrompt(options: PromptOptions): Promise<string> {
    const systemPrompt = await this.generateSystemPrompt(options);

    let fullPrompt = systemPrompt;

    // 添加对话历史上下文
    if (options.conversationHistory && options.conversationHistory.length > 0) {
      const conversationContext = await this.generateConversationContext(
        options.conversationHistory
      );
      fullPrompt += '\n\n' + conversationContext;
    }

    // 添加记忆上下文（预留）
    if (options.memories && options.memories.length > 0) {
      const memoryContext = await this.generateMemoryContext(options.memories);
      fullPrompt += '\n\n' + memoryContext;
    }

    return fullPrompt;
  }

  /**
   * 验证提示词格式
   */
  validatePrompt(prompt: string): boolean {
    // 检查是否包含必要的格式说明
    const requiredElements = ['<scene:', '<action:', '<dialogue>', '<imagePrompt>', '<audioTags>'];

    return requiredElements.some((element) => prompt.includes(element));
  }
}

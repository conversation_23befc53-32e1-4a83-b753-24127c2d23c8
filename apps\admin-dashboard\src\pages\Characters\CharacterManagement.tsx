import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Avatar
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  UserOutlined,
  GlobalOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type {
  SystemCharacter,
  CreateCharacterRequest,
  CharacterCategory
} from '@/services/characters'
import { characterService } from '@/services/characters'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

const CharacterManagement: React.FC = () => {
  const [characters, setCharacters] = useState<SystemCharacter[]>([])
  const [categories, setCategories] = useState<CharacterCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCharacter, setEditingCharacter] = useState<SystemCharacter | null>(null)
  const [form] = Form.useForm()

  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    category: undefined as string | undefined,
    isActive: undefined as boolean | undefined,
    isPublic: undefined as boolean | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    public: 0,
    private: 0
  })

  useEffect(() => {
    loadCharacters()
    loadCategories()
  }, [currentPage, pageSize, searchParams])

  const loadCharacters = async () => {
    try {
      setLoading(true)

      const response = await characterService.getSystemCharacters({
        page: currentPage,
        pageSize,
        ...searchParams
      })

      if (response.success && response.data) {
        setCharacters(response.data.data)
        setTotal(response.data.total)

        // 计算统计数据
        const activeCount = response.data.data.filter(item => item.isActive).length
        const publicCount = response.data.data.filter(item => item.isPublic).length
        setStats({
          total: response.data.total,
          active: activeCount,
          inactive: response.data.total - activeCount,
          public: publicCount,
          private: response.data.total - publicCount
        })
      } else {
        message.error(response.message || '获取系统角色列表失败')
      }
    } catch (error) {
      console.error('获取系统角色列表失败:', error)
      message.error('获取系统角色列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await characterService.getCharacterCategories()
      if (response.success && response.data) {
        setCategories(response.data)
      }
    } catch (error) {
      console.error('获取角色分类失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      category: undefined,
      isActive: undefined,
      isPublic: undefined
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingCharacter(null)
    form.resetFields()
    form.setFieldsValue({
      isActive: true,
      isPublic: true,
      gender: 'female',
      category: '女友'
    })
    setModalVisible(true)
  }

  const handleEdit = (character: SystemCharacter) => {
    setEditingCharacter(character)
    form.setFieldsValue({
      name: character.name,
      description: character.description,
      relationship: character.relationship,
      ethnicity: character.ethnicity,
      gender: character.gender,
      age: character.age,
      eyeColor: character.eyeColor,
      hairStyle: character.hairStyle,
      hairColor: character.hairColor,
      bodyType: character.bodyType,
      breastSize: character.breastSize,
      buttSize: character.buttSize,
      personality: character.personality,
      clothing: character.clothing,
      voice: character.voice,
      voiceModelId: character.voiceModelId,
      keywords: character.keywords,
      prompt: character.prompt,
      imageUrl: character.imageUrl,
      category: character.category,
      isPublic: character.isPublic,
      isActive: character.isActive
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: CreateCharacterRequest) => {
    try {
      if (editingCharacter) {
        const response = await characterService.updateSystemCharacter(editingCharacter.id, values)
        if (response.success) {
          message.success('系统角色更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await characterService.createSystemCharacter(values)
        if (response.success) {
          message.success('系统角色创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }

      setModalVisible(false)
      loadCharacters()
      loadCategories()
    } catch (error) {
      console.error('操作失败:', error)
      message.error(editingCharacter ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await characterService.deleteSystemCharacter(id)
      if (response.success) {
        message.success('系统角色删除成功')
        loadCharacters()
        loadCategories()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleViewDetail = (character: SystemCharacter) => {
    Modal.info({
      title: '系统角色详情',
      width: 800,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <div>
              <strong>基本信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <Row gutter={16}>
                  <Col span={4}>
                    <Avatar size={64} src={character.imageUrl} icon={<UserOutlined />} />
                  </Col>
                  <Col span={20}>
                    <div>
                      <strong>角色名称:</strong> {character.name}
                    </div>
                    <div>
                      <strong>描述:</strong> {character.description || '无'}
                    </div>
                    <div>
                      <strong>关系:</strong> {character.relationship || '无'}
                    </div>
                    <div>
                      <strong>分类:</strong> {character.category || '无'}
                    </div>
                    <div>
                      <strong>种族:</strong> {character.ethnicity || '无'}
                    </div>
                    <div>
                      <strong>性别:</strong> {character.gender || '无'}
                    </div>
                    <div>
                      <strong>年龄:</strong> {character.age || '无'}
                    </div>
                  </Col>
                </Row>
              </div>
            </div>

            <div>
              <strong>外观特征</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>
                  <strong>眼色:</strong> {character.eyeColor || '无'}
                </div>
                <div>
                  <strong>发型:</strong> {character.hairStyle || '无'}
                </div>
                <div>
                  <strong>发色:</strong> {character.hairColor || '无'}
                </div>
                <div>
                  <strong>体型:</strong> {character.bodyType || '无'}
                </div>
                <div>
                  <strong>胸部:</strong> {character.breastSize || '无'}
                </div>
                <div>
                  <strong>臀部:</strong> {character.buttSize || '无'}
                </div>
              </div>
            </div>

            <div>
              <strong>性格特征</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>{character.personality || '无'}</div>
              </div>
            </div>

            <div>
              <strong>服装描述</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>{character.clothing || '无'}</div>
              </div>
            </div>

            <div>
              <strong>AI 配置</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>
                  <strong>关键词:</strong> <Text code>{character.keywords}</Text>
                </div>
                <div>
                  <strong>提示词:</strong>
                </div>
                <div
                  style={{
                    background: '#f5f5f5',
                    padding: 8,
                    borderRadius: 4,
                    marginTop: 4,
                    maxHeight: 120,
                    overflow: 'auto'
                  }}
                >
                  <Text code style={{ whiteSpace: 'pre-wrap' }}>
                    {character.prompt}
                  </Text>
                </div>
              </div>
            </div>

            <div>
              <strong>状态信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>
                  <strong>可见性:</strong>{' '}
                  {character.isPublic ? (
                    <Tag color="green" icon={<GlobalOutlined />}>
                      公开
                    </Tag>
                  ) : (
                    <Tag color="orange" icon={<EyeInvisibleOutlined />}>
                      私有
                    </Tag>
                  )}
                </div>
                <div>
                  <strong>状态:</strong>{' '}
                  {character.isActive ? <Tag color="green">启用</Tag> : <Tag color="red">禁用</Tag>}
                </div>
                <div>
                  <strong>创建时间:</strong>{' '}
                  {dayjs(character.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </div>
                <div>
                  <strong>更新时间:</strong>{' '}
                  {dayjs(character.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                </div>
              </div>
            </div>
          </Space>
        </div>
      )
    })
  }

  const columns: ColumnsType<SystemCharacter> = [
    {
      title: '角色信息',
      key: 'characterInfo',
      render: (_, record) => (
        <Space>
          <Avatar size={48} src={record.imageUrl} icon={<UserOutlined />} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              {record.relationship && `${record.relationship} • `}
              {record.category || '未分类'}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              {record.description?.substring(0, 30)}
              {record.description?.length > 30 ? '...' : ''}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '外观特征',
      key: 'appearance',
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>
            {record.ethnicity} • {record.gender}
          </div>
          <div>
            {record.age && `${record.age} • `}
            {record.hairColor}
            {record.hairStyle}
          </div>
          <div>{record.bodyType}</div>
        </div>
      )
    },
    {
      title: '可见性',
      dataIndex: 'isPublic',
      render: isPublic => (
        <Tag
          color={isPublic ? 'green' : 'orange'}
          icon={isPublic ? <GlobalOutlined /> : <EyeInvisibleOutlined />}
        >
          {isPublic ? '公开' : '私有'}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: isActive => <Tag color={isActive ? 'green' : 'red'}>{isActive ? '启用' : '禁用'}</Tag>
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)} />
          </Tooltip>
          <Tooltip title="编辑">
            <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Popconfirm
            title="确定删除这个系统角色吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        <UserOutlined /> 系统角色管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总角色数"
              value={stats.total}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="启用角色" value={stats.active} valueStyle={{ color: '#1890ff' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="公开角色"
              value={stats.public}
              prefix={<GlobalOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="私有角色"
              value={stats.private}
              prefix={<EyeInvisibleOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索角色名称、描述或性格"
            style={{ width: 240 }}
            value={searchParams.keyword}
            onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="分类"
            style={{ width: 120 }}
            allowClear
            value={searchParams.category}
            onChange={value => setSearchParams({ ...searchParams, category: value })}
          >
            {categories.map(cat => (
              <Select.Option key={cat.name} value={cat.name}>
                {cat.name} ({cat.count})
              </Select.Option>
            ))}
          </Select>

          <Select
            placeholder="可见性"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isPublic}
            onChange={value => setSearchParams({ ...searchParams, isPublic: value })}
          >
            <Select.Option value={true}>公开</Select.Option>
            <Select.Option value={false}>私有</Select.Option>
          </Select>

          <Select
            placeholder="状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={value => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增角色
          </Button>
        </Space>
      </Card>

      {/* 角色列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={characters}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 创建/编辑角色模态框 */}
      <Modal
        title={editingCharacter ? '编辑系统角色' : '新增系统角色'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="例如：小雅" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="category" label="角色分类">
                <Select placeholder="选择分类">
                  <Select.Option value="女友">女友</Select.Option>
                  <Select.Option value="妻子">妻子</Select.Option>
                  <Select.Option value="朋友">朋友</Select.Option>
                  <Select.Option value="同事">同事</Select.Option>
                  <Select.Option value="老师">老师</Select.Option>
                  <Select.Option value="学生">学生</Select.Option>
                  <Select.Option value="其他">其他</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="角色描述">
            <TextArea rows={2} placeholder="简要描述这个角色..." />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="relationship" label="关系">
                <Input placeholder="例如：女友" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="ethnicity" label="种族">
                <Input placeholder="例如：亚洲人" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="gender" label="性别">
                <Select placeholder="选择性别">
                  <Select.Option value="female">女性</Select.Option>
                  <Select.Option value="male">男性</Select.Option>
                  <Select.Option value="other">其他</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="age" label="年龄">
                <Input placeholder="例如：20岁" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="eyeColor" label="眼色">
                <Input placeholder="例如：黑色" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="hairColor" label="发色">
                <Input placeholder="例如：黑色" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="hairStyle" label="发型">
                <Input placeholder="例如：长发" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="bodyType" label="体型">
                <Input placeholder="例如：苗条" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="imageUrl" label="头像URL">
                <Input placeholder="角色头像图片URL" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="personality" label="性格特征">
            <TextArea rows={3} placeholder="描述角色的性格特征..." />
          </Form.Item>

          <Form.Item name="clothing" label="服装描述">
            <TextArea rows={2} placeholder="描述角色的服装..." />
          </Form.Item>

          <Form.Item
            name="keywords"
            label="关键词"
            rules={[{ required: true, message: '请输入关键词' }]}
          >
            <Input placeholder="用于AI识别的关键词" />
          </Form.Item>

          <Form.Item
            name="prompt"
            label="AI提示词"
            rules={[{ required: true, message: '请输入AI提示词' }]}
          >
            <TextArea rows={4} placeholder="AI角色扮演的提示词..." />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="isPublic" label="可见性" valuePropName="checked">
                <Switch checkedChildren="公开" unCheckedChildren="私有" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="isActive" label="启用状态" valuePropName="checked">
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingCharacter ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CharacterManagement

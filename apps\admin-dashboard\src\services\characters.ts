import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

export interface SystemCharacter {
  id: string
  userId: string
  name: string
  description?: string
  relationship?: string
  ethnicity?: string
  gender?: 'male' | 'female' | 'other'
  age?: string
  eyeColor?: string
  hairStyle?: string
  hairColor?: string
  bodyType?: string
  breastSize?: string
  buttSize?: string
  personality?: string
  clothing?: string
  voice?: string
  voiceModelId?: string
  keywords: string
  prompt: string
  imageUrl?: string
  category?: string
  isPublic: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateCharacterRequest {
  name: string
  description?: string
  relationship?: string
  ethnicity?: string
  gender?: 'male' | 'female' | 'other'
  age?: string
  eyeColor?: string
  hairStyle?: string
  hairColor?: string
  bodyType?: string
  breastSize?: string
  buttSize?: string
  personality?: string
  clothing?: string
  voice?: string
  voiceModelId?: string
  keywords: string
  prompt: string
  imageUrl?: string
  category?: string
  isPublic?: boolean
  isActive?: boolean
}

export interface CharacterCategory {
  name: string
  count: number
}

export class CharacterService {
  // 获取系统角色列表
  async getSystemCharacters(params: {
    page?: number
    pageSize?: number
    keyword?: string
    category?: string
    isActive?: boolean
    isPublic?: boolean
  }): Promise<ApiResponse<PaginatedResponse<SystemCharacter>>> {
    return await apiService.get<PaginatedResponse<SystemCharacter>>('/admin/characters', { params })
  }

  // 获取系统角色详情
  async getSystemCharacter(id: string): Promise<ApiResponse<SystemCharacter>> {
    return await apiService.get<SystemCharacter>(`/admin/characters/${id}`)
  }

  // 创建系统角色
  async createSystemCharacter(data: CreateCharacterRequest): Promise<ApiResponse<SystemCharacter>> {
    return await apiService.post<SystemCharacter>('/admin/characters', data)
  }

  // 更新系统角色
  async updateSystemCharacter(id: string, data: Partial<CreateCharacterRequest>): Promise<ApiResponse<SystemCharacter>> {
    return await apiService.put<SystemCharacter>(`/admin/characters/${id}`, data)
  }

  // 删除系统角色
  async deleteSystemCharacter(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/characters/${id}`)
  }

  // 获取角色分类列表
  async getCharacterCategories(): Promise<ApiResponse<CharacterCategory[]>> {
    return await apiService.get<CharacterCategory[]>('/admin/characters/categories/list')
  }
}

export const characterService = new CharacterService()
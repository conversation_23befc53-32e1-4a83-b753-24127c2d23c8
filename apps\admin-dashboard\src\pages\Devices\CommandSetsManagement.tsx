import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Badge
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  CodeOutlined,
  <PERSON>boltOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { DeviceCommandSet } from '@/services/device-command-sets'
import { commandSetService } from '@/services/device-command-sets'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

const CommandSetsManagement: React.FC = () => {
  const [commandSets, setCommandSets] = useState<DeviceCommandSet[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingCommandSet, setEditingCommandSet] = useState<DeviceCommandSet | null>(null)
  const [form] = Form.useForm()

  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    isActive: undefined as boolean | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0
  })

  useEffect(() => {
    loadCommandSets()
  }, [currentPage, pageSize, searchParams])

  const loadCommandSets = async () => {
    try {
      setLoading(true)

      console.log('🔍 开始获取指令集列表:', { currentPage, pageSize, searchParams })
      console.log('🔐 当前token:', localStorage.getItem('admin_token') ? '已设置' : '未设置')

      const response = await commandSetService.getCommandSets({
        page: currentPage,
        pageSize,
        ...searchParams
      })

      console.log('📡 API响应:', response)

      if (response.success && response.data) {
        console.log('📊 设置命令集数据:', response.data.data)
        setCommandSets(response.data.data)
        setTotal(response.data.total)

        // 计算统计数据
        const activeCount = response.data.data.filter(item => item.isActive).length
        setStats({
          total: response.data.total,
          active: activeCount,
          inactive: response.data.total - activeCount
        })
      } else {
        message.error(response.message || '获取指令集列表失败')
      }
    } catch (error) {
      console.error('❌ 获取指令集列表异常:', error)
      message.error('获取指令集列表失败')
    } finally {
      setLoading(false)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      isActive: undefined
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingCommandSet(null)
    form.resetFields()
    form.setFieldsValue({ isActive: true })
    setModalVisible(true)
  }

  const handleEdit = (commandSet: DeviceCommandSet) => {
    setEditingCommandSet(commandSet)
    form.setFieldsValue({
      name: commandSet.name,
      description: commandSet.description,
      command: commandSet.command,
      broadcast: commandSet.broadcast,
      isActive: commandSet.isActive
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingCommandSet) {
        const response = await commandSetService.updateCommandSet(editingCommandSet.id, values)
        if (response.success) {
          message.success('指令集更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await commandSetService.createCommandSet(values)
        if (response.success) {
          message.success('指令集创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }

      setModalVisible(false)
      loadCommandSets()
    } catch (error) {
      console.error('操作失败:', error)
      message.error(editingCommandSet ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await commandSetService.deleteCommandSet(id)
      if (response.success) {
        message.success('指令集删除成功')
        loadCommandSets()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await commandSetService.toggleStatus(id, isActive)
      if (response.success) {
        message.success(isActive ? '指令集已启用' : '指令集已禁用')
        loadCommandSets()
      } else {
        message.error(response.message || '状态切换失败')
      }
    } catch (error) {
      console.error('状态切换失败:', error)
      message.error('状态切换失败')
    }
  }

  const handleViewDetail = (commandSet: DeviceCommandSet) => {
    Modal.info({
      title: '指令集详情',
      width: 700,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="middle">
            <div>
              <strong>基本信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>
                  <strong>名称:</strong> {commandSet.name}
                </div>
                <div>
                  <strong>描述:</strong> {commandSet.description || '无'}
                </div>
                <div>
                  <strong>状态:</strong>{' '}
                  {commandSet.isActive ? (
                    <Tag color="green">启用</Tag>
                  ) : (
                    <Tag color="red">禁用</Tag>
                  )}
                </div>
              </div>
            </div>

            <div>
              <strong>指令信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>
                  <strong>控制指令:</strong>
                </div>
                <div
                  style={{
                    backgroundColor: '#f5f5f5',
                    padding: '8px 12px',
                    borderRadius: '4px',
                    fontFamily: 'monospace',
                    marginTop: 4
                  }}
                >
                  {commandSet.command}
                </div>
                {commandSet.broadcast && (
                  <>
                    <div style={{ marginTop: 8 }}>
                      <strong>广播指令:</strong>
                    </div>
                    <div
                      style={{
                        backgroundColor: '#f5f5f5',
                        padding: '8px 12px',
                        borderRadius: '4px',
                        fontFamily: 'monospace',
                        marginTop: 4
                      }}
                    >
                      {commandSet.broadcast}
                    </div>
                  </>
                )}
              </div>
            </div>

            <div>
              <strong>时间信息</strong>
              <div style={{ marginTop: 8, marginLeft: 16 }}>
                <div>
                  <strong>创建时间:</strong>{' '}
                  {dayjs(commandSet.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </div>
                <div>
                  <strong>更新时间:</strong>{' '}
                  {dayjs(commandSet.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                </div>
              </div>
            </div>
          </Space>
        </div>
      )
    })
  }

  const columns: ColumnsType<DeviceCommandSet> = [
    {
      title: '指令集信息',
      key: 'commandSetInfo',
      render: (_, record) => (
        <Space>
          <CodeOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>{record.description || '无描述'}</div>
          </div>
        </Space>
      )
    },
    {
      title: '控制指令',
      dataIndex: 'command',
      render: command => (
        <Text
          code
          copyable
          style={{
            maxWidth: '200px',
            display: 'block',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
          }}
        >
          {command}
        </Text>
      )
    },
    {
      title: '广播指令',
      dataIndex: 'broadcast',
      render: broadcast =>
        broadcast ? (
          <Text
            code
            copyable
            style={{
              maxWidth: '200px',
              display: 'block',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
            }}
          >
            {broadcast}
          </Text>
        ) : (
          <Text type="secondary">无</Text>
        )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: (isActive, record) => (
        <Switch
          checked={isActive}
          onChange={checked => handleToggleStatus(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)} />
          </Tooltip>
          <Tooltip title="编辑">
            <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Popconfirm
            title="确定删除这个指令集吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        <CodeOutlined /> 指令集管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总指令集数"
              value={stats.total}
              prefix={<CodeOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="启用指令集"
              value={stats.active}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="禁用指令集"
              value={stats.inactive}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索指令集名称或描述"
            style={{ width: 240 }}
            value={searchParams.keyword}
            onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={value => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增指令集
          </Button>
        </Space>
      </Card>

      {/* 指令集列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={commandSets}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 创建/编辑指令集模态框 */}
      <Modal
        title={editingCommandSet ? '编辑指令集' : '新增指令集'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="指令集名称"
            rules={[{ required: true, message: '请输入指令集名称' }]}
          >
            <Input placeholder="例如：震动指令1档" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={2} placeholder="描述这个指令集的用途和特点..." />
          </Form.Item>

          <Form.Item
            name="command"
            label="控制指令"
            rules={[{ required: true, message: '请输入控制指令' }]}
          >
            <TextArea
              rows={3}
              placeholder="输入蓝牙控制指令，例如：AT+VIBRATE=1"
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>

          <Form.Item name="broadcast" label="广播指令（可选）">
            <TextArea
              rows={2}
              placeholder="输入广播指令（如果需要）"
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>

          <Form.Item name="isActive" label="启用状态" valuePropName="checked" initialValue={true}>
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingCommandSet ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CommandSetsManagement

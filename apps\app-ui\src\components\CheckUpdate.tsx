import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  <PERSON><PERSON>,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  useDisclosure,
  Progress,
  Card,
  CardBody,
  addToast
} from '@heroui/react'
import { Icon } from '@iconify/react'
import { useAppUpdate } from '@/hooks/use-app-update'
import { UpdateDialog } from '@/components/update-dialog'

interface CheckUpdateProps {
  trigger?: React.ReactNode
}

export default function CheckUpdate({ trigger }: CheckUpdateProps) {
  const { t } = useTranslation()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [isManualCheck, setIsManualCheck] = useState(false)

  const {
    isChecking,
    updateInfo,
    updateProgress,
    error,
    checkForUpdates,
    performUpdate,
    clearError,
    clearUpdateInfo
  } = useAppUpdate({
    autoCheck: false, // 手动检查时不自动检查
    onUpdateAvailable: (updateInfo) => {
      console.log('发现更新:', updateInfo)
    },
    onUpdateComplete: () => {
      addToast({
        title: t('update.updateComplete'),
        color: 'success'
      })
    },
    onUpdateError: (error) => {
      addToast({
        title: t('update.updateFailed'),
        description: error.message,
        color: 'danger'
      })
    }
  })

  const handleCheckUpdate = async () => {
    setIsManualCheck(true)
    try {
      const result = await checkForUpdates(true) // 强制检查
      if (!result) {
        addToast({
          title: t('update.noUpdate'),
          color: 'success'
        })
      }
    } catch (error) {
      addToast({
        title: t('update.checkFailed'),
        description: error instanceof Error ? error.message : '未知错误',
        color: 'danger'
      })
    } finally {
      setIsManualCheck(false)
    }
  }

  const handleStartUpdate = async () => {
    if (!updateInfo) return

    try {
      await performUpdate(updateInfo)
    } catch (error) {
      console.error('更新失败:', error)
    }
  }

  const handleCloseModal = () => {
    clearError()
    onClose()
  }

  const defaultTrigger = (
    <div
      className='flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg'
      onClick={onOpen}
      style={{
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        userSelect: 'none',
        touchAction: 'manipulation'
      }}
    >
      <div className='flex items-center space-x-4'>
        <div className='w-6 h-6 flex items-center justify-center'>
          <Icon icon='lucide:refresh-cw' className='w-5 h-5 text-white/80' />
        </div>
        <span className='text-white font-medium text-lg'>{t('update.checkUpdate')}</span>
      </div>
      <Icon icon='lucide:chevron-right' className='w-5 h-5 text-white/60' />
    </div>
  )

  const renderModalContent = () => {
    if (updateProgress) {
      // 显示更新进度
      return (
        <>
          <ModalHeader className='flex flex-col gap-1 px-6 py-4'>
            <div className='flex items-center gap-3'>
              <Icon icon='lucide:download' className='w-5 h-5 text-primary' />
              <span className='text-lg font-semibold'>{t('update.downloading')}</span>
            </div>
          </ModalHeader>
          <ModalBody className='px-6 pb-6'>
            <div className='text-center space-y-4'>
              <p className='text-default-600'>{updateProgress.message}</p>
              <Progress
                value={updateProgress.progress}
                className='w-full'
                color='primary'
                showValueLabel
              />
              <p className='text-sm text-default-500'>
                {Math.round(updateProgress.progress)}% {t('update.downloadProgress')}
              </p>
            </div>
          </ModalBody>
        </>
      )
    }

    if (updateInfo) {
      // 显示更新信息
      const primaryUpdate = updateInfo.hasApkUpdate ? updateInfo.apkUpdate : updateInfo.hotfixUpdate

      return (
        <>
          <ModalHeader className='flex flex-col gap-1 px-6 py-4'>
            <div className='flex items-center gap-3'>
              <Icon icon='lucide:download' className='w-5 h-5 text-primary' />
              <span className='text-lg font-semibold'>{t('update.hasUpdate')}</span>
            </div>
          </ModalHeader>
          <ModalBody className='px-6 pb-6'>
            <div className='space-y-4'>
              {primaryUpdate && (
                <Card>
                  <CardBody className='space-y-3'>
                    <div className='flex justify-between'>
                      <span className='text-default-600'>{t('update.latestVersion')}:</span>
                      <span className='font-medium'>{primaryUpdate.version.versionName}</span>
                    </div>
                    {primaryUpdate.version.fileSize && (
                      <div className='flex justify-between'>
                        <span className='text-default-600'>{t('update.fileSize')}:</span>
                        <span className='font-medium'>
                          {(primaryUpdate.version.fileSize / 1024 / 1024).toFixed(1)} MB
                        </span>
                      </div>
                    )}
                    {primaryUpdate.version.releaseNotes && (
                      <div>
                        <div className='text-default-600 mb-2'>{t('update.releaseNotes')}:</div>
                        <div className='text-sm text-default-500 bg-default-50 p-3 rounded-lg'>
                          {primaryUpdate.version.releaseNotes}
                        </div>
                      </div>
                    )}
                  </CardBody>
                </Card>
              )}
            </div>
          </ModalBody>
          <ModalFooter>
            <div className='flex gap-2 w-full'>
              <Button variant='light' onPress={handleCloseModal} className='flex-1'>
                {t('update.laterRemind')}
              </Button>
              <Button color='primary' onPress={handleStartUpdate} className='flex-1'>
                {t('update.updateNow')}
              </Button>
            </div>
          </ModalFooter>
        </>
      )
    }

    // 默认检查更新界面
    return (
      <>
        <ModalHeader className='flex flex-col gap-1 px-6 py-4'>
          <div className='flex items-center gap-3'>
            <Icon icon='lucide:refresh-cw' className='w-5 h-5 text-primary' />
            <span className='text-lg font-semibold'>{t('update.checkUpdate')}</span>
          </div>
        </ModalHeader>
        <ModalBody className='px-6 pb-6'>
          <div className='text-center space-y-4'>
            {isChecking || isManualCheck ? (
              <>
                <Icon
                  icon='lucide:loader-2'
                  className='w-8 h-8 text-primary animate-spin mx-auto'
                />
                <p className='text-default-600'>{t('update.checking')}</p>
              </>
            ) : error ? (
              <>
                <Icon icon='lucide:alert-circle' className='w-8 h-8 text-danger mx-auto' />
                <p className='text-danger'>{t('update.checkFailed')}</p>
                <p className='text-sm text-default-500'>{error.message}</p>
              </>
            ) : (
              <>
                <Icon icon='lucide:check-circle' className='w-8 h-8 text-success mx-auto' />
                <p className='text-success'>{t('update.noUpdate')}</p>
              </>
            )}
          </div>
        </ModalBody>
        <ModalFooter>
          <div className='flex gap-2 w-full'>
            <Button variant='light' onPress={handleCloseModal} className='flex-1'>
              {t('common.cancel')}
            </Button>
            <Button
              color='primary'
              onPress={handleCheckUpdate}
              className='flex-1'
              isDisabled={isChecking || isManualCheck}
            >
              {isChecking || isManualCheck ? t('update.checking') : t('update.checkUpdate')}
            </Button>
          </div>
        </ModalFooter>
      </>
    )
  }

  return (
    <>
      {trigger ? <div onClick={onOpen}>{trigger}</div> : defaultTrigger}

      <Modal
        isOpen={isOpen}
        onClose={handleCloseModal}
        placement='center'
        backdrop='blur'
        size='md'
        isDismissable={!updateProgress}
      >
        <ModalContent>{renderModalContent()}</ModalContent>
      </Modal>

      {/* 更新对话框 */}
      <UpdateDialog
        isOpen={!!updateInfo && !isOpen}
        updateInfo={updateInfo}
        updateProgress={updateProgress}
        isForced={false}
        onUpdate={handleStartUpdate}
        onCancel={() => clearUpdateInfo()}
        onDismiss={() => clearUpdateInfo()}
      />
    </>
  )
}

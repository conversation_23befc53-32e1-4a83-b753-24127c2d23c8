import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  Button,
  useDisclosure,
  Divider
} from '@heroui/react'
import { Icon } from '@iconify/react'
import { languages } from '@/i18n'

interface LanguageSwitcherProps {
  trigger?: React.ReactNode
}

export default function LanguageSwitcher({ trigger }: LanguageSwitcherProps) {
  const { i18n, t } = useTranslation()
  const { isOpen, onOpen, onClose } = useDisclosure()
  const [isChanging, setIsChanging] = useState(false)

  const currentLanguage = languages.find((lang) => lang.code === i18n.language) || languages[0]

  const handleLanguageChange = async (languageCode: string) => {
    if (languageCode === i18n.language) {
      onClose()
      return
    }

    setIsChanging(true)
    try {
      await i18n.changeLanguage(languageCode)
      // 可以在这里添加成功提示
      console.log(`语言已切换到: ${languageCode}`)
    } catch (error) {
      console.error('切换语言失败:', error)
    } finally {
      setIsChanging(false)
      onClose()
    }
  }

  const defaultTrigger = (
    <div
      className='flex items-center justify-between cursor-pointer hover:bg-white/5 transition-colors py-2 px-2 rounded-lg'
      onClick={onOpen}
      style={{
        WebkitTouchCallout: 'none',
        WebkitUserSelect: 'none',
        userSelect: 'none',
        touchAction: 'manipulation'
      }}
    >
      <div className='flex items-center space-x-4'>
        <div className='w-6 h-6 flex items-center justify-center'>
          <Icon icon='lucide:globe' className='w-5 h-5 text-white/80' />
        </div>
        <span className='text-white font-medium text-lg'>{t('settings.language')}</span>
      </div>
      <div className='flex items-center gap-2'>
        <span className='text-white/60 text-sm'>{currentLanguage.nativeName}</span>
        <Icon icon='lucide:chevron-right' className='w-5 h-5 text-white/60' />
      </div>
    </div>
  )

  return (
    <>
      {trigger ? <div onClick={onOpen}>{trigger}</div> : defaultTrigger}

      <Modal
        isOpen={isOpen}
        onClose={onClose}
        placement='bottom'
        backdrop='blur'
        scrollBehavior='inside'
        classNames={{
          base: 'mb-0 sm:mb-0 bg-background',
          wrapper: 'items-end sm:items-end z-[1000]',
          body: 'py-0 bg-background/80 overflow-hidden',
          header: 'border-b border-divider',
          backdrop: 'z-[999]'
        }}
        style={{
          zIndex: 1000
        }}
      >
        <ModalContent>
          <ModalHeader className='flex flex-col gap-1 px-6 py-4'>
            <div className='flex items-center gap-3'>
              <Icon icon='lucide:globe' className='w-5 h-5 text-primary' />
              <span className='text-lg font-semibold'>{t('settings.checkUpdate')}</span>
            </div>
          </ModalHeader>
          <ModalBody className='px-0 pb-6'>
            <div className='space-y-0'>
              {languages.map((language, index) => (
                <div key={language.code}>
                  <Button
                    variant='light'
                    className={`w-full justify-start h-auto py-4 px-6 rounded-none ${
                      language.code === i18n.language
                        ? 'bg-primary/10 text-primary'
                        : 'text-foreground hover:bg-default-100'
                    }`}
                    onPress={() => handleLanguageChange(language.code)}
                    isDisabled={isChanging}
                  >
                    <div className='flex items-center justify-between w-full'>
                      <div className='flex items-center space-x-4'>
                        <div className='text-left'>
                          <div className='font-medium'>{language.nativeName}</div>
                          <div className='text-sm text-default-500'>{language.name}</div>
                        </div>
                      </div>
                      {language.code === i18n.language && (
                        <Icon icon='lucide:check' className='w-5 h-5 text-primary' />
                      )}
                    </div>
                  </Button>
                  {index < languages.length - 1 && <Divider className='mx-6' />}
                </div>
              ))}
            </div>

            {/* 取消按钮 */}
            <div className='px-6 pt-4'>
              <Button variant='light' className='w-full' onPress={onClose}>
                {t('common.cancel')}
              </Button>
            </div>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  )
}

import { useEffect, useState } from 'react'
import { privacyScreenService } from '@/lib/privacy-screen'
import { Capacitor } from '@capacitor/core'

/**
 * 隐私屏幕状态显示组件
 * 仅用于开发调试，显示隐私屏幕的启用状态
 */
export function PrivacyScreenStatus() {
  const [isEnabled, setIsEnabled] = useState(false)
  const [isNative, setIsNative] = useState(false)

  useEffect(() => {
    // 检查是否为原生平台
    setIsNative(Capacitor.isNativePlatform())

    // 检查隐私屏幕状态
    const checkStatus = async () => {
      try {
        const enabled = await privacyScreenService.checkStatus()
        setIsEnabled(enabled)
      } catch (error) {
        console.error('检查隐私屏幕状态失败:', error)
      }
    }

    checkStatus()
  }, [])

  return null
}

# 图片生成队列迁移部署指南

## 🚀 部署步骤

### 1. 创建 Cloudflare 队列

#### 生产环境队列
```bash
# 创建图片生成队列
pnpm wrangler queues create image-generation-queue

# 验证队列创建成功
pnpm wrangler queues list
```

#### 开发环境队列
```bash
# 创建开发环境图片生成队列
pnpm wrangler queues create image-generation-queue-dev

# 验证队列创建成功
pnpm wrangler queues list
```

### 2. 验证环境变量

确保以下环境变量已配置：

```bash
# 检查 Insa3D 配置
pnpm wrangler secret list

# 应该包含：
# - INSA3D_API_TOKEN
# - INSA3D_API_ENDPOINT
```

如果缺少，需要设置：
```bash
pnpm wrangler secret put INSA3D_API_TOKEN
pnpm wrangler secret put INSA3D_API_ENDPOINT
```

### 3. 部署服务

```bash
# 部署到开发环境
pnpm deploy:dev

# 部署到生产环境
pnpm deploy
```

### 4. 验证部署

#### 检查队列绑定
```bash
# 查看 Worker 绑定
pnpm wrangler deployments list

# 检查队列消费者状态
pnpm wrangler queues consumer list image-generation-queue
pnpm wrangler queues consumer list image-generation-queue-dev
```

#### 健康检查
```bash
# 测试新 API 健康状态
curl https://your-worker-domain.workers.dev/api/image-generation-v2/health

# 预期响应：
# {
#   "success": true,
#   "data": {
#     "service": "image-generation-v2",
#     "status": "healthy",
#     "insa3dApi": {
#       "endpoint": "your-endpoint",
#       "configured": true
#     },
#     "queue": {
#       "bound": true,
#       "name": "IMAGE_GENERATION_QUEUE"
#     }
#   }
# }
```

## 🔄 迁移策略

### 渐进式切换

1. **阶段1：双轨运行**
   - 新 API v2 和 Edge Function 同时可用
   - 前端优先尝试 API v2，失败时降级到 Edge Function
   - 监控两个服务的成功率

2. **阶段2：逐步切换**
   - 通过功能开关控制切换比例
   - 新用户优先使用 API v2
   - 老用户逐步迁移

3. **阶段3：完全迁移**
   - 所有请求使用 API v2
   - 保留 Edge Function 作为紧急降级方案

### 回滚计划

如果发现问题需要回滚：

1. **前端回滚**：修改前端代码，跳过 API v2，直接使用 Edge Function
2. **队列暂停**：暂停图片生成队列消费者
3. **紧急修复**：修复问题后重新部署

## 📊 监控指标

### 队列监控
- 队列深度（待处理任务数）
- 消费速率（每分钟处理任务数）
- 错误率（失败任务百分比）
- 平均处理时间

### API 监控
- API v2 成功率
- API v2 响应时间
- 降级到 Edge Function 的频率
- 用户满意度（图片生成成功率）

## 🔧 故障排除

### 常见问题

1. **队列未绑定**
   ```bash
   # 检查 wrangler.toml 配置
   # 确保队列绑定正确
   ```

2. **权限错误**
   ```bash
   # 检查 Insa3D API 配置
   pnpm wrangler secret list
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库 URL 配置
   # 确保 Supabase 连接正常
   ```

### 调试命令

```bash
# 查看队列状态
pnpm wrangler queues consumer list image-generation-queue

# 查看 Worker 日志
pnpm wrangler tail

# 测试队列发送
pnpm wrangler queues producer send image-generation-queue '{"test": true}'
```

## ✅ 部署检查清单

- [ ] Cloudflare 队列已创建
- [ ] 环境变量已配置
- [ ] Worker 已部署
- [ ] 队列绑定正常
- [ ] 健康检查通过
- [ ] 前端降级机制工作正常
- [ ] 监控告警已配置
- [ ] 回滚计划已准备
import { memo, useEffect, useState, useRef } from 'react'
import { useTranslation } from 'react-i18next'
import { useMultimodalVideoGeneration } from '@/hooks/use-multimodal-video-generation'
import { MultimodalVideoPlaceholder } from './multimodal-video-placeholder'
import { multimodalVideoService } from '@/api/services/multimodal-video'
import { useRoleStore } from '@/stores/role-store'
import { VideoStorage } from '@/lib/media/video-storage'
import { VideoDisplay } from './video-display'

interface MultimodalVideoGenerationProps {
  messageId: string
  chatId: string
  prompt: string
  characterAvatar?: string
  onVideoGenerated?: (videoUrl: string) => void
  onError?: (error: string) => void
  existingAttachments?: Array<{
    url: string
    name?: string
    contentType?: string
    metadata?: {
      taskId?: string
      generatedAt?: string
    }
  }>
}

export const MultimodalVideoGeneration = memo(
  ({
    messageId,
    chatId,
    prompt,
    characterAvatar,
    onVideoGenerated,
    onError,
    existingAttachments
  }: MultimodalVideoGenerationProps) => {
    const { t } = useTranslation('chat-v2')
    const { currentRole } = useRoleStore()
    const [hasStarted, setHasStarted] = useState(false)
    const [maxProgress, setMaxProgress] = useState(0) // 记录最高进度，防止回退

    const {
      // 状态
      overallProgress,
      status,
      intermediateImageUrl,
      finalVideoUrl,
      error,
      isActive,
      hasIntermediateImage,
      hasFinalVideo,

      // 操作
      generateVideo,
      updateProgress,
      getCurrentStageDescription,
      reset
    } = useMultimodalVideoGeneration({
      onSuccess: async videoUrl => {
        console.log(`✅ [SUCCESS] ${t('video.generation_complete')}:`, videoUrl)

        // 保存到本地存储（VideoStorage内部会处理数据库保存）
        if (messageId && videoUrl) {
          try {
            const videoStorage = VideoStorage.getInstance()

            // 只需要下载并保存视频，VideoStorage会自动处理数据库记录
            await videoStorage.saveVideoFromUrl(videoUrl, messageId, 'generated')
            console.log(`✅ [VideoGeneration] ${t('video.localization_complete')}`)
          } catch (storageError) {
            console.warn(`⚠️ [VideoGeneration] ${t('video.localization_failed')}:`, storageError)
          }
        }

        onVideoGenerated?.(videoUrl)
      },
      onError: errorMsg => {
        console.error(`❌ [ERROR] ${t('video.generation_failed')}:`, errorMsg)
        onError?.(errorMsg)
      },
      onProgress: (progress, stage, currentStatus) => {
        console.log(`📊 [PROGRESS] ${t('video.generation_progress')}:`, {
          progress,
          stage,
          status: currentStatus
        })
      },
      onImageGenerated: imageUrl => {
        console.log(`🎨 [IMAGE] ${t('image.generation_complete')}:`, imageUrl)
      }
    })

    // 使用 ref 来避免 updateProgress 依赖导致的重复轮询
    const updateProgressRef = useRef(updateProgress)
    updateProgressRef.current = updateProgress

    // 直接检查现有附件，防止重复生成（最高优先级）
    useEffect(() => {
      console.log(`🔍 [INIT] ${t('video.checking_attachments')}:`, {
        messageId,
        existingAttachments
      })

      // 检查是否已有完成的视频附件
      const completedVideoAttachment = existingAttachments?.find(
        att => att.contentType === 'video/mp4' || att.contentType === 'video/webm'
      )

      if (completedVideoAttachment) {
        console.log(`🔍 [INIT] ${t('video.found_completed_video')}:`, completedVideoAttachment.url)
        setHasStarted(true)
        updateProgressRef.current({
          status: 'completed',
          overallProgress: 100,
          currentStage: 'video_generation',
          finalVideoUrl: completedVideoAttachment.url,
          message: t('video.generation_complete')
        })
        return
      }

      // 检查是否有生成中的状态附件
      const generatingAttachment = existingAttachments?.find(
        att => att.contentType?.includes('generating') && att.contentType?.includes('video')
      )

      if (generatingAttachment) {
        console.log(`🔍 [INIT] ${t('video.found_generating_status')}`)
        setHasStarted(true)

        // 设置生成中状态，触发轮询
        const metadata = (generatingAttachment.metadata as any) || {}
        updateProgressRef.current({
          status: 'processing',
          overallProgress: metadata.progress || 0,
          currentStage: metadata.stage || 'image_generation',
          message: generatingAttachment.name || t('video.generating'),
          intermediateImageUrl: metadata.intermediateImageUrl
        })
        return
      }

      console.log(`🔍 [INIT] ${t('video.no_existing_status')}`)
    }, [messageId, existingAttachments])

    // 自动开始生成
    useEffect(() => {
      if (!hasStarted && prompt && !isActive) {
        console.log(`🚀 [AUTO_START] ${t('video.auto_start_generation')}`)
        setHasStarted(true)

        generateVideo({
          messageId,
          chatId,
          prompt,
          characterAvatar,
          characterId: currentRole?.id, // 添加角色 ID
          metadata: {
            width: 720,
            height: 1280,
            duration: 3, // 3秒视频
            fps: 24
          }
        })
      }
    }, [hasStarted, prompt, isActive, generateVideo, messageId, chatId, characterAvatar])

    // 轮询状态更新
    useEffect(() => {
      console.log(`🔄 [POLLING] ${t('video.polling_condition_check')}:`, {
        messageId,
        isActive,
        status,
        shouldPoll: !!(messageId && isActive)
      })

      if (!messageId || !isActive) return

      console.log(`🔄 [POLLING] ${t('video.start_polling')}:`, messageId)

      const pollStatus = async () => {
        try {
          const result = await multimodalVideoService.getGenerationStatus(messageId)

          if (result.success && result.data) {
            const data = result.data as any

            // 进度平滑处理：确保进度只增不减
            const newProgress = data.progress || 0
            const smoothedProgress = Math.max(newProgress, maxProgress)

            console.log(`📊 [POLLING] ${t('video.status_update')}:`, {
              status: data.status,
              rawProgress: newProgress,
              smoothedProgress,
              stage: data.stage
            })

            // 更新最高进度
            if (smoothedProgress > maxProgress) {
              setMaxProgress(smoothedProgress)
            }

            updateProgressRef.current({
              status: data.status,
              overallProgress: smoothedProgress,
              currentStage: data.stage,
              message: data.message,
              ...(data.intermediateImageUrl && { intermediateImageUrl: data.intermediateImageUrl }),
              ...(data.finalVideoUrl && { finalVideoUrl: data.finalVideoUrl }),
              ...(data.error && { error: data.error })
            })

            // 如果完成或失败，停止轮询
            if (data.status === 'completed' || data.status === 'failed') {
              console.log(`✅ [POLLING] ${t('video.generation_complete_stop_polling')}`)
              clearInterval(intervalId)
            }
          }
        } catch (error) {
          console.error(`❌ [POLLING] ${t('video.polling_failed')}:`, error)
        }
      }

      // 立即查询一次
      pollStatus()

      // 每3秒轮询一次
      const intervalId = setInterval(pollStatus, 3000)

      // 10分钟超时
      const timeoutId = setTimeout(() => {
        console.warn(`⚠️ [POLLING] ${t('video.polling_timeout')}`)
        clearInterval(intervalId)
        updateProgressRef.current({
          status: 'failed',
          error: t('video.generation_timeout')
        })
      }, 600000) // 10分钟

      return () => {
        console.log(`🔄 [POLLING] ${t('video.stop_polling')}:`, messageId)
        clearInterval(intervalId)
        clearTimeout(timeoutId)
      }
    }, [messageId, isActive])

    // 错误处理
    if (error) {
      return (
        <div className="w-full aspect-video bg-red-50 border border-red-200 rounded-lg overflow-hidden">
          <div className="h-full flex flex-col items-center justify-center p-6 text-center">
            <div className="w-16 h-16 rounded-full bg-red-100 flex items-center justify-center mb-4">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-red-900 mb-2">
              {t('video.generation_failed')}
            </h3>
            <p className="text-red-700 text-sm mb-4">{error}</p>
            <button
              onClick={reset}
              className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              {t('video.retry')}
            </button>
          </div>
        </div>
      )
    }

    // 显示最终视频
    if (hasFinalVideo && finalVideoUrl) {
      return <VideoDisplay videoUrl={finalVideoUrl} title={t('video.ai_generated_video')} />
    }

    // 显示生成中的状态
    return (
      <MultimodalVideoPlaceholder
        progress={overallProgress}
        status={status}
        message={getCurrentStageDescription()}
        intermediateImageUrl={intermediateImageUrl}
        showBlurredPreview={hasIntermediateImage && overallProgress >= 30}
        onRetry={reset}
      />
    )
  }
)

MultimodalVideoGeneration.displayName = 'MultimodalVideoGeneration'

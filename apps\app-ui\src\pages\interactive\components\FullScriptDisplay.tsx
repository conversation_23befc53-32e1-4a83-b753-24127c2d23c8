import React, { useEffect, useState } from 'react'
import { Card, CardBody, Button, Chip, Divider, ScrollShadow } from '@heroui/react'
import { Icon } from '@iconify/react'
import { usePlayer } from '../context/PlayerContext'
import type { Dialogue } from '../types'
import { timeToSeconds } from '../utils/timeUtils'

interface FullScriptDisplayProps {
  showControls?: boolean // 是否显示切换按钮
}

/**
 * 完整剧本显示组件
 * 显示所有阶段和对白，当前对白高亮显示，其他模糊
 */
export const FullScriptDisplay: React.FC<FullScriptDisplayProps> = ({ showControls = true }) => {
  const { script, currentTime, currentStageIndex, setStage, seek } = usePlayer()

  // 存储当前活跃的对白信息
  const [activeDialogue, setActiveDialogue] = useState<{
    stageIndex: number
    dialogueIndex: number
    dialogue: Dialogue
  } | null>(null)

  // 存储展开的阶段
  const [expandedStages, setExpandedStages] = useState<number[]>([])

  // 存储用户手动折叠的阶段，即使其中有活跃对话
  const [userCollapsedStages, setUserCollapsedStages] = useState<number[]>([])

  // 是否处于完整显示模式
  const [isFullView, setIsFullView] = useState(false)

  // 计算活跃对白
  useEffect(() => {
    // 先检查当前阶段
    if (script && script.length > 0) {
      let foundActive = false

      // 查找当前阶段中的当前对白
      if (currentStageIndex < script.length) {
        const currentStage = script[currentStageIndex]
        if (currentStage?.dialogues) {
          // 找出时间小于等于当前时间的最后一个对白
          for (let i = currentStage.dialogues.length - 1; i >= 0; i--) {
            const dialogue = currentStage.dialogues[i]
            const dialogueTime = timeToSeconds(dialogue.time)

            if (dialogueTime <= currentTime) {
              setActiveDialogue({
                stageIndex: currentStageIndex,
                dialogueIndex: i,
                dialogue: dialogue
              })
              foundActive = true

              // 确保当前阶段是展开的，除非用户明确选择了折叠
              if (
                !expandedStages.includes(currentStageIndex) &&
                !userCollapsedStages.includes(currentStageIndex)
              ) {
                setExpandedStages(prev => [...prev, currentStageIndex])
              }

              break
            }
          }
        }
      }

      // 如果当前阶段没找到，遍历所有阶段寻找最近的对白
      if (!foundActive) {
        let closestTime = Number.NEGATIVE_INFINITY
        let closestStageIndex = -1
        let closestDialogueIndex = -1
        let closestDialogue: Dialogue | null = null

        for (let i = 0; i < script.length; i++) {
          const stage = script[i]
          if (stage.dialogues) {
            for (let j = 0; j < stage.dialogues.length; j++) {
              const dialogue = stage.dialogues[j]
              const dialogueTime = timeToSeconds(dialogue.time)

              if (dialogueTime <= currentTime && dialogueTime > closestTime) {
                closestTime = dialogueTime
                closestStageIndex = i
                closestDialogueIndex = j
                closestDialogue = dialogue
              }
            }
          }
        }

        if (closestDialogue) {
          setActiveDialogue({
            stageIndex: closestStageIndex,
            dialogueIndex: closestDialogueIndex,
            dialogue: closestDialogue
          })

          // 确保相关阶段是展开的，除非用户明确选择了折叠
          if (
            !expandedStages.includes(closestStageIndex) &&
            !userCollapsedStages.includes(closestStageIndex)
          ) {
            setExpandedStages(prev => [...prev, closestStageIndex])
          }
        }
      }
    }
  }, [script, currentStageIndex, currentTime, expandedStages, userCollapsedStages])

  // 切换阶段展开/折叠
  const toggleStage = (stageIndex: number) => {
    if (expandedStages.includes(stageIndex)) {
      // 用户要折叠
      setExpandedStages(expandedStages.filter(i => i !== stageIndex))

      // 如果是当前活跃阶段，记录用户折叠请求
      if (activeDialogue && activeDialogue.stageIndex === stageIndex) {
        if (!userCollapsedStages.includes(stageIndex)) {
          setUserCollapsedStages([...userCollapsedStages, stageIndex])
        }
      }
    } else {
      // 用户要展开
      setExpandedStages([...expandedStages, stageIndex])

      // 移除用户折叠标记
      if (userCollapsedStages.includes(stageIndex)) {
        setUserCollapsedStages(userCollapsedStages.filter(i => i !== stageIndex))
      }
    }
  }

  // 重置用户折叠状态
  const resetUserCollapsedStages = () => {
    setUserCollapsedStages([])
  }

  // 处理阶段切换
  const handleStageSwitch = (stageIndex: number) => {
    // 清除用户折叠状态
    resetUserCollapsedStages()

    // 切换阶段
    setStage(stageIndex)
  }

  // 点击对白，跳转到该对白
  const handleDialogueClick = (stageIndex: number, dialogue: Dialogue) => {
    // 设置阶段
    if (stageIndex !== currentStageIndex) {
      setStage(stageIndex)
    }

    // 跳转到对白时间点
    const dialogueTime = timeToSeconds(dialogue.time)
    seek(dialogueTime)
  }

  // 切换完整/简洁视图
  const toggleView = () => {
    setIsFullView(!isFullView)

    // 如果切换到完整视图，展开所有阶段并清除用户折叠状态
    if (!isFullView) {
      const allStages = Array.from({ length: script.length }, (_, i) => i)
      setExpandedStages(allStages)
      resetUserCollapsedStages()
    } else {
      // 切换到简洁视图，只展开当前阶段
      setExpandedStages(activeDialogue ? [activeDialogue.stageIndex] : [])
      resetUserCollapsedStages()
    }
  }

  // 如果没有剧本或剧本为空，不显示
  if (!script || script.length === 0) {
    return null
  }

  return (
    <Card className="w-full max-w-3xl mx-auto bg-black/80 backdrop-blur-md border-gray-800">
      {/* 控制头部 */}
      {showControls && (
        <CardBody className="p-0">
          <div className="flex items-center justify-between px-4 py-3 bg-gray-900/80 border-b border-gray-800">
            <h3 className="text-white font-bold flex items-center gap-2">
              <Icon icon="solar:document-text-bold" className="text-primary" width={20} />
              剧本对白
            </h3>
            <Button
              size="sm"
              variant="flat"
              color="primary"
              onPress={toggleView}
              startContent={
                <Icon icon={isFullView ? 'solar:eye-closed-linear' : 'solar:eye-bold'} width={16} />
              }
            >
              {isFullView ? '简洁视图' : '完整视图'}
            </Button>
          </div>
        </CardBody>
      )}

      {/* 剧本内容 */}
      <CardBody className="p-2">
        <ScrollShadow className="max-h-[60vh]" hideScrollBar>
          <div className="space-y-2">
            {script.map((stage, stageIndex) => (
              <Card
                key={`stage-${stage.stage}-${stageIndex}`}
                className={`transition-all ${
                  stageIndex === currentStageIndex
                    ? 'border-primary/50 bg-primary/10'
                    : 'border-gray-800/50 bg-gray-900/30'
                }`}
              >
                {/* 阶段标题 */}
                <CardBody className="p-0">
                  <div
                    className="flex items-center justify-between px-3 py-2 cursor-pointer hover:bg-black/30 transition-colors"
                    onClick={() => toggleStage(stageIndex)}
                  >
                    <div className="flex items-center gap-2">
                      <Chip
                        size="sm"
                        variant="flat"
                        color={stageIndex === currentStageIndex ? 'primary' : 'default'}
                      >
                        {stageIndex + 1}
                      </Chip>
                      <h4
                        className={`font-medium ${
                          stageIndex === currentStageIndex ? 'text-primary' : 'text-gray-200'
                        }`}
                      >
                        {stage.stageTitle}
                      </h4>
                    </div>
                    <div className="flex items-center gap-2">
                      <div onClick={e => e.stopPropagation()}>
                        <Button
                          size="sm"
                          variant="flat"
                          color="primary"
                          onPress={() => handleStageSwitch(stageIndex)}
                          startContent={<Icon icon="solar:play-bold" width={14} />}
                        >
                          切换
                        </Button>
                      </div>
                      <Icon
                        icon="solar:alt-arrow-down-linear"
                        className={`text-gray-400 transition-transform ${
                          expandedStages.includes(stageIndex) ? 'transform rotate-180' : ''
                        }`}
                        width={16}
                      />
                    </div>
                  </div>

                  {/* 阶段对白列表 */}
                  {expandedStages.includes(stageIndex) && (
                    <div className="px-2 pb-2">
                      {/* 如果是当前活跃阶段且用户手动折叠，显示提示信息 */}
                      {activeDialogue &&
                        activeDialogue.stageIndex === stageIndex &&
                        userCollapsedStages.includes(stageIndex) && (
                          <Card className="mb-2 bg-primary/20 border-primary/30">
                            <CardBody className="p-2">
                              <div className="flex items-center justify-between">
                                <span className="text-primary text-sm">当前正在播放此阶段对白</span>
                                <div onClick={e => e.stopPropagation()}>
                                  <Button
                                    size="sm"
                                    variant="flat"
                                    color="primary"
                                    onPress={() => {
                                      setUserCollapsedStages(
                                        userCollapsedStages.filter(i => i !== stageIndex)
                                      )
                                    }}
                                  >
                                    显示对白
                                  </Button>
                                </div>
                              </div>
                            </CardBody>
                          </Card>
                        )}

                      {/* 只有在未被用户手动折叠的情况下才显示对白列表 */}
                      {!userCollapsedStages.includes(stageIndex) && (
                        <div className="space-y-1">
                          {stage.dialogues.map((dialogue, dialogueIndex) => {
                            // 判断是否是当前活跃对白
                            const isActive =
                              activeDialogue &&
                              activeDialogue.stageIndex === stageIndex &&
                              activeDialogue.dialogueIndex === dialogueIndex

                            return (
                              <Card
                                key={`dialogue-${stageIndex}-${dialogue.time.replace(
                                  /:/g,
                                  '-'
                                )}-${dialogueIndex}`}
                                isPressable
                                className={`transition-all cursor-pointer ${
                                  isActive
                                    ? 'bg-primary/30 border-primary/50'
                                    : 'bg-gray-800/30 hover:bg-gray-800/50 border-transparent'
                                }`}
                                onPress={() => handleDialogueClick(stageIndex, dialogue)}
                              >
                                <CardBody className="p-2">
                                  <div className="flex items-center mb-1 gap-2">
                                    <Chip
                                      size="sm"
                                      variant="flat"
                                      color={isActive ? 'primary' : 'default'}
                                    >
                                      {dialogue.role}
                                    </Chip>
                                    <span className="text-gray-500 text-xs">{dialogue.time}</span>
                                  </div>
                                  <p
                                    className={`text-sm leading-relaxed ${
                                      isActive ? 'text-white' : 'text-gray-400'
                                    }`}
                                  >
                                    {dialogue.dialogue}
                                  </p>
                                </CardBody>
                              </Card>
                            )
                          })}
                        </div>
                      )}
                    </div>
                  )}
                </CardBody>
              </Card>
            ))}
          </div>
        </ScrollShadow>
      </CardBody>
    </Card>
  )
}

// 添加滚动条样式
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.innerHTML = `
    .scrollbar-thin::-webkit-scrollbar {
      width: 4px;
    }
    .scrollbar-thin::-webkit-scrollbar-track {
      background: transparent;
    }
    .scrollbar-thin::-webkit-scrollbar-thumb {
      background-color: rgba(107, 114, 128, 0.5);
      border-radius: 20px;
    }
    .scrollbar-thin {
      scrollbar-width: thin;
      scrollbar-color: rgba(107, 114, 128, 0.5) transparent;
    }
  `
  document.head.appendChild(style)
}

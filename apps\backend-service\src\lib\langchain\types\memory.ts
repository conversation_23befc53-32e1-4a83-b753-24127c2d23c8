// 记忆系统类型定义（预留接口）

// 记忆类型
export enum MemoryType {
  BUFFER = 'buffer', // 短期记忆
  VECTOR = 'vector', // 长期记忆
  SHARED = 'shared', // 共享记忆
}

// 记忆项
export interface MemoryItem {
  id: string;
  content: string;
  timestamp: Date;
  importance: number; // 重要性评分 0-1
  tags: string[];
  characterId?: string;
  userId: string;
}

// 记忆检索结果
export interface MemorySearchResult {
  item: MemoryItem;
  similarity: number; // 相似度评分 0-1
  relevance: number; // 相关性评分 0-1
}

// 记忆管理器接口（预留）
export interface MemoryManager {
  // 添加记忆
  addMemory(item: Omit<MemoryItem, 'id' | 'timestamp'>): Promise<string>;

  // 检索记忆
  searchMemories(
    query: string,
    options?: {
      limit?: number;
      threshold?: number;
      characterId?: string;
    }
  ): Promise<MemorySearchResult[]>;

  // 更新记忆重要性
  updateImportance(id: string, importance: number): Promise<void>;

  // 删除记忆
  deleteMemory(id: string): Promise<void>;

  // 清理过期记忆
  cleanup(options?: { olderThan?: Date; importanceThreshold?: number }): Promise<number>;
}

// 缓冲记忆配置
export interface BufferMemoryConfig {
  maxSize: number; // 最大消息数量
  windowSize: number; // 滑动窗口大小
}

// 向量记忆配置
export interface VectorMemoryConfig {
  dimension: number; // 向量维度
  similarityThreshold: number; // 相似度阈值
  maxResults: number; // 最大检索结果数
}

// 共享记忆配置（预留）
export interface SharedMemoryConfig {
  enableCrossCharacter: boolean; // 启用跨角色记忆
  relationshipWeight: number; // 关系权重
  knowledgeDecay: number; // 知识衰减率
}

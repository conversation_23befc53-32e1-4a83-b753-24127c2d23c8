import React, { useState, useEffect, useRef, useCallback } from 'react'
import { Chip } from '@heroui/react'
import { timeToSeconds, secondsToTime } from '../utils/timeUtils'
import type { <PERSON>ript } from '../types'

interface StageProgressBarProps {
  currentTime: number
  duration: number
  script: Script
  currentStageIndex: number
  onSeek: (time: number) => void
  className?: string
}

interface StageMarker {
  stageIndex: number
  time: number
  title: string
  position: number // 百分比位置
}

/**
 * 带阶段标记的进度条组件
 * 功能：
 * 1. 显示播放进度
 * 2. 在进度条上显示阶段标记
 * 3. 点击标记或播放到对应时间时显示tooltip
 */
export const StageProgressBar: React.FC<StageProgressBarProps> = ({
  currentTime,
  duration,
  script,
  currentStageIndex,
  onSeek,
  className = ''
}) => {
  const [tooltip, setTooltip] = useState<{
    visible: boolean
    stageIndex: number
    title: string
    position: number
  }>({
    visible: false,
    stageIndex: -1,
    title: '',
    position: 0
  })

  const [hoveredMarker, setHoveredMarker] = useState<number>(-1)
  const progressBarRef = useRef<HTMLDivElement>(null)
  const tooltipTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)

  // 用于跟踪用户是否刚刚手动跳转了时间
  const [userJustSeeked, setUserJustSeeked] = useState(false)
  const userSeekTimeoutRef = useRef<NodeJS.Timeout | null>(null)

  // 计算阶段标记
  const stageMarkers = React.useMemo<StageMarker[]>(() => {
    if (!script || script.length === 0 || !duration) return []

    const markers: StageMarker[] = []
    const MIN_TIME_THRESHOLD = 1 // 降低时间阈值，只过滤掉第一秒内的阶段

    script.forEach((stage, index) => {
      let stageTime: number | undefined = undefined

      // 优先使用 stageTime
      if (typeof (stage as any).stageTime === 'string' && (stage as any).stageTime.length > 0) {
        stageTime = timeToSeconds((stage as any).stageTime)
      }
      // 如果没有 stageTime，使用 dialogues 数组的第一个时间
      else if (stage.dialogues && stage.dialogues.length > 0) {
        stageTime = timeToSeconds(stage.dialogues[0].time)
      }

      // 如果有有效时间且不是太接近开头，添加标记
      if (stageTime !== undefined && stageTime >= MIN_TIME_THRESHOLD) {
        const position = (stageTime / duration) * 100
        const marker = {
          stageIndex: index,
          time: stageTime,
          title: stage.stageTitle,
          position: Math.min(position, 100) // 确保不超过100%
        }

        markers.push(marker)
      }
    })

    console.log(
      `🎯 StageProgressBar - 总共创建了 ${markers.length} 个标记，共 ${script.length} 个阶段`
    )
    return markers
  }, [script, duration])

  // 隐藏tooltip
  const hideTooltip = useCallback(() => {
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current)
    }
    setTooltip(prev => ({ ...prev, visible: false }))
  }, [])

  // 处理进度条点击
  const handleProgressClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (!progressBarRef.current || !duration) return

      const rect = progressBarRef.current.getBoundingClientRect()
      const clickX = event.clientX - rect.left
      const progressWidth = rect.width
      const percentage = Math.max(0, Math.min(1, clickX / progressWidth))
      const newTime = percentage * duration

      // 点击非标记区域时立即隐藏tooltip
      hideTooltip()

      // 标记用户刚刚手动跳转了时间
      setUserJustSeeked(true)

      // 清除之前的定时器
      if (userSeekTimeoutRef.current) {
        clearTimeout(userSeekTimeoutRef.current)
      }

      // 2秒后重新启用自动显示tooltip
      userSeekTimeoutRef.current = setTimeout(() => {
        setUserJustSeeked(false)
      }, 2000)

      onSeek(newTime)
    },
    [duration, onSeek, hideTooltip]
  )

  // 显示tooltip的通用函数
  const showTooltip = useCallback((marker: StageMarker) => {
    setTooltip({
      visible: true,
      stageIndex: marker.stageIndex,
      title: marker.title,
      position: marker.position
    })

    // 清除之前的定时器
    if (tooltipTimeoutRef.current) {
      clearTimeout(tooltipTimeoutRef.current)
    }

    // 3秒后隐藏tooltip
    tooltipTimeoutRef.current = setTimeout(() => {
      setTooltip(prev => ({ ...prev, visible: false }))
    }, 3000)
  }, [])

  // 处理标记点击
  const handleMarkerClick = useCallback(
    (event: React.MouseEvent, marker: StageMarker) => {
      event.stopPropagation() // 阻止事件冒泡到进度条

      // 显示tooltip
      showTooltip(marker)

      // 跳转到对应时间
      onSeek(marker.time)
    },
    [onSeek, showTooltip]
  )

  // 处理标记hover
  const handleMarkerMouseEnter = useCallback((marker: StageMarker) => {
    setHoveredMarker(marker.stageIndex)
  }, [])

  const handleMarkerMouseLeave = useCallback(() => {
    setHoveredMarker(-1)
  }, [])

  // 监听播放时间变化，当到达阶段时间时显示tooltip
  useEffect(() => {
    // 如果用户刚刚手动跳转了时间，暂时禁用自动显示tooltip
    if (userJustSeeked) {
      return
    }

    const currentMarker = stageMarkers.find(marker => {
      const timeDiff = Math.abs(currentTime - marker.time)
      return timeDiff < 0.5 // 误差范围内认为到达了阶段时间
    })

    if (currentMarker && currentMarker.stageIndex === currentStageIndex) {
      showTooltip(currentMarker)
    }
  }, [currentTime, currentStageIndex, stageMarkers, showTooltip, userJustSeeked])

  // 监听当前阶段变化，更新标记状态和显示对应的tooltip
  useEffect(() => {
    // 当阶段切换时，查找对应的标记并短暂显示tooltip
    const currentMarker = stageMarkers.find(marker => marker.stageIndex === currentStageIndex)
    if (currentMarker) {
      showTooltip(currentMarker)
    }
  }, [currentStageIndex, stageMarkers, showTooltip])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (tooltipTimeoutRef.current) {
        clearTimeout(tooltipTimeoutRef.current)
      }
      if (userSeekTimeoutRef.current) {
        clearTimeout(userSeekTimeoutRef.current)
      }
    }
  }, [])

  // 计算当前播放进度百分比
  const progressPercentage = duration > 0 ? (currentTime / duration) * 100 : 0

  // 根据当前播放时间计算应该高亮的阶段
  const getActiveStageByTime = useCallback(
    (time: number) => {
      if (!script || script.length === 0) return -1

      // 找到当前时间点应该对应的阶段
      for (let i = script.length - 1; i >= 0; i--) {
        const stage = script[i]
        let stageTime: number | undefined = undefined

        // 优先使用 stageTime
        if (typeof (stage as any).stageTime === 'string' && (stage as any).stageTime.length > 0) {
          stageTime = timeToSeconds((stage as any).stageTime)
        }
        // 如果没有 stageTime，使用 dialogues 数组的第一个时间
        else if (stage.dialogues && stage.dialogues.length > 0) {
          stageTime = timeToSeconds(stage.dialogues[0].time)
        }

        // 如果当前时间大于等于这个阶段的开始时间，说明应该高亮这个阶段
        if (stageTime !== undefined && time >= stageTime) {
          return i
        }
      }

      return 0 // 默认返回第一个阶段
    },
    [script]
  )

  return (
    <div className={`relative w-full ${className}`}>
      {/* 进度条容器 */}
      <div className="relative">
        {/* Tooltip - 使用 bubble.svg 作为背景 */}
        {tooltip.visible && (
          <div
            ref={tooltipRef}
            className="absolute -top-16 transform -translate-x-1/2 z-50 animate-in fade-in-0 zoom-in-95 duration-200"
            style={{ left: `${tooltip.position}%` }}
          >
            <div className="relative inline-block">
              {/* 使用 bubble.svg 作为背景，支持文字自适应 */}
              <div
                className="relative min-w-[72px] h-[39px] flex items-center justify-center"
                style={{
                  backgroundImage: `url('/images/interactive/bubble.svg')`,
                  backgroundSize: '100% 100%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: 'center'
                }}
              >
                <div className="text-white text-xs font-medium whitespace-nowrap text-center px-4 py-1 mb-2">
                  {tooltip.title}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* 阶段标记 - 在进度条上方 */}
        {stageMarkers.map(marker => {
          // 根据当前播放时间计算应该高亮的阶段
          const activeStageByTime = getActiveStageByTime(currentTime)
          const isActive = marker.stageIndex === activeStageByTime
          const isHovered = hoveredMarker === marker.stageIndex

          return (
            <div
              key={`marker-${marker.stageIndex}`}
              className="absolute -top-4 transform -translate-x-1/2 cursor-pointer z-30"
              style={{ left: `${marker.position}%` }}
              onClick={e => handleMarkerClick(e, marker)}
              onMouseEnter={() => handleMarkerMouseEnter(marker)}
              onMouseLeave={handleMarkerMouseLeave}
            >
              {/* 标记点 - 椭圆形 */}
              <div
                className={`w-1 h-2 rounded-full transition-all duration-200 ${
                  isActive
                    ? 'bg-[#ff2d97] shadow-lg shadow-[#ff2d97]/50'
                    : isHovered
                    ? 'bg-white shadow-md'
                    : 'bg-white/60 hover:bg-white'
                }`}
              />
            </div>
          )
        })}

        {/* 进度条主体 */}
        <div
          ref={progressBarRef}
          className="relative w-full h-1 bg-white/20 rounded-full cursor-pointer"
          onClick={handleProgressClick}
        >
          {/* 播放进度 */}
          <div
            className="absolute left-0 top-0 h-full bg-[#ff2d97] rounded-full transition-all duration-100"
            style={{ width: `${Math.min(progressPercentage, 100)}%` }}
          />

          {/* 播放头 - 使用 progress-btn.svg */}
          <div
            className="absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 z-20 transition-all duration-100"
            style={{ left: `${Math.min(progressPercentage, 100)}%` }}
          >
            <div
              className="w-7 h-4"
              style={{
                backgroundImage: `url('/images/interactive/progress-btn.svg')`,
                backgroundSize: 'contain',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center'
              }}
            />
          </div>
        </div>
      </div>

      {/* 时间显示 - 在进度条下方 */}
      <div className="flex items-center justify-between mt-4">
        <span className="text-white/70 text-sm font-mono">{secondsToTime(currentTime)}</span>
        <span className="text-white/70 text-sm font-mono">{secondsToTime(duration)}</span>
      </div>
    </div>
  )
}

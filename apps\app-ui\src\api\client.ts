import { isCapacitorEnvironment } from '@/lib/utils'
import { addToast } from '@heroui/react'
import { useTranslation } from 'react-i18next'
import i18n from '@/i18n'
import { getCurrentLanguage, formatAcceptLanguage } from '@/config/i18n'

// API基础URL配置
const API_URL = isCapacitorEnvironment()
  ? import.meta.env.VITE_APP_API_URL
  : import.meta.env.VITE_WEB_API_URL

// API响应基础接口
export interface ApiResponse<T> {
  success: boolean
  data: T
  message?: string
  error?: string
}

// 错误类型
export class ApiError extends Error {
  status: number

  constructor(message: string, status: number) {
    super(message)
    this.name = 'ApiError'
    this.status = status
  }
}

// API请求选项接口
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: unknown
  credentials?: RequestCredentials
  requireAuth?: boolean // 是否需要认证
  silentError?: boolean // 是否静默处理错误（不显示 toast）
}

// 获取访问令牌
const getAccessToken = (): string | null => {
  return localStorage.getItem('access_token')
}

// API响应处理工具
class ApiClient {
  // 获取认证头
  private getAuthHeaders(): Record<string, string> {
    const token = getAccessToken()
    return token ? { Authorization: `Bearer ${token}` } : {}
  }

  // 获取语言头
  private getLanguageHeaders(): Record<string, string> {
    const language = getCurrentLanguage()
    return {
      'Accept-Language': formatAcceptLanguage(language)
    }
  }

  // 获取默认 credentials 设置
  private getDefaultCredentials(): RequestCredentials {
    // 在 Capacitor 环境中，通常不需要 credentials
    // 这可以避免 CORS 问题
    return isCapacitorEnvironment() ? 'omit' : 'include'
  }

  // 基础请求方法 - 统一使用标准 fetch API
  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    // 默认请求配置
    const defaultOptions: RequestOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        ...this.getAuthHeaders(), // 添加认证头
        ...this.getLanguageHeaders() // 添加语言头
      },
      credentials: this.getDefaultCredentials() // 根据环境设置 credentials
    }

    // 合并选项
    const mergedOptions: RequestOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    // 如果有body，序列化为JSON
    if (mergedOptions.body && typeof mergedOptions.body === 'object') {
      mergedOptions.body = JSON.stringify(mergedOptions.body)
    }

    try {
      console.log(`[API请求] ${mergedOptions.method} ${API_URL}${endpoint}`)
      console.log('[API请求配置]', mergedOptions)

      // 发送请求
      const response = await fetch(`${API_URL}${endpoint}`, mergedOptions as RequestInit)

      // 检查响应状态
      if (!response.ok) {
        let errorMessage = `API Error: ${response.status} ${response.statusText}`

        // 尝试解析错误响应
        try {
          const errorData = await response.json()
          errorMessage = errorData.error || errorData.message || errorMessage
          console.error('[API错误详情]', JSON.stringify(errorData))
        } catch {
          // 解析错误响应失败，保持默认错误信息
          console.error('[API错误] 无法解析错误响应')
        }

        // 显示多语言错误提示
        this.showErrorToast(errorMessage, response.status, mergedOptions.silentError)
        throw new ApiError(errorMessage, response.status)
      }

      // 检查响应内容类型
      const contentType = response.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        const data = await response.json()
        console.log(`[API响应] ${endpoint}:`, data)
        return data as T
      }

      const textResponse = await response.text()
      console.log(`[API响应文本] ${endpoint}:`, textResponse)
      return textResponse as unknown as T
    } catch (error) {
      console.error(`[API错误] ${endpoint}:`, error)

      if (error instanceof ApiError) {
        throw error
      }

      // 显示多语言错误提示
      this.showErrorToast(
        error instanceof Error ? error.message : this.getTranslation('toast:api.unknown_error'),
        0,
        mergedOptions.silentError
      )
      // 处理其他错误（网络错误等）
      throw new ApiError(error instanceof Error ? error.message : 'Unknown API error', 0)
    }
  }

  // 获取翻译文本的辅助方法
  private getTranslation(key: string, options?: any): string {
    try {
      const result = i18n.t(key, options)
      return typeof result === 'string' ? result : key
    } catch (error) {
      console.warn(`Translation key not found: ${key}`)
      return key
    }
  }

  // 显示错误提示的统一方法
  private showErrorToast(errorMessage: string, status: number, silentError?: boolean) {
    // 如果设置了静默错误，则不显示 toast
    if (silentError) {
      return
    }

    // 根据状态码显示不同的错误提示
    if (status === 401) {
      addToast({
        title: this.getTranslation('toast:api.unauthorized'),
        description: this.getTranslation('toast:api.unauthorized_description'),
        color: 'warning'
      })
    } else if (status === 403) {
      addToast({
        title: this.getTranslation('toast:api.forbidden'),
        description: this.getTranslation('toast:api.forbidden_description'),
        color: 'warning'
      })
    } else if (status === 404) {
      addToast({
        title: this.getTranslation('toast:api.not_found'),
        description: this.getTranslation('toast:api.not_found_description'),
        color: 'warning'
      })
    } else if (status >= 500) {
      addToast({
        title: this.getTranslation('toast:api.server_error'),
        description: this.getTranslation('toast:api.server_error_description'),
        color: 'danger'
      })
    } else {
      addToast({
        title: this.getTranslation('toast:api.request_failed'),
        description: errorMessage,
        color: 'danger'
      })
    }
  }

  // GET请求快捷方法
  async get<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' })
  }

  // POST请求快捷方法
  async post<T>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data
    })
  }

  // PUT请求快捷方法
  async put<T>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data
    })
  }

  // PATCH请求
  async patch<T>(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> = {}
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data
    })
  }

  // DELETE请求快捷方法
  async delete<T>(endpoint: string, options: Omit<RequestOptions, 'method'> = {}): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'DELETE'
    })
  }

  // 流式POST请求方法
  async streamPost(
    endpoint: string,
    data?: unknown,
    options: Omit<RequestOptions, 'method' | 'body'> & { signal?: AbortSignal } = {}
  ): Promise<Response> {
    // 默认请求配置
    const defaultOptions: RequestOptions = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...this.getAuthHeaders(), // 添加认证头
        ...this.getLanguageHeaders() // 添加语言头
      },
      credentials: this.getDefaultCredentials() // 根据环境设置 credentials
    }

    // 合并选项
    const mergedOptions: RequestOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    // 如果有body，序列化为JSON
    if (data && typeof data === 'object') {
      mergedOptions.body = JSON.stringify(data)
    }

    try {
      console.log(`[流式API请求] ${mergedOptions.method} ${API_URL}${endpoint}`)
      console.log('[流式API请求配置]', mergedOptions)

      // 发送请求并返回 Response 对象（不解析响应体）
      const fetchOptions: RequestInit = {
        method: mergedOptions.method,
        headers: mergedOptions.headers as HeadersInit,
        body: mergedOptions.body as BodyInit,
        credentials: mergedOptions.credentials,
        signal: options.signal
      }
      const response = await fetch(`${API_URL}${endpoint}`, fetchOptions)

      // 检查响应状态
      if (!response.ok) {
        let errorMessage = `API Error: ${response.status} ${response.statusText}`

        // 尝试解析错误响应
        try {
          const errorData = await response.clone().json()
          errorMessage = errorData.error || errorData.message || errorMessage
          console.error('[流式API错误详情]', errorData)
        } catch {
          // 解析错误响应失败，保持默认错误信息
          console.error('[流式API错误] 无法解析错误响应')
        }

        throw new ApiError(errorMessage, response.status)
      }

      console.log(`[流式API响应] ${endpoint}: 流式响应已建立`)
      return response
    } catch (error) {
      console.error(`[流式API错误] ${endpoint}:`, error)

      if (error instanceof ApiError) {
        throw error
      }

      // 处理其他错误（网络错误等）
      throw new ApiError(error instanceof Error ? error.message : 'Unknown API error', 0)
    }
  }
}

// 导出API客户端实例
export const apiClient = new ApiClient()

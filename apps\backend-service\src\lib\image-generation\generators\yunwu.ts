import type { Env } from '@/types/env';
import type { GenerateImageRequest, YunwuApiResponse } from '@/types/image-generation';
import { downloadAndUploadImage, uploadBase64Image } from '../upload';

/**
 * 云雾AI图片生成器
 * 特点：同步生成，直接返回图片URL
 */
export class YunwuGenerator {
  constructor(private env: Env) {}

  /**
   * 生成图片
   */
  async generateImage(request: GenerateImageRequest): Promise<string> {
    const { prompt } = request;

    if (!this.env.YUNWU_API_KEY) {
      throw new Error('云雾AI API密钥未配置');
    }

    try {
      // 调用云雾AI API
      const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/images/generations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.env.YUNWU_API_KEY}`,
        },
        body: JSON.stringify({
          prompt: prompt,
          n: 1,
          size: '864x1152',
          model: 'doubao-seedream-3-0-t2i-250415',
          response_format: 'url',
          watermark: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`云雾AI API请求失败: ${response.statusText}`);
      }

      const data: YunwuApiResponse = await response.json();

      // 处理返回的数据
      if (data.data?.[0]?.url) {
        // 如果返回的是URL，下载并上传到R2
        return await downloadAndUploadImage(this.env, data.data[0].url);
      } else if (data.data?.[0]?.b64_json) {
        // 如果返回的是base64数据，直接上传到R2
        return await uploadBase64Image(this.env, data.data[0].b64_json);
      } else {
        throw new Error('云雾AI返回数据格式错误');
      }
    } catch (error) {
      console.error('云雾AI生成图片失败:', error);
      throw new Error('生成图片失败');
    }
  }
}

import { useEffect, useState } from 'react'
import { useNavigate, useLocation } from 'react-router'
import { Capacitor } from '@capacitor/core'
import { App as CapacitorApp } from '@capacitor/app'

// 底部导航的真正标签页路径列表，仅包含需要replace导航的标签
const TAB_PATHS = ['/discover', '/chat-history']

// 需要禁用左滑返回的页面路径
const SWIPE_DISABLED_PATHS = ['/interactive', '/interactive/player']

/**
 * 检查是否是标签页之间的切换
 * @param currentPath 当前路径
 * @param previousPath 前一个路径
 * @returns 是否是标签切换
 */
function isTabSwitch(currentPath: string, previousPath: string): boolean {
  // 获取当前路径和上一个路径的根路径
  const currentRoot = currentPath.split('?')[0] // 移除查询参数
  const previousRoot = previousPath.split('?')[0]

  // 如果当前路径和上一个路径都在标签路径列表中，则视为标签切换
  return TAB_PATHS.includes(currentRoot) && TAB_PATHS.includes(previousRoot)
}

/**
 * 检查当前页面是否应该禁用左滑返回
 * @param currentPath 当前路径
 * @returns 是否禁用左滑
 */
function isSwipeDisabled(currentPath: string): boolean {
  const currentRoot = currentPath.split('?')[0] // 移除查询参数
  
  // 检查完全匹配的路径
  if (SWIPE_DISABLED_PATHS.includes(currentRoot)) {
    return true
  }
  
  // 检查以禁用路径开头的路径（用于处理动态路由）
  return SWIPE_DISABLED_PATHS.some(disabledPath => 
    currentRoot.startsWith(disabledPath + '/')
  )
}

/**
 * 左滑返回钩子
 * 实现移动端左滑返回上一页功能，以及无页面可返回时的退出确认提示
 */
export function useSwipeBack() {
  const navigate = useNavigate()
  const location = useLocation()
  const [showExitConfirm, setShowExitConfirm] = useState(false)
  // 保存当前和上一个路径，用于判断是否是标签切换
  const [previousPaths, setPreviousPaths] = useState<string[]>([])

  // 监听路径变化，更新路径历史
  useEffect(() => {
    setPreviousPaths(prev => {
      // 如果当前路径与上一个路径不同，则添加到历史记录
      if (prev.length === 0 || prev[prev.length - 1] !== location.pathname) {
        // 限制历史记录长度，避免无限增长
        const newPaths = [...prev, location.pathname]
        if (newPaths.length > 10) {
          return newPaths.slice(-10)
        }
        return newPaths
      }
      return prev
    })
  }, [location.pathname])

  useEffect(() => {
    // 处理左滑手势
    let startX = 0
    let startY = 0
    const threshold = 100 // 滑动触发阈值

    const handleTouchStart = (e: TouchEvent) => {
      startX = e.touches[0].clientX
      startY = e.touches[0].clientY
    }

    const handleTouchEnd = (e: TouchEvent) => {
      const endX = e.changedTouches[0].clientX
      const endY = e.changedTouches[0].clientY
      const diffX = endX - startX
      const diffY = endY - startY

      // 确保是横向滑动（水平距离大于垂直距离，避免与垂直滚动冲突）
      if (Math.abs(diffX) > Math.abs(diffY)) {
        // 检测右向左滑动（在中文环境下通常是返回）
        if (diffX < -threshold && Capacitor.isNativePlatform()) {
          // 检查当前页面是否禁用左滑返回
          if (isSwipeDisabled(location.pathname)) {
            console.log('当前页面已禁用左滑返回:', location.pathname)
            return
          }
          
          // 检查触摸元素是否禁用左滑返回
          const target = e.target as Element
          const swipeDisabledElement = target.closest('[data-swipe-disabled="true"]')
          if (swipeDisabledElement) {
            console.log('触摸区域已禁用左滑返回')
            return
          }
          
          handleBackNavigation()
        }
      }
    }

    // 处理返回导航逻辑
    const handleBackNavigation = () => {
      // 检查历史记录是否有上一页
      if (window.history.length > 1) {
        // 获取当前路径和上一个可能的路径
        const currentPath = location.pathname
        const previousPath = previousPaths.length > 1 ? previousPaths[previousPaths.length - 2] : ''

        // 只有在真正的标签页之间切换时，才跳过返回操作
        if (previousPath && isTabSwitch(currentPath, previousPath)) {
          console.log('标签页切换，不执行返回操作:', currentPath, '<=>', previousPath)
          return
        }

        // 非标签切换或非标签页面，正常返回
        console.log('执行返回操作:', currentPath, '<=>', previousPath)
        navigate(-1)
      } else {
        // 没有上一页，显示退出确认弹窗
        setShowExitConfirm(true)
      }
    }

    document.addEventListener('touchstart', handleTouchStart)
    document.addEventListener('touchend', handleTouchEnd)

    // 监听原生返回按钮事件（安卓）
    if (Capacitor.isNativePlatform()) {
      CapacitorApp.addListener('backButton', ({ canGoBack }: { canGoBack: boolean }) => {
        console.log('canGoBack', canGoBack)
        if (canGoBack) {
          // 处理与左滑相同的逻辑
          handleBackNavigation()
        } else {
          setShowExitConfirm(true)
        }
      })
    }

    return () => {
      document.removeEventListener('touchstart', handleTouchStart)
      document.removeEventListener('touchend', handleTouchEnd)

      if (Capacitor.isNativePlatform()) {
        CapacitorApp.removeAllListeners()
      }
    }
  }, [navigate, location.pathname, previousPaths])

  // 处理退出应用
  const handleExitApp = () => {
    if (Capacitor.isNativePlatform()) {
      CapacitorApp.exitApp()
    }
    setShowExitConfirm(false)
  }

  // 关闭退出确认弹窗
  const closeExitConfirm = () => {
    setShowExitConfirm(false)
  }

  return {
    showExitConfirm,
    setShowExitConfirm,
    handleExitApp,
    closeExitConfirm
  }
}

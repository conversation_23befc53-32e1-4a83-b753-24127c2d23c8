// 结构化输出解析器

import type { MultiModalResponse } from '../types/multimodal';
import { TagType } from '../types/multimodal';
import { TagParser } from './tag-parser';

export class StructuredOutputParser {
  private tagParser: TagParser;

  constructor() {
    this.tagParser = new TagParser();
  }

  /**
   * 解析 AI 输出文本为结构化的多模态响应
   */
  parse(aiOutput: string): MultiModalResponse {
    // 验证标签格式
    const validation = this.tagParser.validateTagFormat(aiOutput);
    if (!validation.isValid) {
      console.warn('标签格式验证失败:', validation.errors);
    }

    // 解析所有标签
    const tags = this.tagParser.parseAllTags(aiOutput);

    // 构建多模态响应
    const response: MultiModalResponse = {
      text: {},
    };

    // 处理文本内容
    tags.forEach((tag) => {
      switch (tag.type) {
        case TagType.SCENE:
          response.text.scene = tag.content;
          break;
        case TagType.ACTION:
          response.text.action = tag.content;
          break;
        case TagType.DIALOGUE:
          response.text.dialogue = tag.content;
          break;
        case TagType.IMAGE_PROMPT:
          response.image = {
            prompt: tag.content,
          };
          break;
        case TagType.AUDIO_TAGS:
          response.audio = {
            tags: tag.content.split(',').map((t) => t.trim()),
          };
          break;
      }
    });

    return response;
  }

  /**
   * 将多模态响应转换回文本格式（用于存储或显示）
   */
  serialize(response: MultiModalResponse): string {
    let result = '';

    // 添加场景描述
    if (response.text.scene) {
      result += `<scene:${response.text.scene}>\n`;
    }

    // 添加动作描述
    if (response.text.action) {
      result += `<action:${response.text.action}>`;
    }

    // 添加对话内容
    if (response.text.dialogue) {
      result += `<dialogue>${response.text.dialogue}</dialogue>\n`;
    }

    // 添加图片提示
    if (response.image?.prompt) {
      result += `<imagePrompt>${response.image.prompt}</imagePrompt>\n`;
    }

    // 添加音频标签
    if (response.audio?.tags && response.audio.tags.length > 0) {
      result += `<audioTags>${response.audio.tags.join(',')}</audioTags>\n`;
    }

    return result.trim();
  }

  /**
   * 提取纯文本内容（移除所有标签）
   */
  extractPlainText(aiOutput: string): string {
    return this.tagParser.removeAllTags(aiOutput);
  }

  /**
   * 检查输出是否包含特定模态
   */
  hasModality(aiOutput: string, modality: 'text' | 'image' | 'audio'): boolean {
    switch (modality) {
      case 'text':
        return (
          this.tagParser.hasTag(aiOutput, TagType.DIALOGUE) ||
          this.tagParser.hasTag(aiOutput, TagType.SCENE) ||
          this.tagParser.hasTag(aiOutput, TagType.ACTION)
        );
      case 'image':
        return this.tagParser.hasTag(aiOutput, TagType.IMAGE_PROMPT);
      case 'audio':
        return this.tagParser.hasTag(aiOutput, TagType.AUDIO_TAGS);
      default:
        return false;
    }
  }

  /**
   * 获取特定模态的内容
   */
  getModalityContent(aiOutput: string, modality: TagType): string[] {
    return this.tagParser.getTagContent(aiOutput, modality);
  }

  /**
   * 验证输出格式是否符合要求
   */
  validateOutput(aiOutput: string): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 基础格式验证
    const tagValidation = this.tagParser.validateTagFormat(aiOutput);
    if (!tagValidation.isValid) {
      errors.push(...tagValidation.errors);
    }

    // 检查是否至少包含对话内容
    if (!this.tagParser.hasTag(aiOutput, TagType.DIALOGUE)) {
      warnings.push('输出中缺少对话内容');
    }

    // 检查图片提示的质量（简单检查）
    const imagePrompts = this.tagParser.getTagContent(aiOutput, TagType.IMAGE_PROMPT);
    imagePrompts.forEach((prompt) => {
      if (prompt.length < 10) {
        warnings.push('图片提示过于简短，可能影响生成质量');
      }
    });

    // 检查音频标签格式
    const audioTags = this.tagParser.getTagContent(aiOutput, TagType.AUDIO_TAGS);
    audioTags.forEach((tags) => {
      const tagList = tags.split(',').map((t) => t.trim());
      if (tagList.some((tag) => tag.length === 0)) {
        warnings.push('音频标签中包含空标签');
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }
}

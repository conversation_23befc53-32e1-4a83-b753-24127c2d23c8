import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { createPaymentService } from '@/lib/payment/payment-factory';
import { PaymentMethod } from '@/lib/payment/payment-config';
import {
  createPaymentOrder,
  getPaymentOrderByOrderNo,
  getPaymentOrderById,
  updatePaymentOrderStatus,
  getPaymentOrderWithDetails,
} from '@/lib/db/queries/payment';
import {
  getMembershipPlanById,
  createUserSubscription,
  getUserPoints,
  updateUserPoints,
  getLocalUserIdFromSupabaseId,
  ensureUserExists,
  getUserActiveSubscription,
  updateSubscriptionStatus,
} from '@/lib/db/queries/membership';
import { pointsPackageQueries } from '@/lib/db/queries/points-package';
import { calculateAndCreateCommission } from '@/lib/commission/service';
import { authMiddleware } from '@/middleware/auth';
import { createPointsCycleManager } from '@/lib/membership/points-cycle';
import { languageMiddleware } from '@/middleware/language';
import type { Env } from '@/types/env';

const app = new Hono<{ Bindings: Env }>();

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: any): Promise<string> {
  const supabaseUser = c.get('user');
  const t = c.get('t');
  if (!supabaseUser) {
    throw new Error(t('user_not_authenticated'));
  }

  // 确保用户在本地数据库中存在
  const localUserId = await ensureUserExists(c.env, supabaseUser.id, supabaseUser.email || '');
  return localUserId;
}

// 创建支付订单 Schema
const createOrderSchema = z.object({
  planId: z.string().uuid(),
  paymentMethod: z.enum(['alipay', 'wechat']).default('alipay'),
  description: z.string().optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * POST /api/payment/create-order
 * 创建支付订单
 */
app.post('/create-order', languageMiddleware, authMiddleware, zValidator('json', createOrderSchema), async (c) => {
  try {
    const { planId, paymentMethod, description, metadata } = c.req.valid('json');
    const t = c.get('t');

    // 获取本地用户ID
    const localUserId = await resolveLocalUserId(c);

    // 获取目标会员计划信息
    const targetPlan = await getMembershipPlanById(c.env, planId);
    if (!targetPlan) {
      return c.json({ success: false, error: t('membership_plan_not_exist') }, 404);
    }

    // 检查用户当前订阅状态
    const currentSubscription = await getUserActiveSubscription(c.env, localUserId);
    let actualAmount = Number.parseFloat(targetPlan.price);
    let orderDescription = description || `订阅${targetPlan.name}`;
    let isUpgrade = false;

    // 如果用户有活跃订阅，计算升级差价
    if (currentSubscription) {
      const currentPlan = await getMembershipPlanById(c.env, currentSubscription.planId);
      if (currentPlan) {
        const currentPlanPrice = Number.parseFloat(currentPlan.price);
        const targetPlanPrice = Number.parseFloat(targetPlan.price);

        // 判断升级或降级
        if (targetPlanPrice > currentPlanPrice) {
          isUpgrade = true;
        } else if (targetPlanPrice < currentPlanPrice) {
          // 不允许降级
          return c.json(
            {
              success: false,
              error: t('downgrade_not_supported'),
            },
            400
          );
        } else {
          // 价格相同，可能是重新订阅相同计划
          return c.json(
            {
              success: false,
              error: t('already_same_tier_member'),
            },
            400
          );
        }

        if (isUpgrade) {
          // 计算剩余天数的比例退款
          const now = new Date();
          const endDate = new Date(currentSubscription.endDate);
          const totalDays = currentPlan.durationDays;
          const remainingDays = Math.max(
            0,
            Math.floor((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
          );

          // 计算当前计划的剩余价值
          const remainingValue = (currentPlanPrice * remainingDays) / totalDays;

          // 升级差价 = 目标计划价格 - 剩余价值，保留2位小数
          actualAmount = Math.max(0, Math.round((targetPlanPrice - remainingValue) * 100) / 100);
          orderDescription = `升级到${targetPlan.name}（补差价）`;

          console.log('💡 [PAYMENT] 升级价格计算:', {
            currentPlan: currentPlan.name,
            targetPlan: targetPlan.name,
            currentPrice: currentPlanPrice,
            targetPrice: targetPlanPrice,
            remainingDays,
            totalDays,
            remainingValue: remainingValue.toFixed(2),
            actualAmount: actualAmount.toFixed(2),
          });
        }
      }
    }

    // 生成订单号和过期时间
    const orderNo = `ORDER_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 8)
      .toUpperCase()}`;
    const expiresAt = new Date(Date.now() + 30 * 60 * 1000); // 30分钟后过期

    // 构建回调URL
    const callbackDomain = c.env.PAY_CALLBACK_DOMAIN || `https://${c.req.header('host')}`;
    const webDomain = c.env.PAY_WEB_DOMAIN || 'http://***********:3001';

    // 先在数据库中创建支付订单记录
    const paymentOrderData = {
      userId: localUserId,
      planId: targetPlan.id,
      orderNo,
      amount: actualAmount,
      currency: 'CNY',
      paymentMethod,
      description: orderDescription,
      isUpgrade,
      originalAmount: isUpgrade ? Number.parseFloat(targetPlan.price) : undefined,
      currentSubscriptionId: currentSubscription?.id,
      callbackUrl: `${callbackDomain}/api/payment/callback/${paymentMethod}`,
      returnUrl: `${webDomain}/pay/result?orderId=${orderNo}`,
      notifyUrl: `${callbackDomain}/api/payment/callback/${paymentMethod}`,
      expiresAt,
      metadata: {
        planName: targetPlan.name,
        pointsIncluded: targetPlan.pointsIncluded,
        durationDays: targetPlan.durationDays,
        isUpgrade,
        originalPrice: Number.parseFloat(targetPlan.price),
        actualPrice: actualAmount,
        currentSubscriptionId: currentSubscription?.id,
        ...metadata,
      },
    };

    // 保存到数据库
    const [dbOrder] = await createPaymentOrder(c.env, paymentOrderData);

    // 创建支付订单
    const paymentService = createPaymentService(c.env);
    const orderResult = await paymentService.createOrder({
      amount: actualAmount,
      currency: 'CNY',
      description: orderDescription,
      userId: localUserId,
      planId: targetPlan.id,
      paymentMethod: paymentMethod as PaymentMethod,
      metadata: paymentOrderData.metadata,
    });

    if (!orderResult.success) {
      return c.json(
        {
          success: false,
          error: orderResult.error,
        },
        500
      );
    }

    // 构建支付页面URL（如果是真实支付服务，redirectUrl就是支付宝URL；如果是Mock，需要构建我们的支付页面URL）
    const finalRedirectUrl = orderResult.redirectUrl || `${webDomain}/pay/start?orderId=${orderNo}`;

    return c.json({
      success: true,
      data: {
        orderId: orderNo,
        paymentId: orderResult.paymentId || orderNo,
        redirectUrl: finalRedirectUrl,
        amount: actualAmount,
        originalAmount: Number.parseFloat(targetPlan.price),
        planName: targetPlan.name,
        description: orderDescription,
        paymentMethod,
        isUpgrade,
        savings: isUpgrade
          ? Math.round((Number.parseFloat(targetPlan.price) - actualAmount) * 100) / 100
          : 0,
        expiresAt: expiresAt.toISOString(),
      },
    });
  } catch (error) {
    const t = c.get('t');
    console.error('❌ [API] 创建支付订单失败:', error);
    return c.json(
      {
        success: false,
        error: t('create_payment_order_failed'),
      },
      500
    );
  }
});

/**
 * POST /api/payment/callback/alipay
 * 支付宝支付回调处理
 */
app.post('/callback/alipay', async (c) => {
  try {
    console.log('📨 [API] 接收支付宝回调');

    // 支付宝回调是表单数据，不是JSON
    const formData = await c.req.formData();
    const callbackData: any = {};

    for (const [key, value] of formData.entries()) {
      callbackData[key] = value.toString();
    }

    console.log('💳 [ALIPAY-CALLBACK] 回调数据:', {
      outTradeNo: callbackData.out_trade_no,
      tradeStatus: callbackData.trade_status,
      totalAmount: callbackData.total_amount,
    });

    const paymentService = createPaymentService(c.env);
    const callbackResult = await paymentService.processCallback(callbackData, PaymentMethod.ALIPAY);

    if (!callbackResult.success) {
      console.error('❌ [ALIPAY-CALLBACK] 回调处理失败:', callbackResult.message);
      return new Response('fail', { status: 400 });
    }

    // 更新数据库订单状态
    if (
      callbackData.trade_status === 'TRADE_SUCCESS' ||
      callbackData.trade_status === 'TRADE_FINISHED'
    ) {
      await updatePaymentOrderStatus(
        c.env,
        callbackData.out_trade_no,
        'paid',
        callbackData.trade_no,
        new Date()
      );

      // 激活会员订阅
      await activateMembershipByOrderNo(c.env, callbackData.out_trade_no);
    }

    // 支付宝要求返回 'success' 字符串
    return new Response('success');
  } catch (error) {
    console.error('❌ [API] 支付宝回调处理失败:', error);
    return new Response('fail', { status: 500 });
  }
});

/**
 * POST /api/payment/callback/wechat
 * 微信支付回调处理 (预留)
 */
app.post('/callback/wechat', async (c) => {
  try {
    console.log('📨 [API] 接收微信支付回调');

    // 微信支付回调是XML格式，需要解析
    const xmlData = await c.req.text();
    console.log('💳 [WECHAT-CALLBACK] 回调数据:', xmlData.substring(0, 200));

    // TODO: 实现微信支付回调处理
    const t = c.get('t');
    return c.json({ success: false, message: t('wechat_callback_not_implemented') }, 501);
  } catch (error) {
    const t = c.get('t');
    console.error('❌ [API] 微信支付回调处理失败:', error);
    return c.json({ success: false, error: t('wechat_callback_processing_failed') }, 500);
  }
});

/**
 * GET /api/payment/order/:orderId/info
 * 获取支付订单信息 (供支付页面使用)
 */
app.get('/order/:orderId/info', languageMiddleware, async (c) => {
  try {
    const orderId = c.req.param('orderId');
    const t = c.get('t');
    console.log('🔍 [API] 获取支付订单信息:', orderId);

    const orderDetail = await getPaymentOrderWithDetails(c.env, orderId);

    if (!orderDetail) {
      return c.json(
        {
          success: false,
          error: t('order_not_exist'),
        },
        404
      );
    }

    const order = orderDetail.order;
    const plan = orderDetail.plan;
    const pointsPackage = orderDetail.pointsPackage;

    // 检查订单是否过期
    const now = new Date();
    const isExpired = order.expiresAt && new Date(order.expiresAt) < now;

    return c.json({
      success: true,
      data: {
        orderId: order.orderNo,
        amount: Number.parseFloat(order.amount),
        currency: order.currency,
        description: order.description,
        paymentMethod: order.paymentMethod,
        status: isExpired ? 'expired' : order.status,
        isUpgrade: order.isUpgrade,
        originalAmount: order.originalAmount ? Number.parseFloat(order.originalAmount) : null,
        planInfo: plan
          ? {
              id: plan.id,
              name: plan.name,
              description: plan.description,
              pointsIncluded: plan.pointsIncluded,
              durationDays: plan.durationDays,
            }
          : null,
        packageInfo: pointsPackage
          ? {
              id: pointsPackage.id,
              name: pointsPackage.name,
              description: pointsPackage.description,
              points: pointsPackage.points,
              bonusPoints: pointsPackage.bonusPoints || 0,
              totalPoints: pointsPackage.points + (pointsPackage.bonusPoints || 0),
            }
          : null,
        createdAt: order.createdAt,
        expiresAt: order.expiresAt,
        metadata: order.metadata,
      },
    });
  } catch (error) {
    const t = c.get('t');
    console.error('❌ [API] 获取支付订单信息失败:', error);
    return c.json(
      {
        success: false,
        error: t('get_order_info_failed'),
      },
      500
    );
  }
});

/**
 * GET /api/payment/status/:paymentId
 * 查询支付状态
 */
app.get('/status/:paymentId', languageMiddleware, async (c) => {
  try {
    const paymentId = c.req.param('paymentId');
    console.log('🔍 [API] 查询支付状态:', paymentId);

    // 先从数据库查询订单状态
    const dbOrder = await getPaymentOrderByOrderNo(c.env, paymentId);

    if (dbOrder) {
      // 如果订单已支付，直接返回数据库状态
      if (dbOrder.status === 'paid') {
        return c.json({
          success: true,
          data: {
            success: true,
            orderId: dbOrder.orderNo,
            paymentId: dbOrder.externalOrderId || paymentId,
            status: 'completed',
            amount: Number.parseFloat(dbOrder.amount),
            paidAt: dbOrder.paidAt,
          },
        });
      }

      // 如果订单未支付，检查是否过期
      const now = new Date();
      if (dbOrder.expiresAt && new Date(dbOrder.expiresAt) < now) {
        // 更新过期状态
        await updatePaymentOrderStatus(c.env, paymentId, 'expired');
        return c.json({
          success: true,
          data: {
            success: false,
            orderId: dbOrder.orderNo,
            paymentId,
            status: 'expired',
            amount: Number.parseFloat(dbOrder.amount),
          },
        });
      }
    }

    // 调用支付服务查询最新状态
    const paymentService = createPaymentService(c.env);
    const verification = await paymentService.verifyPayment(paymentId);

    // 如果支付服务返回已完成，更新数据库状态
    if (verification.success && verification.status === 'completed' && dbOrder) {
      await updatePaymentOrderStatus(
        c.env,
        paymentId,
        'paid',
        verification.paymentId,
        verification.paidAt
      );

      // 激活会员服务
      await activateMembershipByOrderNo(c.env, paymentId);
    }

    return c.json({
      success: true,
      data: verification,
    });
  } catch (error) {
    const t = c.get('t');
    console.error('❌ [API] 查询支付状态失败:', error);
    return c.json(
      {
        success: false,
        error: t('query_payment_status_failed'),
      },
      500
    );
  }
});

/**
 * GET /api/payment/debug
 * 获取调试信息（仅开发环境）
 */
app.get('/debug', languageMiddleware, async (c) => {
  try {
    const paymentService = createPaymentService(c.env);
    const debugInfo = paymentService.getDebugInfo();

    // 获取Alipay基本配置信息
    let alipayConfig = null;
    try {
      const { getPaymentConfig } = await import('@/lib/payment/payment-config');
      const config = getPaymentConfig(c.env);
      alipayConfig = {
        appId: config.alipay.appId ? `${config.alipay.appId.substring(0, 8)}...` : 'NOT_SET',
        gateway: config.alipay.gateway,
        signType: config.alipay.signType,
        charset: config.alipay.charset,
        version: config.alipay.version,
        isSandbox:
          config.alipay.gateway.includes('sandbox') || config.alipay.gateway.includes('dev'),
      };
    } catch (error) {
      const t = c.get('t');
      console.log(t('cannot_get_alipay_config'), error);
    }

    return c.json({
      success: true,
      data: {
        ...debugInfo,
        alipayConfig,
      },
    });
  } catch (error) {
    const t = c.get('t');
    console.error('❌ [API] 获取调试信息失败:', error);
    return c.json(
      {
        success: false,
        error: t('get_debug_info_failed'),
      },
      500
    );
  }
});

/**
 * 根据订单号处理支付成功（会员订阅或积分包）
 */
async function activateMembershipByOrderNo(env: Env, orderNo: string) {
  try {
    console.log('🎉 [PAYMENT] 根据订单号处理支付成功:', orderNo);

    // 从数据库获取支付订单信息
    const dbOrder = await getPaymentOrderByOrderNo(env, orderNo);

    if (!dbOrder) {
      throw new Error('支付订单不存在');
    }

    if (dbOrder.status !== 'paid') {
      throw new Error('订单未支付，无法激活会员');
    }

    // 检查是会员计划还是积分包
    if (dbOrder.pointsPackageId) {
      // 处理积分包购买
      console.log('🔍 [PAYMENT] 处理积分包购买:', dbOrder.pointsPackageId);
      await processPointsPackagePurchase(env, dbOrder);
      return;
    }

    if (!dbOrder.planId) {
      throw new Error('订单缺少会员计划信息');
    }

    // 获取会员计划信息
    const plan = await getMembershipPlanById(env, dbOrder.planId);
    if (!plan) {
      throw new Error('会员计划不存在');
    }

    const isUpgrade = dbOrder.isUpgrade;
    const currentSubscriptionId = dbOrder.currentSubscriptionId;

    // 计算订阅期限
    const startDate = new Date();
    const endDate = new Date(startDate.getTime() + plan.durationDays * 24 * 60 * 60 * 1000);

    console.log('🔍 [PAYMENT] 准备创建订阅:', {
      userId: dbOrder.userId,
      planId: plan.id,
      paymentId: dbOrder.externalOrderId,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      isUpgrade,
      currentSubscriptionId,
    });

    // 如果是升级，先获取旧订阅信息（在取消之前）
    let oldSubscription = null;
    if (isUpgrade && currentSubscriptionId) {
      console.log('🔍 [PAYMENT] 获取旧订阅信息:', currentSubscriptionId);
      oldSubscription = await getUserActiveSubscription(env, dbOrder.userId);
      console.log('📋 [PAYMENT] 旧订阅信息:', {
        id: oldSubscription?.id,
        planId: oldSubscription?.planId,
        endDate: oldSubscription?.endDate,
      });
    }

    // 如果是升级，取消当前订阅
    if (isUpgrade && currentSubscriptionId) {
      console.log('🔄 [PAYMENT] 取消现有订阅:', currentSubscriptionId);
      await updateSubscriptionStatus(env, currentSubscriptionId, 'cancelled');
    }

    // 创建用户订阅
    const subscription = await createUserSubscription(env, {
      userId: dbOrder.userId,
      planId: plan.id,
      startDate,
      endDate,
      status: 'active' as const,
      paymentId: dbOrder.externalOrderId || orderNo,
      autoRenew: false,
    });

    console.log('✅ [PAYMENT] 订阅创建成功:', subscription[0]);

    // 处理积分（升级 vs 新订阅）
    if (isUpgrade && oldSubscription && currentSubscriptionId) {
      await handleUpgradePoints(
        env,
        dbOrder.userId,
        plan,
        dbOrder.externalOrderId || orderNo,
        oldSubscription,
        currentSubscriptionId
      );
    } else {
      await grantMembershipPoints(env, dbOrder.userId, plan, dbOrder.externalOrderId || orderNo);
    }

    console.log('🎊 [PAYMENT] 会员激活完成:', {
      userId: dbOrder.userId,
      planName: plan.name,
      pointsGranted: plan.pointsIncluded,
      validUntil: endDate,
      isUpgrade,
    });

    // 计算和创建佣金记录
    try {
      const commissionResult = await calculateAndCreateCommission(env, dbOrder.id);
      if (
        commissionResult.success &&
        commissionResult.commissionAmount &&
        commissionResult.commissionAmount > 0
      ) {
        console.log('💰 [COMMISSION] 会员订阅佣金结算成功:', {
          orderId: dbOrder.id,
          orderNo: dbOrder.orderNo,
          inviterId: commissionResult.inviterId,
          commissionAmount: commissionResult.commissionAmount,
        });
      }
    } catch (commissionError) {
      console.error('❌ [COMMISSION] 佣金计算失败，但不影响会员激活:', commissionError);
      // 佣金计算失败不影响会员激活流程
    }
  } catch (error) {
    console.error('❌ [PAYMENT] 根据订单号激活会员订阅失败:', error);
    throw error;
  }
}

/**
 * 激活会员订阅 (兼容旧版本，已弃用，使用 activateMembershipByOrderNo)
 */
async function activateMembership(env: Env, orderId: string, _paymentId: string) {
  console.log('⚠️ [PAYMENT] 使用已弃用的激活方法，重定向到新方法');
  await activateMembershipByOrderNo(env, orderId);
}

/**
 * 发放会员积分
 */
async function grantMembershipPoints(env: Env, userId: string, plan: any, paymentId: string) {
  try {
    // 发放积分并记录交易（updateUserPoints包含了交易记录功能）
    // 注意：sourceId 必须是 UUID，对于支付ID我们放在 description 中
    await updateUserPoints(
      env,
      userId,
      plan.pointsIncluded,
      'earn',
      'subscription',
      undefined, // sourceId 不传递，避免 UUID 格式错误
      `订阅${plan.name}获得积分 (支付ID: ${paymentId})`
    );

    console.log('💰 [PAYMENT] 积分发放成功:', {
      userId,
      pointsGranted: plan.pointsIncluded,
      planName: plan.name,
      paymentId,
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 积分发放失败:', error);
    throw error;
  }
}

/**
 * 处理升级积分（使用积分周期管理器的升级逻辑）
 */
async function handleUpgradePoints(
  env: Env,
  userId: string,
  plan: any,
  paymentId: string,
  oldSubscription: any,
  oldSubscriptionId: string
) {
  try {
    console.log('🚀 [PAYMENT] 处理升级积分:', { userId, planName: plan.name });

    const cycleManager = createPointsCycleManager(env);

    // 映射计划名称到会员等级（支持大小写）
    const levelMap: Record<string, 'FREE' | 'PRO' | 'ELITE' | 'ULTRA'> = {
      free: 'FREE',
      Free: 'FREE',
      pro: 'PRO',
      Pro: 'PRO',
      elite: 'ELITE',
      Elite: 'ELITE',
      ultra: 'ULTRA',
      Ultra: 'ULTRA',
    };

    const toLevel = levelMap[plan.name] || 'PRO';

    // 获取旧订阅的会员等级
    let fromLevel: 'FREE' | 'PRO' | 'ELITE' | 'ULTRA' = 'FREE';
    if (oldSubscription) {
      const oldPlan = await getMembershipPlanById(env, oldSubscription.planId);
      console.log('🔍 [PAYMENT] 旧订阅计划信息:', {
        oldSubscriptionId,
        oldPlanId: oldSubscription.planId,
        oldPlanName: oldPlan?.name,
        oldPlanMapping: oldPlan ? levelMap[oldPlan.name] : 'not found',
      });
      if (oldPlan) {
        fromLevel = levelMap[oldPlan.name] || 'FREE';
      }
    }

    console.log('🔍 [PAYMENT] 目标计划信息:', {
      targetPlanName: plan.name,
      targetPlanMapping: levelMap[plan.name],
    });

    // 计算剩余天数
    const now = new Date();
    const endDate = new Date(oldSubscription.endDate);
    const remainingDays = Math.max(
      0,
      Math.floor((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
    );

    console.log('🔍 [PAYMENT] 升级积分计算参数:', {
      fromLevel,
      toLevel,
      remainingDays,
      userId,
    });

    // 使用升级补差逻辑而不是重置
    const result = await cycleManager.handleMembershipUpgrade(
      userId,
      fromLevel,
      toLevel,
      remainingDays
    );

    console.log('💰 [PAYMENT] 升级积分处理成功:', {
      userId,
      planName: plan.name,
      fromLevel,
      toLevel,
      bonusPoints: result.bonusPoints,
      paymentId,
    });
  } catch (error) {
    console.error('❌ [PAYMENT] 升级积分处理失败:', error);
    throw error;
  }
}

/**
 * 处理积分包购买
 */
async function processPointsPackagePurchase(env: Env, dbOrder: any) {
  try {
    console.log('💰 [PAYMENT] 开始处理积分包购买:', {
      orderId: dbOrder.orderNo,
      userId: dbOrder.userId,
      pointsPackageId: dbOrder.pointsPackageId,
    });

    // 获取积分包信息
    const pointsPackage = await pointsPackageQueries.getPackageById(env, dbOrder.pointsPackageId);
    if (!pointsPackage) {
      throw new Error('积分包不存在');
    }

    console.log('📦 [PAYMENT] 积分包信息:', {
      id: pointsPackage.id,
      name: pointsPackage.name,
      points: pointsPackage.points,
      bonusPoints: pointsPackage.bonusPoints || 0,
    });

    // 计算总积分 = 基础积分 + 赠送积分
    const totalPoints = pointsPackage.points + (pointsPackage.bonusPoints || 0);

    // 先确保用户有积分记录
    console.log('🔍 [PAYMENT] 确保用户积分记录存在:', dbOrder.userId);
    await getUserPoints(env, dbOrder.userId);

    // 发放积分
    console.log('💰 [PAYMENT] 开始发放积分:', {
      userId: dbOrder.userId,
      totalPoints,
      basePoints: pointsPackage.points,
      bonusPoints: pointsPackage.bonusPoints || 0,
    });

    const updatedPoints = await updateUserPoints(
      env,
      dbOrder.userId,
      totalPoints,
      'earn',
      'purchase',
      undefined, // sourceId 不传递，避免 UUID 格式错误
      `购买${pointsPackage.name}获得积分 (订单: ${dbOrder.orderNo})`
    );

    console.log('✅ [PAYMENT] 积分发放成功:', {
      userId: dbOrder.userId,
      newAvailablePoints: updatedPoints.availablePoints,
      newTotalPoints: updatedPoints.totalPoints,
    });

    console.log('✅ [PAYMENT] 积分包处理成功:', {
      userId: dbOrder.userId,
      packageName: pointsPackage.name,
      pointsGranted: totalPoints,
      basePoints: pointsPackage.points,
      bonusPoints: pointsPackage.bonusPoints || 0,
      orderId: dbOrder.orderNo,
    });

    // 计算和创建佣金记录
    try {
      const commissionResult = await calculateAndCreateCommission(env, dbOrder.id);
      if (
        commissionResult.success &&
        commissionResult.commissionAmount &&
        commissionResult.commissionAmount > 0
      ) {
        console.log('💰 [COMMISSION] 积分包购买佣金结算成功:', {
          orderId: dbOrder.id,
          orderNo: dbOrder.orderNo,
          inviterId: commissionResult.inviterId,
          commissionAmount: commissionResult.commissionAmount,
        });
      }
    } catch (commissionError) {
      console.error('❌ [COMMISSION] 佣金计算失败，但不影响积分包处理:', commissionError);
      // 佣金计算失败不影响积分包处理流程
    }
  } catch (error) {
    console.error('❌ [PAYMENT] 积分包处理失败:', error);
    throw error;
  }
}

export { app as paymentRoutes };

import { createMiddleware } from 'hono/factory';
import { verifySupabaseToken } from '@/lib/supabase';
import type { Env } from '@/types/env';
import { ApiError } from '@/types/api';

// 扩展 Hono 上下文类型
type Variables = {
  user?: any;
};

// 判断是否为网络错误
function isNetworkError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toLowerCase() || '';

  return (
    errorMessage.includes('network') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('fetch') ||
    errorCode.includes('network') ||
    error.name === 'AbortError' ||
    error.retryable === true
  );
}

// 认证中间件
export const authMiddleware = createMiddleware<{
  Bindings: Env;
  Variables: Variables;
}>(async (c, next) => {
  // 跳过 OPTIONS 请求的认证检查
  if (c.req.method === 'OPTIONS') {
    await next();
    return;
  }

  const authHeader = c.req.header('Authorization');

  if (!authHeader) {
    throw new ApiError(401, '缺少认证令牌');
  }

  const token = authHeader.replace('Bearer ', '');

  if (!token) {
    throw new ApiError(401, '无效的认证令牌格式');
  }

  try {
    const { user, error } = await verifySupabaseToken(c.env, token);

    if (error) {
      // 区分网络错误和认证错误
      if (isNetworkError(error)) {
        console.error('Supabase网络连接错误:', error);
        throw new ApiError(503, '服务暂时不可用，请稍后重试');
      } else {
        console.warn('认证令牌验证失败:', error.message);
        throw new ApiError(401, '认证令牌无效或已过期');
      }
    }

    if (!user) {
      throw new ApiError(401, '认证令牌无效或已过期');
    }

    // 将用户信息存储到上下文中
    c.set('user', user);

    await next();
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    // 处理其他未预期的错误
    if (isNetworkError(error)) {
      console.error('认证中间件网络错误:', error);
      throw new ApiError(503, '服务暂时不可用，请稍后重试');
    } else {
      console.error('认证中间件未知错误:', error);
      throw new ApiError(401, '认证验证失败');
    }
  }
});

// 可选认证中间件 (不强制要求认证)
export const optionalAuthMiddleware = createMiddleware<{
  Bindings: Env;
  Variables: Variables;
}>(async (c, next) => {
  const authHeader = c.req.header('Authorization');

  if (authHeader) {
    const token = authHeader.replace('Bearer ', '');

    try {
      const { user, error } = await verifySupabaseToken(c.env, token);

      if (!error && user) {
        c.set('user', user);
      } else if (error && !isNetworkError(error)) {
        // 只在非网络错误时记录警告
        console.warn('可选认证失败:', error.message);
      }
    } catch (error) {
      // 可选认证失败时不抛出错误，只是不设置用户信息
      if (isNetworkError(error)) {
        console.warn('可选认证网络错误:', error);
      } else {
        console.warn('可选认证失败:', error);
      }
    }
  }

  await next();
});

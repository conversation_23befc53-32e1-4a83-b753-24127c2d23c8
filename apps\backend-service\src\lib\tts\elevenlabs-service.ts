import type { Env } from '@/types/env';
import { generateUUID } from '@/lib/utils';
import { uploadToR2, IMAGE_UPLOAD_OPTIONS, getR2ConfigFromEnv } from '@/lib/utils/r2-upload';
import { updateMessageAttachments, getMessageById } from '@/lib/db/queries/chat';
import { getVoiceModelByModelId, getVoiceModelById } from '@/lib/db/queries/voice';
// 创建TTS任务的参数
export interface CreateTTSTaskParams {
  text: string;
  messageId?: string;
  chatId?: string;
  voiceModelId?: string; // 声音模型ID
  userId: string;
}

// ElevenLabs API请求参数
interface ElevenLabsRequest {
  text: string;
  model_id?: string;
  voice_settings?: {
    stability: number;
    similarity_boost: number;
    style?: number;
    use_speaker_boost?: boolean;
  };
  seed?: number;
  previous_text?: string;
  next_text?: string;
}

export class ElevenLabsService {
  private env: Env;
  private readonly baseUrl = 'https://api.elevenlabs.io/v1';
  private readonly defaultModel = 'eleven_multilingual_v3';

  constructor(env: Env) {
    this.env = env;

    if (!env.ELEVENLABS_API_KEY) {
      throw new Error('ElevenLabs API Key未配置');
    }
  }

  /**
   * 同步生成音频
   */
  async generateAudioSync(params: CreateTTSTaskParams): Promise<{ audioUrl: string }> {
    console.log('开始同步生成ElevenLabs TTS音频:', params.text.substring(0, 100) + '...');

    try {
      // 获取voice_id（modelId）
      const voiceId = await this.getVoiceId(params.voiceModelId);

      // 调用ElevenLabs API
      const audioBuffer = await this.callElevenLabsAPI(params.text, voiceId);

      // 生成任务ID用于文件命名
      const taskId = generateUUID();

      // 上传到R2
      const audioUrl = await this.uploadAudioToR2(audioBuffer, taskId);

      // 更新消息附件（如果有messageId）
      if (params.messageId && params.chatId) {
        await this.updateMessageWithAudio(params.messageId, audioUrl);
      }

      console.log('ElevenLabs TTS音频生成完成:', audioUrl);

      return { audioUrl };
    } catch (error) {
      console.error('同步生成ElevenLabs TTS音频失败:', error);
      throw error;
    }
  }

  /**
   * 获取voice_id（从数据库中的声音模型获取modelId）
   */
  private async getVoiceId(voiceModelId?: string): Promise<string> {
    if (!voiceModelId) {
      // 使用默认的ElevenLabs voice_id
      return 'JBFqnCBsd6RMkjVDRZzb'; // ElevenLabs默认声音
    }

    try {
      // 检查是否为UUID格式（数据库ID）
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

      if (uuidPattern.test(voiceModelId)) {
        // 是UUID，从数据库中查找声音模型
        const voiceModel = await getVoiceModelById(this.env, voiceModelId);

        if (voiceModel && voiceModel.modelId) {
          console.log(`使用声音模型: ${voiceModel.displayName} (${voiceModel.modelId})`);
          return voiceModel.modelId;
        }
      } else {
        // 不是UUID，可能是旧的modelId格式，尝试按modelId查找
        const voiceModel = await getVoiceModelByModelId(this.env, voiceModelId);

        if (voiceModel && voiceModel.modelId) {
          console.log(`使用声音模型: ${voiceModel.displayName} (${voiceModel.modelId})`);
          return voiceModel.modelId;
        }
      }

      // 如果没找到，直接使用传入的值作为voice_id（兼容性）
      console.log(`未找到声音模型，直接使用voice_id: ${voiceModelId}`);
      return voiceModelId;
    } catch (error) {
      console.error('获取声音模型失败，使用默认voice_id:', error);
      return 'JBFqnCBsd6RMkjVDRZzb';
    }
  }

  /**
   * 调用ElevenLabs API（使用原生fetch）
   */
  private async callElevenLabsAPI(text: string, voiceId: string): Promise<ArrayBuffer> {
    const apiKey = this.env.ELEVENLABS_API_KEY;

    if (!apiKey) {
      throw new Error('ElevenLabs API Key未配置');
    }

    // 构建请求体
    const requestBody: ElevenLabsRequest = {
      text,
      model_id: this.defaultModel,
      voice_settings: {
        stability: 0.5,
        similarity_boost: 0.75,
        style: 0.0,
        use_speaker_boost: true,
      },
    };

    console.log('调用ElevenLabs API:', {
      text: text.substring(0, 100) + '...',
      voiceId,
      model: this.defaultModel,
    });

    const response = await fetch(`${this.baseUrl}/text-to-speech/${voiceId}`, {
      method: 'POST',
      headers: {
        Accept: 'audio/mpeg',
        'Content-Type': 'application/json',
        'xi-api-key': apiKey,
      },
      body: JSON.stringify(requestBody),
      // 增加超时时间
      signal: AbortSignal.timeout(60000), // 60秒超时
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('ElevenLabs API错误:', response.status, errorText);
      throw new Error(`ElevenLabs API错误: ${response.status} ${errorText}`);
    }

    // ElevenLabs API直接返回音频流
    const audioBuffer = await response.arrayBuffer();
    console.log('ElevenLabs API返回音频大小:', audioBuffer.byteLength, 'bytes');

    if (audioBuffer.byteLength === 0) {
      throw new Error('ElevenLabs API返回空音频');
    }

    return audioBuffer;
  }

  /**
   * 上传音频到R2
   */
  private async uploadAudioToR2(audioBuffer: ArrayBuffer, taskId: string): Promise<string> {
    const r2Config = getR2ConfigFromEnv(this.env);
    if (!r2Config) {
      throw new Error('R2配置不完整');
    }

    const fileName = `elevenlabs_tts_${taskId}.mp3`;
    const uploadOptions = {
      ...IMAGE_UPLOAD_OPTIONS,
      fileName,
      folder: 'audio',
      allowedTypes: [
        'audio/mpeg',
        'audio/mp3',
        'audio/wav',
        'audio/ogg',
        'application/octet-stream',
      ],
      maxSize: 50 * 1024 * 1024, // 50MB
    };

    const result = await uploadToR2(audioBuffer, r2Config, uploadOptions);

    if (!result.success || !result.url) {
      throw new Error(result.error || '上传音频文件失败');
    }

    return result.url;
  }

  /**
   * 更新消息附件（追加音频附件，保留现有附件）
   */
  private async updateMessageWithAudio(messageId: string, audioUrl: string): Promise<void> {
    try {
      // 获取现有消息
      const messages = await getMessageById(this.env, { id: messageId });
      const existingMessage = messages[0];

      if (!existingMessage) {
        console.error('消息不存在:', messageId);
        return;
      }

      // 解析现有附件
      let existingAttachments: any[] = [];
      try {
        if (existingMessage.attachments) {
          existingAttachments =
            typeof existingMessage.attachments === 'string'
              ? JSON.parse(existingMessage.attachments)
              : existingMessage.attachments;
        }
      } catch (parseError) {
        console.error('解析现有附件失败:', parseError);
        existingAttachments = [];
      }

      // 检查是否已经有音频附件，避免重复添加
      const hasAudioAttachment = existingAttachments.some((attachment) =>
        attachment.contentType?.startsWith('audio/')
      );

      if (hasAudioAttachment) {
        console.log('消息已有音频附件，跳过添加');
        return;
      }

      // 创建新的音频附件
      const audioAttachment = {
        url: audioUrl,
        name: '语音',
        contentType: 'audio/mpeg',
      };

      // 合并现有附件和新的音频附件
      const updatedAttachments = [...existingAttachments, audioAttachment];

      // 更新消息附件
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments,
      });

      console.log('成功追加音频附件到消息:', messageId);
    } catch (error) {
      console.error('更新消息附件失败:', error);
      // 不抛出错误，因为音频已经生成成功
    }
  }

  /**
   * 获取可用的ElevenLabs声音列表
   */
  async getAvailableVoices(): Promise<any[]> {
    const apiKey = this.env.ELEVENLABS_API_KEY;

    if (!apiKey) {
      throw new Error('ElevenLabs API Key未配置');
    }

    try {
      const response = await fetch(`${this.baseUrl}/voices`, {
        headers: {
          'xi-api-key': apiKey,
        },
      });

      if (!response.ok) {
        throw new Error(`获取声音列表失败: ${response.status}`);
      }

      const data = (await response.json()) as { voices?: any[] };
      return data.voices || [];
    } catch (error) {
      console.error('获取ElevenLabs声音列表失败:', error);
      throw error;
    }
  }
}

import { useState, useEffect, useRef } from 'react'
import type { Devi<PERSON>, Dialogue } from '../types'
import { ControlMode, PlayerState } from '../types'
import { sendBluetoothCommand } from '../utils/bluetoothUtils'

interface UseDeviceControlProps {
  device: Device | null
  activeDialogue: Dialogue | null
  controlMode: ControlMode
  playerState: PlayerState
}

interface UseDeviceControlReturn {
  manualIntensity: { [key: string]: number }
  setManualIntensity: (key: string, intensity: number) => void
  currentIntensity: { [key: string]: number }
  sendCommand: (key: string, intensity: number) => void
}

// 命令队列接口
interface CommandQueueItem {
  key: string
  intensity: number
  timestamp: number
}

/**
 * 设备控制Hook
 * 管理设备控制模式与强度
 */
export const useDeviceControl = ({
  device,
  activeDialogue,
  controlMode,
  playerState
}: UseDeviceControlProps): UseDeviceControlReturn => {
  // 手动设置的强度
  const [manualIntensity, setManualIntensityState] = useState<{
    [key: string]: number
  }>({})

  // 当前实际强度 (根据控制模式决定使用手动还是自动强度)
  const [currentIntensity, setCurrentIntensity] = useState<{
    [key: string]: number
  }>({})

  // 命令队列和处理状态
  const commandQueue = useRef<CommandQueueItem[]>([])
  const isProcessingQueue = useRef<boolean>(false)
  const lastSentCommands = useRef<{ [key: string]: number }>({})

  // 保存暂停前的状态，用于恢复
  const pausedState = useRef<{ [key: string]: number }>({})

  // 处理命令队列
  const processCommandQueue = async () => {
    if (isProcessingQueue.current || commandQueue.current.length === 0) {
      return
    }

    isProcessingQueue.current = true

    try {
      // 按时间戳排序，确保按添加顺序处理
      commandQueue.current.sort((a, b) => a.timestamp - b.timestamp)

      // 获取队列中的第一个命令
      const command = commandQueue.current.shift()

      if (command && device) {
        const { key, intensity } = command

        // 检查是否与上次发送的命令相同，避免重复发送
        if (lastSentCommands.current[key] !== intensity) {
          // 查找对应功能
          const targetFunction = device.func.find(f => f.key === key)
          if (targetFunction) {
            console.log(`从队列发送命令: ${key}, 强度: ${intensity}`)
            await sendBluetoothCommand(key, intensity, device.func)
            // 记录最后发送的命令
            lastSentCommands.current[key] = intensity
          } else {
            console.error('无法发送命令: 找不到功能', key)
          }
        } else {
          console.log(`跳过重复命令: ${key}, 强度: ${intensity}`)
        }
      }
    } catch (error) {
      console.error('处理命令队列时出错:', error)
    } finally {
      isProcessingQueue.current = false

      // 如果队列中还有命令，继续处理
      if (commandQueue.current.length > 0) {
        // 添加小延迟，避免命令发送过快
        setTimeout(processCommandQueue, 200)
      }
    }
  }

  // 发送停止命令到所有设备功能
  const sendStopCommands = () => {
    if (!device) return

    console.log('🛑 发送停止命令到所有设备功能')

    // 保存当前状态用于恢复
    pausedState.current = { ...currentIntensity }

    // 清空命令队列
    commandQueue.current = []

    // 发送停止命令到所有功能
    device.func.forEach(func => {
      sendCommand(func.key, -1) // -1 表示停止
    })
  }

  // 恢复设备状态
  const restoreDeviceState = () => {
    if (!device) return

    console.log('🔄 恢复设备状态')

    // 清空命令队列
    commandQueue.current = []

    // 根据控制模式恢复相应状态
    if (controlMode === ControlMode.AUTO && activeDialogue) {
      // 自动模式：发送对话中的强度
      Object.entries(activeDialogue.intensity).forEach(([key, intensity]) => {
        const isSupported = device.func.some(func => func.key === key)
        if (isSupported && intensity > 0) {
          sendCommand(key, intensity)
        }
      })
    } else if (controlMode === ControlMode.MANUAL) {
      // 手动模式：发送手动设置的强度
      Object.entries(manualIntensity).forEach(([key, intensity]) => {
        const isSupported = device.func.some(func => func.key === key)
        if (isSupported && intensity > 0) {
          sendCommand(key, intensity)
        }
      })
    }
  }

  // 记录上一次的播放状态，用于判断状态变化
  const previousPlayerState = useRef<PlayerState>(PlayerState.IDLE)
  const isInitializing = useRef<boolean>(true)

  // 监听播放状态变化
  useEffect(() => {
    if (!device) return

    const prevState = previousPlayerState.current
    const currentState = playerState

    // 初始化阶段不处理状态变化，避免音频解锁时的误触发
    if (isInitializing.current) {
      if (currentState === PlayerState.PAUSED || currentState === PlayerState.IDLE) {
        isInitializing.current = false
        console.log('🔄 设备控制初始化完成')
      }
      previousPlayerState.current = currentState
      return
    }

    // 只有从 PLAYING 状态变为 PAUSED 时才发送停止命令
    if (prevState === PlayerState.PLAYING && currentState === PlayerState.PAUSED) {
      console.log('🛑 从播放状态暂停，发送停止命令')
      sendStopCommands()
    }
    // 只有从非 PLAYING 状态变为 PLAYING 时才恢复设备状态
    else if (prevState !== PlayerState.PLAYING && currentState === PlayerState.PLAYING) {
      console.log('🔄 开始播放，恢复设备状态')
      // 添加小延迟，确保状态稳定后再恢复
      setTimeout(() => {
        restoreDeviceState()
      }, 300)
    }

    // 更新上一次状态记录
    previousPlayerState.current = currentState
  }, [playerState, device])

  // 发送蓝牙命令
  const sendCommand = (key: string, intensity: number) => {
    if (!device) {
      return
    }

    // 🚫 关键修复：在暂停状态下，只允许发送停止命令（intensity: -1）
    if (playerState === PlayerState.PAUSED && intensity !== -1) {
      console.log(`🚫 暂停状态下阻止发送设备命令: ${key}, 强度: ${intensity}`)
      return
    }

    // 将命令添加到队列
    commandQueue.current.push({
      key,
      intensity,
      timestamp: Date.now()
    })

    // 如果没有正在处理的命令，开始处理队列
    if (!isProcessingQueue.current) {
      processCommandQueue()
    }
  }

  // 设置手动强度
  const setManualIntensity = (key: string, intensity: number) => {
    setManualIntensityState(prev => ({
      ...prev,
      [key]: intensity
    }))

    // 如果当前是手动模式且音频正在播放，立即发送命令
    if (controlMode === ControlMode.MANUAL && device && playerState === PlayerState.PLAYING) {
      // 检查设备是否支持该功能
      const isSupported = device.func.some(func => func.key === key)
      if (isSupported) {
        sendCommand(key, intensity)
      } else {
        console.log(`跳过不支持的功能: ${key}`)
      }
    }
  }

  // 当活跃对话改变时，更新自动强度
  useEffect(() => {
    if (controlMode === ControlMode.AUTO && activeDialogue) {
      const autoIntensity = { ...activeDialogue.intensity }

      // 只在播放状态下发送命令和更新实际强度
      if (playerState === PlayerState.PLAYING) {
        // 更新当前强度
        setCurrentIntensity(autoIntensity)

        // 发送所有功能的命令，使用队列机制
        if (device) {
          // 清空当前队列，确保新的对话命令优先处理
          commandQueue.current = []

          // 将所有命令添加到队列，但只添加设备支持的功能
          Object.entries(autoIntensity).forEach(([key, intensity]) => {
            // 检查设备是否支持该功能
            const isSupported = device.func.some(func => func.key === key)
            if (isSupported) {
              sendCommand(key, intensity)
            } else {
              console.log(`跳过不支持的功能: ${key}`)
            }
          })
        }
      } else {
        // 暂停状态下只更新UI显示，不发送命令
        console.log(`🔄 暂停状态下更新对话强度显示，但不发送命令`)
        setCurrentIntensity(autoIntensity)
      }
    }
  }, [activeDialogue, controlMode, device, playerState])

  // 当控制模式改变时，切换强度来源
  useEffect(() => {
    if (controlMode === ControlMode.MANUAL) {
      // 切换到手动模式时，使用手动设置的强度
      setCurrentIntensity(manualIntensity)

      // 只在播放状态下发送命令
      if (playerState === PlayerState.PLAYING) {
        // 清空当前队列
        commandQueue.current = []

        // 发送所有手动功能的命令
        if (device) {
          Object.entries(manualIntensity).forEach(([key, intensity]) => {
            // 检查设备是否支持该功能
            const isSupported = device.func.some(func => func.key === key)
            if (isSupported) {
              sendCommand(key, intensity)
            } else {
              console.log(`跳过不支持的功能: ${key}`)
            }
          })
        }
      } else {
        console.log(`🔄 暂停状态下切换到手动模式，只更新显示，不发送命令`)
      }
    } else if (controlMode === ControlMode.AUTO && activeDialogue) {
      // 切换到自动模式时，使用对话中的强度
      setCurrentIntensity(activeDialogue.intensity)

      // 只在播放状态下发送命令
      if (playerState === PlayerState.PLAYING) {
        // 清空当前队列
        commandQueue.current = []

        // 发送所有自动功能的命令
        if (device) {
          Object.entries(activeDialogue.intensity).forEach(([key, intensity]) => {
            // 检查设备是否支持该功能
            const isSupported = device.func.some(func => func.key === key)
            if (isSupported) {
              sendCommand(key, intensity)
            } else {
              console.log(`跳过不支持的功能: ${key}`)
            }
          })
        }
      } else {
        console.log(`🔄 暂停状态下切换到自动模式，只更新显示，不发送命令`)
      }
    }
  }, [controlMode, manualIntensity, activeDialogue, device, playerState])

  return {
    manualIntensity,
    setManualIntensity,
    currentIntensity,
    sendCommand
  }
}

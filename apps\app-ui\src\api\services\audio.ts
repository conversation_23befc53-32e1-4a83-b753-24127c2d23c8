import { apiClient } from '../client'

// 音效数据类型定义
export interface AudioEffect {
  id: number
  tags: string[]
  url: string
  duration: number | string
  avg_pitch: number | string
  avg_loudness: number | string
  energy_variation: number | string
}

// 音效库响应类型
export interface AudioLibraryResponse {
  data: AudioEffect[]
  pagination?: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 音效查询参数
export interface AudioQueryParams {
  page?: number
  pageSize?: number
  tags?: string[]
  search?: string
  sortBy?: 'id' | 'duration' | 'avgPitch' | 'avgLoudness' | 'createdAt' | 'usageCount'
  sortOrder?: 'asc' | 'desc'
  isActive?: boolean
  categoryId?: string
}

// 音效搜索参数
export interface AudioSearchParams {
  tags: string[]
  limit?: number
}

// 音效统计信息
export interface AudioStats {
  totalEffects: number
  totalCategories: number
  totalTags: number
  avgDuration: number
}

// 音效分类
export interface AudioCategory {
  id: string
  name: string
  displayName?: string
  description?: string
  parentId?: string
  sortOrder?: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

/**
 * 音频 API 服务
 */
export const audioService = {
  /**
   * 获取音频效果列表
   */
  async getAudioEffects(params: AudioQueryParams = {}): Promise<AudioLibraryResponse> {
    const searchParams = new URLSearchParams()

    if (params.page) searchParams.append('page', params.page.toString())
    if (params.pageSize) searchParams.append('pageSize', params.pageSize.toString())
    if (params.tags && params.tags.length > 0) searchParams.append('tags', params.tags.join(','))
    if (params.search) searchParams.append('search', params.search)
    if (params.sortBy) searchParams.append('sortBy', params.sortBy)
    if (params.sortOrder) searchParams.append('sortOrder', params.sortOrder)
    if (params.isActive !== undefined) searchParams.append('isActive', params.isActive.toString())
    if (params.categoryId) searchParams.append('categoryId', params.categoryId)

    const queryString = searchParams.toString()
    const endpoint = queryString ? `/api/audio?${queryString}` : '/api/audio'

    return apiClient.get<AudioLibraryResponse>(endpoint)
  },

  /**
   * 根据标签搜索音频
   */
  async searchAudioByTags(params: AudioSearchParams): Promise<AudioLibraryResponse> {
    const searchParams = new URLSearchParams()
    searchParams.append('tags', params.tags.join(','))
    if (params.limit) searchParams.append('limit', params.limit.toString())

    return apiClient.get<AudioLibraryResponse>(`/api/audio/search?${searchParams.toString()}`)
  },

  /**
   * 获取随机音频效果
   */
  async getRandomAudioEffects(limit: number = 10): Promise<AudioLibraryResponse> {
    return apiClient.get<AudioLibraryResponse>(`/api/audio/random?limit=${limit}`)
  },

  /**
   * 获取音频效果详情
   */
  async getAudioEffectById(id: number): Promise<{ audioEffect: AudioEffect }> {
    return apiClient.get<{ audioEffect: AudioEffect }>(`/api/audio/${id}`)
  },

  /**
   * 获取所有音频标签
   */
  async getAllAudioTags(): Promise<{ tags: string[] }> {
    return apiClient.get<{ tags: string[] }>('/api/audio/tags')
  },

  /**
   * 获取音频统计信息
   */
  async getAudioStats(): Promise<{ stats: AudioStats }> {
    return apiClient.get<{ stats: AudioStats }>('/api/audio/stats')
  },

  /**
   * 获取音频分类列表
   */
  async getAudioCategories(
    params: { parentId?: string; isActive?: boolean } = {}
  ): Promise<{ categories: AudioCategory[] }> {
    const searchParams = new URLSearchParams()
    if (params.parentId !== undefined) searchParams.append('parentId', params.parentId || 'null')
    if (params.isActive !== undefined) searchParams.append('isActive', params.isActive.toString())

    const queryString = searchParams.toString()
    const endpoint = queryString ? `/api/audio/categories?${queryString}` : '/api/audio/categories'

    return apiClient.get<{ categories: AudioCategory[] }>(endpoint)
  },

  /**
   * 获取音频分类详情
   */
  async getAudioCategoryById(id: string): Promise<{ category: AudioCategory }> {
    return apiClient.get<{ category: AudioCategory }>(`/api/audio/categories/${id}`)
  }
}

import { useNavigate } from 'react-router'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>dalBody,
  Modal<PERSON>ooter,
  useDisclosure,
  Skeleton,
  Card,
  CardBody,
  addToast
} from '@heroui/react'
import { Icon } from '@iconify/react'
import { useEffect, useState, useCallback } from 'react'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/contexts/auth-context'
import { ChatHistoryGroup } from '@/components/chat-history/chat-history-group'
import { groupChatsByDate } from '@/lib/chat-history-utils'
import { apiService } from '@/api'
import type { ChatHistory } from '@/lib/types'
import { useRoleStore } from '@/stores/role-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'
import { useChatHistory } from '@/hooks/use-chat-history'

// 角色信息缓存相关常量和工具函数
const ROLE_CACHE_KEY = 'role_cache'

const getCachedRoles = (): Record<string, { name: string; avatar: string }> => {
  try {
    const cached = sessionStorage.getItem(ROLE_CACHE_KEY)
    return cached ? JSON.parse(cached) : {}
  } catch {
    return {}
  }
}

const setCachedRoles = (roles: Record<string, { name: string; avatar: string }>) => {
  try {
    sessionStorage.setItem(ROLE_CACHE_KEY, JSON.stringify(roles))
  } catch (error) {
    console.warn('保存角色缓存失败:', error)
  }
}

export default function ChatHistoryPage() {
  const navigate = useNavigate()
  const { status } = useAuth()
  const { t } = useTranslation('chat-history')
  const [deleteId, setDeleteId] = useState<string | null>(null)
  const {
    isOpen: showDeleteDialog,
    onOpen: openDeleteDialog,
    onOpenChange: setShowDeleteDialog
  } = useDisclosure()

  // 🚀 使用新的 SQLite 优先的聊天历史 Hook
  const { chatHistory, isLoading, error, deleteChat: deleteChatFromHook } = useChatHistory()

  // 角色信息缓存 - 保持现有的角色缓存逻辑
  const [roleCache, setRoleCache] = useState<Record<string, { name: string; avatar: string }>>(() =>
    getCachedRoles()
  )

  // 使用全局角色状态管理
  const { setRole: setGlobalRole } = useRoleStore()

  // 检查用户是否已登录
  useEffect(() => {
    if (status === 'unauthenticated') {
      navigate('/login')
    }
  }, [status, navigate])

  // 批量获取角色信息 - 保持现有逻辑
  const fetchRoleInfo = useCallback(
    async (roleIds: string[]) => {
      try {
        const newCache = { ...roleCache }
        const idsToFetch = roleIds.filter(id => !roleCache[id])

        // 如果所有角色信息都已缓存，则不再请求
        if (idsToFetch.length === 0) {
          return
        }

        // 1. 使用Zustand store获取用户自定义角色
        let userCharacters: Array<{ id: string; name: string; imageUrl?: string }> = []
        try {
          const userCharactersStore = useUserCharactersStore.getState()
          const characters = await userCharactersStore.fetchUserCharacters()
          userCharacters = (characters || []) as any[]
        } catch (error) {
          console.error(t('log.get_user_roles_failed'), error)
        }

        // 2. 从自定义角色中匹配需要的角色
        const foundInUserChars: Array<{ id: string; name: string; avatar: string }> = []
        const remainingIds: string[] = []

        idsToFetch.forEach(roleId => {
          const char = userCharacters.find(c => c.id === roleId)
          if (char) {
            foundInUserChars.push({
              id: roleId,
              name: char.name,
              avatar: char.imageUrl || '/images/roles/default.jpg'
            })
          } else {
            remainingIds.push(roleId)
          }
        })

        // 3. 对于剩余的ID，批量获取系统角色
        const systemRolePromises = remainingIds.map(async roleId => {
          try {
            const data = await apiService.roles.getSystemRole(roleId)
            return {
              id: roleId,
              name: data.character || t('item.unknown_role'),
              avatar: data.avatar || '/images/roles/default.jpg'
            }
          } catch (error) {
            return {
              id: roleId,
              name: t('item.unknown_role'),
              avatar: '/images/roles/default.jpg'
            }
          }
        })

        const systemRoles = await Promise.all(systemRolePromises)

        // 4. 合并结果并更新缓存
        const allResults = [...foundInUserChars, ...systemRoles]
        allResults.forEach(result => {
          newCache[result.id] = {
            name: result.name,
            avatar: result.avatar
          }
        })

        setRoleCache(newCache)
        setCachedRoles(newCache) // 同步保存到sessionStorage
      } catch (error) {
        console.error(t('log.batch_get_role_info_failed'), error)
      }
    },
    [roleCache]
  )

  // 确保角色信息完整
  const ensureRoleInfo = useCallback(
    async (chats: ChatHistory[]) => {
      const uniqueRoleIds = [...new Set(chats.map(chat => chat.roleId).filter(Boolean))]
      // 获取当前最新的角色缓存（从sessionStorage和当前状态中取最新的）
      const currentRoleCache = { ...getCachedRoles(), ...roleCache }
      const missingRoleIds = uniqueRoleIds.filter(roleId => !currentRoleCache[roleId])

      if (missingRoleIds.length > 0) {
        await fetchRoleInfo(missingRoleIds as string[])
      }
    },
    [roleCache, fetchRoleInfo]
  )

  // 监听聊天历史数据变化，确保角色信息完整
  useEffect(() => {
    if (chatHistory.length > 0) {
      ensureRoleInfo(chatHistory)
    }
  }, [chatHistory, ensureRoleInfo])

  // 处理聊天选择
  const handleChatSelect = async (chatId: string, roleId: string) => {
    try {
      // 先设置全局角色状态
      await setGlobalRole(roleId)
      // 然后导航到聊天页面
      navigate(`/chat/${chatId}?role=${roleId}`)
    } catch (error) {
      console.error(t('log.set_role_failed'), error)
      // 即使设置角色失败，也尝试导航
      navigate(`/chat/${chatId}?role=${roleId}`)
    }
  }

  // 处理删除聊天
  const handleDelete = async () => {
    if (!deleteId) return

    try {
      await deleteChatFromHook(deleteId)
      addToast({
        title: t('delete.success'),
        color: 'primary'
      })
    } catch (error) {
      console.error(t('log.delete_chat_error'), error)
      addToast({
        title: t('delete.error'),
        color: 'danger'
      })
    } finally {
      setShowDeleteDialog()
      setDeleteId(null)
    }
  }

  // 准备删除聊天记录
  const handleDeletePrepare = (chatId: string) => {
    setDeleteId(chatId)
    openDeleteDialog()
  }

  // 取消删除操作
  const handleCancelDelete = () => {
    setShowDeleteDialog()
    setDeleteId(null)
  }

  // 按日期分组聊天历史
  const groupedChats = groupChatsByDate(chatHistory)

  // 格式化时间的函数
  const formatTime = (dateStr: string) => {
    const date = new Date(dateStr)
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="container px-4 py-6 pb-20 max-w-4xl mx-auto">
      <div className="flex items-center gap-3 mb-6">
        <Icon icon="solar:chat-round-line-bold-duotone" className="text-2xl text-primary" />
        <h1 className="text-2xl font-bold">{t('page.title')}</h1>
      </div>

      {/* 错误状态显示 */}
      {error && !isLoading && (
        <Card className="w-full mb-4">
          <CardBody className="flex flex-col items-center justify-center py-8 text-center">
            <div className="w-16 h-16 rounded-full bg-danger/10 flex items-center justify-center mb-4">
              <Icon icon="solar:wifi-router-minimalistic-bold" className="text-3xl text-danger" />
            </div>
            <h2 className="text-xl font-semibold mb-2 text-danger">{t('page.error.title')}</h2>
            <p className="text-default-500 mb-4">{error}</p>
            <Button
              color="primary"
              variant="solid"
              onPress={() => window.location.reload()}
              startContent={<Icon icon="solar:refresh-bold" width={18} />}
            >
              {t('page.error.retry')}
            </Button>
          </CardBody>
        </Card>
      )}

      {isLoading ? (
        <div className="space-y-4">
          {[1, 2, 3].map(placeholder => (
            <Card key={placeholder} className="w-full">
              <CardBody className="p-4">
                <div className="flex items-center gap-4">
                  <Skeleton className="w-12 h-12 rounded-full" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-4 w-3/4" />
                    <Skeleton className="h-3 w-1/2" />
                  </div>
                  <Skeleton className="h-3 w-16" />
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      ) : chatHistory.length > 0 ? (
        <div className="space-y-6">
          {Object.entries(groupedChats).map(([date, chats]) => (
            <ChatHistoryGroup
              key={date}
              title={date}
              chats={chats}
              formatTime={formatTime}
              onSelect={handleChatSelect}
              onDelete={handleDeletePrepare}
              roleCache={roleCache}
            />
          ))}
        </div>
      ) : (
        <Card className="w-full">
          <CardBody className="flex flex-col items-center justify-center py-16 text-center">
            <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
              <Icon icon="solar:chat-round-line-bold" className="text-3xl text-primary" />
            </div>
            <h2 className="text-xl font-semibold mb-2">{t('page.empty.title')}</h2>
            <p className="text-default-500 mb-6">{t('page.empty.description')}</p>
            <Button
              color="primary"
              variant="solid"
              startContent={<Icon icon="solar:user-speak-rounded-bold" width={18} />}
              onPress={() => navigate('/roles/custom')}
            >
              {t('page.empty.explore_roles')}
            </Button>
          </CardBody>
        </Card>
      )}

      {/* 删除确认对话框 */}
      <Modal
        isOpen={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        placement="center"
        backdrop="blur"
      >
        <ModalContent>
          {onClose => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                <div className="flex items-center gap-2">
                  <Icon icon="solar:trash-bin-minimalistic-bold" className="text-danger text-xl" />
                  <span>{t('delete.title')}</span>
                </div>
              </ModalHeader>
              <ModalBody>
                <p className="text-default-600">{t('delete.description')}</p>
              </ModalBody>
              <ModalFooter>
                <Button color="default" variant="flat" onPress={() => handleCancelDelete()}>
                  {t('delete.cancel')}
                </Button>
                <Button
                  color="danger"
                  variant="solid"
                  onPress={() => handleDelete()}
                  startContent={<Icon icon="solar:trash-bin-minimalistic-bold" width={16} />}
                >
                  {t('delete.confirm')}
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </div>
  )
}

CREATE TABLE IF NOT EXISTS "ApiUsage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid,
	"endpoint" varchar(100) NOT NULL,
	"method" varchar(10) NOT NULL,
	"status_code" integer NOT NULL,
	"response_time" integer,
	"ip_address" varchar(45),
	"user_agent" text,
	"error_message" text,
	"metadata" json,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AudioCategory" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(50) NOT NULL,
	"display_name" varchar(100),
	"description" text,
	"parent_id" uuid,
	"sort_order" integer DEFAULT 0,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "AudioCategory_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "AudioEffect" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100),
	"description" text,
	"category_id" uuid,
	"tags" json DEFAULT '[]' NOT NULL,
	"url" text NOT NULL,
	"duration" numeric(10, 2) NOT NULL,
	"avg_pitch" numeric(10, 2),
	"avg_loudness" numeric(10, 2),
	"energy_variation" numeric(10, 4),
	"is_public" boolean DEFAULT true NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"usage_count" integer DEFAULT 0 NOT NULL,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "Character" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"relationship" varchar(50),
	"ethnicity" varchar(50),
	"gender" varchar,
	"age" varchar(20),
	"eye_color" varchar(30),
	"hair_style" varchar(50),
	"hair_color" varchar(30),
	"body_type" varchar(30),
	"breast_size" varchar(20),
	"butt_size" varchar(20),
	"personality" text,
	"clothing" text,
	"voice" varchar(50),
	"keywords" text NOT NULL,
	"prompt" text NOT NULL,
	"image_url" text,
	"category" varchar(50),
	"is_public" boolean DEFAULT false NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "Chat" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"title" text NOT NULL,
	"visibility" varchar DEFAULT 'private' NOT NULL,
	"character_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "GenerationHistory" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"template_id" uuid,
	"original_image_url" text,
	"generated_image_url" text,
	"prompt" text,
	"negative_prompt" text,
	"points_used" integer NOT NULL,
	"status" varchar DEFAULT 'pending' NOT NULL,
	"error_message" text,
	"generation_time" integer,
	"metadata" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "MembershipPlan" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" text,
	"price" numeric(10, 2) NOT NULL,
	"duration_days" integer NOT NULL,
	"points_included" integer NOT NULL,
	"features" json,
	"is_active" boolean DEFAULT true NOT NULL,
	"sort_order" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "Message" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"chat_id" uuid NOT NULL,
	"role" varchar NOT NULL,
	"parts" json NOT NULL,
	"attachments" json DEFAULT '[]' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "PointsPackage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(50) NOT NULL,
	"description" text,
	"points" integer NOT NULL,
	"price" numeric(10, 2) NOT NULL,
	"bonus_points" integer DEFAULT 0,
	"is_active" boolean DEFAULT true NOT NULL,
	"sort_order" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "PointsTransaction" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"transaction_type" varchar NOT NULL,
	"amount" integer NOT NULL,
	"source" varchar NOT NULL,
	"source_id" uuid,
	"description" text,
	"balance_after" integer NOT NULL,
	"metadata" json,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "SubscriptionHistory" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"plan_id" uuid NOT NULL,
	"subscription_id" uuid,
	"action" varchar NOT NULL,
	"amount" numeric(10, 2),
	"points_granted" integer,
	"payment_id" text,
	"metadata" json,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "SystemConfig" (
	"key" varchar(50) PRIMARY KEY NOT NULL,
	"value" json NOT NULL,
	"description" text,
	"is_public" boolean DEFAULT false NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "Template" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(100) NOT NULL,
	"description" text,
	"category" varchar(50),
	"preview_image" text,
	"prompt" text NOT NULL,
	"negative_prompt" text,
	"points_cost" integer DEFAULT 1 NOT NULL,
	"is_premium" boolean DEFAULT false NOT NULL,
	"is_public" boolean DEFAULT true NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"tags" json,
	"settings" json,
	"created_by" uuid,
	"usage_count" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "User" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(64) NOT NULL,
	"supabase_user_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "User_email_unique" UNIQUE("email"),
	CONSTRAINT "User_supabase_user_id_unique" UNIQUE("supabase_user_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "UserPoints" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"total_points" integer DEFAULT 0 NOT NULL,
	"used_points" integer DEFAULT 0 NOT NULL,
	"available_points" integer DEFAULT 0 NOT NULL,
	"last_updated" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "UserPoints_user_id_unique" UNIQUE("user_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "UserProfile" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"nickname" varchar(50),
	"gender" varchar,
	"avatar_url" text,
	"bio" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "UserSession" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"device_info" json,
	"ip_address" varchar(45),
	"user_agent" text,
	"last_active" timestamp DEFAULT now() NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "UserSubscription" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"plan_id" uuid NOT NULL,
	"start_date" timestamp NOT NULL,
	"end_date" timestamp NOT NULL,
	"status" varchar DEFAULT 'pending' NOT NULL,
	"auto_renew" boolean DEFAULT false NOT NULL,
	"payment_id" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ApiUsage" ADD CONSTRAINT "ApiUsage_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AudioEffect" ADD CONSTRAINT "AudioEffect_category_id_AudioCategory_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."AudioCategory"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "AudioEffect" ADD CONSTRAINT "AudioEffect_created_by_User_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Character" ADD CONSTRAINT "Character_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Chat" ADD CONSTRAINT "Chat_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "GenerationHistory" ADD CONSTRAINT "GenerationHistory_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "GenerationHistory" ADD CONSTRAINT "GenerationHistory_template_id_Template_id_fk" FOREIGN KEY ("template_id") REFERENCES "public"."Template"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Message" ADD CONSTRAINT "Message_chat_id_Chat_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."Chat"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PointsTransaction" ADD CONSTRAINT "PointsTransaction_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SubscriptionHistory" ADD CONSTRAINT "SubscriptionHistory_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SubscriptionHistory" ADD CONSTRAINT "SubscriptionHistory_plan_id_MembershipPlan_id_fk" FOREIGN KEY ("plan_id") REFERENCES "public"."MembershipPlan"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "SubscriptionHistory" ADD CONSTRAINT "SubscriptionHistory_subscription_id_UserSubscription_id_fk" FOREIGN KEY ("subscription_id") REFERENCES "public"."UserSubscription"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Template" ADD CONSTRAINT "Template_created_by_User_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "UserPoints" ADD CONSTRAINT "UserPoints_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "UserProfile" ADD CONSTRAINT "UserProfile_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "UserSession" ADD CONSTRAINT "UserSession_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "UserSubscription" ADD CONSTRAINT "UserSubscription_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "UserSubscription" ADD CONSTRAINT "UserSubscription_plan_id_MembershipPlan_id_fk" FOREIGN KEY ("plan_id") REFERENCES "public"."MembershipPlan"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_usage_user_id" ON "ApiUsage" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_usage_endpoint" ON "ApiUsage" USING btree ("endpoint");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_usage_created_at" ON "ApiUsage" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_api_usage_status" ON "ApiUsage" USING btree ("status_code");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_audio_category_active" ON "AudioCategory" USING btree ("is_active") WHERE "AudioCategory"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_audio_category_parent" ON "AudioCategory" USING btree ("parent_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_audio_category" ON "AudioEffect" USING btree ("category_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_audio_public" ON "AudioEffect" USING btree ("is_public") WHERE "AudioEffect"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_audio_active" ON "AudioEffect" USING btree ("is_active") WHERE "AudioEffect"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_audio_created_by" ON "AudioEffect" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_character_user_id" ON "Character" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_character_public" ON "Character" USING btree ("is_public") WHERE "Character"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_character_category" ON "Character" USING btree ("category");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_character_created_at" ON "Character" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_character_public_category" ON "Character" USING btree ("is_public","category") WHERE "Character"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_chat_user_id" ON "Chat" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_chat_created_at" ON "Chat" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_chat_updated_at" ON "Chat" USING btree ("updated_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_chat_user_created" ON "Chat" USING btree ("user_id","created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_generation_user_id" ON "GenerationHistory" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_generation_created_at" ON "GenerationHistory" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_generation_status" ON "GenerationHistory" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_generation_user_created" ON "GenerationHistory" USING btree ("user_id","created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_membership_active" ON "MembershipPlan" USING btree ("is_active") WHERE "MembershipPlan"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_message_chat_id" ON "Message" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_message_created_at" ON "Message" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_message_chat_created" ON "Message" USING btree ("chat_id","created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_points_package_active" ON "PointsPackage" USING btree ("is_active") WHERE "PointsPackage"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_points_transaction_user" ON "PointsTransaction" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_points_transaction_created" ON "PointsTransaction" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_points_transaction_type" ON "PointsTransaction" USING btree ("transaction_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_points_transaction_user_created" ON "PointsTransaction" USING btree ("user_id","created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_subscription_history_user" ON "SubscriptionHistory" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_subscription_history_created" ON "SubscriptionHistory" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_template_public" ON "Template" USING btree ("is_public") WHERE "Template"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_template_category" ON "Template" USING btree ("category");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_template_premium" ON "Template" USING btree ("is_premium");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_template_public_category" ON "Template" USING btree ("is_public","category") WHERE "Template"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_supabase_id" ON "User" USING btree ("supabase_user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_email" ON "User" USING btree ("email");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_points_user_id" ON "UserPoints" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_userprofile_user_id" ON "UserProfile" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_session_user_id" ON "UserSession" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_session_last_active" ON "UserSession" USING btree ("last_active" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_session_active" ON "UserSession" USING btree ("is_active") WHERE "UserSession"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_subscription_user_id" ON "UserSubscription" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_subscription_status" ON "UserSubscription" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_subscription_expires" ON "UserSubscription" USING btree ("end_date");
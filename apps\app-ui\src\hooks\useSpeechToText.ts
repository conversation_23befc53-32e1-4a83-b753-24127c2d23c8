/**
 * 语音转文本 Hook
 * 处理音频上传、转换进度和结果管理
 */

import { useState, useCallback, useRef } from 'react'
import {
  speechToText,
  type SpeechToTextResponse,
  type SpeechToTextError,
  type SpeechToTextOptions,
  type ProgressCallback,
  estimateConversionTime
} from '../api'

export type SpeechToTextStatus =
  | 'idle' // 空闲状态
  | 'uploading' // 上传中
  | 'processing' // 处理中
  | 'completed' // 完成
  | 'error' // 错误状态

export interface SpeechToTextState {
  status: SpeechToTextStatus
  progress: number // 上传进度 0-100
  estimatedTime: number // 预估剩余时间（秒）
  elapsedTime: number // 已用时间（秒）
  result?: {
    text: string
    confidence?: number
    language?: string
    duration?: number
  }
  error?: string
}

export interface SpeechToTextActions {
  convert: (audioBlob: Blob, options?: SpeechToTextOptions) => Promise<string | null>
  cancel: () => void
  reset: () => void
}

export function useSpeechToText(): SpeechToTextState & SpeechToTextActions {
  const [state, setState] = useState<SpeechToTextState>({
    status: 'idle',
    progress: 0,
    estimatedTime: 0,
    elapsedTime: 0
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const startTimeRef = useRef<number>(0)
  const timerRef = useRef<number | null>(null)

  // 更新状态的辅助函数
  const updateState = useCallback((updates: Partial<SpeechToTextState>) => {
    setState(prev => ({ ...prev, ...updates }))
  }, [])

  // 开始计时
  const startTimer = useCallback(
    (estimatedTotalTime: number) => {
      startTimeRef.current = Date.now()

      timerRef.current = window.setInterval(() => {
        const elapsed = (Date.now() - startTimeRef.current) / 1000
        const remaining = Math.max(0, estimatedTotalTime - elapsed)

        updateState({
          elapsedTime: elapsed,
          estimatedTime: remaining
        })

        // 如果预估时间用完，停止计时器
        if (remaining <= 0) {
          if (timerRef.current) {
            clearInterval(timerRef.current)
            timerRef.current = null
          }
        }
      }, 100)
    },
    [updateState]
  )

  // 停止计时
  const stopTimer = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
  }, [])

  // 进度回调
  const handleProgress: ProgressCallback = useCallback(
    progress => {
      updateState({ progress })

      // 上传完成后切换到处理状态
      if (progress >= 100 && state.status === 'uploading') {
        updateState({ status: 'processing' })
      }
    },
    [state.status, updateState]
  )

  // 转换音频为文本
  const convert = useCallback(
    async (audioBlob: Blob, options: SpeechToTextOptions = {}): Promise<string | null> => {
      // 重置状态
      setState({
        status: 'uploading',
        progress: 0,
        estimatedTime: 0,
        elapsedTime: 0,
        result: undefined,
        error: undefined
      })

      // 创建新的 AbortController
      abortControllerRef.current = new AbortController()

      try {
        // 估算转换时间
        const audioDuration = await getAudioDuration(audioBlob)
        const estimatedTime = estimateConversionTime(audioDuration)

        // 开始计时
        startTimer(estimatedTime)

        // 开始转换
        const response = await speechToText(
          audioBlob,
          {
            timeout: Math.max(30000, estimatedTime * 1000 + 10000), // 预估时间 + 10秒缓冲
            ...options
          },
          handleProgress
        )

        // 停止计时
        stopTimer()

        // 检查响应类型
        if ('success' in response && response.success) {
          // 成功响应
          const result = {
            text: response.transcription,
            confidence: 1.0, // Whisper 不提供置信度，设为1.0
            language: 'auto', // Whisper 自动检测语言，设为auto
            duration: response.metadata.audioFileSize / 16000 // 粗略估算时长
          }

          updateState({
            status: 'completed',
            result,
            progress: 100,
            estimatedTime: 0
          })

          return response.transcription
        } else {
          // 错误响应
          const errorMessage = 'error' in response ? response.error : '转换失败'
          updateState({
            status: 'error',
            error: errorMessage,
            progress: 0,
            estimatedTime: 0
          })
          return null
        }
      } catch (error) {
        stopTimer()

        // 检查是否是用户取消
        if (abortControllerRef.current?.signal.aborted) {
          updateState({
            status: 'idle',
            progress: 0,
            estimatedTime: 0,
            error: undefined
          })
          return null
        }

        const errorMessage = error instanceof Error ? error.message : '转换过程中发生错误'
        updateState({
          status: 'error',
          error: errorMessage,
          progress: 0,
          estimatedTime: 0
        })

        console.error('语音转文字失败:', error)
        return null
      } finally {
        abortControllerRef.current = null
      }
    },
    [handleProgress, startTimer, stopTimer, updateState]
  )

  // 取消转换
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    stopTimer()

    updateState({
      status: 'idle',
      progress: 0,
      estimatedTime: 0,
      elapsedTime: 0,
      error: undefined
    })
  }, [stopTimer, updateState])

  // 重置状态
  const reset = useCallback(() => {
    cancel() // 先取消当前操作

    setState({
      status: 'idle',
      progress: 0,
      estimatedTime: 0,
      elapsedTime: 0,
      result: undefined,
      error: undefined
    })
  }, [cancel])

  return {
    status: state.status,
    progress: state.progress,
    estimatedTime: state.estimatedTime,
    elapsedTime: state.elapsedTime,
    result: state.result,
    error: state.error,
    convert,
    cancel,
    reset
  }
}

/**
 * 获取音频时长的辅助函数
 */
async function getAudioDuration(audioBlob: Blob): Promise<number> {
  return new Promise(resolve => {
    const audio = new Audio()
    const url = URL.createObjectURL(audioBlob)

    audio.onloadedmetadata = () => {
      URL.revokeObjectURL(url)
      resolve(audio.duration || 0)
    }

    audio.onerror = () => {
      URL.revokeObjectURL(url)
      resolve(5) // 默认5秒
    }

    audio.src = url
  })
}

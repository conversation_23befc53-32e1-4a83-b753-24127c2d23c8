import { memo, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ImageDisplay } from './image-display'
import { ImageGeneration } from './image-generation'
import { ImageLocalizationService } from './image-localization.service'

interface ImageContainerProps {
  messageId: string
  mode?: 'display' | 'generate' | 'auto'
  prompt?: string
  characterAvatar?: string | null
  chatId?: string
  existingAttachments?: Array<{
    url: string
    name: string
    contentType: string
    metadata?: {
      status?: string
      progress?: number
      timestamp?: string
    }
  }>
}

interface ImageContainerState {
  imageUrl: string | null
  isLoading: boolean
  isLocalizing: boolean
  error: string | null
  mode: 'display' | 'generate'
}

const PureImageContainer = ({
  messageId,
  mode = 'auto',
  prompt,
  characterAvatar,
  chatId,
  existingAttachments
}: ImageContainerProps) => {
  const { t } = useTranslation('chat-v2')
  const [state, setState] = useState<ImageContainerState>({
    imageUrl: null,
    isLoading: true,
    isLocalizing: false,
    error: null,
    mode: mode === 'auto' ? 'display' : mode
  })

  // 简化的本地化检查
  const tryLocalizeImage = async (remoteUrl: string, messageId: string): Promise<string> => {
    try {
      setState(prev => ({ ...prev, isLocalizing: true }))

      const imageLocalizationService = ImageLocalizationService.getInstance()

      // 检查本地是否已有
      const localUrl = await imageLocalizationService.checkLocalImage(messageId)
      if (localUrl) {
        console.log(`📷 [CONTAINER] ${t('image.using_local_image')}`, localUrl)
        return localUrl
      }

      // 本地化现有图片
      const localizedUrl = await imageLocalizationService.localizeExistingImage(
        remoteUrl,
        messageId
      )
      if (localizedUrl) {
        console.log(`📷 [CONTAINER] ${t('image.localization_success')}`, localizedUrl)
        return localizedUrl
      }

      // 降级使用远程URL
      return remoteUrl
    } catch (error) {
      console.warn(`⚠️ [CONTAINER] ${t('image.localization_failed')}`, error)
      return remoteUrl
    } finally {
      setState(prev => ({ ...prev, isLocalizing: false }))
    }
  }

  // 检查现有附件
  useEffect(() => {
    const checkExistingAttachments = async () => {
      console.log(`📷 [CONTAINER] ${t('image.checking_attachments')}`, { messageId, existingAttachments })

      try {
        // 检查本地图片
        const imageLocalizationService = ImageLocalizationService.getInstance()
        const localUrl = await imageLocalizationService.checkLocalImage(messageId)
        if (localUrl) {
          console.log(`📷 [CONTAINER] ${t('image.found_local_image')}`, localUrl)
          setState(prev => ({
            ...prev,
            imageUrl: localUrl,
            isLoading: false,
            mode: 'display'
          }))
          return
        }

        // 检查远程附件
        if (existingAttachments && existingAttachments.length > 0) {
          const imageAttachment = existingAttachments.find(
            att =>
              att.contentType.startsWith('image/') &&
              !att.contentType.startsWith('image/generating')
          )

          if (imageAttachment) {
            console.log(`📷 [CONTAINER] ${t('image.found_remote_image')}`, imageAttachment.url)

            // 先显示远程图片
            setState(prev => ({
              ...prev,
              imageUrl: imageAttachment.url,
              isLoading: false,
              mode: 'display'
            }))

            // 后台本地化
            const finalUrl = await tryLocalizeImage(imageAttachment.url, messageId)
            if (finalUrl !== imageAttachment.url) {
              setState(prev => ({ ...prev, imageUrl: finalUrl }))
            }
            return
          }
        }

        // 没有现有图片，判断是否需要生成
        if (prompt && (mode === 'auto' || mode === 'generate')) {
          console.log(`📷 [CONTAINER] ${t('image.need_generation')}`)
          setState(prev => ({
            ...prev,
            isLoading: false,
            mode: 'generate'
          }))
        } else {
          console.log(`📷 [CONTAINER] ${t('image.no_image_no_generation')}`)
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: t('image.image_not_found')
          }))
        }
      } catch (error) {
        console.error(`❌ [CONTAINER] ${t('image.checking_failed')}`, error)
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: t('image.image_check_failed')
        }))
      }
    }

    checkExistingAttachments()
  }, [messageId, existingAttachments, prompt, mode])

  // 渲染逻辑
  if (state.isLoading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2 text-sm text-gray-500">{t('image.checking_image')}</span>
      </div>
    )
  }

  if (state.error) {
    return (
      <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
        <div className="text-red-500 text-sm">{state.error}</div>
      </div>
    )
  }

  // 显示模式
  if (state.mode === 'display' && state.imageUrl) {
    return (
      <div className="relative">
        <ImageDisplay imageUrl={state.imageUrl} alt={t('image.ai_generated_image')} />
      </div>
    )
  }

  // 生成模式
  if (state.mode === 'generate' && prompt) {
    return (
      <ImageGeneration
        prompt={prompt}
        messageId={messageId}
        characterAvatar={characterAvatar}
        chatId={chatId}
        existingAttachments={existingAttachments}
      />
    )
  }

  return null
}

export const ImageContainer = memo(PureImageContainer, (prevProps, nextProps) => {
  return (
    prevProps.messageId === nextProps.messageId &&
    prevProps.mode === nextProps.mode &&
    prevProps.prompt === nextProps.prompt &&
    prevProps.characterAvatar === nextProps.characterAvatar &&
    prevProps.chatId === nextProps.chatId &&
    JSON.stringify(prevProps.existingAttachments) === JSON.stringify(nextProps.existingAttachments)
  )
})

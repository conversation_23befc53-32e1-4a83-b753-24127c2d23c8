import { Resend } from 'resend'
import type { Env } from '@/types/env'

const name = 'PleasureHub'

// 创建 Resend 客户端
function createResendClient(env: Env) {
  return new Resend(env.RESEND_API_KEY)
}

// 生成6位数字验证码
export function generateVerificationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// 注册验证码邮件模板
function getRegistrationEmailTemplate(env: Env, code: string): { html: string; text: string } {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>邮箱验证</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
      <div style="background: white; border-radius: 12px; padding: 40px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="font-size: 24px; font-weight: bold; color: #2563eb; margin-bottom: 10px;">${name}</div>
          <h1 style="font-size: 20px; font-weight: 600; color: #1f2937; margin-bottom: 10px; margin-top: 0;">验证您的邮箱地址</h1>
          <p style="color: #6b7280; font-size: 14px; margin: 0;">请使用以下验证码完成注册</p>
        </div>

        <div style="background: #f3f4f6; border: 2px dashed #d1d5db; border-radius: 8px; padding: 20px; text-align: center; margin: 30px 0;">
          <div style="font-size: 32px; font-weight: bold; color: #2563eb; letter-spacing: 4px; font-family: 'Courier New', monospace;">${code}</div>
          <div style="color: #6b7280; font-size: 12px; margin-top: 8px;">验证码</div>
        </div>

        <div style="background: #eff6ff; border-left: 4px solid #2563eb; padding: 16px; margin: 20px 0; border-radius: 0 8px 8px 0;">
          <p style="margin: 0; color: #1e40af; font-size: 14px; font-weight: bold;">使用说明：</p>
          <p style="margin: 8px 0 0 0; color: #1e40af; font-size: 14px;">1. 请在注册页面输入上方的6位验证码</p>
          <p style="margin: 4px 0 0 0; color: #1e40af; font-size: 14px;">2. 验证码有效期为10分钟</p>
          <p style="margin: 4px 0 0 0; color: #1e40af; font-size: 14px;">3. 如果验证码过期，请重新获取</p>
        </div>

        <div style="background: #fef3cd; border: 1px solid #fbbf24; border-radius: 6px; padding: 12px; margin: 20px 0; font-size: 13px; color: #92400e;">
          <strong>安全提醒：</strong> 请勿将此验证码分享给他人。我们的工作人员不会主动向您索要验证码。
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">
          <p style="margin: 0;">此邮件由系统自动发送，请勿回复。</p>
          <p style="margin: 4px 0 0 0;">如有疑问，请联系客服支持。</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    ${name} - 邮箱验证
    
    您的验证码是：${code}
    
    请在注册页面输入此验证码完成注册。
    验证码有效期为10分钟。
    
    如果您没有请求此验证码，请忽略此邮件。
    
    此邮件由系统自动发送，请勿回复。
  `

  return { html, text }
}

// 登录验证码邮件模板
function getLoginEmailTemplate(env: Env, code: string): { html: string; text: string } {
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>登录验证</title>
    </head>
    <body style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f8fafc;">
      <div style="background: white; border-radius: 12px; padding: 40px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
        <div style="text-align: center; margin-bottom: 30px;">
          <div style="font-size: 24px; font-weight: bold; color: #059669; margin-bottom: 10px;">${name}</div>
          <h1 style="font-size: 20px; font-weight: 600; color: #1f2937; margin-bottom: 10px; margin-top: 0;">登录验证码</h1>
          <p style="color: #6b7280; font-size: 14px; margin: 0;">请使用以下验证码完成登录</p>
        </div>

        <div style="background: #f0fdf4; border: 2px dashed #22c55e; border-radius: 8px; padding: 20px; text-align: center; margin: 30px 0;">
          <div style="font-size: 32px; font-weight: bold; color: #059669; letter-spacing: 4px; font-family: 'Courier New', monospace;">${code}</div>
          <div style="color: #6b7280; font-size: 12px; margin-top: 8px;">登录验证码</div>
        </div>

        <div style="background: #f0fdf4; border-left: 4px solid #22c55e; padding: 16px; margin: 20px 0; border-radius: 0 8px 8px 0;">
          <p style="margin: 0; color: #166534; font-size: 14px; font-weight: bold;">使用说明：</p>
          <p style="margin: 8px 0 0 0; color: #166534; font-size: 14px;">1. 请在登录页面输入上方的6位验证码</p>
          <p style="margin: 4px 0 0 0; color: #166534; font-size: 14px;">2. 验证码有效期为10分钟</p>
          <p style="margin: 4px 0 0 0; color: #166534; font-size: 14px;">3. 如果验证码过期，请重新获取</p>
        </div>

        <div style="background: #fef3cd; border: 1px solid #fbbf24; border-radius: 6px; padding: 12px; margin: 20px 0; font-size: 13px; color: #92400e;">
          <strong>安全提醒：</strong> 如果您没有尝试登录，请忽略此邮件。建议您定期更改密码以保护账户安全。
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 12px;">
          <p style="margin: 0;">此邮件由系统自动发送，请勿回复。</p>
          <p style="margin: 4px 0 0 0;">如有疑问，请联系客服支持。</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    ${name} - 登录验证
    
    您的登录验证码是：${code}
    
    请在登录页面输入此验证码完成登录。
    验证码有效期为10分钟。
    
    如果您没有尝试登录，请忽略此邮件。
    
    此邮件由系统自动发送，请勿回复。
  `

  return { html, text }
}

// 发送注册验证码邮件
export async function sendRegistrationCodeEmail(
  env: Env,
  email: string,
  code: string
): Promise<{ success: boolean; error?: any }> {
  try {
    const resend = createResendClient(env)
    const { html, text } = getRegistrationEmailTemplate(env, code)

    const { data, error } = await resend.emails.send({
      from: `${name} <${env.RESEND_FROM_EMAIL}>`,
      to: [email],
      subject: '验证您的邮箱地址 - 完成注册',
      html,
      text
    })

    if (error) {
      console.error('Resend 发送注册验证码邮件失败:', error)
      return { success: false, error }
    }

    console.log('注册验证码邮件发送成功:', data)
    return { success: true }
  } catch (error) {
    console.error('发送注册验证码邮件时出现错误:', error)
    return { success: false, error }
  }
}

// 发送登录验证码邮件
export async function sendLoginCodeEmail(
  env: Env,
  email: string,
  code: string
): Promise<{ success: boolean; error?: any }> {
  try {
    const resend = createResendClient(env)
    const { html, text } = getLoginEmailTemplate(env, code)

    const { data, error } = await resend.emails.send({
      from: `${name} <${env.RESEND_FROM_EMAIL}>`,
      to: [email],
      subject: '登录验证码 - 安全登录',
      html,
      text
    })

    if (error) {
      console.error('Resend 发送登录验证码邮件失败:', error)
      return { success: false, error }
    }

    console.log('登录验证码邮件发送成功:', data)
    return { success: true }
  } catch (error) {
    console.error('发送登录验证码邮件时出现错误:', error)
    return { success: false, error }
  }
}

// 保留原有函数以保持向后兼容（用于注册）
export async function sendVerificationCodeEmail(
  env: Env,
  email: string,
  code: string
): Promise<{ success: boolean; error?: any }> {
  return sendRegistrationCodeEmail(env, email, code)
}

// 验证码存储接口（使用 KV 存储）
export async function storeVerificationCode(
  env: Env,
  email: string,
  code: string,
  expirationMinutes = 10
): Promise<void> {
  const key = `verification_code:${email}`
  const value = {
    code,
    createdAt: Date.now(),
    expiresAt: Date.now() + expirationMinutes * 60 * 1000
  }

  // 存储到 KV，设置过期时间
  await env.CACHE.put(key, JSON.stringify(value), {
    expirationTtl: expirationMinutes * 60
  })
}

// 验证验证码
export async function verifyStoredCode(
  env: Env,
  email: string,
  inputCode: string
): Promise<{ valid: boolean; error?: string }> {
  const key = `verification_code:${email}`

  try {
    const stored = await env.CACHE.get(key)

    if (!stored) {
      return { valid: false, error: '验证码不存在或已过期' }
    }

    const data = JSON.parse(stored)

    // 检查是否过期
    if (Date.now() > data.expiresAt) {
      // 删除过期的验证码
      await env.CACHE.delete(key)
      return { valid: false, error: '验证码已过期' }
    }

    // 验证码匹配
    if (data.code === inputCode) {
      // 验证成功后删除验证码（一次性使用）
      await env.CACHE.delete(key)
      return { valid: true }
    }

    return { valid: false, error: '验证码错误' }
  } catch (error) {
    console.error('验证验证码时出现错误:', error)
    return { valid: false, error: '验证过程中出现错误' }
  }
}

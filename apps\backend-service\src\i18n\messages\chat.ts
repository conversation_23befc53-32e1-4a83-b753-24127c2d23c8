// 聊天相关消息
export const chatMessages = {
  zh: {
    // 通用消息
    user_not_found: '未找到用户信息',
    chat_not_found: '聊天不存在',
    chat_no_permission: '无权限访问此聊天',
    message_not_found: '消息不存在',
    message_update_failed: '消息不存在或更新失败',

    // 验证错误
    message_id_required: '消息ID不能为空',
    message_id_invalid: '消息ID格式无效',
    attachments_invalid: '附件格式无效',

    // 操作成功消息
    chat_list_success: '获取聊天列表成功',
    chat_create_success: '创建聊天成功',
    chat_get_success: '获取聊天详情成功',
    chat_update_success: '更新聊天成功',
    chat_delete_success: '聊天删除成功',
    message_attachments_update_success: '消息附件更新成功',

    // 操作失败消息
    chat_list_failed: '获取聊天列表失败',
    chat_create_failed: '创建聊天失败',
    chat_get_failed: '获取聊天详情失败',
    chat_update_failed: '更新聊天失败',
    chat_delete_failed: '删除聊天失败',
    message_attachments_update_failed: '更新消息附件失败',

    // 角色相关
    character_not_found: '角色不存在',
    character_no_permission: '无权限使用此角色',

    // 系统相关
    system_config_incomplete: '系统配置不完整，请联系管理员',
    langchain_config_incomplete: 'LangChain 系统配置不完整，请联系管理员',
    langchain_stream_failed: '流式聊天 API 失败',
    chat_messages_get_failed: '获取聊天消息失败',
    chat_access_denied: '聊天不存在或无权限访问，返回空数据',

    // 内容违规相关
    content_violation: '内容违规，请修改后重试'
  },
  'zh-TW': {
    // 通用訊息
    user_not_found: '未找到使用者資訊',
    chat_not_found: '聊天不存在',
    chat_no_permission: '無權限存取此聊天',
    message_not_found: '訊息不存在',
    message_update_failed: '訊息不存在或更新失敗',

    // 驗證錯誤
    message_id_required: '訊息ID不能為空',
    message_id_invalid: '訊息ID格式無效',
    attachments_invalid: '附件格式無效',

    // 操作成功訊息
    chat_list_success: '取得聊天列表成功',
    chat_create_success: '建立聊天成功',
    chat_get_success: '取得聊天詳情成功',
    chat_update_success: '更新聊天成功',
    chat_delete_success: '聊天刪除成功',
    message_attachments_update_success: '訊息附件更新成功',

    // 操作失敗訊息
    chat_list_failed: '取得聊天列表失敗',
    chat_create_failed: '建立聊天失敗',
    chat_get_failed: '取得聊天詳情失敗',
    chat_update_failed: '更新聊天失敗',
    chat_delete_failed: '刪除聊天失敗',
    message_attachments_update_failed: '更新訊息附件失敗',

    // 角色相關
    character_not_found: '角色不存在',
    character_no_permission: '無權限使用此角色',

    // 系統相關
    system_config_incomplete: '系統設定不完整，請聯絡客服',
    langchain_config_incomplete: 'LangChain 系統設定不完整，請聯絡客服',
    langchain_stream_failed: '串流聊天 API 失敗',
    chat_messages_get_failed: '取得聊天訊息失敗',
    chat_access_denied: '此聊天不可用或您沒有存取權限',

    // 內容違規相關
    content_violation: '內容違規，請修改後重試'
  },
  ja: {
    // 共通メッセージ
    user_not_found: 'ユーザー情報が見つかりません',
    chat_not_found: 'チャットが存在しません',
    chat_no_permission: 'このチャットにアクセスする権限がありません',
    message_not_found: 'メッセージが存在しません',
    message_update_failed: 'メッセージが存在しないか、更新に失敗しました',

    // 検証エラー
    message_id_required: 'メッセージIDを入力してください',
    message_id_invalid: 'メッセージIDの形式が無効です',
    attachments_invalid: '添付ファイルの形式が無効です',

    // 操作成功メッセージ
    chat_list_success: 'チャットリストの取得に成功しました',
    chat_create_success: 'チャットの作成に成功しました',
    chat_get_success: 'チャット詳細の取得に成功しました',
    chat_update_success: 'チャットの更新に成功しました',
    chat_delete_success: 'チャットの削除に成功しました',
    message_attachments_update_success: 'メッセージ添付ファイルの更新に成功しました',

    // 操作失敗メッセージ
    chat_list_failed: 'チャットリストの取得に失敗しました',
    chat_create_failed: 'チャットの作成に失敗しました',
    chat_get_failed: 'チャット詳細の取得に失敗しました',
    chat_update_failed: 'チャットの更新に失敗しました',
    chat_delete_failed: 'チャットの削除に失敗しました',
    message_attachments_update_failed: 'メッセージ添付ファイルの更新に失敗しました',

    // キャラクター関連
    character_not_found: 'キャラクターが存在しません',
    character_no_permission: 'このキャラクターを使用する権限がありません',

    // システム関連
    system_config_incomplete: 'システム設定が不完全です。管理者にお問い合わせください',
    langchain_config_incomplete: 'LangChainシステム設定が不完全です。管理者にお問い合わせください',
    langchain_stream_failed: 'ストリーミングチャットAPIに失敗しました',
    chat_messages_get_failed: 'チャットメッセージの取得に失敗しました',
    chat_access_denied: 'このチャットは利用できないか、アクセス権限がありません',

    // コンテンツ違反関連
    content_violation: 'コンテンツ違反です。修正後に再試行してください'
  },
  es: {
    // Mensajes comunes
    user_not_found: 'No se encontró la información del usuario',
    chat_not_found: 'El chat no existe',
    chat_no_permission: 'No tiene permisos para acceder a este chat',
    message_not_found: 'El mensaje no existe',
    message_update_failed: 'El mensaje no existe o la actualización falló',

    // Errores de validación
    message_id_required: 'El ID del mensaje no puede estar vacío',
    message_id_invalid: 'El formato del ID del mensaje es inválido',
    attachments_invalid: 'El formato del archivo adjunto es inválido',

    // Mensajes de éxito en operaciones
    chat_list_success: 'Lista de chats obtenida correctamente',
    chat_create_success: 'Chat creado correctamente',
    chat_get_success: 'Detalles del chat obtenidos correctamente',
    chat_update_success: 'Chat actualizado correctamente',
    chat_delete_success: 'Chat eliminado correctamente',
    message_attachments_update_success: 'Archivos adjuntos del mensaje actualizados correctamente',

    // Mensajes de error en operaciones
    chat_list_failed: 'Error al obtener la lista de chats',
    chat_create_failed: 'Error al crear el chat',
    chat_get_failed: 'Error al obtener los detalles del chat',
    chat_update_failed: 'Error al actualizar el chat',
    chat_delete_failed: 'Error al eliminar el chat',
    message_attachments_update_failed: 'Error al actualizar los archivos adjuntos del mensaje',

    // Relacionado con personajes
    character_not_found: 'El personaje no existe',
    character_no_permission: 'No tiene permisos para usar este personaje',

    // Relacionado con el sistema
    system_config_incomplete:
      'La configuración del sistema está incompleta, por favor contacte al soporte',
    langchain_config_incomplete:
      'La configuración del sistema LangChain está incompleta, por favor contacte al soporte',
    langchain_stream_failed: 'Error en la API de chat en tiempo real',
    chat_messages_get_failed: 'Error al obtener los mensajes del chat',
    chat_access_denied: 'Este chat no está disponible o no tiene permisos para acceder a él',

    // Relacionado con violaciones de contenido
    content_violation: 'Contenido en violación, por favor modifique y vuelva a intentar'
  },
  en: {
    // Common messages
    user_not_found: 'User information not found',
    chat_not_found: 'Chat does not exist',
    chat_no_permission: 'No permission to access this chat',
    message_not_found: 'Message does not exist',
    message_update_failed: 'Message does not exist or update failed',

    // Validation errors
    message_id_required: 'Message ID cannot be empty',
    message_id_invalid: 'Invalid message ID format',
    attachments_invalid: 'Invalid attachment format',

    // Success messages
    chat_list_success: 'Chat list retrieved successfully',
    chat_create_success: 'Chat created successfully',
    chat_get_success: 'Chat details retrieved successfully',
    chat_update_success: 'Chat updated successfully',
    chat_delete_success: 'Chat deleted successfully',
    message_attachments_update_success: 'Message attachments updated successfully',

    // Failure messages
    chat_list_failed: 'Failed to retrieve chat list',
    chat_create_failed: 'Failed to create chat',
    chat_get_failed: 'Failed to retrieve chat details',
    chat_update_failed: 'Failed to update chat',
    chat_delete_failed: 'Failed to delete chat',
    message_attachments_update_failed: 'Failed to update message attachments',

    // Character related
    character_not_found: 'Character does not exist',
    character_no_permission: 'No permission to use this character',

    // System related
    system_config_incomplete: 'System configuration incomplete, please contact support',
    langchain_config_incomplete:
      'LangChain system configuration incomplete, please contact support',
    langchain_stream_failed: 'Streaming chat API failed',
    chat_messages_get_failed: 'Failed to retrieve chat messages',
    chat_access_denied: "This chat is not available or you don't have permission to access it",

    // Content violation related
    content_violation: 'Content violation detected, please modify and try again'
  }
}

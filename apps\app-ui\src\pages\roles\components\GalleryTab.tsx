import { useState, useEffect } from 'react'
import { Skeleton } from '@heroui/react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { usePhotoHistory } from '@/stores/photo-generation-store'
import { PullToRefresh } from '@/components/ui/PullToRefresh'
import { PhotoViewer } from './PhotoViewer'
import type { DisplayRole } from '@/lib/types'
import type { PhotoHistory } from '@/stores/photo-generation-store'

interface GalleryTabProps {
  roleId?: string
  role: DisplayRole | null
}

export function GalleryTab({ roleId, role }: GalleryTabProps) {
  const { history, fetchHistory } = usePhotoHistory()
  const [loading, setLoading] = useState(true)
  const [selectedPhoto, setSelectedPhoto] = useState<PhotoHistory | null>(null)
  const [showPhotoViewer, setShowPhotoViewer] = useState(false)

  // 获取写真历史记录
  useEffect(() => {
    if (roleId) {
      loadHistory()
    }
  }, [roleId])

  const loadHistory = async () => {
    if (!roleId) return

    try {
      setLoading(true)
      await fetchHistory(roleId)
    } catch (error) {
      console.error('获取写真历史失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 刷新历史记录
  const handleRefresh = async () => {
    if (roleId) {
      await fetchHistory(roleId)
    }
  }

  // 点击图片查看大图
  const handlePhotoClick = (photo: PhotoHistory) => {
    setSelectedPhoto(photo)
    setShowPhotoViewer(true)
  }

  // 关闭图片查看器
  const handleCloseViewer = () => {
    setShowPhotoViewer(false)
    setSelectedPhoto(null)
  }

  if (loading) {
    return (
      <div className="py-4">
        <div className="grid grid-cols-2 gap-4">
          {[...Array(6)].map((_, index) => (
            <Skeleton key={index} className="aspect-[3/4] rounded-lg" />
          ))}
        </div>
      </div>
    )
  }

  return (
    <>
      <PullToRefresh
        onRefresh={handleRefresh}
        pullText="下拉刷新写真记录"
        releaseText="释放刷新写真记录"
        refreshingText="正在刷新写真记录..."
      >
        <div className="py-4">
          {!Array.isArray(history) || history.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-20">
              <Icon icon="solar:gallery-bold" className="w-16 h-16 text-gray-600 mb-4" />
              <div className="text-gray-400 text-lg">暂无写真记录</div>
              <div className="text-gray-500 text-sm mt-2">{role?.character} 还没有生成过写真</div>
            </div>
          ) : (
            <div className="grid grid-cols-2 gap-4">
              {history.map((photo, index) => (
                <motion.div
                  key={photo.id}
                  className="relative aspect-[3/4] rounded-lg overflow-hidden cursor-pointer group"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handlePhotoClick(photo)}
                >
                  <motion.img
                    layoutId={`photo-${photo.id}`}
                    src={photo.generatedImageUrl}
                    alt={photo.templateName}
                    className="w-full h-full object-cover"
                  />

                  {/* 悬停遮罩 */}
                  <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200" />

                  {/* 底部信息 */}
                  <div className="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-black/80 to-transparent">
                    <div className="absolute bottom-0 left-0 right-0 p-3">
                      <div className="text-white text-sm font-medium line-clamp-1">
                        {photo.templateName}
                      </div>
                      <div className="text-gray-300 text-xs">
                        {new Date(photo.createdAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>

                  {/* 状态指示器 */}
                  {photo.status === 'pending' && (
                    <div className="absolute top-2 right-2">
                      <div className="bg-blue-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                        <Icon icon="solar:refresh-linear" className="w-3 h-3 mr-1 animate-spin" />
                        生成中
                      </div>
                    </div>
                  )}

                  {photo.status === 'failed' && (
                    <div className="absolute top-2 right-2">
                      <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-full flex items-center">
                        <Icon icon="solar:close-circle-bold" className="w-3 h-3 mr-1" />
                        失败
                      </div>
                    </div>
                  )}

                  {/* 积分消耗标识 */}
                  {photo.pointsUsed && (
                    <div className="absolute top-2 left-2">
                      <div className="bg-black/60 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full flex items-center">
                        <Icon icon="solar:star-bold" className="w-3 h-3 text-yellow-400 mr-1" />
                        {photo.pointsUsed}
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </PullToRefresh>

      {/* 全屏图片查看器 */}
      <PhotoViewer isVisible={showPhotoViewer} photo={selectedPhoto} onClose={handleCloseViewer} />
    </>
  )
}

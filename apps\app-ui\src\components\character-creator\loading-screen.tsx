import { Progress, Card, CardBody, Chip } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'

interface CharacterGenerationLoadingProps {
  progress?: number
  estimatedSteps?: number
  completedSteps?: number
  taskId?: string | null
}

export function CharacterGenerationLoading({
  progress = 0,
  estimatedSteps = 0,
  completedSteps = 0,
  taskId
}: CharacterGenerationLoadingProps) {
  const { t } = useTranslation(['customRole'])

  // 根据进度确定当前阶段
  const getCurrentStage = () => {
    if (progress === 0) return 0
    if (progress < 25) return 1
    if (progress < 50) return 2
    if (progress < 75) return 3
    if (progress < 100) return 4
    return 5
  }

  const currentStage = getCurrentStage()

  const stages = [
    { text: t('customRole:loading.stages.preparing_canvas'), color: 'default' },
    { text: t('customRole:loading.stages.drawing_character_outline'), color: 'primary' },
    { text: t('customRole:loading.stages.adding_details'), color: 'secondary' },
    { text: t('customRole:loading.stages.carefully_drawing'), color: 'success' },
    { text: t('customRole:loading.stages.final_touches'), color: 'warning' },
    { text: t('customRole:loading.stages.almost_done'), color: 'success' }
  ]

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm z-50 flex items-center justify-center mx-4">
      <Card className="bg-gradient-to-br from-purple-900/80 to-blue-900/80 border border-white/10 shadow-xl max-w-md w-full">
        <CardBody className="p-8">
          <div className="text-center space-y-6">
            <div className="relative">
              {/* 魔法光环动画 */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="size-24 rounded-full bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 opacity-30 animate-pulse" />
              </div>

              {/* 魔法棒图标 */}
              <div className="animate-bounce relative">
                <Icon icon="solar:magic-stick-3-bold" className="mx-auto size-16 text-white" />
              </div>

              {/* 装饰性星星 */}
              <div className="absolute top-0 left-1/4 animate-pulse">
                <Icon icon="solar:star-bold" className="size-5 text-yellow-300" />
              </div>
              <div className="absolute bottom-0 right-1/4 animate-pulse delay-300">
                <Icon icon="solar:stars-bold" className="size-5 text-blue-300" />
              </div>
            </div>

            <h2 className="text-xl font-bold text-white">
              {t('customRole:loading.creating_your_exclusive_character')}
            </h2>

            {/* 进度信息 */}
            <div className="space-y-3">
              <div className="flex justify-between text-sm text-white/80">
                <span>{t('customRole:loading.creation_progress')}</span>
                <span>{progress}%</span>
              </div>

              {/* 真实进度条 */}
              <Progress
                value={Math.max(progress, 5)}
                color="secondary"
                size="md"
                classNames={{
                  track: 'bg-white/10',
                  indicator: 'bg-gradient-to-r from-purple-400 to-pink-400'
                }}
              />
            </div>

            {/* 阶段提示 */}
            <div className="space-y-3 text-sm text-white/80">
              {stages.map((stage, index) => {
                const isActive = index === currentStage
                const isCompleted = index < currentStage

                return (
                  <div key={index} className="flex items-center gap-3">
                    <div
                      className={`size-1.5 rounded-full transition-all duration-300 ${
                        isCompleted
                          ? 'bg-success-400'
                          : isActive
                          ? 'bg-primary-400 animate-pulse'
                          : 'bg-default-400'
                      }`}
                    />
                    <p className={`flex-1 ${isActive ? 'text-white font-medium' : ''}`}>
                      {stage.text}
                    </p>
                    {isActive && (
                      <Chip size="sm" color="primary" variant="flat">
                        {t('customRole:loading.in_progress')}
                      </Chip>
                    )}
                    {isCompleted && (
                      <Icon icon="solar:check-circle-bold" className="size-4 text-success-400" />
                    )}
                  </div>
                )
              })}
            </div>

            <p className="text-white/60 text-xs">
              {progress < 100
                ? t('customRole:loading.drawing_unique_character')
                : t('customRole:loading.almost_complete_please_wait')}
            </p>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}

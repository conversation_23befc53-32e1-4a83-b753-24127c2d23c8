/* 移动端适配样式 */

/* 基础样式设置 */
:root {
  --viewport-height: 100vh;
  --viewport-width: 100vw;
  --safe-area-top: env(safe-area-inset-top, 0px);
  --safe-area-bottom: env(safe-area-inset-bottom, 0px);
  --safe-area-left: env(safe-area-inset-left, 0px);
  --safe-area-right: env(safe-area-inset-right, 0px);
}

/* 针对Capacitor应用的样式 */
body.capacitor-app {
  width: var(--viewport-width, 100vw);
  min-height: var(--viewport-height, 100vh);
  overflow-x: hidden;
  overflow-y: auto;
  /* 确保内容不会在安全区域之外 */
  /* padding-top: var(--safe-area-top); */
  padding-bottom: var(--safe-area-bottom);
  padding-left: var(--safe-area-left);
  padding-right: var(--safe-area-right);

  /* 启用回弹滚动效果 */
  -webkit-overflow-scrolling: touch;
}

/* 禁用双击缩放 */
.capacitor-app * {
  touch-action: manipulation;
}

/* 滚动容器样式 */
.capacitor-app .scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-y: auto;
  height: 100%;
  width: 100%;
}

/* 修复输入框在移动设备上的问题 */
.capacitor-app input,
.capacitor-app textarea {
  font-size: 16px; /* 防止iOS上自动缩放 */
}

/* 对齐安全区域的工具类 */
.safe-area-top {
  padding-top: var(--safe-area-top);
}

.safe-area-bottom {
  padding-bottom: var(--safe-area-bottom);
}

.safe-area-left {
  padding-left: var(--safe-area-left);
}

.safe-area-right {
  padding-right: var(--safe-area-right);
}

/* 键盘适配 - 简化版本，只处理输入框本身，不影响消息显示 */
@media (max-width: 768px) {
  /* 防止输入框字体过小导致的缩放问题 */
  input[type='text'],
  input[type='email'],
  input[type='password'],
  textarea {
    font-size: 16px !important; /* 防止iOS上自动缩放 */
  }
}

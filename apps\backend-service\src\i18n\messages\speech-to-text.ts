// 语音转文本相关消息
export const speechToTextMessages = {
  zh: {
    // 错误消息
    'speech_to_text.api_key_not_configured': 'API Key 未配置',
    'speech_to_text.missing_audio_file': '缺少音频文件或文件格式不正确',
    'speech_to_text.unsupported_audio_format': '不支持的音频格式',
    'speech_to_text.file_too_large': '文件过大',
    'speech_to_text.processing_failed': '语音转文本处理失败，请重试',
    'speech_to_text.rate_limited': 'API 调用频率超限，请稍后重试',
    'speech_to_text.invalid_api_key': 'API 密钥无效或未授权',
    'speech_to_text.service_unavailable': '语音转文本服务暂时不可用，请稍后重试',

    // 日志消息
    'speech_to_text.start_processing': '开始语音转文本处理:',
    'speech_to_text.recognition_result': '语音识别结果:',
    'speech_to_text.processing_failed_log': '语音转文本处理失败:'
  },
  'zh-TW': {
    // 錯誤訊息
    'speech_to_text.api_key_not_configured': 'API Key 未配置',
    'speech_to_text.missing_audio_file': '缺少音訊檔案或檔案格式不正確',
    'speech_to_text.unsupported_audio_format': '不支援的音訊格式',
    'speech_to_text.file_too_large': '檔案過大',
    'speech_to_text.processing_failed': '語音轉文字處理失敗，請重試',
    'speech_to_text.rate_limited': 'API 呼叫頻率超限，請稍後重試',
    'speech_to_text.invalid_api_key': 'API 金鑰無效或未授權',
    'speech_to_text.service_unavailable': '語音轉文字服務暫時無法使用，請稍後重試',

    // 日誌訊息
    'speech_to_text.start_processing': '開始語音轉文字處理:',
    'speech_to_text.recognition_result': '語音識別結果:',
    'speech_to_text.processing_failed_log': '語音轉文字處理失敗:'
  },
  ja: {
    // エラーメッセージ
    'speech_to_text.api_key_not_configured': 'API Key が設定されていません',
    'speech_to_text.missing_audio_file':
      'オーディオファイルが不足しているか、ファイル形式が正しくありません',
    'speech_to_text.unsupported_audio_format': 'サポートされていないオーディオ形式です',
    'speech_to_text.file_too_large': 'ファイルが大きすぎます',
    'speech_to_text.processing_failed': '音声テキスト変換処理に失敗しました。再試行してください',
    'speech_to_text.rate_limited':
      'API 呼び出し頻度の制限を超えました。しばらく後に再試行してください',
    'speech_to_text.invalid_api_key': 'API キーが無効または未認証です',
    'speech_to_text.service_unavailable':
      '音声テキスト変換サービスが一時的に利用できません。しばらく後に再試行してください',

    // ログメッセージ
    'speech_to_text.start_processing': '音声テキスト変換処理を開始:',
    'speech_to_text.recognition_result': '音声認識結果:',
    'speech_to_text.processing_failed_log': '音声テキスト変換処理に失敗:'
  },
  es: {
    // Mensajes de error
    'speech_to_text.api_key_not_configured': 'API Key no configurada',
    'speech_to_text.missing_audio_file':
      'Falta el archivo de audio o el formato del archivo es incorrecto',
    'speech_to_text.unsupported_audio_format': 'Formato de audio no compatible',
    'speech_to_text.file_too_large': 'Archivo demasiado grande',
    'speech_to_text.processing_failed':
      'Error en el procesamiento de voz a texto, por favor inténtelo de nuevo',
    'speech_to_text.rate_limited':
      'Se excedió el límite de frecuencia de llamadas a la API, por favor inténtelo más tarde',
    'speech_to_text.invalid_api_key': 'Clave API inválida o no autorizada',
    'speech_to_text.service_unavailable':
      'El servicio de voz a texto no está disponible temporalmente, por favor inténtelo más tarde',

    // Mensajes de registro
    'speech_to_text.start_processing': 'Iniciando procesamiento de voz a texto:',
    'speech_to_text.recognition_result': 'Resultado del reconocimiento de voz:',
    'speech_to_text.processing_failed_log': 'Error en el procesamiento de voz a texto:'
  },
  en: {
    // 错误消息
    'speech_to_text.api_key_not_configured': 'API Key not configured',
    'speech_to_text.missing_audio_file': 'Missing audio file or incorrect file format',
    'speech_to_text.unsupported_audio_format': 'Unsupported audio format',
    'speech_to_text.file_too_large': 'File too large',
    'speech_to_text.processing_failed': 'Speech-to-text processing failed, please try again',
    'speech_to_text.rate_limited': 'API rate limit exceeded, please try again later',
    'speech_to_text.invalid_api_key': 'Invalid or unauthorized API key',
    'speech_to_text.service_unavailable':
      'Speech-to-text service temporarily unavailable, please try again later',

    // 日志消息
    'speech_to_text.start_processing': 'Starting speech-to-text processing:',
    'speech_to_text.recognition_result': 'Speech recognition result:',
    'speech_to_text.processing_failed_log': 'Speech-to-text processing failed:'
  }
}

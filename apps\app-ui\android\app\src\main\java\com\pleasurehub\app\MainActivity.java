package com.pleasurehub.app;

import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;
import android.view.View;
import android.view.WindowManager;
import android.os.Build;
import com.getcapacitor.BridgeActivity;
import com.pleasurehub.app.security.SecurityManager;

public class MainActivity extends BridgeActivity {
    
    private static final String TAG = "MainActivity";
    private static final boolean ENABLE_SECURITY_CHECK = false; // 暂时禁用安全检查
    
    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 设置全屏沉浸式状态栏
        setupImmersiveStatusBar();
        
        // 执行安全检查（目前禁用）
        if (ENABLE_SECURITY_CHECK) {
            performSecurityCheck();
        } else {
            // 即使不执行安全检查，也获取当前签名用于配置
            logCurrentSignature();
        }
    }
    
    /**
     * 设置沉浸式状态栏，让WebView可以使用全屏空间
     */
    private void setupImmersiveStatusBar() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE |
                View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN |
                View.SYSTEM_UI_FLAG_FULLSCREEN |
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            );
            
            // 设置状态栏透明
            getWindow().setStatusBarColor(android.graphics.Color.TRANSPARENT);
            getWindow().setFlags(
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS,
                WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS
            );
            
            Log.i(TAG, "已设置沉浸式全屏状态栏");
        }
    }
    
    /**
     * 记录当前应用签名，用于后续配置
     */
    private void logCurrentSignature() {
        try {
            SecurityManager securityManager = new SecurityManager(this);
            String signature = securityManager.getAppSignatureSHA256();
            if (signature != null) {
                Log.i(TAG, "当前应用签名 SHA256: " + signature);
                if (BuildConfig.DEBUG) {
                    Toast.makeText(this, "签名: " + signature.substring(0, 16) + "...", Toast.LENGTH_LONG).show();
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "获取签名失败", e);
        }
    }
    
    /**
     * 执行应用安全检查
     */
    private void performSecurityCheck() {
        try {
            SecurityManager securityManager = new SecurityManager(this);
            
            // 执行安全检查
            boolean isSecure = securityManager.performSecurityCheck();
            
            if (!isSecure) {
                // 安全检查失败，可以选择：
                // 1. 显示警告但继续运行（开发阶段）
                // 2. 退出应用（生产环境）
                
                Log.w(TAG, "应用安全检查失败");
                
                // 开发阶段：显示警告
                if (BuildConfig.DEBUG) {
                    Toast.makeText(this, "安全检查失败 - 开发模式", Toast.LENGTH_LONG).show();
                    
                    // 输出当前签名用于配置
                    String signature = securityManager.getAppSignatureSHA256();
                    if (signature != null) {
                        Log.i(TAG, "当前应用签名 SHA256: " + signature);
                    }
                } else {
                    // 生产环境：退出应用
                    // Toast.makeText(this, "应用完整性验证失败", Toast.LENGTH_SHORT).show();
                    finish();
                    System.exit(0);
                }
            } else {
                Log.i(TAG, "应用安全检查通过");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "安全检查异常", e);
            
            // 生产环境中，异常也应该导致应用退出
            if (!BuildConfig.DEBUG) {
                finish();
                System.exit(0);
            }
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        
        // 在应用恢复时也可以进行安全检查
        if (ENABLE_SECURITY_CHECK && !BuildConfig.DEBUG) {
            performSecurityCheck();
        }
    }
}

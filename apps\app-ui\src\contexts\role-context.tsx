// 兼容性文件：保持与原有代码的兼容
// 实际的角色状态管理已迁移到 stores/role-store.tsx

import type { CharacterInfo } from '@/stores/role-store'

// 重新导出类型和 hooks，保持 API 兼容性
export type { CharacterInfo } from '@/stores/role-store'
export { useRoleInfo, useRoleStore } from '@/stores/role-store'

// 角色上下文接口（保持兼容性）
export interface RoleContextType {
  character: CharacterInfo | null
  isLoading: boolean
}

// 注意：RoleProvider 已废弃，请使用全局的 RoleStoreProvider
// 该 Provider 现在在 App.tsx 根组件中提供

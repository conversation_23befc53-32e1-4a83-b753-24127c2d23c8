import { Hono } from 'hono'
import type { Env } from '@/types/env'
import {
  createElevenLabsSpeechClient,
  extractTextFromResponse,
  formatAlignment
} from '@/lib/speech/elevenlabs-speech'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { SupportedLanguage } from '@/i18n/config'

const app = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

/**
 * POST /api/speech-to-text
 * 语音转文本服务
 */
app.post('/', authMiddleware, languageMiddleware, async c => {
  // 获取翻译函数
  const t = c.get('t')

  try {
    // 检查 API Key
    if (!c.env.ELEVENLABS_API_KEY) {
      return c.json({ error: t('speech_to_text.api_key_not_configured') }, 500)
    }

    // 解析 multipart/form-data
    const formData = await c.req.formData()
    const audioFileEntry = formData.get('audio')
    const language = formData.get('language') as string | null

    // 检查是否为 File 对象
    if (!audioFileEntry || typeof audioFileEntry === 'string') {
      return c.json({ error: t('speech_to_text.missing_audio_file') }, 400)
    }

    const audioFile = audioFileEntry as File

    // 验证文件类型（ElevenLabs 支持的格式）
    const allowedTypes = [
      'audio/wav',
      'audio/webm',
      'audio/mp4',
      'audio/ogg',
      'audio/mpeg',
      'audio/m4a',
      'audio/flac',
      'audio/mp3'
    ]
    const baseMimeType = audioFile.type.split(';')[0].trim()

    if (!allowedTypes.includes(baseMimeType)) {
      return c.json(
        {
          error: t('speech_to_text.unsupported_audio_format'),
          receivedType: audioFile.type,
          baseMimeType: baseMimeType,
          allowedTypes: allowedTypes
        },
        400
      )
    }

    // 验证文件大小 (ElevenLabs 通常限制为 25MB)
    const maxSize = 25 * 1024 * 1024 // 25MB
    if (audioFile.size > maxSize) {
      return c.json(
        {
          error: t('speech_to_text.file_too_large'),
          maxSize: '25MB',
          receivedSize: `${Math.round((audioFile.size / 1024 / 1024) * 100) / 100}MB`
        },
        400
      )
    }

    console.log(t('speech_to_text.start_processing'), {
      fileName: audioFile.name,
      fileType: audioFile.type,
      fileSize: `${Math.round((audioFile.size / 1024) * 100) / 100}KB`
    })

    // 创建语音转文本客户端
    const speechClient = createElevenLabsSpeechClient(c.env.ELEVENLABS_API_KEY)

    // 调用语音转文本 API
    const speechResponse = await speechClient.speechToTextWithRetry(audioFile, {
      model_id: 'scribe_v1', // 推荐的语音转文本模型
      language_code: language || 'zh' // 默认中文
    })

    // 提取文本
    const transcription = extractTextFromResponse(speechResponse)

    // 格式化分段信息
    const segments = formatAlignment(speechResponse)

    console.log(t('speech_to_text.recognition_result'), {
      originalLength: audioFile.size,
      transcriptionLength: transcription.length,
      segmentsCount: segments.length,
      hasAlignment: !!speechResponse.alignment,
      transcription: transcription.substring(0, 100) + (transcription.length > 100 ? '...' : '')
    })

    // 返回结果
    return c.json({
      success: true,
      transcription: transcription,
      metadata: {
        audioFileName: audioFile.name,
        audioFileType: audioFile.type,
        audioFileSize: audioFile.size,
        transcriptionLength: transcription.length,
        processingTime: new Date().toISOString(),
        language: language || 'zh',
        provider: 'speech_service',
        duration: undefined, // 不返回音频时长
        segmentsCount: segments.length,
        processed: false, // 直接输出最终结果，无需后处理
        hasTranslation: false, // 统一输出目标语言，无需翻译
        hasPunctuation: true // 自动处理标点符号
      },
      // 可选：返回详细的分段信息
      segments: segments
    })
  } catch (error) {
    console.error(t('speech_to_text.processing_failed_log'), error)

    // 针对不同错误类型返回更具体的错误信息
    if (error instanceof Error) {
      if (error.message.includes('429') || error.message.includes('rate limit')) {
        return c.json(
          {
            error: t('speech_to_text.rate_limited'),
            code: 'RATE_LIMITED',
            retryAfter: 60
          },
          429
        )
      }
      if (error.message.includes('401') || error.message.includes('unauthorized')) {
        return c.json({ error: t('speech_to_text.invalid_api_key') }, 401)
      }
      if (
        error.message.includes('500') ||
        error.message.includes('502') ||
        error.message.includes('503')
      ) {
        return c.json({ error: t('speech_to_text.service_unavailable') }, 503)
      }
    }

    return c.json(
      {
        error: t('speech_to_text.processing_failed'),
        provider: 'speech_service',
        details: error instanceof Error ? error.message : t('common.unknown_error')
      },
      500
    )
  }
})

export default app

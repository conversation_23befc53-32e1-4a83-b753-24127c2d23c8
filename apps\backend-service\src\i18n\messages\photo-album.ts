// 写真集生成相关消息
export const photoAlbumMessages = {
  zh: {
    template_id_required: '模板ID不能为空',
    character_id_required: '角色ID不能为空',
    character_name_required: '角色名称不能为空',
    character_image_url_invalid: '角色图片URL格式不正确',
    task_id_required: '任务ID不能为空',
    user_not_authenticated: '用户未认证',
    template_not_found: '模板不存在',
    template_disabled: '模板已停用',
    task_submitted: '任务已提交到队列，请轮询状态',
    generation_failed: '写真集生成失败',
    task_not_found: '任务不存在',
    query_status_failed: '查询任务状态失败',
    get_history_failed: '获取历史记录失败'
  },
  'zh-TW': {
    template_id_required: '模板ID不能為空',
    character_id_required: '角色ID不能為空',
    character_name_required: '角色名稱不能為空',
    character_image_url_invalid: '角色圖片URL格式不正確',
    task_id_required: '任務ID不能為空',
    user_not_authenticated: '使用者未認證',
    template_not_found: '模板不存在',
    template_disabled: '模板已停用',
    task_submitted: '任務已提交到佇列，請輪詢狀態',
    generation_failed: '寫真集生成失敗',
    task_not_found: '任務不存在',
    query_status_failed: '查詢任務狀態失敗',
    get_history_failed: '取得歷史記錄失敗'
  },
  ja: {
    template_id_required: 'テンプレートIDを入力してください',
    character_id_required: 'キャラクターIDを入力してください',
    character_name_required: 'キャラクター名を入力してください',
    character_image_url_invalid: 'キャラクター画像URLの形式が正しくありません',
    task_id_required: 'タスクIDを入力してください',
    user_not_authenticated: 'ユーザーが認証されていません',
    template_not_found: 'テンプレートが見つかりません',
    template_disabled: 'テンプレートは無効です',
    task_submitted: 'タスクがキューに送信されました。ステータスをポーリングしてください',
    generation_failed: 'フォトアルバムの生成に失敗しました',
    task_not_found: 'タスクが見つかりません',
    query_status_failed: 'タスクステータスの取得に失敗しました',
    get_history_failed: '履歴の取得に失敗しました'
  },
  es: {
    template_id_required: 'El ID de plantilla no puede estar vacío',
    character_id_required: 'El ID del personaje no puede estar vacío',
    character_name_required: 'El nombre del personaje no puede estar vacío',
    character_image_url_invalid: 'El formato de la URL de imagen del personaje es incorrecto',
    task_id_required: 'El ID de tarea no puede estar vacío',
    user_not_authenticated: 'Usuario no autenticado',
    template_not_found: 'Plantilla no encontrada',
    template_disabled: 'La plantilla está deshabilitada',
    task_submitted: 'Tarea enviada a la cola, por favor consulte el estado',
    generation_failed: 'Error en la generación del álbum de fotos',
    task_not_found: 'Tarea no encontrada',
    query_status_failed: 'Error al consultar el estado de la tarea',
    get_history_failed: 'Error al obtener el historial'
  },
  en: {
    template_id_required: 'Template ID cannot be empty',
    character_id_required: 'Character ID cannot be empty',
    character_name_required: 'Character name cannot be empty',
    character_image_url_invalid: 'Character image URL format is incorrect',
    task_id_required: 'Task ID cannot be empty',
    user_not_authenticated: 'User not authenticated',
    template_not_found: 'Template not found',
    template_disabled: 'Template is disabled',
    task_submitted: 'Task submitted to queue, please poll for status',
    generation_failed: 'Photo album generation failed',
    task_not_found: 'Task not found',
    query_status_failed: 'Failed to query task status',
    get_history_failed: 'Failed to get history records'
  }
}

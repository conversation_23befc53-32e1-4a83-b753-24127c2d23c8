import type {
  ChatDatabaseInterface,
  ChatDatabaseConfig,
  ChatSession,
  ChatMessage,
  MessageAttachment
} from './types'
import { ChatDatabaseError, DEFAULT_DATABASE_CONFIG } from './types'

// 导入模块化组件
import { DatabaseConnection } from './database-connection'
import { DatabaseSchema } from './database-schema'
import { SessionRepository } from './session-repository'
import { MessageRepository } from './message-repository'
import { AttachmentRepository } from './attachment-repository'
import { BackgroundRepository } from './background-repository'
import { DatabaseUtils } from './database-utils'

/**
 * 聊天数据库实现类
 * 基于模块化架构，整合各个仓储和工具类
 */
export class ChatDatabase implements ChatDatabaseInterface {
  private connection: DatabaseConnection
  private schema!: DatabaseSchema
  private sessionRepo!: SessionRepository
  private messageRepo!: MessageRepository
  private attachmentRepo!: AttachmentRepository
  private backgroundRepo!: BackgroundRepository
  private utils!: DatabaseUtils

  // 🔧 简化并发控制：使用简单的互斥锁替代复杂队列
  private isSaving = false

  constructor(config: ChatDatabaseConfig = DEFAULT_DATABASE_CONFIG) {
    this.connection = new DatabaseConnection(config)
  }

  // 🔧 简单的互斥锁
  private async waitForSaveComplete(): Promise<void> {
    while (this.isSaving) {
      await new Promise(resolve => setTimeout(resolve, 10))
    }
  }

  /**
   * 初始化数据库
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🗄️ [ChatDB] 开始初始化聊天数据库')

      // 检查是否已初始化
      if (this.connection.isInitialized()) {
        console.log('✅ [ChatDB] 数据库已初始化，跳过')
        return true
      }

      // 初始化数据库连接
      const success = await this.connection.initialize()
      if (!success) {
        console.warn('⚠️ [ChatDB] 数据库连接初始化失败，使用降级模式')
        return false
      }

      // 获取数据库实例
      const db = this.connection.getDatabase()

      // 创建其他组件实例
      this.schema = new DatabaseSchema(db)
      this.sessionRepo = new SessionRepository(db)
      this.messageRepo = new MessageRepository(db)
      this.attachmentRepo = new AttachmentRepository(db)
      this.backgroundRepo = new BackgroundRepository(db)
      this.utils = new DatabaseUtils(db)

      // 确保表结构是最新的
      await this.schema.ensureTablesExist()

      // 确保背景图字段存在（用于已有数据库的升级）
      await this.ensureBackgroundColumns()

      console.log('✅ [ChatDB] 数据库初始化完成')
      return true
    } catch (error) {
      console.error('❌ [ChatDB] 数据库初始化失败:', error)
      throw new ChatDatabaseError('数据库初始化失败', 'INIT_FAILED', error as Error)
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    await this.connection.close()
  }

  /**
   * 检查数据库是否已初始化
   */
  private ensureInitialized(): void {
    if (!this.connection.isInitialized()) {
      throw new ChatDatabaseError('数据库未初始化', 'NOT_INITIALIZED')
    }
  }

  /**
   * 确保背景图相关字段存在（用于数据库升级）
   */
  private async ensureBackgroundColumns(): Promise<void> {
    try {
      console.log('🔧 [ChatDB] 检查并添加背景图字段...')

      await this.schema.addColumnIfNotExists('chat_sessions', 'backgroundImagePath', 'TEXT')
      await this.schema.addColumnIfNotExists('chat_sessions', 'backgroundImageUrl', 'TEXT')
      await this.schema.addColumnIfNotExists('chat_sessions', 'backgroundSceneDescription', 'TEXT')
      await this.schema.addColumnIfNotExists('chat_sessions', 'backgroundUpdatedAt', 'TEXT')

      console.log('✅ [ChatDB] 背景图字段检查完成')
    } catch (error) {
      console.warn('⚠️ [ChatDB] 背景图字段添加失败:', error)
      // 不抛出错误，允许系统继续运行
    }
  }

  // ============ 会话操作 ============

  async createSession(session: Omit<ChatSession, 'createdAt' | 'updatedAt'>): Promise<ChatSession> {
    this.ensureInitialized()
    return this.sessionRepo.createSession(session)
  }

  async getSession(id: string): Promise<ChatSession | null> {
    this.ensureInitialized()
    return this.sessionRepo.getSession(id)
  }

  async getAllSessions(): Promise<ChatSession[]> {
    this.ensureInitialized()
    return this.sessionRepo.getAllSessions()
  }

  async getSessionsByRole(roleId: string): Promise<ChatSession[]> {
    this.ensureInitialized()
    return this.sessionRepo.getSessionsByRole(roleId)
  }

  async getLatestSessionByRole(roleId: string): Promise<ChatSession | null> {
    this.ensureInitialized()
    return this.sessionRepo.getLatestSessionByRole(roleId)
  }

  async updateSession(id: string, updates: Partial<ChatSession>): Promise<boolean> {
    this.ensureInitialized()
    return this.sessionRepo.updateSession(id, updates)
  }

  async deleteSession(id: string): Promise<boolean> {
    this.ensureInitialized()
    return this.sessionRepo.deleteSession(id)
  }

  // ============ 消息操作 ============

  async createMessage(message: Omit<ChatMessage, 'createdAt' | 'updatedAt'>): Promise<ChatMessage> {
    this.ensureInitialized()
    return this.messageRepo.createMessage(message)
  }

  async getMessage(id: string): Promise<ChatMessage | null> {
    this.ensureInitialized()
    return this.messageRepo.getMessage(id)
  }

  async getMessagesByChat(chatId: string, limit = 100, offset = 0): Promise<ChatMessage[]> {
    this.ensureInitialized()
    return this.messageRepo.getMessagesByChat(chatId, limit, offset)
  }

  async updateMessage(id: string, updates: Partial<ChatMessage>): Promise<boolean> {
    this.ensureInitialized()
    return this.messageRepo.updateMessage(id, updates)
  }

  async updateMessageContent(messageId: string, content: string): Promise<void> {
    this.ensureInitialized()
    await this.waitForSaveComplete()
    await this.messageRepo.updateMessageContent(messageId, content)
  }

  async deleteMessage(id: string): Promise<boolean> {
    this.ensureInitialized()
    return this.messageRepo.deleteMessage(id)
  }

  // ============ 附件操作 ============

  async createAttachment(
    attachment: Omit<MessageAttachment, 'createdAt' | 'updatedAt'>
  ): Promise<MessageAttachment> {
    this.ensureInitialized()
    return this.attachmentRepo.createAttachment(attachment)
  }

  async getAttachment(id: string): Promise<MessageAttachment | null> {
    this.ensureInitialized()
    return this.attachmentRepo.getAttachment(id)
  }

  async getAttachmentsByMessage(messageId: string): Promise<MessageAttachment[]> {
    this.ensureInitialized()
    return this.attachmentRepo.getAttachmentsByMessage(messageId)
  }

  async updateAttachment(id: string, updates: Partial<MessageAttachment>): Promise<boolean> {
    this.ensureInitialized()
    return this.attachmentRepo.updateAttachment(id, updates)
  }

  async deleteAttachment(id: string): Promise<boolean> {
    this.ensureInitialized()
    return this.attachmentRepo.deleteAttachment(id)
  }

  // ============ 背景图操作 ============

  async updateSessionBackground(
    sessionId: string,
    backgroundImageUrl: string,
    backgroundImagePath: string,
    sceneDescription: string
  ): Promise<boolean> {
    this.ensureInitialized()
    return this.backgroundRepo.updateSessionBackground(
      sessionId,
      backgroundImageUrl,
      backgroundImagePath,
      sceneDescription
    )
  }

  async getSessionBackground(sessionId: string): Promise<{
    backgroundImagePath: string
    backgroundImageUrl: string
    backgroundSceneDescription: string
    backgroundUpdatedAt: string
  } | null> {
    this.ensureInitialized()
    return this.backgroundRepo.getSessionBackground(sessionId)
  }

  async clearSessionBackground(sessionId: string): Promise<boolean> {
    this.ensureInitialized()
    return this.backgroundRepo.clearSessionBackground(sessionId)
  }

  // ============ 复合操作 ============

  async saveCompleteMessage(
    chatId: string,
    message: Omit<ChatMessage, 'chatId' | 'createdAt' | 'updatedAt'>,
    attachments: Omit<MessageAttachment, 'messageId' | 'createdAt' | 'updatedAt'>[] = []
  ): Promise<{ message: ChatMessage; attachments: MessageAttachment[] }> {
    this.ensureInitialized()

    // 🔧 等待其他保存操作完成
    await this.waitForSaveComplete()

    // 🔧 设置保存标志
    this.isSaving = true

    try {
      const operationId = `save_${message.id}_${Date.now()}`
      const now = new Date().toISOString()

      console.log(`🔄 [ChatDB] 开始保存消息: ${operationId}`)

      // 1. 创建消息
      const newMessage = await this.messageRepo.createMessage({
        ...message,
        chatId
      })

      // 2. 创建附件
      const savedAttachments: MessageAttachment[] = []
      for (const attachment of attachments) {
        const newAttachment = await this.attachmentRepo.createAttachment({
          ...attachment,
          messageId: newMessage.id
        })
        savedAttachments.push(newAttachment)
      }

      // 3. 更新会话指标
      await this.sessionRepo.updateSessionMetrics(chatId, true)

      console.log(`✅ [ChatDB] 消息保存完成: ${operationId}`)

      return {
        message: newMessage,
        attachments: savedAttachments
      }
    } catch (error) {
      console.error(`❌ [ChatDB] 保存消息失败: ${message.id}`, error)
      throw new ChatDatabaseError(
        `保存完整消息失败: ${message.id}`,
        'SAVE_COMPLETE_MESSAGE_FAILED',
        error as Error
      )
    } finally {
      // 🔧 确保释放保存标志
      this.isSaving = false
    }
  }

  // 🚀 新增：清理重复的空助手消息
  async cleanupDuplicateEmptyAssistantMessages(chatId: string): Promise<number> {
    this.ensureInitialized()

    try {
      const db = this.connection.getDatabase()

      // 查找同一聊天中的空助手消息（内容为空或很短且没有附件）
      const emptyMessages = await db.query(
        `SELECT id, content, createdAt FROM chat_messages 
         WHERE chatId = ? AND role = 'assistant' 
         AND (content = '' OR LENGTH(TRIM(content)) < 5)
         ORDER BY createdAt ASC`,
        [chatId]
      )

      if (!emptyMessages.values || emptyMessages.values.length <= 1) {
        return 0 // 没有重复消息
      }

      console.log(`🧹 [ChatDB] 发现 ${emptyMessages.values.length} 条空助手消息，开始清理`)

      // 保留最新的一条，删除其他的
      const messagesToDelete = emptyMessages.values.slice(0, -1)
      let deletedCount = 0

      for (const msg of messagesToDelete) {
        try {
          // 删除消息的附件
          await db.run('DELETE FROM message_attachments WHERE messageId = ?', [msg.id])

          // 删除消息
          const result = await db.run('DELETE FROM chat_messages WHERE id = ?', [msg.id])

          if (result.changes?.changes > 0) {
            deletedCount++
            console.log(`🗑️ [ChatDB] 已删除空助手消息: ${msg.id}`)
          }
        } catch (error) {
          console.error(`❌ [ChatDB] 删除空助手消息失败 ${msg.id}:`, error)
        }
      }

      // 更新会话指标
      if (deletedCount > 0) {
        await this.sessionRepo.updateSessionMetrics(chatId, false)
        console.log(`✅ [ChatDB] 清理完成，删除了 ${deletedCount} 条重复空消息`)
      }

      return deletedCount
    } catch (error) {
      console.error(`❌ [ChatDB] 清理重复空消息失败:`, error)
      throw new ChatDatabaseError(
        `清理重复空消息失败: ${chatId}`,
        'CLEANUP_DUPLICATE_MESSAGES_FAILED',
        error as Error
      )
    }
  }

  // ============ 清理操作 ============

  async cleanupOldData(
    daysToKeep: number
  ): Promise<{ deletedSessions: number; deletedMessages: number; deletedAttachments: number }> {
    this.ensureInitialized()
    return this.utils.cleanupOldData(daysToKeep)
  }

  async getStorageStats(): Promise<{
    sessionCount: number
    messageCount: number
    attachmentCount: number
    totalSize: number
  }> {
    this.ensureInitialized()
    const stats = await this.utils.getStorageStats()
    return {
      sessionCount: stats.sessionCount,
      messageCount: stats.messageCount,
      attachmentCount: stats.attachmentCount,
      totalSize: stats.totalSize
    }
  }

  // ============ 扩展功能 ============

  /**
   * 获取会话仓储（用于高级操作）
   */
  getSessionRepository(): SessionRepository {
    this.ensureInitialized()
    return this.sessionRepo
  }

  /**
   * 获取消息仓储（用于高级操作）
   */
  getMessageRepository(): MessageRepository {
    this.ensureInitialized()
    return this.messageRepo
  }

  /**
   * 获取附件仓储（用于高级操作）
   */
  getAttachmentRepository(): AttachmentRepository {
    this.ensureInitialized()
    return this.attachmentRepo
  }

  /**
   * 获取背景图仓储（用于高级操作）
   */
  getBackgroundRepository(): BackgroundRepository {
    this.ensureInitialized()
    return this.backgroundRepo
  }

  /**
   * 获取数据库工具（用于维护操作）
   */
  getDatabaseUtils(): DatabaseUtils {
    this.ensureInitialized()
    return this.utils
  }

  /**
   * 获取数据库连接（用于底层操作）
   */
  getDatabaseConnection(): DatabaseConnection {
    return this.connection
  }
}

// 创建数据库单例实例
export const chatDatabase = new ChatDatabase()

// 导出初始化函数
export const initializeChatDatabase = async (): Promise<boolean> => {
  try {
    const success = await chatDatabase.initialize()
    if (success) {
      console.log('📦 [ChatDB] 聊天数据库初始化成功')
    } else {
      console.warn('⚠️ [ChatDB] 聊天数据库初始化失败，运行在降级模式')
    }
    return success
  } catch (error) {
    console.error('❌ [ChatDB] 聊天数据库初始化异常:', error)
    return false
  }
}

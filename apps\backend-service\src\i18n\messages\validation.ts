// 验证相关消息
export const validationMessages = {
  zh: {
    'validation.error': '验证错误',
    'validation.nickname_min': '昵称至少需要2个字符',
    'validation.nickname_max': '昵称不能超过50个字符',
    'validation.gender_invalid': '请选择有效的性别',
    'validation.avatar_url_invalid': '请提供有效的头像URL',
    'validation.message_id_required': '消息ID不能为空',
    'validation.url_invalid': '请提供有效的URL',
    'validation.name_required': '名称不能为空',
    'validation.name_too_long': '名称不能超过200个字符'
  },
  'zh-TW': {
    'validation.error': '驗證錯誤',
    'validation.nickname_min': '暱稱至少需要2個字元',
    'validation.nickname_max': '暱稱不能超過50個字元',
    'validation.gender_invalid': '請選擇有效的性別',
    'validation.avatar_url_invalid': '請提供有效的頭像URL',
    'validation.message_id_required': '訊息ID不能為空',
    'validation.url_invalid': '請提供有效的URL',
    'validation.name_required': '名稱不能為空',
    'validation.name_too_long': '名稱不能超過200個字元'
  },
  ja: {
    'validation.error': '検証エラー',
    'validation.nickname_min': 'ニックネームは2文字以上である必要があります',
    'validation.nickname_max': 'ニックネームは50文字以下である必要があります',
    'validation.gender_invalid': '有効な性別を選択してください',
    'validation.avatar_url_invalid': '有効なアバターURLを提供してください',
    'validation.message_id_required': 'メッセージIDを入力してください',
    'validation.url_invalid': '有効なURLを提供してください',
    'validation.name_required': '名前を入力してください',
    'validation.name_too_long': '名前は200文字以下である必要があります'
  },
  es: {
    'validation.error': 'Error de validación',
    'validation.nickname_min': 'El apodo debe tener al menos 2 caracteres',
    'validation.nickname_max': 'El apodo no puede exceder los 50 caracteres',
    'validation.gender_invalid': 'Por favor seleccione un género válido',
    'validation.avatar_url_invalid': 'Por favor proporcione una URL de avatar válida',
    'validation.message_id_required': 'El ID del mensaje no puede estar vacío',
    'validation.url_invalid': 'Por favor proporcione una URL válida',
    'validation.name_required': 'El nombre no puede estar vacío',
    'validation.name_too_long': 'El nombre no puede exceder los 200 caracteres'
  },
  en: {
    'validation.error': 'Validation Error',
    'validation.nickname_min': 'Nickname must be at least 2 characters',
    'validation.nickname_max': 'Nickname cannot exceed 50 characters',
    'validation.gender_invalid': 'Please select a valid gender',
    'validation.avatar_url_invalid': 'Please provide a valid avatar URL',
    'validation.message_id_required': 'Message ID cannot be empty',
    'validation.url_invalid': 'Please provide a valid URL',
    'validation.name_required': 'Name cannot be empty',
    'validation.name_too_long': 'Name cannot exceed 200 characters'
  }
}

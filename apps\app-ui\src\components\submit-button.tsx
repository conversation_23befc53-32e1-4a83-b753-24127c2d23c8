'use client'

import { LoaderIcon } from './icons'

import { Button } from './ui/button'

export function SubmitButton({
  children,
  isSuccessful,
  isLoading,
  className
}: {
  children: React.ReactNode
  isSuccessful: boolean
  isLoading?: boolean
  className?: string
}) {
  // React 18 中不支持 useFormStatus，直接使用传入的 isLoading 状态
  const isPending = isLoading

  return (
    <Button
      type={isPending ? 'button' : 'submit'}
      aria-disabled={isPending || isSuccessful}
      disabled={isPending || isSuccessful}
      className={`relative ${className || ''}`}
    >
      {children}

      {(isPending || isSuccessful) && (
        <span className="animate-spin absolute right-4">
          <LoaderIcon />
        </span>
      )}

      <output aria-live="polite" className="sr-only">
        {isPending || isSuccessful ? 'Loading' : 'Submit form'}
      </output>
    </Button>
  )
}

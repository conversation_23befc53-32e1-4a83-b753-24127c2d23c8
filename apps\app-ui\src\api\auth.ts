import { apiClient } from './client'

// 用户类型
export interface User {
  id: string
  email: string
  emailConfirmed: boolean
  dbUserId?: number
  createdAt?: string
  lastSignIn?: string
}

// 会话类型
export interface Session {
  user: User
  expires: string
}

// 登录响应类型
export interface LoginResponse {
  success: boolean
  message?: string
  session?: {
    access_token: string
    refresh_token: string
    expires_at: number
    user: User
  }
}

// 登录请求类型
export interface LoginRequest {
  email: string
  password: string
}

// 注册请求类型
export interface RegisterRequest {
  email: string
  password: string
  name?: string
  inviteCode?: string
}

// 注册响应类型
export interface RegisterResponse {
  success: boolean
  message?: string
  user?: User
}

// 发送验证码请求类型
export interface SendCodeRequest {
  email: string
}

// 发送验证码响应类型
export interface SendCodeResponse {
  success: boolean
  message?: string
}

// 验证验证码请求类型
export interface VerifyCodeRequest {
  email: string
  code: string
  name?: string
  password?: string
  inviteCode?: string
}

// 验证验证码响应类型
export interface VerifyCodeResponse {
  success: boolean
  message?: string
  session?: {
    access_token: string
    refresh_token: string
    expires_at: number
    user: User
  }
}

// 登录验证码请求类型
export interface LoginCodeRequest {
  email: string
  code: string
}

// 登录验证码响应类型
export interface LoginCodeResponse {
  success: boolean
  message?: string
  session?: {
    access_token: string
    refresh_token: string
    expires_at: number
    user: User
  }
  requireEmailConfirm?: boolean
}

// 认证状态
export type AuthStatus =
  | 'authenticated' // 已认证
  | 'unauthenticated' // 未认证
  | 'loading' // 加载中

// 认证API服务
export const authApi = {
  // 登录
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    try {
      const response = await apiClient.post<LoginResponse>('/api/auth/login', credentials, {
        silentError: true // 静默处理错误，由页面自己处理错误提示
      })

      // 登录成功，预加载逻辑由AuthProvider处理

      return response
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '登录失败'
      }
    }
  },

  // 刷新令牌
  async refreshToken(refreshToken: string): Promise<LoginResponse> {
    try {
      return await apiClient.post<LoginResponse>('/api/auth/refresh', {
        refresh_token: refreshToken
      })
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '刷新令牌失败'
      }
    }
  },

  // 验证码登录
  async loginWithCode(data: LoginCodeRequest): Promise<LoginCodeResponse> {
    try {
      const response = await apiClient.post<LoginCodeResponse>('/api/auth/login-code', data, {
        silentError: true // 静默处理错误，由页面自己处理错误提示
      })

      // 登录成功，预加载逻辑由AuthProvider处理

      return response
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '登录失败'
      }
    }
  },

  // 注册
  async register(data: RegisterRequest): Promise<RegisterResponse> {
    try {
      return await apiClient.post<RegisterResponse>('/api/auth/register', data)
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '注册失败'
      }
    }
  },

  // 发送验证码
  async sendCode(data: SendCodeRequest): Promise<SendCodeResponse> {
    try {
      return await apiClient.post<SendCodeResponse>('/api/auth/send-code', data)
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '发送验证码失败'
      }
    }
  },

  // 发送登录验证码
  async sendLoginCode(data: SendCodeRequest): Promise<SendCodeResponse> {
    try {
      return await apiClient.post<SendCodeResponse>('/api/auth/send-login-code', data, {
        silentError: true // 静默处理错误，由页面自己处理错误提示
      })
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '发送验证码失败'
      }
    }
  },

  // 验证验证码
  async verifyCode(data: VerifyCodeRequest): Promise<VerifyCodeResponse> {
    try {
      const response = await apiClient.post<VerifyCodeResponse>('/api/auth/verify-code', data)

      // 注册成功，预加载逻辑由AuthProvider处理

      return response
    } catch (error) {
      return {
        success: false,
        message: error instanceof Error ? error.message : '验证失败'
      }
    }
  },

  // 注销
  async logout(): Promise<{ success: boolean }> {
    try {
      await apiClient.post('/api/auth/logout', {})

      // 注销成功，缓存清除由AuthProvider处理

      return { success: true }
    } catch {
      // API调用失败，缓存清除由AuthProvider处理
      return { success: false }
    }
  },

  // 获取当前会话
  async getSession(): Promise<Session | null> {
    try {
      return await apiClient.get<Session>('/api/auth/session')
    } catch {
      return null
    }
  }
}

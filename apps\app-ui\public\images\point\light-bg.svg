<svg width="375" height="267" viewBox="0 0 375 267" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.5" filter="url(#filter0_f_915_1272)">
<path d="M388.387 -62.78C399.249 40.5832 301.302 95.0914 201.747 105.553C102.192 116.014 -3.57086 251.642 -14.4321 148.279C-25.2934 44.9156 70.7036 -183.649 170.259 -194.11C269.814 -204.571 377.526 -166.143 388.387 -62.78Z" fill="#892FFF"/>
</g>
<g opacity="0.4" filter="url(#filter1_f_915_1272)">
<ellipse cx="-24.9749" cy="130.566" rx="94.2418" ry="85.1896" transform="rotate(-42.9448 -24.9749 130.566)" fill="#FF2D97"/>
</g>
<g opacity="0.4" filter="url(#filter2_f_915_1272)">
<path d="M-103.574 121.722C-68.4286 163.72 12.6964 123.873 94.8584 84.9649C197.46 55.7005 232.931 -12.0324 197.786 -54.0306C162.641 -96.0287 42.0315 -129.422 -32.4143 -67.1238C-106.86 -4.82522 -138.719 79.724 -103.574 121.722Z" fill="#66FFDE"/>
</g>
<defs>
<filter id="filter0_f_915_1272" x="-87.2742" y="-267.77" width="548.489" height="526.677" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_915_1272"/>
</filter>
<filter id="filter1_f_915_1272" x="-187.13" y="-30.9395" width="324.311" height="323.01" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_915_1272"/>
</filter>
<filter id="filter2_f_915_1272" x="-189.875" y="-173.525" width="472.834" height="385.184" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_915_1272"/>
</filter>
</defs>
</svg>

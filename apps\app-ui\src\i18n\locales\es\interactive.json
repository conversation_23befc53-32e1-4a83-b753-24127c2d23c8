{"title": "Historias interactivas", "subtitle": "Elige un guión para comenzar tu experiencia", "loading": "Cargando datos del guión...", "error": {"title": "Ocurrió un error", "retry": "Reintentar"}, "scriptSelector": {"title": "Selección de guión", "subtitle": "Elige un guión para comenzar tu experiencia interactiva", "pullToRefresh": {"pullDown": "Desliza hacia abajo para actualizar guiones", "release": "Suelta para actualizar guiones", "refreshing": "Actualizando gui<PERSON>..."}, "noScripts": "Sin guiones disponibles", "errorRefresh": ", desliza hacia abajo para reintentar"}, "deviceConnect": {"title": "Conectar dispositivo", "subtitle": "Conecta tu dispositivo inteligente para una mejor experiencia", "connectButton": "Conectar dispositivo", "skipButton": "<PERSON><PERSON><PERSON>", "enterDeviceCodeError": "Ingresa el código del dispositivo", "invalidDeviceCodeError": "Código de dispositivo inválido, vuelve a ingresarlo", "inputLabel": "Ingresar código del dispositivo", "inputPlaceholder": "Ingresa código de 6 dígitos", "exampleCodes": "Códigos de ejemplo: 1234, 5678, 9012", "or": "o", "scanning": "Escaneando...", "scanQrCode": "Escanear código QR del dispositivo", "scanInstructions": "Coloca el código QR del dispositivo frente a la cámara"}, "scriptDetail": {"startScript": "Comenzar historia", "downloading": "Descargando...", "purchase": "Punt<PERSON>", "purchasing": "Comprando...", "purchased": "Comprado", "purchaseSuccess": "¡Compra exitosa! Toca para comenzar", "pointsRequired": "Re<PERSON>ere {{points}} puntos", "validityPeriod": "(Acceso de un año después de la compra)", "validityPeriodShort": "(Válido por un año)"}, "purchaseConfirm": {"title": "Confirmar compra", "message": "¿Estás seguro de que quieres gastar {{points}} puntos para comprar", "scriptTitle": "'{{title}}'?", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar compra", "purchasing": "Comprando..."}, "toast": {"deviceConnected": {"title": "Dispositivo conectado", "description": "Comenzando '{{scriptTitle}}' con {{deviceName}}"}, "purchaseSuccess": {"title": "Compra exitosa", "description": "'{{scriptTitle}}' se ha añadido a tu biblioteca"}, "purchaseFailed": {"title": "Compra fallida", "insufficientPoints": "Puntos insuficientes, recarga primero", "networkError": "Error de red, verifica tu conexión", "unknownError": "Error descon<PERSON>"}, "startScriptFailed": {"title": "Error al iniciar historia"}}, "purchaseSuccessModal": {"title": "¡Compra exitosa!", "congratulations": "¡Felicidades por comprar '{{scriptTitle}}'", "pointsSpent": "<PERSON><PERSON><PERSON> gas<PERSON>", "pointsAmount": "{{pointsCost}} puntos", "validityPeriod": "<PERSON><PERSON><PERSON>", "oneYear": "Un año", "experienceLater": "Experimentar más tarde", "startScript": "Comenzar historia"}}
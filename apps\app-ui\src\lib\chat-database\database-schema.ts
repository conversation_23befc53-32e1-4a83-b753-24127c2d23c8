import { ChatDatabaseError } from './types'

/**
 * 数据库表结构管理器
 * 负责表的创建、更新和索引管理
 */
export class DatabaseSchema {
  private db: any

  constructor(database: any) {
    this.db = database
  }

  /**
   * 确保所有表都存在
   */
  async ensureTablesExist(): Promise<void> {
    try {
      console.log('🏗️ [DB Schema] 开始创建数据库表结构')

      // 创建会话表
      await this.createSessionsTable()

      // 创建消息表
      await this.createMessagesTable()

      // 创建附件表
      await this.createAttachmentsTable()

      // 创建索引以提高查询性能
      await this.createIndexes()

      console.log('✅ [DB Schema] 数据库表结构创建完成')
    } catch (error) {
      throw new ChatDatabaseError('创建数据库表结构失败', 'CREATE_SCHEMA_FAILED', error as Error)
    }
  }

  /**
   * 创建会话表
   */
  private async createSessionsTable(): Promise<void> {
    await this.db.execute(`
      CREATE TABLE IF NOT EXISTS chat_sessions (
        id TEXT PRIMARY KEY,
        roleId TEXT NOT NULL,
        title TEXT NOT NULL,
        messageCount INTEGER DEFAULT 0,
        backgroundImagePath TEXT,
        backgroundImageUrl TEXT,
        backgroundSceneDescription TEXT,
        backgroundUpdatedAt TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        lastMessageAt TEXT NOT NULL
      );
    `)
    console.log('✅ [DB Schema] 会话表创建完成')
  }

  /**
   * 创建消息表
   */
  private async createMessagesTable(): Promise<void> {
    await this.db.execute(`
      CREATE TABLE IF NOT EXISTS chat_messages (
        id TEXT PRIMARY KEY,
        chatId TEXT NOT NULL,
        role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
        content TEXT NOT NULL,
        isStreaming INTEGER DEFAULT 0,
        streamingVersion INTEGER DEFAULT 0,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (chatId) REFERENCES chat_sessions(id) ON DELETE CASCADE
      );
    `)
    console.log('✅ [DB Schema] 消息表创建完成')
  }

  /**
   * 创建附件表
   */
  private async createAttachmentsTable(): Promise<void> {
    await this.db.execute(`
      CREATE TABLE IF NOT EXISTS message_attachments (
        id TEXT PRIMARY KEY,
        messageId TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('audio', 'image', 'video')),
        name TEXT NOT NULL,
        contentType TEXT NOT NULL,
        originalUrl TEXT NOT NULL,
        localPath TEXT,
        status TEXT NOT NULL CHECK (status IN ('generating', 'downloading', 'completed', 'failed')),
        fileSize INTEGER,
        metadata TEXT NOT NULL DEFAULT '{}',
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL,
        FOREIGN KEY (messageId) REFERENCES chat_messages(id) ON DELETE CASCADE
      );
    `)
    console.log('✅ [DB Schema] 附件表创建完成')
  }

  /**
   * 创建数据库索引
   */
  private async createIndexes(): Promise<void> {
    const indexes = [
      // 会话表索引
      {
        name: 'idx_chat_sessions_updated_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_chat_sessions_updated_at ON chat_sessions(updatedAt DESC);'
      },
      {
        name: 'idx_chat_sessions_role_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_chat_sessions_role_id ON chat_sessions(roleId);'
      },

      // 消息表索引
      {
        name: 'idx_chat_messages_chat_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_id ON chat_messages(chatId);'
      },
      {
        name: 'idx_chat_messages_created_at',
        sql: 'CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(createdAt DESC);'
      },
      {
        name: 'idx_chat_messages_chat_created',
        sql: 'CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_created ON chat_messages(chatId, createdAt DESC);'
      },

      // 附件表索引
      {
        name: 'idx_message_attachments_message_id',
        sql: 'CREATE INDEX IF NOT EXISTS idx_message_attachments_message_id ON message_attachments(messageId);'
      },
      {
        name: 'idx_message_attachments_status',
        sql: 'CREATE INDEX IF NOT EXISTS idx_message_attachments_status ON message_attachments(status);'
      },
      {
        name: 'idx_message_attachments_type',
        sql: 'CREATE INDEX IF NOT EXISTS idx_message_attachments_type ON message_attachments(type);'
      }
    ]

    for (const index of indexes) {
      try {
        await this.db.execute(index.sql)
        console.log(`✅ [DB Schema] 索引创建完成: ${index.name}`)
      } catch (error) {
        console.warn(`⚠️ [DB Schema] 索引创建失败: ${index.name}`, error)
      }
    }

    console.log('✅ [DB Schema] 所有索引创建完成')
  }

  /**
   * 检查表是否存在
   */
  async tableExists(tableName: string): Promise<boolean> {
    try {
      const result = await this.db.query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName]
      )
      return result.values && result.values.length > 0
    } catch (error) {
      console.warn(`⚠️ [DB Schema] 检查表存在性失败: ${tableName}`, error)
      return false
    }
  }

  /**
   * 获取表的列信息
   */
  async getTableColumns(
    tableName: string
  ): Promise<
    Array<{ name: string; type: string; notnull: boolean; dflt_value: any; pk: boolean }>
  > {
    try {
      const result = await this.db.query(`PRAGMA table_info(${tableName})`)
      return result.values || []
    } catch (error) {
      console.warn(`⚠️ [DB Schema] 获取表列信息失败: ${tableName}`, error)
      return []
    }
  }

  /**
   * 添加新列到表（用于数据库升级）
   */
  async addColumnIfNotExists(
    tableName: string,
    columnName: string,
    columnDefinition: string
  ): Promise<boolean> {
    try {
      const columns = await this.getTableColumns(tableName)
      const columnExists = columns.some(col => col.name === columnName)

      if (!columnExists) {
        await this.db.execute(
          `ALTER TABLE ${tableName} ADD COLUMN ${columnName} ${columnDefinition}`
        )
        console.log(`✅ [DB Schema] 新列已添加: ${tableName}.${columnName}`)
        return true
      } else {
        console.log(`📝 [DB Schema] 列已存在: ${tableName}.${columnName}`)
        return false
      }
    } catch (error) {
      console.warn(`⚠️ [DB Schema] 添加列失败: ${tableName}.${columnName}`, error)
      throw new ChatDatabaseError(
        `添加列失败: ${tableName}.${columnName}`,
        'ADD_COLUMN_FAILED',
        error as Error
      )
    }
  }

  /**
   * 升级数据库模式（用于版本迁移）
   */
  async upgradeSchema(fromVersion: number, toVersion: number): Promise<void> {
    console.log(`🔄 [DB Schema] 开始数据库模式升级: v${fromVersion} → v${toVersion}`)

    try {
      // 这里可以根据版本添加特定的升级逻辑
      if (fromVersion < 1 && toVersion >= 1) {
        // 示例：添加背景图相关字段
        await this.addColumnIfNotExists('chat_sessions', 'backgroundImagePath', 'TEXT')
        await this.addColumnIfNotExists('chat_sessions', 'backgroundImageUrl', 'TEXT')
        await this.addColumnIfNotExists('chat_sessions', 'backgroundSceneDescription', 'TEXT')
        await this.addColumnIfNotExists('chat_sessions', 'backgroundUpdatedAt', 'TEXT')
      }

      console.log(`✅ [DB Schema] 数据库模式升级完成: v${fromVersion} → v${toVersion}`)
    } catch (error) {
      throw new ChatDatabaseError(
        `数据库模式升级失败: v${fromVersion} → v${toVersion}`,
        'SCHEMA_UPGRADE_FAILED',
        error as Error
      )
    }
  }
}

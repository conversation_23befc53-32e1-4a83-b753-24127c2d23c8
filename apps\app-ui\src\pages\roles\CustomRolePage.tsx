import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { Button } from '@/components/ui/button'
import CharacterCreator from '@/components/character-creator'
import { ArrowLeft } from 'lucide-react'

export default function CustomRolePage() {
  const navigate = useNavigate()
  const { t } = useTranslation('customRole')

  const goBack = () => {
    navigate(-1)
  }

  return (
    <div className="container p-4 pt-10 max-w-md mx-auto">
      <div className="flex items-center mb-6">
        <Button variant="ghost" size="icon" onClick={goBack} className="mr-2">
          <ArrowLeft className="size-5" />
        </Button>
        <div>
          <h3 className="font-bold">{t('page_title')}</h3>
          <p className="text-xs text-muted-foreground">{t('page_subtitle')}</p>
        </div>
      </div>

      <CharacterCreator />
    </div>
  )
}

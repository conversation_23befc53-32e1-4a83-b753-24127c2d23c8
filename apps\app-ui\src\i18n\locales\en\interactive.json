{"title": "Interactive Stories", "subtitle": "Choose a script to start your experience", "loading": "Loading script data...", "error": {"title": "An error occurred", "retry": "Retry"}, "scriptSelector": {"title": "Script Selection", "subtitle": "Choose a script to start your interactive experience", "pullToRefresh": {"pullDown": "Pull down to refresh scripts", "release": "Release to refresh scripts", "refreshing": "Refreshing scripts..."}, "noScripts": "No scripts available", "errorRefresh": ", pull down to retry"}, "deviceConnect": {"title": "Connect Device", "subtitle": "Connect your smart device for a better experience", "connectButton": "Connect Device", "skipButton": "Skip Connection", "enterDeviceCodeError": "Please enter device code", "invalidDeviceCodeError": "Invalid device code, please re-enter", "inputLabel": "Enter Device Code", "inputPlaceholder": "Please enter 6-digit device code", "exampleCodes": "Sample device codes: 1234, 5678, 9012", "or": "or", "scanning": "Scanning...", "scanQrCode": "Scan Device QR Code", "scanInstructions": "Place the device QR code in front of the camera"}, "scriptDetail": {"startScript": "Start Story", "downloading": "Downloading...", "purchase": "Points", "purchasing": "Purchasing...", "purchased": "Purchased", "purchaseSuccess": "Purchase successful! Click to start", "pointsRequired": "Requires {{points}} points", "validityPeriod": "(One year access after purchase)", "validityPeriodShort": "(Valid for one year)"}, "scriptDetailModal": {"buttonText": {"startScript": "Start Story", "downloading": "Downloading...", "purchasing": "Purchasing...", "purchaseSuccess": "Purchase successful! Click to start", "pointsCost": "{{pointsCost}} Points"}, "purchaseSuccess": "Purchase Successful!", "pointsRequired": "Requires {{points}} points", "validityPeriod": "(One year access after purchase)", "validityPeriodShort": "(Valid for one year)", "noDescription": "No description available", "purchased": "Purchased"}, "purchaseConfirm": {"title": "Confirm Purchase", "message": "Are you sure you want to spend {{points}} points to purchase", "scriptTitle": "'{{title}}'?", "cancel": "Cancel", "confirm": "Confirm Purchase", "purchasing": "Purchasing..."}, "purchaseConfirmModal": {"title": "Confirm Purchase", "pointsCost": "Requires {{pointsCost}} points", "validityPeriod": "(One year access after purchase)", "cancelButton": "Cancel", "confirmButton": "Confirm Purchase", "purchasingButton": "Purchasing...", "description": {"part1": "Are you sure you want to spend", "part2": "to purchase script", "part3": "?", "scriptTitle": "'{{title}}'?"}, "points": "points"}, "toast": {"deviceConnected": {"title": "Device Connected", "description": "Starting '{{scriptTitle}}' with {{deviceName}}"}, "purchaseSuccess": {"title": "Purchase Successful", "description": "'{{scriptTitle}}' has been added to your library"}, "purchaseFailed": {"title": "Purchase Failed", "insufficientPoints": "Insufficient points, please recharge first", "networkError": "Network error, please check your connection", "unknownError": "Unknown error"}, "startScriptFailed": {"title": "Failed to start story"}, "playbackFailed": {"title": "Playback Failed", "description": "Please check audio permissions or restart the app"}, "audioBlocked": {"title": "Audio Playback Blocked", "description": "Please tap the screen to enable audio playback"}, "audioPlaybackFailed": {"title": "Playback Failed", "description": "Please try refreshing the page or check audio permissions"}, "bluetoothCommandFailed": {"title": "Bluetooth Command Failed", "description": "Please check device connection status"}}, "errors": {"initDevicesFailed": "Failed to initialize device list:", "deviceConnectFailed": "Device connection failed:", "updateScriptUsageFailed": "Failed to update script usage count:", "getScriptsFailed": "Failed to get script list:", "refreshScriptsFailed": "Failed to refresh script list:", "startScriptFailed": "Failed to start story:", "purchaseScriptFailed": "Failed to purchase script:"}, "stageSelector": {"title": "Select Stage", "closeAriaLabel": "Close stage selector", "duration": "Duration: {{duration}}"}, "commandPanel": {"title": "Script Commands", "closeAriaLabel": "Close command panel", "noCommands": "No commands available"}, "deviceControlPanel": {"title": "Device Control", "closeAriaLabel": "Close device control panel", "autoMode": "Auto mode: Intensity follows script settings", "manualMode": "Manual mode: Customize intensity settings", "currentMode": "Current mode is", "manualModeSwitch": "Manual Mode", "intensity": "Intensity", "level": "Level", "off": "Off", "noDeviceFunctions": "No controllable device functions"}, "purchaseSuccessModal": {"title": "Purchase Successful!", "congratulations": "Congratulations on purchasing '{{scriptTitle}}'", "pointsSpent": "Points Spent", "pointsAmount": "{{pointsCost}} points", "validityPeriod": "Validity Period", "oneYear": "One Year", "experienceLater": "Experience Later", "startScript": "Start Story"}}
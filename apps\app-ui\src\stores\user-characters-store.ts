import { create } from 'zustand'
import { charactersService } from '@/api/services/characters'
import type { CharacterData } from '@/api/services/characters'

// Store状态接口
interface UserCharactersState {
  // 数据状态
  userCharacters: CharacterData[]
  isLoading: boolean
  isRefreshing: boolean
  error: string | null
  lastFetchTime: number | null

  // 请求状态管理
  fetchPromise: Promise<CharacterData[]> | null
}

// Store方法接口
interface UserCharactersActions {
  // 获取用户角色（带缓存）
  fetchUserCharacters: (forceRefresh?: boolean) => Promise<CharacterData[]>

  // 强制刷新角色数据
  refreshUserCharacters: () => Promise<CharacterData[]>

  // 添加新角色到缓存
  addUserCharacter: (character: CharacterData) => void

  // 更新角色数据
  updateUserCharacter: (characterId: string, updates: Partial<CharacterData>) => void

  // 删除角色
  removeUserCharacter: (characterId: string) => void

  // 根据ID获取单个角色
  getUserCharacterById: (characterId: string) => CharacterData | undefined

  // 清除缓存
  clearUserCharacters: () => void

  // 检查缓存是否有效
  isCacheValid: () => boolean
}

// 缓存配置
const CACHE_EXPIRY_TIME = 5 * 60 * 1000 // 5分钟缓存
const MAX_CACHE_SIZE = 100 // 最大缓存角色数量

// 创建store
export const useUserCharactersStore = create<UserCharactersState & UserCharactersActions>(
  (set, get) => ({
    // 初始状态
    userCharacters: [],
    isLoading: false,
    isRefreshing: false,
    error: null,
    lastFetchTime: null,
    fetchPromise: null,

    // 检查缓存是否有效
    isCacheValid: () => {
      const { lastFetchTime } = get()
      if (!lastFetchTime) return false
      return Date.now() - lastFetchTime < CACHE_EXPIRY_TIME
    },

    // 获取用户角色（带缓存逻辑）
    fetchUserCharacters: async (forceRefresh = false) => {
      const state = get()

      // 如果不强制刷新且缓存有效，直接返回缓存数据
      if (!forceRefresh && state.isCacheValid() && state.userCharacters.length > 0) {
        console.log('🎯 使用缓存的用户角色数据')
        return state.userCharacters
      }

      // 如果正在请求中，返回同一个Promise避免重复请求
      if (state.fetchPromise) {
        console.log('🔄 等待进行中的请求')
        return state.fetchPromise
      }

      // 创建新的请求Promise
      const fetchPromise = (async () => {
        try {
          console.log('🚀 开始获取用户角色数据')

          set({
            isLoading: !state.userCharacters.length, // 如果有缓存数据，不显示loading
            isRefreshing: !!state.userCharacters.length, // 如果有缓存数据，显示refreshing
            error: null
          })

          const response = await charactersService.getUserCharacters()
          const characters = response?.characters || []

          // 限制缓存大小
          const limitedCharacters = characters.slice(0, MAX_CACHE_SIZE)

          set({
            userCharacters: limitedCharacters,
            isLoading: false,
            isRefreshing: false,
            error: null,
            lastFetchTime: Date.now(),
            fetchPromise: null
          })

          console.log(`✅ 成功获取 ${limitedCharacters.length} 个用户角色`)
          return limitedCharacters
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取用户角色失败'
          console.error('❌ 获取用户角色失败:', errorMessage)

          set({
            isLoading: false,
            isRefreshing: false,
            error: errorMessage,
            fetchPromise: null
          })

          // 如果有缓存数据，在错误时返回缓存数据
          if (state.userCharacters.length > 0) {
            console.log('⚠️ 请求失败，返回缓存数据')
            return state.userCharacters
          }

          throw error
        }
      })()

      // 保存Promise到状态中
      set({ fetchPromise })

      return fetchPromise
    },

    // 强制刷新角色数据
    refreshUserCharacters: async () => {
      return get().fetchUserCharacters(true)
    },

    // 添加新角色到缓存
    addUserCharacter: (character: CharacterData) => {
      set(state => {
        // 检查是否已存在
        const exists = state.userCharacters.some(c => c.id === character.id)
        if (exists) {
          console.log('⚠️ 角色已存在，跳过添加:', character.name)
          return state
        }

        const newCharacters = [character, ...state.userCharacters].slice(0, MAX_CACHE_SIZE)
        console.log('➕ 添加新角色到缓存:', character.name)

        return {
          userCharacters: newCharacters,
          lastFetchTime: Date.now() // 更新缓存时间
        }
      })
    },

    // 更新角色数据
    updateUserCharacter: (characterId: string, updates: Partial<CharacterData>) => {
      set(state => {
        const index = state.userCharacters.findIndex(c => c.id === characterId)
        if (index === -1) {
          console.log('⚠️ 未找到要更新的角色:', characterId)
          return state
        }

        const newCharacters = [...state.userCharacters]
        newCharacters[index] = { ...newCharacters[index], ...updates }

        console.log('🔄 更新角色数据:', characterId)

        return {
          userCharacters: newCharacters,
          lastFetchTime: Date.now()
        }
      })
    },

    // 删除角色
    removeUserCharacter: (characterId: string) => {
      set(state => {
        const newCharacters = state.userCharacters.filter(c => c.id !== characterId)
        console.log('🗑️ 从缓存中删除角色:', characterId)

        return {
          userCharacters: newCharacters,
          lastFetchTime: Date.now()
        }
      })
    },

    // 根据ID获取单个角色
    getUserCharacterById: (characterId: string) => {
      const { userCharacters } = get()
      return userCharacters.find(c => c.id === characterId)
    },

    // 清除缓存
    clearUserCharacters: () => {
      console.log('🧹 清除用户角色缓存')
      set({
        userCharacters: [],
        isLoading: false,
        isRefreshing: false,
        error: null,
        lastFetchTime: null,
        fetchPromise: null
      })
    }
  })
)

// 便捷的Hook，用于只获取数据而不触发请求
export const useUserCharactersData = () => {
  const store = useUserCharactersStore()
  return {
    userCharacters: store.userCharacters,
    isLoading: store.isLoading,
    isRefreshing: store.isRefreshing,
    error: store.error,
    isCacheValid: store.isCacheValid()
  }
}

// 便捷的Hook，用于获取特定角色
export const useUserCharacter = (characterId: string) => {
  const getUserCharacterById = useUserCharactersStore(state => state.getUserCharacterById)
  return getUserCharacterById(characterId)
}

import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type { MediaGeneration } from '../schema'
import type { Env } from '@/types/env'

// ==================== 媒体生成记录操作 ====================

/**
 * 创建媒体生成记录
 */
export async function createMediaGeneration(
  env: Env,
  data: {
    userId: string
    characterId?: string
    chatId?: string
    messageId?: string
    mediaType: 'image' | 'video' | 'audio'
    generationType: 'multimodal_chat' | 'standalone' | 'template_based'
    prompt?: string
    negativePrompt?: string
    inputImageUrl?: string
    pointsUsed?: number
    metadata?: any
  }
): Promise<MediaGeneration> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .insert({
        user_id: data.userId,
        character_id: data.characterId,
        chat_id: data.chatId,
        message_id: data.messageId,
        media_type: data.mediaType,
        generation_type: data.generationType,
        prompt: data.prompt,
        negative_prompt: data.negativePrompt,
        input_image_url: data.inputImageUrl,
        points_used: data.pointsUsed || 0,
        output_urls: [],
        status: 'pending',
        metadata: data.metadata,
        created_at: new Date().toISOString()
      })
      .select()
      .single()

    const { data: record, error } = handleSupabaseSingleResult(result)
    if (error) throw error

    console.log('✅ [MEDIA-GEN] 创建媒体生成记录:', record.id)
    return record
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 创建媒体生成记录失败:', error)
    throw error
  }
}

/**
 * 更新媒体生成记录
 */
export async function updateMediaGeneration(
  env: Env,
  id: string,
  data: {
    status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
    outputUrls?: string[]
    errorMessage?: string
    generationTime?: number
    completedAt?: Date
    metadata?: any
  }
): Promise<MediaGeneration> {
  try {
    const supabase = getSupabase(env)

    const updateData: any = {}
    if (data.status !== undefined) updateData.status = data.status
    if (data.outputUrls !== undefined) updateData.output_urls = data.outputUrls
    if (data.errorMessage !== undefined) updateData.error_message = data.errorMessage
    if (data.generationTime !== undefined) updateData.generation_time = data.generationTime
    if (data.completedAt !== undefined) updateData.completed_at = data.completedAt.toISOString()
    if (data.metadata !== undefined) updateData.metadata = data.metadata

    updateData.updated_at = new Date().toISOString()

    const result = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    const { data: record, error } = handleSupabaseSingleResult(result)
    if (error) throw error

    console.log('✅ [MEDIA-GEN] 更新媒体生成记录:', record.id, data.status)
    return record
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 更新媒体生成记录失败:', error)
    throw error
  }
}

/**
 * 获取用户的媒体生成记录列表
 */
export async function getUserMediaGenerations(
  env: Env,
  userId: string,
  options: {
    characterId?: string
    mediaType?: 'image' | 'video' | 'audio'
    generationType?: 'multimodal_chat' | 'standalone' | 'template_based'
    status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
    limit?: number
    offset?: number
  } = {}
): Promise<MediaGeneration[]> {
  try {
    const supabase = getSupabase(env)
    const { limit = 20, offset = 0 } = options

    let query = supabase.from(TABLE_NAMES.mediaGeneration).select('*').eq('user_id', userId)

    // 添加过滤条件
    if (options.characterId) {
      query = query.eq('character_id', options.characterId)
    }
    if (options.mediaType) {
      query = query.eq('media_type', options.mediaType)
    }
    if (options.generationType) {
      query = query.eq('generation_type', options.generationType)
    }
    if (options.status) {
      query = query.eq('status', options.status)
    }

    query = query.order('created_at', { ascending: false }).range(offset, offset + limit - 1)

    const result = await query
    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    console.log('✅ [MEDIA-GEN] 查询用户媒体生成记录:', userId, data?.length || 0)
    return data || []
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 查询用户媒体生成记录失败:', error)
    throw error
  }
}

/**
 * 根据 generationId 查找媒体生成记录
 */
export async function getMediaGenerationByGenerationId(
  env: Env,
  userId: string,
  generationId: string
): Promise<MediaGeneration | null> {
  try {
    const supabase = getSupabase(env)

    // 使用正确的 JSON 查询语法
    const result = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .select('*')
      .eq('user_id', userId)
      .eq('metadata->>generationId', generationId) // 使用 ->> 操作符查询 JSON 字段
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) {
      if (error.code === 'PGRST116') {
        // 没有找到记录
        console.log('🔍 [MEDIA-GEN] 未找到 generationId:', generationId)
        return null
      }
      throw error
    }

    console.log('✅ [MEDIA-GEN] 找到 generationId 记录:', generationId, data.id)
    return data
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 查询 generationId 失败:', error)
    return null
  }
}

/**
 * 获取角色的媒体生成记录
 */
export async function getCharacterMediaGenerations(
  env: Env,
  userId: string,
  characterId: string,
  options: {
    mediaType?: 'image' | 'video' | 'audio'
    limit?: number
    offset?: number
  } = {}
): Promise<MediaGeneration[]> {
  try {
    const supabase = getSupabase(env)
    const { limit = 20, offset = 0 } = options

    let query = supabase
      .from(TABLE_NAMES.mediaGeneration)
      .select('*')
      .eq('user_id', userId)
      .eq('character_id', characterId)
      .eq('generation_type', 'template_based') // 只返回模板生成的记录

    if (options.mediaType) {
      query = query.eq('media_type', options.mediaType)
    }

    query = query.order('created_at', { ascending: false }).range(offset, offset + limit - 1)

    const result = await query
    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    console.log('✅ [MEDIA-GEN] 查询角色媒体生成记录:', characterId, data?.length || 0)
    return data || []
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 查询角色媒体生成记录失败:', error)
    throw error
  }
}

/**
 * 获取单个媒体生成记录
 */
export async function getMediaGenerationById(
  env: Env,
  id: string
): Promise<MediaGeneration | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .select('*')
      .eq('id', id)
      .single()

    const { data, error } = handleSupabaseSingleResult(result)

    // 如果是"没有找到记录"的错误，返回null而不是抛出异常
    if (error && error.code === 'PGRST116') {
      console.log(`MediaGeneration with id ${id} not found`)
      return null
    }

    if (error) throw error
    return data
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 查询媒体生成记录失败:', error)
    throw error
  }
}

/**
 * 删除媒体生成记录
 */
export async function deleteMediaGeneration(
  env: Env,
  id: string,
  userId: string
): Promise<boolean> {
  try {
    const supabase = getSupabase(env)

    // 确保只能删除自己的记录
    const { error } = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) throw error

    console.log('✅ [MEDIA-GEN] 删除媒体生成记录:', id)
    return true
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 删除媒体生成记录失败:', error)
    throw error
  }
}

/**
 * 获取用户媒体生成统计
 */
export async function getUserMediaGenerationStats(
  env: Env,
  userId: string
): Promise<{
  total: number
  byMediaType: Record<string, number>
  byStatus: Record<string, number>
}> {
  try {
    const supabase = getSupabase(env)

    // 获取总数和状态统计
    const { data: allRecords, error } = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .select('media_type, status')
      .eq('user_id', userId)

    if (error) throw error

    const total = allRecords?.length || 0
    const byMediaType: Record<string, number> = {}
    const byStatus: Record<string, number> = {}

    allRecords?.forEach(record => {
      byMediaType[record.media_type] = (byMediaType[record.media_type] || 0) + 1
      byStatus[record.status] = (byStatus[record.status] || 0) + 1
    })

    console.log('✅ [MEDIA-GEN] 查询用户媒体生成统计:', userId, { total })
    return { total, byMediaType, byStatus }
  } catch (error) {
    console.error('❌ [MEDIA-GEN] 查询用户媒体生成统计失败:', error)
    throw error
  }
}

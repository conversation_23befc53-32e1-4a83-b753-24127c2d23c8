require_relative '../../../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '14.0'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios'
  pod 'CapacitorCommunityBluetoothLe', :path => '../../../../node_modules/.pnpm/@capacitor-community+bluetooth-le@7.0.0_@capacitor+core@7.2.0/node_modules/@capacitor-community/bluetooth-le'
  pod 'CapacitorCommunityMedia', :path => '../../../../node_modules/.pnpm/@capacitor-community+media@8.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/media'
  pod 'CapacitorCommunitySqlite', :path => '../../../../node_modules/.pnpm/@capacitor-community+sqlite@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/sqlite'
  pod 'CapacitorApp', :path => '../../../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/app'
  pod 'CapacitorBarcodeScanner', :path => '../../../../node_modules/.pnpm/@capacitor+barcode-scanner@2.0.3_@capacitor+core@7.2.0/node_modules/@capacitor/barcode-scanner'
  pod 'CapacitorDevice', :path => '../../../../node_modules/.pnpm/@capacitor+device@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/device'
  pod 'CapacitorFilesystem', :path => '../../../../node_modules/.pnpm/@capacitor+filesystem@7.1.2_@capacitor+core@7.2.0/node_modules/@capacitor/filesystem'
  pod 'CapacitorKeyboard', :path => '../../../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/keyboard'
  pod 'CapacitorNetwork', :path => '../../../../node_modules/.pnpm/@capacitor+network@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/network'
  pod 'CapacitorPreferences', :path => '../../../../node_modules/.pnpm/@capacitor+preferences@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/preferences'
  pod 'CapacitorPrivacyScreen', :path => '../../../../node_modules/.pnpm/@capacitor+privacy-screen@1.1.0_@capacitor+core@7.2.0/node_modules/@capacitor/privacy-screen'
  pod 'CapacitorStatusBar', :path => '../../../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/status-bar'
  pod 'CapawesomeCapacitorLiveUpdate', :path => '../../../../node_modules/.pnpm/@capawesome+capacitor-live-update@7.2.0_@capacitor+core@7.2.0/node_modules/@capawesome/capacitor-live-update'
  pod 'CapacitorBleAdvertiser', :path => '../../../../packages/capacitor-ble-advertiser'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
end

post_install do |installer|
  assertDeploymentTarget(installer)
end

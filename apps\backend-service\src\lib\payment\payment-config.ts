/**
 * 支付系统配置管理
 */

export interface PaymentConfig {
  // 支付宝配置
  alipay: {
    appId: string;
    privateKey: string;
    publicKey: string;
    gateway: string;
    signType: 'RSA2';
    charset: 'UTF-8';
    version: '1.0';
    format: 'JSON';
  };

  // 微信支付配置 (预留)
  wechat: {
    mchId: string;
    apiKey: string;
    appId: string;
    gateway: string;
  };

  // 通用配置
  common: {
    callbackDomain: string;
    webDomain: string;
    orderExpireMinutes: number;
  };
}

/**
 * 从环境变量获取支付配置
 */
export function getPaymentConfig(env: any): PaymentConfig {
  return {
    alipay: {
      appId: env.ALIPAY_APP_ID || '',
      privateKey: env.ALIPAY_PRIVATE_KEY || '',
      publicKey: env.ALIPAY_PUBLIC_KEY || '',
      gateway: env.ALIPAY_GATEWAY || 'https://openapi.alipay.com/gateway.do',
      signType: 'RSA2',
      charset: 'UTF-8',
      version: '1.0',
      format: 'JSON',
    },
    wechat: {
      mchId: env.WECHAT_MCHID || '',
      apiKey: env.WECHAT_API_KEY || '',
      appId: env.WECHAT_APPID || '',
      gateway: env.WECHAT_GATEWAY || 'https://api.mch.weixin.qq.com',
    },
    common: {
      callbackDomain: env.PAY_CALLBACK_DOMAIN || 'https://your-worker.workers.dev',
      webDomain: env.PAY_WEB_DOMAIN || 'https://pay.example.com',
      orderExpireMinutes: 30,
    },
  };
}

/**
 * 验证支付配置是否完整
 */
export function validatePaymentConfig(config: PaymentConfig): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 验证支付宝配置
  if (!config.alipay.appId) errors.push('支付宝 APP_ID 未配置');
  if (!config.alipay.privateKey) errors.push('支付宝私钥未配置');
  if (!config.alipay.publicKey) errors.push('支付宝公钥未配置');

  // 验证通用配置
  if (!config.common.callbackDomain) errors.push('回调域名未配置');
  if (!config.common.webDomain) errors.push('支付页面域名未配置');

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
}

/**
 * 订单状态枚举
 */
export enum OrderStatus {
  PENDING = 'pending',
  PAID = 'paid',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  EXPIRED = 'expired',
}

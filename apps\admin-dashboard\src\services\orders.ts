import { apiService } from './api'
import type { ApiResponse } from '@/types/api'

export interface PaymentOrder {
  id: string
  userId: string
  userEmail?: string
  type: 'membership' | 'points'
  itemName?: string
  amount: number
  currency: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  paymentMethod: string
  description?: string
  tradeNo?: string
  paidAt?: string
  createdAt: string
  updatedAt: string
}

export interface OrderListParams {
  page?: number
  pageSize?: number
  keyword?: string
  status?: string
  type?: string
  paymentMethod?: string
  startDate?: string
  endDate?: string
}

export interface OrderListResponse {
  data: PaymentOrder[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface OrderDetailResponse {
  id: string
  userId: string
  userEmail: string
  type: 'membership' | 'points'
  amount: number
  currency: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  paymentMethod: string
  description?: string
  tradeNo?: string
  paidAt?: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
  metadata?: Record<string, any>
  itemInfo?: {
    type: 'membership' | 'points'
    id: string
    name: string
    description?: string
    price: number
    durationDays?: number
    pointsIncluded?: number
    points?: number
    bonusPoints?: number
  }
}

export interface OrderStatsResponse {
  totalOrders: number
  completedOrders: number
  todayOrders: number
  todayRevenue: number
  totalRevenue: number
}

class OrderService {
  async getOrders(params: OrderListParams = {}): Promise<ApiResponse<OrderListResponse>> {
    return await apiService.get<OrderListResponse>('/admin/orders', { params })
  }

  async getOrderDetail(orderId: string): Promise<ApiResponse<OrderDetailResponse>> {
    return await apiService.get<OrderDetailResponse>(`/admin/orders/${orderId}`)
  }

  async getOrderStats(): Promise<ApiResponse<OrderStatsResponse>> {
    return await apiService.get<OrderStatsResponse>('/admin/orders/stats/summary')
  }

  async exportOrders(params: OrderListParams = {}): Promise<Blob> {
    const response = await apiService.get('/admin/orders/export', {
      params,
      responseType: 'blob'
    })
    return response as any
  }
}

export const orderService = new OrderService()
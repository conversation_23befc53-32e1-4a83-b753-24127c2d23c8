/**
 * 将时间字符串转换为秒数
 * @param time 时间字符串，格式为 "MM:SS" 或 "HH:MM:SS"
 * @returns 转换后的秒数
 */
export const timeToSeconds = (time: string): number => {
  try {
    if (!time || typeof time !== 'string') {
      console.warn('无效的时间格式:', time);
      return 0;
    }

    const parts = time.split(':').map((part) => {
      const parsed = Number.parseInt(part, 10);
      return Number.isNaN(parsed) ? 0 : parsed;
    });

    if (parts.length === 2) {
      // 格式为 "MM:SS"
      const [minutes, seconds] = parts;
      return minutes * 60 + seconds;
    } else if (parts.length === 3) {
      // 格式为 "HH:MM:SS"
      const [hours, minutes, seconds] = parts;
      return hours * 3600 + minutes * 60 + seconds;
    } else {
      console.warn('不支持的时间格式:', time);
      return 0;
    }
  } catch (error) {
    console.error('时间转换错误:', error, '输入:', time);
    return 0;
  }
};

/**
 * 将秒数转换为时间字符串
 * @param seconds 秒数
 * @returns 格式为 "MM:SS" 的时间字符串，超过1小时则为 "HH:MM:SS"
 */
export const secondsToTime = (seconds: number): string => {
  try {
    if (Number.isNaN(seconds) || seconds < 0) {
      console.warn('无效的秒数:', seconds);
      return '00:00';
    }

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    const paddedMinutes = String(minutes).padStart(2, '0');
    const paddedSeconds = String(secs).padStart(2, '0');

    if (hours > 0) {
      const paddedHours = String(hours).padStart(2, '0');
      return `${paddedHours}:${paddedMinutes}:${paddedSeconds}`;
    }

    return `${paddedMinutes}:${paddedSeconds}`;
  } catch (error) {
    console.error('秒数转换时间错误:', error, '输入:', seconds);
    return '00:00';
  }
};

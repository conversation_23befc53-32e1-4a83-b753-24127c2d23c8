// 图片生成相关消息
export const imageGenerationMessages = {
  zh: {
    // 验证错误
    message_id_required: '消息ID不能为空',
    chat_id_required: '聊天ID不能为空',
    prompt_required: '提示词不能为空',
    prompt_too_long: '提示词长度不能超过2000字符',
    task_id_required: '任务ID不能为空',
    inputs_missing_or_invalid: '缺少inputs参数或格式错误',

    // 用户相关错误
    user_info_not_found: '未找到用户信息',
    user_not_exist: '用户不存在',

    // 系统配置错误
    image_service_config_incomplete: '图片生成服务配置不完整',
    missing_env_vars: '缺少环境变量',

    // 消息相关错误
    message_not_exist: '消息不存在',
    message_chat_mismatch: '消息与聊天不匹配',

    // 生成相关消息
    image_generation_task_started: '图片生成任务已启动',
    generation_task_points_consumed: '消费{points}积分',
    queue_send_failed: '启动图片生成失败，积分已退还，请重试',
    generation_failed: '图片生成失败',
    service_internal_error: '服务内部错误，请重试',

    // 状态查询
    realtime_status_message: '请通过 Realtime 监听获取实时状态',
    query_status_failed: '查询状态失败',

    // 健康检查
    service_unhealthy: '服务不健康',
    health_check_failed: '健康检查失败',

    // 附件相关
    preparing_image_generation: '正在准备图片生成...'
  },
  'zh-TW': {
    // 驗證錯誤
    message_id_required: '訊息ID不能為空',
    chat_id_required: '聊天ID不能為空',
    prompt_required: '提示詞不能為空',
    prompt_too_long: '提示詞長度不能超過2000字元',
    task_id_required: '任務ID不能為空',
    inputs_missing_or_invalid: '缺少inputs參數或格式錯誤',

    // 使用者相關錯誤
    user_info_not_found: '未找到使用者資訊',
    user_not_exist: '使用者不存在',

    // 系統配置錯誤
    image_service_config_incomplete: '圖片生成服務配置不完整',
    missing_env_vars: '缺少環境變數',

    // 訊息相關錯誤
    message_not_exist: '訊息不存在',
    message_chat_mismatch: '訊息與聊天不匹配',

    // 生成相關訊息
    image_generation_task_started: '圖片生成任務已啟動',
    generation_task_points_consumed: '消費{points}積分',
    queue_send_failed: '啟動圖片生成失敗，積分已退還，請重試',
    generation_failed: '圖片生成失敗',
    service_internal_error: '服務內部錯誤，請重試',

    // 狀態查詢
    realtime_status_message: '請通過 Realtime 監聽取得即時狀態',
    query_status_failed: '查詢狀態失敗',

    // 健康檢查
    service_unhealthy: '服務不健康',
    health_check_failed: '健康檢查失敗',

    // 附件相關
    preparing_image_generation: '正在準備圖片生成...'
  },
  ja: {
    // 検証エラー
    message_id_required: 'メッセージIDを入力してください',
    chat_id_required: 'チャットIDを入力してください',
    prompt_required: 'プロンプトを入力してください',
    prompt_too_long: 'プロンプトの長さは2000文字以下である必要があります',
    task_id_required: 'タスクIDを入力してください',
    inputs_missing_or_invalid: 'inputsパラメータが不足しているか、形式が正しくありません',

    // ユーザー関連エラー
    user_info_not_found: 'ユーザー情報が見つかりません',
    user_not_exist: 'ユーザーが存在しません',

    // システム設定エラー
    image_service_config_incomplete: '画像生成サービスの設定が不完全です',
    missing_env_vars: '環境変数が不足しています',

    // メッセージ関連エラー
    message_not_exist: 'メッセージが存在しません',
    message_chat_mismatch: 'メッセージがチャットと一致しません',

    // 生成関連メッセージ
    image_generation_task_started: '画像生成タスクが開始されました',
    generation_task_points_consumed: '{points}ポイントを消費しました',
    queue_send_failed: '画像生成の開始に失敗しました。ポイントが返金されました。再試行してください',
    generation_failed: '画像生成に失敗しました',
    service_internal_error: 'サービス内部エラーが発生しました。再試行してください',

    // ステータス照会
    realtime_status_message: 'リアルタイムでステータスを監視してください',
    query_status_failed: 'ステータスの照会に失敗しました',

    // ヘルスチェック
    service_unhealthy: 'サービスが正常ではありません',
    health_check_failed: 'ヘルスチェックに失敗しました',

    // 添付ファイル関連
    preparing_image_generation: '画像生成を準備中...'
  },
  es: {
    // Errores de validación
    message_id_required: 'El ID del mensaje no puede estar vacío',
    chat_id_required: 'El ID del chat no puede estar vacío',
    prompt_required: 'El prompt no puede estar vacío',
    prompt_too_long: 'La longitud del prompt no puede exceder los 2000 caracteres',
    task_id_required: 'El ID de la tarea no puede estar vacío',
    inputs_missing_or_invalid: 'Faltan parámetros de inputs o el formato es incorrecto',

    // Errores relacionados con el usuario
    user_info_not_found: 'No se encontró la información del usuario',
    user_not_exist: 'El usuario no existe',

    // Errores de configuración del sistema
    image_service_config_incomplete:
      'La configuración del servicio de generación de imágenes está incompleta',
    missing_env_vars: 'Faltan variables de entorno',

    // Errores relacionados con mensajes
    message_not_exist: 'El mensaje no existe',
    message_chat_mismatch: 'El mensaje no coincide con el chat',

    // Mensajes relacionados con la generación
    image_generation_task_started: 'Tarea de generación de imágenes iniciada',
    generation_task_points_consumed: 'Se consumieron {points} puntos',
    queue_send_failed:
      'Error al iniciar la generación de imágenes, puntos reembolsados, por favor inténtelo de nuevo',
    generation_failed: 'Error en la generación de imágenes',
    service_internal_error: 'Error interno del servicio, por favor inténtelo de nuevo',

    // Consulta de estado
    realtime_status_message: 'Por favor escuche el estado en tiempo real a través de Realtime',
    query_status_failed: 'Error al consultar el estado',

    // Verificación de salud
    service_unhealthy: 'El servicio no está saludable',
    health_check_failed: 'Error en la verificación de salud',

    // Relacionado con archivos adjuntos
    preparing_image_generation: 'Preparando generación de imágenes...'
  },
  en: {
    // 验证错误
    message_id_required: 'Message ID cannot be empty',
    chat_id_required: 'Chat ID cannot be empty',
    prompt_required: 'Prompt cannot be empty',
    prompt_too_long: 'Prompt length cannot exceed 2000 characters',
    task_id_required: 'Task ID cannot be empty',
    inputs_missing_or_invalid: 'Missing inputs parameter or invalid format',

    // 用户相关错误
    user_info_not_found: 'User information not found',
    user_not_exist: 'User does not exist',

    // 系统配置错误
    image_service_config_incomplete: 'Image generation service configuration incomplete',
    missing_env_vars: 'Missing environment variables',

    // 消息相关错误
    message_not_exist: 'Message does not exist',
    message_chat_mismatch: 'Message does not match chat',

    // 生成相关消息
    image_generation_task_started: 'Image generation task started',
    generation_task_points_consumed: 'Consumed {points} points',
    queue_send_failed: 'Failed to start image generation, points refunded, please try again',
    generation_failed: 'Image generation failed',
    service_internal_error: 'Internal service error, please try again',

    // 状态查询
    realtime_status_message: 'Please listen for real-time status via Realtime',
    query_status_failed: 'Failed to query status',

    // 健康检查
    service_unhealthy: 'Service unhealthy',
    health_check_failed: 'Health check failed',

    // 附件相关
    preparing_image_generation: 'Preparing image generation...'
  }
}

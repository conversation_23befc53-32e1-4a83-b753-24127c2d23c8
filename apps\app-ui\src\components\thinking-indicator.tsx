import { memo } from 'react'
import { SparklesIcon } from './icons'
import { useRoleInfo } from '@/contexts/role-context'
import { Spinner } from '@heroui/react'

const PureThinkingIndicator = () => {
  const { character } = useRoleInfo()
  const characterAvatar = character?.imageUrl || null

  return (
    <div className="w-full mx-auto max-w-3xl px-4 animate-in fade-in-50 duration-300">
      <div className="flex gap-3 w-full">
        {/* 头像 */}
        <div className="size-8 flex items-center rounded-full justify-center shrink-0 bg-default-100 overflow-hidden">
          {characterAvatar ? (
            <img src={characterAvatar} alt="角色头像" className="object-cover size-full" />
          ) : (
            <div className="text-default-500">
              <SparklesIcon size={12} />
            </div>
          )}
        </div>

        {/* 思考内容 */}
        <div className="flex items-center gap-2 py-2">
          <Spinner classNames={{ label: 'text-foreground mt-4' }} variant="dots" />
        </div>
      </div>
    </div>
  )
}

export const ThinkingIndicator = memo(PureThinkingIndicator)

import {
  create<PERSON>ontext,
  useContext,
  useReducer,
  useEffect,
  useCallback,
  type ReactNode
} from 'react'
import { useUserCharactersStore } from './user-characters-store'
import { useSystemCharactersStore } from './system-characters-store'
import type { CharacterData } from '@/api/services/characters'

// 使用完整的角色数据接口
export interface CharacterInfo extends CharacterData {}

// 角色状态接口
interface RoleState {
  currentRole: CharacterInfo | null
  isLoading: boolean
  error: string | null
}

// 角色操作接口
interface RoleActions {
  setRole: (roleId: string) => Promise<void>
  clearRole: () => void
  refreshRole: () => Promise<void>
}

// 角色上下文类型
type RoleContextType = RoleState & RoleActions

// 操作类型
type RoleAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ROLE'; payload: CharacterInfo }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'CLEAR_ROLE' }
  | { type: 'CLEAR_ERROR' }

// 初始状态
const initialState: RoleState = {
  currentRole: null,
  isLoading: false,
  error: null
}

// 状态管理器
function roleReducer(state: RoleState, action: RoleAction): RoleState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
    case 'SET_ROLE':
      return { ...state, currentRole: action.payload, isLoading: false, error: null }
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false }
    case 'CLEAR_ROLE':
      return { ...state, currentRole: null, error: null }
    case 'CLEAR_ERROR':
      return { ...state, error: null }
    default:
      return state
  }
}

// 创建上下文
const RoleStoreContext = createContext<RoleContextType | undefined>(undefined)

// 本地存储键名
const STORAGE_KEY = 'current-role-id'

// 存储策略配置
const STORAGE_CONFIG = {
  // 使用 sessionStorage 进行会话级别存储
  useSessionStorage: true,
  // 备用 localStorage 键名（用于记住用户偏好）
  preferenceKey: 'preferred-role-id'
}

// 获取存储实例
const getStorage = () => {
  return STORAGE_CONFIG.useSessionStorage ? sessionStorage : localStorage
}

// 存储角色ID
const saveRoleId = (roleId: string) => {
  try {
    const storage = getStorage()
    storage.setItem(STORAGE_KEY, roleId)

    // 同时保存到 localStorage 作为用户偏好（可选）
    localStorage.setItem(STORAGE_CONFIG.preferenceKey, roleId)
  } catch (error) {
    console.warn('保存角色ID失败:', error)
  }
}

// 获取角色ID
const getSavedRoleId = (): string | null => {
  try {
    const storage = getStorage()
    let roleId = storage.getItem(STORAGE_KEY)

    // 如果 sessionStorage 中没有，尝试从 localStorage 获取用户偏好
    if (!roleId && STORAGE_CONFIG.useSessionStorage) {
      roleId = localStorage.getItem(STORAGE_CONFIG.preferenceKey)
      // 如果找到偏好设置，保存到当前会话
      if (roleId) {
        storage.setItem(STORAGE_KEY, roleId)
      }
    }

    return roleId
  } catch (error) {
    console.warn('获取角色ID失败:', error)
    return null
  }
}

// 清除角色ID
const clearRoleId = () => {
  try {
    const storage = getStorage()
    storage.removeItem(STORAGE_KEY)
    // 注意：不清除用户偏好设置
  } catch (error) {
    console.warn('清除角色ID失败:', error)
  }
}

// 全局角色状态提供者
export function RoleStoreProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(roleReducer, initialState)

  // 清除角色状态的方法（用于用户切换时）
  const clearAllRoleData = useCallback(() => {
    console.log('🧹 清除所有角色状态数据')
    clearRoleId()
    dispatch({ type: 'CLEAR_ROLE' })
    dispatch({ type: 'CLEAR_ERROR' })
  }, [])

  // 监听用户状态变化，在用户切换时清除角色数据
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      // 监听认证状态变化，如果用户登出则清除角色状态
      if (e.key === 'auth-storage' || e.key === 'user-session') {
        console.log('🔄 检测到用户状态变化，清除角色数据')
        clearAllRoleData()
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [clearAllRoleData])

  // 获取角色信息的通用方法
  const fetchRoleInfo = async (roleId: string): Promise<CharacterInfo> => {
    try {
      console.log('🔍 查找角色信息:', roleId)

      // 1. 优先从系统角色store中查找
      const systemCharactersStore = useSystemCharactersStore.getState()

      // 先尝试从缓存获取系统角色
      let systemChar = systemCharactersStore.getSystemCharacterById(roleId)

      // 如果缓存中没有或缓存过期，则获取最新数据
      if (!systemChar || !systemCharactersStore.isCacheValid()) {
        console.log('🔄 从API获取系统角色数据')
        try {
          const systemCharacters = await systemCharactersStore.fetchSystemCharacters()
          systemChar = systemCharacters.find((char: any) => char.id === roleId)
        } catch (error) {
          console.warn('⚠️ 获取系统角色数据失败，继续查找用户角色:', error)
        }
      } else {
        console.log('🎯 从缓存获取系统角色数据')
      }

      if (systemChar) {
        console.log('✅ 找到系统角色:', systemChar.name)
        return {
          ...systemChar,
          imageUrl: systemChar.imageUrl || '/images/roles/default.jpg',
          // 系统角色的默认值
          userId: 'system',
          createdAt: systemChar.createdAt || new Date().toISOString(),
          updatedAt: systemChar.updatedAt || new Date().toISOString()
        }
      }

      // 2. 如果系统角色中没有找到，从用户角色store中查找
      const userCharactersStore = useUserCharactersStore.getState()

      // 先尝试从缓存获取用户角色
      let userChar = userCharactersStore.getUserCharacterById(roleId)

      // 如果缓存中没有或缓存过期，则获取最新数据
      if (!userChar || !userCharactersStore.isCacheValid()) {
        console.log('🔄 从API获取用户角色数据')
        try {
          const userCharacters = await userCharactersStore.fetchUserCharacters()
          userChar = userCharacters.find((char: any) => char.id === roleId)
        } catch (error) {
          console.warn('⚠️ 获取用户角色数据失败，继续查找本地角色:', error)
        }
      } else {
        console.log('🎯 从缓存获取用户角色数据')
      }

      if (userChar) {
        console.log('✅ 找到用户角色:', userChar.name)
        return {
          ...userChar,
          imageUrl: userChar.imageUrl || '/images/roles/default.jpg'
        }
      }

      // 如果都没有找到，抛出错误
      throw new Error(`找不到指定的角色: ${roleId}`)
    } catch (error) {
      console.error('❌ 获取角色信息失败:', error)
      throw error
    }
  }

  // 设置当前角色
  const setRole = useCallback(async (roleId: string) => {
    try {
      console.log('🔥 开始设置角色:', roleId)
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'CLEAR_ERROR' })

      const roleInfo = await fetchRoleInfo(roleId)
      console.log('✅ 获取到角色信息:', roleInfo)

      // 保存到本地存储
      saveRoleId(roleId)

      dispatch({ type: 'SET_ROLE', payload: roleInfo })
      console.log('✅ 角色状态设置完成')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '获取角色信息失败'
      console.error('❌ 设置角色失败:', errorMessage)
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
    }
  }, []) // 空依赖数组，因为fetchRoleInfo是稳定的

  // 清除当前角色
  const clearRole = useCallback(() => {
    clearRoleId()
    dispatch({ type: 'CLEAR_ROLE' })
  }, [])

  // 刷新当前角色信息
  const refreshRole = useCallback(async () => {
    if (state.currentRole) {
      await setRole(state.currentRole.id)
    }
  }, [state.currentRole, setRole])

  // 初始化时从本地存储恢复角色
  useEffect(() => {
    const initializeRole = async () => {
      try {
        const savedRoleId = getSavedRoleId()
        if (savedRoleId) {
          console.log('🔄 从本地存储恢复角色:', savedRoleId)
          await setRole(savedRoleId)
        }
      } catch (error) {
        console.error('❌ 初始化角色失败:', error)
        // 如果初始化失败，确保至少有一个可用的状态
        dispatch({ type: 'SET_ERROR', payload: '角色初始化失败，请刷新页面重试' })
      }
    }

    initializeRole()
  }, [])

  const contextValue: RoleContextType = {
    ...state,
    setRole,
    clearRole,
    refreshRole
  }

  return <RoleStoreContext.Provider value={contextValue}>{children}</RoleStoreContext.Provider>
}

// 自定义Hook，用于获取角色状态和操作
export function useRoleStore() {
  const context = useContext(RoleStoreContext)

  if (context === undefined) {
    // 提供更详细的错误信息和调试帮助
    console.error('❌ useRoleStore 调用失败: RoleStoreContext 未定义')
    console.error('请确保组件在 RoleStoreProvider 内部使用')
    console.error('当前组件调用栈:', new Error().stack)
    console.error('当前路径:', window.location.pathname)
    console.error('当前时间:', new Date().toISOString())

    // 提供一个临时的默认状态，避免应用崩溃
    console.warn('⚠️ 使用临时默认状态，请检查 RoleStoreProvider 配置')

    return {
      currentRole: null,
      isLoading: true,
      error: 'RoleStoreProvider 未正确配置',
      setRole: async () => {
        console.error('setRole called outside RoleStoreProvider')
      },
      clearRole: () => {
        console.error('clearRole called outside RoleStoreProvider')
      },
      refreshRole: async () => {
        console.error('refreshRole called outside RoleStoreProvider')
      }
    }
  }

  return context
}

// 兼容性Hook，保持与原有代码的兼容
export function useRoleInfo() {
  const { currentRole, isLoading, error } = useRoleStore()
  return {
    character: currentRole,
    isLoading,
    error
  }
}

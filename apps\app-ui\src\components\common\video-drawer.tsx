import { useState, useEffect } from 'react'
import { <PERSON>dal, Modal<PERSON>ontent, ModalHeader, ModalBody, Button } from '@heroui/react'
import ReactPlayer from 'react-player'
import { MediaDownloader } from '@/utils/media-download'
import { useTranslation } from 'react-i18next'

interface VideoDrawerProps {
  isOpen: boolean
  onClose: () => void
  videoUrl: string
  title?: string
}

export const VideoDrawer = ({ isOpen, onClose, videoUrl, title }: VideoDrawerProps) => {
  const { t } = useTranslation('chat-v2')
  const [playing, setPlaying] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setPlaying(true)
    } else {
      setPlaying(false)
    }
  }, [isOpen])

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      classNames={{
        base: 'bg-black/95',
        body: 'p-0',
        header: 'border-b border-gray-700',
        wrapper: 'z-100'
      }}
      hideCloseButton={true}
      isDismissable={true}
      isKeyboardDismissDisabled={false}
    >
      <ModalContent>
        <ModalHeader className="flex justify-between items-center text-white">
          <h3 className="text-lg font-medium">{title || t('video.playback')}</h3>
          <Button
            isIconOnly
            variant="light"
            onPress={onClose}
            className="text-white hover:bg-white/10"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        </ModalHeader>
        <ModalBody>
          <div className="w-full h-[80vh] flex items-center justify-center bg-black">
            <div className="w-full h-full flex items-center justify-center relative">
              {isOpen && (
                <ReactPlayer
                  src={videoUrl}
                  playing={playing}
                  controls
                  loop
                  width="100%"
                  height="100%"
                  onPlay={() => setPlaying(true)}
                  onPause={() => setPlaying(false)}
                  onError={(error: any) => {
                    console.error(`${t('video.player_error')}:`, error)
                  }}
                />
              )}

              <div className="absolute bottom-4 right-4">
                <Button
                  variant="solid"
                  color="primary"
                  onPress={async () => {
                    try {
                      const result = await MediaDownloader.downloadVideo(videoUrl)
                      if (!result.success) {
                        console.error(`${t('video.download_failed')}:`, result.message)
                      }
                    } catch (error) {
                      console.error(`${t('video.download_error')}:`, error)
                    }
                  }}
                  className="bg-white/20 backdrop-blur-sm hover:bg-white/30"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                  {t('video.download')}
                </Button>
              </div>
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

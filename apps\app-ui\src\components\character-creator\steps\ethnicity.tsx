import React from 'react'
import { Chip, Card, CardBody, Image } from '@heroui/react'
import { useTranslation } from 'react-i18next'
import type { CharacterData } from '..'
import { characterMapping } from '../mapping'

// 人种选项
const ethnicityOptions = Object.entries(characterMapping.ethnicity).map(
  ([value, label], index) => ({
    value,
    label,
    pos: index
  })
)

// 眼睛颜色选项
const eyeColorMap = {
  brown: { color: '#582900' },
  blue: { color: '#0077b6' },
  green: { color: '#2d6a4f' }
}

const eyeColorOptions = Object.entries(characterMapping.eyeColor).map(([value, label], index) => ({
  value,
  label,
  pos: index,
  color: eyeColorMap[value as keyof typeof eyeColorMap]?.color || '#000000'
}))

interface EthnicityProps {
  data: CharacterData
  onUpdate: (data: Partial<CharacterData>) => void
}

// 对应每个section的组件
const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-6">
    <h3 className="font-medium text-center text-lg mb-4 text-primary">{title}</h3>
    {children}
  </div>
)

export default function Ethnicity({ data, onUpdate }: EthnicityProps) {
  const { t } = useTranslation(['customRole'])
  
  // 直接使用gender字段，如果没有则默认为female
  const gender = React.useMemo(() => {
    return (data.gender as 'male' | 'female') || 'female'
  }, [data.gender])

  // 记录当前性别，方便调试
  React.useEffect(() => {
    console.log(`Ethnicity组件检测到性别: ${gender}, gender字段: ${data.gender}`)
  }, [gender, data.gender])

  return (
    <div className="space-y-6">
      {/* 人种选择 */}
      <Section title={t('customRole:ethnicity.select_ethnicity')}>
        <div
          className="grid grid-cols-2 gap-3 sm:grid-cols-3"
          role="radiogroup"
          aria-label={t('customRole:ethnicity.select_ethnicity_aria')}
        >
          {ethnicityOptions.map(option => {
            const imageFile = `${gender === 'female' ? 'female' : 'male'}_0${option.pos + 1}`
            const ethnicityValue = `${gender}-${option.value}`
            const isSelected = data.ethnicity === ethnicityValue

            return (
              <Card
                key={option.value}
                isPressable
                isHoverable
                onPress={() => onUpdate({ ethnicity: ethnicityValue })}
                className={`
                  cursor-pointer transition-all duration-200
                  ${isSelected ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : ''}
                `}
                role="radio"
                aria-checked={isSelected}
                aria-label={t('customRole:ethnicity.select_specific_ethnicity_aria', { label: option.label })}
              >
                <CardBody className="p-0 relative overflow-hidden">
                  <div className="aspect-[1/1] bg-gradient-to-b from-default-100/30 to-transparent">
                    <Image
                      src={`/images/custom/${imageFile}.png`}
                      alt={option.label}
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                      classNames={{
                        wrapper: 'w-full h-full'
                      }}
                    />
                  </div>
                  <Chip
                    color={isSelected ? 'primary' : 'default'}
                    variant={isSelected ? 'solid' : 'flat'}
                    className="absolute bottom-2 right-2 z-10"
                    size="sm"
                    classNames={{
                      content: isSelected ? 'text-white font-medium' : 'text-foreground'
                    }}
                  >
                    {option.label}
                  </Chip>
                </CardBody>
              </Card>
            )
          })}
        </div>
      </Section>

      {/* 眼睛颜色选择 */}
      <Section title={t('customRole:ethnicity.select_eye_color')}>
        <div className="grid grid-cols-3 gap-3" role="radiogroup" aria-label={t('customRole:ethnicity.select_eye_color_aria')}>
          {eyeColorOptions.map(option => {
            const imageFile = `eye-${option.pos + 1}`
            const isSelected = data.eyeColor === option.value

            return (
              <Card
                key={option.value}
                isPressable
                isHoverable
                onPress={() => onUpdate({ eyeColor: option.value })}
                className={`
                  cursor-pointer transition-all duration-200
                  ${isSelected ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : ''}
                `}
                role="radio"
                aria-checked={isSelected}
                aria-label={t('customRole:ethnicity.select_specific_eye_color_aria', { label: option.label })}
              >
                <CardBody className="p-0 relative overflow-hidden">
                  <div className="aspect-[1/1] bg-gradient-to-b from-default-100/30 to-transparent">
                    <Image
                      src={`/images/custom/${imageFile}.png`}
                      alt={option.label}
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                      classNames={{
                        wrapper: 'w-full h-full'
                      }}
                    />
                  </div>
                  <Chip
                    color={isSelected ? 'primary' : 'default'}
                    variant={isSelected ? 'solid' : 'flat'}
                    className="absolute bottom-2 right-2 z-10"
                    size="sm"
                    classNames={{
                      content: isSelected ? 'text-white font-medium' : 'text-foreground'
                    }}
                  >
                    {option.label}
                  </Chip>
                </CardBody>
              </Card>
            )
          })}
        </div>
      </Section>
    </div>
  )
}

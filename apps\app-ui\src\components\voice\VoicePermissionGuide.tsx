import React from 'react'
import { Button } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'

interface VoicePermissionGuideProps {
  /** 重试权限请求的回调 */
  onRetry: () => void
  /** 组件类名 */
  className?: string
}

/**
 * 简化的语音权限引导组件
 * 专为Capacitor应用设计，使用原生权限API
 */
export function VoicePermissionGuide({ onRetry, className }: VoicePermissionGuideProps) {
  const { t } = useTranslation('voice')
  return (
    <div className={`flex items-center gap-3 ${className}`}>
      {/* 权限图标 */}
      <div className="flex-shrink-0">
        <Icon icon="solar:microphone-off-bold" className="text-warning text-xl" />
      </div>

      {/* 提示文字 */}
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-default-700">{t('permission.denied')}</p>
        <p className="text-xs text-default-500">请允许应用访问麦克风以使用语音功能</p>
      </div>

      {/* 重试按钮 */}
      <Button
        size="sm"
        color="primary"
        variant="flat"
        onPress={onRetry}
        className="flex-shrink-0"
        startContent={<Icon icon="solar:refresh-bold" width={16} />}
      >
        {t('permission.retry')}
      </Button>
    </div>
  )
}

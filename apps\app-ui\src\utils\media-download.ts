/**
 * 媒体文件下载工具
 * 使用 @capacitor-community/media 插件保存到设备相册
 */
import { CapacitorHttp } from '@capacitor/core'
import { Capacitor } from '@capacitor/core'
import { Filesystem, Directory } from '@capacitor/filesystem'
import { addToast } from '@heroui/react'

export interface DownloadResult {
  success: boolean
  message: string
  filePath?: string
}

export class MediaDownloader {
  // 缓存相册 ID，避免重复创建
  private static pleasureHubAlbumId: string | null = null
  /**
   * 获取文件扩展名
   */
  private static getFileExtension(url: string): string {
    const urlWithoutQuery = url.split('?')[0]
    const parts = urlWithoutQuery.split('.')
    return parts.length > 1 ? parts[parts.length - 1] : 'bin'
  }

  /**
   * 生成唯一文件名
   */
  private static generateFileName(originalUrl: string, prefix: string = 'download'): string {
    const extension = this.getFileExtension(originalUrl)
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    return `${prefix}-${timestamp}.${extension}`
  }

  /**
   * 显示 Toast 消息
   */
  private static showToast(message: string, type: 'success' | 'error' | 'info' = 'info') {
    try {
      const color = type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'

      // 添加详细日志用于调试
      console.log(`📱 Toast: ${message} (${type})`)

      addToast({
        title: type === 'success' ? '下载成功' : type === 'error' ? '下载失败' : '提示',
        description: message,
        color: color as any
      })
    } catch (error) {
      // 如果 addToast 不可用，降级到 console
      console.log(`📱 ${message}`)
      console.error('Toast显示失败:', error)
    }
  }

  /**
   * 检查媒体权限
   */
  private static async checkMediaPermissions(): Promise<boolean> {
    try {
      if (Capacitor.getPlatform() === 'web') {
        return true
      }

      // 对于原生平台，尝试通过getAlbums调用来间接检查权限
      // 如果权限未授予，getAlbums通常会失败或返回空结果
      return true // 我们将通过实际调用来检查
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  }

  /**
   * 获取或创建 PleasureHub 相册
   */
  private static async getOrCreatePleasureHubAlbum(Media: any): Promise<string | null> {
    try {
      // 如果已经缓存了相册 ID，直接返回
      if (this.pleasureHubAlbumId) {
        return this.pleasureHubAlbumId
      }

      // 获取所有相册
      console.log('正在获取设备相册列表...')
      const albumsResponse = await Media.getAlbums()
      console.log('Albums response:', albumsResponse)
      console.log('Available albums:', albumsResponse.albums)
      console.log('Albums count:', albumsResponse.albums?.length || 0)

      // 查找是否已存在 PleasureHub 相册
      const existingAlbum = albumsResponse.albums?.find(
        (album: any) => album.name === 'PleasureHub'
      )

      if (existingAlbum) {
        this.pleasureHubAlbumId = existingAlbum.identifier
        console.log('Found existing PleasureHub album:', existingAlbum.identifier)
        return existingAlbum.identifier
      }

      // 如果不存在，尝试创建新相册（仅 iOS 支持）
      if (Capacitor.getPlatform() === 'ios') {
        const newAlbum = await Media.createAlbum({ name: 'PleasureHub' })
        this.pleasureHubAlbumId = newAlbum.identifier
        console.log('Created new PleasureHub album:', newAlbum.identifier)
        return newAlbum.identifier
      }

      // Android: 必须使用现有相册，查找最适合的相册
      if (albumsResponse.albums && albumsResponse.albums.length > 0) {
        // 优先查找相机相册
        const cameraAlbum = albumsResponse.albums.find(
          (album: any) =>
            album.name?.toLowerCase().includes('camera') ||
            album.name?.toLowerCase().includes('dcim') ||
            album.name?.toLowerCase().includes('pictures')
        )

        if (cameraAlbum) {
          this.pleasureHubAlbumId = cameraAlbum.identifier
          console.log('Using camera album:', cameraAlbum)
          return cameraAlbum.identifier
        }

        // 如果没找到相机相册，使用第一个用户相册
        const firstUserAlbum = albumsResponse.albums.find((album: any) => album.type === 'user')
        if (firstUserAlbum) {
          this.pleasureHubAlbumId = firstUserAlbum.identifier
          console.log('Using first user album:', firstUserAlbum)
          return firstUserAlbum.identifier
        }

        // 最后使用第一个可用相册
        const firstAlbum = albumsResponse.albums[0]
        this.pleasureHubAlbumId = firstAlbum.identifier
        console.log('Using first available album:', firstAlbum)
        return firstAlbum.identifier
      }

      console.warn('No albums available on device')
      return null
    } catch (error) {
      console.error('获取或创建相册失败:', error)
      return null
    }
  }

  /**
   * 下载图片到相册
   */
  static async downloadImage(imageUrl: string, fileName?: string): Promise<DownloadResult> {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web环境：使用传统下载方法
        return this.downloadFileWeb(imageUrl, fileName || 'image')
      }

      this.showToast('开始下载图片...', 'info')

      // 动态导入 @capacitor-community/media
      const { Media } = await import('@capacitor-community/media')

      let base64Data: string

      // 检查是否为 data URI
      if (imageUrl.startsWith('data:')) {
        console.log('处理 data URI 格式的图片')
        // 提取 base64 数据
        const base64Match = imageUrl.match(/^data:[^;]+;base64,(.+)$/)
        if (!base64Match) {
          throw new Error('无效的 data URI 格式')
        }
        base64Data = base64Match[1]
      } else {
        // HTTP/HTTPS URL：下载文件数据
        console.log('下载网络图片:', imageUrl)
        const response = await CapacitorHttp.get({
          url: imageUrl,
          responseType: 'blob'
        })

        if (response.status < 200 || response.status >= 300) {
          throw new Error(`HTTP ${response.status}: 下载失败`)
        }

        // 转换为base64
        if (response.data instanceof ArrayBuffer) {
          const uint8Array = new Uint8Array(response.data)
          base64Data = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)))
        } else if (typeof response.data === 'string') {
          base64Data = response.data
        } else {
          throw new Error('不支持的数据格式')
        }
      }

      // 生成文件名
      const finalFileName = fileName || this.generateFileName(imageUrl, 'image')

      // 先保存到临时文件
      const tempFileName = `temp_${Date.now()}.jpg`
      await Filesystem.writeFile({
        path: tempFileName,
        data: base64Data,
        directory: Directory.Cache
      })

      // 获取临时文件的完整路径
      const tempFileUri = await Filesystem.getUri({
        directory: Directory.Cache,
        path: tempFileName
      })

      // 尝试获取相册，如果失败则保存到默认位置
      let albumIdentifier: string | null = null
      try {
        albumIdentifier = await this.getOrCreatePleasureHubAlbum(Media)
      } catch (error) {
        console.warn('无法获取相册，将保存到默认位置:', error)
      }

      // 保存到相册
      const saveOptions: any = {
        path: tempFileUri.uri,
        fileName: finalFileName
      }

      // 只有在成功获取相册 ID 时才指定
      if (albumIdentifier) {
        saveOptions.albumIdentifier = albumIdentifier
      }

      const result = await Media.savePhoto(saveOptions)

      // 清理临时文件
      try {
        await Filesystem.deleteFile({
          path: tempFileName,
          directory: Directory.Cache
        })
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError)
      }

      console.log('✅ 图片下载成功，即将显示Toast')
      this.showToast('图片已保存到相册！', 'success')

      return {
        success: true,
        message: '图片下载成功',
        filePath: result.filePath
      }
    } catch (error: any) {
      console.error('图片下载失败:', error)

      // 如果是插件未安装的错误，提供友好提示
      if (error?.toString?.().includes('Media')) {
        this.showToast('需要安装媒体插件才能保存到相册', 'error')
      } else {
        this.showToast('图片下载失败', 'error')
      }

      return {
        success: false,
        message: `下载失败: ${error?.message || error}`
      }
    }
  }

  /**
   * 下载视频到相册
   */
  static async downloadVideo(videoUrl: string, fileName?: string): Promise<DownloadResult> {
    try {
      if (!Capacitor.isNativePlatform()) {
        // Web环境：使用传统下载方法
        return this.downloadFileWeb(videoUrl, fileName || 'video')
      }

      this.showToast('开始下载视频...', 'info')

      // 动态导入 @capacitor-community/media
      const { Media } = await import('@capacitor-community/media')

      let base64Data: string

      // 检查是否为 data URI
      if (videoUrl.startsWith('data:')) {
        console.log('处理 data URI 格式的视频')
        // 提取 base64 数据
        const base64Match = videoUrl.match(/^data:[^;]+;base64,(.+)$/)
        if (!base64Match) {
          throw new Error('无效的 data URI 格式')
        }
        base64Data = base64Match[1]
      } else {
        // HTTP/HTTPS URL：下载文件数据
        console.log('下载网络视频:', videoUrl)
        const response = await CapacitorHttp.get({
          url: videoUrl,
          responseType: 'blob'
        })

        if (response.status < 200 || response.status >= 300) {
          throw new Error(`HTTP ${response.status}: 下载失败`)
        }

        // 转换为base64
        if (response.data instanceof ArrayBuffer) {
          const uint8Array = new Uint8Array(response.data)
          base64Data = btoa(String.fromCharCode.apply(null, Array.from(uint8Array)))
        } else if (typeof response.data === 'string') {
          base64Data = response.data
        } else {
          throw new Error('不支持的数据格式')
        }
      }

      // 生成文件名
      const finalFileName = fileName || this.generateFileName(videoUrl, 'video')

      // 先保存到临时文件
      const tempFileName = `temp_${Date.now()}.mp4`
      await Filesystem.writeFile({
        path: tempFileName,
        data: base64Data,
        directory: Directory.Cache
      })

      // 获取临时文件的完整路径
      const tempFileUri = await Filesystem.getUri({
        directory: Directory.Cache,
        path: tempFileName
      })

      // 尝试获取相册，如果失败则保存到默认位置
      let albumIdentifier: string | null = null
      try {
        albumIdentifier = await this.getOrCreatePleasureHubAlbum(Media)
      } catch (error) {
        console.warn('无法获取相册，将保存到默认位置:', error)
      }

      // 保存到相册
      const saveOptions: any = {
        path: tempFileUri.uri,
        fileName: finalFileName
      }

      // 只有在成功获取相册 ID 时才指定
      if (albumIdentifier) {
        saveOptions.albumIdentifier = albumIdentifier
      }

      const result = await Media.saveVideo(saveOptions)

      // 清理临时文件
      try {
        await Filesystem.deleteFile({
          path: tempFileName,
          directory: Directory.Cache
        })
      } catch (cleanupError) {
        console.warn('清理临时文件失败:', cleanupError)
      }

      console.log('✅ 视频下载成功，即将显示Toast')
      this.showToast('视频已保存到相册！', 'success')

      return {
        success: true,
        message: '视频下载成功',
        filePath: result.filePath
      }
    } catch (error: any) {
      console.error('视频下载失败:', error)

      // 如果是插件未安装的错误，提供友好提示
      if (error?.toString?.().includes('Media')) {
        this.showToast('需要安装媒体插件才能保存到相册', 'error')
      } else {
        this.showToast('视频下载失败', 'error')
      }

      return {
        success: false,
        message: `下载失败: ${error?.message || error}`
      }
    }
  }

  /**
   * Web环境下的下载方法（传统方式）
   */
  private static downloadFileWeb(url: string, defaultName: string): DownloadResult {
    try {
      const link = document.createElement('a')
      link.href = url
      const fileName = this.generateFileName(url, defaultName)
      link.download = fileName
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      this.showToast('下载已开始', 'success')

      return {
        success: true,
        message: '下载已开始'
      }
    } catch (error) {
      this.showToast('下载失败', 'error')
      return {
        success: false,
        message: `下载失败: ${error}`
      }
    }
  }

  /**
   * 通用下载方法（自动检测文件类型）
   */
  static async downloadMedia(url: string, fileName?: string): Promise<DownloadResult> {
    const extension = this.getFileExtension(url).toLowerCase()

    // 图片格式
    if (['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'].includes(extension)) {
      return this.downloadImage(url, fileName)
    }
    // 视频格式
    else if (['mp4', 'webm', 'mov', 'avi'].includes(extension)) {
      return this.downloadVideo(url, fileName)
    }
    // 其他格式，默认作为图片处理
    else {
      return this.downloadImage(url, fileName)
    }
  }
}

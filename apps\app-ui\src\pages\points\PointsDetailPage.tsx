import { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/contexts/auth-context'
import { useUserPoints, usePointsTransactions } from '@/hooks/use-membership'

// HeroUI 组件
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Chip,
  Divider,
  addToast,
  Progress,
  Spinner
} from '@heroui/react'

// Lucide 图标
import {
  ArrowLeft,
  Coins,
  TrendingUp,
  TrendingDown,
  Clock,
  Gift,
  Crown,
  Zap,
  RefreshCw
} from 'lucide-react'

export default function PointsDetailPage() {
  const { t } = useTranslation('points')
  const navigate = useNavigate()
  const { status } = useAuth()
  const { points, isLoading: pointsLoading, refetch: refetchPoints } = useUserPoints()
  const {
    transactions,
    isLoading: transactionsLoading,
    refetch: refetchTransactions
  } = usePointsTransactions(50)

  // 检查认证状态
  useEffect(() => {
    if (status === 'unauthenticated') {
      addToast({
        title: t('common.login_required'),
        color: 'warning'
      })
      navigate('/login')
    }
  }, [status, navigate, t])

  const handleRefresh = useCallback(async () => {
    try {
      await Promise.all([refetchPoints(), refetchTransactions()])
      addToast({
        title: t('detail.refresh_success'),
        color: 'success'
      })
    } catch (error) {
      addToast({
        title: t('detail.refresh_failed'),
        color: 'danger'
      })
    }
  }, [refetchPoints, refetchTransactions, t])

  // 获取来源标题
  const getSourceTitle = useCallback(
    (source: string) => {
      const sourceKey = `transaction.sources.${source}` as const
      return t(sourceKey, { defaultValue: t('transaction.sources.default') })
    },
    [t]
  )

  // 获取交易类型的显示信息
  const getTransactionInfo = useCallback(
    (transaction: any) => {
      const { transactionType, source, amount, description } = transaction

      switch (transactionType) {
        case 'earn':
          return {
            icon: <TrendingUp className="w-4 h-4 text-success" />,
            color: 'success' as const,
            prefix: '+',
            title: getSourceTitle(source),
            description: description || t('transaction.descriptions.earn')
          }
        case 'spend':
          return {
            icon: <TrendingDown className="w-4 h-4 text-danger" />,
            color: 'danger' as const,
            prefix: '-',
            title: getSourceTitle(source),
            description: description || t('transaction.descriptions.spend')
          }
        case 'upgrade_bonus':
          return {
            icon: <Crown className="w-4 h-4 text-warning" />,
            color: 'warning' as const,
            prefix: '+',
            title: t('transaction.types.upgrade_bonus'),
            description: description || t('transaction.descriptions.upgrade_bonus')
          }
        case 'cycle_grant':
          return {
            icon: <Gift className="w-4 h-4 text-primary" />,
            color: 'primary' as const,
            prefix: '+',
            title: t('transaction.types.cycle_grant'),
            description: description || t('transaction.descriptions.cycle_grant')
          }
        case 'cycle_reset':
          return {
            icon: <RefreshCw className="w-4 h-4 text-secondary" />,
            color: 'secondary' as const,
            prefix: amount > 0 ? '+' : '',
            title: t('transaction.types.cycle_reset'),
            description: description || t('transaction.descriptions.cycle_reset')
          }
        default:
          return {
            icon: <Coins className="w-4 h-4 text-default-500" />,
            color: 'default' as const,
            prefix: amount > 0 ? '+' : '-',
            title: t('transaction.types.points_change'),
            description: description || t('transaction.descriptions.default')
          }
      }
    },
    [t, getSourceTitle]
  )

  // 格式化日期
  const formatDate = useCallback(
    (dateString: string) => {
      const date = new Date(dateString)
      const now = new Date()
      const diff = now.getTime() - date.getTime()
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))

      if (days === 0) {
        return (
          t('time.today') +
          ' ' +
          date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        )
      } else if (days === 1) {
        return (
          t('time.yesterday') +
          ' ' +
          date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
        )
      } else if (days < 7) {
        return t('time.days_ago', { days })
      } else {
        return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
      }
    },
    [t]
  )

  const isLoading = useMemo(
    () => pointsLoading || transactionsLoading,
    [pointsLoading, transactionsLoading]
  )

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Spinner size="lg" color="primary" />
          <p className="text-default-500">{t('detail.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-[#121521] from-background via-content1 to-content2 safe-area-top">
      {/* 顶部导航 */}
      <div className="grid grid-cols-3 text-center items-center p-4 pt-12 relative">
        <Button
          isIconOnly
          variant="light"
          className="text-[#fff] hover:text-foreground relative z-1"
          onPress={() => navigate(-1)}
        >
          <ArrowLeft className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-[#fff] relative z-1">{t('detail.title')}</h1>
        {/* 预留刷新按钮位置 */}
        <div />

        <img src="/images/point/lightning-lg.svg" alt="" className="absolute top-0 right-0 w-39" />
      </div>

      <img src="/images/point/light-bg.svg" alt="" className="absolute top-0 left-0 w-full" />

      {/* 积分概览 */}
      {points && (
        <div className="px-4 relative flex items-center gap-4 mb-6">
          <img src="/images/point/point-icon.png" alt="" className="w-13 h-13" />
          <div>
            <span className="text-sm text-[#fff] font-semibold">
              {t('detail.remaining_points')}
            </span>
            <p>
              <span className="text-3xl font-semibold text-[#FF831D]">
                {points.availablePoints}
              </span>
              <span className="text-[#7C85B6]">/{points.totalPoints}</span>
            </p>
          </div>
        </div>
      )}

      {/* 交易记录 */}
      <div className="px-4 relative">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-sm font-semibold text-[#7C85B6]">
            {t('detail.transaction_history')}
          </h2>
        </div>

        <div className="bg-inherit backdrop-blur-sm">
          <div className="p-0">
            {transactions && transactions.length > 0 ? (
              <div className="divide-y divide-default-200">
                {transactions.map((transaction, index) => {
                  const info = getTransactionInfo(transaction)
                  return (
                    <div
                      key={transaction.id || index}
                      className="border-none p-4 bg-[#1D2135] rounded-3xl mb-2"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div>
                            <p className="text-[#fff] text-base font-medium">{info.title}</p>
                          </div>
                        </div>

                        <div className="text-right">
                          <div className="flex items-center gap-2">
                            <span className="text-[#FF831D] text-base font-medium">
                              -{Math.abs(transaction.amount)}
                              {t('store.points_unit')}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* 余额信息 */}
                      <div className="mt-4">
                        <p className="text-sm text-[#556097] flex justify-between items-center">
                          <span>{formatDate(transaction.createdAt)}</span>
                          <span>
                            {t('detail.transaction_after_balance', {
                              balance: transaction.balanceAfter
                            })}
                          </span>
                        </p>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="p-8 text-center">
                <Coins className="w-12 h-12 text-default-300 mx-auto mb-4" />
                <p className="text-default-500 mb-2">{t('detail.no_transactions')}</p>
                <p className="text-xs text-default-400">
                  {t('detail.no_transactions_description')}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 底部空间 */}
      <div className="h-16" />
    </div>
  )
}

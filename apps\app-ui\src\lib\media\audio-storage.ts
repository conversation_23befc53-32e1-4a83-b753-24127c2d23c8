/**
 * 音频本地存储工具
 * 使用 Capacitor Filesystem 存储TTS音频文件
 */

import { Filesystem, Directory } from '@capacitor/filesystem'
import { Capacitor } from '@capacitor/core'
import { FileSystemDownloadService } from '@/services/filesystem-download'

const AUDIO_DIR = 'chat_audios'

export interface AudioFile {
  url: string
  path: string
  timestamp: number
  messageId?: string
  textHash?: string
}

export class AudioStorage {
  private static instance: AudioStorage

  static getInstance(): AudioStorage {
    if (!AudioStorage.instance) {
      AudioStorage.instance = new AudioStorage()
    }
    return AudioStorage.instance
  }

  private constructor() {}

  /**
   * 初始化音频存储目录
   */
  async initialize(): Promise<void> {
    try {
      await Filesystem.mkdir({
        path: AUDIO_DIR,
        directory: Directory.Data,
        recursive: true
      })
      console.log('🎵 [AudioStorage] 音频目录初始化完成')
    } catch (error) {
      console.warn('⚠️ [AudioStorage] 目录可能已存在:', error)
    }
  }

  /**
   * 保存音频文件并返回文件路径
   */
  async saveAudio(
    audioData: Uint8Array | Blob,
    messageId: string,
    textHash?: string
  ): Promise<string> {
    try {
      const timestamp = Date.now()
      const hashSuffix = textHash ? `_${textHash.substring(0, 8)}` : ''
      const filename = `audio_${messageId}${hashSuffix}_${timestamp}.mp3`
      const path = `${AUDIO_DIR}/${filename}`

      // 确保目录存在
      await this.ensureDirectoryExists()

      let base64Data: string

      if (audioData instanceof Uint8Array) {
        // 直接从Uint8Array转换
        base64Data = this.uint8ArrayToBase64(audioData)
      } else {
        // 从Blob转换
        base64Data = await this.blobToBase64(audioData)
      }

      // 保存文件
      await Filesystem.writeFile({
        path,
        data: base64Data,
        directory: Directory.Data
      })

      console.log('💾 [AudioStorage] 音频已保存:', path, '大小:', base64Data.length, 'bytes')
      return path
    } catch (error) {
      console.error('❌ [AudioStorage] 保存音频失败:', error)
      throw error
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(): Promise<void> {
    try {
      await Filesystem.mkdir({
        path: AUDIO_DIR,
        directory: Directory.Data,
        recursive: true
      })
    } catch (error) {
      // 目录可能已存在，忽略错误
      console.log('📁 [AudioStorage] 目录检查:', error)
    }
  }

  /**
   * 从URL下载音频并保存到本地
   * 🆕 使用FileSystemDownloadService避免CORS问题
   */
  async saveAudioFromUrl(audioUrl: string, messageId: string, textHash?: string): Promise<string> {
    try {
      console.log('📥 [AudioStorage] 开始下载音频:', audioUrl.substring(0, 50))

      // 🆕 使用FileSystemDownloadService下载
      const downloadedFile = await FileSystemDownloadService.downloadFileToFileSystem(
        audioUrl,
        `chat_audio_${messageId}` // 使用messageId作为scriptId
      )

      // 复制到我们自己的目录结构
      const timestamp = Date.now()
      const hashSuffix = textHash ? `_${textHash.substring(0, 8)}` : ''
      const filename = `audio_${messageId}${hashSuffix}_${timestamp}.mp3`
      const targetPath = `${AUDIO_DIR}/${filename}`

      // 读取下载的文件
      const fileContent = await Filesystem.readFile({
        path: downloadedFile.filePath,
        directory: Directory.Data
      })

      // 保存到我们的目录
      await Filesystem.writeFile({
        path: targetPath,
        data: fileContent.data,
        directory: Directory.Data
      })

      console.log('✅ [AudioStorage] 音频下载并保存完成:', targetPath)
      return targetPath
    } catch (error) {
      console.error('❌ [AudioStorage] 从URL保存音频失败:', error)
      throw error
    }
  }

  /**
   * 获取音频的可用URL
   */
  async getAudioUrl(path: string): Promise<string | null> {
    try {
      console.log('🎵 [AudioStorage] 尝试使用本地音频文件:', path)

      // 在移动设备上，需要读取文件并转换为data URL
      if (Capacitor.isNativePlatform()) {
        try {
          const result = await Filesystem.readFile({
            path,
            directory: Directory.Data
          })

          // 检查返回的数据格式
          if (typeof result.data === 'string') {
            const dataUrl = `data:audio/mpeg;base64,${result.data}`
            console.log('✅ [AudioStorage] 成功转换为data URL，大小:', dataUrl.length, 'bytes')
            return dataUrl
          } else {
            console.error('❌ [AudioStorage] 文件读取返回格式错误')
            return null
          }
        } catch (readError) {
          console.error('❌ [AudioStorage] 文件读取失败:', readError)
          return null
        }
      } else {
        // Web环境，尝试使用getUri
        try {
          const uriResult = await Filesystem.getUri({
            path,
            directory: Directory.Data
          })
          console.log('✅ [AudioStorage] 使用文件URI:', uriResult.uri)
          return uriResult.uri
        } catch (uriError) {
          console.error('❌ [AudioStorage] 获取URI失败:', uriError)
          return null
        }
      }
    } catch (error) {
      console.error('❌ [AudioStorage] 获取音频URL失败:', error)
      return null
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(path: string): Promise<boolean> {
    try {
      await Filesystem.stat({
        path,
        directory: Directory.Data
      })
      return true
    } catch {
      return false
    }
  }

  /**
   * 删除音频文件
   */
  async deleteAudio(path: string): Promise<void> {
    try {
      await Filesystem.deleteFile({
        path,
        directory: Directory.Data
      })
      console.log('🗑️ [AudioStorage] 音频已删除:', path)
    } catch (error) {
      console.error('❌ [AudioStorage] 删除音频失败:', error)
      throw error
    }
  }

  /**
   * 根据messageId查找音频文件
   */
  async findAudioByMessageId(messageId: string): Promise<string | null> {
    try {
      const result = await Filesystem.readdir({
        path: AUDIO_DIR,
        directory: Directory.Data
      })

      const audioFile = result.files.find(
        file => file.name.startsWith(`audio_${messageId}`) && file.name.endsWith('.mp3')
      )

      if (audioFile) {
        const path = `${AUDIO_DIR}/${audioFile.name}`
        console.log('🔍 [AudioStorage] 找到音频文件:', path)
        return path
      }

      return null
    } catch (error) {
      console.warn('⚠️ [AudioStorage] 查找音频文件失败:', error)
      return null
    }
  }

  /**
   * 清理所有音频文件
   */
  async clearAllAudios(): Promise<void> {
    try {
      await Filesystem.rmdir({
        path: AUDIO_DIR,
        directory: Directory.Data,
        recursive: true
      })

      // 重新创建目录
      await this.initialize()
      console.log('🧹 [AudioStorage] 所有音频已清理')
    } catch (error) {
      console.error('❌ [AudioStorage] 清理音频失败:', error)
      throw error
    }
  }

  /**
   * 将 Uint8Array 转换为 base64 字符串
   */
  private uint8ArrayToBase64(uint8Array: Uint8Array): string {
    const binaryString = Array.from(uint8Array, byte => String.fromCharCode(byte)).join('')
    return btoa(binaryString)
  }

  /**
   * 将 Blob 转换为 base64 字符串
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // 移除 data URL 前缀，只保留 base64 数据
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 获取目录中的所有音频文件
   */
  async listAllAudios(): Promise<string[]> {
    try {
      const result = await Filesystem.readdir({
        path: AUDIO_DIR,
        directory: Directory.Data
      })

      return result.files
        .filter(file => file.name.startsWith('audio_') && file.name.endsWith('.mp3'))
        .map(file => `${AUDIO_DIR}/${file.name}`)
    } catch (error) {
      console.error('❌ [AudioStorage] 列出音频文件失败:', error)
      return []
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalFiles: number
    totalSize: number
  }> {
    try {
      const audioFiles = await this.listAllAudios()
      let totalSize = 0

      for (const audioPath of audioFiles) {
        try {
          const stat = await Filesystem.stat({
            path: audioPath,
            directory: Directory.Data
          })
          totalSize += stat.size || 0
        } catch (error) {
          console.warn('⚠️ [AudioStorage] 获取文件大小失败:', audioPath)
        }
      }

      return {
        totalFiles: audioFiles.length,
        totalSize
      }
    } catch (error) {
      console.error('❌ [AudioStorage] 获取存储统计失败:', error)
      return { totalFiles: 0, totalSize: 0 }
    }
  }
}

import type { Env } from '@/types/env';
import type { CacheOptions, CachedData } from '@/types/api';

// 缓存工具类
export class CacheManager {
  constructor(private env: Env) {}

  // 设置缓存
  async set<T>(key: string, data: T, ttl = 3600): Promise<void> {
    const cachedData: CachedData<T> = {
      data,
      timestamp: Date.now(),
      ttl,
    };

    await this.env.CACHE.put(key, JSON.stringify(cachedData), {
      expirationTtl: ttl,
    });
  }

  // 获取缓存
  async get<T>(key: string): Promise<T | null> {
    const cached = await this.env.CACHE.get(key);

    if (!cached) {
      return null;
    }

    try {
      const cachedData: CachedData<T> = JSON.parse(cached);

      // 检查是否过期
      const now = Date.now();
      if (now - cachedData.timestamp > cachedData.ttl * 1000) {
        await this.delete(key);
        return null;
      }

      return cachedData.data;
    } catch (error) {
      console.error('缓存解析错误:', error);
      await this.delete(key);
      return null;
    }
  }

  // 删除缓存
  async delete(key: string): Promise<void> {
    await this.env.CACHE.delete(key);
  }

  // 清空所有缓存 (谨慎使用)
  async clear(): Promise<void> {
    // KV 不支持批量删除，需要逐个删除
    // 这里只是示例，实际使用时需要维护一个键列表
    console.warn('KV 不支持批量清空，需要逐个删除键');
  }

  // 缓存装饰器函数
  async cached<T>(key: string, fetcher: () => Promise<T>, ttl = 3600): Promise<T> {
    // 先尝试从缓存获取
    const cached = await this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // 缓存未命中，执行获取函数
    const data = await fetcher();

    // 存入缓存
    await this.set(key, data, ttl);

    return data;
  }
}

// 创建缓存管理器实例
export function createCacheManager(env: Env): CacheManager {
  return new CacheManager(env);
}

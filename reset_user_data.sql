-- 清空用户 b98ba07a-289b-41c4-8949-6457559e82ca 的会员数据
-- 请按顺序执行以下SQL语句

-- 1. 删除积分交易记录
DELETE FROM "PointsTransaction" 
WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca';

-- 2. 删除订阅历史记录  
DELETE FROM "SubscriptionHistory"
WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca';

-- 3. 删除用户订阅记录
DELETE FROM "UserSubscription"
WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca';

-- 4. 重置用户积分记录为初始状态（免费用户：5积分）
UPDATE "UserPoints" 
SET 
  total_points = 5,
  used_points = 0,
  available_points = 5,
  cycle_start_date = NULL,
  cycle_end_date = NULL,
  membership_level = NULL,
  monthly_allocation = 0,
  cycle_consumed = 0,
  cycle_gifted = 0,
  cycle_received = 0,
  last_cycle_check = NULL,
  last_updated = NOW()
WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca';

-- 5. 如果用户积分记录不存在，创建一个新的（可选）
INSERT INTO "UserPoints" (
  user_id, 
  total_points, 
  used_points, 
  available_points,
  cycle_start_date,
  cycle_end_date,
  membership_level,
  monthly_allocation,
  cycle_consumed,
  cycle_gifted,
  cycle_received,
  last_cycle_check,
  last_updated
) 
SELECT 
  'b98ba07a-289b-41c4-8949-6457559e82ca',
  5,
  0,
  5,
  NULL,
  NULL,
  NULL,
  0,
  0,
  0,
  0,
  NULL,
  NOW()
WHERE NOT EXISTS (
  SELECT 1 FROM "UserPoints" 
  WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca'
);

-- 验证清空结果
SELECT 'UserPoints' as table_name, count(*) as records FROM "UserPoints" WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca'
UNION ALL
SELECT 'UserSubscription' as table_name, count(*) as records FROM "UserSubscription" WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca'
UNION ALL  
SELECT 'SubscriptionHistory' as table_name, count(*) as records FROM "SubscriptionHistory" WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca'
UNION ALL
SELECT 'PointsTransaction' as table_name, count(*) as records FROM "PointsTransaction" WHERE user_id = 'b98ba07a-289b-41c4-8949-6457559e82ca';
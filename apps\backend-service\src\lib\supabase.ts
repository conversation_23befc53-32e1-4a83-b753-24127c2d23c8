import { createClient, type SupabaseClient } from '@supabase/supabase-js';
import type { Env } from '@/types/env';

// 创建 Supabase 客户端 (使用 anon key，用于客户端操作)
export function createSupabaseClient(env: Env): SupabaseClient {
  return createClient(env.SUPABASE_URL, env.SUPABASE_ANON_KEY, {
    auth: {
      autoRefreshToken: true, // ✅ 启用自动刷新令牌
      persistSession: true, // ✅ 启用会话持久化
      detectSessionInUrl: false, // 服务端不需要检测URL中的会话
      // 注意：refreshThreshold 在服务端不可用，需要在客户端配置
    },
    global: {
      fetch: (url, options = {}) => {
        // 添加超时和重试机制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

        return fetch(url, {
          ...options,
          signal: controller.signal,
        }).finally(() => {
          clearTimeout(timeoutId);
        });
      },
    },
  });
}

// 创建 Supabase 服务端客户端 (使用 service role key，用于管理员操作)
export function createSupabaseServiceClient(env: Env): SupabaseClient {
  return createClient(env.SUPABASE_URL, env.SUPABASE_SERVICE_ROLE_KEY, {
    auth: {
      autoRefreshToken: true, // ✅ 启用自动刷新令牌
      persistSession: true, // ✅ 启用会话持久化
      detectSessionInUrl: false, // 服务端不需要检测URL中的会话
    },
    global: {
      fetch: (url, options = {}) => {
        // 添加超时和重试机制
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时

        return fetch(url, {
          ...options,
          signal: controller.signal,
        }).finally(() => {
          clearTimeout(timeoutId);
        });
      },
    },
  });
}

// 带重试机制的验证 JWT Token
export async function verifySupabaseToken(
  env: Env,
  token: string,
  maxRetries = 2
): Promise<{ user: any; error: any }> {
  let lastError: any = null;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const supabase = createSupabaseClient(env);
      const { data, error } = await supabase.auth.getUser(token);

      // 如果成功或者是认证错误（不是网络错误），直接返回
      if (!error || (error && !isNetworkError(error))) {
        return {
          user: data.user,
          error,
        };
      }

      lastError = error;

      // 如果是网络错误且还有重试次数，等待后重试
      if (attempt < maxRetries) {
        console.warn(`Supabase认证网络错误，第${attempt + 1}次重试...`, error.message);
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000)); // 指数退避
        continue;
      }
    } catch (error) {
      lastError = error;

      // 如果是网络错误且还有重试次数，等待后重试
      if (isNetworkError(error) && attempt < maxRetries) {
        console.warn(`Supabase认证网络错误，第${attempt + 1}次重试...`, error);
        await new Promise((resolve) => setTimeout(resolve, Math.pow(2, attempt) * 1000)); // 指数退避
        continue;
      }
    }
  }

  return {
    user: null,
    error: lastError,
  };
}

// 判断是否为网络错误
function isNetworkError(error: any): boolean {
  if (!error) return false;

  const errorMessage = error.message?.toLowerCase() || '';
  const errorCode = error.code?.toLowerCase() || '';

  return (
    errorMessage.includes('network') ||
    errorMessage.includes('connection') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('fetch') ||
    errorCode.includes('network') ||
    error.name === 'AbortError' ||
    error.retryable === true
  );
}

// 发送验证码到邮箱 (用于注册)
export async function sendVerificationCode(
  env: Env,
  email: string
): Promise<{ success: boolean; error?: any }> {
  const supabase = createSupabaseClient(env);

  const { error } = await supabase.auth.signInWithOtp({
    email,
    options: {
      shouldCreateUser: true, // 如果用户不存在则创建
      data: {
        // 可以添加额外的用户元数据
      },
    },
  });

  if (error) {
    return { success: false, error };
  }

  return { success: true };
}

// 验证验证码并完成注册
export async function verifyCodeAndSignUp(
  env: Env,
  email: string,
  token: string
): Promise<{ data: any; error: any }> {
  const supabase = createSupabaseClient(env);

  return await supabase.auth.verifyOtp({
    email,
    token,
    type: 'email',
  });
}

// 用户注册 (邮箱密码)
export async function signUpWithEmail(
  env: Env,
  email: string,
  password: string,
  metadata?: Record<string, any>
) {
  const supabase = createSupabaseClient(env);

  return await supabase.auth.signUp({
    email,
    password,
    options: {
      data: metadata,
    },
  });
}

// 用户登录 (邮箱密码)
export async function signInWithEmail(env: Env, email: string, password: string) {
  const supabase = createSupabaseClient(env);

  return await supabase.auth.signInWithPassword({
    email,
    password,
  });
}

// 用户登出
export async function signOut(env: Env, token?: string) {
  const supabase = createSupabaseClient(env);

  if (token) {
    // 如果提供了 token，先设置会话
    await supabase.auth.setSession({
      access_token: token,
      refresh_token: '', // 这里可能需要实际的 refresh token
    });
  }

  return await supabase.auth.signOut();
}

// 管理员创建用户 (用于服务端操作)
export async function signUpUser(
  env: Env,
  email: string,
  password: string,
  metadata?: Record<string, any>
) {
  const supabase = createSupabaseServiceClient(env);

  return await supabase.auth.admin.createUser({
    email,
    password,
    user_metadata: metadata,
    email_confirm: true, // 自动确认邮箱
  });
}

// 用户登录 (保持向后兼容)
export async function signInUser(env: Env, email: string, password: string) {
  return await signInWithEmail(env, email, password);
}

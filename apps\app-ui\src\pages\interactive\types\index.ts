import type { Device, DeviceCommand, DeviceFunction } from '@/api/services/devices'

export type { Device, <PERSON>ceCommand, DeviceFunction }

// 指令类型定义
export interface Command {
  command: string
  time: string
}

// 剧本类型定义
export interface ScenePic {
  name: string
  pic: string
  type?: 'image' | 'video' // 媒体类型，默认为 image
}

export interface Intensity {
  [key: string]: number // 键是功能名称，值是强度
}

export interface Dialogue {
  role: string
  time: string
  dialogue: string
  pics?: ScenePic[]
  intensity: Intensity
}

export interface Stage {
  stage: number
  stageTitle: string
  pics?: ScenePic[]
  dialogues: Dialogue[]
}

export type Script = Stage[]

// 播放器状态
export enum PlayerState {
  IDLE = 'idle', // 初始状态
  LOADING = 'loading', // 加载资源
  PLAYING = 'playing', // 正常播放
  PAUSED = 'paused', // 暂停
  ERROR = 'error' // 错误状态
}

// 设备控制模式
export enum ControlMode {
  AUTO = 'auto', // 自动(根据剧本)
  MANUAL = 'manual' // 手动(用户控制)
}

// 上下文类型
export interface PlayerContextType {
  script: Script
  commands: Command[] // 添加指令列表
  device: Device | null
  currentTime: number
  duration: number
  currentStageIndex: number
  currentDialogues: Dialogue[]
  playerState: PlayerState
  controlMode: ControlMode
  manualIntensity: { [key: string]: number }
  play: () => void
  pause: () => void
  seek: (time: number) => void
  setStage: (index: number) => void
  setControlMode: (mode: ControlMode) => void
  setManualIntensity: (key: string, intensity: number) => void
  sendCommand: (key: string, intensity: number) => void
  updateDevice: (device: Device) => void
  updateScript: (script: Script) => void
}

// 工具函数类型
export type TimeToSeconds = (time: string) => number
export type SecondsToTime = (seconds: number) => string

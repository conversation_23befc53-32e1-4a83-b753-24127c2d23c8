import React from 'react'
import { <PERSON>er, <PERSON>er<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>er<PERSON><PERSON>, <PERSON>, But<PERSON> } from '@heroui/react'
import { Icon } from '@iconify/react'
import { secondsToTime } from '../utils/timeUtils'
import { useTranslation } from 'react-i18next'

interface StageSelectorProps {
  isOpen: boolean
  onClose: () => void
  stageTitles: string[]
  currentStageIndex: number
  onStageChange: (index: number) => void
  stageDurations?: number[] // 可选的阶段时长数组
}

/**
 * 阶段选择组件
 * 底部弹出的抽屉式阶段选择器
 */
export const StageSelector: React.FC<StageSelectorProps> = ({
  isOpen,
  onClose,
  stageTitles,
  currentStageIndex,
  onStageChange,
  stageDurations = []
}) => {
  const { t } = useTranslation('interactive')
  
  const handleStageSelect = (index: number) => {
    onStageChange(index)
    onClose()
  }

  return (
    <Drawer
      isOpen={isOpen}
      onClose={onClose}
      placement="bottom"
      size="lg"
      backdrop="blur"
      hideCloseButton={true}
      classNames={{
        base: 'max-h-[60vh]',
        backdrop: 'bg-black/50',
        wrapper: 'items-end'
      }}
    >
      <DrawerContent className="bg-[#1a1e2e] text-white rounded-t-2xl border-t border-gray-700">
        <DrawerHeader className="flex flex-col gap-1 bg-[#1a1e2e] text-white border-b border-gray-700 px-4 py-3">
          <div className="flex items-center justify-between w-full">
            <h2 className="text-lg font-semibold text-white">{t('stageSelector.title')}</h2>
            <Button
              isIconOnly
              variant="light"
              size="sm"
              onPress={onClose}
              className="text-gray-400 hover:text-white"
              aria-label={t('stageSelector.closeAriaLabel')}
            >
              <Icon icon="solar:close-circle-linear" width={24} />
            </Button>
          </div>
        </DrawerHeader>

        <DrawerBody className="bg-[#1a1e2e] text-white p-0 max-h-[50vh] overflow-y-auto">
          <div className="space-y-0">
            {stageTitles.map((title, index) => {
              const isActive = index === currentStageIndex
              const duration = stageDurations[index]

              return (
                <div
                  key={index}
                  className={`flex items-center p-4 cursor-pointer transition-colors ${
                    isActive
                      ? 'bg-[#ff2d97]/10 border-l-4 border-[#ff2d97]'
                      : 'hover:bg-[#33395c]/50'
                  }`}
                  onClick={() => handleStageSelect(index)}
                >
                  {/* 左侧：阶段编号 */}
                  <div className="flex-shrink-0 mr-4">
                    <div
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold ${
                        isActive ? 'bg-[#ff2d97] text-white' : 'bg-[#33395c] text-[#7c85b6]'
                      }`}
                    >
                      {index + 1}
                    </div>
                  </div>

                  {/* 中间：阶段信息 */}
                  <div className="flex-1 min-w-0">
                    <h3
                      className={`text-base font-medium mb-1 ${
                        isActive ? 'text-white' : 'text-[#e2e8f0]'
                      }`}
                    >
                      {title}
                    </h3>
                    {duration && (
                      <p className="text-sm text-[#7c85b6]">{t('stageSelector.duration', { duration: secondsToTime(duration) })}</p>
                    )}
                  </div>

                  {/* 右侧：时长标签 */}
                  <div className="flex-shrink-0 ml-4">
                    {duration && (
                      <Chip
                        size="sm"
                        variant="flat"
                        className={`${
                          isActive
                            ? 'bg-[#ff2d97]/20 text-[#ff2d97] border border-[#ff2d97]/30'
                            : 'bg-[#33395c] text-[#7c85b6]'
                        }`}
                      >
                        {secondsToTime(duration)}
                      </Chip>
                    )}
                    {isActive && !duration && (
                      <Icon icon="solar:play-circle-bold" width={20} className="text-[#ff2d97]" />
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </DrawerBody>
      </DrawerContent>
    </Drawer>
  )
}

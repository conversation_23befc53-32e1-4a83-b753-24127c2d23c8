// 文件上传相关消息
export const uploadMessages = {
  zh: {
    // 基础错误消息
    'upload.file_data_required': '文件数据不能为空',
    'upload.filename_required': '文件名不能为空',
    'upload.filename_too_long': '文件名过长',
    'upload.unsupported_file_type': '只支持 JPEG、PNG、WebP 格式的图片',
    'upload.invalid_upload_type': '上传类型必须是 avatar 或 character-image',
    'upload.wrong_upload_type_avatar': '上传类型错误，应该是 avatar',
    'upload.wrong_upload_type_character': '上传类型错误，应该是 character-image',
    'upload.unsupported_upload_type': '不支持的上传类型',
    'upload.r2_config_not_found': 'R2 存储配置未找到',
    'upload.upload_failed': '上传失败',
    'upload.avatar_upload_failed': '上传头像失败',
    'upload.character_image_upload_failed': '上传角色图片失败',
    'upload.file_upload_failed': '文件上传失败'
  },
  'zh-TW': {
    // 基礎錯誤訊息
    'upload.file_data_required': '檔案資料不能為空',
    'upload.filename_required': '檔案名不能為空',
    'upload.filename_too_long': '檔案名過長',
    'upload.unsupported_file_type': '只支援 JPEG、PNG、WebP 格式的圖片',
    'upload.invalid_upload_type': '上傳類型必須是 avatar 或 character-image',
    'upload.wrong_upload_type_avatar': '上傳類型錯誤，應該是 avatar',
    'upload.wrong_upload_type_character': '上傳類型錯誤，應該是 character-image',
    'upload.unsupported_upload_type': '不支援的上傳類型',
    'upload.r2_config_not_found': 'R2 儲存設定未找到',
    'upload.upload_failed': '上傳失敗',
    'upload.avatar_upload_failed': '上傳頭像失敗',
    'upload.character_image_upload_failed': '上傳角色圖片失敗',
    'upload.file_upload_failed': '檔案上傳失敗'
  },
  ja: {
    // 基本エラーメッセージ
    'upload.file_data_required': 'ファイルデータを入力してください',
    'upload.filename_required': 'ファイル名を入力してください',
    'upload.filename_too_long': 'ファイル名が長すぎます',
    'upload.unsupported_file_type': 'JPEG、PNG、WebP形式の画像のみサポートしています',
    'upload.invalid_upload_type': 'アップロードタイプはavatarまたはcharacter-imageである必要があります',
    'upload.wrong_upload_type_avatar': 'アップロードタイプが正しくありません。avatarである必要があります',
    'upload.wrong_upload_type_character': 'アップロードタイプが正しくありません。character-imageである必要があります',
    'upload.unsupported_upload_type': 'サポートされていないアップロードタイプです',
    'upload.r2_config_not_found': 'R2ストレージ設定が見つかりません',
    'upload.upload_failed': 'アップロードに失敗しました',
    'upload.avatar_upload_failed': 'アバターのアップロードに失敗しました',
    'upload.character_image_upload_failed': 'キャラクター画像のアップロードに失敗しました',
    'upload.file_upload_failed': 'ファイルのアップロードに失敗しました'
  },
  es: {
    // Mensajes de error básicos
    'upload.file_data_required': 'Los datos del archivo no pueden estar vacíos',
    'upload.filename_required': 'El nombre del archivo no puede estar vacío',
    'upload.filename_too_long': 'El nombre del archivo es demasiado largo',
    'upload.unsupported_file_type': 'Solo se admiten imágenes en formato JPEG, PNG, WebP',
    'upload.invalid_upload_type': 'El tipo de carga debe ser avatar o character-image',
    'upload.wrong_upload_type_avatar': 'Por favor, suba una imagen de avatar',
    'upload.wrong_upload_type_character': 'Por favor, suba una imagen de personaje',
    'upload.unsupported_upload_type': 'Tipo de carga no compatible',
    'upload.r2_config_not_found': 'No se encontró la configuración de almacenamiento R2',
    'upload.upload_failed': 'Error al subir',
    'upload.avatar_upload_failed': 'Error al subir el avatar',
    'upload.character_image_upload_failed': 'Error al subir la imagen del personaje',
    'upload.file_upload_failed': 'Error al subir el archivo'
  },
  en: {
    // Basic error messages
    'upload.file_data_required': 'File data cannot be empty',
    'upload.filename_required': 'File name cannot be empty',
    'upload.filename_too_long': 'File name is too long',
    'upload.unsupported_file_type': 'Only JPEG, PNG, WebP image formats are supported',
    'upload.invalid_upload_type': 'Upload type must be avatar or character-image',
    'upload.wrong_upload_type_avatar': 'Please upload an avatar image',
    'upload.wrong_upload_type_character': 'Please upload a character image',
    'upload.unsupported_upload_type': 'Unsupported upload type',
    'upload.r2_config_not_found': 'R2 storage configuration not found',
    'upload.upload_failed': 'Upload failed',
    'upload.avatar_upload_failed': 'Failed to upload avatar',
    'upload.character_image_upload_failed': 'Failed to upload character image',
    'upload.file_upload_failed': 'File upload failed'
  }
}

import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { AudioCategory, AudioEffect } from '../schema';
import type {
  AudioEffectQuery,
  CreateAudioEffectRequest,
  UpdateAudioEffectRequest,
  AudioCategoryQuery,
  CreateAudioCategoryRequest,
  UpdateAudioCategoryRequest,
  PaginatedResponse,
  AudioStats,
  AudioSearchResponse,
  RandomAudioResponse,
} from '@/types/audio';
import type { Env } from '@/types/env';

// ==================== 音频效果查询 ====================

/**
 * 获取音频效果列表（分页）
 */
export async function getAudioEffects(
  env: Env,
  query: AudioEffectQuery
): Promise<PaginatedResponse<AudioEffect>> {
  const supabase = getSupabase(env);

  const {
    page = 1,
    pageSize = 20,
    tags = [],
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    isActive = true,
    categoryId,
  } = query;

  const offset = (page - 1) * pageSize;

  // 构建查询
  let queryBuilder = supabase.from('AudioEffect').select('*', { count: 'exact' });

  // 构建条件
  if (isActive !== undefined) {
    queryBuilder = queryBuilder.eq('is_active', isActive);
  }

  if (categoryId) {
    queryBuilder = queryBuilder.eq('category_id', categoryId);
  }

  if (search) {
    queryBuilder = queryBuilder.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
  }

  if (tags.length > 0) {
    // Supabase 的 JSON 查询语法
    for (const tag of tags) {
      queryBuilder = queryBuilder.contains('tags', [tag]);
    }
  }

  // 排序
  const sortColumn =
    sortBy === 'createdAt'
      ? 'created_at'
      : sortBy === 'usageCount'
        ? 'usage_count'
        : sortBy === 'duration'
          ? 'duration'
          : sortBy === 'avgPitch'
            ? 'avg_pitch'
            : sortBy === 'avgLoudness'
              ? 'avg_loudness'
              : sortBy === 'id'
                ? 'id'
                : 'created_at';

  queryBuilder = queryBuilder.order(sortColumn, { ascending: sortOrder === 'asc' });

  // 分页
  queryBuilder = queryBuilder.range(offset, offset + pageSize - 1);

  const result = await queryBuilder;
  const { data, error, count } = result;
  if (error) throw error;

  const { data: convertedData } = handleSupabaseResult({ data, error: null });
  const total = count || 0;
  const totalPages = Math.ceil(total / pageSize);

  return {
    data: convertedData || [],
    pagination: {
      page,
      pageSize,
      total,
      totalPages,
    },
  };
}

/**
 * 根据ID获取音频效果
 */
export async function getAudioEffectById(env: Env, id: string): Promise<AudioEffect | null> {
  const supabase = getSupabase(env);

  const result = await supabase.from('AudioEffect').select('*').eq('id', id).single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data;
}

/**
 * 创建音频效果
 */
export async function createAudioEffect(
  env: Env,
  data: CreateAudioEffectRequest
): Promise<AudioEffect> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from('AudioEffect')
    .insert({
      name: data.name,
      description: data.description,
      category_id: data.categoryId,
      tags: data.tags,
      url: data.url,
      duration: data.duration.toString(),
      avg_pitch: data.avgPitch?.toString(),
      avg_loudness: data.avgLoudness?.toString(),
      energy_variation: data.energyVariation?.toString(),
      is_public: data.isPublic ?? true,
    })
    .select()
    .single();

  const { data: audioEffect, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return audioEffect;
}

/**
 * 更新音频效果
 */
export async function updateAudioEffect(
  env: Env,
  id: string,
  data: UpdateAudioEffectRequest
): Promise<AudioEffect> {
  const supabase = getSupabase(env);

  const updateData: any = {};
  if (data.name !== undefined) updateData.name = data.name;
  if (data.description !== undefined) updateData.description = data.description;
  if (data.categoryId !== undefined) updateData.category_id = data.categoryId;
  if (data.tags !== undefined) updateData.tags = data.tags;
  if (data.url !== undefined) updateData.url = data.url;
  if (data.duration !== undefined) updateData.duration = data.duration.toString();
  if (data.avgPitch !== undefined) updateData.avg_pitch = data.avgPitch?.toString();
  if (data.avgLoudness !== undefined) updateData.avg_loudness = data.avgLoudness?.toString();
  if (data.energyVariation !== undefined)
    updateData.energy_variation = data.energyVariation?.toString();
  if (data.isPublic !== undefined) updateData.is_public = data.isPublic;
  if (data.isActive !== undefined) updateData.is_active = data.isActive;

  updateData.updated_at = new Date().toISOString();

  const result = await supabase
    .from('AudioEffect')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  const { data: audioEffect, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return audioEffect;
}

/**
 * 删除音频效果
 */
export async function deleteAudioEffect(env: Env, id: string): Promise<boolean> {
  const supabase = getSupabase(env);

  const result = await supabase.from('AudioEffect').delete().eq('id', id);

  return !result.error;
}

/**
 * 增加音频效果使用次数
 */
export async function incrementAudioUsage(env: Env, id: string): Promise<void> {
  const supabase = getSupabase(env);

  // 由于 Supabase 没有原子递增，我们需要先查询再更新
  const currentResult = await supabase
    .from('AudioEffect')
    .select('usage_count')
    .eq('id', id)
    .single();

  const { data: current } = handleSupabaseSingleResult(currentResult);
  if (!current) return;

  await supabase
    .from('AudioEffect')
    .update({
      usage_count: (current.usageCount || 0) + 1,
      updated_at: new Date().toISOString(),
    })
    .eq('id', id);
}

// ==================== 音频分类查询 ====================

/**
 * 获取音频分类列表
 */
export async function getAudioCategories(
  env: Env,
  query: AudioCategoryQuery = {}
): Promise<AudioCategory[]> {
  const supabase = getSupabase(env);

  const { isActive = true, parentId } = query;

  let queryBuilder = supabase.from('AudioCategory').select('*').order('sort_order');

  if (isActive !== undefined) {
    queryBuilder = queryBuilder.eq('is_active', isActive);
  }

  if (parentId !== undefined) {
    if (parentId === null) {
      queryBuilder = queryBuilder.is('parent_id', null);
    } else {
      queryBuilder = queryBuilder.eq('parent_id', parentId);
    }
  }

  const result = await queryBuilder;
  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;
  return data || [];
}

/**
 * 根据ID获取音频分类
 */
export async function getAudioCategoryById(env: Env, id: string): Promise<AudioCategory | null> {
  const supabase = getSupabase(env);

  const result = await supabase.from('AudioCategory').select('*').eq('id', id).single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data;
}

/**
 * 创建音频分类
 */
export async function createAudioCategory(
  env: Env,
  data: CreateAudioCategoryRequest
): Promise<AudioCategory> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from('AudioCategory')
    .insert({
      name: data.name,
      display_name: data.displayName,
      description: data.description,
      parent_id: data.parentId,
      sort_order: data.sortOrder || 0,
    })
    .select()
    .single();

  const { data: category, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return category;
}

/**
 * 更新音频分类
 */
export async function updateAudioCategory(
  env: Env,
  id: string,
  data: UpdateAudioCategoryRequest
): Promise<AudioCategory> {
  const supabase = getSupabase(env);

  const updateData: any = {};
  if (data.name !== undefined) updateData.name = data.name;
  if (data.displayName !== undefined) updateData.display_name = data.displayName;
  if (data.description !== undefined) updateData.description = data.description;
  if (data.parentId !== undefined) updateData.parent_id = data.parentId;
  if (data.sortOrder !== undefined) updateData.sort_order = data.sortOrder;
  if (data.isActive !== undefined) updateData.is_active = data.isActive;

  updateData.updated_at = new Date().toISOString();

  const result = await supabase
    .from('AudioCategory')
    .update(updateData)
    .eq('id', id)
    .select()
    .single();

  const { data: category, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return category;
}

/**
 * 删除音频分类
 */
export async function deleteAudioCategory(env: Env, id: string): Promise<boolean> {
  const supabase = getSupabase(env);

  const result = await supabase.from('AudioCategory').delete().eq('id', id);

  return !result.error;
}

// ==================== 音频搜索与统计 ====================

/**
 * 搜索音频效果
 */
export async function searchAudioEffects(
  env: Env,
  searchTerm: string,
  options: {
    limit?: number;
    categoryId?: string;
    tags?: string[];
  } = {}
): Promise<AudioSearchResponse> {
  const supabase = getSupabase(env);
  const { limit = 50, categoryId, tags = [] } = options;

  let queryBuilder = supabase
    .from('AudioEffect')
    .select('*')
    .eq('is_active', true)
    .eq('is_public', true);

  // 搜索条件
  if (searchTerm) {
    queryBuilder = queryBuilder.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
  }

  if (categoryId) {
    queryBuilder = queryBuilder.eq('category_id', categoryId);
  }

  if (tags.length > 0) {
    for (const tag of tags) {
      queryBuilder = queryBuilder.contains('tags', [tag]);
    }
  }

  queryBuilder = queryBuilder.order('usage_count', { ascending: false }).limit(limit);

  const result = await queryBuilder;
  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;

  return {
    data: data || [],
    total: (data || []).length,
  };
}

/**
 * 获取音频统计信息
 */
export async function getAudioStats(env: Env): Promise<AudioStats> {
  const supabase = getSupabase(env);

  // 总音频数
  const { count: totalAudios } = await supabase
    .from('AudioEffect')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true);

  // 总分类数
  const { count: totalCategories } = await supabase
    .from('AudioCategory')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true);

  // 公开音频数
  const { count: publicAudios } = await supabase
    .from('AudioEffect')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true)
    .eq('is_public', true);

  // 今日新增（需要根据 created_at 过滤）
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const { count: todayAdded } = await supabase
    .from('AudioEffect')
    .select('*', { count: 'exact', head: true })
    .gte('created_at', today.toISOString());

  // 计算平均时长和总使用量（需要额外查询）
  const audioDetailsResult = await supabase
    .from('AudioEffect')
    .select('duration, usage_count')
    .eq('is_active', true);

  const { data: audioDetails } = handleSupabaseResult(audioDetailsResult);

  const avgDuration =
    audioDetails && audioDetails.length > 0
      ? audioDetails.reduce((sum: number, item: any) => sum + (Number(item.duration) || 0), 0) /
        audioDetails.length
      : 0;

  const totalUsage = audioDetails
    ? audioDetails.reduce((sum: number, item: any) => sum + (item.usageCount || 0), 0)
    : 0;

  return {
    totalAudios: totalAudios || 0,
    avgDuration,
    topTags: [], // 可以根据需要实现标签统计
    categoriesCount: totalCategories || 0,
    totalUsage,
  };
}

/**
 * 获取随机音频效果
 */
export async function getRandomAudioEffects(
  env: Env,
  options: {
    limit?: number;
    categoryId?: string;
    tags?: string[];
  } = {}
): Promise<RandomAudioResponse> {
  const supabase = getSupabase(env);
  const { limit = 10, categoryId, tags = [] } = options;

  let queryBuilder = supabase
    .from('AudioEffect')
    .select('*')
    .eq('is_active', true)
    .eq('is_public', true);

  if (categoryId) {
    queryBuilder = queryBuilder.eq('category_id', categoryId);
  }

  if (tags.length > 0) {
    for (const tag of tags) {
      queryBuilder = queryBuilder.contains('tags', [tag]);
    }
  }

  // Supabase 的随机排序（使用 random()）
  // 注意：这在大数据集上可能性能不佳
  const result = await queryBuilder.limit(limit * 3); // 获取更多然后随机选择

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;

  // 客户端随机排序并限制数量
  const shuffled = (data || []).sort(() => Math.random() - 0.5).slice(0, limit);

  return {
    data: shuffled,
    count: shuffled.length,
  };
}

// ==================== 辅助函数 ====================

/**
 * 验证音频效果数据
 */
export function validateAudioEffectData(
  data: CreateAudioEffectRequest | UpdateAudioEffectRequest
): string[] {
  const errors: string[] = [];

  if ('name' in data && data.name && data.name.length > 100) {
    errors.push('音效名称不能超过100个字符');
  }

  if ('duration' in data && data.duration && data.duration <= 0) {
    errors.push('音效时长必须大于0');
  }

  if ('url' in data && data.url && !isValidUrl(data.url)) {
    errors.push('音效URL格式不正确');
  }

  return errors;
}

/**
 * 验证URL格式
 */
function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

// ==================== 兼容性函数 ====================

/**
 * 根据标签搜索音频（兼容性函数）
 */
export async function searchAudioByTags(
  env: Env,
  tags: string[],
  options?: {
    limit?: number;
    categoryId?: string;
  }
): Promise<any> {
  return searchAudioEffects(env, '', {
    limit: options?.limit,
    categoryId: options?.categoryId,
    tags,
  });
}

/**
 * 获取所有音频标签（兼容性函数）
 */
export async function getAllAudioTags(env: Env): Promise<string[]> {
  const supabase = getSupabase(env);
  const result = await supabase
    .from(TABLE_NAMES.audioEffect)
    .select('tags')
    .eq('is_active', true)
    .eq('is_public', true);

  const { data, error } = handleSupabaseResult(result);
  if (error) throw error;

  const allTags = new Set<string>();
  (data || []).forEach((item: any) => {
    if (item.tags && Array.isArray(item.tags)) {
      item.tags.forEach((tag: string) => allTags.add(tag));
    }
  });

  return Array.from(allTags);
}

/**
 * 获取音频统计信息（兼容性函数）
 */
export const getAudioStatistics = getAudioStats;

/**
 * 批量创建音频效果（兼容性函数）
 */
export async function batchCreateAudioEffects(
  env: Env,
  effects: Array<CreateAudioEffectRequest>
): Promise<any[]> {
  const results = [];
  for (const effect of effects) {
    try {
      const result = await createAudioEffect(env, effect);
      results.push(Array.isArray(result) ? result[0] : result);
    } catch (error) {
      console.error('批量创建音频效果失败:', error);
      results.push(null);
    }
  }
  return results;
}

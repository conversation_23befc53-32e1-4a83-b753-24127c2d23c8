# 邀请码营销系统实现文档

## 概述

已成功实现完整的邀请码营销系统，包括邀请码管理、推荐关系建立、佣金计算结算、提现申请等功能。

## 功能特性

### 1. 邀请码管理
- ✅ 用户可生成专属6位邀请码（字母数字组合）
- ✅ 邀请码永久有效，无使用次数限制
- ✅ 邀请码唯一性验证
- ✅ 邀请码使用统计

### 2. 推荐关系建立
- ✅ 用户注册时支持邀请码输入（可选）
- ✅ 自动建立推荐关系
- ✅ 防止自己邀请自己
- ✅ 防止重复邀请
- ✅ 支持验证码注册和直接注册两种方式

### 3. 佣金计算和结算
- ✅ 会员套餐购买：5% 佣金
- ✅ 积分包购买：10% 佣金
- ✅ 支付成功后立即结算佣金
- ✅ 自动更新邀请人佣金账户余额

### 4. 佣金账户管理
- ✅ 佣金余额查询
- ✅ 佣金记录查询（分页）
- ✅ 邀请统计数据
- ✅ 累计收入、可提现余额、已提现金额等统计

### 5. 提现申请功能
- ✅ 最低提现金额：100元
- ✅ 提现手续费：3%
- ✅ 银行卡信息填写
- ✅ 提现申请提交
- ✅ 提现记录查询

### 6. 管理员审核功能
- ✅ 待审核提现申请列表
- ✅ 提现申请审核（批准/拒绝）
- ✅ 提现完成标记
- ✅ 自动处理佣金账户余额

## 数据库设计

### 新增表结构

1. **InviteCode** - 邀请码表
   - 存储用户生成的邀请码
   - 支持使用次数统计和过期时间

2. **ReferralRelation** - 推荐关系表
   - 记录邀请人和被邀请人的关系
   - 确保一个用户只能被邀请一次

3. **CommissionAccount** - 佣金账户表
   - 管理用户的佣金余额
   - 包含总收入、可提现余额、冻结余额、已提现金额

4. **CommissionRecord** - 佣金记录表
   - 记录每笔佣金的详细信息
   - 包含来源订单、佣金金额、比例等

5. **WithdrawRequest** - 提现申请表
   - 存储用户的提现申请
   - 包含银行卡信息、审核状态等

## API 接口

### 邀请码管理
- `POST /api/referral/invite-code/generate` - 生成邀请码
- `GET /api/referral/invite-code/my` - 获取我的邀请码信息
- `POST /api/referral/invite-code/validate` - 验证邀请码
- `GET /api/referral/invites` - 获取我邀请的用户列表

### 佣金账户管理
- `GET /api/referral/commission/account` - 获取佣金账户信息
- `GET /api/referral/commission/records` - 获取佣金记录列表
- `GET /api/referral/statistics` - 获取邀请统计数据

### 提现申请
- `GET /api/referral/withdraw/config` - 获取提现配置
- `POST /api/referral/withdraw/apply` - 提交提现申请
- `GET /api/referral/withdraw/records` - 获取提现记录

### 管理员功能
- `GET /api/admin/referral/withdraw/pending` - 获取待审核提现申请
- `GET /api/admin/referral/withdraw/all` - 获取所有提现申请
- `POST /api/admin/referral/withdraw/review` - 审核提现申请
- `POST /api/admin/referral/withdraw/complete` - 标记提现完成

## 系统配置

在 `SystemConfig` 表中添加了以下配置项：

```sql
-- 佣金比例配置
COMMISSION_RATE_MEMBERSHIP = '0.05'  -- 会员套餐佣金比例（5%）
COMMISSION_RATE_POINTS = '0.10'      -- 积分包佣金比例（10%）

-- 提现配置
MIN_WITHDRAW_AMOUNT = '100'          -- 最低提现金额（元）
WITHDRAW_FEE_RATE = '0.03'           -- 提现手续费比例（3%）

-- 其他配置
COMMISSION_FREEZE_DAYS = '7'         -- 佣金冻结天数（预留）
INVITE_CODE_LENGTH = '6'             -- 邀请码长度
INVITE_CODE_ENABLED = 'true'         -- 邀请码功能是否启用
```

## 业务流程

### 邀请注册流程
1. 用户A生成邀请码
2. 用户A分享邀请码给用户B
3. 用户B使用邀请码注册
4. 系统验证邀请码有效性
5. 建立A和B的推荐关系
6. 为A创建佣金账户（如果不存在）

### 佣金结算流程
1. 用户B购买会员套餐或积分包
2. 支付成功后触发佣金计算
3. 根据订单类型获取佣金比例
4. 计算佣金金额
5. 创建佣金记录
6. 更新邀请人A的佣金账户余额

### 提现申请流程
1. 用户A申请提现
2. 验证最低提现金额和余额
3. 计算手续费和实际到账金额
4. 冻结相应金额
5. 创建提现申请记录
6. 管理员审核
7. 审核通过后标记完成
8. 更新佣金账户状态

## 部署说明

1. **数据库迁移**
   ```bash
   cd apps/backend-service
   pnpm db:migrate
   ```

2. **初始化系统配置**
   在 Supabase Dashboard 中执行：
   ```sql
   -- 执行 apps/backend-service/database/init-referral-system.sql
   ```

3. **验证功能**
   - 测试邀请码生成
   - 测试注册流程
   - 测试支付回调
   - 测试提现申请

## 注意事项

1. **安全性**
   - 邀请码生成使用随机算法，避免冲突
   - 防止循环邀请和重复邀请
   - 提现申请需要管理员审核

2. **性能优化**
   - 数据库索引已优化
   - 分页查询支持
   - 事务处理确保数据一致性

3. **扩展性**
   - 系统配置可动态调整
   - 支持多级推荐（预留）
   - 支持不同佣金比例

## 测试建议

1. **功能测试**
   - 邀请码生成和验证
   - 注册流程集成
   - 支付成功佣金计算
   - 提现申请和审核

2. **边界测试**
   - 最低提现金额验证
   - 余额不足处理
   - 重复邀请防护
   - 无效邀请码处理

3. **性能测试**
   - 大量邀请码生成
   - 并发支付处理
   - 批量数据查询

## 后续优化

1. **功能增强**
   - 邀请码分享页面
   - 佣金提醒通知
   - 数据统计图表
   - 多级推荐支持

2. **运营工具**
   - 邀请活动管理
   - 佣金比例调整
   - 用户行为分析
   - 反作弊机制

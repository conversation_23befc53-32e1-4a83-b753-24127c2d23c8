// LangChain 对话系统主入口

// 已完成的模块
export { PromptManager } from './prompts/prompt-manager'
export { StructuredOutputParser } from './parsers/output-parser'
export { TagParser } from './parsers/tag-parser'
export { LangChainChatEngine } from './engine/chat-engine'
export { MultiModalCoordinator } from './multimodal/coordinator'
export { ConfigManager, defaultConfigManager } from './config'

// 提供商相关
export { createXaiProviderConfig } from './engine/providers'

// 模型配置相关
export { getDefaultModel } from './config'

// 类型定义
export type * from './types'

// 版本信息
export const LANGCHAIN_VERSION = '1.0.0'

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// 使用 Supabase 内置环境变量（自动提供）
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!

// CORS 头配置
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
}

// 图片生成服务
class ImageGenerationService {
  private supabase: any

  constructor(private env: any, supabase: any) {
    this.supabase = supabase
  }

  // 更新生成状态到 attachments 字段
  async updateGenerationStatus(
    messageId: string,
    status: string,
    progress: number,
    message: string
  ) {
    console.log('📊 [Progress] 更新生成状态:', { messageId, status, progress, message })

    // 先查询现有的附件
    const { data: currentMessage, error: queryError } = await this.supabase
      .from('Message')
      .select('attachments')
      .eq('id', messageId)
      .single()

    if (queryError) {
      console.warn('⚠️ [Progress] 查询现有附件失败:', queryError)
      return
    }

    // 解析现有附件
    let existingAttachments = []
    if (currentMessage.attachments) {
      existingAttachments = Array.isArray(currentMessage.attachments)
        ? currentMessage.attachments
        : JSON.parse(currentMessage.attachments as string)
    }

    // 移除之前的生成状态附件（如果有）
    const filteredAttachments = existingAttachments.filter(
      (att: any) => !att.contentType?.startsWith('image/generating')
    )

    // 添加新的状态附件
    const statusAttachment = {
      url: `generating://${status}`,
      name: message,
      contentType: `image/generating`,
      metadata: {
        status,
        progress,
        timestamp: new Date().toISOString()
      }
    }

    const updatedAttachments = [...filteredAttachments, statusAttachment]

    const { error } = await this.supabase
      .from('Message')
      .update({
        attachments: updatedAttachments
      })
      .eq('id', messageId)

    if (error) {
      console.warn('⚠️ [Progress] 更新状态失败:', error)
    }
  }

  // 更新失败状态
  async updateFailureStatus(messageId: string, error: string) {
    console.log('❌ [Failure] 更新失败状态:', { messageId, error })

    // 先查询现有的附件
    const { data: currentMessage, error: queryError } = await this.supabase
      .from('Message')
      .select('attachments')
      .eq('id', messageId)
      .single()

    if (queryError) {
      console.warn('⚠️ [Failure] 查询现有附件失败:', queryError)
      return
    }

    // 解析现有附件
    let existingAttachments = []
    if (currentMessage.attachments) {
      existingAttachments = Array.isArray(currentMessage.attachments)
        ? currentMessage.attachments
        : JSON.parse(currentMessage.attachments as string)
    }

    // 移除生成状态附件
    const filteredAttachments = existingAttachments.filter(
      (att: any) => !att.contentType?.startsWith('image/generating')
    )

    const { error: updateError } = await this.supabase
      .from('Message')
      .update({
        attachments: filteredAttachments
      })
      .eq('id', messageId)

    if (updateError) {
      console.warn('⚠️ [Failure] 更新失败状态失败:', updateError)
    }
  }

  async generateImageAsync(request: any, type: string, messageId: string): Promise<string> {
    console.log('🚀 [Edge Function] 调用 Insa3D API:', { prompt: request.prompt.substring(0, 100) })

    // 发送开始状态
    await this.updateGenerationStatus(messageId, 'starting', 10, '正在连接图片生成服务...')

    // 调用 Insa3D v2 API
    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${this.env.INSA3D_API_ENDPOINT}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.env.INSA3D_API_TOKEN}`
        },
        body: JSON.stringify({
          inputs: {
            '96b9fc105f305520': {
              title: 'width',
              value: 720
            },
            '577750b1e716b191': {
              title: 'height',
              value: 1280
            },
            '6307d2aa9851566f': {
              title: 'prompt',
              value: request.prompt
            },
            ...(request.imageUrl && {
              '4bee95674812740b': {
                title: 'Load Image',
                value: request.imageUrl
              }
            })
          }
        })
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      await this.updateGenerationStatus(messageId, 'failed', 0, `API请求失败: ${response.status}`)
      throw new Error(`Insa3D API 请求失败: ${response.status} ${errorText}`)
    }

    const result = await response.json()
    console.log('✅ [Edge Function] Insa3D 任务已启动:', result.task_id)

    // 发送处理中状态
    await this.updateGenerationStatus(
      messageId,
      'processing',
      30,
      '图片生成任务已启动，正在处理中...'
    )

    return result.task_id
  }

  async waitForTaskCompletion(taskId: string, messageId: string): Promise<any> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ [Edge Function] 开始轮询任务状态:', taskId)

    let progressStep = 40 // 从40%开始
    let consecutiveErrors = 0 // 连续错误计数
    const maxConsecutiveErrors = 5 // 最大连续错误次数

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(
          `https://api.instasd.com/api_endpoints/${this.env.INSA3D_API_ENDPOINT}/task_status/${taskId}`,
          {
            headers: {
              Authorization: `Bearer ${this.env.INSA3D_API_TOKEN}`
            }
          }
        )

        if (!response.ok) {
          consecutiveErrors++
          console.warn(
            `⚠️ [Edge Function] 查询任务状态失败 (${consecutiveErrors}/${maxConsecutiveErrors}):`,
            response.status
          )

          // 如果连续错误次数过多，停止轮询
          if (consecutiveErrors >= maxConsecutiveErrors) {
            await this.updateGenerationStatus(messageId, 'failed', 0, '查询任务状态失败次数过多')
            throw new Error('查询任务状态失败次数过多，停止轮询')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        // 重置错误计数
        consecutiveErrors = 0

        const task = await response.json()
        console.log('📊 [Edge Function] 任务状态:', {
          taskId,
          status: task.status,
          hasImageUrls: !!task.image_urls,
          elapsedMs: Date.now() - startTime
        })

        // 更新进度
        if (task.status === 'IN_PROGRESS' || task.status === 'EXECUTING') {
          progressStep = Math.min(progressStep + 5, 90) // 逐步增加到90%
          await this.updateGenerationStatus(
            messageId,
            'processing',
            progressStep,
            '正在生成图片，请稍候...'
          )
        }

        // 检查任务完成状态
        if (task.status === 'COMPLETED') {
          return {
            taskId,
            status: 'completed',
            imageUrl: task.image_urls?.[0], // 使用 image_urls 数组的第一个
            errorMessage: null
          }
        }

        if (task.status === 'FAILED') {
          return {
            taskId,
            status: 'failed',
            imageUrl: null,
            errorMessage: task.error_message || '图片生成失败'
          }
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(
          `❌ [Edge Function] 轮询异常 (${consecutiveErrors}/${maxConsecutiveErrors}):`,
          error
        )

        // 如果连续错误次数过多，停止轮询
        if (consecutiveErrors >= maxConsecutiveErrors) {
          await this.updateGenerationStatus(messageId, 'failed', 0, '轮询过程中发生异常')
          throw new Error('轮询过程中发生异常，停止轮询')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    await this.updateGenerationStatus(messageId, 'failed', 0, '生成超时，请重试')
    throw new Error('任务等待超时')
  }
}

serve(async req => {
  // 处理 OPTIONS 预检请求
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  const startTime = Date.now()

  try {
    const { messageId, chatId, prompt, characterAvatar } = await req.json()

    console.log('🎨 [Edge Function] 开始生成图片:', {
      messageId,
      chatId,
      promptLength: prompt.length,
      hasCharacterAvatar: !!characterAvatar,
      timestamp: new Date().toISOString()
    })

    // 获取环境变量
    const env = {
      INSA3D_API_ENDPOINT: Deno.env.get('INSA3D_API_ENDPOINT'),
      INSA3D_API_TOKEN: Deno.env.get('INSA3D_API_TOKEN')
    }

    // 验证环境变量
    if (!env.INSA3D_API_ENDPOINT || !env.INSA3D_API_TOKEN) {
      throw new Error('缺少 Insa3D API 配置')
    }

    // 创建 Supabase 客户端（使用内置环境变量）
    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    // 生成图片
    const imageService = new ImageGenerationService(env, supabase)
    const taskId = await imageService.generateImageAsync(
      {
        prompt,
        imageUrl: characterAvatar
      },
      'insa3d-v2',
      messageId
    )

    console.log('🎨 [Edge Function] 任务已启动:', { taskId, elapsedMs: Date.now() - startTime })

    // 等待完成
    const task = await imageService.waitForTaskCompletion(taskId, messageId)

    if (task.status === 'failed') {
      await imageService.updateFailureStatus(messageId, task.errorMessage || '未知错误')
      throw new Error(`图片生成失败: ${task.errorMessage || '未知错误'}`)
    }

    if (task.imageUrl) {
      console.log('🎉 [Success] 图片生成成功，更新消息附件')

      // 先查询现有的附件
      const { data: currentMessage, error: queryError } = await supabase
        .from('Message')
        .select('attachments')
        .eq('id', messageId)
        .single()

      if (queryError) {
        console.error('❌ [Error] 查询现有附件失败:', queryError)
        return new Response(
          JSON.stringify({
            success: false,
            error: '查询现有附件失败'
          }),
          {
            status: 500,
            headers: {
              'Content-Type': 'application/json',
              ...corsHeaders
            }
          }
        )
      }

      // 解析现有附件
      let existingAttachments = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? currentMessage.attachments
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件，添加真实图片附件
      const filteredAttachments = existingAttachments.filter(
        (att: any) => !att.contentType?.startsWith('image/generating')
      )

      const imageAttachment = {
        url: task.imageUrl,
        name: `generated-image-${Date.now()}.png`,
        contentType: 'image/png'
      }

      const updatedAttachments = [...filteredAttachments, imageAttachment]

      // 先获取原始消息的创建时间，确保排序不受影响
      const { data: originalMessage } = await supabase
        .from('Message')
        .select('created_at')
        .eq('id', messageId)
        .single()

      const { error: updateError } = await supabase
        .from('Message')
        .update({
          attachments: updatedAttachments,
          // 明确保持原始创建时间，防止任何触发器修改
          created_at: originalMessage?.created_at || new Date().toISOString(),
          // 明确不更新 updated_at，避免影响排序
        })
        .eq('id', messageId)

      if (updateError) {
        await imageService.updateFailureStatus(messageId, '保存图片失败')
        throw new Error(`数据库更新失败: ${updateError.message}`)
      }

      console.log('🎉 [Edge Function] 图片生成完成:', {
        messageId,
        imageUrl: task.imageUrl,
        totalElapsedMs: Date.now() - startTime
      })

      return new Response(
        JSON.stringify({
          success: true,
          messageId,
          imageUrl: task.imageUrl,
          elapsedMs: Date.now() - startTime
        }),
        {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders
          }
        }
      )
    } else {
      throw new Error('图片生成返回空结果')
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误'
    console.error('❌ [Edge Function] 错误:', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      elapsedMs: Date.now() - startTime
    })

    // 尝试清理失败状态（如果有messageId的话）
    try {
      const { messageId } = await req.json()
      if (messageId) {
        const supabase = createClient(supabaseUrl, supabaseServiceKey)
        const imageService = new ImageGenerationService({}, supabase)
        await imageService.updateFailureStatus(messageId, errorMessage)
      }
    } catch (cleanupError) {
      console.warn('⚠️ [Edge Function] 清理失败状态时出错:', cleanupError)
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: errorMessage,
        elapsedMs: Date.now() - startTime
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders
        }
      }
    )
  }
})

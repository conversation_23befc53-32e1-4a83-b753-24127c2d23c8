import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, ModalHeader, ModalBody, Button } from '@heroui/react'
import { motion, AnimatePresence } from 'framer-motion'
import { MediaDownloader } from '@/utils/media-download'
import { useTranslation } from 'react-i18next'

interface ImageDrawerProps {
  isOpen: boolean
  onClose: () => void
  imageUrl: string
  title?: string
}

export const ImageDrawer = ({ isOpen, onClose, imageUrl, title }: ImageDrawerProps) => {
  const { t } = useTranslation('chat-v2')
  const [imageLoaded, setImageLoaded] = useState(false)
  const [imageError, setImageError] = useState(false)

  useEffect(() => {
    if (imageUrl) {
      setImageLoaded(false)
      setImageError(false)
    }
  }, [imageUrl])

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      size="5xl"
      classNames={{
        base: 'bg-black/95',
        body: 'p-0',
        header: 'border-b border-gray-700',
        wrapper: 'z-100'
      }}
      hideCloseButton={true}
      isDismissable={true}
      isKeyboardDismissDisabled={false}
    >
      <ModalContent>
        <ModalHeader className="flex justify-between items-center text-white">
          <h3 className="text-lg font-medium">{title || t('image.preview')}</h3>
          <Button
            isIconOnly
            variant="light"
            onPress={onClose}
            className="text-white hover:bg-white/10"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </Button>
        </ModalHeader>
        <ModalBody>
          <div className="w-full h-[80vh] flex items-center justify-center bg-black">
            <div className="w-full h-full flex items-center justify-center relative">
              <AnimatePresence mode="wait">
                {!imageLoaded && !imageError && (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <div className="flex flex-col items-center gap-2">
                      <svg
                        className="w-8 h-8 text-white animate-spin"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                        />
                      </svg>
                      <p className="text-sm text-white">{t('image.loading')}</p>
                    </div>
                  </motion.div>
                )}

                {imageError && (
                  <motion.div
                    key="error"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="absolute inset-0 flex items-center justify-center"
                  >
                    <div className="flex flex-col items-center gap-2">
                      <svg
                        className="w-8 h-8 text-red-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                        />
                      </svg>
                      <p className="text-sm text-red-500">{t('image.load_failed')}</p>
                      <Button
                        size="sm"
                        variant="light"
                        onPress={() => {
                          setImageError(false)
                          setImageLoaded(false)
                        }}
                        className="text-white hover:bg-white/10"
                      >
                        {t('image.retry')}
                      </Button>
                    </div>
                  </motion.div>
                )}

                <motion.img
                  key={imageUrl}
                  src={imageUrl}
                  alt={title || t('image.generated_image')}
                  className={`max-w-full max-h-full object-contain transition-opacity duration-300 ${
                    imageLoaded ? 'opacity-100' : 'opacity-0'
                  }`}
                  onLoad={() => setImageLoaded(true)}
                  onError={() => setImageError(true)}
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{
                    scale: imageLoaded ? 1 : 0.9,
                    opacity: imageLoaded ? 1 : 0
                  }}
                  transition={{ duration: 0.3 }}
                />
              </AnimatePresence>

              {imageLoaded && (
                <div className="absolute bottom-4 right-4">
                  <Button
                    variant="solid"
                    color="primary"
                    onPress={async () => {
                      try {
                        const result = await MediaDownloader.downloadImage(imageUrl)
                        if (!result.success) {
                          console.error(`${t('image.download_failed')}:`, result.message)
                        }
                      } catch (error) {
                        console.error(`${t('image.download_error')}:`, error)
                      }
                    }}
                    className="bg-white/20 backdrop-blur-sm hover:bg-white/30"
                  >
                    <svg
                      className="w-4 h-4 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    {t('image.download')}
                  </Button>
                </div>
              )}
            </div>
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

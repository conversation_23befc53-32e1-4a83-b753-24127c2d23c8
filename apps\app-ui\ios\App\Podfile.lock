PODS:
  - Alamofire (5.10.2)
  - Capacitor (7.2.0):
    - CapacitorCordova
  - CapacitorApp (7.0.1):
    - Capacitor
  - CapacitorBarcodeScanner (2.0.3):
    - Capacitor
    - OSBarcodeLib (~> 1.1.3)
  - CapacitorBleAdvertiser (0.1.0):
    - Capacitor
  - CapacitorCommunityBluetoothLe (7.0.0):
    - Capacitor
  - CapacitorCommunityMedia (8.0.1):
    - Capacitor
    - SDWebImage
  - CapacitorCommunitySqlite (7.0.1):
    - Capacitor
    - SQLCipher
    - ZIPFoundation
  - CapacitorCordova (7.2.0)
  - CapacitorDevice (7.0.1):
    - Capacitor
  - CapacitorFilesystem (7.1.2):
    - Capacitor
    - IONFilesystemLib (~> 1.0)
  - CapacitorKeyboard (7.0.1):
    - Capacitor
  - CapacitorNetwork (7.0.1):
    - Capacitor
  - CapacitorPreferences (7.0.1):
    - Capacitor
  - CapacitorPrivacyScreen (1.1.0):
    - Capacitor
  - CapacitorStatusBar (7.0.1):
    - Capacitor
  - CapawesomeCapacitorLiveUpdate (7.2.0):
    - Alamofire (~> 5.9)
    - Capacitor
    - SSZipArchive (~> 2.2.3)
  - IONFilesystemLib (1.0.0)
  - OSBarcodeLib (1.1.3)
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - SQLCipher (4.9.0):
    - SQLCipher/standard (= 4.9.0)
  - SQLCipher/common (4.9.0)
  - SQLCipher/standard (4.9.0):
    - SQLCipher/common
  - SSZipArchive (2.2.3)
  - ZIPFoundation (0.9.19)

DEPENDENCIES:
  - "Capacitor (from `../../../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/app`)"
  - "CapacitorBarcodeScanner (from `../../../../node_modules/.pnpm/@capacitor+barcode-scanner@2.0.3_@capacitor+core@7.2.0/node_modules/@capacitor/barcode-scanner`)"
  - CapacitorBleAdvertiser (from `../../../../packages/capacitor-ble-advertiser`)
  - "CapacitorCommunityBluetoothLe (from `../../../../node_modules/.pnpm/@capacitor-community+bluetooth-le@7.0.0_@capacitor+core@7.2.0/node_modules/@capacitor-community/bluetooth-le`)"
  - "CapacitorCommunityMedia (from `../../../../node_modules/.pnpm/@capacitor-community+media@8.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/media`)"
  - "CapacitorCommunitySqlite (from `../../../../node_modules/.pnpm/@capacitor-community+sqlite@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/sqlite`)"
  - "CapacitorCordova (from `../../../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../../../node_modules/.pnpm/@capacitor+device@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../../../node_modules/.pnpm/@capacitor+filesystem@7.1.2_@capacitor+core@7.2.0/node_modules/@capacitor/filesystem`)"
  - "CapacitorKeyboard (from `../../../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/keyboard`)"
  - "CapacitorNetwork (from `../../../../node_modules/.pnpm/@capacitor+network@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/network`)"
  - "CapacitorPreferences (from `../../../../node_modules/.pnpm/@capacitor+preferences@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/preferences`)"
  - "CapacitorPrivacyScreen (from `../../../../node_modules/.pnpm/@capacitor+privacy-screen@1.1.0_@capacitor+core@7.2.0/node_modules/@capacitor/privacy-screen`)"
  - "CapacitorStatusBar (from `../../../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/status-bar`)"
  - "CapawesomeCapacitorLiveUpdate (from `../../../../node_modules/.pnpm/@capawesome+capacitor-live-update@7.2.0_@capacitor+core@7.2.0/node_modules/@capawesome/capacitor-live-update`)"

SPEC REPOS:
  trunk:
    - Alamofire
    - IONFilesystemLib
    - OSBarcodeLib
    - SDWebImage
    - SQLCipher
    - SSZipArchive
    - ZIPFoundation

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/app"
  CapacitorBarcodeScanner:
    :path: "../../../../node_modules/.pnpm/@capacitor+barcode-scanner@2.0.3_@capacitor+core@7.2.0/node_modules/@capacitor/barcode-scanner"
  CapacitorBleAdvertiser:
    :path: "../../../../packages/capacitor-ble-advertiser"
  CapacitorCommunityBluetoothLe:
    :path: "../../../../node_modules/.pnpm/@capacitor-community+bluetooth-le@7.0.0_@capacitor+core@7.2.0/node_modules/@capacitor-community/bluetooth-le"
  CapacitorCommunityMedia:
    :path: "../../../../node_modules/.pnpm/@capacitor-community+media@8.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/media"
  CapacitorCommunitySqlite:
    :path: "../../../../node_modules/.pnpm/@capacitor-community+sqlite@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/sqlite"
  CapacitorCordova:
    :path: "../../../../node_modules/.pnpm/@capacitor+ios@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../../../node_modules/.pnpm/@capacitor+device@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../../../node_modules/.pnpm/@capacitor+filesystem@7.1.2_@capacitor+core@7.2.0/node_modules/@capacitor/filesystem"
  CapacitorKeyboard:
    :path: "../../../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/keyboard"
  CapacitorNetwork:
    :path: "../../../../node_modules/.pnpm/@capacitor+network@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/network"
  CapacitorPreferences:
    :path: "../../../../node_modules/.pnpm/@capacitor+preferences@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/preferences"
  CapacitorPrivacyScreen:
    :path: "../../../../node_modules/.pnpm/@capacitor+privacy-screen@1.1.0_@capacitor+core@7.2.0/node_modules/@capacitor/privacy-screen"
  CapacitorStatusBar:
    :path: "../../../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/status-bar"
  CapawesomeCapacitorLiveUpdate:
    :path: "../../../../node_modules/.pnpm/@capawesome+capacitor-live-update@7.2.0_@capacitor+core@7.2.0/node_modules/@capawesome/capacitor-live-update"

SPEC CHECKSUMS:
  Alamofire: 7193b3b92c74a07f85569e1a6c4f4237291e7496
  Capacitor: 03bc7cbdde6a629a8b910a9d7d78c3cc7ed09ea7
  CapacitorApp: febecbb9582cb353aed037e18ec765141f880fe9
  CapacitorBarcodeScanner: 29c88bf098adb0830975d4f14c107a6bb7722e3d
  CapacitorBleAdvertiser: 78b126c0c1b1f6d7e54c4102eb0305f2c41ed306
  CapacitorCommunityBluetoothLe: f911fcbecc25033bca1cfcc5782c46c11ebf62ec
  CapacitorCommunityMedia: d1538b56a3303298e7cf45a18b11e624fbcbdc8e
  CapacitorCommunitySqlite: 8b2c6bab33e3519280811d481f8bd0fa90343e1b
  CapacitorCordova: 5967b9ba03915ef1d585469d6e31f31dc49be96f
  CapacitorDevice: c6f6d587dd310527f8a48bf09c4e7b4a4cf14329
  CapacitorFilesystem: 337eeaf16a9fa5a44d2329df411523fa3a9a6ee9
  CapacitorKeyboard: 09fd91dcde4f8a37313e7f11bde553ad1ed52036
  CapacitorNetwork: 15cb4385f0913a8ceb5e9a4d7af1ec554bdb8de8
  CapacitorPreferences: 6c98117d4d7508034a4af9db64d6b26fc75d7b94
  CapacitorPrivacyScreen: 3a6a5933b994bda73f46375de59a79109014ddfc
  CapacitorStatusBar: 6e7af040d8fc4dd655999819625cae9c2d74c36f
  CapawesomeCapacitorLiveUpdate: 4589d25437c980a8be605523557097ad1118ba91
  IONFilesystemLib: ceacae793975039530458eabab0c495c70515a0d
  OSBarcodeLib: f2e981270a64faf476cb790864262b29ab8cd68a
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  SQLCipher: 31878d8ebd27e5c96db0b7cb695c96e9f8ad77da
  SSZipArchive: 62d4947b08730e4cda640473b0066d209ff033c9
  ZIPFoundation: b8c29ea7ae353b309bc810586181fd073cb3312c

PODFILE CHECKSUM: 8228544b36f9282177390953e9f4cce824c7abf7

COCOAPODS: 1.16.2

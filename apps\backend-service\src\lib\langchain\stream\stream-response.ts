// LangChain 流式响应主函数

import { LangChainChatEngine } from '../engine/chat-engine'
import type { LangChainEnv, CharacterType } from '../types'
import type { LangChainChatOptions, LangChainMessage } from '../types/chat'
import { generateUUID } from '@/lib/utils'
import { saveMessages, getMessagesByChatId } from '../../db/queries'
import { getRoleInfoById } from '../../db/queries/character'
import type { Env } from '../../../types/env'

export interface StreamResponseOptions {
  // 基础聊天选项
  chatId: string
  message: LangChainMessage
  userId: string
  userType: 'regular' | 'premium' | 'enterprise'
  username: string
  userGender?: string // 用户性别（可选）
  characterId?: string
  character?: CharacterType
  isNewChat?: boolean // 是否为新聊天，如果是则不需要查询历史消息
  language?: string // 用户语言（可选）
}

/**
 * 解析LLM回复中的图片提示
 */
function parseImagePrompt(content: string): string | null {
  const imagePromptMatch = content.match(/<imagePrompt>([^<]+)<\/imagePrompt>/)
  return imagePromptMatch ? imagePromptMatch[1].trim() : null
}

/**
 * 异步获取角色信息，不阻塞流式响应
 */
async function getCharacterAsync(
  env: Env,
  characterId?: string
): Promise<CharacterType | undefined> {
  if (!characterId) return undefined

  try {
    const roleInfo = await getRoleInfoById(env, characterId)
    if (roleInfo) {
      return {
        id: roleInfo.id,
        name: roleInfo.name,
        gender: roleInfo.gender as 'male' | 'female' | 'other',
        age: roleInfo.age || '',
        relationship: roleInfo.relationship || '',
        ethnicity: roleInfo.ethnicity || '',
        eyeColor: roleInfo.eyeColor || '',
        hairStyle: roleInfo.hairStyle || '',
        hairColor: roleInfo.hairColor || '',
        bodyType: roleInfo.bodyType || '',
        breastSize: roleInfo.breastSize || '',
        buttSize: roleInfo.buttSize || '',
        personality: roleInfo.personality || '',
        clothing: roleInfo.clothing || '',
        voice: roleInfo.voice || '',
        imageUrl: roleInfo.imageUrl || ''
      }
    }
  } catch (error) {
    console.error('🔍 [DEBUG] 异步获取角色信息失败:', error)
  }
  return undefined
}

/**
 * 创建 LangChain 流式响应
 * 这是唯一的流式响应方法，包含完整的对话历史处理
 */
export async function createLangChainStreamResponse(
  env: LangChainEnv | Env,
  options: StreamResponseOptions
) {
  const isDev = process.env.NODE_ENV === 'development'

  try {
    let langchainMessages: LangChainMessage[] = []

    // 🚀 优化：只有非新聊天才需要查询历史消息
    if (!options.isNewChat) {
      // 从数据库获取历史消息，保证数据一致性
      const previousMessages = await getMessagesByChatId(env as Env, { id: options.chatId })

      // 转换历史消息为 LangChain 格式
      langchainMessages = previousMessages.map(msg => {
        // 解析 parts 字段
        const parts = typeof msg.parts === 'string' ? JSON.parse(msg.parts) : msg.parts
        const content = parts
          .filter((part: any) => part.type === 'text')
          .map((part: any) => part.text)
          .join('')

        return {
          id: msg.id,
          role: msg.role as 'user' | 'assistant',
          content: content || '',
          parts: parts,
          attachments:
            typeof msg.attachments === 'string'
              ? JSON.parse(msg.attachments)
              : msg.attachments || [],
          createdAt: msg.createdAt
        }
      })

      if (isDev) {
        console.log('🔍 [DEBUG] 从数据库获取历史消息，数量:', previousMessages.length)
      }
    } else {
      if (isDev) {
        console.log('🔍 [DEBUG] 新聊天，跳过历史消息查询')
      }
    }

    // 🚀 关键修复：将当前用户消息添加到历史消息中，确保 AI 能看到用户刚发送的内容
    langchainMessages.push(options.message)

    if (isDev) {
      console.log('🔍 [DEBUG] 包含当前用户消息后，总消息数量:', langchainMessages.length)
    }

    // 转换为标准的 LangChainChatOptions
    const chatOptions: LangChainChatOptions = {
      chatId: options.chatId,
      message: options.message,
      userId: options.userId,
      userType: options.userType,
      username: options.username,
      userGender: options.userGender, // 添加用户性别参数
      characterId: options.characterId,
      language: options.language // 添加语言参数
    }

    // 创建对话引擎
    const chatEngine = new LangChainChatEngine(env)

    // 创建流式响应
    const stream = new ReadableStream({
      async start(controller) {
        let fullResponse = ''
        const assistantMessageId = generateUUID()
        const character: CharacterType | undefined = options.character

        try {
          const encoder = new TextEncoder()

          // 🔥 首先发送助手消息的真实 ID
          const messageIdInfo =
            JSON.stringify({
              type: 'message_id',
              assistantMessageId: assistantMessageId
            }) + '\n'
          controller.enqueue(encoder.encode(messageIdInfo))

          // 🚀 角色信息现在由前端直接传递，无需异步获取
          if (character) {
            console.log('🔍 [DEBUG] 使用前端传递的角色信息:', character.name)
          } else {
            console.log('🔍 [DEBUG] 未提供角色信息，使用默认设置')
          }

          const responseStream = chatEngine.generateStreamResponse(
            chatOptions,
            langchainMessages, // 传递完整历史！
            character // 可能为 undefined，但不会阻塞
          )

          for await (const chunk of responseStream) {
            fullResponse += chunk

            // 将每个 chunk 编码为 Uint8Array 并发送
            controller.enqueue(encoder.encode(chunk))
          }

          const imagePrompt = parseImagePrompt(fullResponse)

          // 流式响应完成后，保存消息到数据库
          try {
            await saveMessages(env as Env, {
              messages: [
                // 保存用户消息
                {
                  id: options.message.id,
                  chatId: options.chatId,
                  role: 'user' as const,
                  parts: options.message.parts,
                  attachments: options.message.attachments || []
                },
                // 保存AI回复
                {
                  id: assistantMessageId,
                  chatId: options.chatId,
                  role: 'assistant' as const,
                  parts: [{ type: 'text', text: fullResponse }],
                  attachments: [] // 图片附件将通过异步任务添加
                }
              ]
            })

            console.log('💾 [DEBUG] 消息保存成功')

            // 🎨 如果检测到图片需求，记录日志（前端会自动处理图片生成）
            if (imagePrompt) {
              console.log('🎨 [INFO] 检测到图片生成需求，前端将自动处理:', {
                assistantMessageId,
                chatId: options.chatId,
                promptLength: imagePrompt.length,
                hasCharacterImage: !!character?.imageUrl
              })
            } else {
              console.log('ℹ️ [INFO] 未检测到图片生成需求')
            }
          } catch (saveError) {
            console.error('❌ [ERROR] 保存聊天记录失败:', saveError)
          }

          controller.close()
        } catch (error) {
          console.error('❌ [ERROR] 流式生成错误:', error)
          controller.error(error)
        }
      }
    })

    // 返回流式响应
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Encoding': 'identity',
        'X-Accel-Buffering': 'no',
        Connection: 'keep-alive',
        'X-Engine': 'langchain',
        'X-Version': '2.0.0',
        'X-History-Messages': langchainMessages.length.toString()
      }
    })
  } catch (error) {
    console.error('❌ [ERROR] LangChain 流式响应错误:', error)

    return new Response(
      JSON.stringify({
        success: false,
        message: (error as Error).message || '生成响应失败',
        error: 'LANGCHAIN_STREAM_ERROR'
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'X-Engine': 'langchain',
          'X-Version': '2.0.0'
        }
      }
    )
  }
}

# Mobile Input 组件

## 概述

Mobile Input 组件库专为移动设备设计，解决了键盘遮挡输入框的问题。当用户在移动设备上点击输入框时，组件会在键盘上方显示一个浮动的输入框，确保用户能够清楚地看到正在输入的内容。

## 组件列表

### KeyboardAdaptiveInput

通用的键盘适配输入组件，支持 input 和 textarea 两种类型。

**特性：**

- 🎯 **智能适配**: 自动检测原生平台，只在需要时启用键盘适配
- 📱 **移动优先**: 专为移动设备键盘遮挡问题设计
- 🔄 **双向同步**: 浮动输入框与原始输入框内容自动同步
- ⌨️ **键盘感知**: 使用 Capacitor Keyboard API 精确获取键盘高度
- 🎨 **简洁样式**: 浮动输入框贴合键盘，简洁设计
- 🔧 **高度可配置**: 支持所有原生 input/textarea 属性
- ⌨️ **键盘提交**: Input 回车提交，Textarea Ctrl/Cmd+回车提交

### KeyboardAdaptiveInputField

预设为 input 类型的便捷组件。

### KeyboardAdaptiveTextarea

预设为 textarea 类型的便捷组件。

## 安装依赖

确保已安装 Capacitor Keyboard 插件：

```bash
pnpm add @capacitor/keyboard
```

## 基础用法

### Input 输入框

```tsx
import { KeyboardAdaptiveInputField } from '@/components/mobile-input'

function MyForm() {
  const [value, setValue] = useState('')

  return (
    <KeyboardAdaptiveInputField
      placeholder="请输入内容"
      value={value}
      onChange={e => setValue(e.target.value)}
      onSubmit={value => {
        console.log('提交内容:', value)
        // 处理提交逻辑
      }}
    />
  )
}
```

### Textarea 文本域

```tsx
import { KeyboardAdaptiveTextarea } from '@/components/mobile-input'

function CommentForm() {
  const [comment, setComment] = useState('')

  return (
    <KeyboardAdaptiveTextarea
      placeholder="写下你的评论..."
      value={comment}
      onChange={e => setComment(e.target.value)}
      rows={4}
      onSubmit={value => {
        console.log('发送评论:', value)
        // 发送评论
        setComment('') // 清空输入
      }}
    />
  )
}
```

### 通用组件

```tsx
import { KeyboardAdaptiveInput } from '@/components/mobile-input'

function FlexibleInput() {
  return (
    <KeyboardAdaptiveInput
      type="input" // 或 "textarea"
      placeholder="灵活的输入组件"
      onSubmit={value => console.log(value)}
    />
  )
}
```

## API 参考

### Props

| 属性           | 类型                       | 默认值   | 描述                   |
| -------------- | -------------------------- | -------- | ---------------------- |
| `type`         | `'input' \| 'textarea'`    | -        | 输入框类型             |
| `buttonType`   | `'send' \| 'done'`         | `'done'` | 浮动输入框中按钮的类型 |
| `onSubmit`     | `(value: string) => void`  | -        | 点击提交按钮时的回调   |
| `className`    | `string`                   | -        | 原始输入框的样式类名   |
| `placeholder`  | `string`                   | -        | 占位符文本             |
| `value`        | `string`                   | -        | 受控组件的值           |
| `defaultValue` | `string`                   | -        | 非受控组件的默认值     |
| `onChange`     | `(e: ChangeEvent) => void` | -        | 值变化时的回调         |
| `disabled`     | `boolean`                  | `false`  | 是否禁用               |
| `readOnly`     | `boolean`                  | `false`  | 是否只读               |
| `maxLength`    | `number`                   | -        | 最大字符长度           |
| `rows`         | `number`                   | -        | textarea 的行数        |

## 工作原理

1. **平台检测**: 组件使用 `Capacitor.isNativePlatform()` 检测是否在原生平台运行
2. **焦点拦截**: 在原生平台上，拦截原始输入框的焦点事件
3. **键盘监听**: 监听 Capacitor Keyboard API 的 `keyboardWillShow` 和 `keyboardWillHide` 事件
4. **浮动显示**: 键盘弹出时，在键盘上方显示浮动输入框
5. **内容同步**: 用户输入时实时同步内容，提交时同步回原始输入框
6. **自动隐藏**: 键盘隐藏时，浮动输入框也自动隐藏

## 注意事项

- 该组件只在 Capacitor 原生平台上启用键盘适配功能
- 在 Web 平台上，组件表现与普通的 Input/Textarea 相同
- 浮动输入框使用 Portal 渲染到 document.body，确保在最顶层显示
- 组件会自动处理受控和非受控两种使用模式

## 样式定制

浮动输入框使用内联样式以确保正确的层级和定位，如需定制样式，可以通过以下方式：

1. 修改组件内部的 `floatingStyle` 对象
2. 通过 CSS 选择器覆盖样式（注意 z-index 层级）
3. 传递 `className` 来定制原始输入框样式

## 演示组件

使用 `KeyboardAdaptiveInputDemo` 组件来测试和展示功能：

```tsx
import { KeyboardAdaptiveInputDemo } from '@/components/mobile-input'

function TestPage() {
  return <KeyboardAdaptiveInputDemo />
}
```

{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off"}, "complexity": {"noForEach": "off"}, "style": {"noNonNullAssertion": "off"}}}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineWidth": 100, "lineEnding": "lf"}, "javascript": {"formatter": {"quoteStyle": "single", "trailingCommas": "es5", "semicolons": "always"}}, "json": {"formatter": {"enabled": true}}, "files": {"ignore": ["node_modules/**", "dist/**", ".wrangler/**", "src/lib/db/migrations/**"]}}
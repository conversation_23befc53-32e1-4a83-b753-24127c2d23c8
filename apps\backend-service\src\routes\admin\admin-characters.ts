import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult } from '@/lib/db/supabase-types'

const adminCharacters = new Hono<{ Bindings: Env }>()

// 系统角色用户ID常量
const SYSTEM_USER_ID = '00000000-0000-0000-0000-000000000000'

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 系统角色列表管理 ====================

// 查询参数验证
const characterListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  category: z.string().optional(),
  isActive: z
    .string()
    .transform(val => val === 'true')
    .optional(),
  isPublic: z
    .string()
    .transform(val => val === 'true')
    .optional()
})

// 获取系统角色列表
adminCharacters.get('/', authMiddleware, zValidator('query', characterListSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { page, pageSize, keyword, category, isActive, isPublic } = c.req.valid('query')
    const env = c.env

    console.log('📋 [ADMIN-CHARACTERS] 获取系统角色列表:', {
      page,
      pageSize,
      keyword,
      category,
      isActive,
      isPublic
    })

    // 从数据库查询系统角色列表
    const supabase = getSupabase(env)

    let query = supabase.from('Character').select('*').eq('user_id', SYSTEM_USER_ID)

    // 构建筛选条件
    if (keyword) {
      query = query.or(
        `name.ilike.%${keyword}%,description.ilike.%${keyword}%,personality.ilike.%${keyword}%`
      )
    }

    if (category) {
      query = query.eq('category', category)
    }

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive)
    }

    if (isPublic !== undefined) {
      query = query.eq('is_public', isPublic)
    }

    // 添加排序
    query = query.order('created_at', { ascending: false })

    // 计算总数
    let countQuery = supabase
      .from('Character')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', SYSTEM_USER_ID)

    if (keyword) {
      countQuery = countQuery.or(
        `name.ilike.%${keyword}%,description.ilike.%${keyword}%,personality.ilike.%${keyword}%`
      )
    }
    if (category) {
      countQuery = countQuery.eq('category', category)
    }
    if (isActive !== undefined) {
      countQuery = countQuery.eq('is_active', isActive)
    }
    if (isPublic !== undefined) {
      countQuery = countQuery.eq('is_public', isPublic)
    }

    const countResult = await countQuery
    const total = countResult.count || 0

    // 分页查询
    const offset = (page - 1) * pageSize
    const result = await query.range(offset, offset + pageSize - 1)
    const { data: characters, error } = handleSupabaseResult(result)

    if (error) throw error

    console.log(`📋 [ADMIN-CHARACTERS] 查询到 ${characters.length} 个系统角色，共 ${total} 个`)

    // 转换数据格式 (已经是 camelCase)
    const formattedCharacters = (characters || []).map((char: any) => ({
      id: char.id,
      user_id: char.user_id,
      name: char.name,
      description: char.description,
      relationship: char.relationship,
      ethnicity: char.ethnicity,
      gender: char.gender,
      age: char.age,
      eyeColor: char.eyeColor,
      hairStyle: char.hairStyle,
      hairColor: char.hairColor,
      bodyType: char.bodyType,
      breastSize: char.breastSize,
      buttSize: char.buttSize,
      personality: char.personality,
      clothing: char.clothing,
      voice: char.voice,
      voiceModelId: char.voiceModelId,
      keywords: char.keywords,
      prompt: char.prompt,
      imageUrl: char.imageUrl,
      category: char.category,
      isPublic: char.isPublic,
      isActive: char.isActive,
      createdAt: char.createdAt,
      updatedAt: char.updatedAt
    }))

    return c.json({
      success: true,
      data: {
        data: formattedCharacters,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('❌ [ADMIN-CHARACTERS] 获取系统角色列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取系统角色列表失败'
      },
      500
    )
  }
})

// ==================== 系统角色详情管理 ====================

// 获取系统角色详情
adminCharacters.get('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-CHARACTERS] 获取系统角色详情:', id)

    // 查询系统角色详情
    const supabase = getSupabase(env)
    const result = await supabase
      .from('Character')
      .select('*')
      .eq('id', id)
      .eq('user_id', SYSTEM_USER_ID)
      .single()

    const { data: character, error } = handleSupabaseResult(result)

    if (error || !character) {
      return c.json(
        {
          success: false,
          message: '系统角色不存在'
        },
        404
      )
    }

    // 格式化数据 (已经是 camelCase)
    const formattedCharacter = {
      id: character.id,
      user_id: character.user_id,
      name: character.name,
      description: character.description,
      relationship: character.relationship,
      ethnicity: character.ethnicity,
      gender: character.gender,
      age: character.age,
      eyeColor: character.eyeColor,
      hairStyle: character.hairStyle,
      hairColor: character.hairColor,
      bodyType: character.bodyType,
      breastSize: character.breastSize,
      buttSize: character.buttSize,
      personality: character.personality,
      clothing: character.clothing,
      voice: character.voice,
      voiceModelId: character.voiceModelId,
      keywords: character.keywords,
      prompt: character.prompt,
      imageUrl: character.imageUrl,
      category: character.category,
      isPublic: character.isPublic,
      isActive: character.isActive,
      createdAt: character.createdAt,
      updatedAt: character.updatedAt
    }

    return c.json({
      success: true,
      data: formattedCharacter
    })
  } catch (error) {
    console.error('❌ [ADMIN-CHARACTERS] 获取系统角色详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取系统角色详情失败'
      },
      500
    )
  }
})

// ==================== 系统角色创建和编辑 ====================

// 创建系统角色验证模式
const createCharacterSchema = z.object({
  name: z.string().min(1, '角色名称不能为空'),
  description: z.string().optional(),
  relationship: z.string().optional(),
  ethnicity: z.string().optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  age: z.string().optional(),
  eyeColor: z.string().optional(),
  hairStyle: z.string().optional(),
  hairColor: z.string().optional(),
  bodyType: z.string().optional(),
  breastSize: z.string().optional(),
  buttSize: z.string().optional(),
  personality: z.string().optional(),
  clothing: z.string().optional(),
  voice: z.string().optional(),
  voiceModelId: z.string().optional(),
  keywords: z.string().min(1, '关键词不能为空'),
  prompt: z.string().min(1, 'AI提示词不能为空'),
  imageUrl: z.string().optional(),
  category: z.string().optional(),
  isPublic: z.boolean().default(true),
  isActive: z.boolean().default(true)
})

// 创建系统角色
adminCharacters.post('/', authMiddleware, zValidator('json', createCharacterSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const characterData = c.req.valid('json')
    const env = c.env

    console.log('➕ [ADMIN-CHARACTERS] 创建系统角色:', characterData.name)

    // 创建系统角色
    const supabase = getSupabase(env)
    const result = await supabase
      .from('Character')
      .insert([
        {
          user_id: SYSTEM_USER_ID,
          name: characterData.name,
          description: characterData.description,
          relationship: characterData.relationship,
          ethnicity: characterData.ethnicity,
          gender: characterData.gender,
          age: characterData.age,
          eyeColor: characterData.eyeColor,
          hairStyle: characterData.hairStyle,
          hairColor: characterData.hairColor,
          bodyType: characterData.bodyType,
          breastSize: characterData.breastSize,
          buttSize: characterData.buttSize,
          personality: characterData.personality,
          clothing: characterData.clothing,
          voice: characterData.voice,
          voiceModelId: characterData.voiceModelId,
          keywords: characterData.keywords,
          prompt: characterData.prompt,
          imageUrl: characterData.imageUrl,
          category: characterData.category,
          isPublic: characterData.isPublic,
          isActive: characterData.isActive
        }
      ])
      .select()

    const { data: newCharacters, error } = handleSupabaseResult(result)

    if (error || !newCharacters || newCharacters.length === 0) {
      return c.json({ success: false, message: '创建系统角色失败' }, 500)
    }

    const newCharacter = newCharacters[0]

    console.log('✅ [ADMIN-CHARACTERS] 系统角色创建成功:', newCharacter.id)

    return c.json({
      success: true,
      data: {
        id: newCharacter.id,
        name: newCharacter.name,
        description: newCharacter.description,
        category: newCharacter.category,
        isPublic: newCharacter.isPublic,
        isActive: newCharacter.isActive,
        createdAt: newCharacter.createdAt,
        updatedAt: newCharacter.updatedAt
      },
      message: '系统角色创建成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-CHARACTERS] 创建系统角色失败:', error)
    return c.json(
      {
        success: false,
        message: '创建系统角色失败'
      },
      500
    )
  }
})

// 更新系统角色
adminCharacters.put(
  '/:id',
  authMiddleware,
  zValidator('json', createCharacterSchema.partial()),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const updateData = c.req.valid('json')
      const env = c.env

      console.log('📝 [ADMIN-CHARACTERS] 更新系统角色:', id)

      // 更新系统角色
      const supabase = getSupabase(env)
      const updatePayload: any = {}

      if (updateData.name !== undefined) updatePayload.name = updateData.name
      if (updateData.description !== undefined) updatePayload.description = updateData.description
      if (updateData.relationship !== undefined)
        updatePayload.relationship = updateData.relationship
      if (updateData.ethnicity !== undefined) updatePayload.ethnicity = updateData.ethnicity
      if (updateData.gender !== undefined) updatePayload.gender = updateData.gender
      if (updateData.age !== undefined) updatePayload.age = updateData.age
      if (updateData.eyeColor !== undefined) updatePayload.eyeColor = updateData.eyeColor
      if (updateData.hairStyle !== undefined) updatePayload.hairStyle = updateData.hairStyle
      if (updateData.hairColor !== undefined) updatePayload.hairColor = updateData.hairColor
      if (updateData.bodyType !== undefined) updatePayload.bodyType = updateData.bodyType
      if (updateData.breastSize !== undefined) updatePayload.breastSize = updateData.breastSize
      if (updateData.buttSize !== undefined) updatePayload.buttSize = updateData.buttSize
      if (updateData.personality !== undefined) updatePayload.personality = updateData.personality
      if (updateData.clothing !== undefined) updatePayload.clothing = updateData.clothing
      if (updateData.voice !== undefined) updatePayload.voice = updateData.voice
      if (updateData.voiceModelId !== undefined)
        updatePayload.voiceModelId = updateData.voiceModelId
      if (updateData.keywords !== undefined) updatePayload.keywords = updateData.keywords
      if (updateData.prompt !== undefined) updatePayload.prompt = updateData.prompt
      if (updateData.imageUrl !== undefined) updatePayload.imageUrl = updateData.imageUrl
      if (updateData.category !== undefined) updatePayload.category = updateData.category
      if (updateData.isPublic !== undefined) updatePayload.isPublic = updateData.isPublic
      if (updateData.isActive !== undefined) updatePayload.isActive = updateData.isActive

      updatePayload.updatedAt = new Date().toISOString()

      const result = await supabase
        .from('Character')
        .update(updatePayload)
        .eq('id', id)
        .eq('user_id', SYSTEM_USER_ID)
        .select()

      const { data: updatedCharacters, error } = handleSupabaseResult(result)

      if (error || !updatedCharacters || updatedCharacters.length === 0) {
        return c.json({ success: false, message: '系统角色不存在' }, 404)
      }

      const updatedCharacter = updatedCharacters[0]

      console.log('✅ [ADMIN-CHARACTERS] 系统角色更新成功:', id)

      return c.json({
        success: true,
        data: {
          id: updatedCharacter.id,
          name: updatedCharacter.name,
          description: updatedCharacter.description,
          category: updatedCharacter.category,
          isPublic: updatedCharacter.isPublic,
          isActive: updatedCharacter.isActive,
          createdAt: updatedCharacter.createdAt,
          updatedAt: updatedCharacter.updatedAt
        },
        message: '系统角色更新成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-CHARACTERS] 更新系统角色失败:', error)
      return c.json(
        {
          success: false,
          message: '更新系统角色失败'
        },
        500
      )
    }
  }
)

// 删除系统角色
adminCharacters.delete('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🗑️ [ADMIN-CHARACTERS] 删除系统角色:', id)

    // 删除系统角色
    const supabase = getSupabase(env)
    const result = await supabase
      .from('Character')
      .delete()
      .eq('id', id)
      .eq('user_id', SYSTEM_USER_ID)
      .select()

    const { data: deletedCharacters, error } = handleSupabaseResult(result)

    if (error || !deletedCharacters || deletedCharacters.length === 0) {
      return c.json({ success: false, message: '系统角色不存在' }, 404)
    }

    console.log('✅ [ADMIN-CHARACTERS] 系统角色删除成功:', id)

    return c.json({
      success: true,
      message: '系统角色删除成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-CHARACTERS] 删除系统角色失败:', error)
    return c.json(
      {
        success: false,
        message: '删除系统角色失败'
      },
      500
    )
  }
})

// ==================== 获取角色分类列表 ====================

// 获取系统角色分类列表
adminCharacters.get('/categories/list', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📋 [ADMIN-CHARACTERS] 获取角色分类列表')

    // 查询所有不同的分类
    const supabase = getSupabase(env)
    const result = await supabase
      .from('Character')
      .select('category')
      .eq('user_id', SYSTEM_USER_ID)
      .not('category', 'is', null)

    const { data: categories, error } = handleSupabaseResult(result)

    if (error) throw error

    // 去重并统计每个分类的数量
    const categoryMap = new Map<string, number>()
    categories?.forEach((item: any) => {
      if (item.category) {
        categoryMap.set(item.category, (categoryMap.get(item.category) || 0) + 1)
      }
    })

    const categoryList = Array.from(categoryMap.entries()).map(([name, count]) => ({
      name,
      count
    }))

    return c.json({
      success: true,
      data: categoryList
    })
  } catch (error) {
    console.error('❌ [ADMIN-CHARACTERS] 获取角色分类列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取角色分类列表失败'
      },
      500
    )
  }
})

export default adminCharacters

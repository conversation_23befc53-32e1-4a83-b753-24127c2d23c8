import React from 'react'
import { <PERSON><PERSON>, <PERSON>dal<PERSON>ontent, ModalHeader, ModalBody } from '@heroui/react'
import { VoiceRecorder } from './VoiceRecorder'

interface VoiceInputProps {
  /** 是否显示语音录制界面 */
  isOpen: boolean
  /** 关闭语音录制界面 */
  onClose: () => void
  /** 语音转文本完成回调 */
  onTextResult: (text: string) => void
  /** 最大录制时长(秒) */
  maxDuration?: number
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 语音输入组件
 * 提供模态化的语音录制界面
 */
export function VoiceInput({
  isOpen,
  onClose,
  onTextResult,
  maxDuration = 60,
  disabled = false
}: VoiceInputProps) {
  // 处理语音转文本结果
  const handleTextResult = (text: string) => {
    onTextResult(text)
    onClose() // 成功后自动关闭
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      placement="center"
      backdrop="blur"
      className="mx-4"
      classNames={{
        body: 'py-6',
        base: 'border-none bg-background/80 backdrop-blur-md',
        header: 'border-b-[1px] border-divider',
        footer: 'border-t-[1px] border-divider'
      }}
    >
      <ModalContent>
        <ModalHeader className="flex flex-col gap-1">
          <h3 className="text-lg font-semibold">语音输入</h3>
          <p className="text-sm text-default-500">点击录制或长按快速录制，说出您想要输入的内容</p>
        </ModalHeader>

        <ModalBody>
          <div className="flex justify-center py-4">
            <VoiceRecorder
              onTextResult={handleTextResult}
              disabled={disabled}
              maxDuration={maxDuration}
              className="w-full"
            />
          </div>
        </ModalBody>
      </ModalContent>
    </Modal>
  )
}

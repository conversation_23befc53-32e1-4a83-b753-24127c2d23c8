import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'
import type {
  Template,
  TemplateListParams,
  TemplateCreateParams,
  TemplateUpdateParams,
  TemplateStats
} from '@/types/template'

// 写真集模板管理服务
export class TemplateService {
  // ==================== 模板CRUD ====================

  // 获取模板列表
  async getTemplates(params?: TemplateListParams): Promise<ApiResponse<PaginatedResponse<Template>>> {
    return await apiService.get<PaginatedResponse<Template>>('/admin/content/templates', { params })
  }

  // 获取模板详情
  async getTemplate(id: string): Promise<ApiResponse<Template>> {
    return await apiService.get<Template>(`/admin/content/templates/${id}`)
  }

  // 创建模板
  async createTemplate(params: TemplateCreateParams): Promise<ApiResponse<Template>> {
    return await apiService.post<Template>('/admin/content/templates', params)
  }

  // 更新模板
  async updateTemplate(params: TemplateUpdateParams): Promise<ApiResponse<Template>> {
    const { id, ...updateData } = params
    return await apiService.put<Template>(`/admin/content/templates/${id}`, updateData)
  }

  // 删除模板
  async deleteTemplate(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/content/templates/${id}`)
  }

  // ==================== 模板状态管理 ====================

  // 启用/禁用模板
  async toggleTemplateStatus(id: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/templates/${id}/toggle-status`, { isActive })
  }

  // 设置模板公开状态
  async toggleTemplatePublic(id: string, isPublic: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/content/templates/${id}/toggle-public`, { isPublic })
  }

  // 上传模板预览图片
  async uploadPreviewImage(file: File): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData()
    formData.append('file', file)
    return await apiService.post<{ url: string }>('/admin/content/templates/upload-preview', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }

  // ==================== 统计和分析 ====================

  // 获取模板统计数据
  async getStats(): Promise<ApiResponse<TemplateStats>> {
    return await apiService.get<TemplateStats>('/admin/content/templates/stats/summary')
  }

  // 获取模板分类列表
  async getCategories(): Promise<ApiResponse<string[]>> {
    return await apiService.get<string[]>('/admin/content/templates/categories')
  }

  // ==================== 批量操作 ====================

  // 批量删除模板
  async batchDeleteTemplates(ids: string[]): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/templates/batch-delete', { ids })
  }

  // 批量设置状态
  async batchToggleStatus(ids: string[], isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/templates/batch-toggle-status', { ids, isActive })
  }

  // 批量设置公开状态
  async batchTogglePublic(ids: string[], isPublic: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/content/templates/batch-toggle-public', { ids, isPublic })
  }

  // ==================== 导入导出 ====================

  // 导出模板数据
  async exportTemplates(params?: {
    format?: 'json' | 'csv'
    ids?: string[]
  }): Promise<ApiResponse<{ downloadUrl: string }>> {
    return await apiService.post<{ downloadUrl: string }>('/admin/content/templates/export', params)
  }

  // 导入模板数据
  async importTemplates(file: File): Promise<ApiResponse<{ 
    success: number
    failed: number
    errors?: string[]
  }>> {
    const formData = new FormData()
    formData.append('file', file)
    return await apiService.post<{
      success: number
      failed: number
      errors?: string[]
    }>('/admin/content/templates/import', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export const templateService = new TemplateService()
CREATE TABLE IF NOT EXISTS "TTSTask" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"message_id" uuid,
	"chat_id" uuid,
	"text" text NOT NULL,
	"voice" varchar(50),
	"status" varchar DEFAULT 'pending' NOT NULL,
	"audio_url" text,
	"error_message" text,
	"progress" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TTSTask" ADD CONSTRAINT "TTSTask_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TTSTask" ADD CONSTRAINT "TTSTask_message_id_Message_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."Message"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "TTSTask" ADD CONSTRAINT "TTSTask_chat_id_Chat_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."Chat"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_tts_user_id" ON "TTSTask" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_tts_status" ON "TTSTask" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_tts_message_id" ON "TTSTask" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_tts_chat_id" ON "TTSTask" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_tts_created_at" ON "TTSTask" USING btree ("created_at" DESC NULLS LAST);
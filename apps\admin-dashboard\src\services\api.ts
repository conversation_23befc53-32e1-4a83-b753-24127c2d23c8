import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import type { ApiResponse } from '@/types/api'
import { API_BASE_URL } from '@/constants'

// 创建 axios 实例
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 60000, // 增加超时时间到60秒
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器 - 添加认证头
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 防止频繁跳转的标记
let isRedirecting = false

// 响应拦截器 - 统一处理错误
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    // 处理网络错误
    if (error.code === 'ECONNABORTED' || error.name === 'AbortError') {
      console.warn('请求超时或被中断:', error.message)
      return Promise.reject({
        code: 'TIMEOUT',
        message: '请求超时，请重试',
        ...error
      })
    }

    // 处理401未授权错误，但防止频繁跳转
    if (error.response?.status === 401 && !isRedirecting) {
      isRedirecting = true
      console.warn('认证失效，跳转到登录页')
      
      // 清除token
      localStorage.removeItem('admin_token')
      
      // 延迟跳转，避免过于频繁
      setTimeout(() => {
        window.location.href = '/login'
      }, 100)
      
      return Promise.reject({
        code: 'UNAUTHORIZED',
        message: '认证失效，请重新登录',
        ...error
      })
    }

    // 处理其他HTTP错误
    if (error.response) {
      const { status, data } = error.response
      console.error(`API错误 ${status}:`, data?.message || error.message)
      
      return Promise.reject({
        code: status,
        message: data?.message || `HTTP ${status} 错误`,
        ...error
      })
    }

    // 处理网络连接错误
    console.error('网络错误:', error.message)
    return Promise.reject({
      code: 'NETWORK_ERROR',
      message: '网络连接失败，请检查网络',
      ...error
    })
  }
)

// 通用请求方法
class ApiService {
  // GET 请求
  async get<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.get(url, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  // POST 请求
  async post<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.post(url, data, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  // PUT 请求
  async put<T>(
    url: string,
    data?: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.put(url, data, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  // DELETE 请求
  async delete<T>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> {
    try {
      const response = await apiClient.delete(url, config)
      return response.data
    } catch (error: any) {
      throw this.handleError(error)
    }
  }

  // 错误处理
  private handleError(error: any) {
    const message = error.response?.data?.message || 
                   error.response?.data?.error || 
                   error.message || 
                   '请求失败'
    
    return {
      success: false,
      error: message,
      status: error.response?.status
    }
  }
}

export const apiService = new ApiService()
export default apiClient
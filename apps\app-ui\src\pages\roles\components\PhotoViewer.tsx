import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'
import type { PhotoHistory } from '@/stores/photo-generation-store'

interface PhotoViewerProps {
  isVisible: boolean
  photo: PhotoHistory | null
  onClose: () => void
}

export function PhotoViewer({ isVisible, photo, onClose }: PhotoViewerProps) {
  if (!photo) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-[100] bg-black flex items-center justify-center"
          onClick={onClose}
        >
          {/* 关闭按钮 */}
          <motion.button
            className="absolute top-6 right-6 z-10 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white"
            onClick={e => {
              e.stopPropagation()
              onClose()
            }}
            whileTap={{ scale: 0.9 }}
          >
            <Icon icon="solar:close-circle-bold" className="w-6 h-6" />
          </motion.button>

          {/* 图片信息 */}
          <motion.div
            className="absolute bottom-6 left-6 right-6 z-10 bg-black/50 backdrop-blur-sm rounded-2xl p-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
          >
            <div className="text-white text-lg font-semibold mb-1">{photo.templateName}</div>
            <div className="text-gray-300 text-sm">
              {new Date(photo.createdAt).toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
              })}
            </div>
            {photo.pointsUsed && (
              <div className="flex items-center mt-2">
                <Icon icon="solar:star-bold" className="w-4 h-4 text-yellow-400 mr-1" />
                <span className="text-yellow-400 text-sm">消耗 {photo.pointsUsed} 积分</span>
              </div>
            )}
          </motion.div>

          {/* 全屏图片 */}
          <motion.img
            layoutId={`photo-${photo.id}`}
            src={photo.generatedImageUrl}
            alt={photo.templateName}
            className="max-w-full max-h-full object-contain"
            onClick={e => e.stopPropagation()}
          />
        </motion.div>
      )}
    </AnimatePresence>
  )
}

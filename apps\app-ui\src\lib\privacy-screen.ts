import { PrivacyScreen } from '@capacitor/privacy-screen'
import { Capacitor } from '@capacitor/core'

/**
 * 隐私屏幕服务
 * 用于在应用切换时保护敏感信息
 * 自动启用，不允许用户关闭
 */
export class PrivacyScreenService {
  private static instance: PrivacyScreenService
  private isEnabled = false

  private constructor() {
    // 初始化时自动启用隐私屏幕
    this.autoEnable()
  }

  static getInstance(): PrivacyScreenService {
    if (!PrivacyScreenService.instance) {
      PrivacyScreenService.instance = new PrivacyScreenService()
    }
    return PrivacyScreenService.instance
  }

  /**
   * 自动启用隐私屏幕（应用启动时调用）
   */
  private async autoEnable(): Promise<void> {
    try {
      if (Capacitor.isNativePlatform()) {
        await this.enable()
        console.log('隐私屏幕已自动启用')
      }
    } catch (error) {
      console.error('自动启用隐私屏幕失败:', error)
    }
  }

  /**
   * 启用隐私屏幕
   */
  async enable(): Promise<boolean> {
    try {
      // 只在原生平台上启用
      if (!Capacitor.isNativePlatform()) {
        console.log('隐私屏幕功能仅在原生平台可用')
        return false
      }

      const result = await PrivacyScreen.enable({
        // Android 配置
        android: {
          dimBackground: true, // 在后台时变暗
          preventScreenshots: true, // 防止截屏
          privacyModeOnActivityHidden: 'splash' // 显示启动画面
        },
        // iOS 配置
        ios: {
          blurEffect: 'dark' // 使用深色模糊效果
        }
      })

      this.isEnabled = result.success
      console.log('隐私屏幕已启用:', result.success)
      return result.success
    } catch (error) {
      console.error('启用隐私屏幕失败:', error)
      return false
    }
  }

  /**
   * 禁用隐私屏幕（已禁用此功能，隐私屏幕将始终保持启用状态）
   */
  async disable(): Promise<boolean> {
    console.log('隐私屏幕已被强制启用，无法关闭')
    return false
  }

  /**
   * 检查隐私屏幕是否启用
   */
  async checkStatus(): Promise<boolean> {
    try {
      if (!Capacitor.isNativePlatform()) {
        return false
      }

      const result = await PrivacyScreen.isEnabled()
      this.isEnabled = result.enabled
      return result.enabled
    } catch (error) {
      console.error('检查隐私屏幕状态失败:', error)
      return false
    }
  }

  /**
   * 获取当前状态
   */
  getLocalStatus(): boolean {
    return this.isEnabled
  }
}

// 导出单例实例
export const privacyScreenService = PrivacyScreenService.getInstance()

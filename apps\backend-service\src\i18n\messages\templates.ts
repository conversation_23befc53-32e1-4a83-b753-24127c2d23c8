// 模板相关消息
export const templateMessages = {
  zh: {
    // 通用错误消息
    get_templates_failed: '获取模板列表失败',
    template_search_failed: '模板搜索失败',
    get_categories_failed: '获取模板分类失败',
    template_id_required: '模板ID不能为空',
    template_not_found: '模板不存在',
    get_template_detail_failed: '获取模板详情失败',

    // 权限相关
    user_not_logged_in: '用户未登录',
    template_requires_membership: '此模板需要会员权限',
    insufficient_permission: '权限不足',

    // 生成相关
    insufficient_points: '点数不足',
    generation_failed: '图像生成失败',
    generation_started: '图像生成已开始，请稍候...',
    queue_send_failed: '启动图像生成失败，积分已退还，请重试',
    get_generation_history_failed: '获取生成历史失败',

    // 管理员功能
    template_name_required: '模板名称不能为空',
    template_name_too_long: '模板名称不能超过100个字符',
    template_category_required: '模板分类不能为空',
    category_name_too_long: '分类名称不能超过50个字符',
    preview_image_url_invalid: '预览图片URL格式不正确',
    prompt_required: '提示词不能为空',
    points_cost_invalid: '点数消耗必须大于0',
    template_id_invalid: '模板ID格式不正确',
    original_image_url_invalid: '原始图片URL格式不正确',

    // 成功消息
    template_created: '模板创建成功',
    template_updated: '模板更新成功',
    template_deleted: '模板删除成功',
    create_template_failed: '创建模板失败',
    update_template_failed: '更新模板失败',
    delete_template_failed: '删除模板失败',
    get_template_stats_failed: '获取模板统计失败'
  },
  'zh-TW': {
    // 通用錯誤訊息
    get_templates_failed: '取得模板列表失敗',
    template_search_failed: '模板搜尋失敗',
    get_categories_failed: '取得模板分類失敗',
    template_id_required: '模板ID不能為空',
    template_not_found: '模板不存在',
    get_template_detail_failed: '取得模板詳情失敗',

    // 權限相關
    user_not_logged_in: '使用者未登入',
    template_requires_membership: '此模板需要會員權限',
    insufficient_permission: '權限不足',

    // 生成相關
    insufficient_points: '點數不足',
    generation_failed: '圖像生成失敗',
    generation_started: '圖像生成已開始，請稍候...',
    queue_send_failed: '啟動圖像生成失敗，積分已退還，請重試',
    get_generation_history_failed: '取得生成歷史失敗',

    // 管理員功能
    template_name_required: '模板名稱不能為空',
    template_name_too_long: '模板名稱不能超過100個字元',
    template_category_required: '模板分類不能為空',
    category_name_too_long: '分類名稱不能超過50個字元',
    preview_image_url_invalid: '預覽圖片URL格式不正確',
    prompt_required: '提示詞不能為空',
    points_cost_invalid: '點數消耗必須大於0',
    template_id_invalid: '模板ID格式不正確',
    original_image_url_invalid: '原始圖片URL格式不正確',

    // 成功訊息
    template_created: '模板建立成功',
    template_updated: '模板更新成功',
    template_deleted: '模板刪除成功',
    create_template_failed: '建立模板失敗',
    update_template_failed: '更新模板失敗',
    delete_template_failed: '刪除模板失敗',
    get_template_stats_failed: '取得模板統計失敗'
  },
  ja: {
    // 一般的なエラーメッセージ
    get_templates_failed: 'テンプレートリストの取得に失敗しました',
    template_search_failed: 'テンプレート検索に失敗しました',
    get_categories_failed: 'テンプレートカテゴリの取得に失敗しました',
    template_id_required: 'テンプレートIDを入力してください',
    template_not_found: 'テンプレートが見つかりません',
    get_template_detail_failed: 'テンプレート詳細の取得に失敗しました',

    // 権限関連
    user_not_logged_in: 'ユーザーがログインしていません',
    template_requires_membership: 'このテンプレートにはメンバーシップが必要です',
    insufficient_permission: '権限が不足しています',

    // 生成関連
    insufficient_points: 'ポイントが不足しています',
    generation_failed: '画像生成に失敗しました',
    generation_started: '画像生成が開始されました。しばらくお待ちください...',
    queue_send_failed: '画像生成の開始に失敗しました。ポイントが返金されました。再試行してください',
    get_generation_history_failed: '生成履歴の取得に失敗しました',

    // 管理者機能
    template_name_required: 'テンプレート名を入力してください',
    template_name_too_long: 'テンプレート名は100文字以下である必要があります',
    template_category_required: 'テンプレートカテゴリを入力してください',
    category_name_too_long: 'カテゴリ名は50文字以下である必要があります',
    preview_image_url_invalid: 'プレビュー画像URLの形式が正しくありません',
    prompt_required: 'プロンプトを入力してください',
    points_cost_invalid: 'ポイント消費は0より大きい値である必要があります',
    template_id_invalid: 'テンプレートIDの形式が正しくありません',
    original_image_url_invalid: '元画像URLの形式が正しくありません',

    // 成功メッセージ
    template_created: 'テンプレートが作成されました',
    template_updated: 'テンプレートが更新されました',
    template_deleted: 'テンプレートが削除されました',
    create_template_failed: 'テンプレートの作成に失敗しました',
    update_template_failed: 'テンプレートの更新に失敗しました',
    delete_template_failed: 'テンプレートの削除に失敗しました',
    get_template_stats_failed: 'テンプレート統計の取得に失敗しました'
  },
  es: {
    // Mensajes de error generales
    get_templates_failed: 'Error al obtener la lista de plantillas',
    template_search_failed: 'Error en la búsqueda de plantillas',
    get_categories_failed: 'Error al obtener las categorías de plantillas',
    template_id_required: 'El ID de plantilla no puede estar vacío',
    template_not_found: 'Plantilla no encontrada',
    get_template_detail_failed: 'Error al obtener los detalles de la plantilla',

    // Relacionado con permisos
    user_not_logged_in: 'Usuario no ha iniciado sesión',
    template_requires_membership: 'Esta plantilla requiere membresía',
    insufficient_permission: 'Permisos insuficientes',

    // Relacionado con generación
    insufficient_points: 'Puntos insuficientes',
    generation_failed: 'Error en la generación de imágenes',
    generation_started: 'Generación de imágenes iniciada, por favor espere...',
    queue_send_failed:
      'Error al iniciar la generación de imágenes, puntos reembolsados, por favor inténtelo de nuevo',
    get_generation_history_failed: 'Error al obtener el historial de generación',

    // Funciones de administrador
    template_name_required: 'El nombre de la plantilla no puede estar vacío',
    template_name_too_long: 'El nombre de la plantilla no puede exceder los 100 caracteres',
    template_category_required: 'La categoría de plantilla no puede estar vacía',
    category_name_too_long: 'El nombre de la categoría no puede exceder los 50 caracteres',
    preview_image_url_invalid: 'El formato de la URL de imagen de vista previa es incorrecto',
    prompt_required: 'El prompt no puede estar vacío',
    points_cost_invalid: 'El costo en puntos debe ser mayor que 0',
    template_id_invalid: 'El formato del ID de plantilla es incorrecto',
    original_image_url_invalid: 'El formato de la URL de imagen original es incorrecto',

    // Mensajes de éxito
    template_created: 'Plantilla creada correctamente',
    template_updated: 'Plantilla actualizada correctamente',
    template_deleted: 'Plantilla eliminada correctamente',
    create_template_failed: 'Error al crear la plantilla',
    update_template_failed: 'Error al actualizar la plantilla',
    delete_template_failed: 'Error al eliminar la plantilla',
    get_template_stats_failed: 'Error al obtener las estadísticas de la plantilla'
  },
  en: {
    // 通用错误消息
    get_templates_failed: 'Failed to get template list',
    template_search_failed: 'Template search failed',
    get_categories_failed: 'Failed to get template categories',
    template_id_required: 'Template ID cannot be empty',
    template_not_found: 'Template not found',
    get_template_detail_failed: 'Failed to get template details',

    // 权限相关
    user_not_logged_in: 'User not logged in',
    template_requires_membership: 'This template requires membership',
    insufficient_permission: 'Insufficient permission',

    // 生成相关
    insufficient_points: 'Insufficient points',
    generation_failed: 'Image generation failed',
    generation_started: 'Image generation started, please wait...',
    queue_send_failed: 'Failed to start image generation, points refunded, please try again',
    get_generation_history_failed: 'Failed to get generation history',

    // 管理员功能
    template_name_required: 'Template name cannot be empty',
    template_name_too_long: 'Template name cannot exceed 100 characters',
    template_category_required: 'Template category cannot be empty',
    category_name_too_long: 'Category name cannot exceed 50 characters',
    preview_image_url_invalid: 'Preview image URL format is incorrect',
    prompt_required: 'Prompt cannot be empty',
    points_cost_invalid: 'Points cost must be greater than 0',
    template_id_invalid: 'Template ID format is incorrect',
    original_image_url_invalid: 'Original image URL format is incorrect',

    // 成功消息
    template_created: 'Template created successfully',
    template_updated: 'Template updated successfully',
    template_deleted: 'Template deleted successfully',
    create_template_failed: 'Failed to create template',
    update_template_failed: 'Failed to update template',
    delete_template_failed: 'Failed to delete template',
    get_template_stats_failed: 'Failed to get template statistics'
  }
}

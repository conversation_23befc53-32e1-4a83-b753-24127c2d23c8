// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN
include ':capacitor-android'
project(':capacitor-android').projectDir = new File('../../../node_modules/.pnpm/@capacitor+android@7.2.0_@capacitor+core@7.2.0/node_modules/@capacitor/android/capacitor')

include ':capacitor-community-bluetooth-le'
project(':capacitor-community-bluetooth-le').projectDir = new File('../../../node_modules/.pnpm/@capacitor-community+bluetooth-le@7.0.0_@capacitor+core@7.2.0/node_modules/@capacitor-community/bluetooth-le/android')

include ':capacitor-community-media'
project(':capacitor-community-media').projectDir = new File('../../../node_modules/.pnpm/@capacitor-community+media@8.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/media/android')

include ':capacitor-community-sqlite'
project(':capacitor-community-sqlite').projectDir = new File('../../../node_modules/.pnpm/@capacitor-community+sqlite@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor-community/sqlite/android')

include ':capacitor-app'
project(':capacitor-app').projectDir = new File('../../../node_modules/.pnpm/@capacitor+app@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/app/android')

include ':capacitor-barcode-scanner'
project(':capacitor-barcode-scanner').projectDir = new File('../../../node_modules/.pnpm/@capacitor+barcode-scanner@2.0.3_@capacitor+core@7.2.0/node_modules/@capacitor/barcode-scanner/android')

include ':capacitor-device'
project(':capacitor-device').projectDir = new File('../../../node_modules/.pnpm/@capacitor+device@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/device/android')

include ':capacitor-filesystem'
project(':capacitor-filesystem').projectDir = new File('../../../node_modules/.pnpm/@capacitor+filesystem@7.1.2_@capacitor+core@7.2.0/node_modules/@capacitor/filesystem/android')

include ':capacitor-keyboard'
project(':capacitor-keyboard').projectDir = new File('../../../node_modules/.pnpm/@capacitor+keyboard@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/keyboard/android')

include ':capacitor-network'
project(':capacitor-network').projectDir = new File('../../../node_modules/.pnpm/@capacitor+network@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/network/android')

include ':capacitor-preferences'
project(':capacitor-preferences').projectDir = new File('../../../node_modules/.pnpm/@capacitor+preferences@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/preferences/android')

include ':capacitor-privacy-screen'
project(':capacitor-privacy-screen').projectDir = new File('../node_modules/@capacitor/privacy-screen/android')

include ':capacitor-status-bar'
project(':capacitor-status-bar').projectDir = new File('../../../node_modules/.pnpm/@capacitor+status-bar@7.0.1_@capacitor+core@7.2.0/node_modules/@capacitor/status-bar/android')

include ':capawesome-capacitor-live-update'
project(':capawesome-capacitor-live-update').projectDir = new File('../../../node_modules/.pnpm/@capawesome+capacitor-live-update@7.2.0_@capacitor+core@7.2.0/node_modules/@capawesome/capacitor-live-update/android')

include ':capacitor-ble-advertiser'
project(':capacitor-ble-advertiser').projectDir = new File('../../../packages/capacitor-ble-advertiser/android')

CREATE TABLE IF NOT EXISTS "CommissionAccount" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"total_earned" numeric(10, 2) DEFAULT '0' NOT NULL,
	"available_balance" numeric(10, 2) DEFAULT '0' NOT NULL,
	"frozen_balance" numeric(10, 2) DEFAULT '0' NOT NULL,
	"total_withdrawn" numeric(10, 2) DEFAULT '0' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "CommissionAccount_user_id_unique" UNIQUE("user_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "CommissionRecord" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"inviter_id" uuid NOT NULL,
	"invitee_id" uuid NOT NULL,
	"order_id" uuid NOT NULL,
	"commission_amount" numeric(10, 2) NOT NULL,
	"source_type" varchar NOT NULL,
	"source_amount" numeric(10, 2) NOT NULL,
	"commission_rate" numeric(5, 4) NOT NULL,
	"status" varchar DEFAULT 'pending' NOT NULL,
	"settled_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "InviteCode" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"code" varchar(20) NOT NULL,
	"max_uses" integer,
	"used_count" integer DEFAULT 0 NOT NULL,
	"expires_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "InviteCode_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ReferralRelation" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"inviter_id" uuid NOT NULL,
	"invitee_id" uuid NOT NULL,
	"invite_code_id" uuid NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "WithdrawRequest" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"fee_amount" numeric(10, 2) NOT NULL,
	"actual_amount" numeric(10, 2) NOT NULL,
	"status" varchar DEFAULT 'pending' NOT NULL,
	"bank_info" json,
	"admin_note" text,
	"processed_by" uuid,
	"processed_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CommissionAccount" ADD CONSTRAINT "CommissionAccount_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CommissionRecord" ADD CONSTRAINT "CommissionRecord_inviter_id_User_id_fk" FOREIGN KEY ("inviter_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CommissionRecord" ADD CONSTRAINT "CommissionRecord_invitee_id_User_id_fk" FOREIGN KEY ("invitee_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "CommissionRecord" ADD CONSTRAINT "CommissionRecord_order_id_PaymentOrder_id_fk" FOREIGN KEY ("order_id") REFERENCES "public"."PaymentOrder"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "InviteCode" ADD CONSTRAINT "InviteCode_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ReferralRelation" ADD CONSTRAINT "ReferralRelation_inviter_id_User_id_fk" FOREIGN KEY ("inviter_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ReferralRelation" ADD CONSTRAINT "ReferralRelation_invitee_id_User_id_fk" FOREIGN KEY ("invitee_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ReferralRelation" ADD CONSTRAINT "ReferralRelation_invite_code_id_InviteCode_id_fk" FOREIGN KEY ("invite_code_id") REFERENCES "public"."InviteCode"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WithdrawRequest" ADD CONSTRAINT "WithdrawRequest_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "WithdrawRequest" ADD CONSTRAINT "WithdrawRequest_processed_by_User_id_fk" FOREIGN KEY ("processed_by") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_commission_account_user_id" ON "CommissionAccount" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_commission_record_inviter_id" ON "CommissionRecord" USING btree ("inviter_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_commission_record_invitee_id" ON "CommissionRecord" USING btree ("invitee_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_commission_record_order_id" ON "CommissionRecord" USING btree ("order_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_commission_record_status" ON "CommissionRecord" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_commission_record_created_at" ON "CommissionRecord" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_invite_code_user_id" ON "InviteCode" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_invite_code_code" ON "InviteCode" USING btree ("code");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_invite_code_active" ON "InviteCode" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_invite_code_expires_at" ON "InviteCode" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_referral_relation_inviter_id" ON "ReferralRelation" USING btree ("inviter_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_referral_relation_invitee_id" ON "ReferralRelation" USING btree ("invitee_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_referral_relation_invite_code_id" ON "ReferralRelation" USING btree ("invite_code_id");--> statement-breakpoint
CREATE UNIQUE INDEX IF NOT EXISTS "idx_referral_relation_invitee_unique" ON "ReferralRelation" USING btree ("invitee_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_withdraw_request_user_id" ON "WithdrawRequest" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_withdraw_request_status" ON "WithdrawRequest" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_withdraw_request_processed_by" ON "WithdrawRequest" USING btree ("processed_by");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_withdraw_request_created_at" ON "WithdrawRequest" USING btree ("created_at" DESC NULLS LAST);
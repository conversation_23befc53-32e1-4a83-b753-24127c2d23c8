import type { ChatMessage } from './types'
import { ChatDatabaseError } from './types'

/**
 * 消息仓储类
 * 负责消息相关的数据库操作
 */
export class MessageRepository {
  private db: any

  constructor(database: any) {
    this.db = database
  }

  /**
   * 创建消息
   */
  async createMessage(message: Omit<ChatMessage, 'createdAt' | 'updatedAt'>): Promise<ChatMessage> {
    const now = new Date().toISOString()
    const newMessage: ChatMessage = {
      ...message,
      createdAt: now,
      updatedAt: now
    }

    try {
      await this.db.run(
        `INSERT INTO chat_messages (id, chatId, role, content, isStreaming, streamingVersion, createdAt, updatedAt)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          newMessage.id,
          newMessage.chatId,
          newMessage.role,
          newMessage.content,
          newMessage.isStreaming ? 1 : 0,
          newMessage.streamingVersion,
          newMessage.createdAt,
          newMessage.updatedAt
        ]
      )

      console.log(`✅ [MessageRepo] 消息已创建: ${newMessage.id}`)
      return newMessage
    } catch (error) {
      throw new ChatDatabaseError(
        `创建消息失败: ${newMessage.id}`,
        'CREATE_MESSAGE_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取消息
   */
  async getMessage(id: string): Promise<ChatMessage | null> {
    try {
      const result = await this.db.query('SELECT * FROM chat_messages WHERE id = ?', [id])

      if (result.values?.length > 0) {
        const message = result.values[0]
        // 转换布尔值
        message.isStreaming = Boolean(message.isStreaming)
        return message
      }

      return null
    } catch (error) {
      throw new ChatDatabaseError(`获取消息失败: ${id}`, 'GET_MESSAGE_FAILED', error as Error)
    }
  }

  /**
   * 根据聊天ID获取消息
   */
  async getMessagesByChat(chatId: string, limit = 100, offset = 0): Promise<ChatMessage[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM chat_messages WHERE chatId = ? ORDER BY createdAt ASC LIMIT ? OFFSET ?',
        [chatId, limit, offset]
      )

      // 转换布尔值
      return (result.values || []).map((message: any) => ({
        ...message,
        isStreaming: Boolean(message.isStreaming)
      }))
    } catch (error) {
      throw new ChatDatabaseError(
        `获取聊天消息失败: ${chatId}`,
        'GET_MESSAGES_BY_CHAT_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取最新的N条消息
   */
  async getLatestMessages(chatId: string, count: number = 10): Promise<ChatMessage[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM chat_messages WHERE chatId = ? ORDER BY createdAt DESC LIMIT ?',
        [chatId, count]
      )

      // 转换布尔值并恢复时间顺序
      return (result.values || [])
        .map((message: any) => ({
          ...message,
          isStreaming: Boolean(message.isStreaming)
        }))
        .reverse() // 恢复时间顺序（最早的在前）
    } catch (error) {
      throw new ChatDatabaseError(
        `获取最新消息失败: ${chatId}`,
        'GET_LATEST_MESSAGES_FAILED',
        error as Error
      )
    }
  }

  /**
   * 更新消息
   */
  async updateMessage(id: string, updates: Partial<ChatMessage>): Promise<boolean> {
    try {
      const setClause = Object.keys(updates)
        .filter(key => key !== 'id')
        .map(key => `${key} = ?`)
        .join(', ')

      if (!setClause) return false

      const values = Object.entries(updates)
        .filter(([key]) => key !== 'id')
        .map(([key, value]) => {
          // 处理布尔值转换
          if (key === 'isStreaming') {
            return value ? 1 : 0
          }
          return value
        })

      values.push(new Date().toISOString()) // updatedAt
      values.push(id) // WHERE 条件

      const result = await this.db.run(
        `UPDATE chat_messages SET ${setClause}, updatedAt = ? WHERE id = ?`,
        values
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [MessageRepo] 消息已更新: ${id}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(`更新消息失败: ${id}`, 'UPDATE_MESSAGE_FAILED', error as Error)
    }
  }

  /**
   * 更新消息内容
   */
  async updateMessageContent(messageId: string, content: string): Promise<boolean> {
    try {
      const result = await this.db.run(
        'UPDATE chat_messages SET content = ?, updatedAt = ? WHERE id = ?',
        [content, new Date().toISOString(), messageId]
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [MessageRepo] 消息内容已更新: ${messageId}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `更新消息内容失败: ${messageId}`,
        'UPDATE_MESSAGE_CONTENT_FAILED',
        error as Error
      )
    }
  }

  /**
   * 删除消息
   */
  async deleteMessage(id: string): Promise<boolean> {
    try {
      const result = await this.db.run('DELETE FROM chat_messages WHERE id = ?', [id])
      const success = result.changes?.changes > 0
      if (success) {
        console.log(`🗑️ [MessageRepo] 消息已删除: ${id}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(`删除消息失败: ${id}`, 'DELETE_MESSAGE_FAILED', error as Error)
    }
  }

  /**
   * 删除聊天的所有消息
   */
  async deleteMessagesByChat(chatId: string): Promise<number> {
    try {
      const result = await this.db.run('DELETE FROM chat_messages WHERE chatId = ?', [chatId])
      const deletedCount = result.changes?.changes || 0
      console.log(`🗑️ [MessageRepo] 已删除聊天的所有消息: ${chatId}, 删除数量: ${deletedCount}`)
      return deletedCount
    } catch (error) {
      throw new ChatDatabaseError(
        `删除聊天消息失败: ${chatId}`,
        'DELETE_MESSAGES_BY_CHAT_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取消息数量
   */
  async getMessageCount(chatId?: string): Promise<number> {
    try {
      const query = chatId
        ? 'SELECT COUNT(*) as count FROM chat_messages WHERE chatId = ?'
        : 'SELECT COUNT(*) as count FROM chat_messages'

      const params = chatId ? [chatId] : []
      const result = await this.db.query(query, params)

      return result.values?.[0]?.count || 0
    } catch (error) {
      throw new ChatDatabaseError(
        `获取消息数量失败: ${chatId || 'all'}`,
        'GET_MESSAGE_COUNT_FAILED',
        error as Error
      )
    }
  }

  /**
   * 搜索消息
   */
  async searchMessages(
    chatId: string,
    keyword: string,
    limit: number = 50
  ): Promise<ChatMessage[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM chat_messages WHERE chatId = ? AND content LIKE ? ORDER BY createdAt DESC LIMIT ?',
        [chatId, `%${keyword}%`, limit]
      )

      return (result.values || []).map((message: any) => ({
        ...message,
        isStreaming: Boolean(message.isStreaming)
      }))
    } catch (error) {
      throw new ChatDatabaseError(
        `搜索消息失败: ${chatId}, ${keyword}`,
        'SEARCH_MESSAGES_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取流式消息
   */
  async getStreamingMessages(chatId: string): Promise<ChatMessage[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM chat_messages WHERE chatId = ? AND isStreaming = 1 ORDER BY createdAt ASC',
        [chatId]
      )

      return (result.values || []).map((message: any) => ({
        ...message,
        isStreaming: Boolean(message.isStreaming)
      }))
    } catch (error) {
      throw new ChatDatabaseError(
        `获取流式消息失败: ${chatId}`,
        'GET_STREAMING_MESSAGES_FAILED',
        error as Error
      )
    }
  }

  /**
   * 标记流式消息为完成
   */
  async markStreamingComplete(messageId: string): Promise<boolean> {
    try {
      const result = await this.db.run(
        'UPDATE chat_messages SET isStreaming = 0, updatedAt = ? WHERE id = ?',
        [new Date().toISOString(), messageId]
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [MessageRepo] 流式消息已标记完成: ${messageId}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `标记流式消息完成失败: ${messageId}`,
        'MARK_STREAMING_COMPLETE_FAILED',
        error as Error
      )
    }
  }

  /**
   * 批量标记流式消息为完成
   */
  async markAllStreamingComplete(chatId: string): Promise<number> {
    try {
      const result = await this.db.run(
        'UPDATE chat_messages SET isStreaming = 0, updatedAt = ? WHERE chatId = ? AND isStreaming = 1',
        [new Date().toISOString(), chatId]
      )

      const updatedCount = result.changes?.changes || 0
      if (updatedCount > 0) {
        console.log(`✅ [MessageRepo] 批量标记流式消息完成: ${chatId}, 更新数量: ${updatedCount}`)
      }
      return updatedCount
    } catch (error) {
      throw new ChatDatabaseError(
        `批量标记流式消息完成失败: ${chatId}`,
        'MARK_ALL_STREAMING_COMPLETE_FAILED',
        error as Error
      )
    }
  }
}

{"message": {"ai_assistant": "Asistente IA"}, "multimodal_input": {"connected": "Conectado", "stop_device_failed": "Error al detener función del dispositivo", "device_disconnected": "Dispositivo desconectado", "disconnect_device": "Desconectar dispositivo", "recognizing_voice": "Reconociendo voz...", "message_sent": "Mensaje enviado...", "input_message": "Escribe un mensaje...", "stop_generating": "Detener generación", "send_message": "<PERSON><PERSON><PERSON> men<PERSON>", "connect_device": "Conectar dispositivo"}, "image": {"using_local_image": "Usando imagen local:", "localization_success": "Localización de imagen exitosa:", "localization_failed": "Localización fallida:", "checking_attachments": "Verificando archivos adjuntos existentes:", "found_local_image": "Imagen local encontrada:", "found_remote_image": "Imagen remota encontrada, iniciando localización:", "need_generation": "Se necesita generar imagen", "no_image_no_generation": "Sin imagen y sin necesidad de generación", "image_not_found": "Imagen no encontrada", "checking_failed": "Error al verificar archivos adjuntos:", "image_check_failed": "Error al verificar imagen", "checking_image": "Verificando imagen...", "ai_generated_image": "Imagen generada por IA", "click_to_view": "Toca para ver imagen completa", "generation_failed": "Generación fallida", "retry": "Reintentar", "preview": "Vista previa de imagen", "loading": "Cargando...", "load_failed": "Error al cargar imagen", "generated_image": "Imagen generada", "download": "<PERSON><PERSON><PERSON> imagen", "download_failed": "<PERSON><PERSON><PERSON> fallida", "download_error": "<PERSON><PERSON><PERSON> <PERSON>"}, "video": {"save_failed": "Error al guardar en base de datos, pero archivo local guardado:", "localization_failed": "Localización fallida:", "no_video": "Sin video para mostrar", "ai_generated_video": "Video generado por IA", "processing": "Procesando...", "thumbnail": "Miniatura del video", "thumbnail_load_failed": "⚠️ Error al cargar miniatura", "loading": "Cargando video...", "load_failed": "Error al cargar video", "generated_video": "Video generado", "playback": "Reproducción de video", "player_error": "Error del reproductor", "download_failed": "<PERSON><PERSON><PERSON> fallida", "download_error": "<PERSON><PERSON><PERSON> <PERSON>", "download": "Des<PERSON><PERSON> video"}, "voice_recorder": {"get_mic_permission_failed": "Error al obtener permisos del micrófono:", "recording_failed": "Error de grabación", "recording_too_short": "Grabación demasiado corta", "operation_failed": "Operación fallida, inténtalo de nuevo", "click_to_record": "Toca para grabar o mantén presionado para envío rápido", "click_to_start_recording": "Toca para iniciar grabación de voz", "click_to_stop": "Toca para detener grabación", "release_to_cancel": "Suelta para cancelar grabación", "release_to_send": "Suelta para enviar inmediatamente", "processing_audio": "Procesando audio...", "ready": "Listo", "cancel": "<PERSON><PERSON><PERSON>", "need_microphone_permission": "Se requiere permiso de micrófono", "voice_recording_needs_mic": "La función de grabación de voz necesita acceso a tu micrófono", "privacy_protection": "Protección de privacidad", "privacy_description": "No guardamos tus grabaciones, todos los datos de voz solo se usan para conversión a texto", "allow_microphone_access": "Permitir acceso al micrófono"}, "device_control": {"device_control_panel": "Panel de control del dispositivo", "connected": "Conectado", "bluetooth_error": "<PERSON><PERSON><PERSON>", "bluetooth_ready": "Bluetooth listo", "bluetooth_initializing": "Inicializando Bluetooth...", "intensity": "Intensidad", "off": "<PERSON><PERSON><PERSON>", "level": "<PERSON><PERSON>", "classic_mode": "Modo clásico", "disconnect": "Desconectar"}}
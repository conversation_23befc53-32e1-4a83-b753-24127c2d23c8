import type { Message as <PERSON><PERSON>hainMessage } from '@/api/services'
import type { ChatMessage, MessageAttachment, MessageConverter } from './types'

/**
 * 消息转换工具类
 * 负责在LangChain消息格式和数据库消息格式之间进行转换
 */
export class MessageConverterImpl implements MessageConverter {
  /**
   * 将LangChain消息转换为数据库格式
   */
  fromLangChain(
    message: LangChainMessage,
    chatId: string
  ): {
    message: Omit<ChatMessage, 'createdAt' | 'updatedAt'>
    attachments: Omit<MessageAttachment, 'messageId' | 'createdAt' | 'updatedAt'>[]
  } {
    // 🔧 修复：确保消息ID存在，如果不存在则生成一个
    if (!message.id) {
      console.warn('⚠️ [MessageConverter] 消息ID为空，自动生成ID')
      message.id = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    // 转换消息
    const dbMessage: Omit<ChatMessage, 'createdAt' | 'updatedAt'> = {
      id: message.id,
      chatId,
      role: message.role,
      content: message.content,
      isStreaming: false, // 默认不是流式
      streamingVersion: 0 // 默认版本
    }

    // 转换附件
    const dbAttachments: Omit<MessageAttachment, 'messageId' | 'createdAt' | 'updatedAt'>[] = (
      message.attachments || []
    ).map((attachment, index) => {

      // 根据contentType判断媒体类型
      const mediaType = this.getMediaTypeFromContentType(attachment.contentType)

      // 生成附件ID（基于消息ID和索引）
      const attachmentId = `${message.id}_${mediaType}_${index}`

      // 判断附件状态
      const status = this.determineAttachmentStatus(attachment)

      return {
        id: attachmentId,
        type: mediaType,
        name: attachment.name,
        contentType: attachment.contentType,
        originalUrl: attachment.url,
        localPath: undefined, // 初始为空，后续下载时填充
        status,
        fileSize: undefined, // 初始为空，下载时填充
        metadata: JSON.stringify(attachment.metadata || {})
      }
    })

    return {
      message: dbMessage,
      attachments: dbAttachments
    }
  }

  /**
   * 将数据库消息转换为LangChain格式
   */
  toLangChain(message: ChatMessage, attachments: MessageAttachment[]): LangChainMessage {
    // 转换附件
    const langChainAttachments = attachments.map(attachment => ({
      url: attachment.localPath || attachment.originalUrl, // 优先使用本地路径
      name: attachment.name,
      contentType: attachment.contentType,
      metadata: this.safeParseJSON(attachment.metadata)
    }))

    // 构建LangChain消息
    const langChainMessage: LangChainMessage = {
      id: message.id,
      role: message.role,
      content: message.content,
      parts: [
        {
          type: 'text',
          text: message.content
        }
      ],
      attachments: langChainAttachments.length > 0 ? langChainAttachments : undefined,
      createdAt: new Date(message.createdAt)
    }

    return langChainMessage
  }

  /**
   * 根据contentType判断媒体类型
   */
  private getMediaTypeFromContentType(contentType: string): 'audio' | 'image' | 'video' {
    if (contentType.startsWith('audio/')) {
      return 'audio'
    } else if (contentType.startsWith('image/')) {
      return 'image'
    } else if (contentType.startsWith('video/')) {
      return 'video'
    } else {
      // 默认当作图片处理
      console.warn(`🤔 [MessageConverter] 未知的contentType: ${contentType}，默认当作image处理`)
      return 'image'
    }
  }

  /**
   * 判断附件的初始状态
   * 基于URL特征来判断是否为生成中的内容
   */
  private determineAttachmentStatus(attachment: {
    url: string
    contentType: string
  }): 'generating' | 'downloading' | 'completed' | 'failed' {
    // 如果URL包含临时标识或生成标识，则认为是生成中
    if (
      attachment.url.includes('generating') ||
      attachment.url.includes('temp') ||
      attachment.url.includes('placeholder') ||
      attachment.url === '' ||
      attachment.url.startsWith('data:') // 临时的data URL
    ) {
      return 'generating'
    }

    // 如果是正常的HTTP URL，则可以下载
    if (attachment.url.startsWith('http://') || attachment.url.startsWith('https://')) {
      return 'downloading'
    }

    // 如果是本地文件路径，则已完成
    if (attachment.url.startsWith('file://') || attachment.url.startsWith('/')) {
      return 'completed'
    }

    // 默认状态
    return 'downloading'
  }

  /**
   * 安全地解析JSON字符串
   */
  private safeParseJSON(jsonString: string): any {
    try {
      return JSON.parse(jsonString)
    } catch (error) {
      console.warn('🚨 [MessageConverter] JSON解析失败:', error)
      return {}
    }
  }

  /**
   * 批量转换LangChain消息列表
   */
  batchFromLangChain(
    messages: LangChainMessage[],
    chatId: string
  ): Array<{
    message: Omit<ChatMessage, 'createdAt' | 'updatedAt'>
    attachments: Omit<MessageAttachment, 'messageId' | 'createdAt' | 'updatedAt'>[]
  }> {
    return messages.map(message => this.fromLangChain(message, chatId))
  }

  /**
   * 批量转换数据库消息为LangChain格式
   */
  batchToLangChain(
    messagesWithAttachments: Array<{
      message: ChatMessage
      attachments: MessageAttachment[]
    }>
  ): LangChainMessage[] {
    return messagesWithAttachments.map(({ message, attachments }) =>
      this.toLangChain(message, attachments)
    )
  }

  /**
   * 创建会话标题（从第一条用户消息提取）
   */
  generateSessionTitle(firstUserMessage: string): string {
    // 清理消息内容，去除多余的空白字符
    const cleanContent = firstUserMessage.trim().replace(/\s+/g, ' ')

    // 截取前30个字符作为标题
    if (cleanContent.length <= 30) {
      return cleanContent
    }

    // 在30字符内找最后一个完整的词
    const truncated = cleanContent.substring(0, 30)
    const lastSpaceIndex = truncated.lastIndexOf(' ')

    if (lastSpaceIndex > 15) {
      // 确保标题不会太短
      return truncated.substring(0, lastSpaceIndex) + '...'
    }

    return truncated + '...'
  }

  /**
   * 检查消息是否包含流式更新
   * 更保守的判断逻辑，避免误判
   */
  isStreamingMessage(message: LangChainMessage): boolean {
    // 只有assistant消息才可能是流式的
    if (message.role !== 'assistant') {
      return false
    }

    // 🎯 更准确的流式判断：
    // 1. 内容完全为空 - 刚开始流式
    // 2. 内容长度很短且看起来不完整 - 流式进行中
    // 3. 🚀 新增：检查消息是否明显不完整（没有结束标点）

    const content = message.content.trim()

    // 空内容认为是流式开始
    if (content.length === 0) {
      return true
    }

    // 内容很短且明显不完整（比如只有几个字符，没有标点）
    if (content.length < 5 && !/[。！？.,!?]/.test(content)) {
      return true
    }

    // 🚀 新增：检查内容是否以不完整的标签结尾（如 "<scene>正在生成"）
    if (/<[^>]*$/.test(content) || /<\w+>[^<]*$/.test(content)) {
      return true
    }

    // 🚀 新增：检查是否以不完整的句子结尾（没有标点且不是完整表达）
    if (
      content.length < 20 &&
      !/[。！？.,!?]$/.test(content) &&
      !/\s(了|的|是|在|会|要|能|可以|应该)$/.test(content)
    ) {
      return true
    }

    // 其他情况认为是完整消息
    return false
  }

  /**
   * 计算消息内容的哈希值（用于检测变化）
   */
  calculateMessageHash(message: LangChainMessage): string {
    const content = JSON.stringify({
      content: message.content,
      attachments: message.attachments?.map(att => ({
        url: att.url,
        name: att.name,
        contentType: att.contentType
      }))
    })

    // 简单的哈希算法（生产环境可以使用更好的）
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转换为32位整数
    }

    return hash.toString(36)
  }
}

// 创建单例实例
export const messageConverter = new MessageConverterImpl()

// 导出常用的转换函数
export const convertFromLangChain = (message: LangChainMessage, chatId: string) =>
  messageConverter.fromLangChain(message, chatId)

export const convertToLangChain = (message: ChatMessage, attachments: MessageAttachment[]) =>
  messageConverter.toLangChain(message, attachments)

export const generateSessionTitle = (firstUserMessage: string) =>
  messageConverter.generateSessionTitle(firstUserMessage)

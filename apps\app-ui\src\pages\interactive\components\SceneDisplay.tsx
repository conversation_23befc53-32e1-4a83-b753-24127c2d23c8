import React, { useState, useEffect, useCallback, useRef } from 'react'
import useEmblaCarousel from 'embla-carousel-react'
import emblaFade from 'embla-carousel-fade'
import { Button } from '@heroui/react'
import { Icon } from '@iconify/react'
import type { ScenePic } from '../types'
import { usePlayer } from '../context/PlayerContext'
import { PlayerState } from '../types'
import { NetworkUtils } from '../../../utils/networkUtils'

interface SceneDisplayProps {
  scenePics?: ScenePic[]
  interval?: number // 图片轮播间隔，单位毫秒（仅对图片有效）
}

/**
 * 场景展示组件
 * 全屏显示当前场景图片/视频，支持轮播
 * 图片：按固定间隔轮播
 * 视频：循环播放，除非有下一个视频才切换
 * 视频播放状态跟随音频播放状态
 */
export const SceneDisplay: React.FC<SceneDisplayProps> = ({ scenePics = [], interval = 8000 }) => {
  // 获取播放器状态
  const { playerState } = usePlayer()

  // Embla Carousel 配置 - 完全禁用拖拽
  const options = {
    loop: true,
    draggable: false,
    dragFree: false
  }

  const [emblaRef, emblaApi] = useEmblaCarousel(options, [emblaFade()])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const [fadeOpacity, setFadeOpacity] = useState(1)
  const videoRefs = useRef<(HTMLVideoElement | null)[]>([])
  const autoplayTimerRef = useRef<NodeJS.Timeout | null>(null)
  const [videoDurations, setVideoDurations] = useState<{ [index: number]: number }>({})
  const [userInteracted, setUserInteracted] = useState(false)

  // 检测用户交互
  useEffect(() => {
    const handleUserInteraction = () => {
      setUserInteracted(true)
    }

    // 监听各种用户交互事件
    const events = ['click', 'touchstart', 'keydown']
    events.forEach(event => {
      document.addEventListener(event, handleUserInteraction, { once: true })
    })

    return () => {
      events.forEach(event => {
        document.removeEventListener(event, handleUserInteraction)
      })
    }
  }, [])

  // 获取媒体类型的函数
  const getMediaType = useCallback((url: string, declaredType?: string): 'video' | 'image' => {
    // 添加url参数的安全检查，防止toLowerCase调用失败
    if (!url || typeof url !== 'string') {
      console.warn('getMediaType收到无效的url参数:', url)
      return 'image' // 默认返回图片类型
    }

    // 如果明确声明了类型，优先使用
    if (declaredType === 'video' || declaredType === 'image') {
      return declaredType
    }

    // 基于文件扩展名判断
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.avi', '.mov', '.wmv', '.flv', '.mkv']
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg', '.bmp']

    const urlLower = url.toLowerCase()

    if (videoExtensions.some(ext => urlLower.includes(ext))) {
      return 'video'
    }

    if (imageExtensions.some(ext => urlLower.includes(ext))) {
      return 'image'
    }

    // 默认返回图片类型
    return 'image'
  }, [])

  // 获取当前媒体项和类型
  const currentMedia = scenePics[selectedIndex]
  const currentMediaType = currentMedia
    ? getMediaType(currentMedia.pic, currentMedia.type)
    : 'image'
  const isCurrentVideo = currentMediaType === 'video'

  // 清理定时器函数（提前定义，避免依赖顺序问题）
  const clearAutoplayTimer = useCallback(() => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current)
      autoplayTimerRef.current = null
    }
  }, [])

  // 检查是否有下一个视频
  const hasNextVideo = useCallback(() => {
    if (scenePics.length <= 1) return false

    // 检查后续的场景中是否有视频
    for (let i = selectedIndex + 1; i < scenePics.length; i++) {
      const mediaType = getMediaType(scenePics[i].pic, scenePics[i].type)
      if (mediaType === 'video') {
        return true
      }
    }

    // 如果是循环的，检查从头开始的场景
    if (scenePics.length > 1) {
      for (let i = 0; i < selectedIndex; i++) {
        const mediaType = getMediaType(scenePics[i].pic, scenePics[i].type)
        if (mediaType === 'video') {
          return true
        }
      }
    }

    return false
  }, [scenePics, selectedIndex, getMediaType])

  // 安全播放视频
  const safePlayVideo = useCallback(
    async (video: HTMLVideoElement, index: number) => {
      try {
        // 确保视频已加载
        if (video.readyState < 2) {
          await new Promise(resolve => {
            const handleCanPlay = () => {
              video.removeEventListener('canplay', handleCanPlay)
              resolve(void 0)
            }
            video.addEventListener('canplay', handleCanPlay)

            // 超时保护
            setTimeout(() => {
              video.removeEventListener('canplay', handleCanPlay)
              resolve(void 0)
            }, 2000)
          })
        }

        // 只有在音频播放时才播放视频
        if (playerState === PlayerState.PLAYING) {
          await video.play()
        }
      } catch (error) {
        console.warn(`视频 ${index} 播放失败:`, error)
      }
    },
    [playerState]
  )

  // 监听音频播放状态，同步视频播放
  useEffect(() => {
    const currentVideo = videoRefs.current[selectedIndex]
    if (!currentVideo || !isCurrentVideo) return

    if (playerState === PlayerState.PLAYING) {
      // 音频播放时，播放视频
      if (currentVideo.paused) {
        safePlayVideo(currentVideo, selectedIndex)
      }
    } else {
      // 音频暂停时，暂停视频并清除自动轮播
      if (!currentVideo.paused) {
        currentVideo.pause()
      }
      // 确保清除自动轮播定时器
      clearAutoplayTimer()
    }
  }, [playerState, selectedIndex, isCurrentVideo, safePlayVideo, clearAutoplayTimer])

  // 视频加载完成，获取时长
  const handleVideoLoadedMetadata = useCallback((index: number, video: HTMLVideoElement) => {
    if (video.duration && !isNaN(video.duration) && isFinite(video.duration)) {
      setVideoDurations(prev => ({
        ...prev,
        [index]: video.duration
      }))
    }
  }, [])

  // 跟踪当前滑块索引
  const onSelect = useCallback(() => {
    if (!emblaApi) return
    const newIndex = emblaApi.selectedScrollSnap()
    setSelectedIndex(newIndex)

    // 停止所有视频播放
    videoRefs.current.forEach((video, index) => {
      if (video && index !== newIndex) {
        video.pause()
        video.currentTime = 0
      }
    })

    // 如果当前是视频且音频在播放，尝试播放视频
    const currentVideo = videoRefs.current[newIndex]
    const mediaType = scenePics[newIndex]
      ? getMediaType(scenePics[newIndex].pic, scenePics[newIndex].type)
      : 'image'
    if (currentVideo && mediaType === 'video' && playerState === PlayerState.PLAYING) {
      // 延迟一下再播放，确保切换动画完成
      setTimeout(() => {
        safePlayVideo(currentVideo, newIndex)
      }, 50)
    }
  }, [emblaApi, scenePics, getMediaType, safePlayVideo, playerState])

  // 初始化 Embla 事件监听
  useEffect(() => {
    if (!emblaApi) return

    onSelect()
    emblaApi.on('select', onSelect)
    emblaApi.on('reInit', onSelect)

    return () => {
      emblaApi.off('select', onSelect)
      emblaApi.off('reInit', onSelect)
    }
  }, [emblaApi, onSelect])

  // 切换到下一个媒体
  const goToNext = useCallback(async () => {
    if (!emblaApi || scenePics.length <= 1) return

    // 更自然的淡出效果
    setFadeOpacity(0)

    // 延长过渡时间，让切换更自然
    await new Promise(resolve => setTimeout(resolve, 800))
    emblaApi.scrollNext()

    // 缓慢淡入效果
    setFadeOpacity(1)
  }, [emblaApi, scenePics.length])

  // 自动轮播逻辑 - 只有在音频播放时才进行轮播
  useEffect(() => {
    if (!emblaApi || scenePics.length <= 1) return

    clearAutoplayTimer()

    // 只有在音频播放时才启动自动轮播
    if (playerState === PlayerState.PLAYING) {
      // 如果当前是图片，使用固定间隔轮播
      if (!isCurrentVideo) {
        autoplayTimerRef.current = setInterval(goToNext, interval)
      }
      // 如果是视频，视频会循环播放，不需要自动切换
    }

    return clearAutoplayTimer
  }, [
    emblaApi,
    interval,
    scenePics.length,
    isCurrentVideo,
    goToNext,
    clearAutoplayTimer,
    playerState
  ])

  // 视频播放完成处理
  const handleVideoEnded = useCallback(
    (index: number) => {
      // 只有在音频播放时才处理视频结束事件
      if (playerState !== PlayerState.PLAYING) {
        return
      }

      // 检查是否有下一个视频
      if (hasNextVideo()) {
        // 如果有下一个视频，切换到下一个场景
        goToNext()
      } else {
        // 如果没有下一个视频，重新播放当前视频（循环）
        const currentVideo = videoRefs.current[index]
        if (currentVideo) {
          currentVideo.currentTime = 0
          currentVideo.play().catch(console.error)
        }
      }
    },
    [goToNext, hasNextVideo, playerState]
  )

  // 手动切换到指定场景
  const scrollTo = useCallback(
    async (index: number) => {
      if (!emblaApi || index === selectedIndex) return

      clearAutoplayTimer()

      // 更自然的淡出效果
      setFadeOpacity(0)

      // 延长过渡时间，让手动切换也更自然
      await new Promise(resolve => setTimeout(resolve, 600))
      emblaApi.scrollTo(index)

      // 缓慢淡入效果
      setFadeOpacity(1)
    },
    [emblaApi, selectedIndex, clearAutoplayTimer]
  )

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearAutoplayTimer()
    }
  }, [clearAutoplayTimer])

  // 如果没有场景图片，显示默认背景
  if (scenePics.length === 0) {
    return (
      <div className="absolute inset-0 bg-gray-900 flex items-center justify-center">
        <p className="text-gray-500">暂无场景</p>
      </div>
    )
  }

  return (
    <div className="absolute inset-0 overflow-hidden">
      {/* Embla Carousel 容器 */}
      <div
        className="absolute inset-0"
        ref={emblaRef}
        style={{ touchAction: 'none' }}
        onTouchStart={e => e.preventDefault()}
        onTouchMove={e => e.preventDefault()}
      >
        <div className="flex h-full">
          {scenePics.map((pic, index) => {
            const mediaType = getMediaType(pic.pic, pic.type)
            // 判断是否应该循环播放：如果是视频且没有下一个视频，则循环
            const shouldLoop = mediaType === 'video' && !hasNextVideo()

            return (
              <div key={`scene-${pic.name}-${index}`} className="relative flex-[0_0_100%] h-full">
                <div
                  className="absolute inset-0 transition-opacity duration-1000 ease-in-out"
                  style={{ opacity: fadeOpacity }}
                >
                  {mediaType === 'video' ? (
                    <video
                      ref={el => {
                        videoRefs.current[index] = el
                      }}
                      src={pic.pic}
                      className="absolute inset-0 w-full h-full object-cover"
                      muted
                      playsInline
                      preload="metadata"
                      loop={shouldLoop}
                      crossOrigin={NetworkUtils.isCapacitor() ? undefined : 'anonymous'}
                      onLoadedMetadata={e => {
                        const video = e.currentTarget
                        handleVideoLoadedMetadata(index, video)
                      }}
                      onEnded={() => handleVideoEnded(index)}
                      onError={e => {
                        console.error('视频加载失败:', pic.pic, e)
                      }}
                    />
                  ) : (
                    <img
                      src={pic.pic}
                      alt={pic.name}
                      className="absolute inset-0 w-full h-full object-cover"
                      loading={index === 0 ? 'eager' : 'lazy'}
                      crossOrigin={NetworkUtils.isCapacitor() ? undefined : 'anonymous'}
                      onError={e => {
                        console.error('图片显示失败:', pic.pic, e)
                        // 简单的CORS错误处理：尝试移除crossOrigin重新加载
                        const img = e.currentTarget as HTMLImageElement
                        if (img.crossOrigin) {
                          console.log('尝试移除crossOrigin重新加载图片:', pic.pic)
                          img.crossOrigin = null
                          // 触发重新加载
                          const originalSrc = img.src
                          img.src = ''
                          setTimeout(() => {
                            img.src = originalSrc
                          }, 100)
                        }
                      }}
                    />
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* 黑色渐变覆盖层，确保文字可读性 */}
      <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/90 pointer-events-none" />

      {/* 小圆点指示器 */}
      {scenePics.length > 1 && (
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 flex gap-2 z-10">
          {scenePics.map((pic, index) => {
            const mediaType = getMediaType(pic.pic, pic.type)
            const isActive = index === selectedIndex

            return (
              <Button
                key={`dot-${pic.name}-${index}`}
                isIconOnly
                size="sm"
                variant={isActive ? 'solid' : 'flat'}
                color={isActive ? 'primary' : 'default'}
                className={`min-w-unit-8 h-unit-8 rounded-full transition-all ${
                  isActive ? 'scale-125' : ''
                }`}
                onPress={() => scrollTo(index)}
                aria-label={`切换到场景${index + 1}${
                  mediaType === 'video'
                    ? ` (视频${
                        videoDurations[index] ? ` ${Math.round(videoDurations[index])}s` : ''
                      })`
                    : ''
                }`}
              >
                {mediaType === 'video' && (
                  <Icon
                    icon="solar:play-circle-bold"
                    width={12}
                    className={isActive ? 'text-white' : 'text-danger'}
                  />
                )}
              </Button>
            )
          })}
        </div>
      )}
    </div>
  )
}

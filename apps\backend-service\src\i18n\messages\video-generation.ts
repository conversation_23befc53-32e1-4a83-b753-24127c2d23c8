// 视频生成相关消息
export const videoGenerationMessages = {
  zh: {
    // 基础错误消息
    'video-generation.user_not_logged_in': '用户未登录',
    'video-generation.user_not_exist': '用户不存在',
    'video-generation.insufficient_points': '积分不足',
    'video-generation.generation_failed': '多模态视频生成失败',
    'video-generation.unknown_error': '未知错误',
    'video-generation.missing_message_id': '缺少 messageId 参数',
    'video-generation.message_not_exist': '消息不存在',
    'video-generation.query_status_failed': '查询状态失败',

    // 成功消息
    'video-generation.task_started': '多模态视频生成任务已开始，请稍候...',
    'video-generation.video_completed': '视频生成完成',
    'video-generation.generating_video': '正在生成视频...',
    'video-generation.waiting_to_start': '等待开始生成',

    // 验证错误消息
    'video-generation.prompt_required': '提示词不能为空',
    'video-generation.prompt_too_long': '提示词过长',
    'video-generation.invalid_avatar_url': '角色头像URL格式不正确',
    'video-generation.invalid_chat_id': '聊天ID格式不正确',
    'video-generation.invalid_message_id': '消息ID格式不正确'
  },
  'zh-TW': {
    // 基礎錯誤訊息
    'video-generation.user_not_logged_in': '使用者未登入',
    'video-generation.user_not_exist': '使用者不存在',
    'video-generation.insufficient_points': '積分不足',
    'video-generation.generation_failed': '多模態影片產生失敗',
    'video-generation.unknown_error': '未知錯誤',
    'video-generation.missing_message_id': '缺少 messageId 參數',
    'video-generation.message_not_exist': '訊息不存在',
    'video-generation.query_status_failed': '查詢狀態失敗',

    // 成功訊息
    'video-generation.task_started': '多模態影片產生任務已開始，請稍候...',
    'video-generation.video_completed': '影片產生完成',
    'video-generation.generating_video': '正在產生影片...',
    'video-generation.waiting_to_start': '等待開始產生',

    // 驗證錯誤訊息
    'video-generation.prompt_required': '提示詞不能為空',
    'video-generation.prompt_too_long': '提示詞過長',
    'video-generation.invalid_avatar_url': '角色頭像URL格式不正確',
    'video-generation.invalid_chat_id': '聊天ID格式不正確',
    'video-generation.invalid_message_id': '訊息ID格式不正確'
  },
  ja: {
    // 基本エラーメッセージ
    'video-generation.user_not_logged_in': 'ユーザーがログインしていません',
    'video-generation.user_not_exist': 'ユーザーが存在しません',
    'video-generation.insufficient_points': 'ポイントが不足しています',
    'video-generation.generation_failed': 'マルチモーダル動画生成に失敗しました',
    'video-generation.unknown_error': '不明なエラーです',
    'video-generation.missing_message_id': 'messageIdパラメータが不足しています',
    'video-generation.message_not_exist': 'メッセージが存在しません',
    'video-generation.query_status_failed': 'ステータスの照会に失敗しました',

    // 成功メッセージ
    'video-generation.task_started': 'マルチモーダル動画生成タスクが開始されました。しばらくお待ちください...',
    'video-generation.video_completed': '動画生成が完了しました',
    'video-generation.generating_video': '動画を生成中...',
    'video-generation.waiting_to_start': '生成開始を待っています',

    // 検証エラーメッセージ
    'video-generation.prompt_required': 'プロンプトを入力してください',
    'video-generation.prompt_too_long': 'プロンプトが長すぎます',
    'video-generation.invalid_avatar_url': 'キャラクターアバターのURL形式が正しくありません',
    'video-generation.invalid_chat_id': 'チャットIDの形式が正しくありません',
    'video-generation.invalid_message_id': 'メッセージIDの形式が正しくありません'
  },
  es: {
    // Mensajes de error básicos
    'video-generation.user_not_logged_in': 'El usuario no ha iniciado sesión',
    'video-generation.user_not_exist': 'El usuario no existe',
    'video-generation.insufficient_points': 'Puntos insuficientes',
    'video-generation.generation_failed': 'Error en la generación de video multimodal',
    'video-generation.unknown_error': 'Error desconocido',
    'video-generation.missing_message_id': 'Falta el parámetro messageId',
    'video-generation.message_not_exist': 'El mensaje no existe',
    'video-generation.query_status_failed': 'Error al consultar el estado',

    // Mensajes de éxito
    'video-generation.task_started': 'La tarea de generación de video multimodal ha comenzado, por favor espere...',
    'video-generation.video_completed': 'Generación de video completada',
    'video-generation.generating_video': 'Generando video...',
    'video-generation.waiting_to_start': 'Esperando para comenzar la generación',

    // Mensajes de error de validación
    'video-generation.prompt_required': 'El prompt no puede estar vacío',
    'video-generation.prompt_too_long': 'El prompt es demasiado largo',
    'video-generation.invalid_avatar_url': 'El formato de la URL del avatar del personaje es incorrecto',
    'video-generation.invalid_chat_id': 'El formato del ID del chat es incorrecto',
    'video-generation.invalid_message_id': 'El formato del ID del mensaje es incorrecto'
  },
  en: {
    // Basic error messages
    'video-generation.user_not_logged_in': 'User not logged in',
    'video-generation.user_not_exist': 'User does not exist',
    'video-generation.insufficient_points': 'Insufficient points',
    'video-generation.generation_failed': 'Multimodal video generation failed',
    'video-generation.unknown_error': 'Unknown error',
    'video-generation.missing_message_id': 'Missing messageId parameter',
    'video-generation.message_not_exist': 'Message does not exist',
    'video-generation.query_status_failed': 'Failed to query status',

    // Success messages
    'video-generation.task_started': 'Video generation has started, please wait...',
    'video-generation.video_completed': 'Video generation completed',
    'video-generation.generating_video': 'Generating video...',
    'video-generation.waiting_to_start': 'Waiting to start generation',

    // Validation error messages
    'video-generation.prompt_required': 'Prompt cannot be empty',
    'video-generation.prompt_too_long': 'Prompt is too long',
    'video-generation.invalid_avatar_url': 'Invalid character avatar URL format',
    'video-generation.invalid_chat_id': 'Invalid chat ID format',
    'video-generation.invalid_message_id': 'Invalid message ID format'
  }
}

// 账号管理器 - 使用 Cloudflare KV 存储
import type { Env, ElevenLabsAccount, AccountHealth } from './types'

// 硬编码账号列表 - 在这里配置你的 ElevenLabs 账号
const HARDCODED_ACCOUNTS = [
  {
    email: '<EMAIL>',
    password: 'fkj2vrb-YZA*fvf9rua'
  }
  // {
  //   email: '<EMAIL>',
  //   password: 'your-password2'
  // }
  // 添加更多账号...
]

export class AccountManager {
  private env: Env
  private readonly KV_PREFIX = 'elevenlabs_worker_v3' // 独特的前缀避免冲突

  constructor(env: Env) {
    this.env = env
  }

  /**
   * 获取下一个可用账号（轮询策略）
   */
  async getNextAvailableAccount(): Promise<ElevenLabsAccount | null> {
    console.log('AccountManager: 开始获取所有账号...')
    const accounts = await this.getAllAccounts()
    console.log('AccountManager: 获取到账号数量:', accounts.length)

    if (accounts.length === 0) {
      console.log('AccountManager: 没有找到任何账号，初始化硬编码账号...')
      await this.initializeHardcodedAccounts()
      return await this.getNextAvailableAccount()
    }

    // 过滤活跃账号
    const activeAccounts = accounts.filter(acc => acc.isActive && acc.failureCount < 3)

    if (activeAccounts.length === 0) {
      // 重置失败计数，给账号第二次机会
      await this.resetFailureCounts()
      return accounts.find(acc => acc.isActive) || null
    }

    // 按最后使用时间排序，选择最久未使用的
    activeAccounts.sort((a, b) => {
      const aTime = a.lastUsed?.getTime() || 0
      const bTime = b.lastUsed?.getTime() || 0
      return aTime - bTime
    })

    const selectedAccount = activeAccounts[0]

    // 更新最后使用时间
    await this.updateAccountUsage(selectedAccount.id)

    return selectedAccount
  }

  /**
   * 初始化硬编码账号列表
   */
  private async initializeHardcodedAccounts(): Promise<void> {
    console.log('AccountManager: 初始化硬编码账号列表...')

    for (const accountData of HARDCODED_ACCOUNTS) {
      const newAccount: ElevenLabsAccount = {
        id: this.generateAccountId(),
        email: accountData.email,
        password: accountData.password,
        isActive: true,
        failureCount: 0
      }
      await this.saveAccount(newAccount)
    }

    console.log(`AccountManager: 已初始化 ${HARDCODED_ACCOUNTS.length} 个账号`)
  }

  /**
   * 获取所有账号
   */
  async getAllAccounts(): Promise<ElevenLabsAccount[]> {
    try {
      console.log('AccountManager: 从KV读取账号数据...')
      const accountsJson = await this.env.ELEVENLABS_CACHE?.get(`${this.KV_PREFIX}:accounts`)
      console.log('AccountManager: KV数据:', accountsJson ? '有数据' : '无数据')

      if (!accountsJson) {
        console.log('AccountManager: KV中没有账号数据')
        return []
      }

      const accounts = JSON.parse(accountsJson) as ElevenLabsAccount[]
      console.log('AccountManager: 解析到账号数量:', accounts.length)

      // 转换日期字符串为 Date 对象
      const processedAccounts = accounts.map(acc => ({
        ...acc,
        tokenExpiry: acc.tokenExpiry ? new Date(acc.tokenExpiry) : undefined,
        lastUsed: acc.lastUsed ? new Date(acc.lastUsed) : undefined
      }))

      console.log(
        'AccountManager: 处理后的账号:',
        processedAccounts.map(acc => ({
          id: acc.id,
          email: acc.email,
          isActive: acc.isActive,
          failureCount: acc.failureCount
        }))
      )
      return processedAccounts
    } catch (error) {
      console.error('获取账号列表失败:', error)
      return []
    }
  }

  /**
   * 保存账号信息
   */
  async saveAccount(account: ElevenLabsAccount): Promise<void> {
    const accounts = await this.getAllAccounts()
    const existingIndex = accounts.findIndex(acc => acc.id === account.id)

    if (existingIndex >= 0) {
      accounts[existingIndex] = account
    } else {
      accounts.push(account)
    }

    await this.env.ELEVENLABS_CACHE?.put(`${this.KV_PREFIX}:accounts`, JSON.stringify(accounts))
  }

  /**
   * 更新账号会话信息
   */
  async updateAccountSession(
    accountId: string,
    sessionToken: string,
    refreshToken?: string,
    expiry?: Date
  ): Promise<void> {
    const accounts = await this.getAllAccounts()
    const account = accounts.find(acc => acc.id === accountId)

    if (account) {
      account.sessionToken = sessionToken
      account.refreshToken = refreshToken
      account.tokenExpiry = expiry
      account.failureCount = 0 // 重置失败计数
      await this.saveAccount(account)
    }
  }

  /**
   * 标记账号失败
   */
  async markAccountFailed(accountId: string, error?: string): Promise<void> {
    const accounts = await this.getAllAccounts()
    const account = accounts.find(acc => acc.id === accountId)

    if (account) {
      account.failureCount += 1
      account.isActive = account.failureCount < 5 // 5次失败后禁用

      // 保存健康检查信息
      await this.saveAccountHealth(accountId, {
        accountId,
        isHealthy: false,
        lastCheck: new Date(),
        errorCount: account.failureCount,
        lastError: error
      })

      await this.saveAccount(account)
    }
  }

  /**
   * 更新账号使用时间
   */
  async updateAccountUsage(accountId: string): Promise<void> {
    const accounts = await this.getAllAccounts()
    const account = accounts.find(acc => acc.id === accountId)

    if (account) {
      account.lastUsed = new Date()
      await this.saveAccount(account)
    }
  }

  /**
   * 重置所有账号的失败计数
   */
  async resetFailureCounts(): Promise<void> {
    const accounts = await this.getAllAccounts()

    for (const account of accounts) {
      account.failureCount = 0
      account.isActive = true
    }

    await this.env.ELEVENLABS_CACHE?.put(`${this.KV_PREFIX}:accounts`, JSON.stringify(accounts))
  }

  /**
   * 检查 token 是否即将过期
   */
  isTokenExpiringSoon(account: ElevenLabsAccount, thresholdMs: number = 5 * 60 * 1000): boolean {
    if (!account.tokenExpiry) {
      return true // 没有过期时间，认为需要刷新
    }

    const now = Date.now()
    const expiry = account.tokenExpiry.getTime()

    return expiry - now < thresholdMs
  }

  /**
   * 保存账号健康状态
   */
  private async saveAccountHealth(accountId: string, health: AccountHealth): Promise<void> {
    await this.env.ELEVENLABS_CACHE?.put(
      `${this.KV_PREFIX}:health:${accountId}`,
      JSON.stringify(health)
    )
  }

  /**
   * 获取账号健康状态
   */
  async getAccountHealth(accountId: string): Promise<AccountHealth | null> {
    try {
      const healthJson = await this.env.ELEVENLABS_CACHE?.get(
        `${this.KV_PREFIX}:health:${accountId}`
      )
      if (!healthJson) {
        return null
      }

      const health = JSON.parse(healthJson) as AccountHealth
      return {
        ...health,
        lastCheck: new Date(health.lastCheck)
      }
    } catch (error) {
      console.error('获取账号健康状态失败:', error)
      return null
    }
  }

  /**
   * 获取账号统计信息
   */
  async getAccountStats(): Promise<{
    total: number
    active: number
    failed: number
    healthy: number
  }> {
    const accounts = await this.getAllAccounts()
    const total = accounts.length
    const active = accounts.filter(acc => acc.isActive).length
    const failed = accounts.filter(acc => !acc.isActive).length

    // 计算健康账号数量
    let healthy = 0
    for (const account of accounts) {
      const health = await this.getAccountHealth(account.id)
      if (health && health.isHealthy) {
        healthy++
      }
    }

    return { total, active, failed, healthy }
  }

  /**
   * 生成账号ID
   */
  private generateAccountId(): string {
    return 'acc_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now().toString(36)
  }
}

import { useState, useRef, useEffect } from 'react'
import { Link, useNavigate } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { useTranslation } from 'react-i18next'

// HeroUI 组件
import { Input, Button, addToast } from '@heroui/react'

// Lucide 图标
import { Eye, EyeOff, Mail, ShieldCheck } from 'lucide-react'

// 缓存邮箱的 localStorage 键名
const CACHED_EMAIL_KEY = 'cached_login_email'

// 统一邮箱清理函数，先 JSON.stringify 再去除所有空白和不可见字符
const cleanEmail = (raw: string) => {
  // 先转字符串，再去除所有空白和不可见字符
  const str = typeof raw === 'string' ? raw : String(raw)
  // JSON.stringify 主要用于调试，实际清理用 replace
  return str.replace(/["]+/g, '').replace(/[\s\u200B-\u200D\uFEFF]/g, '')
}

// 从缓存中获取邮箱
const getCachedEmail = () => {
  try {
    return cleanEmail(localStorage.getItem(CACHED_EMAIL_KEY) || '')
  } catch {
    return ''
  }
}

// 缓存邮箱
const cacheEmail = (email: string) => {
  try {
    const cleaned = cleanEmail(email)
    if (cleaned) {
      localStorage.setItem(CACHED_EMAIL_KEY, cleaned)
    }
  } catch {
    // 忽略存储错误
  }
}

export default function LoginPage() {
  const navigate = useNavigate()
  const { login, loginWithCode, sendLoginCode, status } = useAuth()
  const { t } = useTranslation('login')

  // 初始化时清理
  const [email, setEmail] = useState(getCachedEmail() || cleanEmail('<EMAIL>'))
  const [password, setPassword] = useState('123456')
  const [verificationCode, setVerificationCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isPasswordVisible, setIsPasswordVisible] = useState(false)
  const [loginMethod, setLoginMethod] = useState<'password' | 'code'>('code')
  const [countdown, setCountdown] = useState(0)
  const [codeSent, setCodeSent] = useState(false) // 标记是否已发送验证码
  const hasRedirected = useRef(false)
  const [isPasswordFocused, setIsPasswordFocused] = useState(false) // 新增状态

  // 倒计时效果
  useEffect(() => {
    let timer: NodeJS.Timeout
    if (countdown > 0) {
      timer = setTimeout(() => setCountdown(countdown - 1), 1000)
    }
    return () => clearTimeout(timer)
  }, [countdown])

  // 检查用户是否已经登录，如果已登录则重定向到首页
  useEffect(() => {
    console.log('登录页面-当前认证状态:', status)
    if (status === 'authenticated' && !hasRedirected.current) {
      console.log('用户已登录，重定向到首页')
      hasRedirected.current = true
      navigate('/')
    }
  }, [status, navigate])

  // 密码登录
  const handlePasswordLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email || !password) {
      addToast({
        title: t('input_email_password'),
        color: 'warning'
      })
      return
    }

    try {
      setIsLoading(true)

      const success = await login(email, password)

      if (success && !hasRedirected.current) {
        hasRedirected.current = true
        // 缓存成功登录的邮箱
        cacheEmail(email)
        setTimeout(() => {
          navigate('/')
        }, 500)
      } else {
        // 优先展示 useAuth 里的 error
        addToast({
          title:
            typeof status === 'string' && status !== 'authenticated' && status !== 'unauthenticated'
              ? status
              : t('login_failed'),
          color: 'danger'
        })
      }
    } catch (error: any) {
      let msg = t('login_error')
      if (error?.issues?.[0]?.message) {
        msg = error.issues[0].message
      } else if (typeof error === 'string') {
        msg = error
      } else if (error?.message) {
        msg = error.message
      } else if (error?.error?.issues?.[0]?.message) {
        msg = error.error.issues[0].message
      }
      addToast({
        title: msg,
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 发送验证码
  const handleSendCode = async () => {
    if (!email) {
      addToast({
        title: t('input_email'),
        color: 'warning'
      })
      return
    }

    try {
      setIsLoading(true)
      const success = await sendLoginCode(email)

      if (success) {
        addToast({
          title: t('code_sent'),
          color: 'success'
        })
        setCountdown(60)
        setCodeSent(true)
      } else {
        addToast({
          title: t('send_code_failed'),
          color: 'danger'
        })
      }
    } catch (error: any) {
      let msg = t('send_code_error')
      if (error?.issues?.[0]?.message) {
        msg = error.issues[0].message
      } else if (typeof error === 'string') {
        msg = error
      } else if (error?.message) {
        msg = error.message
      } else if (error?.error?.issues?.[0]?.message) {
        msg = error.error.issues[0].message
      }
      addToast({
        title: msg,
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 验证码登录
  const handleCodeLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email || !verificationCode) {
      addToast({
        title: t('input_email_code'),
        color: 'warning'
      })
      return
    }

    if (verificationCode.length < 6) {
      addToast({
        title: t('input_code'),
        color: 'warning'
      })
      return
    }

    try {
      setIsLoading(true)
      const success = await loginWithCode(email, verificationCode)

      if (success && !hasRedirected.current) {
        hasRedirected.current = true
        // 缓存成功登录的邮箱
        cacheEmail(email)
        setTimeout(() => {
          navigate('/')
        }, 500)
      } else {
        addToast({
          title: t('code_error'),
          color: 'danger'
        })
      }
    } catch (error: any) {
      let msg = t('login_error')
      if (error?.issues?.[0]?.message) {
        msg = error.issues[0].message
      } else if (typeof error === 'string') {
        msg = error
      } else if (error?.message) {
        msg = error.message
      } else if (error?.error?.issues?.[0]?.message) {
        msg = error.error.issues[0].message
      }
      addToast({
        title: msg,
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 处理验证码模式的主按钮点击
  const handleCodeModeSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!codeSent) {
      // 还没发送验证码，执行发送验证码
      await handleSendCode()
    } else {
      // 已发送验证码，执行登录
      await handleCodeLogin(e)
    }
  }

  // 邮箱输入处理，统一清理
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(cleanEmail(e.target.value))
  }

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible)
  }

  // 切换登录方式时重置状态
  const switchLoginMethod = (method: 'password' | 'code') => {
    setLoginMethod(method)
    setCodeSent(false)
    setVerificationCode('')
    setCountdown(0)
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <div className="absolute top-0 left-0 w-screen z-10">
        <img src="/images/login/img-bg.svg" className="w-full h-full object-cover" />
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col justify-center px-6 pb-20">
        {/* 标题 */}
        <div className="mb-12 flex flex-col items-center relative z-10">
          {isPasswordFocused ? (
            <img src="/images/login/covering-eyes.png" className="w-24 h-24 mb-3" />
          ) : (
            <img src="/logo.svg" className="w-24 h-24 mb-3" />
          )}
          <h1 className="text-2xl font-light text-foreground mb-2"></h1>
        </div>

        {/* 登录表单 */}
        {loginMethod === 'code' ? (
          <form onSubmit={handleCodeModeSubmit} className="space-y-4">
            {/* 邮箱输入 */}
            <div className="space-y-2">
              <Input
                type="email"
                placeholder={t('email_placeholder')}
                value={email}
                onChange={handleEmailChange}
                variant="flat"
                size="lg"
                isRequired
                autoFocus
                classNames={{
                  input: 'text-foreground placeholder:text-default-400 text-center',
                  inputWrapper: 'bg-content2/50 border-default-300 focus-within:border-primary'
                }}
              />
            </div>

            {/* 验证码输入 - 只在已发送验证码后显示 */}
            {codeSent && (
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <label className="text-sm text-default-600 font-medium">
                    {t('input_code_label')}
                  </label>
                  {countdown > 0 ? (
                    <span className="text-xs text-default-500">
                      {countdown}
                      {t('send_code_loading')}
                    </span>
                  ) : (
                    <Button
                      variant="light"
                      size="sm"
                      onPress={handleSendCode}
                      isLoading={isLoading}
                      isDisabled={!email || isLoading}
                      className="text-primary hover:text-primary-600 text-xs"
                    >
                      {t('resend_code')}
                    </Button>
                  )}
                </div>
                <Input
                  type="text"
                  placeholder={t('input_code')}
                  value={verificationCode}
                  onChange={e => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  variant="flat"
                  size="lg"
                  isRequired
                  maxLength={6}
                  classNames={{
                    input:
                      'text-foreground placeholder:text-default-400 text-center text-lg tracking-widest',
                    inputWrapper:
                      'bg-content2/50 border-default-300 hover:border-default-400 focus-within:border-primary backdrop-blur-sm'
                  }}
                />
              </div>
            )}

            {/* 主按钮 - 根据状态显示不同文本 */}
            <Button
              color="primary"
              type="submit"
              size="lg"
              className="w-full bg-button-primary"
              isLoading={isLoading}
              isDisabled={!email || isLoading || (codeSent && verificationCode.length < 6)}
              radius="full"
            >
              {isLoading
                ? codeSent
                  ? t('login_loading')
                  : t('send_code_loading')
                : codeSent
                ? t('login')
                : t('send_code')}
            </Button>

            {/* 密码登录链接 */}
            <div className="text-center">
              <Button
                variant="light"
                size="sm"
                className="text-default-500 hover:text-foreground text-sm"
                onPress={() => switchLoginMethod('password')}
              >
                {t('use_password_login')}
              </Button>
            </div>
          </form>
        ) : (
          <form onSubmit={handlePasswordLogin} className="space-y-4">
            {/* 邮箱输入 */}
            <div className="space-y-2">
              <div className="flex items-center">
                <Mail className="w-4 h-4 mr-2 text-default-600" />
                <label className="text-sm text-default-600 font-medium">
                  {t('input_email_label')}
                </label>
              </div>
              <Input
                type="email"
                placeholder={t('email_placeholder')}
                value={email}
                onChange={handleEmailChange}
                variant="flat"
                size="lg"
                isRequired
                autoFocus
                classNames={{
                  input: 'text-foreground placeholder:text-default-400',
                  inputWrapper:
                    'bg-content2/50 border-default-300 hover:border-default-400 focus-within:border-primary backdrop-blur-sm'
                }}
              />
            </div>

            {/* 密码输入 */}
            <div className="space-y-2">
              <div className="flex items-center">
                <ShieldCheck className="w-4 h-4 mr-2 text-default-600" />
                <label className="text-sm text-default-600 font-medium">
                  {t('input_password_label')}
                </label>
              </div>
              <Input
                type={isPasswordVisible ? 'text' : 'password'}
                placeholder={t('password_placeholder')}
                value={password}
                onChange={e => setPassword(e.target.value)}
                onFocus={() => setIsPasswordFocused(true)}
                onBlur={() => setIsPasswordFocused(false)}
                endContent={
                  <button
                    className="focus:outline-none"
                    type="button"
                    onClick={togglePasswordVisibility}
                  >
                    {isPasswordVisible ? (
                      <EyeOff className="w-4 h-4 text-default-400" />
                    ) : (
                      <Eye className="w-4 h-4 text-default-400" />
                    )}
                  </button>
                }
                variant="flat"
                size="lg"
                isRequired
                classNames={{
                  input: 'text-foreground placeholder:text-default-400',
                  inputWrapper:
                    'bg-content2/50 border-default-300 hover:border-default-400 focus-within:border-primary backdrop-blur-sm'
                }}
              />
            </div>

            {/* 登录按钮 */}
            <Button
              color="primary"
              type="submit"
              size="lg"
              className="w-full bg-button-primary"
              isLoading={isLoading}
              isDisabled={!email || !password || isLoading}
              radius="full"
            >
              {isLoading ? t('login_loading') : t('login')}
            </Button>

            {/* 验证码登录链接 */}
            <div className="text-center">
              <Button
                variant="light"
                size="sm"
                className="text-default-500 hover:text-foreground text-sm"
                onPress={() => switchLoginMethod('code')}
              >
                {t('use_code_login')}
              </Button>
            </div>
          </form>
        )}

        {/* 注册链接 */}
        <div className="text-center mt-12 text-sm flex items-center justify-center space-x-2">
          <p className="text-default-500">{t('no_account')}</p>
          <Link to="/register" className="text-[#892FFF] hover:text-foreground hover:underline">
            {t('create_account')}
          </Link>
        </div>
      </div>
    </div>
  )
}

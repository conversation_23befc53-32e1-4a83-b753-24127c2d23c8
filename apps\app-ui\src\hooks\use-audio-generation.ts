import { useState, useCallback } from 'react'
import { apiService } from '@/api/services'
import { useRoleStore } from '@/stores/role-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'

// TTS服务版本控制：'tts' 使用Fish Audio，'tts2' 使用ElevenLabs，'tts3' 使用ElevenLabs V3 流式
let ttsVersion: 'tts' | 'tts2' | 'tts3' = 'tts3' // 默认使用ElevenLabs V3 流式TTS服务

export type AudioGenerationStatus = 'idle' | 'generating' | 'completed' | 'failed'

export interface AudioGenerationState {
  status: AudioGenerationStatus
  audioUrl: string | null
  error: string | null
}

// 设置TTS服务版本
export const setTtsVersion = (version: 'tts' | 'tts2' | 'tts3') => {
  ttsVersion = version
}

// 获取当前TTS服务版本
export const getTtsVersion = () => ttsVersion

export const useAudioGeneration = () => {
  const [state, setState] = useState<AudioGenerationState>({
    status: 'idle',
    audioUrl: null,
    error: null
  })

  // 获取当前角色信息
  const { currentRole } = useRoleStore()

  // 重置状态
  const reset = useCallback(() => {
    setState({
      status: 'idle',
      audioUrl: null,
      error: null
    })
  }, [])

  // 获取用户角色缓存
  const { getUserCharacterById } = useUserCharactersStore()

  // 获取当前角色的声音模型ID
  const getCurrentVoice = useCallback(async (): Promise<string | undefined> => {
    if (!currentRole) {
      console.warn('当前没有选择角色，使用默认声音')
      return undefined
    }

    try {
      // UUID 格式正则，用于判断是否为自定义角色
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

      if (uuidPattern.test(currentRole.id)) {
        // 自定义角色，优先从缓存获取
        const cachedCharacter = getUserCharacterById(currentRole.id)

        if (cachedCharacter) {
          console.log('🎵 [AudioGen] 从缓存获取角色声音信息:', cachedCharacter.name)
          return cachedCharacter.voiceModelId || cachedCharacter.voice
        }

        console.warn('⚠️ [AudioGen] 缓存中未找到角色信息，使用默认声音:', currentRole.id)
        return 'soft'
      } else {
        // 系统角色，使用默认声音映射
        const systemVoiceMap: Record<string, string> = {
          ruyun: 'soft',
          jinlin: 'sweet',
          chenqian: 'elegant',
          ruyan: 'sweet',
          heliping: 'soft'
          // 可以添加更多系统角色的声音映射
        }
        const voice = systemVoiceMap[currentRole.id] || 'soft'
        console.log('🎵 [AudioGen] 系统角色声音映射:', currentRole.id, '->', voice)
        return voice
      }
    } catch (error) {
      console.error('获取角色声音信息失败:', error)
      return 'soft'
    }
  }, [currentRole, getUserCharacterById])

  // 生成音频
  const generateAudio = useCallback(
    async (text: string, messageId?: string, chatId?: string, voiceOverride?: string) => {
      try {
        // 开始生成
        setState({
          status: 'generating',
          audioUrl: null,
          error: null
        })

        // 获取声音参数：优先使用传入的 voiceOverride，否则从当前角色获取
        let voiceModelId = voiceOverride
        if (!voiceModelId) {
          voiceModelId = await getCurrentVoice()
        }

        console.log('🎵 使用声音模型ID:', voiceModelId)

        // 根据配置选择TTS服务
        const response =
          ttsVersion === 'tts2'
            ? await apiService.tts2.generateAudio({
                text,
                messageId,
                chatId,
                voiceModelId
              })
            : await apiService.tts.generateAudio({
                text,
                messageId,
                chatId,
                voiceModelId
              })

        if (!response.success) {
          throw new Error(response.message || '生成音频失败')
        }

        // 生成成功
        setState({
          status: 'completed',
          audioUrl: response.data.audioUrl,
          error: null
        })

        return response.data.audioUrl
      } catch (error) {
        console.error('音频生成失败:', error)
        setState({
          status: 'failed',
          audioUrl: null,
          error: error instanceof Error ? error.message : '生成音频失败'
        })
        throw error
      }
    },
    [getCurrentVoice]
  )

  return {
    state,
    generateAudio,
    reset,
    currentVoice: currentRole?.name // 暴露当前角色名称，方便调试
  }
}

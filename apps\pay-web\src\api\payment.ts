/**
 * 支付API客户端
 */

import type { PaymentOrderInfo, PaymentStatus, ApiResponse } from '@/types/payment'

const API_BASE = '/api'

class PaymentAPI {
  /**
   * 获取支付订单信息
   */
  async getOrderInfo(orderId: string): Promise<PaymentOrderInfo> {
    try {
      console.log('🔍 [PAY-API] 获取订单信息:', orderId)

      const response = await fetch(`${API_BASE}/payment/order/${orderId}/info`)
      const result: ApiResponse<PaymentOrderInfo> = await response.json()

      if (!result.success) {
        throw new Error(result.error || '获取订单信息失败')
      }

      console.log('✅ [PAY-API] 订单信息获取成功:', result.data?.orderId)
      return result.data!
    } catch (error) {
      console.error('❌ [PAY-API] 获取订单信息失败:', error)
      throw error
    }
  }

  /**
   * 查询支付状态
   */
  async getPaymentStatus(paymentId: string): Promise<PaymentStatus> {
    try {
      console.log('🔍 [PAY-API] 查询支付状态:', paymentId)

      const response = await fetch(`${API_BASE}/payment/status/${paymentId}`)
      const result: ApiResponse<PaymentStatus> = await response.json()

      if (!result.success) {
        throw new Error(result.error || '查询支付状态失败')
      }

      console.log('📊 [PAY-API] 支付状态:', result.data?.status)
      return result.data!
    } catch (error) {
      console.error('❌ [PAY-API] 查询支付状态失败:', error)
      throw error
    }
  }

  /**
   * 轮询支付状态
   */
  async pollPaymentStatus(
    paymentId: string,
    options: {
      interval?: number // 轮询间隔(ms)
      maxAttempts?: number // 最大尝试次数
      onStatusChange?: (status: PaymentStatus) => void
    } = {}
  ): Promise<PaymentStatus> {
    const {
      interval = 3000,
      maxAttempts = 100, // 5分钟
      onStatusChange
    } = options

    console.log('⏰ [PAY-API] 开始轮询支付状态:', { paymentId, interval, maxAttempts })

    return new Promise((resolve, reject) => {
      let attempts = 0

      const poll = async () => {
        try {
          attempts++
          const status = await this.getPaymentStatus(paymentId)

          if (onStatusChange) {
            onStatusChange(status)
          }

          // 如果支付完成或失败，停止轮询
          if (['completed', 'failed', 'cancelled', 'expired'].includes(status.status)) {
            console.log('✅ [PAY-API] 轮询结束，最终状态:', status.status)
            resolve(status)
            return
          }

          // 如果达到最大尝试次数，停止轮询
          if (attempts >= maxAttempts) {
            console.log('⏰ [PAY-API] 轮询超时，停止查询')
            resolve(status)
            return
          }

          // 继续轮询
          setTimeout(poll, interval)
        } catch (error) {
          console.error('❌ [PAY-API] 轮询过程中出错:', error)

          // 如果是网络错误且未达到最大尝试次数，继续重试
          if (attempts < maxAttempts) {
            setTimeout(poll, interval)
          } else {
            reject(error)
          }
        }
      }

      // 开始轮询
      poll()
    })
  }
}

export const paymentAPI = new PaymentAPI()
export default paymentAPI

import React, { useState, useRef } from 'react'
import { Button, Card, CardBody, Progress } from '@heroui/react'
import { Icon } from '@iconify/react'
import cx from 'classnames'
import { useTranslation } from 'react-i18next'

interface RecordingControlsProps {
  /** 录制的音频Blob */
  audioBlob: Blob
  /** 录制时长(秒) */
  duration: number
  /** 发送录音回调 */
  onSend: () => void
  /** 重新录制回调 */
  onRetry: () => void
  /** 取消录制回调 */
  onCancel: () => void
  /** 是否正在转换中 */
  isConverting?: boolean
  /** 转换错误信息 */
  convertError?: string | null
  /** 组件类名 */
  className?: string
}

/**
 * 录制控制面板组件
 * 提供预览播放、重录、发送、取消等操作
 */
export function RecordingControls({
  audioBlob,
  duration,
  onSend,
  onRetry,
  onCancel,
  isConverting = false,
  convertError,
  className
}: RecordingControlsProps) {
  const { t } = useTranslation('voice')
  const [isPlaying, setIsPlaying] = useState(false)
  const [playProgress, setPlayProgress] = useState(0)
  const audioRef = useRef<HTMLAudioElement | null>(null)
  const audioUrl = useRef<string | null>(null)

  // 格式化时长显示
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 初始化音频URL
  if (!audioUrl.current && audioBlob) {
    audioUrl.current = URL.createObjectURL(audioBlob)
  }

  // 播放/暂停音频
  const togglePlayback = async () => {
    if (!audioUrl.current) return

    try {
      if (!audioRef.current) {
        audioRef.current = new Audio(audioUrl.current)

        // 监听播放进度
        audioRef.current.addEventListener('timeupdate', () => {
          if (audioRef.current) {
            const progress = (audioRef.current.currentTime / audioRef.current.duration) * 100
            setPlayProgress(progress)
          }
        })

        // 监听播放结束
        audioRef.current.addEventListener('ended', () => {
          setIsPlaying(false)
          setPlayProgress(0)
        })
      }

      if (isPlaying) {
        audioRef.current.pause()
        setIsPlaying(false)
      } else {
        await audioRef.current.play()
        setIsPlaying(true)
      }
    } catch (error) {
      console.error('音频播放失败:', error)
    }
  }

  // 组件卸载时清理资源
  React.useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current = null
      }
      if (audioUrl.current) {
        URL.revokeObjectURL(audioUrl.current)
        audioUrl.current = null
      }
    }
  }, [])

  return (
    <Card className={cx('w-full max-w-sm', className)}>
      <CardBody className="space-y-4 p-4">
        {/* 录音信息 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Icon icon="solar:microphone-bold" className="text-lg text-primary" />
            <span className="text-sm font-medium">录音时长: {formatDuration(duration)}</span>
          </div>

          {/* 文件大小 */}
          <span className="text-xs text-default-500">{t('controls.file_size', { size: (audioBlob.size / (1024 * 1024)).toFixed(2) })}</span>
        </div>

        {/* 播放控制 */}
        <div className="space-y-2">
          <div className="flex items-center space-x-3">
            {/* 播放/暂停按钮 */}
            <Button
              isIconOnly
              size="sm"
              variant="flat"
              color="primary"
              onPress={togglePlayback}
              isDisabled={isConverting}
            >
              <Icon icon={isPlaying ? 'solar:pause-bold' : 'solar:play-bold'} className="text-lg" />
              <span className="sr-only">{isPlaying ? t('controls.pause') : t('controls.play')}</span>
            </Button>

            {/* 播放进度条 */}
            <div className="flex-1">
              <Progress value={playProgress} size="sm" color="primary" className="w-full" />
            </div>

            {/* 播放时长 */}
            <span className="text-xs text-default-500 w-12 text-right">
              {isPlaying
                ? `${Math.floor((playProgress * duration) / 100)}s`
                : `${Math.floor(duration)}s`}
            </span>
          </div>
        </div>

        {/* 转换状态 */}
        {isConverting && (
          <div className="bg-primary/5 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Icon icon="svg-spinners:3-dots-bounce" className="text-primary text-lg" />
              <span className="text-sm text-primary">{t('recorder.status.converting')}</span>
            </div>
          </div>
        )}

        {/* 错误提示 */}
        {convertError && (
          <div className="bg-danger/5 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <Icon icon="solar:danger-bold" className="text-danger text-lg" />
              <div className="flex-1">
                <p className="text-sm text-danger font-medium">{t('controls.convert_error')}</p>
                <p className="text-xs text-danger/80">{convertError}</p>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          {/* 取消按钮 */}
          <Button
            variant="light"
            color="default"
            onPress={onCancel}
            isDisabled={isConverting}
            startContent={<Icon icon="solar:close-circle-linear" width={18} />}
            className="flex-1"
          >
            {t('controls.cancel')}
          </Button>

          {/* 重录按钮 */}
          <Button
            variant="flat"
            color="warning"
            onPress={onRetry}
            isDisabled={isConverting}
            startContent={<Icon icon="solar:refresh-bold" width={18} />}
            className="flex-1"
          >
            {t('controls.retry')}
          </Button>

          {/* 发送按钮 */}
          <Button
            variant="solid"
            color="primary"
            onPress={onSend}
            isDisabled={isConverting}
            isLoading={isConverting}
            startContent={!isConverting && <Icon icon="solar:paper-bin-bold" width={18} />}
            className="flex-1"
          >
            {isConverting ? t('recorder.status.converting') : t('controls.send')}
          </Button>
        </div>

        {/* 使用提示 */}
        <div className="text-center">
          <p className="text-xs text-default-400">点击播放预览录音，确认无误后发送</p>
        </div>
      </CardBody>
    </Card>
  )
}

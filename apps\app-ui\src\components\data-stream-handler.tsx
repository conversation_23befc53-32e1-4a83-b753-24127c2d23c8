import { useChat } from '@ai-sdk/react';
import { useEffect, useRef, useState, createContext, useContext } from 'react';

// 定义流式数据类型
export type DataStreamDelta = {
  type:
    | 'text-delta'
    | 'code-delta'
    | 'sheet-delta'
    | 'image-delta'
    | 'title'
    | 'id'
    | 'clear'
    | 'finish'
    | 'kind';
  content: string;
};

// 创建上下文来管理流数据
type StreamingContextType = {
  streamingMessageId: string | null;
  streamingContent: string;
  isStreaming: boolean;
};

const StreamingContext = createContext<StreamingContextType>({
  streamingMessageId: null,
  streamingContent: '',
  isStreaming: false,
});

// 导出自定义钩子以在组件中使用
export const useStreamingContent = () => useContext(StreamingContext);

// 导出一个组件引用，方便其他组件获取上下文实例
let setStreamingState: React.Dispatch<
  React.SetStateAction<StreamingContextType>
> | null = null;

// 流式处理上下文提供者
export function StreamingProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<StreamingContextType>({
    streamingMessageId: null,
    streamingContent: '',
    isStreaming: false,
  });

  // 保存setState引用以便其他组件可以访问
  setStreamingState = setState;

  return (
    <StreamingContext.Provider value={state}>
      {children}
    </StreamingContext.Provider>
  );
}

// 实际的数据流处理组件
export function DataStreamHandler({ id }: { id: string }) {
  const { data: dataStream } = useChat({ id });
  const lastProcessedIndex = useRef(-1);

  // 使用上下文
  // const { streamingMessageId } = useStreamingContent();

  useEffect(() => {
    if (!dataStream?.length) return;

    const newDeltas = dataStream.slice(lastProcessedIndex.current + 1);
    lastProcessedIndex.current = dataStream.length - 1;

    // 处理数据流
    (newDeltas as DataStreamDelta[]).forEach((delta: DataStreamDelta) => {
      if (delta.type === 'text-delta' && setStreamingState) {
        setStreamingState((prev) => {
          // 确保流式内容按顺序累积
          const updatedContent = prev.streamingContent + delta.content;
          return {
            ...prev,
            streamingContent: updatedContent,
            isStreaming: true,
          };
        });
      } else if (delta.type === 'id' && setStreamingState) {
        setStreamingState((prev) => ({
          ...prev,
          streamingMessageId: delta.content,
          streamingContent: '',
          isStreaming: true,
        }));
      } else if (delta.type === 'finish' && setStreamingState) {
        setStreamingState((prev) => ({
          ...prev,
          isStreaming: false,
        }));
      }
    });
  }, [dataStream]);

  return null;
}

// 导出辅助函数，用于手动重置流状态
export function resetStreamingState() {
  if (setStreamingState) {
    setStreamingState({
      streamingMessageId: null,
      streamingContent: '',
      isStreaming: false,
    });
  }
}

import { CapacitorHttp } from '@capacitor/core'
import { Capacitor } from '@capacitor/core'

/**
 * 网络请求工具类
 * 在 Capacitor 环境中使用原生 HTTP 插件绕过 CORS 限制
 */
export class NetworkUtils {
  /**
   * 检查是否在 Capacitor 环境中运行
   */
  static isCapacitor(): boolean {
    return Capacitor.isNativePlatform()
  }

  /**
   * 安全地加载图片资源
   * 在 Capacitor 中直接使用原始 URL，在 Web 中使用标准方式
   */
  static async loadImage(url: string): Promise<string> {
    if (this.isCapacitor()) {
      // 在 Capacitor 环境中，直接返回原始 URL
      // 因为 Capacitor 的原生环境可以直接访问外部资源，不受 CORS 限制
      console.log('Capacitor 环境：直接使用原始图片 URL:', url)
      return url
    } else {
      // 在 Web 环境中直接返回 URL
      return url
    }
  }

  /**
   * 安全地加载视频资源
   */
  static async loadVideo(url: string): Promise<string> {
    if (this.isCapacitor()) {
      try {
        // 对于视频，我们通常直接使用 URL，因为视频元素可以处理跨域
        // 但如果需要，也可以使用原生 HTTP
        return url
      } catch (error) {
        console.warn('处理视频 URL 失败:', error)
        return url
      }
    } else {
      return url
    }
  }

  /**
   * 预加载资源
   */
  static async preloadResource(url: string, type: 'image' | 'video' = 'image'): Promise<boolean> {
    try {
      if (this.isCapacitor()) {
        // 在 Capacitor 中使用 HEAD 请求检查资源是否可用
        const response = await CapacitorHttp.request({
          method: 'HEAD',
          url: url,
          headers: {}
        })
        return response.status >= 200 && response.status < 300
      } else {
        // 在 Web 中使用标准预加载方式
        if (type === 'image') {
          return new Promise(resolve => {
            const img = new Image()
            img.onload = () => resolve(true)
            img.onerror = () => resolve(false)
            img.src = url
          })
        } else {
          // 视频预加载
          return new Promise(resolve => {
            const video = document.createElement('video')
            video.onloadedmetadata = () => resolve(true)
            video.onerror = () => resolve(false)
            video.src = url
          })
        }
      }
    } catch (error) {
      console.warn('预加载资源失败:', url, error)
      return false
    }
  }

  /**
   * 获取资源的 blob URL（用于绕过 CORS）
   */
  static async getBlobUrl(url: string): Promise<string> {
    if (this.isCapacitor()) {
      try {
        const response = await CapacitorHttp.get({
          url: url,
          headers: {},
          responseType: 'blob'
        })

        const blob = new Blob([response.data])
        return URL.createObjectURL(blob)
      } catch (error) {
        console.warn('获取 blob URL 失败:', error)
        return url
      }
    } else {
      return url
    }
  }
}

/**
 * 资源加载器类
 * 提供统一的资源加载接口
 */
export class ResourceLoader {
  private static cache = new Map<string, string>()

  /**
   * 加载并缓存资源
   */
  static async load(url: string, type: 'image' | 'video' = 'image'): Promise<string> {
    // 检查缓存
    if (this.cache.has(url)) {
      return this.cache.get(url)!
    }

    try {
      let processedUrl: string

      if (type === 'image') {
        processedUrl = await NetworkUtils.loadImage(url)
      } else {
        processedUrl = await NetworkUtils.loadVideo(url)
      }

      // 缓存结果
      this.cache.set(url, processedUrl)
      return processedUrl
    } catch (error) {
      console.error('资源加载失败:', url, error)
      return url // 返回原始 URL 作为回退
    }
  }

  /**
   * 清理缓存
   */
  static clearCache(): void {
    // 释放 blob URLs
    for (const url of this.cache.values()) {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url)
      }
    }
    this.cache.clear()
  }

  /**
   * 批量预加载资源
   */
  static async preloadBatch(urls: Array<{ url: string; type: 'image' | 'video' }>): Promise<void> {
    const promises = urls.map(async ({ url, type }) => {
      try {
        await this.load(url, type)
      } catch (error) {
        console.warn('批量预加载失败:', url, error)
      }
    })

    await Promise.allSettled(promises)
  }
}

// 会话管理器 - 处理登录和 token 刷新
import type {
  Env,
  ElevenLabsAccount,
  SessionInfo,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse
} from './types'

export class SessionManager {
  private env: Env
  private readonly KV_PREFIX = 'elevenlabs_worker_v3_session' // 独特的前缀避免冲突

  constructor(env: Env) {
    this.env = env
  }

  private readonly LOGIN_API_KEY = 'AIzaSyBSsRE_1Os04-bxpd5JTLIniy3UK4OqKys'
  private readonly API_KEY = '***************************************************'
  private readonly BASE_URL = 'https://identitytoolkit.googleapis.com'

  /**
   * 登录获取会话 token
   */
  async login(account: ElevenLabsAccount): Promise<SessionInfo | null> {
    try {
      console.log('SessionManager: 开始Firebase Auth登录，账号:', account.email)

      // 修复：添加 clientType 字段
      const loginData: LoginRequest = {
        returnSecureToken: true,
        email: account.email,
        password: account.password,
        clientType: 'CLIENT_TYPE_WEB' // 修复类型错误
      }

      console.log('SessionManager: 发送Firebase Auth请求...')

      // 创建一个带超时的fetch请求
      const controller = new AbortController()
      const timeoutId = setTimeout(() => {
        console.log('SessionManager: Firebase Auth请求超时，中断请求')
        controller.abort()
      }, 30000) // 30秒超时

      let result: LoginResponse

      try {
        // Firebase Auth 登录请求
        const response = await fetch(
          `${this.BASE_URL}/v1/accounts:signInWithPassword?key=${this.LOGIN_API_KEY}`,
          {
            method: 'POST',
            headers: {
              'Content-type': 'application/json',
              Referer: 'https://elevenlabs.io'
            },
            body: JSON.stringify(loginData),
            signal: controller.signal
          }
        )

        clearTimeout(timeoutId)
        console.log('SessionManager: Firebase Auth响应状态:', response.status)

        if (!response.ok) {
          const errorText = await response.text()
          console.error(`登录失败: ${response.status} ${response.statusText}`, errorText)
          return null
        }

        result = (await response.json()) as LoginResponse
      } catch (fetchError) {
        clearTimeout(timeoutId)
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          console.error('Firebase Auth请求超时')
        } else {
          console.error('Firebase Auth请求失败:', fetchError)
        }
        return null
      }

      if (!result.idToken) {
        console.error('登录响应无效:', result)
        return null
      }

      // 计算过期时间（expiresIn 是秒数）
      const expiryTime = new Date(Date.now() + parseInt(result.expiresIn) * 1000)

      const sessionInfo: SessionInfo = {
        token: result.idToken,
        refreshToken: result.refreshToken,
        expiry: expiryTime,
        userId: account.id
      }

      // 缓存会话信息
      await this.saveSession(account.id, sessionInfo)

      return sessionInfo
    } catch (error) {
      console.error(`账号 ${account.email} 登录失败:`, error)
      return null
    }
  }

  /**
   * 刷新 token
   */
  async refreshToken(account: ElevenLabsAccount): Promise<SessionInfo | null> {
    if (!account.refreshToken) {
      console.log(`账号 ${account.email} 无 refresh token，尝试重新登录`)
      return this.login(account)
    }

    try {
      // 修复：使用正确的类型和转换
      const refreshData: RefreshTokenRequest = {
        grant_type: 'refresh_token',
        refresh_token: account.refreshToken
      }

      // Firebase Auth 刷新 token 请求
      const response = await fetch(`${this.BASE_URL}/v1/token?key=${this.API_KEY}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          Referer: 'https://elevenlabs.io/',
          'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
          'User-Agent':
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36'
        },
        // 修复：正确转换为 URLSearchParams
        body: new URLSearchParams(refreshData as Record<string, string>).toString()
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.log(`刷新 token 失败，尝试重新登录: ${response.status}`, errorText)
        return this.login(account)
      }

      const result = (await response.json()) as RefreshTokenResponse

      if (!result.access_token) {
        console.log('刷新 token 响应无效，尝试重新登录')
        return this.login(account)
      }

      // 计算过期时间
      const expiryTime = new Date(Date.now() + parseInt(result.expires_in) * 1000)

      const sessionInfo: SessionInfo = {
        token: result.access_token,
        refreshToken: result.refresh_token,
        expiry: expiryTime,
        userId: account.id
      }

      // 更新缓存
      await this.saveSession(account.id, sessionInfo)

      return sessionInfo
    } catch (error) {
      console.error(`刷新 token 失败，尝试重新登录:`, error)
      return this.login(account)
    }
  }

  /**
   * 获取有效的会话信息
   */
  async getValidSession(account: ElevenLabsAccount): Promise<SessionInfo | null> {
    console.log('SessionManager: 开始获取有效会话，账号:', account.email)

    // 先检查缓存的会话
    console.log('SessionManager: 检查缓存会话...')
    const cachedSession = await this.getSession(account.id)
    console.log('SessionManager: 缓存会话结果:', cachedSession ? '存在' : '不存在')

    if (cachedSession && this.isSessionValid(cachedSession)) {
      console.log('SessionManager: 使用缓存会话')
      return cachedSession
    }

    // 如果账号有 token 且未过期，使用账号的 token
    console.log('SessionManager: 检查账号自带token...')
    console.log('SessionManager: sessionToken存在:', !!account.sessionToken)
    console.log('SessionManager: tokenExpiry存在:', !!account.tokenExpiry)

    if (account.sessionToken && account.tokenExpiry) {
      console.log('SessionManager: 检查token是否过期...')
      if (account.tokenExpiry > new Date()) {
        console.log('SessionManager: 使用账号自带的有效token')
        const sessionInfo: SessionInfo = {
          token: account.sessionToken,
          refreshToken: account.refreshToken,
          expiry: account.tokenExpiry,
          userId: account.id
        }

        // 更新缓存
        await this.saveSession(account.id, sessionInfo)
        return sessionInfo
      } else {
        console.log('SessionManager: 账号token已过期')
      }
    }

    // 尝试刷新 token
    console.log('SessionManager: refreshToken存在:', !!account.refreshToken)
    if (account.refreshToken) {
      console.log('SessionManager: 尝试刷新token...')
      const refreshedSession = await this.refreshToken(account)
      if (refreshedSession) {
        console.log('SessionManager: token刷新成功')
        return refreshedSession
      } else {
        console.log('SessionManager: token刷新失败')
      }
    }

    // 最后尝试重新登录
    console.log('SessionManager: 尝试重新登录...')
    const loginResult = await this.login(account)
    console.log('SessionManager: 登录结果:', loginResult ? '成功' : '失败')
    return loginResult
  }

  /**
   * 检查会话是否有效
   */
  private isSessionValid(session: SessionInfo): boolean {
    const now = new Date()
    const buffer = 5 * 60 * 1000 // 5分钟缓冲时间

    return session.expiry.getTime() > now.getTime() + buffer
  }

  /**
   * 保存会话信息到 KV
   */
  private async saveSession(accountId: string, session: SessionInfo): Promise<void> {
    try {
      await this.env.ELEVENLABS_CACHE?.put(
        `${this.KV_PREFIX}:${accountId}`,
        JSON.stringify(session),
        {
          expirationTtl: Math.floor((session.expiry.getTime() - Date.now()) / 1000)
        }
      )
    } catch (error) {
      console.error('保存会话失败:', error)
    }
  }

  /**
   * 从 KV 获取会话信息
   */
  private async getSession(accountId: string): Promise<SessionInfo | null> {
    try {
      const sessionJson = await this.env.ELEVENLABS_CACHE?.get(`${this.KV_PREFIX}:${accountId}`)
      if (!sessionJson) {
        return null
      }

      const session = JSON.parse(sessionJson) as SessionInfo
      return {
        ...session,
        expiry: new Date(session.expiry)
      }
    } catch (error) {
      console.error('获取会话失败:', error)
      return null
    }
  }

  /**
   * 清除会话缓存
   */
  async clearSession(accountId: string): Promise<void> {
    try {
      await this.env.ELEVENLABS_CACHE?.delete(`${this.KV_PREFIX}:${accountId}`)
    } catch (error) {
      console.error('清除会话失败:', error)
    }
  }

  /**
   * 验证 token 是否有效（通过调用简单的 API）
   */
  async validateToken(token: string): Promise<boolean> {
    try {
      // 使用 ElevenLabs API 验证 token
      const response = await fetch('https://api.elevenlabs.io/v1/user', {
        headers: {
          Authorization: `Bearer ${token}`,
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
        }
      })

      return response.ok
    } catch (error) {
      console.error('验证 token 失败:', error)
      return false
    }
  }
}

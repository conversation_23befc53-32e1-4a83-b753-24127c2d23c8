1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.rcapp.bleadvertiser" >
4
5    <uses-sdk android:minSdkVersion="22" />
6
7    <!-- 基本蓝牙权限 -->
8    <uses-permission android:name="android.permission.BLUETOOTH" />
8-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:6:5-68
8-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:6:22-65
9    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
9-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:7:5-74
9-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:7:22-71
10
11    <!-- Android 12及以上需要的蓝牙权限 -->
12    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
12-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:10:5-78
12-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:10:22-75
13    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
13-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:11:5-76
13-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:11:22-73
14    <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
14-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:12:5-73
14-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:12:22-70
15
16    <!-- 位置权限（Android 11以下需要） -->
17    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
17-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:15:5-79
17-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:15:22-76
18    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
18-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:16:5-81
18-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:16:22-78
19
20    <!-- 声明设备需要蓝牙LE功能 -->
21    <uses-feature
21-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:19:5-90
22        android:name="android.hardware.bluetooth_le"
22-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:19:19-63
23        android:required="true" />
23-->/Users/<USER>/Documents/WWW/AI/rc-demo-app/packages/capacitor-ble-advertiser/android/src/main/AndroidManifest.xml:19:64-87
24
25</manifest>

/**
 * 积分商城API路由
 */

import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '../middleware/auth'
import { languageMiddleware } from '../middleware/language'
import type { SupportedLanguage } from '../i18n/config'
import { pointsPackageQueries } from '../lib/db/queries/points-package'
import { createPaymentService } from '../lib/payment/payment-factory'
import { ensureUserExists } from '../lib/db/queries/membership'
import type { PaymentMethod } from '../lib/payment/payment-config'
import type { Env } from '../types/env'
import type { Context } from 'hono'

// 定义应用上下文类型
type AppContext = {
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
    user?: any // 与 authMiddleware 中的 Variables 类型保持一致
  }
}

const app = new Hono<AppContext>()

// 简单的用户ID缓存 (5分钟有效期)
const userIdCache = new Map<string, { localUserId: string; timestamp: number }>()
const CACHE_TTL = 5 * 60 * 1000 // 5分钟

// 辅助函数：从Supabase用户上下文解析本地用户ID（带缓存）
async function resolveLocalUserId(c: Context<AppContext>): Promise<string> {
  const supabaseUser = c.get('user')
  const t = c.get('t')
  if (!supabaseUser) {
    throw new Error(t('points.user_not_authenticated'))
  }

  // 检查缓存
  const cached = userIdCache.get(supabaseUser.id)
  const now = Date.now()

  if (cached && now - cached.timestamp < CACHE_TTL) {
    return cached.localUserId
  }

  // 确保用户在本地数据库中存在
  const localUserId = await ensureUserExists(c.env, supabaseUser.id, supabaseUser.email)

  // 更新缓存
  userIdCache.set(supabaseUser.id, { localUserId, timestamp: now })

  return localUserId
}

// 获取积分包列表
app.get('/packages', languageMiddleware, async c => {
  try {
    console.log('🛍️ [POINTS] 获取积分包列表')

    const packages = await pointsPackageQueries.getActivePackages(c.env)

    console.log('✅ [POINTS] 积分包列表获取成功，数量:', packages.length)

    return c.json({
      success: true,
      data: packages
    })
  } catch (error) {
    console.error('❌ [POINTS] 获取积分包列表失败:', error)
    const t = c.get('t')
    return c.json(
      {
        success: false,
        error: t('points.packages_list_failed')
      },
      500
    )
  }
})

// 获取单个积分包详情
app.get('/packages/:packageId', languageMiddleware, async c => {
  try {
    const packageId = c.req.param('packageId')

    console.log('🛍️ [POINTS] 获取积分包详情:', packageId)

    const pointsPackage = await pointsPackageQueries.getPackageById(c.env, packageId)

    if (!pointsPackage) {
      const t = c.get('t')
      return c.json(
        {
          success: false,
          error: t('points.package_not_found')
        },
        404
      )
    }

    console.log('✅ [POINTS] 积分包详情获取成功:', pointsPackage.name)

    return c.json({
      success: true,
      data: pointsPackage
    })
  } catch (error) {
    console.error('❌ [POINTS] 获取积分包详情失败:', error)
    const t = c.get('t')
    return c.json(
      {
        success: false,
        error: t('points.package_detail_failed')
      },
      500
    )
  }
})

// 创建积分包购买订单
const createPointsOrderSchema = z.object({
  description: z.string().optional(),
  paymentMethod: z.enum(['alipay', 'wechat']).default('alipay'),
  metadata: z.record(z.any()).optional()
})

app.post(
  '/packages/:packageId/purchase',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createPointsOrderSchema),
  async c => {
    try {
      const packageId = c.req.param('packageId')
      const { description, paymentMethod, metadata } = c.req.valid('json')

      // 解析本地用户ID
      const userId = await resolveLocalUserId(c)

      console.log('🛒 [POINTS] 创建积分包购买订单:', { packageId, userId, paymentMethod })

      // 获取积分包信息
      const pointsPackage = await pointsPackageQueries.getPackageById(c.env, packageId)
      if (!pointsPackage) {
        const t = c.get('t')
        return c.json(
          {
            success: false,
            error: t('points.package_not_found')
          },
          404
        )
      }

      if (!pointsPackage.isActive) {
        const t = c.get('t')
        return c.json(
          {
            success: false,
            error: t('points.package_inactive')
          },
          400
        )
      }

      // 创建支付订单
      const paymentService = createPaymentService(c.env)
      const orderDescription =
        description ||
        `购买${pointsPackage.name} - ${pointsPackage.points + (pointsPackage.bonusPoints ?? 0)}积分`

      const paymentResult = await paymentService.createOrder({
        userId,
        pointsPackageId: packageId,
        amount: Number.parseFloat(pointsPackage.price),
        description: orderDescription,
        paymentMethod: paymentMethod as PaymentMethod,
        metadata: {
          ...metadata,
          packageName: pointsPackage.name,
          pointsAmount: pointsPackage.points,
          bonusPoints: pointsPackage.bonusPoints ?? 0,
          totalPoints: pointsPackage.points + (pointsPackage.bonusPoints ?? 0)
        }
      })

      console.log('✅ [POINTS] 积分包购买订单创建成功:', paymentResult.paymentId)

      return c.json({
        success: true,
        data: {
          orderId: paymentResult.orderId,
          paymentId: paymentResult.paymentId,
          redirectUrl: paymentResult.redirectUrl,
          amount: Number.parseFloat(pointsPackage.price),
          packageName: pointsPackage.name,
          description: orderDescription,
          paymentMethod,
          isUpgrade: false,
          packageInfo: {
            id: pointsPackage.id,
            name: pointsPackage.name,
            points: pointsPackage.points,
            bonusPoints: pointsPackage.bonusPoints ?? 0,
            totalPoints: pointsPackage.points + (pointsPackage.bonusPoints ?? 0)
          }
        }
      })
    } catch (error: any) {
      console.error('❌ [POINTS] 创建积分包购买订单失败:', error)

      if (error.issues) {
        return c.json(
          {
            success: false,
            error: {
              issues: error.issues,
              name: 'ZodError'
            }
          },
          400
        )
      }

      const t = c.get('t')
      return c.json(
        {
          success: false,
          error: error.message || t('points.create_order_failed')
        },
        500
      )
    }
  }
)

export default app

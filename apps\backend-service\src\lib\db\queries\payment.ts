/**
 * 支付订单数据库查询操作
 */

import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { PaymentOrder } from '../schema';
import type { Env } from '@/types/env';

/**
 * 创建支付订单
 */
export interface CreatePaymentOrderData {
  userId: string;
  planId?: string;
  pointsPackageId?: string;
  orderNo: string;
  amount: number;
  currency?: string;
  paymentMethod: string;
  description: string;
  isUpgrade?: boolean;
  originalAmount?: number;
  currentSubscriptionId?: string;
  callbackUrl?: string;
  returnUrl?: string;
  notifyUrl?: string;
  expiresAt: Date;
  metadata?: any;
}

export async function createPaymentOrder(
  env: Env,
  orderData: CreatePaymentOrderData
): Promise<PaymentOrder[]> {
  try {
    const supabase = getSupabase(env);

    console.log('💳 [DB-PAYMENT] 创建支付订单:', {
      userId: orderData.userId,
      orderNo: orderData.orderNo,
      amount: orderData.amount,
      paymentMethod: orderData.paymentMethod,
    });

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .insert({
        user_id: orderData.userId,
        plan_id: orderData.planId,
        points_package_id: orderData.pointsPackageId,
        order_no: orderData.orderNo,
        amount: orderData.amount.toString(),
        currency: orderData.currency || 'CNY',
        payment_method: orderData.paymentMethod,
        description: orderData.description,
        is_upgrade: orderData.isUpgrade || false,
        original_amount: orderData.originalAmount?.toString(),
        current_subscription_id: orderData.currentSubscriptionId,
        callback_url: orderData.callbackUrl,
        return_url: orderData.returnUrl,
        notify_url: orderData.notifyUrl,
        expires_at: orderData.expiresAt.toISOString(),
        metadata: orderData.metadata || {},
        status: 'pending',
      })
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    console.log('✅ [DB-PAYMENT] 支付订单创建成功:', data?.[0]?.id);
    return data || [];
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 创建支付订单失败:', error);
    throw error;
  }
}

/**
 * 根据订单号获取支付订单
 */
export async function getPaymentOrderByOrderNo(
  env: Env,
  orderNo: string
): Promise<PaymentOrder | null> {
  try {
    const supabase = getSupabase(env);

    console.log('🔍 [DB-PAYMENT] 查询支付订单:', orderNo);

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*')
      .eq('order_no', orderNo)
      .single();

    const { data: order, error } = handleSupabaseSingleResult(result);
    if (error) return null;

    console.log('📋 [DB-PAYMENT] 订单查询结果:', order ? '找到订单' : '订单不存在');
    return order;
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 查询支付订单失败:', error);
    throw error;
  }
}

/**
 * 根据ID获取支付订单
 */
export async function getPaymentOrderById(env: Env, orderId: string): Promise<PaymentOrder | null> {
  try {
    const supabase = getSupabase(env);

    console.log('🔍 [DB-PAYMENT] 根据ID查询支付订单:', orderId);

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*')
      .eq('id', orderId)
      .single();

    const { data: order, error } = handleSupabaseSingleResult(result);
    if (error) return null;

    console.log('📋 [DB-PAYMENT] 订单查询结果:', order ? '找到订单' : '订单不存在');
    return order;
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 根据ID查询支付订单失败:', error);
    throw error;
  }
}

/**
 * 更新支付订单状态
 */
export async function updatePaymentOrderStatus(
  env: Env,
  orderNo: string,
  status: 'pending' | 'paid' | 'failed' | 'cancelled' | 'expired',
  externalOrderId?: string,
  paidAt?: Date
): Promise<PaymentOrder[]> {
  try {
    const supabase = getSupabase(env);

    console.log('📝 [DB-PAYMENT] 更新支付订单状态:', {
      orderNo,
      status,
      externalOrderId,
    });

    const updateData: any = { status };

    if (externalOrderId) {
      updateData.external_order_id = externalOrderId;
    }

    if (paidAt) {
      updateData.paid_at = paidAt.toISOString();
    }

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .update(updateData)
      .eq('order_no', orderNo)
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    console.log('✅ [DB-PAYMENT] 支付订单状态更新成功');
    return data || [];
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 更新支付订单状态失败:', error);
    throw error;
  }
}

/**
 * 获取用户的支付订单历史
 */
export async function getUserPaymentOrders(
  env: Env,
  userId: string,
  limit = 20,
  offset = 0
): Promise<PaymentOrder[]> {
  try {
    const supabase = getSupabase(env);

    console.log('📋 [DB-PAYMENT] 获取用户支付订单历史:', {
      userId,
      limit,
      offset,
    });

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    console.log('📊 [DB-PAYMENT] 找到订单数量:', (data || []).length);
    return data || [];
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 获取用户支付订单历史失败:', error);
    throw error;
  }
}

/**
 * 获取待处理的支付订单 (用于定时检查过期)
 */
export async function getPendingPaymentOrders(env: Env, limit = 100): Promise<PaymentOrder[]> {
  try {
    const supabase = getSupabase(env);

    console.log('⏰ [DB-PAYMENT] 获取待处理支付订单');

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*')
      .eq('status', 'pending')
      .is('paid_at', null)
      .order('created_at')
      .limit(limit);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    console.log('📊 [DB-PAYMENT] 待处理订单数量:', (data || []).length);
    return data || [];
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 获取待处理支付订单失败:', error);
    throw error;
  }
}

/**
 * 获取过期的支付订单
 */
export async function getExpiredPaymentOrders(env: Env, limit = 100): Promise<PaymentOrder[]> {
  try {
    const supabase = getSupabase(env);

    console.log('⏰ [DB-PAYMENT] 获取过期支付订单');
    const now = new Date();

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*')
      .eq('status', 'pending')
      .lt('expires_at', now.toISOString())
      .order('expires_at')
      .limit(limit);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    console.log('📊 [DB-PAYMENT] 过期订单数量:', (data || []).length);
    return data || [];
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 获取过期支付订单失败:', error);
    throw error;
  }
}

/**
 * 获取支付订单详情 (包含关联的会员计划信息)
 */
export async function getPaymentOrderWithDetails(env: Env, orderNo: string): Promise<any> {
  try {
    const supabase = getSupabase(env);

    console.log('🔍 [DB-PAYMENT] 获取支付订单详情:', orderNo);

    // 先获取订单基本信息
    const orderResult = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*')
      .eq('order_no', orderNo)
      .single();

    const { data: order, error: orderError } = handleSupabaseSingleResult(orderResult);
    if (orderError || !order) {
      console.log('📋 [DB-PAYMENT] 订单详情查询结果: 订单不存在');
      return null;
    }

    // 获取会员计划信息（如果有）
    let plan = null;
    if (order.planId) {
      const planResult = await supabase
        .from(TABLE_NAMES.membershipPlan)
        .select('*')
        .eq('id', order.planId)
        .single();

      const { data: planData } = handleSupabaseSingleResult(planResult);
      plan = planData;
    }

    // 获取积分套餐信息（如果有）
    let pointsPackage = null;
    if (order.pointsPackageId) {
      const packageResult = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .select('*')
        .eq('id', order.pointsPackageId)
        .single();

      const { data: packageData } = handleSupabaseSingleResult(packageResult);
      pointsPackage = packageData;
    }

    const orderDetail = {
      order,
      plan,
      pointsPackage,
    };

    console.log('📋 [DB-PAYMENT] 订单详情查询结果: 找到详情');
    return orderDetail;
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 获取支付订单详情失败:', error);
    throw error;
  }
}

/**
 * 删除测试订单 (开发环境用)
 */
export async function deleteTestPaymentOrders(env: Env): Promise<number> {
  try {
    const supabase = getSupabase(env);

    console.log('🧹 [DB-PAYMENT] 删除测试订单');

    const result = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .delete()
      .eq('payment_method', 'test')
      .select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    console.log('✅ [DB-PAYMENT] 测试订单删除完成');
    return (data || []).length;
  } catch (error) {
    console.error('❌ [DB-PAYMENT] 删除测试订单失败:', error);
    throw error;
  }
}

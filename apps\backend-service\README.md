# Pleasurehub App - Backend Service

基于 Cloudflare Workers + Hono + Supabase 的现代化 API 服务

## 🏗️ 技术架构

```
┌─────────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Cloudflare Workers  │───▶│   Supabase DB    │    │ Supabase Auth   │
│   (Hono Framework)  │    │  (PostgreSQL)    │    │  (用户认证)      │
└─────────────────────┘    └──────────────────┘    └─────────────────┘
           │                          │                        │
           ▼                          ▼                        ▼
┌─────────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Cloudflare KV       │    │ Supabase Storage │    │ Supabase        │
│   (缓存存储)         │    │   (文件存储)      │    │ Realtime        │
└─────────────────────┘    └──────────────────┘    └─────────────────┘
```

## 🚀 核心特性

- **⚡ 极速响应**: Cloudflare Workers 边缘计算，全球 <50ms 延迟
- **🔒 安全认证**: Supabase Auth 内置认证系统
- **📊 实时数据**: PostgreSQL + 实时订阅支持
- **🗄️ 文件存储**: Supabase Storage 一体化文件管理
- **🔄 智能缓存**: Cloudflare KV 高性能缓存
- **🤖 AI 集成**: 支持多种 AI 模型 (XAI, OpenAI 等)

## 📦 技术栈

### 核心框架

- **[Hono](https://hono.dev/)** - 轻量级 Web 框架
- **[Cloudflare Workers](https://workers.cloudflare.com/)** - 边缘计算平台
- **[Supabase](https://supabase.com/)** - 后端即服务平台

### 数据层

- **PostgreSQL** - 主数据库 (Supabase)
- **Drizzle ORM** - 类型安全的 ORM
- **Cloudflare KV** - 键值缓存存储

### 开发工具

- **TypeScript** - 类型安全
- **Wrangler** - Cloudflare 开发工具
- **Biome** - 代码格式化和检查
- **Vitest** - 单元测试框架

## 🛠️ 开发环境设置

### 1. 环境要求

```bash
Node.js >= 18
pnpm >= 8
```

### 2. 安装依赖

```bash
cd apps/backend-service
pnpm install
```

### 3. 环境变量配置

```bash
cp .dev.vars.example .dev.vars
```

配置以下环境变量：

```env
# Supabase 配置
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# 数据库连接
DATABASE_URL=postgresql://postgres:[password]@db.[project].supabase.co:5432/postgres

# AI 服务配置
XAI_API_KEY=your-xai-api-key
OPENAI_API_KEY=your-openai-api-key

# Cloudflare 配置
CLOUDFLARE_ACCOUNT_ID=your-account-id
CLOUDFLARE_API_TOKEN=your-api-token
```

### 4. 数据库设置

```bash
# 生成数据库迁移
pnpm db:generate

# 执行数据库迁移
pnpm db:migrate

# 查看数据库
pnpm db:studio
```

## 🚀 开发命令

```bash
# 开发模式
pnpm dev

# 构建项目
pnpm build

# 部署到 Cloudflare
pnpm deploy

# 代码检查
pnpm lint

# 代码格式化
pnpm format

# 运行测试
pnpm test

# 数据库相关
pnpm db:generate    # 生成迁移文件
pnpm db:migrate     # 执行迁移
pnpm db:studio      # 打开数据库管理界面
pnpm db:seed        # 初始化种子数据
```

## 📁 项目结构

```
apps/backend-service/
├── src/
│   ├── routes/          # API 路由
│   │   ├── auth.ts      # 认证相关
│   │   ├── chat.ts      # 聊天功能
│   │   ├── users.ts     # 用户管理
│   │   ├── upload.ts    # 文件上传
│   │   └── admin.ts     # 管理功能
│   ├── middleware/      # 中间件
│   │   ├── auth.ts      # 认证中间件
│   │   ├── cors.ts      # CORS 处理
│   │   ├── cache.ts     # 缓存中间件
│   │   └── logger.ts    # 日志中间件
│   ├── lib/            # 工具库
│   │   ├── db/         # 数据库相关
│   │   │   ├── schema.ts    # 数据库模式
│   │   │   ├── migrate.ts   # 迁移脚本
│   │   │   └── seed.ts      # 种子数据
│   │   ├── supabase.ts     # Supabase 客户端
│   │   ├── cache.ts        # 缓存工具
│   │   ├── ai.ts          # AI 服务集成
│   │   └── utils.ts       # 通用工具
│   ├── types/          # 类型定义
│   │   ├── auth.ts     # 认证类型
│   │   ├── api.ts      # API 类型
│   │   └── env.ts      # 环境变量类型
│   └── index.ts        # 应用入口
├── test/               # 测试文件
├── migrations/         # 数据库迁移
├── wrangler.toml      # Cloudflare 配置
├── drizzle.config.ts  # Drizzle 配置
├── vitest.config.ts   # 测试配置
├── biome.jsonc        # 代码规范配置
└── package.json       # 项目配置
```

## 🚀 部署指南

### 1. Cloudflare Workers 部署

```bash
# 登录 Cloudflare
pnpm wrangler login

# 部署到生产环境
pnpm deploy
```

### 2. 环境变量设置

```bash
# 设置生产环境变量
pnpm wrangler secret put SUPABASE_URL
pnpm wrangler secret put SUPABASE_SERVICE_ROLE_KEY
pnpm wrangler secret put DATABASE_URL
```

### 调试技巧

```bash
# 查看 Workers 日志
pnpm wrangler tail

# 本地调试
pnpm dev --local

# 测试 API 接口
pnpm test:api
```

## 📚 相关文档

- [Hono 官方文档](https://hono.dev/)
- [Cloudflare Workers 文档](https://developers.cloudflare.com/workers/)
- [Supabase 文档](https://supabase.com/docs)
- [Drizzle ORM 文档](https://orm.drizzle.team/)

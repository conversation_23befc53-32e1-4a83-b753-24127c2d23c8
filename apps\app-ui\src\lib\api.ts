import { isCapacitorEnvironment } from './utils';

// API基础URL配置
const API_URL = isCapacitorEnvironment()
  ? import.meta.env.VITE_APP_API_URL
  : import.meta.env.VITE_WEB_API_URL;

// 错误类型
export class ApiError extends Error {
  status: number;

  constructor(message: string, status: number) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
  }
}

// API请求选项接口
interface RequestOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
  credentials?: RequestCredentials;
}

// API响应处理工具
class ApiClient {
  // 基础请求方法
  async request<T>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    // 默认请求配置
    const defaultOptions: RequestOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include', // 自动携带cookies
    };

    // 合并选项
    const mergedOptions: RequestOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers,
      },
    };

    // 如果有body，序列化为JSON
    if (mergedOptions.body && typeof mergedOptions.body === 'object') {
      mergedOptions.body = JSON.stringify(mergedOptions.body);
    }

    try {
      // 发送请求
      const response = await fetch(
        `${API_URL}${endpoint}`,
        mergedOptions as RequestInit,
      );

      // 检查响应状态
      if (!response.ok) {
        let errorMessage = `API Error: ${response.status} ${response.statusText}`;

        // 尝试解析错误响应
        try {
          const errorData = await response.json();
          errorMessage = errorData.message || errorMessage;
        } catch (e) {
          // 解析错误响应失败，保持默认错误信息
        }

        throw new ApiError(errorMessage, response.status);
      }

      // 检查响应内容类型
      const contentType = response.headers.get('content-type');
      if (contentType?.includes('application/json')) {
        return (await response.json()) as T;
      }

      return (await response.text()) as unknown as T;
    } catch (error) {
      if (error instanceof ApiError) {
        throw error;
      }

      // 处理其他错误（网络错误等）
      throw new ApiError(
        error instanceof Error ? error.message : 'Unknown API error',
        0,
      );
    }
  }

  // GET请求快捷方法
  async get<T>(
    endpoint: string,
    options: Omit<RequestOptions, 'method'> = {},
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  // POST请求快捷方法
  async post<T>(
    endpoint: string,
    data?: any,
    options: Omit<RequestOptions, 'method' | 'body'> = {},
  ): Promise<T> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data,
    });
  }

  // PUT请求快捷方法
  async put<T>(
    endpoint: string,
    data?: any,
    options: Omit<RequestOptions, 'method' | 'body'> = {},
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body: data });
  }

  // DELETE请求快捷方法
  async delete<T>(
    endpoint: string,
    options: Omit<RequestOptions, 'method'> = {},
  ): Promise<T> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }
}

// 导出API客户端实例
export const api = new ApiClient();

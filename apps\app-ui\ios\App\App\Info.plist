<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
	<dict>
		<key>CFBundleDevelopmentRegion</key>
		<string>en</string>
		<key>CFBundleDisplayName</key>
		<string>RC Demo App</string>
		<key>CFBundleExecutable</key>
		<string>$(EXECUTABLE_NAME)</string>
		<key>CFBundleIdentifier</key>
		<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
		<key>CFBundleInfoDictionaryVersion</key>
		<string>6.0</string>
		<key>CFBundleName</key>
		<string>$(PRODUCT_NAME)</string>
		<key>CFBundlePackageType</key>
		<string>APPL</string>
		<key>CFBundleShortVersionString</key>
		<string>$(MARKETING_VERSION)</string>
		<key>CFBundleVersion</key>
		<string>$(CURRENT_PROJECT_VERSION)</string>
		<key>LSRequiresIPhoneOS</key>
		<true/>

		<!-- URL Scheme 配置 -->
		<key>CFBundleURLTypes</key>
		<array>
			<dict>
				<key>CFBundleURLName</key>
				<string>com.pleasurehub.app.deeplink</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>com.pleasurehub.app</string>
				</array>
			</dict>
			<dict>
				<key>CFBundleURLName</key>
				<string>com.pleasurehub.app.https</string>
				<key>CFBundleURLSchemes</key>
				<array>
					<string>https</string>
				</array>
			</dict>
		</array>

		<key>UILaunchStoryboardName</key>
		<string>LaunchScreen</string>
		<key>UIMainStoryboardFile</key>
		<string>Main</string>
		<key>UIRequiredDeviceCapabilities</key>
		<array>
			<string>armv7</string>
		</array>
		<key>UISupportedInterfaceOrientations</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UISupportedInterfaceOrientations~ipad</key>
		<array>
			<string>UIInterfaceOrientationPortrait</string>
			<string>UIInterfaceOrientationPortraitUpsideDown</string>
			<string>UIInterfaceOrientationLandscapeLeft</string>
			<string>UIInterfaceOrientationLandscapeRight</string>
		</array>
		<key>UIViewControllerBasedStatusBarAppearance</key>
		<true/>

		<key>NSBluetoothAlwaysUsageDescription</key>
		<string>应用需要使用蓝牙功能来发送设备控制命令</string>
		<key>NSBluetoothPeripheralUsageDescription</key>
		<string>应用需要使用蓝牙功能来发送设备控制命令</string>

		<key>NSMicrophoneUsageDescription</key>
		<string>应用需要访问麦克风来录制语音消息</string>

		<!-- 相机权限（用于二维码扫描） -->
		<key>NSCameraUsageDescription</key>
		<string>应用需要访问摄像头来扫描设备二维码</string>

		<!-- @capacitor-community/media 插件所需权限 -->
		<key>NSPhotoLibraryUsageDescription</key>
		<string>应用需要访问相册来保存生成的图片和视频</string>
		<key>NSPhotoLibraryAddUsageDescription</key>
		<string>应用需要将生成的图片和视频保存到相册中</string>

		<key>UIBackgroundModes</key>
		<array>
			<string>bluetooth-peripheral</string>
		</array>
	</dict>
</plist>

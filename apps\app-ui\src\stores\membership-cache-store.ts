import { create } from 'zustand'
import {
  membershipService,
  type MembershipStatus,
  type UserPoints
} from '@/api/services/membership'

// 缓存数据结构
interface MembershipCacheData {
  membershipStatus: MembershipStatus | null
  userPoints: UserPoints | null
  lastFetchTime: number | null
  isInitialized: boolean
}

// Store状态接口
interface MembershipCacheState {
  // 缓存数据
  cache: MembershipCacheData

  // 加载状态
  isLoading: boolean
  isRefreshing: boolean
  error: string | null

  // 请求状态管理
  fetchPromise: Promise<void> | null
}

// Store操作接口
interface MembershipCacheActions {
  // 缓存有效性检查
  isCacheValid: () => boolean

  // 获取数据（优先使用缓存）
  getMembershipStatus: () => MembershipStatus | null
  getUserPoints: () => UserPoints | null

  // 数据获取和刷新
  fetchMembershipData: (forceRefresh?: boolean) => Promise<void>
  refreshMembershipData: () => Promise<void>

  // 异步无感更新
  updateInBackground: () => void

  // 缓存管理
  clearCache: () => void
  resetState: () => void
}

// 缓存配置
const CACHE_EXPIRY_TIME = 5 * 60 * 1000 // 5分钟缓存
const BACKGROUND_UPDATE_INTERVAL = 30 * 1000 // 30秒后台更新间隔

// 创建store
export const useMembershipCacheStore = create<MembershipCacheState & MembershipCacheActions>(
  (set, get) => ({
    // 初始状态
    cache: {
      membershipStatus: null,
      userPoints: null,
      lastFetchTime: null,
      isInitialized: false
    },
    isLoading: false,
    isRefreshing: false,
    error: null,
    fetchPromise: null,

    // 检查缓存是否有效
    isCacheValid: () => {
      const { cache } = get()
      if (!cache.lastFetchTime || !cache.isInitialized) return false
      return Date.now() - cache.lastFetchTime < CACHE_EXPIRY_TIME
    },

    // 获取会员状态（从缓存）
    getMembershipStatus: () => {
      const { cache } = get()
      return cache.membershipStatus
    },

    // 获取用户积分（从缓存）
    getUserPoints: () => {
      const { cache } = get()
      return cache.userPoints
    },

    // 获取会员数据（优先使用缓存）
    fetchMembershipData: async (forceRefresh = false) => {
      const state = get()

      // 如果不强制刷新且缓存有效，直接返回
      if (!forceRefresh && state.isCacheValid()) {
        console.log('🎯 [MembershipCache] 使用缓存数据')
        return
      }

      // 如果正在请求中，返回同一个Promise避免重复请求
      if (state.fetchPromise) {
        console.log('🔄 [MembershipCache] 等待进行中的请求')
        return state.fetchPromise
      }

      const fetchPromise = (async () => {
        try {
          // 设置加载状态
          set(prevState => ({
            isLoading: !prevState.cache.isInitialized,
            isRefreshing: prevState.cache.isInitialized,
            error: null
          }))

          console.log('🔄 [MembershipCache] 开始获取会员数据')

          // 并行请求会员状态和积分信息
          const [membershipResponse, pointsResponse] = await Promise.all([
            membershipService.getMembershipStatus(),
            membershipService.getPoints()
          ])

          // 处理响应
          const membershipStatus = membershipResponse.success ? membershipResponse.data : null
          const userPoints = pointsResponse.success ? pointsResponse.data : null

          // 更新缓存
          set({
            cache: {
              membershipStatus,
              userPoints,
              lastFetchTime: Date.now(),
              isInitialized: true
            },
            isLoading: false,
            isRefreshing: false,
            error: null,
            fetchPromise: null
          })

          console.log('✅ [MembershipCache] 会员数据获取成功')

          // 处理错误情况
          if (!membershipResponse.success || !pointsResponse.success) {
            const errorMsg = membershipResponse.error || pointsResponse.error || '获取会员数据失败'
            console.warn('⚠️ [MembershipCache] 部分数据获取失败:', errorMsg)
          }
        } catch (error) {
          console.error('❌ [MembershipCache] 获取会员数据失败:', error)

          set({
            isLoading: false,
            isRefreshing: false,
            error: error instanceof Error ? error.message : '获取会员数据失败',
            fetchPromise: null
          })

          // 如果是首次加载失败，仍然标记为已初始化，避免阻塞UI
          if (!get().cache.isInitialized) {
            set(prevState => ({
              cache: {
                ...prevState.cache,
                isInitialized: true
              }
            }))
          }
        }
      })()

      // 保存Promise引用
      set({ fetchPromise })

      return fetchPromise
    },

    // 强制刷新数据
    refreshMembershipData: async () => {
      console.log('🔄 [MembershipCache] 强制刷新会员数据')
      return get().fetchMembershipData(true)
    },

    // 异步无感更新（后台更新）
    updateInBackground: () => {
      const state = get()

      // 如果正在加载或刷新中，跳过后台更新
      if (state.isLoading || state.isRefreshing || state.fetchPromise) {
        console.log('🔄 [MembershipCache] 跳过后台更新（正在进行其他请求）')
        return
      }

      console.log('🔄 [MembershipCache] 开始后台更新')

      // 异步更新，不阻塞UI
      setTimeout(async () => {
        try {
          await get().fetchMembershipData(true)
          console.log('✅ [MembershipCache] 后台更新完成')
        } catch (error) {
          console.warn('⚠️ [MembershipCache] 后台更新失败:', error)
          // 后台更新失败不影响用户体验，只记录日志
        }
      }, 100) // 短暂延迟，让UI先渲染
    },

    // 清除缓存
    clearCache: () => {
      console.log('🧹 [MembershipCache] 清除缓存')
      set({
        cache: {
          membershipStatus: null,
          userPoints: null,
          lastFetchTime: null,
          isInitialized: false
        },
        error: null
      })
    },

    // 重置状态
    resetState: () => {
      console.log('🔄 [MembershipCache] 重置状态')
      set({
        cache: {
          membershipStatus: null,
          userPoints: null,
          lastFetchTime: null,
          isInitialized: false
        },
        isLoading: false,
        isRefreshing: false,
        error: null,
        fetchPromise: null
      })
    }
  })
)

// 便捷的数据获取hooks
export const useMembershipData = () => {
  const store = useMembershipCacheStore()

  return {
    // 数据
    membershipStatus: store.getMembershipStatus(),
    userPoints: store.getUserPoints(),

    // 状态
    isLoading: store.isLoading,
    isRefreshing: store.isRefreshing,
    error: store.error,
    isInitialized: store.cache.isInitialized,
    isCacheValid: store.isCacheValid(),

    // 操作
    fetchData: store.fetchMembershipData,
    refreshData: store.refreshMembershipData,
    updateInBackground: store.updateInBackground,
    clearCache: store.clearCache
  }
}

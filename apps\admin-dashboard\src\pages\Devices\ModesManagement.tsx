import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Input,
  Select,
  message,
  Modal,
  Form,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Badge,
  InputNumber,
  Divider,
  Progress,
  Timeline
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  BugOutlined,
  CopyOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { DeviceMode, PatternStep, ModeTemplate, ModePreview } from '@/services/device-modes'
import { modeService } from '@/services/device-modes'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

const ModesManagement: React.FC = () => {
  const [modes, setModes] = useState<DeviceMode[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [previewModalVisible, setPreviewModalVisible] = useState(false)
  const [templateModalVisible, setTemplateModalVisible] = useState(false)
  const [editingMode, setEditingMode] = useState<DeviceMode | null>(null)
  const [previewMode, setPreviewMode] = useState<ModePreview | null>(null)
  const [templates, setTemplates] = useState<ModeTemplate[]>([])
  const [form] = Form.useForm()

  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    isActive: undefined as boolean | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0
  })

  useEffect(() => {
    loadModes()
    loadTemplates()
  }, [currentPage, pageSize, searchParams])

  const loadModes = async () => {
    try {
      setLoading(true)

      const response = await modeService.getModes({
        page: currentPage,
        pageSize,
        ...searchParams
      })

      if (response.success && response.data) {
        setModes(response.data.data)
        setTotal(response.data.total)

        // 计算统计数据
        const activeCount = response.data.data.filter(item => item.isActive).length
        setStats({
          total: response.data.total,
          active: activeCount,
          inactive: response.data.total - activeCount
        })
      } else {
        message.error(response.message || '获取设备模式列表失败')
      }
    } catch (error) {
      console.error('获取设备模式列表失败:', error)
      message.error('获取设备模式列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadTemplates = async () => {
    try {
      const response = await modeService.getTemplates()
      if (response.success && response.data) {
        setTemplates(response.data)
      }
    } catch (error) {
      console.error('获取模板失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      isActive: undefined
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingMode(null)
    form.resetFields()
    form.setFieldsValue({
      isActive: true,
      pattern: [{ duration: 1000, intensity: 1, description: '第一步' }]
    })
    setModalVisible(true)
  }

  const handleCreateFromTemplate = (template: ModeTemplate) => {
    setEditingMode(null)
    form.resetFields()
    form.setFieldsValue({
      name: template.name,
      description: template.description,
      pattern: template.pattern,
      isActive: true
    })
    setTemplateModalVisible(false)
    setModalVisible(true)
  }

  const handleEdit = (mode: DeviceMode) => {
    setEditingMode(mode)
    form.setFieldsValue({
      name: mode.name,
      description: mode.description,
      pattern: mode.pattern,
      isActive: mode.isActive
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingMode) {
        const response = await modeService.updateMode(editingMode.id, values)
        if (response.success) {
          message.success('设备模式更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await modeService.createMode(values)
        if (response.success) {
          message.success('设备模式创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }

      setModalVisible(false)
      loadModes()
    } catch (error) {
      console.error('操作失败:', error)
      message.error(editingMode ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await modeService.deleteMode(id)
      if (response.success) {
        message.success('设备模式删除成功')
        loadModes()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await modeService.toggleStatus(id, isActive)
      if (response.success) {
        message.success(isActive ? '模式已启用' : '模式已禁用')
        loadModes()
      } else {
        message.error(response.message || '状态切换失败')
      }
    } catch (error) {
      console.error('状态切换失败:', error)
      message.error('状态切换失败')
    }
  }

  const handlePreview = async (mode: DeviceMode) => {
    try {
      const response = await modeService.getModePreview(mode.id)
      if (response.success && response.data) {
        setPreviewMode(response.data)
        setPreviewModalVisible(true)
      } else {
        message.error('获取模式预览失败')
      }
    } catch (error) {
      console.error('获取模式预览失败:', error)
      message.error('获取模式预览失败')
    }
  }

  const formatDuration = (ms: number) => {
    if (ms < 1000) return `${ms}ms`
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`
    return `${(ms / 60000).toFixed(1)}min`
  }

  const getPatternSummary = (pattern: PatternStep[]) => {
    if (!pattern || pattern.length === 0) return '无'

    const totalDuration = pattern.reduce((sum, step) => sum + step.duration, 0)
    const maxIntensity = Math.max(...pattern.map(step => step.intensity))
    const minIntensity = Math.min(...pattern.map(step => step.intensity))

    return `${pattern.length}步 / ${formatDuration(
      totalDuration
    )} / ${minIntensity}-${maxIntensity}级`
  }

  const columns: ColumnsType<DeviceMode> = [
    {
      title: '模式信息',
      key: 'modeInfo',
      render: (_, record) => (
        <Space>
          <PlayCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>{record.description || '无描述'}</div>
          </div>
        </Space>
      )
    },
    {
      title: '模式规律',
      key: 'pattern',
      render: (_, record) => (
        <div>
          <Text style={{ fontSize: '12px' }}>{getPatternSummary(record.pattern)}</Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: (isActive, record) => (
        <Switch
          checked={isActive}
          onChange={checked => handleToggleStatus(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="预览模式">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handlePreview(record)} />
          </Tooltip>
          <Tooltip title="编辑">
            <Button type="link" icon={<EditOutlined />} onClick={() => handleEdit(record)} />
          </Tooltip>
          <Popconfirm
            title="确定删除这个设备模式吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="link" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        <PlayCircleOutlined /> 设备模式管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总模式数"
              value={stats.total}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic title="启用模式" value={stats.active} valueStyle={{ color: '#1890ff' }} />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic title="禁用模式" value={stats.inactive} valueStyle={{ color: '#ff4d4f' }} />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索模式名称或描述"
            style={{ width: 240 }}
            value={searchParams.keyword}
            onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={value => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button icon={<BugOutlined />} onClick={() => setTemplateModalVisible(true)}>
            从模板创建
          </Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增模式
          </Button>
        </Space>
      </Card>

      {/* 模式列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={modes}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 创建/编辑模式模态框 */}
      <Modal
        title={editingMode ? '编辑设备模式' : '新增设备模式'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Form.Item
            name="name"
            label="模式名称"
            rules={[{ required: true, message: '请输入模式名称' }]}
          >
            <Input placeholder="例如：波浪模式" />
          </Form.Item>

          <Form.Item name="description" label="模式描述">
            <TextArea rows={2} placeholder="描述这个模式的特点和效果..." />
          </Form.Item>

          <Form.Item
            name="pattern"
            label="模式规律"
            rules={[{ required: true, message: '请配置模式规律' }]}
          >
            <Form.List name="pattern">
              {(fields, { add, remove }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => (
                    <Card key={key} size="small" style={{ marginBottom: 8 }}>
                      <Row gutter={16}>
                        <Col span={6}>
                          <Form.Item
                            {...restField}
                            name={[name, 'duration']}
                            label="持续时间(ms)"
                            rules={[{ required: true, message: '请输入持续时间' }]}
                          >
                            <InputNumber min={100} max={60000} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={6}>
                          <Form.Item
                            {...restField}
                            name={[name, 'intensity']}
                            label="强度等级"
                            rules={[{ required: true, message: '请输入强度等级' }]}
                          >
                            <InputNumber min={0} max={10} style={{ width: '100%' }} />
                          </Form.Item>
                        </Col>
                        <Col span={10}>
                          <Form.Item {...restField} name={[name, 'description']} label="步骤描述">
                            <Input placeholder="描述这一步的作用" />
                          </Form.Item>
                        </Col>
                        <Col span={2}>
                          <Button
                            type="link"
                            danger
                            onClick={() => remove(name)}
                            icon={<DeleteOutlined />}
                            style={{ marginTop: 30 }}
                          />
                        </Col>
                      </Row>
                    </Card>
                  ))}
                  <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                    添加步骤
                  </Button>
                </>
              )}
            </Form.List>
          </Form.Item>

          <Form.Item name="isActive" label="启用状态" valuePropName="checked" initialValue={true}>
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingMode ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 模式预览模态框 */}
      <Modal
        title={`模式预览 - ${previewMode?.name}`}
        open={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={null}
        width={800}
      >
        {previewMode && (
          <div>
            {/* 统计信息 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic
                  title="总时长"
                  value={formatDuration(previewMode.statistics.totalDuration)}
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic title="总步数" value={previewMode.statistics.totalSteps} suffix="步" />
              </Col>
              <Col span={6}>
                <Statistic
                  title="最大强度"
                  value={previewMode.statistics.maxIntensity}
                  suffix="级"
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="平均强度"
                  value={previewMode.statistics.avgIntensity}
                  precision={1}
                  suffix="级"
                />
              </Col>
            </Row>

            <Divider />

            {/* 时间线预览 */}
            <Title level={5}>执行时间线</Title>
            <Timeline>
              {previewMode.pattern.map((step: PatternStep, index: number) => (
                <Timeline.Item key={index} dot={<Badge count={step.intensity} />}>
                  <div>
                    <strong>{step.description || `步骤 ${index + 1}`}</strong>
                    <div style={{ color: '#666' }}>
                      持续 {formatDuration(step.duration)}，强度 {step.intensity} 级
                    </div>
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>

            {/* 强度图表预览 */}
            <Divider />
            <Title level={5}>强度变化图</Title>
            <div style={{ padding: '20px 0' }}>
              {previewMode.pattern.map((step: PatternStep, index: number) => (
                <div key={index} style={{ display: 'inline-block', marginRight: 8 }}>
                  <Progress
                    type="circle"
                    size={60}
                    percent={(step.intensity / 10) * 100}
                    format={() => step.intensity}
                  />
                  <div style={{ textAlign: 'center', fontSize: '12px', marginTop: 4 }}>
                    {formatDuration(step.duration)}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Modal>

      {/* 模板选择模态框 */}
      <Modal
        title="选择模式模板"
        open={templateModalVisible}
        onCancel={() => setTemplateModalVisible(false)}
        footer={null}
        width={800}
      >
        <Row gutter={16}>
          {templates.map(template => (
            <Col span={12} key={template.id} style={{ marginBottom: 16 }}>
              <Card
                size="small"
                title={template.name}
                extra={
                  <Button
                    type="primary"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => handleCreateFromTemplate(template)}
                  >
                    使用模板
                  </Button>
                }
              >
                <div style={{ marginBottom: 8 }}>
                  <Text type="secondary">{template.description}</Text>
                </div>
                <div style={{ fontSize: '12px', color: '#666' }}>
                  {getPatternSummary(template.pattern)}
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </Modal>
    </div>
  )
}

export default ModesManagement

export default {
  plugins: {
    // CSS @layer 兼容性插件 - 必须放在最前面
    '@csstools/postcss-cascade-layers': {},

    // 添加 CSS @import 处理
    'postcss-import': {},

    // 嵌套规则支持
    'postcss-nested': {},

    // 增强的 autoprefixer 处理 - 专门针对 Android 12 及以下
    autoprefixer: {
      overrideBrowserslist: [
        'Chrome >= 75', // Android 10 WebView (更保守的版本)
        'Android >= 10', // Android 10+
        'Safari >= 12', // iOS 兼容性
        'Firefox >= 68', // Firefox 兼容性
        'last 3 versions', // 增加版本覆盖
        '> 0.5%', // 降低使用率阈值
        'not dead'
      ],
      flexbox: 'no-2009', // 禁用旧的 flexbox 语法
      grid: 'autoplace', // 启用 grid 自动放置
      add: true, // 添加前缀
      remove: false, // 保留现有前缀
      // 强制添加特定前缀
      supports: false, // 禁用 @supports 查询转换，我们手动处理
      cascade: true // 保持层叠样式
    },

    // 简化的 PostCSS Preset Env - 针对 Android 兼容性
    'postcss-preset-env': {
      stage: 2, // 使用更稳定的 Stage 2 特性
      features: {
        'custom-properties': {
          preserve: true
        },
        'color-functional-notation': {
          preserve: true
        },
        'color-mix': false, // 禁用 color-mix 转换，使用我们的 fallback
        'custom-media-queries': true,
        'logical-properties-and-values': true,
        'media-query-ranges': true,
        'nesting-rules': true,
        'focus-visible-pseudo-class': false, // 禁用，避免复杂选择器转换问题
        'focus-within-pseudo-class': false, // 禁用，避免复杂选择器转换问题
        'is-pseudo-class': false, // 禁用 :is() 转换，避免复杂选择器问题
        'not-pseudo-class': false, // 禁用 :not() 转换，避免复杂选择器问题
        'double-position-gradients': true
      },
      autoprefixer: false, // 避免与 autoprefixer 冲突
      // 明确针对 Android 12 及以下的浏览器
      browsers: ['Chrome >= 75', 'Android >= 10', 'Safari >= 12', 'Firefox >= 68']
    },

    // 增强的 CSS 变量 fallback 插件
    'postcss-custom-properties': {
      preserve: true, // 保留原始 CSS 变量
      fallback: true // 生成 fallback 值
    },

    // 增强的 calc() 兼容性处理
    'postcss-calc': {
      preserve: true, // 保留原始 calc()
      precision: 3, // 计算精度
      // 为 Android 设备优化计算
      warnWhenCannotResolve: false
    }
  }
}

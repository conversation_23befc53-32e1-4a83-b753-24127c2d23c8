// hero.ts - HeroUI 主题配置
import { heroui } from '@heroui/react'

export default heroui({
  addCommonColors: true,
  // 确保与 globals.css 中的颜色变量保持一致
  themes: {
    light: {
      colors: {
        // 使用与 CSS 变量一致的颜色值
        primary: {
          DEFAULT: '#e91e63',
          foreground: '#ffffff',
          50: '#fce4ec',
          100: '#f8bbd9',
          200: '#f48fb1',
          300: '#f06292',
          400: '#ec407a',
          500: '#e91e63',
          600: '#d81b60',
          700: '#c2185b',
          800: '#ad1457',
          900: '#880e4f'
        },
        secondary: {
          DEFAULT: '#9c27b0',
          foreground: '#ffffff',
          50: '#f3e8ff',
          100: '#e9d5ff',
          200: '#d8b4fe',
          300: '#c084fc',
          400: '#a855f7',
          500: '#9c27b0',
          600: '#7c3aed',
          700: '#6d28d9',
          800: '#5b21b6',
          900: '#4c1d95'
        }
      }
    },
    dark: {
      colors: {
        // 深色模式颜色配置
        primary: {
          DEFAULT: '#f06292',
          foreground: '#ffffff',
          50: '#880e4f',
          100: '#ad1457',
          200: '#c2185b',
          300: '#d81b60',
          400: '#e91e63',
          500: '#f06292',
          600: '#f48fb1',
          700: '#f8bbd9',
          800: '#fce4ec',
          900: '#fef7f0'
        },
        secondary: {
          DEFAULT: '#ba68c8',
          foreground: '#ffffff',
          50: '#4c1d95',
          100: '#5b21b6',
          200: '#6d28d9',
          300: '#7c3aed',
          400: '#9c27b0',
          500: '#ba68c8',
          600: '#c084fc',
          700: '#d8b4fe',
          800: '#e9d5ff',
          900: '#f3e8ff'
        }
      }
    }
  }
})

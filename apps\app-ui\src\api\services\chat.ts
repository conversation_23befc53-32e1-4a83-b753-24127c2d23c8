import { apiClient } from '../client'

// 聊天消息类型
export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  parts: Array<{
    type: 'text'
    text: string
  }>
  attachments?: Array<{
    url: string
    name: string
    contentType: string
    metadata?: any
  }>
  createdAt: Date
}

// 为Chat组件定义UI消息类型
export interface TextUIPart {
  type: 'text'
  text: string
}

export interface UIMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  createdAt: Date | string
  parts: TextUIPart[]
  experimental_attachments?: Array<{
    url: string
    name: string
    contentType: string
  }>
}

// 聊天会话类型
export interface ChatConversation {
  id: string
  title: string
  characterId?: string
  createdAt: string
  updatedAt: string
  meta?: {
    engine: string
    version: string
  }
}

// 聊天历史类型
export interface ChatHistory {
  id: string
  title: string
  roleId: string
  userId: string
  createdAt: string
  updatedAt: string
  lastMessageAt: string
  messageCount: number
  visibility: 'public' | 'private'
  lastMessage: string
}

// LangChain 模型类型
export interface LangChainModel {
  id: string
  name: string
  description: string
  maxTokens: number
  features: string[]
}

// LangChain 系统状态
export interface LangChainStatus {
  available: boolean
  version: string
  features: {
    streaming: boolean
    multiModal: boolean
    memory: boolean
    sharedMemory: boolean
  }
  models: string[]
  message?: string
}

// ChatV2 API 服务（LangChain 系统）
export const chatV2Service = {
  // 获取聊天消息列表
  async getMessages(chatId: string) {
    const response = await apiClient.get<{
      success: boolean
      data: {
        messages: Message[]
        chatExists: boolean
      }
    }>(`/api/chatv2/conversations/${chatId}/messages`)

    if (response.success && response.data) {
      return {
        messages: response.data.messages || [],
        chatExists: response.data.chatExists
      }
    }
    return {
      messages: [],
      chatExists: false
    }
  }
}

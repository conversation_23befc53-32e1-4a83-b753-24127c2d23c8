import { apiClient } from '../client'

// TTS2 请求和响应类型
export interface GenerateAudioRequest {
  text: string
  messageId?: string
  chatId?: string
  voiceModelId?: string // 声音模型ID
}

export interface GenerateAudioResponse {
  success: boolean
  data: {
    audioUrl: string
    status: string
  }
  message?: string
}

export interface VoicesResponse {
  success: boolean
  data: {
    voices: Array<{
      voice_id: string
      name: string
      category: string
      description?: string
    }>
  }
  message?: string
}

// TTS2 服务类
class TTS2Service {
  /**
   * 生成音频（同步）
   */
  async generateAudio(request: GenerateAudioRequest): Promise<GenerateAudioResponse> {
    try {
      const response = await apiClient.post<GenerateAudioResponse>('/api/tts2/generate', request)
      return response
    } catch (error) {
      console.error('TTS2 生成音频失败:', error)
      throw error
    }
  }

  /**
   * 获取可用声音列表
   */
  async getVoices(): Promise<VoicesResponse> {
    try {
      const response = await apiClient.get<VoicesResponse>('/api/tts2/voices')
      return response
    } catch (error) {
      console.error('获取TTS2声音列表失败:', error)
      throw error
    }
  }
}

// 导出服务实例
export const tts2Service = new TTS2Service()

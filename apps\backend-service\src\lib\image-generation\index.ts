import type { Env } from '@/types/env';
import type {
  GenerateImageRequest,
  ImageGenerationTask,
  GeneratorType,
} from '@/types/image-generation';
import { YunwuGenerator } from './generators/yunwu';
import { Insa3DGenerator } from './generators/insa3d';
import { TaskManager } from './task-manager';

/**
 * 统一的图片生成服务
 */
export class ImageGenerationService {
  private taskManager: TaskManager;

  constructor(private env: Env) {
    this.taskManager = new TaskManager(env);
  }

  /**
   * 同步生成图片（云雾AI）
   * 直接返回图片URL
   */
  async generateImageSync(request: GenerateImageRequest): Promise<string> {
    const generator = new YunwuGenerator(this.env);
    return await generator.generateImage(request);
  }

  /**
   * 异步生成图片（Insa3D）
   * 返回任务ID，需要轮询状态
   */
  async generateImageAsync(
    request: GenerateImageRequest,
    generatorType: 'insa3d-v2' | 'insa3d-v3' = 'insa3d-v2'
  ): Promise<string> {
    const version = generatorType === 'insa3d-v3' ? 'v3' : 'v2';
    const generator = new Insa3DGenerator(this.env, version);

    // 启动生成任务
    const task = await generator.startGenerationTask(request);

    // 保存任务状态
    await this.taskManager.saveTask(task);

    return task.taskId;
  }

  /**
   * 查询任务状态
   */
  async getTaskStatus(taskId: string): Promise<ImageGenerationTask | null> {
    // 先从缓存获取任务信息
    const cachedTask = await this.taskManager.getTask(taskId);

    // 如果任务已完成或失败，直接返回缓存结果
    if (cachedTask && (cachedTask.status === 'completed' || cachedTask.status === 'failed')) {
      return cachedTask;
    }

    // 如果任务还在进行中或缓存中没有任务，查询最新状态
    try {
      // 尝试使用v3生成器查询（优先，因为新接口默认使用v3）
      let updatedTask: ImageGenerationTask | null = null;

      try {
        const generatorV3 = new Insa3DGenerator(this.env, 'v3');
        updatedTask = await generatorV3.getTaskStatus(taskId);
      } catch (v3Error) {
        console.log('v3查询失败，尝试v2:', v3Error);
        // 如果v3查询失败，尝试v2
        const generatorV2 = new Insa3DGenerator(this.env, 'v2');
        updatedTask = await generatorV2.getTaskStatus(taskId);
      }

      if (updatedTask) {
        // 更新缓存
        await this.taskManager.saveTask(updatedTask);
        return updatedTask;
      }

      // 如果API查询也失败，返回缓存的任务状态（如果有的话）
      return cachedTask;
    } catch (error) {
      console.error('查询任务状态失败:', error);
      // 返回缓存的任务状态（如果有的话）
      return cachedTask;
    }
  }

  /**
   * 等待任务完成（用于同步等待异步任务）
   */
  async waitForTaskCompletion(
    taskId: string,
    maxWaitTime = 300000, // 5分钟
    pollInterval = 3000 // 3秒
  ): Promise<ImageGenerationTask> {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      const task = await this.getTaskStatus(taskId);

      if (!task) {
        throw new Error('任务不存在');
      }

      if (task.status === 'completed' || task.status === 'failed') {
        return task;
      }

      // 等待下次轮询
      await new Promise((resolve) => setTimeout(resolve, pollInterval));
    }

    throw new Error('任务等待超时');
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<void> {
    await this.taskManager.deleteTask(taskId);
  }

  /**
   * 根据生成器类型生成图片
   */
  async generateImage(
    request: GenerateImageRequest,
    generatorType: GeneratorType = 'yunwu',
    waitForCompletion = false
  ): Promise<{ imageUrl?: string; taskId?: string; task?: ImageGenerationTask }> {
    switch (generatorType) {
      case 'yunwu':
        // 云雾AI同步生成
        const imageUrl = await this.generateImageSync(request);
        return { imageUrl };

      case 'insa3d-v2':
      case 'insa3d-v3':
        // Insa3D异步生成
        const taskId = await this.generateImageAsync(request, generatorType);

        if (waitForCompletion) {
          // 等待任务完成
          const task = await this.waitForTaskCompletion(taskId);
          return { task, taskId, imageUrl: task.imageUrl };
        } else {
          // 立即返回任务ID
          return { taskId };
        }

      default:
        throw new Error(`不支持的生成器类型: ${generatorType}`);
    }
  }
}

// 导出类型和服务
export * from './generators/yunwu';
export * from './generators/insa3d';
export * from './task-manager';
export * from './upload';

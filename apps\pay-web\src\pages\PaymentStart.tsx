/**
 * 支付启动页面
 * 获取订单信息并自动跳转到支付平台
 */

import { useEffect, useState } from 'react'
import { useSearchParams, useNavigate } from 'react-router'
import { Card, CardBody, Button, Spinner, Chip, Divider } from '@heroui/react'
import { paymentAPI } from '@/api/payment'
import { redirectToPayment } from '@/utils/payment-redirect'
import type { PaymentOrderInfo } from '@/types/payment'

export default function PaymentStart() {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')
  const [orderInfo, setOrderInfo] = useState<PaymentOrderInfo | null>(null)
  const [redirecting, setRedirecting] = useState(false)

  const orderId = searchParams.get('orderId')

  useEffect(() => {
    if (!orderId) {
      setError('订单ID不存在')
      setLoading(false)
      return
    }

    loadOrderInfo()
  }, [orderId]) // loadOrderInfo在函数内部定义，依赖项已包含orderId

  const loadOrderInfo = async () => {
    try {
      setLoading(true)
      setError('')

      console.log('🔍 [PAY-START] 加载订单信息:', orderId)
      const info = await paymentAPI.getOrderInfo(orderId!)

      // 检查订单状态
      if (info.status === 'expired') {
        setError('订单已过期，请重新下单')
        return
      }

      if (info.status === 'paid') {
        // 如果已支付，直接跳转到结果页
        navigate(`/pay/result?orderId=${orderId}&status=success`)
        return
      }

      if (info.status !== 'pending') {
        setError(`订单状态异常: ${info.status}`)
        return
      }

      setOrderInfo(info)

      // 自动跳转到支付平台
      setTimeout(() => {
        handlePaymentRedirect(info)
      }, 2000) // 2秒后自动跳转，让用户看到订单信息
    } catch (error) {
      console.error('❌ [PAY-START] 加载订单信息失败:', error)
      setError(error instanceof Error ? error.message : '加载订单信息失败')
    } finally {
      setLoading(false)
    }
  }

  const handlePaymentRedirect = async (info: PaymentOrderInfo) => {
    try {
      setRedirecting(true)
      console.log('💳 [PAY-START] 开始跳转到支付平台')

      // 跳转到支付平台
      redirectToPayment(info)
    } catch (error) {
      console.error('❌ [PAY-START] 跳转支付平台失败:', error)
      setError('跳转支付平台失败，请重试')
      setRedirecting(false)
    }
  }

  const handleRetry = () => {
    setError('')
    loadOrderInfo()
  }

  const handleCancel = () => {
    // 可以添加取消订单的逻辑
    window.history.back()
  }

  // 格式化金额显示
  const formatAmount = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  // 格式化过期时间
  const formatExpireTime = (expiresAt: string) => {
    const expireTime = new Date(expiresAt)
    const now = new Date()
    const diffMs = expireTime.getTime() - now.getTime()
    const diffMins = Math.floor(diffMs / 60000)

    if (diffMins <= 0) {
      return '已过期'
    } else if (diffMins < 60) {
      return `${diffMins}分钟后过期`
    } else {
      const hours = Math.floor(diffMins / 60)
      const mins = diffMins % 60
      return `${hours}小时${mins}分钟后过期`
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <Card className="w-full max-w-sm mx-4 bg-gray-900/90 backdrop-blur-xl border-gray-700">
          <CardBody className="text-center py-8">
            <Spinner size="md" color="primary" className="mb-3" />
            <p className="text-white text-sm">加载订单信息</p>
            <p className="text-gray-400 text-xs mt-1">请稍候...</p>
          </CardBody>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800 p-4">
        <Card className="w-full max-w-sm bg-gray-900/90 backdrop-blur-xl border-gray-700">
          <CardBody className="text-center py-6">
            <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-red-900/50 flex items-center justify-center">
              <div className="w-5 h-5 border-2 border-red-400 rounded-full relative">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-1.5 h-1.5 bg-red-400 rounded-full"></div>
                </div>
              </div>
            </div>
            <h2 className="text-lg font-bold text-red-400 mb-2">订单异常</h2>
            <p className="text-gray-300 text-sm mb-6 leading-relaxed">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button
                color="primary"
                size="sm"
                onPress={handleRetry}
                isDisabled={error.includes('过期') || error.includes('已支付')}
                className="px-6"
              >
                重试
              </Button>
              <Button
                variant="bordered"
                size="sm"
                onPress={handleCancel}
                className="px-6 border-gray-600 text-gray-300"
              >
                返回
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    )
  }

  if (!orderInfo) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-3">
      <div className="max-w-sm mx-auto">
        {/* 订单信息卡片 */}
        <Card className="mb-4 bg-gray-900/90 backdrop-blur-xl border-gray-700">
          <CardBody className="p-4">
            <div className="text-center mb-4">
              <h1 className="text-xl font-bold text-white mb-1">确认支付</h1>
              <p className="text-gray-400 text-sm">请核对订单信息</p>
            </div>

            {/* 订单详情 */}
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/50 border border-gray-700">
                <span className="text-gray-400 text-sm">商品</span>
                <span className="text-white text-sm font-medium text-right flex-1 ml-3 truncate">
                  {orderInfo.description}
                </span>
              </div>

              {orderInfo.planInfo && (
                <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/50 border border-gray-700">
                  <span className="text-gray-400 text-sm">套餐</span>
                  <span className="text-white text-sm font-medium text-right flex-1 ml-3 truncate">
                    {orderInfo.planInfo.name}
                  </span>
                </div>
              )}

              <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/50 border border-gray-700">
                <span className="text-gray-400 text-sm">支付方式</span>
                <Chip
                  color={orderInfo.paymentMethod === 'alipay' ? 'primary' : 'success'}
                  size="sm"
                  className="text-xs"
                >
                  {orderInfo.paymentMethod === 'alipay' ? '支付宝' : '微信支付'}
                </Chip>
              </div>

              {orderInfo.isUpgrade && orderInfo.originalAmount && (
                <>
                  <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/30 border border-gray-700/50">
                    <span className="text-gray-500 text-sm">原价</span>
                    <span className="text-gray-500 text-sm line-through">
                      {formatAmount(orderInfo.originalAmount)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 rounded-lg bg-green-900/20 border border-green-700/50">
                    <span className="text-gray-400 text-sm">升级补差</span>
                    <span className="text-green-400 text-sm font-medium">
                      -¥{(orderInfo.originalAmount - orderInfo.amount).toFixed(2)}
                    </span>
                  </div>
                </>
              )}

              <Divider className="bg-gray-700 my-3" />

              <div className="flex justify-between items-center p-4 rounded-xl bg-gradient-to-r from-primary-900/30 to-secondary-900/30 border border-primary-700/50">
                <span className="text-gray-200 font-medium">应付金额</span>
                <span className="text-2xl font-bold bg-gradient-to-r from-primary-400 to-secondary-400 bg-clip-text text-transparent">
                  {formatAmount(orderInfo.amount)}
                </span>
              </div>

              <div className="text-center p-2 rounded-lg bg-orange-900/20 border border-orange-700/50">
                <span className="text-orange-400 text-xs">
                  {formatExpireTime(orderInfo.expiresAt)}
                </span>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="mt-4 space-y-3">
              {redirecting ? (
                <div className="text-center py-4">
                  <Spinner size="sm" color="primary" className="mb-2" />
                  <p className="text-white text-sm font-medium">正在跳转</p>
                  <p className="text-gray-400 text-xs">即将跳转到支付平台</p>
                </div>
              ) : (
                <>
                  <Button
                    color="primary"
                    size="lg"
                    className="w-full font-semibold bg-gradient-to-r from-primary-500 to-secondary-500"
                    onPress={() => handlePaymentRedirect(orderInfo)}
                  >
                    立即支付 {formatAmount(orderInfo.amount)}
                  </Button>
                  <Button
                    variant="bordered"
                    size="md"
                    className="w-full border-gray-600 text-gray-300"
                    onPress={handleCancel}
                  >
                    取消支付
                  </Button>
                </>
              )}
            </div>
          </CardBody>
        </Card>

        {/* 安全提示 */}
        <Card className="bg-gray-900/70 backdrop-blur-xl border-gray-700">
          <CardBody className="p-3">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-blue-900/50 rounded-full flex items-center justify-center flex-shrink-0">
                <div className="w-3 h-3 border border-blue-400 rounded-sm"></div>
              </div>
              <div>
                <h3 className="text-white text-sm font-medium mb-0.5">安全支付</h3>
                <p className="text-gray-400 text-xs leading-relaxed">
                  支付信息经过加密传输，确保交易安全
                </p>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

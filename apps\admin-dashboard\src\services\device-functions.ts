import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

export interface DeviceFunction {
  id: string
  name: string
  key: string
  description?: string
  maxIntensity: number
  isActive: boolean
  createdAt: string
  updatedAt: string
  commandCount?: number
}

export interface DeviceFunctionCommand {
  id: string
  intensity: number
  description?: string
  commandSet: {
    id: string
    name: string
    command: string
    broadcast?: string
    description?: string
  } | null
}

export interface DeviceFunctionDetail extends DeviceFunction {
  commands: DeviceFunctionCommand[]
}

export interface CreateFunctionRequest {
  name: string
  key: string
  description?: string
  maxIntensity?: number
  isActive?: boolean
}

export interface AddFunctionCommandRequest {
  commandSetId: string
  intensity: number
  description?: string
}

export class DeviceFunctionService {
  // 获取设备功能列表
  async getFunctions(params: {
    page?: number
    pageSize?: number
    keyword?: string
    isActive?: boolean
  }): Promise<ApiResponse<PaginatedResponse<DeviceFunction>>> {
    return await apiService.get<PaginatedResponse<DeviceFunction>>('/admin/devices/functions', { params })
  }

  // 获取设备功能详情
  async getFunction(id: string): Promise<ApiResponse<DeviceFunctionDetail>> {
    return await apiService.get<DeviceFunctionDetail>(`/admin/devices/functions/${id}`)
  }

  // 创建设备功能
  async createFunction(data: CreateFunctionRequest): Promise<ApiResponse<DeviceFunction>> {
    return await apiService.post<DeviceFunction>('/admin/devices/functions', data)
  }

  // 更新设备功能
  async updateFunction(id: string, data: Partial<CreateFunctionRequest>): Promise<ApiResponse<DeviceFunction>> {
    return await apiService.put<DeviceFunction>(`/admin/devices/functions/${id}`, data)
  }

  // 删除设备功能
  async deleteFunction(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/devices/functions/${id}`)
  }

  // 为功能添加指令关联
  async addFunctionCommand(functionId: string, data: AddFunctionCommandRequest): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/devices/functions/${functionId}/commands`, data)
  }

  // 删除功能指令关联
  async removeFunctionCommand(functionId: string, commandId: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/devices/functions/${functionId}/commands/${commandId}`)
  }
}

export const functionService = new DeviceFunctionService()
import { memo, useEffect, useState, lazy, Suspense, useRef } from 'react'
import ReactMarkdown, { type Components } from 'react-markdown'
import remarkGfm from 'remark-gfm'
import { Card, CardBody, Skeleton } from '@heroui/react'
import { EnhancedStreamAudioPlayer } from './enhanced-stream-audio-player'
import { useTranslation } from 'react-i18next'

// 🆕 使用统一的图片和视频容器
const ImageContainer = lazy(() =>
  import('./image').then(module => ({ default: module.ImageContainer }))
)
const VideoContainer = lazy(() =>
  import('./video/video-container').then(module => ({ default: module.VideoContainer }))
)

const components: Partial<Components> = {
  pre: ({ children }) => <>{children}</>,
  ol: ({ node, children, ...props }) => {
    return (
      <ol className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ol>
    )
  },
  li: ({ node, children, ...props }) => {
    return (
      <li className="py-1" {...props}>
        {children}
      </li>
    )
  },
  ul: ({ node, children, ...props }) => {
    return (
      <ul className="list-decimal list-outside ml-4" {...props}>
        {children}
      </ul>
    )
  },
  strong: ({ node, children, ...props }) => {
    return (
      <span className="font-semibold" {...props}>
        {children}
      </span>
    )
  },
  a: ({ node, children, ...props }) => {
    return (
      <a className="text-primary hover:underline" target="_blank" rel="noreferrer" {...props}>
        {children}
      </a>
    )
  },
  h1: ({ node, children, ...props }) => {
    return (
      <h1 className="text-3xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h1>
    )
  },
  h2: ({ node, children, ...props }) => {
    return (
      <h2 className="text-2xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h2>
    )
  },
  h3: ({ node, children, ...props }) => {
    return (
      <h3 className="text-xl font-semibold mt-6 mb-2" {...props}>
        {children}
      </h3>
    )
  },
  h4: ({ node, children, ...props }) => {
    return (
      <h4 className="text-lg font-semibold mt-6 mb-2" {...props}>
        {children}
      </h4>
    )
  },
  h5: ({ node, children, ...props }) => {
    return (
      <h5 className="text-base font-semibold mt-6 mb-2" {...props}>
        {children}
      </h5>
    )
  },
  h6: ({ node, children, ...props }) => {
    return (
      <h6 className="text-sm font-semibold mt-6 mb-2" {...props}>
        {children}
      </h6>
    )
  }
}

const remarkPlugins = [remarkGfm]

const NonMemoizedMarkdownV2 = ({ children }: { children: string }) => {
  // 过滤掉设备信息标签
  const filteredContent = children.replace(/<device>.*?<\/device>/gs, '')

  return (
    <ReactMarkdown remarkPlugins={remarkPlugins} components={components}>
      {filteredContent}
    </ReactMarkdown>
  )
}

export const MarkdownV2 = memo(
  NonMemoizedMarkdownV2,
  (prevProps, nextProps) => prevProps.children === nextProps.children
)

// 为故事格式的样式定义CSS类
const storyStyles = `
  /* 打字机光标效果 */
  .typing-cursor-v2 {
    display: inline-block;
    width: 0.5em;
    height: 1em;
    background-color: currentColor;
    margin-left: 2px;
    animation: blinkV2 1s step-end infinite;
    vertical-align: text-bottom;
    box-shadow: 0 0 8px currentColor;
  }
  
  @keyframes blinkV2 {
    0%, 100% { opacity: 1; }
    50% { opacity: 0; }
  }

  /* 流式输出时的容器动画 */
  .streaming-content-v2 {
    animation: fadeInUpV2 0.3s ease-out;
  }

  @keyframes fadeInUpV2 {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* 气泡动画效果 */
  .bubble-enter-v2 {
    animation: bubbleSlideInV2 0.4s ease-out;
  }

  @keyframes bubbleSlideInV2 {
    from {
      opacity: 0;
      transform: translateY(10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* 场景背景样式 - 为后续背景图预留 */
  .scene-background-v2 {
    position: relative;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
`

// 确保样式只注入一次
let stylesInjectedV2 = false

// 当检测到imagePrompt时的回调函数接口
type ImagePromptHandler = (imagePrompt: string) => void

// 当检测到场景时的回调函数接口
type SceneHandler = (sceneDescription: string) => void

/**
 * 解析内容的接口定义 - 新格式
 */
interface ParsedContent {
  scene?: string
  dialogues: string[]
  imagePrompt?: string
  videoPrompt?: string // 新增videoPrompt预留
  error?: string // 新增错误信息
  isComplete: {
    scene: boolean
    dialogue: boolean
    imagePrompt: boolean
    videoPrompt: boolean // 新增
    error: boolean // 新增错误完整性标记
  }
}

/**
 * 智能预判和清理流式内容
 * 检测到标签开头就预判类型，移除标签但保持内容的流式显示
 */
const cleanAndDetectStreamingContent = (
  content: string
): {
  cleanedContent: string
  detectedTags: { type: string; content: string; isComplete: boolean }[]
} => {
  let cleaned = content
  const detectedTags: { type: string; content: string; isComplete: boolean }[] = []

  // 移除设备信息标签
  cleaned = cleaned.replace(/<device>.*?<\/device>/gs, '')

  // 定义标签检测模式
  const tagPatterns = [
    { type: 'scene', openTag: '<scene>', closeTag: '</scene>' },
    { type: 'dialogue', openTag: '<dialogue>', closeTag: '</dialogue>' },
    { type: 'imagePrompt', openTag: '<imagePrompt>', closeTag: '</imagePrompt>' },
    { type: 'videoPrompt', openTag: '<videoPrompt>', closeTag: '</videoPrompt>' },
    { type: 'error', openTag: '<error>', closeTag: '</error>' }
  ]

  // 处理每种标签类型
  for (const { type, openTag, closeTag } of tagPatterns) {
    // 1. 检测完整的标签对
    const completeTagRegex = new RegExp(
      `${openTag.replace(/[<>]/g, '\\$&')}([^<]*?)${closeTag.replace(/[<>]/g, '\\$&')}`,
      'gs'
    )
    const completeMatches = Array.from(cleaned.matchAll(completeTagRegex))

    for (const match of completeMatches) {
      detectedTags.push({
        type,
        content: match[1].trim(),
        isComplete: true
      })
      // 移除完整标签，保留内容
      cleaned = cleaned.replace(match[0], match[1])
    }

    // 2. 检测未闭合的开始标签（流式状态）
    const incompleteTagRegex = new RegExp(`${openTag.replace(/[<>]/g, '\\$&')}([^<]*)$`, 'i')
    const incompleteMatch = cleaned.match(incompleteTagRegex)

    if (incompleteMatch) {
      detectedTags.push({
        type,
        content: incompleteMatch[1].trim(),
        isComplete: false
      })
      // 移除标签，保留内容
      cleaned = cleaned.replace(incompleteMatch[0], incompleteMatch[1])
    }
  }

  // 移除其他不完整的标签开头（如 <scene, <dialo 等）
  cleaned = cleaned.replace(/<[^>]*$/, '')

  // 移除不完整的结束标签（如 </d, </dialogue, </scene 等）
  cleaned = cleaned.replace(/<\/[^>]*$/, '')

  return {
    cleanedContent: cleaned.trim(),
    detectedTags
  }
}

/**
 * 基于检测结果重构解析函数
 */
const parseStreamingContentV2 = (
  cleanedContent: string,
  detectedTags: { type: string; content: string; isComplete: boolean }[],
  showCursor: boolean = false
): ParsedContent => {
  const result: ParsedContent = {
    dialogues: [],
    isComplete: {
      scene: false,
      dialogue: false,
      imagePrompt: false,
      videoPrompt: false,
      error: false
    }
  }

  // 根据检测到的标签填充结果
  for (const tag of detectedTags) {
    switch (tag.type) {
      case 'scene':
        result.scene = tag.content
        result.isComplete.scene = tag.isComplete
        break
      case 'dialogue':
        result.dialogues = [tag.content]
        result.isComplete.dialogue = tag.isComplete
        break
      case 'imagePrompt':
        result.imagePrompt = tag.content
        result.isComplete.imagePrompt = tag.isComplete
        break
      case 'videoPrompt':
        result.videoPrompt = tag.content
        result.isComplete.videoPrompt = tag.isComplete
        break
      case 'error':
        result.error = tag.content
        result.isComplete.error = tag.isComplete
        break
    }
  }

  return result
}

/**
 * 提取标签内的纯文本内容，移除所有标签
 */
const extractCleanContent = (content: string): string => {
  let cleaned = content

  // 移除设备标签
  cleaned = cleaned.replace(/<device>.*?<\/device>/gs, '')

  // 移除所有标签相关内容，只保留纯文本
  const tagTypes = ['scene', 'dialogue', 'imagePrompt', 'videoPrompt', 'error']
  for (const tagType of tagTypes) {
    // 1. 移除完整标签对，保留内容
    const completeTagRegex = new RegExp(`<${tagType}>([^<]*?)</${tagType}>`, 'gs')
    cleaned = cleaned.replace(completeTagRegex, '$1')

    // 2. 移除开始标签，保留后面的内容
    const startTagRegex = new RegExp(`<${tagType}>`, 'gi')
    cleaned = cleaned.replace(startTagRegex, '')

    // 3. 移除完整的结束标签
    const endTagRegex = new RegExp(`</${tagType}>`, 'gi')
    cleaned = cleaned.replace(endTagRegex, '')
  }

  // 移除任何剩余的不完整标签（开始或结束）
  cleaned = cleaned.replace(/<[^>]*$/, '')
  cleaned = cleaned.replace(/<\/[^>]*$/, '')

  return cleaned.trim()
}

/**
 * 简化的标签检测函数 - 只检测标签类型，不提取内容
 */
const detectTagType = (content: string): { type: string | null; hasStarted: boolean } => {
  // 移除设备标签
  const cleaned = content.replace(/<device>.*?<\/device>/gs, '')

  // 检测各种标签类型
  const tagTypes = ['scene', 'dialogue', 'imagePrompt', 'videoPrompt', 'error']

  for (const tagType of tagTypes) {
    // 检测是否有这种标签的开始
    if (cleaned.includes(`<${tagType}>`)) {
      return { type: tagType, hasStarted: true }
    }
    // 检测不完整的标签开头
    if (cleaned.match(new RegExp(`<${tagType.slice(0, 5)}[^>]*$`, 'i'))) {
      return { type: tagType, hasStarted: false }
    }
  }

  return { type: null, hasStarted: false }
}

/**
 * 故事格式解析组件 V2 - 新格式支持
 * 支持的标签格式：
 * <scene>场景描述</scene> - 场景描述，独立显示在顶部
 * <dialogue>对话内容(动作)</dialogue> - 对话内容，动作融合在内
 * <imagePrompt>图片提示</imagePrompt> - 图片生成提示
 * <videoPrompt>视频提示</videoPrompt> - 视频生成提示（预留）
 */
export const StoryMarkdownV2 = ({
  content,
  showCursor = false,
  onImagePrompt,
  onScene,
  characterAvatar,
  messageId,
  chatId,
  messageAttachments
}: {
  content: string
  showCursor?: boolean
  onImagePrompt?: ImagePromptHandler
  onScene?: SceneHandler
  characterAvatar?: string | null
  messageId?: string
  chatId?: string
  messageAttachments?: Array<{
    url: string
    name?: string
    contentType?: string
  }>
}) => {
  const { t } = useTranslation('chat-v2')
  // 跟踪上次触发的场景和图片提示，避免重复触发
  const lastSceneRef = useRef<string | null>(null)
  const lastImagePromptRef = useRef<string | null>(null)

  // 🆕 状态记忆：记住已确定的标签类型，避免闪烁
  const detectedTagTypeRef = useRef<string | null>(null)

  // 使用useEffect在组件挂载时注入样式
  useEffect(() => {
    if (!stylesInjectedV2) {
      const styleEl = document.createElement('style')
      styleEl.innerHTML = storyStyles
      document.head.appendChild(styleEl)
      stylesInjectedV2 = true
    }
  }, [])

  // 🆕 在组件顶层处理状态重置 - 延迟重置避免闪烁
  useEffect(() => {
    if (!showCursor) {
      // 延迟重置，给完成阶段渲染时间
      const timer = setTimeout(() => {
        detectedTagTypeRef.current = null
      }, 100) // 100ms延迟

      return () => clearTimeout(timer)
    }
  }, [showCursor])

  // 提取可播放的文本内容 - 过滤动作内容（括号部分）
  const getPlayableText = (parsed: ParsedContent): string => {
    // 过滤掉括号内的动作描述，只保留对话内容
    return parsed.dialogues
      .map(dialogue => dialogue.replace(/\([^)]*\)/g, '').trim())
      .filter(text => text.length > 0)
      .join('。 ')
  }

  // 🆕 智能标签类型检测 - 带记忆功能
  const getTagTypeWithMemory = (content: string): string | null => {
    // 检测当前内容的标签类型
    const currentDetection = detectTagType(content)

    // 如果检测到了标签类型，锁定它（在流式状态下）
    if (currentDetection.type && showCursor) {
      detectedTagTypeRef.current = currentDetection.type
    }

    // 返回当前检测的类型，如果没有则返回记忆的类型（用于平滑切换）
    return currentDetection.type || detectedTagTypeRef.current
  }

  // 🆕 统一的渲染函数 - 流式和完成阶段使用相同逻辑
  const renderContent = (tagType: string | null, content: string, isStreaming: boolean) => {
    const cleanContent = extractCleanContent(content)

    if (!tagType) {
      return <MarkdownV2>{cleanContent + (isStreaming ? ' ▋' : '')}</MarkdownV2>
    }

    return (
      <div className="story-content-v2 space-y-4">
        {tagType === 'scene' && (
          <div className="scene-background-v2 p-6 rounded-xl bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-purple-50/40 dark:from-blue-950/30 dark:via-indigo-950/20 dark:to-purple-950/10 border-l-4 border-blue-400/50 shadow-sm">
            <div className="text-blue-800 dark:text-blue-200 text-sm leading-relaxed font-medium italic">
              {cleanContent || ''}
              {isStreaming && <span className="typing-cursor-v2 ml-1">▋</span>}
            </div>
          </div>
        )}

        {tagType === 'dialogue' && (
          <Card className="border-default-200/50 rounded-2xl rounded-tl-md">
            <CardBody className="space-y-3 bg-[#FF339A]">
              <div className="flex items-center gap-1 text-foreground text-base leading-relaxed">
                <div className="flex-1 font-medium">
                  {cleanContent || ''}
                  {isStreaming && <span className="typing-cursor-v2 ml-1">▋</span>}
                </div>
                {/* 播放按钮 - 只在完成阶段显示 */}
                {!isStreaming && messageId && cleanContent && (
                  <EnhancedStreamAudioPlayer
                    text={cleanContent}
                    messageId={messageId}
                    chatId={chatId}
                    existingAttachments={messageAttachments?.map(att => {
                      return {
                        url: att.url,
                        name: att.name || t('markdown.attachment'),
                        contentType: att.contentType || 'application/octet-stream'
                      }
                    })}
                    className="opacity-70 hover:opacity-100 transition-opacity flex-shrink-0"
                  />
                )}
              </div>
            </CardBody>
          </Card>
        )}

        {tagType === 'error' && (
          <Card className="border-danger-200 bg-danger-50/50 dark:bg-danger-950/30">
            <CardBody className="space-y-2">
              <div className="flex items-center gap-2 text-danger-600 dark:text-danger-400">
                <svg
                  className="w-5 h-5 flex-shrink-0"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="font-medium text-sm">
                  {cleanContent || ''}
                  {isStreaming && <span className="typing-cursor-v2 ml-1">▋</span>}
                </span>
              </div>
            </CardBody>
          </Card>
        )}

        {/* 🆕 图片显示 - 只在完成阶段显示 */}
        {tagType === 'imagePrompt' && !isStreaming && messageId && (
          <div className="mt-4">
            {/* 检查是否已有图片附件 */}
            {messageAttachments &&
            messageAttachments.some(att => att.contentType?.startsWith('image/')) ? (
              <Suspense
                fallback={
                  <Skeleton className="w-full max-w-xs mx-auto aspect-[3/4] rounded-lg">
                    <div className="w-full h-full bg-default-200"></div>
                  </Skeleton>
                }
              >
                <ImageContainer
                  mode="display"
                  messageId={messageId}
                  existingAttachments={messageAttachments?.map(att => ({
                    url: att.url,
                    name: att.name || '',
                    contentType: att.contentType || 'image/png'
                  }))}
                />
              </Suspense>
            ) : characterAvatar ? (
              <Suspense
                fallback={
                  <Skeleton className="w-full max-w-xs mx-auto aspect-[3/4] rounded-lg">
                    <div className="w-full h-full bg-default-200"></div>
                  </Skeleton>
                }
              >
                <ImageContainer
                  key={`${messageId}-${cleanContent.slice(0, 50)}`}
                  mode="generate"
                  prompt={cleanContent}
                  characterAvatar={characterAvatar}
                  messageId={messageId}
                  chatId={chatId}
                  existingAttachments={messageAttachments?.map(att => ({
                    url: att.url,
                    name: att.name || '',
                    contentType: att.contentType || 'image/png'
                  }))}
                />
              </Suspense>
            ) : null}
          </div>
        )}

        {/* 🆕 视频显示 - 只在完成阶段显示 */}
        {tagType === 'videoPrompt' && !isStreaming && messageId && chatId && (
          <div className="mt-4">
            {/* 检查是否已有视频附件 */}
            {messageAttachments &&
            messageAttachments.some(att => att.contentType?.startsWith('video/')) ? (
              <Suspense
                fallback={
                  <Skeleton className="w-full aspect-video rounded-lg">
                    <div className="w-full h-full bg-default-200"></div>
                  </Skeleton>
                }
              >
                <VideoContainer
                  mode="display"
                  messageId={messageId}
                  existingAttachments={messageAttachments?.map(att => ({
                    url: att.url,
                    name: att.name,
                    contentType: att.contentType
                  }))}
                />
              </Suspense>
            ) : (
              <Suspense
                fallback={
                  <Card className="border-dashed border-default-300">
                    <CardBody className="text-center text-default-500">
                      <Skeleton className="w-full h-32 rounded-lg" />
                      <div className="text-sm mt-2">正在加载视频生成组件...</div>
                    </CardBody>
                  </Card>
                }
              >
                <VideoContainer
                  key={`${messageId}-${cleanContent.slice(0, 50)}`}
                  mode="generate"
                  prompt={cleanContent}
                  characterAvatar={characterAvatar || undefined}
                  messageId={messageId}
                  chatId={chatId}
                  existingAttachments={messageAttachments}
                  onVideoGenerated={videoUrl => {
                    console.log('✅ 视频生成完成:', videoUrl)
                  }}
                  onError={error => {
                    console.error('❌ 视频生成失败:', error)
                  }}
                />
              </Suspense>
            )}
          </div>
        )}
      </div>
    )
  }

  try {
    // 如果内容为空，直接返回空组件
    if (!content || content.trim() === '') {
      return <div className="story-content-v2"></div>
    }

    // 🆕 统一使用记忆检测和统一渲染
    const tagType = getTagTypeWithMemory(content)

    if (showCursor) {
      // 流式阶段：使用统一渲染函数
      return renderContent(tagType, content, true)
    } else {
      // 完成阶段：也使用统一渲染函数，但需要处理额外功能
      const result = renderContent(tagType, content, false)

      // 处理场景和图片回调（保持原有逻辑）
      const { cleanedContent, detectedTags } = cleanAndDetectStreamingContent(content)
      const parsed = parseStreamingContentV2(cleanedContent, detectedTags, showCursor)

      // 处理场景回调 - 无感生成背景图（增加防重复检查）
      if (parsed.scene && parsed.isComplete.scene && onScene && typeof onScene === 'function') {
        // 只有当场景内容与上次不同时才触发回调
        if (lastSceneRef.current !== parsed.scene) {
          lastSceneRef.current = parsed.scene
          onScene(parsed.scene)
        }
      }

      // 处理图片提示回调（增加防重复检查）
      if (
        parsed.imagePrompt &&
        parsed.isComplete.imagePrompt &&
        onImagePrompt &&
        typeof onImagePrompt === 'function'
      ) {
        // 只有当图片提示内容与上次不同时才触发回调
        if (lastImagePromptRef.current !== parsed.imagePrompt) {
          lastImagePromptRef.current = parsed.imagePrompt
          onImagePrompt(parsed.imagePrompt)
        }
      }

      return result
    }
  } catch (error) {
    console.error('Error parsing story format:', error)
    // 发生错误时也使用统一渲染
    const cleanContent = extractCleanContent(content)
    return <MarkdownV2>{cleanContent + (showCursor ? ' ▋' : '')}</MarkdownV2>
  }
}

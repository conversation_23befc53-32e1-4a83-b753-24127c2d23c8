import type { Env } from '@/types/env';
import {
  checkUserMembership,
  getUserPoints,
  getUserActiveSubscription,
  getMembershipPlanById,
  updateUserPoints,
} from '@/lib/db/queries/membership';

/**
 * 用户完整会员状态信息
 */
export interface UserMembershipStatus {
  // 基本状态
  isMember: boolean;
  userType: 'free' | 'member';

  // 会员信息
  subscription?: {
    id: string;
    planId: string;
    startDate: Date;
    endDate: Date;
    status: 'active' | 'expired' | 'cancelled' | 'pending';
    daysRemaining: number;
    autoRenew: boolean;
  };

  // 套餐信息
  plan?: {
    id: string;
    name: string;
    price: string;
    durationDays: number;
    pointsIncluded: number;
    features: any;
  };

  // 积分信息
  points: {
    totalPoints: number;
    usedPoints: number;
    availablePoints: number;
    lastUpdated: Date;
  };

  // 权限信息
  permissions: {
    characterCreateLimit: number; // 角色创建限制 (-1表示无限制)
    hasUnlimitedTextChat: boolean; // 无限制文本对话
    canUseVoiceGeneration: boolean; // 语音生成权限
    canUseImageGeneration: boolean; // 图片生成权限
    canUseVideoGeneration: boolean; // 视频生成权限
    canAccessScripts: boolean; // 剧本访问权限
    canUseGalleryGeneration: boolean; // 写真集生成权限
  };

  // 使用统计
  usage?: {
    textChatToday: number;
    imageGenerationToday: number;
    charactersCreated: number;
    scriptsOwned: number;
  };
}

/**
 * 会员状态服务类
 */
export class MembershipService {
  constructor(private env: Env) {}

  /**
   * 获取用户完整的会员状态
   */
  async getUserMembershipStatus(userId: string): Promise<UserMembershipStatus> {
    // 并行获取用户信息
    const [membershipInfo, pointsInfo, activeSubscription] = await Promise.all([
      checkUserMembership(this.env, userId),
      getUserPoints(this.env, userId),
      getUserActiveSubscription(this.env, userId),
    ]);

    const isMember = membershipInfo.isMember;
    const userType = isMember ? 'member' : 'free';

    // 获取套餐详情
    let planDetails = null;
    if (activeSubscription) {
      planDetails = await getMembershipPlanById(this.env, activeSubscription.planId);
    }

    // 计算剩余天数
    let daysRemaining = 0;
    if (activeSubscription && activeSubscription.status === 'active') {
      const endDate = new Date(activeSubscription.endDate);
      const now = new Date();
      daysRemaining = Math.max(
        0,
        Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
      );
    }

    // 计算权限
    const permissions = this.calculatePermissions(userType, planDetails);

    // 构建返回结果
    const result: UserMembershipStatus = {
      isMember,
      userType,
      subscription: activeSubscription
        ? {
            id: activeSubscription.id,
            planId: activeSubscription.planId,
            startDate: activeSubscription.startDate,
            endDate: activeSubscription.endDate,
            status: activeSubscription.status,
            daysRemaining,
            autoRenew: activeSubscription.autoRenew,
          }
        : undefined,
      plan: planDetails
        ? {
            id: planDetails.id,
            name: planDetails.name,
            price: planDetails.price,
            durationDays: planDetails.durationDays,
            pointsIncluded: planDetails.pointsIncluded,
            features: planDetails.features,
          }
        : undefined,
      points: {
        totalPoints: pointsInfo.totalPoints,
        usedPoints: pointsInfo.usedPoints,
        availablePoints: pointsInfo.availablePoints,
        lastUpdated: pointsInfo.lastUpdated,
      },
      permissions,
    };

    return result;
  }

  /**
   * 检查用户是否有指定权限
   */
  async checkPermission(
    userId: string,
    permission: keyof UserMembershipStatus['permissions']
  ): Promise<boolean> {
    const status = await this.getUserMembershipStatus(userId);
    return status.permissions[permission] as boolean;
  }

  /**
   * 检查并扣除积分
   */
  async consumePoints(
    userId: string,
    points: number,
    source: 'generation' | 'purchase' | 'admin',
    sourceId?: string,
    description?: string
  ): Promise<{ success: boolean; remainingPoints?: number; error?: string }> {
    try {
      // 检查积分余额
      const pointsInfo = await getUserPoints(this.env, userId);
      if (pointsInfo.availablePoints < points) {
        return {
          success: false,
          error: `积分不足，需要${points}积分，当前可用${pointsInfo.availablePoints}积分`,
        };
      }

      // 扣除积分
      const updatedPoints = await updateUserPoints(
        this.env,
        userId,
        points,
        'spend',
        source,
        sourceId,
        description
      );

      return {
        success: true,
        remainingPoints: updatedPoints.availablePoints,
      };
    } catch (error) {
      console.error('积分消费失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '积分消费失败',
      };
    }
  }

  /**
   * 添加积分
   */
  async addPoints(
    userId: string,
    points: number,
    source: 'subscription' | 'purchase' | 'bonus' | 'admin',
    sourceId?: string,
    description?: string
  ): Promise<{ success: boolean; totalPoints?: number; error?: string }> {
    try {
      const updatedPoints = await updateUserPoints(
        this.env,
        userId,
        points,
        'earn',
        source,
        sourceId,
        description
      );

      return {
        success: true,
        totalPoints: updatedPoints.availablePoints,
      };
    } catch (error) {
      console.error('积分添加失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '积分添加失败',
      };
    }
  }

  /**
   * 检查会员是否即将到期
   */
  async checkMembershipExpiration(
    userId: string,
    daysThreshold = 7
  ): Promise<{
    isExpiringSoon: boolean;
    daysRemaining: number;
    subscription?: any;
  }> {
    const subscription = await getUserActiveSubscription(this.env, userId);

    if (!subscription) {
      return { isExpiringSoon: false, daysRemaining: 0 };
    }

    const endDate = new Date(subscription.endDate);
    const now = new Date();
    const daysRemaining = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    return {
      isExpiringSoon: daysRemaining <= daysThreshold && daysRemaining > 0,
      daysRemaining: Math.max(0, daysRemaining),
      subscription,
    };
  }

  /**
   * 获取会员等级对应的角色创建限制
   */
  private getCharacterCreateLimit(planName?: string): number {
    if (!planName) return 1; // 免费用户

    const limits: Record<string, number> = {
      Pro: 5,
      Elite: 20,
      Ultra: -1, // 无限制
    };

    return limits[planName] || 1;
  }

  /**
   * 计算用户权限
   */
  private calculatePermissions(userType: 'free' | 'member', plan?: any) {
    const isMember = userType === 'member';

    return {
      characterCreateLimit: this.getCharacterCreateLimit(plan?.name),
      hasUnlimitedTextChat: isMember,
      canUseVoiceGeneration: isMember,
      canUseImageGeneration: true, // 免费用户有每日限制，会员用户消耗积分
      canUseVideoGeneration: isMember,
      canAccessScripts: isMember,
      canUseGalleryGeneration: isMember,
    };
  }
}

/**
 * 会员状态缓存服务
 * 用于减少频繁的数据库查询
 */
export class MembershipCacheService {
  private cache = new Map<string, { data: UserMembershipStatus; timestamp: number }>();
  private cacheTimeout = 5 * 60 * 1000; // 5分钟缓存

  constructor(private membershipService: MembershipService) {}

  /**
   * 获取缓存的会员状态
   */
  async getCachedMembershipStatus(userId: string): Promise<UserMembershipStatus> {
    const cached = this.cache.get(userId);
    const now = Date.now();

    // 检查缓存是否有效
    if (cached && now - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }

    // 获取最新数据
    const status = await this.membershipService.getUserMembershipStatus(userId);

    // 更新缓存
    this.cache.set(userId, { data: status, timestamp: now });

    return status;
  }

  /**
   * 清除用户缓存
   */
  clearUserCache(userId: string): void {
    this.cache.delete(userId);
  }

  /**
   * 清除所有缓存
   */
  clearAllCache(): void {
    this.cache.clear();
  }
}

/**
 * 全局会员服务实例工厂
 */
export function createMembershipService(env: Env): MembershipService {
  return new MembershipService(env);
}

/**
 * 全局缓存服务实例工厂
 */
export function createMembershipCacheService(env: Env): MembershipCacheService {
  const membershipService = createMembershipService(env);
  return new MembershipCacheService(membershipService);
}

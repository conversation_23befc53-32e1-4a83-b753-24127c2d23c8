export interface BleAdvertiserPlugin {
  /**
   * 初始化蓝牙广播服务
   * @returns 是否初始化成功
   */
  initialize(): Promise<{ success: boolean }>;

  /**
   * 检查蓝牙是否已启用
   * @returns 蓝牙是否已启用
   */
  isBluetoothEnabled(): Promise<{ enabled: boolean }>;

  /**
   * 开始蓝牙广播
   * @param options 广播选项
   * @returns 是否成功开始广播
   */
  startAdvertising(options: {
    /**
     * 广播模式
     * 0: 平衡模式
     * 1: 低延迟模式
     * 2: 低功耗模式
     * 默认: 1
     */
    mode?: number;

    /**
     * 制造商ID，用于标识广播数据
     * 默认: 255
     */
    manufacturerId?: number;

    /**
     * 要广播的数据，十六进制字符串或数字数组
     */
    data: string | number[];

    /**
     * 广播实例ID，用于区分多个广播
     * 默认: 自动生成
     */
    instanceId?: number;
  }): Promise<{ success: boolean; instanceId: number }>;

  /**
   * 停止指定的广播
   * @param options 停止选项
   * @returns 是否成功停止广播
   */
  stopAdvertising(options: {
    /**
     * 要停止的广播实例ID
     * 如果不提供，则停止所有广播
     */
    instanceId?: number;
  }): Promise<{ success: boolean }>;

  /**
   * 停止所有广播
   * @returns 是否成功停止所有广播
   */
  stopAllAdvertising(): Promise<{ success: boolean }>;
}

const { heroui } = require('@heroui/react')

/** @type {import('tailwindcss').Config} */
export default {
  content: [
    './index.html',
    './src/**/*.{js,ts,jsx,tsx}',
    '../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}'
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Geist', 'sans-serif'],
        mono: ['Geist Mono', 'monospace']
      },
      colors: {
        border: 'var(--border)',
        input: 'var(--input)',
        ring: 'var(--ring)',
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        primary: {
          DEFAULT: 'var(--primary)',
          foreground: 'var(--primary-foreground)'
        },
        secondary: {
          DEFAULT: 'var(--secondary)',
          foreground: 'var(--secondary-foreground)'
        },
        destructive: {
          DEFAULT: 'var(--destructive)',
          foreground: 'var(--destructive-foreground)'
        },
        muted: {
          DEFAULT: 'var(--muted)',
          foreground: 'var(--muted-foreground)'
        },
        accent: {
          DEFAULT: 'var(--accent)',
          foreground: 'var(--accent-foreground)'
        },
        popover: {
          DEFAULT: 'var(--popover)',
          foreground: 'var(--popover-foreground)'
        },
        card: {
          DEFAULT: 'var(--card)',
          foreground: 'var(--card-foreground)'
        }
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)'
      },
      animation: {
        'gradient-slow': 'gradient 15s ease infinite',
        'float-1': 'float1 20s ease-in-out infinite',
        'float-2': 'float2 25s ease-in-out infinite',
        'float-3': 'float3 30s ease-in-out infinite',
        'float-4': 'float4 35s ease-in-out infinite',
        'float-5': 'float5 40s ease-in-out infinite',
        meteor: 'meteor 5s linear infinite',
        glow: 'glow 2s ease-in-out infinite'
      },
      keyframes: {
        gradient: {
          '0%, 100%': { backgroundPosition: '0% 50%' },
          '50%': { backgroundPosition: '100% 50%' }
        },
        float1: {
          '0%, 100%': { transform: 'translate(0, 0) rotate(0deg)' },
          '25%': { transform: 'translate(20px, 15px) rotate(5deg)' },
          '50%': { transform: 'translate(-5px, 20px) rotate(10deg)' },
          '75%': { transform: 'translate(-15px, 5px) rotate(5deg)' }
        },
        float2: {
          '0%, 100%': { transform: 'translate(0, 0) rotate(0deg)' },
          '20%': { transform: 'translate(-15px, 10px) rotate(-5deg)' },
          '40%': { transform: 'translate(10px, 15px) rotate(3deg)' },
          '60%': { transform: 'translate(15px, -10px) rotate(-3deg)' },
          '80%': { transform: 'translate(-10px, -5px) rotate(5deg)' }
        },
        float3: {
          '0%, 100%': { transform: 'translate(0, 0) rotate(0deg)' },
          '33%': { transform: 'translate(10px, -15px) rotate(-4deg)' },
          '66%': { transform: 'translate(-15px, 12px) rotate(7deg)' }
        },
        float4: {
          '0%, 100%': { transform: 'translate(0, 0) rotate(0deg)' },
          '20%': { transform: 'translate(15px, 10px) rotate(5deg)' },
          '40%': { transform: 'translate(5px, -10px) rotate(-5deg)' },
          '60%': { transform: 'translate(-10px, -5px) rotate(3deg)' },
          '80%': { transform: 'translate(-5px, 10px) rotate(-3deg)' }
        },
        float5: {
          '0%, 100%': { transform: 'translate(0, 0) rotate(0deg)' },
          '25%': { transform: 'translate(-10px, -15px) rotate(-5deg)' },
          '50%': { transform: 'translate(10px, 10px) rotate(10deg)' },
          '75%': { transform: 'translate(15px, -5px) rotate(-10deg)' }
        },
        meteor: {
          '0%': { transform: 'rotate(215deg) translateX(0)', opacity: '0' },
          '10%': { opacity: '1' },
          '90%': { opacity: '1' },
          '100%': {
            transform: 'rotate(215deg) translateX(-500px)',
            opacity: '0'
          }
        },
        glow: {
          '0%, 100%': { boxShadow: '0 0 15px 0px rgba(138, 43, 226, 0.3)' },
          '50%': { boxShadow: '0 0 30px 5px rgba(138, 43, 226, 0.6)' }
        }
      },
      backgroundSize: {
        '300%': '300% 300%'
      },
      boxShadow: {
        'glow-primary': '0 0 15px 0px rgba(var(--primary-rgb), 0.5)',
        'glow-primary-hover': '0 0 25px 5px rgba(var(--primary-rgb), 0.7)',
        'glow-rose': '0 0 15px 0px rgba(225, 29, 72, 0.5)',
        'glow-rose-hover': '0 0 25px 5px rgba(225, 29, 72, 0.7)'
      }
    }
  },
  darkMode: 'class',
  plugins: [
    heroui({
      // 添加常用颜色，但不覆盖基础工具类
      addCommonColors: true,
      // 确保不干扰 Tailwind 的基础工具类
      prefix: undefined,
      themes: {
        light: {
          colors: {
            default: {
              50: '#fdf2f8',
              100: '#fce7f3',
              200: '#fbcfe8',
              300: '#f9a8d4',
              400: '#f472b6',
              500: '#ec4899',
              600: '#db2777',
              700: '#be185d',
              800: '#9d174d',
              900: '#831843',
              foreground: '#000',
              DEFAULT: '#f1f5f9'
            },
            primary: {
              50: '#fce4ec',
              100: '#f8bbd9',
              200: '#f48fb1',
              300: '#e23f9d',
              400: '#ec407a',
              500: '#e91e63',
              600: '#d81b60',
              700: '#c2185b',
              800: '#ad1457',
              900: '#880e4f',
              foreground: '#fff',
              DEFAULT: '#e91e63'
            },
            secondary: {
              50: '#f3e8ff',
              100: '#e9d5ff',
              200: '#d8b4fe',
              300: '#c084fc',
              400: '#a855f7',
              500: '#9333ea',
              600: '#7c3aed',
              700: '#6d28d9',
              800: '#5b21b6',
              900: '#4c1d95',
              foreground: '#fff',
              DEFAULT: '#9333ea'
            },
            success: {
              50: '#ecfdf5',
              100: '#d1fae5',
              200: '#a7f3d0',
              300: '#6ee7b7',
              400: '#34d399',
              500: '#10b981',
              600: '#059669',
              700: '#047857',
              800: '#065f46',
              900: '#064e3b',
              foreground: '#fff',
              DEFAULT: '#10b981'
            },
            warning: {
              50: '#fffbeb',
              100: '#fef3c7',
              200: '#fde68a',
              300: '#fcd34d',
              400: '#fbbf24',
              500: '#f59e0b',
              600: '#d97706',
              700: '#b45309',
              800: '#92400e',
              900: '#78350f',
              foreground: '#000',
              DEFAULT: '#f59e0b'
            },
            danger: {
              50: '#fef2f2',
              100: '#fee2e2',
              200: '#fecaca',
              300: '#fca5a5',
              400: '#f87171',
              500: '#ef4444',
              600: '#dc2626',
              700: '#b91c1c',
              800: '#991b1b',
              900: '#7f1d1d',
              foreground: '#fff',
              DEFAULT: '#ef4444'
            },
            background: '#fdf2f8',
            foreground: '#1f2937',
            content1: {
              DEFAULT: '#ffffff',
              foreground: '#1f2937'
            },
            content2: {
              DEFAULT: '#f9fafb',
              foreground: '#1f2937'
            },
            content3: {
              DEFAULT: '#f3f4f6',
              foreground: '#1f2937'
            },
            content4: {
              DEFAULT: '#e5e7eb',
              foreground: '#1f2937'
            },
            focus: '#e91e63',
            overlay: '#000000'
          }
        },
        dark: {
          colors: {
            default: {
              50: '#2a1a2e',
              100: '#3d2a42',
              200: '#503a56',
              300: '#634a6a',
              400: '#765a7e',
              500: '#8f6f95',
              600: '#a885ac',
              700: '#c19bc3',
              800: '#dab1da',
              900: '#f3c7f1',
              foreground: '#fff',
              DEFAULT: '#503a56'
            },
            primary: {
              50: '#880e4f',
              100: '#ad1457',
              200: '#c2185b',
              300: '#d81b60',
              400: '#e91e63',
              500: '#e23f9d',
              600: '#f48fb1',
              700: '#f8bbd9',
              800: '#fce4ec',
              900: '#fef7f0',
              foreground: '#fff',
              DEFAULT: '#e23f9d'
            },
            secondary: {
              50: '#4c1d95',
              100: '#5b21b6',
              200: '#6d28d9',
              300: '#7c3aed',
              400: '#9333ea',
              500: '#a855f7',
              600: '#c084fc',
              700: '#d8b4fe',
              800: '#e9d5ff',
              900: '#f3e8ff',
              foreground: '#fff',
              DEFAULT: '#a855f7'
            },
            success: {
              50: '#064e3b',
              100: '#065f46',
              200: '#047857',
              300: '#059669',
              400: '#10b981',
              500: '#34d399',
              600: '#6ee7b7',
              700: '#a7f3d0',
              800: '#d1fae5',
              900: '#ecfdf5',
              foreground: '#000',
              DEFAULT: '#34d399'
            },
            warning: {
              50: '#78350f',
              100: '#92400e',
              200: '#b45309',
              300: '#d97706',
              400: '#f59e0b',
              500: '#fbbf24',
              600: '#fcd34d',
              700: '#fde68a',
              800: '#fef3c7',
              900: '#fffbeb',
              foreground: '#000',
              DEFAULT: '#fbbf24'
            },
            danger: {
              50: '#7f1d1d',
              100: '#991b1b',
              200: '#b91c1c',
              300: '#dc2626',
              400: '#ef4444',
              500: '#f87171',
              600: '#fca5a5',
              700: '#fecaca',
              800: '#fee2e2',
              900: '#fef2f2',
              foreground: '#fff',
              DEFAULT: '#f87171'
            },
            background: '#2a1a2e',
            foreground: '#f8fafc',
            content1: {
              DEFAULT: '#3d2a42',
              foreground: '#f8fafc'
            },
            content2: {
              DEFAULT: '#503a56',
              foreground: '#f8fafc'
            },
            content3: {
              DEFAULT: '#634a6a',
              foreground: '#f8fafc'
            },
            content4: {
              DEFAULT: '#765a7e',
              foreground: '#f8fafc'
            },
            focus: '#e23f9d',
            overlay: '#ffffff'
          }
        }
      },
      layout: {
        disabledOpacity: '0.5'
      }
    })
  ]
}

import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { getSupabase } from '@/lib/db/queries/base';
import {
  handleSupabaseResult,
  handleSupabaseSingleResult,
  TABLE_NAMES,
} from '@/lib/db/supabase-types';
import { authMiddleware } from '@/middleware/auth';
import type { Env } from '@/types/env';

const adminDevices = new Hono<{ Bindings: Env }>();

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user');
    if (!supabaseUser) {
      return false;
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {};
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true;

    if (isAdmin) {
      return true;
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>',
      // 在这里添加其他管理员邮箱
    ];

    if (adminEmails.includes(supabaseUser.email)) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('检查管理员权限失败:', error);
    return false;
  }
}

// ==================== 设备管理 ====================

// 设备列表查询参数
const deviceListSchema = z.object({
  page: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform((val) => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  category: z.string().optional(),
  isActive: z
    .string()
    .transform((val) => val === 'true')
    .optional(),
  userId: z.string().optional(),
});

// 获取设备列表
adminDevices.get('/', authMiddleware, zValidator('query', deviceListSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const { page, pageSize, keyword, category, isActive, userId } = c.req.valid('query');
    const env = c.env;
    const supabase = getSupabase(env);

    let query = supabase.from(TABLE_NAMES.device).select(`
        *,
        user:user_id(id, email),
        supportedFunctions:DeviceSupportedFunction(
          id,
          is_active,
          function:DeviceFunction(
            id,
            name,
            key,
            max_intensity,
            is_active
          )
        ),
        supportedModes:DeviceSupportedMode(
          id,
          is_active,
          mode:DeviceMode(
            id,
            name,
            description,
            is_active
          )
        )
      `);

    // 应用过滤条件
    if (keyword) {
      query = query.or(
        `name.ilike.%${keyword}%, device_code.ilike.%${keyword}%, brand.ilike.%${keyword}%`
      );
    }

    if (category) {
      query = query.eq('category', category);
    }

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive);
    }

    if (userId) {
      query = query.eq('user_id', userId);
    }

    // 获取总数
    const countQuery = supabase
      .from(TABLE_NAMES.device)
      .select('*', { count: 'exact', head: true });

    if (keyword) {
      countQuery.or(
        `name.ilike.%${keyword}%, device_code.ilike.%${keyword}%, brand.ilike.%${keyword}%`
      );
    }
    if (category) {
      countQuery.eq('category', category);
    }
    if (isActive !== undefined) {
      countQuery.eq('is_active', isActive);
    }
    if (userId) {
      countQuery.eq('user_id', userId);
    }

    const { count } = await countQuery;

    // 分页查询
    const offset = (page - 1) * pageSize;
    const result = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + pageSize - 1);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    return c.json({
      success: true,
      data: {
        data: data || [],
        total: count || 0,
        page,
        pageSize,
        totalPages: Math.ceil((count || 0) / pageSize),
      },
    });
  } catch (error) {
    console.error('获取设备列表失败:', error);
    return c.json(
      {
        success: false,
        message: '获取设备列表失败',
      },
      500
    );
  }
});

// 创建设备
const createDeviceSchema = z.object({
  deviceCode: z.string().min(1, '设备码不能为空'),
  name: z.string().min(1, '设备名称不能为空'),
  pic: z.string().optional(),
  brand: z.string().optional(),
  model: z.string().optional(),
  category: z.string().optional(),
  description: z.string().optional(),
  userId: z.string().optional(),
  isActive: z.boolean().default(true),
});

adminDevices.post('/', authMiddleware, zValidator('json', createDeviceSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const deviceData = c.req.valid('json');
    const env = c.env;
    const supabase = getSupabase(env);

    // 检查设备码是否已存在
    const existingDevice = await supabase
      .from(TABLE_NAMES.device)
      .select('id')
      .eq('device_code', deviceData.deviceCode)
      .single();

    if (existingDevice.data) {
      return c.json(
        {
          success: false,
          message: '设备码已存在',
        },
        400
      );
    }

    // 创建设备
    const result = await supabase
      .from(TABLE_NAMES.device)
      .insert({
        device_code: deviceData.deviceCode,
        name: deviceData.name,
        pic: deviceData.pic,
        brand: deviceData.brand,
        model: deviceData.model,
        category: deviceData.category,
        description: deviceData.description,
        user_id: deviceData.userId,
        is_active: deviceData.isActive,
      })
      .select()
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) throw error;

    return c.json({
      success: true,
      data,
      message: '设备创建成功',
    });
  } catch (error) {
    console.error('创建设备失败:', error);
    return c.json(
      {
        success: false,
        message: '创建设备失败',
      },
      500
    );
  }
});

// 更新设备
const updateDeviceSchema = z.object({
  name: z.string().min(1, '设备名称不能为空').optional(),
  pic: z.string().optional(),
  brand: z.string().optional(),
  model: z.string().optional(),
  category: z.string().optional(),
  description: z.string().optional(),
  userId: z.string().optional(),
  isActive: z.boolean().optional(),
});

adminDevices.put('/:id', authMiddleware, zValidator('json', updateDeviceSchema), async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const deviceId = c.req.param('id');
    const updateData = c.req.valid('json');
    const env = c.env;
    const supabase = getSupabase(env);

    // 构建更新数据
    const updatePayload: any = {
      updated_at: new Date().toISOString(),
    };

    if (updateData.name !== undefined) updatePayload.name = updateData.name;
    if (updateData.pic !== undefined) updatePayload.pic = updateData.pic;
    if (updateData.brand !== undefined) updatePayload.brand = updateData.brand;
    if (updateData.model !== undefined) updatePayload.model = updateData.model;
    if (updateData.category !== undefined) updatePayload.category = updateData.category;
    if (updateData.description !== undefined) updatePayload.description = updateData.description;
    if (updateData.userId !== undefined) updatePayload.user_id = updateData.userId;
    if (updateData.isActive !== undefined) updatePayload.is_active = updateData.isActive;

    const result = await supabase
      .from(TABLE_NAMES.device)
      .update(updatePayload)
      .eq('id', deviceId)
      .select()
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) {
      if (error.code === 'PGRST116') {
        return c.json(
          {
            success: false,
            message: '设备不存在',
          },
          404
        );
      }
      throw error;
    }

    return c.json({
      success: true,
      data,
      message: '设备更新成功',
    });
  } catch (error) {
    console.error('更新设备失败:', error);
    return c.json(
      {
        success: false,
        message: '更新设备失败',
      },
      500
    );
  }
});

// 删除设备
adminDevices.delete('/:id', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const deviceId = c.req.param('id');
    const env = c.env;
    const supabase = getSupabase(env);

    // 检查是否有使用记录
    const usageResult = await supabase
      .from(TABLE_NAMES.deviceUsage)
      .select('id')
      .eq('device_id', deviceId)
      .limit(1);

    if (usageResult.data && usageResult.data.length > 0) {
      return c.json(
        {
          success: false,
          message: '设备有使用记录，无法删除',
        },
        400
      );
    }

    const result = await supabase.from(TABLE_NAMES.device).delete().eq('id', deviceId);

    if (result.error) {
      throw result.error;
    }

    return c.json({
      success: true,
      message: '设备删除成功',
    });
  } catch (error) {
    console.error('删除设备失败:', error);
    return c.json(
      {
        success: false,
        message: '删除设备失败',
      },
      500
    );
  }
});

// ==================== 设备功能关联管理 ====================

// 获取设备支持的功能列表
adminDevices.get('/:id/functions', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const deviceId = c.req.param('id');
    const env = c.env;
    const supabase = getSupabase(env);

    const result = await supabase
      .from('DeviceSupportedFunction')
      .select(`
        *,
        function:DeviceFunction(
          id,
          name,
          key,
          description,
          max_intensity,
          is_active
        )
      `)
      .eq('device_id', deviceId)
      .order('created_at', { ascending: true });

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    return c.json({
      success: true,
      data: data || [],
    });
  } catch (error) {
    console.error('获取设备功能失败:', error);
    return c.json(
      {
        success: false,
        message: '获取设备功能失败',
      },
      500
    );
  }
});

// 为设备添加功能支持
const addDeviceFunctionSchema = z.object({
  functionId: z.string().uuid('功能ID格式不正确'),
  isActive: z.boolean().default(true),
});

adminDevices.post(
  '/:id/functions',
  authMiddleware,
  zValidator('json', addDeviceFunctionSchema),
  async (c) => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c);
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403);
      }

      const deviceId = c.req.param('id');
      const functionData = c.req.valid('json');
      const env = c.env;
      const supabase = getSupabase(env);

      // 检查设备是否存在
      const deviceResult = await supabase
        .from(TABLE_NAMES.device)
        .select('id')
        .eq('id', deviceId)
        .single();

      if (!deviceResult.data) {
        return c.json(
          {
            success: false,
            message: '设备不存在',
          },
          404
        );
      }

      // 检查功能是否存在
      const functionResult = await supabase
        .from('DeviceFunction')
        .select('id')
        .eq('id', functionData.functionId)
        .single();

      if (!functionResult.data) {
        return c.json(
          {
            success: false,
            message: '功能不存在',
          },
          404
        );
      }

      // 检查是否已关联
      const existingRelation = await supabase
        .from('DeviceSupportedFunction')
        .select('id')
        .eq('device_id', deviceId)
        .eq('function_id', functionData.functionId)
        .single();

      if (existingRelation.data) {
        return c.json(
          {
            success: false,
            message: '设备已支持该功能',
          },
          400
        );
      }

      // 创建关联
      const result = await supabase
        .from('DeviceSupportedFunction')
        .insert({
          device_id: deviceId,
          function_id: functionData.functionId,
          is_active: functionData.isActive,
        })
        .select()
        .single();

      const { data, error } = handleSupabaseSingleResult(result);
      if (error) throw error;

      return c.json({
        success: true,
        data,
        message: '功能关联成功',
      });
    } catch (error) {
      console.error('创建设备功能关联失败:', error);
      return c.json(
        {
          success: false,
          message: '创建设备功能关联失败',
        },
        500
      );
    }
  }
);

// 删除设备功能关联
adminDevices.delete('/:id/functions/:bindingId', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const deviceId = c.req.param('id');
    const bindingId = c.req.param('bindingId');
    const env = c.env;
    const supabase = getSupabase(env);

    // 方案：直接通过绑定记录ID删除，同时验证设备ID匹配
    const result = await supabase
      .from('DeviceSupportedFunction')
      .delete()
      .eq('id', bindingId)
      .eq('device_id', deviceId); // 额外验证设备ID匹配，防止误删

    if (result.error) {
      throw result.error;
    }

    return c.json({
      success: true,
      message: '功能解绑成功',
    });
  } catch (error) {
    console.error('删除设备功能关联失败:', error);
    return c.json(
      {
        success: false,
        message: '删除设备功能关联失败',
      },
      500
    );
  }
});

// ==================== 设备模式关联管理 ====================

// 获取设备支持的模式列表
adminDevices.get('/:id/modes', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const deviceId = c.req.param('id');
    const env = c.env;
    const supabase = getSupabase(env);

    const result = await supabase
      .from('DeviceSupportedMode')
      .select(`
        *,
        mode:DeviceMode(
          id,
          name,
          description,
          pattern,
          is_active
        )
      `)
      .eq('device_id', deviceId)
      .order('created_at', { ascending: true });

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;

    return c.json({
      success: true,
      data: data || [],
    });
  } catch (error) {
    console.error('获取设备模式失败:', error);
    return c.json(
      {
        success: false,
        message: '获取设备模式失败',
      },
      500
    );
  }
});

// 为设备添加模式支持
const addDeviceModeSchema = z.object({
  modeId: z.string().uuid('模式ID格式不正确'),
  isActive: z.boolean().default(true),
});

adminDevices.post(
  '/:id/modes',
  authMiddleware,
  zValidator('json', addDeviceModeSchema),
  async (c) => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c);
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403);
      }

      const deviceId = c.req.param('id');
      const modeData = c.req.valid('json');
      const env = c.env;
      const supabase = getSupabase(env);

      // 检查设备是否存在
      const deviceResult = await supabase
        .from(TABLE_NAMES.device)
        .select('id')
        .eq('id', deviceId)
        .single();

      if (!deviceResult.data) {
        return c.json(
          {
            success: false,
            message: '设备不存在',
          },
          404
        );
      }

      // 检查模式是否存在
      const modeResult = await supabase
        .from('DeviceMode')
        .select('id')
        .eq('id', modeData.modeId)
        .single();

      if (!modeResult.data) {
        return c.json(
          {
            success: false,
            message: '模式不存在',
          },
          404
        );
      }

      // 检查是否已关联
      const existingRelation = await supabase
        .from('DeviceSupportedMode')
        .select('id')
        .eq('device_id', deviceId)
        .eq('mode_id', modeData.modeId)
        .single();

      if (existingRelation.data) {
        return c.json(
          {
            success: false,
            message: '设备已支持该模式',
          },
          400
        );
      }

      // 创建关联
      const result = await supabase
        .from('DeviceSupportedMode')
        .insert({
          device_id: deviceId,
          mode_id: modeData.modeId,
          is_active: modeData.isActive,
        })
        .select()
        .single();

      const { data, error } = handleSupabaseSingleResult(result);
      if (error) throw error;

      return c.json({
        success: true,
        data,
        message: '模式关联成功',
      });
    } catch (error) {
      console.error('创建设备模式关联失败:', error);
      return c.json(
        {
          success: false,
          message: '创建设备模式关联失败',
        },
        500
      );
    }
  }
);

// 删除设备模式关联
adminDevices.delete('/:id/modes/:modeId', authMiddleware, async (c) => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c);
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403);
    }

    const deviceId = c.req.param('id');
    const modeId = c.req.param('modeId');
    const env = c.env;
    const supabase = getSupabase(env);

    const result = await supabase
      .from('DeviceSupportedMode')
      .delete()
      .eq('device_id', deviceId)
      .eq('mode_id', modeId);

    if (result.error) {
      throw result.error;
    }

    return c.json({
      success: true,
      message: '模式关联删除成功',
    });
  } catch (error) {
    console.error('删除设备模式关联失败:', error);
    return c.json(
      {
        success: false,
        message: '删除设备模式关联失败',
      },
      500
    );
  }
});

export default adminDevices;

import { create } from 'zustand'
import { authService } from '@/services/auth'
import type { LoginParams, AdminProfile } from '@/services/auth'

interface AuthState {
  user: AdminProfile | null
  isLoading: boolean
  error: string | null

  // Actions
  login: (params: LoginParams) => Promise<boolean>
  logout: () => Promise<void>
  getProfile: () => Promise<void>
  clearError: () => void
}

export const useAuthStore = create<AuthState>(set => ({
  user: null,
  isLoading: false,
  error: null,

  login: async (params: LoginParams) => {
    set({ isLoading: true, error: null })

    try {
      params = {
        email: '<EMAIL>',
        password: 'jdg6tet_TKU4kcb-muk'
      }
      const response = await authService.login(params)

      if (response.success && response.data) {
        set({
          user: {
            ...response.data.user,
            createdAt: new Date().toISOString()
          },
          isLoading: false,
          error: null
        })
        return true
      } else {
        set({
          error: response.error || '登录失败',
          isLoading: false
        })
        return false
      }
    } catch (error: any) {
      set({
        error: error.message || '登录失败',
        isLoading: false
      })
      return false
    }
  },

  logout: async () => {
    set({ isLoading: true })

    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      set({
        user: null,
        isLoading: false,
        error: null
      })
    }
  },

  getProfile: async () => {
    if (!authService.isAuthenticated()) {
      return
    }

    set({ isLoading: true, error: null })

    try {
      const response = await authService.getProfile()

      if (response.success && response.data) {
        set({
          user: response.data,
          isLoading: false,
          error: null
        })
      } else {
        set({
          error: response.error || '获取用户信息失败',
          isLoading: false
        })
      }
    } catch (error: any) {
      set({
        error: error.message || '获取用户信息失败',
        isLoading: false
      })
    }
  },

  clearError: () => {
    set({ error: null })
  }
}))

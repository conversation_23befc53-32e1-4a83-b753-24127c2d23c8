# 会员系统 API 接口文档

## 概述

本文档描述了 RC Demo App 会员系统的所有 API 接口，包括会员计划、订阅管理、点数系统、模板管理和图像生成等功能。

## 基础信息

- **基础 URL**: `/api`
- **认证方式**: Session-based authentication (NextAuth.js)
- **响应格式**: JSON

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "data": {}, // 具体数据
  "message": "操作成功" // 可选
}
```

### 错误响应

```json
{
  "success": false,
  "message": "错误描述",
  "errors": [] // 可选，验证错误详情
}
```

## 1. 会员计划相关接口

### 1.1 获取所有会员计划

- **接口**: `GET /api/membership/plans`
- **描述**: 获取所有可用的会员计划
- **认证**: 不需要
- **响应示例**:

```json
{
  "success": true,
  "data": [
    {
      "id": "plan-id",
      "name": "基础套餐",
      "description": "适合轻度使用的用户",
      "price": "19.90",
      "durationDays": 30,
      "pointsIncluded": 100,
      "features": ["基础模板", "标准生成质量"],
      "isActive": true
    }
  ]
}
```

### 1.2 获取单个会员计划详情

- **接口**: `GET /api/membership/plans/{id}`
- **描述**: 根据 ID 获取会员计划详情
- **认证**: 不需要
- **参数**:
  - `id` (path): 会员计划 ID

## 2. 用户订阅相关接口

### 2.1 获取用户当前订阅状态

- **接口**: `GET /api/membership/subscription`
- **描述**: 获取用户当前的订阅状态
- **认证**: 需要登录
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "subscription": {
      "id": "sub-id",
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-02-01T00:00:00Z",
      "status": "active",
      "autoRenew": false
    },
    "plan": {
      "id": "plan-id",
      "name": "基础套餐",
      "price": "19.90"
    }
  }
}
```

### 2.2 创建新订阅

- **接口**: `POST /api/membership/subscription`
- **描述**: 创建新的会员订阅
- **认证**: 需要登录
- **请求体**:

```json
{
  "planId": "plan-id",
  "paymentId": "payment-id", // 可选
  "autoRenew": false
}
```

### 2.3 获取订阅历史

- **接口**: `GET /api/membership/subscription/history`
- **描述**: 获取用户的订阅历史记录
- **认证**: 需要登录
- **查询参数**:
  - `limit` (可选): 返回记录数量，默认 20

### 2.4 创建会员套餐支付

- **接口**: `POST /api/membership/subscription/payment`
- **描述**: 创建会员套餐支付
- **认证**: 需要登录
- **请求体**:

```json
{
  "planId": "plan-id"
}
```

- **响应示例**:

```json
{
  "success": true,
  "data": {
    "paymentUrl": "https://nowpayments.io/payment/...",
    "paymentData": {
      "id": "payment-id",
      "orderId": "sub_1234567890_user-id_plan-id",
      "status": "pending",
      "sandbox": false,
      "plan": {
        "id": "plan-id",
        "name": "基础套餐",
        "price": "19.90",
        "pointsIncluded": 100
      }
    }
  }
}
```

## 3. 点数系统相关接口

### 3.1 获取用户点数余额

- **接口**: `GET /api/membership/points`
- **描述**: 获取用户当前的点数余额
- **认证**: 需要登录
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "totalPoints": 150,
    "usedPoints": 50,
    "availablePoints": 100,
    "lastUpdated": "2024-01-01T00:00:00Z"
  }
}
```

### 3.2 获取点数交易记录

- **接口**: `GET /api/membership/points/transactions`
- **描述**: 获取用户的点数交易记录
- **认证**: 需要登录
- **查询参数**:
  - `limit` (可选): 返回记录数量，默认 50

### 3.3 获取点数套餐

- **接口**: `GET /api/membership/points/packages`
- **描述**: 获取所有可用的点数套餐
- **认证**: 不需要

### 3.4 创建点数套餐支付

- **接口**: `POST /api/membership/points/packages/payment`
- **描述**: 创建点数套餐支付
- **认证**: 需要登录
- **请求体**:

```json
{
  "packageId": "package-id"
}
```

- **响应示例**:

```json
{
  "success": true,
  "data": {
    "paymentUrl": "https://nowpayments.io/payment/...",
    "paymentData": {
      "id": "payment-id",
      "orderId": "pts_1234567890_user-id_package-id",
      "status": "pending",
      "sandbox": false,
      "package": {
        "id": "package-id",
        "name": "中包点数",
        "price": "24.90",
        "points": 150,
        "bonusPoints": 20,
        "totalPoints": 170
      }
    }
  }
}
```

## 4. 模板相关接口

### 4.1 获取模板列表

- **接口**: `GET /api/templates`
- **描述**: 获取模板列表（根据用户会员状态过滤）
- **认证**: 可选（影响返回的模板）
- **查询参数**:
  - `category` (可选): 模板分类
  - `premium` (可选): 是否只显示会员模板
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "template-id",
        "name": "风景模板",
        "description": "生成美丽的风景图片",
        "category": "landscape",
        "previewImage": "https://example.com/preview.jpg",
        "pointsCost": 5,
        "isPremium": false,
        "hasAccess": true,
        "requiresMembership": false
      }
    ],
    "userMembership": {
      "isMember": true,
      "canAccessPremium": true
    }
  }
}
```

### 4.2 获取单个模板详情

- **接口**: `GET /api/templates/{id}`
- **描述**: 根据 ID 获取模板详情
- **认证**: 可选（影响访问权限）
- **参数**:
  - `id` (path): 模板 ID

### 4.3 模板搜索

- **接口**: `GET /api/templates/search`
- **描述**: 高级模板搜索功能
- **认证**: 可选（影响返回的模板）
- **查询参数**:
  - `q` (可选): 搜索关键词
  - `category` (可选): 模板分类
  - `tags` (可选): 标签列表（逗号分隔）
  - `premium` (可选): 是否只显示会员模板
  - `sort` (可选): 排序方式（created, popular, cost, name, category）
  - `order` (可选): 排序顺序（asc, desc）
  - `limit` (可选): 返回数量限制，默认 20
  - `offset` (可选): 偏移量，默认 0
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "templates": [...],
    "pagination": {
      "total": 50,
      "limit": 20,
      "offset": 0,
      "hasMore": true
    },
    "filters": {
      "query": "风景",
      "category": "landscape",
      "tags": ["自然", "户外"],
      "premium": null,
      "sortBy": "created",
      "sortOrder": "desc"
    },
    "stats": {
      "total": 50,
      "premium": 20,
      "regular": 30,
      "categories": 5,
      "averageCost": 8
    },
    "userMembership": {
      "isMember": true,
      "canAccessPremium": true
    }
  }
}
```

### 4.4 获取模板分类

- **接口**: `GET /api/templates/categories`
- **描述**: 获取所有模板分类及统计信息
- **认证**: 不需要
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "name": "landscape",
        "totalTemplates": 15,
        "premiumTemplates": 5,
        "regularTemplates": 10,
        "totalUsage": 0,
        "averageRating": 0
      }
    ],
    "total": 5,
    "summary": {
      "totalCategories": 5,
      "totalTemplates": 50,
      "totalPremiumTemplates": 20,
      "totalRegularTemplates": 30
    }
  }
}
```

## 5. 图像生成相关接口

### 5.1 基于模板生成图像

- **接口**: `POST /api/generate/template`
- **描述**: 使用模板生成图像
- **认证**: 需要登录
- **请求体**:

```json
{
  "templateId": "template-id",
  "originalImageUrl": "https://example.com/original.jpg", // 可选
  "customPrompt": "额外的提示词" // 可选
}
```

- **响应示例**:

```json
{
  "success": true,
  "data": {
    "generationId": "gen-id",
    "status": "pending",
    "template": {
      "id": "template-id",
      "name": "风景模板",
      "pointsCost": 5
    },
    "pointsUsed": 5,
    "remainingPoints": 95
  },
  "message": "图像生成已开始，请稍候..."
}
```

### 5.2 查询生成状态

- **接口**: `GET /api/generate/status/{id}`
- **描述**: 查询图像生成的状态
- **认证**: 需要登录
- **参数**:
  - `id` (path): 生成记录 ID
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "id": "gen-id",
    "status": "completed",
    "originalImageUrl": "https://example.com/original.jpg",
    "generatedImageUrl": "https://example.com/generated.jpg",
    "prompt": "生成的提示词",
    "pointsUsed": 5,
    "generationTime": 30,
    "createdAt": "2024-01-01T00:00:00Z",
    "completedAt": "2024-01-01T00:00:30Z",
    "template": {
      "id": "template-id",
      "name": "风景模板",
      "category": "landscape"
    }
  }
}
```

### 5.3 获取生成历史

- **接口**: `GET /api/generate/history`
- **描述**: 获取用户的图像生成历史
- **认证**: 需要登录
- **查询参数**:
  - `limit` (可选): 返回记录数量，默认 50

## 6. 会员状态检查接口

### 6.1 获取完整会员状态

- **接口**: `GET /api/membership/status`
- **描述**: 获取用户完整的会员状态信息
- **认证**: 需要登录
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "membership": {
      "isMember": true,
      "subscription": {
        "id": "sub-id",
        "status": "active"
      },
      "plan": {
        "name": "基础套餐"
      }
    },
    "points": {
      "totalPoints": 150,
      "usedPoints": 50,
      "availablePoints": 100,
      "lastUpdated": "2024-01-01T00:00:00Z"
    },
    "currentSubscription": {
      "id": "sub-id",
      "planName": "基础套餐",
      "startDate": "2024-01-01T00:00:00Z",
      "endDate": "2024-02-01T00:00:00Z",
      "status": "active",
      "autoRenew": false,
      "daysRemaining": 15
    },
    "permissions": {
      "canAccessPremiumTemplates": true,
      "canUseAdvancedFeatures": true,
      "hasUnlimitedGenerations": false
    }
  }
}
```

## 7. 管理员接口

### 7.1 模板管理

#### 7.1.1 获取所有模板（管理员视图）

- **接口**: `GET /api/admin/templates`
- **描述**: 获取所有模板（包括未激活的）
- **认证**: 需要管理员权限
- **查询参数**:
  - `category` (可选): 模板分类
  - `active` (可选): 是否激活（true/false）
  - `premium` (可选): 是否会员专属（true/false）

#### 7.1.2 创建新模板

- **接口**: `POST /api/admin/templates`
- **描述**: 创建新模板
- **认证**: 需要管理员权限
- **请求体**:

```json
{
  "name": "新模板",
  "description": "模板描述",
  "category": "landscape",
  "previewImage": "https://example.com/preview.jpg",
  "prompt": "生成提示词",
  "negativePrompt": "负面提示词",
  "pointsCost": 10,
  "isPremium": false,
  "isActive": true,
  "tags": ["自然", "风景"],
  "settings": {
    "width": 1024,
    "height": 1024,
    "steps": 20,
    "guidance": 7.5
  }
}
```

#### 7.1.3 批量更新模板状态

- **接口**: `PUT /api/admin/templates`
- **描述**: 批量更新模板状态
- **认证**: 需要管理员权限
- **请求体**:

```json
{
  "templateIds": ["id1", "id2", "id3"],
  "action": "activate" // activate, deactivate, makePremium, makeRegular
}
```

#### 7.1.4 获取单个模板详情（管理员视图）

- **接口**: `GET /api/admin/templates/{id}`
- **描述**: 获取模板详情（包括统计信息）
- **认证**: 需要管理员权限

#### 7.1.5 更新单个模板

- **接口**: `PUT /api/admin/templates/{id}`
- **描述**: 更新模板信息
- **认证**: 需要管理员权限
- **请求体**: 与创建模板相同，但所有字段都是可选的

#### 7.1.6 删除模板

- **接口**: `DELETE /api/admin/templates/{id}`
- **描述**: 删除模板（软删除）
- **认证**: 需要管理员权限

### 7.2 模板统计

#### 7.2.1 获取模板统计数据

- **接口**: `GET /api/admin/templates/stats`
- **描述**: 获取详细的模板统计数据
- **认证**: 需要管理员权限
- **响应示例**:

```json
{
  "success": true,
  "data": {
    "overview": {
      "totalTemplates": 50,
      "activeTemplates": 45,
      "inactiveTemplates": 5,
      "premiumTemplates": 20,
      "regularTemplates": 30,
      "totalCategories": 5,
      "averageCost": 8
    },
    "categoryStats": [
      {
        "category": "landscape",
        "total": 15,
        "active": 14,
        "premium": 5,
        "regular": 10,
        "averageCost": 7
      }
    ],
    "costDistribution": {
      "low": 20,
      "medium": 25,
      "high": 5
    },
    "recentActivity": {
      "newTemplatesLast30Days": 8,
      "newTemplatesLast7Days": 2
    },
    "usageStats": {
      "totalGenerations": 0,
      "totalRevenue": 0,
      "averageGenerationsPerTemplate": 0,
      "mostPopularTemplates": [],
      "leastUsedTemplates": []
    },
    "trends": {
      "newTemplates": 2,
      "generationTrend": [],
      "revenueTrend": []
    },
    "topTemplates": {
      "byUsage": [...],
      "byRevenue": [...],
      "newest": [...]
    }
  }
}
```

## 错误代码说明

- **400**: 请求参数错误
- **401**: 用户未登录
- **403**: 权限不足（如访问会员专属功能）
- **404**: 资源不存在
- **500**: 服务器内部错误

## 业务流程示例

### 用户购买会员套餐流程

1. `GET /api/membership/plans` - 获取会员计划
2. `POST /api/membership/subscription/payment` - 创建支付
3. 用户完成支付（跳转到 NOWPayments）
4. 支付成功后自动回调 webhook 创建订阅
5. `GET /api/membership/status` - 确认会员状态

### 用户使用模板生成图像流程

1. `GET /api/templates` - 获取可用模板
2. `GET /api/membership/points` - 检查点数余额
3. `POST /api/generate/template` - 开始生成
4. `GET /api/generate/status/{id}` - 轮询生成状态
5. `GET /api/generate/history` - 查看生成历史

### 用户购买点数流程

1. `GET /api/membership/points/packages` - 获取点数套餐
2. `POST /api/membership/points/packages/payment` - 创建支付
3. 用户完成支付（跳转到 NOWPayments）
4. 支付成功后自动回调 webhook 添加点数
5. `GET /api/membership/points` - 确认点数到账

## 支付系统说明

### 支付流程

1. **创建支付**: 调用支付接口创建 NOWPayments 发票
2. **用户支付**: 跳转到 NOWPayments 支付页面
3. **支付回调**: NOWPayments 调用 webhook 通知支付结果
4. **处理结果**: 根据支付状态自动处理订阅或点数

### 环境变量配置

```env
# NOWPayments 配置
NOWPAYMENTS_API_KEY=your_production_api_key
NOWPAYMENTS_SANDBOX_API_KEY=your_sandbox_api_key
NOWPAYMENTS_USE_SANDBOX=true  # 开发环境使用沙箱

# 站点配置
NEXT_PUBLIC_SITE_URL=https://your-domain.com

# 开发环境模拟支付
NODE_ENV=development
MOCK_PAYMENTS=true  # 开发环境使用模拟支付
```

### Webhook 端点

- 会员套餐: `/api/membership/subscription/webhook`
- 点数套餐: `/api/membership/points/packages/webhook`

### 订单 ID 格式

- 会员套餐: `sub_{timestamp}_{userId}_{planId}`
- 点数套餐: `pts_{timestamp}_{userId}_{packageId}`

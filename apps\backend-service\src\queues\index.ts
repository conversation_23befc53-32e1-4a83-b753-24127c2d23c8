import type { MessageBatch, ExecutionContext } from '@cloudflare/workers-types'
import type { Env } from '@/types/env'
import type { AudioProcessingTask, ImageGenerationTask, FaceSwapTask } from './types'
import type { VideoGenerationTask } from '@/types/video'
import type { PhotoAlbumGenerationTask } from './consumers/photo-album-consumer'
import {
  AudioQueueConsumer,
  ImageQueueConsumer,
  VideoQueueConsumer,
  FaceSwapQueueConsumer
} from './consumers'
import { PhotoAlbumGenerationConsumer } from './consumers/photo-album-consumer'
import { QueueBatchProcessor } from './utils/queue-handler'

/**
 * 队列管理器 - 统一处理所有队列消费
 */
export class QueueManager {
  private audioConsumer: AudioQueueConsumer
  private imageConsumer: ImageQueueConsumer
  private videoConsumer: VideoQueueConsumer
  private photoAlbumConsumer: PhotoAlbumGenerationConsumer
  private faceSwapConsumer: FaceSwapQueueConsumer
  private audioProcessor: QueueBatchProcessor<AudioProcessingTask>
  private imageProcessor: QueueBatchProcessor<ImageGenerationTask>
  private videoProcessor: QueueBatchProcessor<VideoGenerationTask>
  private photoAlbumProcessor: QueueBatchProcessor<PhotoAlbumGenerationTask>
  private faceSwapProcessor: QueueBatchProcessor<FaceSwapTask>

  constructor(private env: Env) {
    // 初始化消费者
    this.audioConsumer = new AudioQueueConsumer(env)
    this.imageConsumer = new ImageQueueConsumer(env)
    this.videoConsumer = new VideoQueueConsumer(env)
    this.photoAlbumConsumer = new PhotoAlbumGenerationConsumer(env)
    this.faceSwapConsumer = new FaceSwapQueueConsumer(env)

    // 初始化批处理器
    this.audioProcessor = new QueueBatchProcessor('音频处理', (task: AudioProcessingTask) =>
      this.audioConsumer.process(task)
    )

    this.imageProcessor = new QueueBatchProcessor('图片生成', (task: ImageGenerationTask) =>
      this.imageConsumer.process(task)
    )

    this.videoProcessor = new QueueBatchProcessor('视频生成', (task: VideoGenerationTask) =>
      this.videoConsumer.process(task)
    )

    this.photoAlbumProcessor = new QueueBatchProcessor(
      '写真集生成',
      (task: PhotoAlbumGenerationTask) =>
        this.photoAlbumConsumer.handleMessage({
          messages: [{ body: task, ack: () => {}, retry: () => {} }]
        } as any)
    )

    this.faceSwapProcessor = new QueueBatchProcessor('换脸处理', (task: FaceSwapTask) =>
      this.faceSwapConsumer.process(task)
    )
  }

  /**
   * 处理音频队列
   */
  async handleAudioQueue(batch: MessageBatch<AudioProcessingTask>): Promise<void> {
    await this.audioProcessor.processBatch(batch)
  }

  /**
   * 处理图片队列
   */
  async handleImageQueue(batch: MessageBatch<ImageGenerationTask>): Promise<void> {
    await this.imageProcessor.processBatch(batch)
  }

  /**
   * 处理视频队列
   */
  async handleVideoQueue(batch: MessageBatch<VideoGenerationTask>): Promise<void> {
    await this.videoProcessor.processBatch(batch)
  }

  /**
   * 处理写真集队列
   */
  async handlePhotoAlbumQueue(batch: MessageBatch<PhotoAlbumGenerationTask>): Promise<void> {
    await this.photoAlbumConsumer.handleMessage(batch)
  }

  /**
   * 处理换脸队列
   */
  async handleFaceSwapQueue(batch: MessageBatch<FaceSwapTask>): Promise<void> {
    await this.faceSwapProcessor.processBatch(batch)
  }
}

/**
 * 创建队列处理函数 - 用于 index.ts 中的队列处理
 */
export function createQueueHandlers(env: Env) {
  const queueManager = new QueueManager(env)

  return {
    // 音频处理队列处理器
    audioQueueHandler: async (
      batch: MessageBatch<AudioProcessingTask>,
      env: Env,
      ctx: ExecutionContext
    ): Promise<void> => {
      await queueManager.handleAudioQueue(batch)
    },

    // 图片生成队列处理器
    imageQueueHandler: async (
      batch: MessageBatch<ImageGenerationTask>,
      env: Env,
      ctx: ExecutionContext
    ): Promise<void> => {
      await queueManager.handleImageQueue(batch)
    },

    // 视频生成队列处理器
    videoQueueHandler: async (
      batch: MessageBatch<VideoGenerationTask>,
      env: Env,
      ctx: ExecutionContext
    ): Promise<void> => {
      await queueManager.handleVideoQueue(batch)
    },

    // 写真集生成队列处理器
    photoAlbumQueueHandler: async (
      batch: MessageBatch<PhotoAlbumGenerationTask>,
      env: Env,
      ctx: ExecutionContext
    ): Promise<void> => {
      await queueManager.handlePhotoAlbumQueue(batch)
    },

    // 换脸处理队列处理器
    faceSwapQueueHandler: async (
      batch: MessageBatch<FaceSwapTask>,
      env: Env,
      ctx: ExecutionContext
    ): Promise<void> => {
      await queueManager.handleFaceSwapQueue(batch)
    }
  }
}

// 重新导出类型
export * from './types'
export * from './consumers'

import type { Message } from 'ai'
import { useCopyToClipboard } from 'usehooks-ts'

import { CopyIcon } from './icons'
import { Button } from './ui/button'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip'
import { memo } from 'react'

export function PureMessageActions({
  message,
  isLoading
}: {
  chatId: string
  message: Message
  isLoading: boolean
}) {
  const [_, copyToClipboard] = useCopyToClipboard()

  if (isLoading) return null
  if (message.role === 'user') return null

  return (
    <TooltipProvider delayDuration={0}>
      <div className="flex flex-row space-x-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="py-1 px-2 h-fit text-muted-foreground"
              variant="outline"
              onClick={async () => {
                const textFromParts = message.parts
                  ?.filter(part => part.type === 'text')
                  .map(part => part.text)
                  .join('\n')
                  .trim()

                if (!textFromParts) {
                  return
                }

                await copyToClipboard(textFromParts)
              }}
            >
              <CopyIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Copy</TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  )
}

export const MessageActions = memo(PureMessageActions, (prevProps, nextProps) => {
  if (prevProps.isLoading !== nextProps.isLoading) return false

  return true
})

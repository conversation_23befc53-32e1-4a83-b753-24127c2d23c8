import i18n from '@/i18n'

// 根据环境变量确定应用版本配置
const APP_VERSION = (globalThis as any).process?.env?.APP_VERSION || 'domestic' // 默认国内版

// 根据版本获取默认语言配置
const getVersionDefaults = (version: string) => {
  switch (version) {
    case 'international':
      return {
        DEFAULT_LANGUAGE: 'en',
        FALLBACK_LANGUAGE: 'zh'
      }
    case 'domestic':
    default:
      return {
        DEFAULT_LANGUAGE: 'zh',
        FALLBACK_LANGUAGE: 'en'
      }
  }
}

// 国际化默认配置
export const I18N_DEFAULTS = getVersionDefaults(APP_VERSION)

// 支持的语言配置
export interface LanguageConfig {
  code: string
  name: string
  nativeName: string
  region?: string
  fallback?: string
}

// 支持的语言列表（可以动态扩展）
export const SUPPORTED_LANGUAGES: Record<string, LanguageConfig> = {
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    region: 'CN',
    fallback: 'en'
  },
  'zh-TW': {
    code: 'zh-TW',
    name: 'Traditional Chinese',
    nativeName: '繁體中文',
    region: 'TW',
    fallback: 'zh'
  },
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    region: 'US'
  },
  ja: {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    region: 'JP',
    fallback: 'en'
  },
  ko: {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    region: 'KR',
    fallback: 'en'
  },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    region: 'ES',
    fallback: 'en'
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    region: 'FR',
    fallback: 'en'
  },
  de: {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    region: 'DE',
    fallback: 'en'
  },
  pt: {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    region: 'PT',
    fallback: 'en'
  },
  ru: {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    region: 'RU',
    fallback: 'en'
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    region: 'SA',
    fallback: 'en'
  }
}

// 获取当前语言设置
export const getCurrentLanguage = (): string => {
  return i18n.language || I18N_DEFAULTS.DEFAULT_LANGUAGE
}

// 获取语言配置
export const getLanguageConfig = (language: string): LanguageConfig | null => {
  return SUPPORTED_LANGUAGES[language] || null
}

// 格式化 Accept-Language 头部
export const formatAcceptLanguage = (language: string): string => {
  const config = getLanguageConfig(language)
  if (!config) {
    const defaultConfig = SUPPORTED_LANGUAGES[I18N_DEFAULTS.DEFAULT_LANGUAGE]
    return `${I18N_DEFAULTS.DEFAULT_LANGUAGE}-${defaultConfig.region},${I18N_DEFAULTS.DEFAULT_LANGUAGE};q=0.9,${I18N_DEFAULTS.FALLBACK_LANGUAGE};q=0.8`
  }

  const region = config.region
  if (region) {
    return `${language}-${region},${language};q=0.9,${I18N_DEFAULTS.FALLBACK_LANGUAGE};q=0.8`
  }

  return `${language};q=0.9,${I18N_DEFAULTS.FALLBACK_LANGUAGE};q=0.8`
}

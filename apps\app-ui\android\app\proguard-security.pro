# =============================================
# 高级安全混淆规则 (仅用于 secureRelease 构建)
# =============================================

# 极致混淆设置
-dontusemixedcaseclassnames
-dontskipnonpubliclibraryclasses
-verbose

# 移除所有调试信息
-keepattributes !SourceFile,!LineNumberTable

# =============================================
# 反调试保护
# =============================================

# 在高安全版本中移除大部分日志输出
-assumenosideeffects class android.util.Log {
    public static *** d(...);
    public static *** v(...);
    # 在高安全版本中也保留错误和警告日志
    # public static *** i(...);
    # public static *** w(...);
    # public static *** e(...);
    public static *** wtf(...);
    public static *** println(...);
}

# 移除 System.out 输出
-assumenosideeffects class java.io.PrintStream {
    public void println(%);
    public void print(%);
}

# 移除异常堆栈跟踪
-assumenosideeffects class java.lang.Exception {
    public void printStackTrace();
}

# =============================================
# 字符串和资源加密
# =============================================

# 深度字符串混淆
-adaptclassstrings
-adaptresourcefilenames    **.xml,**.html,**.js,**.css,**.properties,**.json
-adaptresourcefilecontents **.xml,**.html,**.js,**.css,**.properties,**.json

# 重命名资源文件
-repackageclasses 'o'
-flattenpackagehierarchy 'o'

# =============================================
# 控制流混淆
# =============================================

# 最大化混淆
-allowaccessmodification
-mergeinterfacesaggressively
-overloadaggressively

# 移除未使用的代码
-dontshrink
-dontoptimize

# =============================================
# 反射保护
# =============================================

# 混淆反射调用
-adaptclassstrings
-keepattributes !*Annotation*,!Signature,!InnerClasses,!EnclosingMethod

# =============================================
# 本地库保护
# =============================================

# 保护 native 方法不被重命名（但参数可以混淆）
-keepclasseswithmembernames,includedescriptorclasses class * {
    native <methods>;
}

# =============================================
# 应用完整性检查
# =============================================

# 保持签名验证相关的类
-keep class com.pleasurehub.app.security.SignatureValidator { *; }
-keep class com.pleasurehub.app.security.IntegrityChecker { *; }

# =============================================
# 高级优化
# =============================================

# 启用所有优化
-optimizations !code/simplification/variable,!code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*,!code/allocation/variable
-optimizationpasses 10

# 假设没有副作用的方法
-assumenosideeffects class java.lang.StringBuilder {
    public java.lang.StringBuilder();
    public java.lang.StringBuilder(int);
    public java.lang.StringBuilder(java.lang.String);
    public java.lang.StringBuilder append(java.lang.Object);
    public java.lang.StringBuilder append(java.lang.String);
    public java.lang.StringBuilder append(java.lang.StringBuffer);
    public java.lang.StringBuilder append(char[]);
    public java.lang.StringBuilder append(char[], int, int);
    public java.lang.StringBuilder append(boolean);
    public java.lang.StringBuilder append(char);
    public java.lang.StringBuilder append(int);
    public java.lang.StringBuilder append(long);
    public java.lang.StringBuilder append(float);
    public java.lang.StringBuilder append(double);
    public java.lang.String toString();
}

# =============================================
# 防止常见逆向工程工具
# =============================================

# 混淆类名和包名
-repackageclasses 'a.b.c'
-allowaccessmodification

# 移除调试相关的属性
-keepattributes !LocalVariableTable,!LocalVariableTypeTable,!MethodParameters 
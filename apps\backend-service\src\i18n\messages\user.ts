// 用户相关消息
export const userMessages = {
  zh: {
    'user.not_found': '用户不存在',
    'user.created': '用户创建成功',
    'user.updated': '用户更新成功',
    'user.deleted': '用户删除成功',
    'user.login_success': '登录成功',
    'user.login_failed': '登录失败',
    'user.logout_success': '登出成功',
    'user.password_changed': '密码修改成功',
    'user.email_invalid': '邮箱格式无效',
    'user.password_too_short': '密码至少需要6个字符',
    'user.profile_updated': '用户资料更新成功',
    'user.profile_get_failed': '获取用户资料失败',
    'user.profile_update_failed': '更新用户资料失败',
    'user.subscription_get_failed': '获取订阅信息失败',
    'user.subscription_history_get_failed': '获取订阅历史失败',
    'user.points_get_failed': '获取积分信息失败',
    'user.points_transactions_get_failed': '获取积分交易记录失败',
    'user.status_get_failed': '获取用户状态失败',
    'user.default_name': '用户',
    'user.info_not_found': '未找到用户信息'
  },
  'zh-TW': {
    'user.not_found': '使用者不存在',
    'user.created': '使用者建立成功',
    'user.updated': '使用者更新成功',
    'user.deleted': '使用者刪除成功',
    'user.login_success': '登入成功',
    'user.login_failed': '登入失敗',
    'user.logout_success': '登出成功',
    'user.password_changed': '密碼修改成功',
    'user.email_invalid': '電子郵件格式無效',
    'user.password_too_short': '密碼至少需要6個字元',
    'user.profile_updated': '使用者資料更新成功',
    'user.profile_get_failed': '取得使用者資料失敗',
    'user.profile_update_failed': '更新使用者資料失敗',
    'user.subscription_get_failed': '取得訂閱資訊失敗',
    'user.subscription_history_get_failed': '取得訂閱歷史失敗',
    'user.points_get_failed': '取得積分資訊失敗',
    'user.points_transactions_get_failed': '取得積分交易記錄失敗',
    'user.status_get_failed': '取得使用者狀態失敗',
    'user.default_name': '使用者',
    'user.info_not_found': '未找到使用者資訊'
  },
  ja: {
    'user.default_name': 'ユーザー',
    'user.not_found': 'ユーザーが見つかりません',
    'user.created': 'ユーザーの作成に成功しました',
    'user.updated': 'ユーザーの更新に成功しました',
    'user.deleted': 'ユーザーの削除に成功しました',
    'user.login_success': 'ログインに成功しました',
    'user.login_failed': 'ログインに失敗しました',
    'user.logout_success': 'ログアウトに成功しました',
    'user.password_changed': 'パスワードの変更に成功しました',
    'user.email_invalid': 'メールアドレスの形式が無効です',
    'user.password_too_short': 'パスワードは6文字以上である必要があります',
    'user.profile_updated': 'ユーザープロフィールの更新に成功しました',
    'user.profile_get_failed': 'ユーザープロフィールの取得に失敗しました',
    'user.profile_update_failed': 'ユーザープロフィールの更新に失敗しました',
    'user.subscription_get_failed': 'サブスクリプション情報の取得に失敗しました',
    'user.subscription_history_get_failed': 'サブスクリプション履歴の取得に失敗しました',
    'user.points_get_failed': 'ポイント情報の取得に失敗しました',
    'user.points_transactions_get_failed': 'ポイント取引の取得に失敗しました',
    'user.status_get_failed': 'ユーザーステータスの取得に失敗しました',
    'user.info_not_found': 'ユーザー情報が見つかりません'
  },
  es: {
    'user.default_name': 'Usuario',
    'user.not_found': 'Usuario no encontrado',
    'user.created': 'Usuario creado correctamente',
    'user.updated': 'Usuario actualizado correctamente',
    'user.deleted': 'Usuario eliminado correctamente',
    'user.login_success': 'Inicio de sesión exitoso',
    'user.login_failed': 'Error al iniciar sesión',
    'user.logout_success': 'Cierre de sesión exitoso',
    'user.password_changed': 'Contraseña cambiada correctamente',
    'user.email_invalid': 'Formato de correo electrónico inválido',
    'user.password_too_short': 'La contraseña debe tener al menos 6 caracteres',
    'user.profile_updated': 'Perfil de usuario actualizado correctamente',
    'user.profile_get_failed': 'Error al obtener el perfil del usuario',
    'user.profile_update_failed': 'Error al actualizar el perfil del usuario',
    'user.subscription_get_failed': 'Error al obtener la información de suscripción',
    'user.subscription_history_get_failed': 'Error al obtener el historial de suscripciones',
    'user.points_get_failed': 'Error al obtener la información de puntos',
    'user.points_transactions_get_failed': 'Error al obtener las transacciones de puntos',
    'user.status_get_failed': 'Error al obtener el estado del usuario',
    'user.info_not_found': 'No se encontró la información del usuario'
  },
  en: {
    'user.default_name': 'User',
    'user.not_found': 'User not found',
    'user.created': 'User created successfully',
    'user.updated': 'User updated successfully',
    'user.deleted': 'User deleted successfully',
    'user.login_success': 'Login successful',
    'user.login_failed': 'Login failed',
    'user.logout_success': 'Logout successful',
    'user.password_changed': 'Password changed successfully',
    'user.email_invalid': 'Invalid email format',
    'user.password_too_short': 'Password must be at least 6 characters',
    'user.profile_updated': 'User profile updated successfully',
    'user.profile_get_failed': 'Failed to get user profile',
    'user.profile_update_failed': 'Failed to update user profile',
    'user.subscription_get_failed': 'Failed to get subscription information',
    'user.subscription_history_get_failed': 'Failed to get subscription history',
    'user.points_get_failed': 'Failed to get points information',
    'user.points_transactions_get_failed': 'Failed to get points transactions',
    'user.status_get_failed': 'Failed to get user status',
    'user.info_not_found': 'User information not found'
  }
}

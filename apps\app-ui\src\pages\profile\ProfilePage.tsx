import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { useAuth } from '@/contexts/auth-context'
import ProfileSetup from '@/components/ProfileSetup'

// HeroUI 组件
import { addToast, Button } from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

export default function ProfilePage() {
  const navigate = useNavigate()
  const { t } = useTranslation('profile')
  const { user, status } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // 检查认证状态
    if (status === 'unauthenticated') {
      addToast({
        title: t('page.login_required'),
        color: 'warning'
      })
      navigate('/login')
      return
    }

    if (status === 'authenticated') {
      setIsLoading(false)
    }
  }, [status, navigate])

  const handleComplete = (data: any) => {
    console.log('个人资料更新成功:', data)
    // 延迟跳转到首页
    setTimeout(() => {
      navigate('/')
    }, 1000)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Icon icon="lucide:loader-2" className="w-8 h-8 animate-spin text-primary" />
          <p className="text-default-500">{t('page.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background flex flex-col">
      <div className="absolute top-0 left-0 w-screen">
        <img src="/images/login/img-bg.svg" className="w-full h-full object-cover" />
      </div>

      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12 relative z-10">
        <Button
          isIconOnly
          variant="light"
          className="text-white hover:text-foreground"
          onPress={() => navigate(-1)}
        >
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col px-6 pb-20">
        {/* ProfileSetup组件 - 不显示跳过按钮 */}
        <ProfileSetup
          showSkip={false}
          onComplete={handleComplete}
          title={t('page.title')}
          subtitle={t('page.subtitle')}
        />
      </div>
    </div>
  )
}

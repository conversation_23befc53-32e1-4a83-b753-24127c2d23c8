{"root": ["./src/index.ts", "./src/lib/cache.ts", "./src/lib/email.ts", "./src/lib/supabase.ts", "./src/lib/ai/actions.ts", "./src/lib/ai/entitlements.ts", "./src/lib/ai/index.ts", "./src/lib/ai/models.ts", "./src/lib/ai/prompts.ts", "./src/lib/ai/providers.ts", "./src/lib/ai/stream.ts", "./src/lib/ai/role/unlock.ts", "./src/lib/ai/role/audio.ts", "./src/lib/ai/role/common.ts", "./src/lib/ai/role/device.ts", "./src/lib/ai/role/format.ts", "./src/lib/ai/role/index.ts", "./src/lib/ai/role/jinlin.ts", "./src/lib/ai/role/mapping.ts", "./src/lib/ai/role/suggestedactions.ts", "./src/lib/chat/handlers.ts", "./src/lib/commission/service.ts", "./src/lib/db/index.ts", "./src/lib/db/migrate-scripts.ts", "./src/lib/db/migrate.ts", "./src/lib/db/schema.ts", "./src/lib/db/seed-devices.ts", "./src/lib/db/seed.ts", "./src/lib/db/supabase-types.ts", "./src/lib/db/queries/activation-code.ts", "./src/lib/db/queries/admin-referral.ts", "./src/lib/db/queries/audio.ts", "./src/lib/db/queries/base.ts", "./src/lib/db/queries/character.ts", "./src/lib/db/queries/chat.ts", "./src/lib/db/queries/commission.ts", "./src/lib/db/queries/devices.ts", "./src/lib/db/queries/index.ts", "./src/lib/db/queries/media-generation.ts", "./src/lib/db/queries/membership.ts", "./src/lib/db/queries/payment.ts", "./src/lib/db/queries/points-package.ts", "./src/lib/db/queries/referral.ts", "./src/lib/db/queries/script-purchase.ts", "./src/lib/db/queries/script.ts", "./src/lib/db/queries/system-config.ts", "./src/lib/db/queries/template.ts", "./src/lib/db/queries/user.ts", "./src/lib/db/queries/voice.ts", "./src/lib/dialogue/scripts.ts", "./src/lib/image-generation/background-service.ts", "./src/lib/image-generation/index.ts", "./src/lib/image-generation/task-manager.ts", "./src/lib/image-generation/upload.ts", "./src/lib/image-generation/generators/background.ts", "./src/lib/image-generation/generators/insa3d.ts", "./src/lib/image-generation/generators/yunwu.ts", "./src/lib/langchain/index.ts", "./src/lib/langchain/config/index.ts", "./src/lib/langchain/engine/chat-engine.ts", "./src/lib/langchain/engine/providers.ts", "./src/lib/langchain/integration/hono-adapter.ts", "./src/lib/langchain/multimodal/coordinator.ts", "./src/lib/langchain/parsers/output-parser.ts", "./src/lib/langchain/parsers/tag-parser.ts", "./src/lib/langchain/prompts/prompt-manager.ts", "./src/lib/langchain/prompts/templates-backup.ts", "./src/lib/langchain/prompts/templates.ts", "./src/lib/langchain/prompts/templates_copy.ts", "./src/lib/langchain/stream/stream-response.ts", "./src/lib/langchain/types/chat.ts", "./src/lib/langchain/types/index.ts", "./src/lib/langchain/types/mapping.ts", "./src/lib/langchain/types/memory.ts", "./src/lib/langchain/types/multimodal.ts", "./src/lib/membership/config.ts", "./src/lib/membership/cycle-utils.ts", "./src/lib/membership/init-config.ts", "./src/lib/membership/points-cycle.ts", "./src/lib/membership/points.ts", "./src/lib/membership/service-points.ts", "./src/lib/membership/service.ts", "./src/lib/membership/usage-examples.ts", "./src/lib/payment/alipay-service.ts", "./src/lib/payment/mock-payment.ts", "./src/lib/payment/payment-config.ts", "./src/lib/payment/payment-factory.ts", "./src/lib/payment/real-payment-service.ts", "./src/lib/payment/test-subscription.ts", "./src/lib/speech/elevenlabs-speech.ts", "./src/lib/tts/elevenlabs-service.ts", "./src/lib/tts/tts-service.ts", "./src/lib/utils/index.ts", "./src/lib/utils/r2-upload.ts", "./src/middleware/auth.ts", "./src/middleware/cors.ts", "./src/middleware/permission.ts", "./src/queues/index.ts", "./src/queues/consumers/audio-consumer.ts", "./src/queues/consumers/image-consumer.ts", "./src/queues/consumers/index.ts", "./src/queues/consumers/photo-album-consumer.ts", "./src/queues/consumers/video-consumer.ts", "./src/queues/types/index.ts", "./src/queues/utils/queue-handler.ts", "./src/routes/activation-codes.ts", "./src/routes/admin-config.ts", "./src/routes/admin-devices.ts", "./src/routes/admin-marketing.ts", "./src/routes/admin-membership.ts", "./src/routes/admin-referral.ts", "./src/routes/admin-users.ts", "./src/routes/audio.ts", "./src/routes/auth.ts", "./src/routes/background.ts", "./src/routes/characters.ts", "./src/routes/chat.ts", "./src/routes/chatv2.ts", "./src/routes/devices.ts", "./src/routes/dialogue.ts", "./src/routes/health.ts", "./src/routes/history.ts", "./src/routes/image-generation-v2.ts", "./src/routes/image-generation-with-points.ts", "./src/routes/image-generation.ts", "./src/routes/membership.ts", "./src/routes/multimodal-video-generation.ts", "./src/routes/payment.ts", "./src/routes/photo-album-generation.ts", "./src/routes/points-cycle.ts", "./src/routes/points.ts", "./src/routes/referral.ts", "./src/routes/scripts.ts", "./src/routes/speech-to-text.ts", "./src/routes/templates.ts", "./src/routes/tts-with-points.ts", "./src/routes/tts.ts", "./src/routes/tts2.ts", "./src/routes/tts3.ts", "./src/routes/upload.ts", "./src/routes/users.ts", "./src/routes/video-generation-v2.ts", "./src/routes/voices.ts", "./src/types/api.ts", "./src/types/audio.ts", "./src/types/dialogue.ts", "./src/types/env.ts", "./src/types/image-generation.ts", "./src/types/image.ts", "./src/types/script.ts", "./src/types/template.ts", "./src/types/video.ts", "./src/types/voice.ts"], "version": "5.8.3"}
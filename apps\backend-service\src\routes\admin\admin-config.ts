import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import type { Env } from '@/types/env';
import { authMiddleware } from '@/middleware/auth';
import { createPointsConfigManager } from '@/lib/membership/config';
import { initializePointsConfig, getConfigReport } from '@/lib/membership/init-config';

/**
 * 管理后台配置API
 * 用于动态配置积分消费和会员等级
 */

const app = new Hono<{ Bindings: Env }>();

// 更新积分配置的验证schema
const updatePointsCostSchema = z.object({
  feature: z.enum([
    'IMAGE_GENERATION',
    'VOICE_GENERATION',
    'SCRIPT_PURCHASE',
    'GALLERY_GENERATION',
    'VIDEO_GENERATION',
  ]),
  cost: z.number().min(0).max(1000, '积分消费不能超过1000'),
});

// 批量更新积分配置的验证schema
const batchUpdatePointsCostsSchema = z.object({
  costs: z.record(z.string(), z.number().min(0).max(1000)),
});

/**
 * PUT /api/admin/config/points-cost
 * 更新单个功能的积分消费配置
 */
app.put('/points-cost', authMiddleware, zValidator('json', updatePointsCostSchema), async (c) => {
  try {
    // TODO: 添加管理员权限检查
    const user = c.get('user');
    const { feature, cost } = c.req.valid('json');

    const configManager = createPointsConfigManager(c.env);
    const success = await configManager.updatePointsCost(feature, cost);

    if (!success) {
      return c.json(
        {
          success: false,
          error: '更新积分配置失败',
        },
        500
      );
    }

    return c.json({
      success: true,
      data: {
        feature,
        cost,
        updatedBy: user.id,
        updatedAt: new Date().toISOString(),
      },
      message: `${feature}积分配置已更新为${cost}积分`,
    });
  } catch (error) {
    console.error('更新积分配置失败:', error);
    return c.json(
      {
        success: false,
        error: '更新积分配置失败',
      },
      500
    );
  }
});

/**
 * PUT /api/admin/config/points-costs
 * 批量更新积分消费配置
 */
app.put(
  '/points-costs',
  authMiddleware,
  zValidator('json', batchUpdatePointsCostsSchema),
  async (c) => {
    try {
      // TODO: 添加管理员权限检查
      const user = c.get('user');
      const { costs } = c.req.valid('json');

      const configManager = createPointsConfigManager(c.env);
      const results = [];

      for (const [feature, cost] of Object.entries(costs)) {
        const success = await configManager.updatePointsCost(feature, cost);
        results.push({
          feature,
          cost,
          success,
        });
      }

      const successCount = results.filter((r) => r.success).length;

      return c.json({
        success: true,
        data: {
          results,
          successCount,
          totalCount: results.length,
          updatedBy: user.id,
          updatedAt: new Date().toISOString(),
        },
        message: `成功更新${successCount}/${results.length}个积分配置`,
      });
    } catch (error) {
      console.error('批量更新积分配置失败:', error);
      return c.json(
        {
          success: false,
          error: '批量更新积分配置失败',
        },
        500
      );
    }
  }
);

/**
 * GET /api/admin/config/points-costs
 * 获取当前积分配置（管理员视图）
 */
app.get('/points-costs', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限检查

    const configManager = createPointsConfigManager(c.env);
    const costs = await configManager.getAllPointsCosts();
    const defaultConfig = configManager.getDefaultConfig();

    return c.json({
      success: true,
      data: {
        current: costs,
        default: defaultConfig.points,
        features: [
          {
            key: 'IMAGE_GENERATION',
            name: '图片生成',
            description: '每张图片消费积分',
            current: costs.IMAGE_GENERATION,
            default: defaultConfig.points.IMAGE_GENERATION,
          },
          {
            key: 'VOICE_GENERATION',
            name: '语音生成',
            description: '每次TTS消费积分',
            current: costs.VOICE_GENERATION,
            default: defaultConfig.points.VOICE_GENERATION,
          },
          {
            key: 'SCRIPT_PURCHASE',
            name: '剧本购买',
            description: '购买剧本消费积分',
            current: costs.SCRIPT_PURCHASE,
            default: defaultConfig.points.SCRIPT_PURCHASE,
          },
          {
            key: 'GALLERY_GENERATION',
            name: '写真集生成',
            description: '生成写真集消费积分',
            current: costs.GALLERY_GENERATION,
            default: defaultConfig.points.GALLERY_GENERATION,
          },
          {
            key: 'VIDEO_GENERATION',
            name: '视频生成',
            description: '生成视频消费积分',
            current: costs.VIDEO_GENERATION,
            default: defaultConfig.points.VIDEO_GENERATION,
          },
        ],
      },
    });
  } catch (error) {
    console.error('获取积分配置失败:', error);
    return c.json(
      {
        success: false,
        error: '获取积分配置失败',
      },
      500
    );
  }
});

/**
 * POST /api/admin/config/reset-points-costs
 * 重置积分配置为默认值
 */
app.post('/reset-points-costs', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限检查
    const user = c.get('user');

    const configManager = createPointsConfigManager(c.env);
    const defaultConfig = configManager.getDefaultConfig();
    const results = [];

    for (const [feature, cost] of Object.entries(defaultConfig.points)) {
      const success = await configManager.updatePointsCost(feature, cost);
      results.push({
        feature,
        cost,
        success,
      });
    }

    // 清除缓存
    configManager.clearCache();

    const successCount = results.filter((r) => r.success).length;

    return c.json({
      success: true,
      data: {
        results,
        successCount,
        totalCount: results.length,
        resetBy: user.id,
        resetAt: new Date().toISOString(),
      },
      message: `成功重置${successCount}/${results.length}个积分配置为默认值`,
    });
  } catch (error) {
    console.error('重置积分配置失败:', error);
    return c.json(
      {
        success: false,
        error: '重置积分配置失败',
      },
      500
    );
  }
});

/**
 * POST /api/admin/config/initialize
 * 初始化积分配置（首次部署时使用）
 */
app.post('/initialize', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限检查
    const user = c.get('user');

    await initializePointsConfig(c.env);

    // 获取初始化后的配置报告
    const report = await getConfigReport(c.env);

    return c.json({
      success: true,
      data: {
        initializedBy: user.id,
        initializedAt: new Date().toISOString(),
        report,
      },
      message: '积分配置初始化成功',
    });
  } catch (error) {
    console.error('初始化积分配置失败:', error);
    return c.json(
      {
        success: false,
        error: '初始化积分配置失败',
      },
      500
    );
  }
});

/**
 * GET /api/admin/config/report
 * 获取配置状态报告
 */
app.get('/report', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限检查

    const report = await getConfigReport(c.env);

    return c.json({
      success: true,
      data: report,
    });
  } catch (error) {
    console.error('获取配置报告失败:', error);
    return c.json(
      {
        success: false,
        error: '获取配置报告失败',
      },
      500
    );
  }
});

export default app;

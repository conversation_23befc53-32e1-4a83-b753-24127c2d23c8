// Hono 适配器 - 将 LangChain 系统集成到 Hono 路由

import type { Env } from '../../../types/env';
import type { LangChainEnv } from '../types';

/**
 * 将 Cloudflare Workers Env 转换为 LangChainEnv
 */
function convertEnv(env: Env): LangChainEnv {
  return {
    XAI_API_KEY: env.XAI_API_KEY,
  };
}

/**
 * 验证 LangChain 环境配置
 */
export function validateLangChainEnv(env: Env): boolean {
  try {
    const langchainEnv = convertEnv(env);
    return !!langchainEnv.XAI_API_KEY;
  } catch {
    return false;
  }
}

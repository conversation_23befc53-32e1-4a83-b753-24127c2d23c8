import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

export interface DeviceCommandSet {
  id: string
  name: string
  description?: string
  command: string
  broadcast?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateCommandSetRequest {
  name: string
  description?: string
  command: string
  broadcast?: string
  isActive?: boolean
}


export class DeviceCommandSetService {
  // 获取指令集列表
  async getCommandSets(params: {
    page?: number
    pageSize?: number
    keyword?: string
    isActive?: boolean
  }): Promise<ApiResponse<PaginatedResponse<DeviceCommandSet>>> {
    return await apiService.get<PaginatedResponse<DeviceCommandSet>>('/admin/devices/command-sets', { params })
  }

  // 获取指令集详情
  async getCommandSet(id: string): Promise<ApiResponse<DeviceCommandSet>> {
    return await apiService.get<DeviceCommandSet>(`/admin/devices/command-sets/${id}`)
  }

  // 创建指令集
  async createCommandSet(data: CreateCommandSetRequest): Promise<ApiResponse<DeviceCommandSet>> {
    return await apiService.post<DeviceCommandSet>('/admin/devices/command-sets', data)
  }

  // 更新指令集
  async updateCommandSet(
    id: string,
    data: Partial<CreateCommandSetRequest>
  ): Promise<ApiResponse<DeviceCommandSet>> {
    return await apiService.put<DeviceCommandSet>(`/admin/devices/command-sets/${id}`, data)
  }

  // 删除指令集
  async deleteCommandSet(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/devices/command-sets/${id}`)
  }

  // 切换指令集状态
  async toggleStatus(id: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/devices/command-sets/${id}/toggle-status`, { isActive })
  }
}

export const commandSetService = new DeviceCommandSetService()

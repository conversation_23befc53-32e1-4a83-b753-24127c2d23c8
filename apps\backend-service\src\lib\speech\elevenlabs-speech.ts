/**
 * ElevenLabs Speech-to-Text 服务
 * 文档: https://elevenlabs.io/docs/api-reference/speech-to-text/convert
 *
 * 基于成功的 TTS 服务模式实现
 */

export interface ElevenLabsSegment {
  text: string;
  start_time?: number;
  end_time?: number;
}

export interface ElevenLabsSpeechResponse {
  text: string;
  alignment?: {
    characters: string[];
    character_start_times_seconds: number[];
    character_end_times_seconds: number[];
  };
}

export interface ElevenLabsError {
  detail: {
    status: string;
    message: string;
  };
}

export interface SpeechToTextOptions {
  model_id?: string; // 模型ID，如 'scribe_v1'
  language_code?: string; // 语言代码，如 'zh', 'en'
}

/**
 * ElevenLabs Speech-to-Text 客户端
 */
export class ElevenLabsSpeechClient {
  private apiKey: string;
  private baseUrl = 'https://api.elevenlabs.io';

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    console.log('🎙️ ElevenLabs Speech 客户端初始化:', {
      baseUrl: this.baseUrl,
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey ? apiKey.length : 0,
      apiKeyPrefix: apiKey ? apiKey.substring(0, 8) + '...' : 'none',
    });
  }

  /**
   * 检查服务可用性
   */
  async isServiceAvailable(): Promise<boolean> {
    try {
      console.log('🎙️ 检查 ElevenLabs Speech 服务可用性...');

      const response = await fetch(`${this.baseUrl}/v1/speech-to-text`, {
        method: 'GET',
        headers: {
          'Xi-Api-Key': this.apiKey,
        },
        signal: AbortSignal.timeout(5000), // 5秒超时
      });

      const available = response.status < 500;

      console.log('🎙️ 服务可用性检查结果:', {
        available,
        status: response.status,
        statusText: response.statusText,
      });

      return available;
    } catch (error) {
      console.log('🎙️ 服务不可用:', error instanceof Error ? error.message : error);
      return false;
    }
  }

  /**
   * 语音转文本 - 使用 multipart/form-data 格式
   */
  async speechToText(
    audioFile: File,
    options: SpeechToTextOptions = {}
  ): Promise<ElevenLabsSpeechResponse> {
    try {
      console.log('🎙️ 开始 ElevenLabs 语音识别:', {
        fileName: audioFile.name,
        fileType: audioFile.type,
        fileSize: `${Math.round((audioFile.size / 1024) * 100) / 100}KB`,
        options,
      });

      // 检查服务可用性
      const isAvailable = await this.isServiceAvailable();
      if (!isAvailable) {
        throw new Error('ElevenLabs Speech 服务当前不可用');
      }

      // 创建 FormData - 按照官方示例格式
      const formData = new FormData();
      formData.append('file', audioFile);

      // 按照官方示例，使用 JSON.stringify 包装参数
      if (options.model_id) {
        formData.append('model_id', options.model_id);
      }

      if (options.language_code) {
        formData.append('language_code', options.language_code);
      }

      console.log('🎙️ 开始发送请求到 ElevenLabs Speech API...');

      // 使用与 TTS 服务完全相同的请求方式
      const response = await fetch(`${this.baseUrl}/v1/speech-to-text`, {
        method: 'POST',
        headers: {
          'Xi-Api-Key': this.apiKey,
          // 重要：不设置 Content-Type，让浏览器自动处理 multipart/form-data 边界
        },
        body: formData,
        // 使用 AbortSignal.timeout，与 TTS 服务一致
        signal: AbortSignal.timeout(60000), // 60秒超时
      });

      console.log('🎙️ 收到 ElevenLabs 响应:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('🎙️ ElevenLabs API 错误:', {
          status: response.status,
          statusText: response.statusText,
          errorText,
        });

        // 尝试解析错误信息
        let errorMessage = `ElevenLabs API 错误: ${response.status}`;
        try {
          const errorData = JSON.parse(errorText) as ElevenLabsError;
          if (errorData.detail?.message) {
            errorMessage = errorData.detail.message;
          }
        } catch {
          // 如果无法解析错误，使用原始错误文本
          errorMessage = errorText || errorMessage;
        }

        throw new Error(errorMessage);
      }

      const result: ElevenLabsSpeechResponse = await response.json();

      console.log('🎙️ ElevenLabs 识别成功:', {
        textLength: result.text.length,
        hasAlignment: !!result.alignment,
        preview: result.text.substring(0, 100) + (result.text.length > 100 ? '...' : ''),
      });

      return result;
    } catch (error) {
      console.error('🎙️ ElevenLabs 语音识别失败:', error);

      // 提供更详细的错误信息
      if (error instanceof Error) {
        if (error.name === 'TimeoutError') {
          throw new Error('ElevenLabs API 请求超时，请稍后重试');
        }
        if (error.message.includes('AbortError')) {
          throw new Error('ElevenLabs API 请求被中断');
        }
      }

      throw error;
    }
  }

  /**
   * 带重试机制的语音转文本
   */
  async speechToTextWithRetry(
    audioFile: File,
    options: SpeechToTextOptions = {},
    maxRetries = 3
  ): Promise<ElevenLabsSpeechResponse> {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🎙️ ElevenLabs 调用尝试 ${attempt}/${maxRetries}`);
        const result = await this.speechToText(audioFile, options);
        return result;
      } catch (error: any) {
        const isRateLimited =
          error?.message?.includes('429') || error?.message?.includes('rate limit');
        const isServerError =
          error?.message?.includes('500') ||
          error?.message?.includes('502') ||
          error?.message?.includes('503');

        if ((isRateLimited || isServerError) && attempt < maxRetries) {
          // 指数退避：1秒、2秒、4秒
          const delayMs = Math.pow(2, attempt - 1) * 1000;
          console.log(`🎙️ 服务错误，${delayMs}ms 后重试...`);
          await new Promise((resolve) => setTimeout(resolve, delayMs));
          continue;
        }

        // 最后一次尝试失败或非重试错误，直接抛出
        throw error;
      }
    }

    throw new Error('ElevenLabs 语音识别重试次数已用完');
  }
}

/**
 * 创建 ElevenLabs Speech 客户端实例
 */
export function createElevenLabsSpeechClient(apiKey: string): ElevenLabsSpeechClient {
  return new ElevenLabsSpeechClient(apiKey);
}

/**
 * 处理 ElevenLabs 响应，提取纯文本
 */
export function extractTextFromResponse(response: ElevenLabsSpeechResponse): string {
  return response.text.trim();
}

/**
 * 格式化时间轴信息为分段
 */
export function formatAlignment(response: ElevenLabsSpeechResponse): ElevenLabsSegment[] {
  if (!response.alignment) {
    return [
      {
        text: response.text,
      },
    ];
  }

  const { characters, character_end_times_seconds } = response.alignment;

  // 将字符级时间轴转换为词级分段
  const segments: ElevenLabsSegment[] = [];
  let currentSegment = '';
  let segmentStart = 0;

  for (let i = 0; i < characters.length; i++) {
    const char = characters[i];
    currentSegment += char;

    // 如果遇到空格或标点，创建一个分段
    if (char === ' ' || char.match(/[.!?。！？]/)) {
      if (currentSegment.trim()) {
        segments.push({
          text: currentSegment.trim(),
          start_time: segmentStart,
          end_time: character_end_times_seconds[i],
        });
      }
      currentSegment = '';
      segmentStart = character_end_times_seconds[i] || 0;
    }
  }

  // 添加最后一个分段
  if (currentSegment.trim()) {
    segments.push({
      text: currentSegment.trim(),
      start_time: segmentStart,
      end_time: character_end_times_seconds[character_end_times_seconds.length - 1],
    });
  }

  return segments;
}

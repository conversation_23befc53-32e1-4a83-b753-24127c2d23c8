╭──────────────────────────────────────────────────────────────────────╮
│                              CLIENT (React App)                      │
│                                                                      │
│ ① 用户发起生成请求                                                     │
│ ───────────────────────────────┐                                     │
│                                │                                     │
│ ② POST /api/gen-image          ▼                                     │
│                         ┌────────────────────────┐                   │
│                         │ Cloudflare Worker/API   │                  │
│                         └────────────────────────┘                   │
│                                │                                     │
│                                ▼                                     │
│ ③ 请求第三方服务（TTS/生图API）                                         │
│    → 返回 task_id                                                     │
│                                │                                     │
│ ④ 插入 Supabase `jobs` 表 status='pending'                            │
│                                │                                     │
│ ⑤ 推送任务至 Cloudflare Queue                                         │
│                                ▼                                     │
│                         ┌────────────────────────┐                   │
│                         │ Cloudflare Queue Worker│                   │
│                         └────────────────────────┘                   │
│                                │                                     │
│ ⑥ 后台轮询第三方API（如 status 查询）                                    │
│    → 成功后下载图/音频文件                                               │
│    → 存储到 R2 / Supabase Storage                                     │
│    → 更新 Supabase `jobs` 表 status='done', file_url=xxx              │
│                                                                      │
│                                ▲                                     │
│                                │                                     │
│ ⑦ 前端轮询 /api/task-status?task_id=xxx                               │
│    → 读取 Supabase 表                                                 │
│    → 返回最新状态 / 文件链接                                            │
│                                                                      │
│ ⑧ 前端获取状态 → 渲染图像/音频                                          │
╰──────────────────────────────────────────────────────────────────────╯
import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { scriptService, type Script } from '@/api/services/scripts'

// 剧本缓存数据结构
interface ScriptCacheData {
  scripts: Script[]
  lastUpdated: number
  isLoading: boolean
  error: string | null
}

// Store 状态接口
interface ScriptStoreState {
  cache: ScriptCacheData
}

// Store 方法接口
interface ScriptStoreActions {
  // 获取剧本列表（优先从缓存获取）
  getScripts: () => Promise<Script[]>

  // 强制刷新剧本列表
  refreshScripts: () => Promise<Script[]>

  // 检查缓存是否有效
  isCacheValid: () => boolean

  // 清除缓存
  clearCache: () => void

  // 获取加载状态
  getLoadingState: () => {
    isLoading: boolean
    error: string | null
  }
}

// 缓存配置
const CACHE_CONFIG = {
  // 缓存过期时间（24小时）
  maxAge: 24 * 60 * 60 * 1000,
  // 存储键名
  storageKey: 'script-cache-storage'
}

// 检查缓存是否过期
const isCacheExpired = (lastUpdated: number): boolean => {
  return Date.now() - lastUpdated > CACHE_CONFIG.maxAge
}

export const useScriptStore = create<ScriptStoreState & ScriptStoreActions>()(
  persist(
    (set, get) => ({
      // 初始状态
      cache: {
        scripts: [],
        lastUpdated: 0,
        isLoading: false,
        error: null
      },

      // 获取剧本列表（优先从缓存获取）
      getScripts: async () => {
        const { cache } = get()

        // 如果缓存有效且不为空，直接返回缓存数据
        if (!isCacheExpired(cache.lastUpdated) && cache.scripts.length > 0) {
          return cache.scripts
        }

        // 缓存无效或为空，从服务器获取
        return get().refreshScripts()
      },

      // 强制刷新剧本列表
      refreshScripts: async () => {
        // 设置加载状态
        set(state => ({
          cache: {
            ...state.cache,
            isLoading: true,
            error: null
          }
        }))

        try {
          // 从后端API获取剧本列表
          const response = await scriptService.getPublicScripts({
            limit: 20,
            sortBy: 'latest'
          })

          if (response.success) {
            const scripts = response.data

            // 更新缓存
            set(state => ({
              cache: {
                scripts,
                lastUpdated: Date.now(),
                isLoading: false,
                error: null
              }
            }))

            return scripts
          } else {
            throw new Error('获取剧本列表失败')
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '网络错误，请稍后重试'

          // 设置错误状态
          set(state => ({
            cache: {
              ...state.cache,
              isLoading: false,
              error: errorMessage
            }
          }))

          // 如果有缓存数据，即使过期也返回，否则抛出错误
          const { cache } = get()
          if (cache.scripts.length > 0) {
            return cache.scripts
          } else {
            throw error
          }
        }
      },

      // 检查缓存是否有效
      isCacheValid: () => {
        const { cache } = get()
        return !isCacheExpired(cache.lastUpdated) && cache.scripts.length > 0
      },

      // 清除缓存
      clearCache: () => {
        set({
          cache: {
            scripts: [],
            lastUpdated: 0,
            isLoading: false,
            error: null
          }
        })
      },

      // 获取加载状态
      getLoadingState: () => {
        const { cache } = get()
        return {
          isLoading: cache.isLoading,
          error: cache.error
        }
      }
    }),
    {
      name: CACHE_CONFIG.storageKey,
      storage: createJSONStorage(() => localStorage),
      // 只持久化缓存数据，不持久化加载状态
      partialize: state => ({
        cache: {
          scripts: state.cache.scripts,
          lastUpdated: state.cache.lastUpdated,
          isLoading: false, // 重置加载状态
          error: null // 重置错误状态
        }
      })
    }
  )
)

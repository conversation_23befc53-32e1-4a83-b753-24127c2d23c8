{"page": {"title": "会员中心", "login_required": "请先登录", "loading": "加载中...", "error": "加载失败", "retry": "重试"}, "status": {"current_plan": "当前会员套餐", "regular_user": "普通用户", "available_points": "可用积分", "days_remaining": "剩余 {{days}} 天"}, "plan": {"select": "选择会员套餐", "name": "{{name}}会员", "per_month": "/1个月", "points": "积分", "privileges": "会员特权", "current": "当前套餐", "upgrade": "升级套餐", "downgrade_not_supported": "不支持降级"}, "features": {"maxCharacters": "{{count}}个角色", "unlimitedChat": "无限文本对话", "imageGeneration": "图片生成", "voiceGeneration": "语音生成", "proFeatures": "pro所有功能", "photoAlbum": "写真集生成", "prioritySupport": "优先支持", "canCreatePublicCharacters": "可创建公开角色", "canUseCustomVoices": "可使用自定义声音", "canAccessPremiumTemplates": "可使用付费模板"}, "payment": {"duration": "有效期", "points_included": "包含积分", "original_price": "原价", "discount": "升级优惠", "final_price": "实付金额", "days": "30天", "points_value": "{{points}}积分", "price_value": "¥{{price}}元", "discount_value": "-¥{{discount}}", "final_price_value": "¥{{price}}", "cancel": "取消", "confirm_upgrade": "确认升级"}, "dev": {"title": "开发测试", "alipay_test": "支付宝集成测试", "test_payment": "测试支付"}}
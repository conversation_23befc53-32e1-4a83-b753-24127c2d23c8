/* Android 10 兼容性测试样式 */

/* 全局 test-button 样式 - 不依赖作用域 */
.test-button {
  display: inline-block !important;
  padding: 0.75rem 1.5rem !important;
  margin: 0.5rem !important;
  background-color: #e91e63 !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 0.375rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  font-size: 0.875rem !important;
  font-weight: 500 !important;
  text-decoration: none !important;
  outline: none !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.test-button:hover {
  background-color: #c2185b !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.15) !important;
}

.test-button:active {
  transform: translateY(0) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.test-button:focus {
  outline: 2px solid #e91e63 !important;
  outline-offset: 2px !important;
}

/* 测试基础 Tailwind 工具类是否工作 */
.android-10-test {
  /* 基础布局 */
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  padding: 1rem;

  /* 背景和颜色 */
  background-color: var(--background, #fdfbfc);
  color: var(--foreground, #1a1a1a);

  /* 测试区域 */
  .test-section {
    margin-bottom: 2rem;
    padding: 1rem;
    border: 1px solid var(--border, #e5e7eb);
    border-radius: 0.5rem;
    background-color: var(--card, #ffffff);
  }

  /* 测试按钮 - 强制应用样式 */
  .test-button {
    display: inline-block !important;
    padding: 0.75rem 1.5rem !important;
    margin: 0.5rem !important;
    background-color: var(--primary, #e91e63) !important;
    color: var(--primary-foreground, #ffffff) !important;
    border: none !important;
    border-radius: 0.375rem !important;
    cursor: pointer !important;
    transition: background-color 0.2s ease !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    outline: none !important;
  }

  .test-button:hover {
    background-color: var(--primary, #e91e63) !important;
    opacity: 0.9 !important;
  }

  .test-button:focus {
    outline: 2px solid var(--primary, #e91e63) !important;
    outline-offset: 2px !important;
  }

  /* 测试网格 */
  .test-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }

  /* 如果不支持 grid，使用 flexbox fallback */
  @supports not (display: grid) {
    .test-grid {
      display: flex;
      flex-wrap: wrap;
    }

    .test-grid > * {
      flex: 1 1 200px;
      margin: 0.5rem;
    }
  }

  /* 测试卡片 - 强制应用样式 */
  .test-card {
    padding: 1rem !important;
    background-color: var(--card, #ffffff) !important;
    border: 1px solid var(--border, #e5e7eb) !important;
    border-radius: 0.5rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    margin-bottom: 1rem !important;
    min-height: 120px !important;
  }

  /* 测试颜色变量 */
  .color-test {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
  }

  .color-swatch {
    width: 3rem;
    height: 3rem;
    border-radius: 0.25rem;
    border: 1px solid var(--border, #e5e7eb);
  }

  .color-primary {
    background-color: var(--primary, #e91e63);
  }
  .color-secondary {
    background-color: var(--secondary, #9c27b0);
  }
  .color-gray-100 {
    background-color: var(--color-gray-100, #f3f4f6);
  }
  .color-gray-500 {
    background-color: var(--color-gray-500, #6b7280);
  }
  .color-gray-900 {
    background-color: var(--color-gray-900, #111827);
  }

  /* 测试动画 */
  .test-animation {
    width: 3rem;
    height: 3rem;
    background-color: var(--primary, #e91e63);
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }

  /* 测试毛玻璃效果 - 强制渐变背景 */
  .test-backdrop {
    position: relative;
    padding: 2rem !important;
    background: linear-gradient(45deg, #e91e63, #9c27b0) !important;
    background: -webkit-linear-gradient(45deg, #e91e63, #9c27b0) !important;
    background: -moz-linear-gradient(45deg, #e91e63, #9c27b0) !important;
    border-radius: 0.5rem !important;
    margin-top: 1rem !important;
    min-height: 100px !important;
  }

  .test-backdrop::before {
    content: '';
    position: absolute;
    top: 1rem;
    left: 1rem;
    right: 1rem;
    bottom: 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 0.25rem;
  }

  /* Android 10 fallback */
  @supports not (backdrop-filter: blur(10px)) {
    .test-backdrop::before {
      background-color: rgba(255, 255, 255, 0.8);
    }
  }

  .test-backdrop-content {
    position: relative;
    z-index: 1;
    color: white;
    text-align: center;
  }
}

/* 兼容性状态指示器 */
.compatibility-status {
  position: fixed;
  top: 1rem;
  right: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  z-index: 1000;
}

.compatibility-good {
  background-color: #10b981;
  color: white;
}

.compatibility-warning {
  background-color: #f59e0b;
  color: white;
}

.compatibility-error {
  background-color: #ef4444;
  color: white;
}

/* 测试信息显示 */
.test-info {
  margin-top: 2rem;
  padding: 1rem;
  background-color: var(--muted, #f5f5f5);
  border-radius: 0.5rem;
  font-family: monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.test-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--foreground, #1a1a1a);
}

.test-info ul {
  margin: 0;
  padding-left: 1.5rem;
}

.test-info li {
  margin-bottom: 0.25rem;
}

.support-yes {
  color: #10b981;
}

.support-no {
  color: #ef4444;
}

.support-partial {
  color: #f59e0b;
}

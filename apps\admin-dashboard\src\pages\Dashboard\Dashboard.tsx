import React, { useEffect, useState } from 'react'
import { Row, Col, Card, Statistic, Typography, Space, Spin } from 'antd'
import { 
  UserOutlined, 
  ShoppingCartOutlined, 
  CrownOutlined,
  ArrowUpOutlined
} from '@ant-design/icons'

const { Title } = Typography

interface DashboardStats {
  totalUsers: number
  activeUsers: number
  newUsersToday: number
  verifiedUsers: number
  totalOrders: number
  todayOrders: number
  totalRevenue: number
  monthlyRevenue: number
}

const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      // 这里应该调用获取仪表板数据的API
      // 暂时使用模拟数据
      setTimeout(() => {
        setStats({
          totalUsers: 12580,
          activeUsers: 8420,
          newUsersToday: 156,
          verifiedUsers: 10240,
          totalOrders: 5680,
          todayOrders: 89,
          totalRevenue: 2856000,
          monthlyRevenue: 186500
        })
        setLoading(false)
      }, 1000)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        数据概览
      </Title>

      <Row gutter={[16, 16]}>
        {/* 用户相关统计 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats?.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
              suffix={<ArrowUpOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              <span style={{ color: '#999', fontSize: '12px' }}>
                今日新增: {stats?.newUsersToday}
              </span>
            </div>
          </Card>
        </Col>

        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="活跃用户"
              value={stats?.activeUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <span style={{ color: '#999', fontSize: '12px' }}>
                已验证: {stats?.verifiedUsers}
              </span>
            </div>
          </Card>
        </Col>

        {/* 订单相关统计 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={stats?.totalOrders}
              prefix={<ShoppingCartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <span style={{ color: '#999', fontSize: '12px' }}>
                今日订单: {stats?.todayOrders}
              </span>
            </div>
          </Card>
        </Col>

        {/* 收入相关统计 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总收入"
              value={stats?.totalRevenue}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              suffix={<ArrowUpOutlined />}
            />
            <div style={{ marginTop: 8 }}>
              <span style={{ color: '#999', fontSize: '12px' }}>
                本月收入: ¥{stats?.monthlyRevenue?.toLocaleString()}
              </span>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        {/* 快速操作区域 */}
        <Col xs={24} lg={12}>
          <Card title="快速操作" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Card.Grid style={{ width: '100%', textAlign: 'center', padding: '12px' }}>
                <UserOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                <div style={{ marginTop: 8 }}>用户管理</div>
              </Card.Grid>
              <Card.Grid style={{ width: '100%', textAlign: 'center', padding: '12px' }}>
                <ShoppingCartOutlined style={{ fontSize: '24px', color: '#722ed1' }} />
                <div style={{ marginTop: 8 }}>订单管理</div>
              </Card.Grid>
              <Card.Grid style={{ width: '100%', textAlign: 'center', padding: '12px' }}>
                <CrownOutlined style={{ fontSize: '24px', color: '#faad14' }} />
                <div style={{ marginTop: 8 }}>会员管理</div>
              </Card.Grid>
            </Space>
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={12}>
          <Card title="系统状态" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>服务器状态</span>
                <span style={{ color: '#52c41a' }}>● 正常</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>数据库连接</span>
                <span style={{ color: '#52c41a' }}>● 正常</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>AI服务</span>
                <span style={{ color: '#52c41a' }}>● 正常</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span>支付服务</span>
                <span style={{ color: '#52c41a' }}>● 正常</span>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
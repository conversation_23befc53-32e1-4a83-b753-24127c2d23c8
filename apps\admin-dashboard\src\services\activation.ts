import { apiService } from './api'
import type { ApiResponse, PaginatedResponse, MembershipPlan, PointsPackage } from '@/types/api'

export interface ActivationCode {
  id: string
  code: string
  type: 'membership' | 'points'
  description?: string
  membershipPlanId?: string
  pointsPackageId?: string
  membershipPlan?: MembershipPlan
  pointsPackage?: PointsPackage
  batchId?: string
  isUsed: boolean
  isActive: boolean
  usedBy?: string
  usedAt?: string
  expiresAt?: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface ActivationCodeParams {
  type: 'membership' | 'points'
  membershipPlanId?: string
  pointsPackageId?: string
  description?: string
  expiresAt?: string
  count?: number
}

export interface ActivationCodeListParams {
  page?: number
  pageSize?: number
  type?: 'membership' | 'points'
  isUsed?: boolean
  isActive?: boolean
  batchId?: string
  keyword?: string
}

export interface ActivationCodeStats {
  totalCodes: number
  membershipCodes: number
  pointsCodes: number
  usedCodes: number
  activeCodes: number
  todayUsed: number
  monthlyUsed: number
}

// 激活码管理服务
export class ActivationService {
  // ==================== 激活码管理 ====================

  // 获取激活码列表
  async getActivationCodes(params?: ActivationCodeListParams): Promise<ApiResponse<PaginatedResponse<ActivationCode>>> {
    return await apiService.get<PaginatedResponse<ActivationCode>>('/admin/marketing/activation-codes', { params })
  }

  // 创建会员激活码
  async createMembershipCode(params: {
    membershipPlanId: string
    description?: string
    expiresAt?: string
    count?: number
  }): Promise<ApiResponse<ActivationCode[]>> {
    return await apiService.post<ActivationCode[]>('/admin/marketing/activation-codes/batch', {
      type: 'membership',
      ...params
    })
  }

  // 创建积分激活码
  async createPointsCode(params: {
    pointsPackageId: string
    description?: string
    expiresAt?: string
    count?: number
  }): Promise<ApiResponse<ActivationCode[]>> {
    return await apiService.post<ActivationCode[]>('/admin/marketing/activation-codes/batch', {
      type: 'points',
      ...params
    })
  }

  // 禁用激活码
  async disableCode(id: string): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/marketing/activation-codes/${id}/disable`)
  }

  // 启用激活码
  async enableCode(id: string): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/marketing/activation-codes/${id}/enable`)
  }

  // 获取激活码统计
  async getStats(params?: {
    type?: 'membership' | 'points'
    batchId?: string
    dateFrom?: string
    dateTo?: string
  }): Promise<ApiResponse<ActivationCodeStats>> {
    return await apiService.get<ActivationCodeStats>('/admin/marketing/activation-codes/stats', { params })
  }

  // 获取会员套餐列表
  async getMembershipPlans(): Promise<ApiResponse<MembershipPlan[]>> {
    return await apiService.get<MembershipPlan[]>('/admin/membership/plans')
  }

  // 获取积分包列表
  async getPointsPackages(): Promise<ApiResponse<PointsPackage[]>> {
    return await apiService.get<PointsPackage[]>('/admin/membership/points-packages')
  }
}

export const activationService = new ActivationService()
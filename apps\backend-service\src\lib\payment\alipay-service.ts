/**
 * 支付宝支付服务
 */

import { getPaymentConfig, type PaymentConfig } from './payment-config';
import type { PaymentResult, PaymentVerification } from './mock-payment';

export interface AlipayOrderRequest {
  outTradeNo: string; // 商户订单号
  totalAmount: string; // 订单总金额(元)
  subject: string; // 订单标题
  body?: string; // 订单描述
  returnUrl?: string; // 同步回调地址
  notifyUrl: string; // 异步回调地址
}

export interface AlipayOrderResponse {
  code: string;
  msg: string;
  outTradeNo: string;
  tradeNo?: string;
  totalAmount: string;
  payUrl?: string;
}

export interface AlipayNotifyData {
  gmt_create: string;
  charset: string;
  gmt_payment: string;
  notify_time: string;
  subject: string;
  sign: string;
  buyer_id: string;
  body?: string;
  invoice_amount: string;
  version: string;
  notify_id: string;
  fund_bill_list: string;
  notify_type: string;
  out_trade_no: string;
  total_amount: string;
  trade_status: string;
  trade_no: string;
  auth_app_id: string;
  receipt_amount: string;
  point_amount: string;
  app_id: string;
  buyer_pay_amount: string;
  sign_type: string;
  seller_id: string;
}

/**
 * 支付宝支付服务类
 */
export class AlipayService {
  private config: PaymentConfig;

  constructor(env: any) {
    this.config = getPaymentConfig(env);
  }

  /**
   * 创建支付宝订单
   */
  async createOrder(orderRequest: AlipayOrderRequest): Promise<PaymentResult> {
    try {
      console.log('🏦 [ALIPAY] 创建支付宝订单:', orderRequest);

      // 验证必需参数
      if (!orderRequest.outTradeNo || !orderRequest.totalAmount || !orderRequest.subject) {
        throw new Error('缺少必需参数: outTradeNo, totalAmount, subject');
      }

      // 验证金额格式
      const amount = Number.parseFloat(orderRequest.totalAmount);
      if (isNaN(amount) || amount <= 0) {
        throw new Error(`无效的金额: ${orderRequest.totalAmount}`);
      }

      // 确保金额格式正确（精确到小数点后两位）
      const formattedAmount = Number.parseFloat(orderRequest.totalAmount).toFixed(2);

      // 构建支付宝请求参数
      const bizContent: Record<string, any> = {
        out_trade_no: orderRequest.outTradeNo,
        total_amount: formattedAmount,
        subject: orderRequest.subject,
        body: orderRequest.body || orderRequest.subject,
        product_code: 'QUICK_WAP_WAY', // 手机网站支付，匹配 alipay.trade.wap.pay 接口
      };

      // 不使用 timeout_express，避免参数冲突

      const params: Record<string, any> = {
        app_id: this.config.alipay.appId,
        method: 'alipay.trade.wap.pay', // 手机网站支付接口
        charset: this.config.alipay.charset,
        sign_type: this.config.alipay.signType,
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        version: this.config.alipay.version,
        notify_url: orderRequest.notifyUrl,
        return_url: orderRequest.returnUrl,
        biz_content: JSON.stringify(bizContent),
      };

      // 先记录参数用于调试
      console.log('🔍 [ALIPAY] 支付参数:', {
        app_id: params.app_id,
        method: params.method,
        charset: params.charset,
        sign_type: params.sign_type,
        timestamp: params.timestamp,
        version: params.version,
        notify_url: params.notify_url,
        return_url: params.return_url,
        gateway: this.config.alipay.gateway,
      });

      console.log('🔍 [ALIPAY] 业务参数 (biz_content):', JSON.stringify(bizContent, null, 2));

      // 生成签名
      const sign = await this.generateSignAsync(params);
      if (!sign) {
        throw new Error('签名生成失败');
      }

      // 添加签名到参数中
      const paramsWithSign = { ...params, sign };

      // 构建支付URL
      const payUrl = `${this.config.alipay.gateway}?${this.buildQuery(paramsWithSign)}`;

      console.log('✅ [ALIPAY] 支付宝订单创建成功:', {
        outTradeNo: orderRequest.outTradeNo,
        payUrl: payUrl.substring(0, 100) + '...',
      });

      return {
        success: true,
        orderId: orderRequest.outTradeNo,
        paymentId: orderRequest.outTradeNo,
        redirectUrl: payUrl,
      };
    } catch (error) {
      console.error('❌ [ALIPAY] 创建支付宝订单失败:', error);
      return {
        success: false,
        orderId: orderRequest.outTradeNo,
        error: '创建支付宝订单失败',
      };
    }
  }

  /**
   * 查询支付宝订单状态
   */
  async queryOrder(outTradeNo: string): Promise<PaymentVerification> {
    try {
      console.log('🔍 [ALIPAY] 查询支付宝订单状态:', outTradeNo);

      const bizContent = {
        out_trade_no: outTradeNo,
      };

      const params: Record<string, any> = {
        app_id: this.config.alipay.appId,
        method: 'alipay.trade.query',
        charset: this.config.alipay.charset,
        sign_type: this.config.alipay.signType,
        timestamp: new Date().toISOString().replace('T', ' ').substring(0, 19),
        version: this.config.alipay.version,
        biz_content: JSON.stringify(bizContent),
      };

      console.log('🔍 [ALIPAY] 查询请求参数:', {
        app_id: params.app_id,
        method: params.method,
        timestamp: params.timestamp,
        out_trade_no: outTradeNo,
      });

      // 生成签名
      const sign = await this.generateSignAsync(params);
      params.sign = sign;

      // 发送查询请求
      const response = await this.sendRequest(params);

      console.log('🔍 [ALIPAY] 查询响应原始数据:', JSON.stringify(response, null, 2));

      // 支付宝查询接口的响应结构是: { alipay_trade_query_response: {...}, sign: "..." }
      const queryResponse = response.alipay_trade_query_response || response;

      console.log('🔍 [ALIPAY] 解析后的查询响应:', JSON.stringify(queryResponse, null, 2));

      if (queryResponse.code === '10000') {
        const tradeStatus = queryResponse.trade_status;
        console.log('🔍 [ALIPAY] 交易状态:', tradeStatus);

        let status: 'pending' | 'completed' | 'failed' | 'cancelled' = 'pending';

        switch (tradeStatus) {
          case 'TRADE_SUCCESS':
          case 'TRADE_FINISHED':
            status = 'completed';
            break;
          case 'TRADE_CLOSED':
            status = 'cancelled';
            break;
          case 'WAIT_BUYER_PAY':
            status = 'pending';
            break;
          default:
            status = 'failed';
            console.log('⚠️ [ALIPAY] 未知交易状态:', tradeStatus);
        }

        return {
          success: status === 'completed',
          orderId: outTradeNo,
          paymentId: queryResponse.trade_no || outTradeNo,
          status,
          amount: Number.parseFloat(queryResponse.total_amount || '0'),
          paidAt: status === 'completed' ? new Date() : undefined,
        };
      } else {
        console.error('❌ [ALIPAY] 查询失败:', {
          code: queryResponse.code,
          msg: queryResponse.msg,
          sub_code: queryResponse.sub_code,
          sub_msg: queryResponse.sub_msg,
        });

        return {
          success: false,
          orderId: outTradeNo,
          paymentId: outTradeNo,
          status: 'failed',
          amount: 0,
        };
      }
    } catch (error) {
      console.error('❌ [ALIPAY] 查询支付宝订单失败:', error);
      return {
        success: false,
        orderId: outTradeNo,
        paymentId: outTradeNo,
        status: 'failed',
        amount: 0,
      };
    }
  }

  /**
   * 验证支付宝回调签名
   */
  verifyNotify(notifyData: AlipayNotifyData): boolean {
    try {
      console.log('🔐 [ALIPAY] 验证回调签名');

      // 从通知数据中提取签名
      const sign = notifyData.sign;

      // 移除sign和sign_type参数
      const params: Record<string, any> = { ...notifyData };
      delete (params as any).sign;
      delete (params as any).sign_type;

      // 生成签名用于验证
      const expectedSign = this.generateSign(params);

      const isValid = sign === expectedSign;
      console.log(isValid ? '✅ [ALIPAY] 签名验证成功' : '❌ [ALIPAY] 签名验证失败');

      return isValid;
    } catch (error) {
      console.error('❌ [ALIPAY] 签名验证异常:', error);
      return false;
    }
  }

  /**
   * 处理支付宝异步回调
   */
  processNotify(notifyData: AlipayNotifyData): {
    success: boolean;
    message: string;
    orderData?: any;
  } {
    try {
      console.log('📨 [ALIPAY] 处理支付宝回调:', {
        outTradeNo: notifyData.out_trade_no,
        tradeStatus: notifyData.trade_status,
        totalAmount: notifyData.total_amount,
      });

      // 验证签名
      if (!this.verifyNotify(notifyData)) {
        return { success: false, message: '签名验证失败' };
      }

      // 检查交易状态
      const tradeStatus = notifyData.trade_status;
      if (tradeStatus === 'TRADE_SUCCESS' || tradeStatus === 'TRADE_FINISHED') {
        return {
          success: true,
          message: '支付成功',
          orderData: {
            outTradeNo: notifyData.out_trade_no,
            tradeNo: notifyData.trade_no,
            totalAmount: notifyData.total_amount,
            buyerId: notifyData.buyer_id,
            tradeStatus: notifyData.trade_status,
            gmtPayment: notifyData.gmt_payment,
          },
        };
      } else {
        return {
          success: false,
          message: `交易状态不正确: ${tradeStatus}`,
        };
      }
    } catch (error) {
      console.error('❌ [ALIPAY] 处理回调失败:', error);
      return { success: false, message: '处理回调失败' };
    }
  }

  /**
   * 生成签名（异步版本）
   */
  private async generateSignAsync(params: Record<string, any>): Promise<string> {
    try {
      // 过滤并排序参数
      const filteredParams: Record<string, any> = {};

      for (const [key, value] of Object.entries(params)) {
        // 过滤掉签名参数但保留 sign_type，过滤空值
        if (key !== 'sign' && value !== '' && value !== null && value !== undefined) {
          filteredParams[key] = value;
        }
      }

      // 按字母顺序排序并构建签名字符串
      const sortedKeys = Object.keys(filteredParams).sort();
      const signString = sortedKeys.map((key) => `${key}=${filteredParams[key]}`).join('&');

      console.log('🔐 [ALIPAY] 待签名参数:', Object.keys(filteredParams).join(', '));
      console.log(
        '🔐 [ALIPAY] 签名字符串:',
        signString.length > 200 ? signString.substring(0, 200) + '...' : signString
      );

      // 使用异步方式生成真正的 RSA2 签名
      const signature = await this.rsaSignAsync(signString, this.config.alipay.privateKey);

      console.log('🔐 [ALIPAY] 生成的签名:', signature.substring(0, 50) + '...');

      return signature;
    } catch (error) {
      console.error('❌ [ALIPAY] 生成签名失败:', error);
      return this.createMockSign(JSON.stringify(params));
    }
  }

  /**
   * 生成签名（同步版本，向后兼容）
   */
  private generateSign(params: Record<string, any>): string {
    try {
      // 过滤并排序参数
      const filteredParams: Record<string, any> = {};

      for (const [key, value] of Object.entries(params)) {
        // 过滤掉签名参数但保留 sign_type，过滤空值
        if (key !== 'sign' && value !== '' && value !== null && value !== undefined) {
          filteredParams[key] = value;
        }
      }

      // 按字母顺序排序并构建签名字符串
      const sortedKeys = Object.keys(filteredParams).sort();
      const signString = sortedKeys.map((key) => `${key}=${filteredParams[key]}`).join('&');

      console.log('🔐 [ALIPAY] 待签名参数:', Object.keys(filteredParams).join(', '));
      console.log(
        '🔐 [ALIPAY] 签名字符串:',
        signString.length > 200 ? signString.substring(0, 200) + '...' : signString
      );

      // 使用同步方式生成签名（回调验证时使用）
      const signature = this.rsaSignSync(signString, this.config.alipay.privateKey);

      console.log('🔐 [ALIPAY] 生成的签名:', signature.substring(0, 50) + '...');

      return signature;
    } catch (error) {
      console.error('❌ [ALIPAY] 生成签名失败:', error);
      return this.createMockSign(JSON.stringify(params));
    }
  }

  /**
   * RSA2 签名（异步版本，使用 Web Crypto API）
   */
  private async rsaSignAsync(data: string, privateKey: string): Promise<string> {
    try {
      console.log('🔐 [ALIPAY] 开始 RSA2 签名');

      if (!privateKey || privateKey.includes('MOCK')) {
        console.log('⚠️ [ALIPAY] 使用模拟私钥，降级到模拟签名');
        return this.createMockSign(data);
      }

      // 清理私钥格式，处理多种格式
      const cleanPrivateKey = privateKey
        .replace(/-----BEGIN PRIVATE KEY-----/g, '')
        .replace(/-----END PRIVATE KEY-----/g, '')
        .replace(/-----BEGIN RSA PRIVATE KEY-----/g, '')
        .replace(/-----END RSA PRIVATE KEY-----/g, '')
        .replace(/\r\n/g, '')
        .replace(/\n/g, '')
        .replace(/\r/g, '')
        .replace(/\s+/g, '');

      console.log('🔐 [ALIPAY] 私钥长度:', cleanPrivateKey.length);

      // Base64 解码私钥
      const binaryKey = Uint8Array.from(atob(cleanPrivateKey), (c) => c.charCodeAt(0));

      // 导入私钥
      const cryptoKey = await crypto.subtle.importKey(
        'pkcs8',
        binaryKey,
        {
          name: 'RSASSA-PKCS1-v1_5',
          hash: 'SHA-256',
        },
        false,
        ['sign']
      );

      // 将数据转换为 Uint8Array
      const dataBuffer = new TextEncoder().encode(data);

      // 生成签名
      const signature = await crypto.subtle.sign('RSASSA-PKCS1-v1_5', cryptoKey, dataBuffer);

      // 转换为 Base64
      const signatureArray = new Uint8Array(signature);
      const signatureBase64 = btoa(String.fromCharCode(...signatureArray));

      console.log('✅ [ALIPAY] RSA2 签名生成成功');
      return signatureBase64;
    } catch (error) {
      console.error('❌ [ALIPAY] RSA2 签名失败:', error);
      console.error('❌ [ALIPAY] 私钥长度:', privateKey?.length);
      console.error(
        '❌ [ALIPAY] 错误详情:',
        error instanceof Error ? error.message : String(error)
      );
      console.log('⚠️ [ALIPAY] 降级使用模拟签名');
      return this.createMockSign(data);
    }
  }

  /**
   * RSA2 签名（同步包装器，用于兼容）
   */
  private rsaSignSync(data: string, privateKey: string): string {
    // 对于回调验证，返回模拟签名
    console.log('⚠️ [ALIPAY] 使用同步包装器，建议使用异步版本');
    return this.createMockSign(data);
  }

  /**
   * 发送HTTP请求到支付宝
   */
  private async sendRequest(params: Record<string, any>): Promise<any> {
    try {
      const queryString = this.buildQuery(params);
      const url = `${this.config.alipay.gateway}?${queryString}`;

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const responseText = await response.text();

      // 解析支付宝响应 (简化版本，实际需要根据具体API响应格式处理)
      try {
        return JSON.parse(responseText);
      } catch {
        // 如果不是JSON，返回包装的响应
        return { code: '10000', msg: 'Success', data: responseText };
      }
    } catch (error) {
      console.error('❌ [ALIPAY] 请求发送失败:', error);
      throw error;
    }
  }

  /**
   * 构建查询字符串
   */
  private buildQuery(params: Record<string, any>): string {
    return Object.keys(params)
      .sort() // 保持参数顺序一致
      .filter((key) => params[key] !== null && params[key] !== undefined && params[key] !== '')
      .map((key) => {
        // 对于 sign 参数，需要特殊处理 URL 编码
        const value = params[key];
        if (key === 'sign') {
          // sign 参数进行 URL 编码
          return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
        } else {
          // 其他参数正常编码
          return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
        }
      })
      .join('&');
  }

  /**
   * 创建模拟签名 (开发测试用)
   * 在生产环境中需要替换为真实的RSA2签名算法
   */
  private createMockSign(data: string): string {
    // 简单的哈希模拟，生产环境需要使用RSA2
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return `mock_sign_${Math.abs(hash).toString(16)}`;
  }
}

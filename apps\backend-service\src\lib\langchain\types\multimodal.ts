// 多模态相关类型定义

// 标签类型枚举
export enum TagType {
  SCENE = 'scene',
  ACTION = 'action',
  DIALOGUE = 'dialogue',
  IMAGE_PROMPT = 'imagePrompt',
  AUDIO_TAGS = 'audioTags',
}

// 解析后的标签内容
export interface ParsedTag {
  type: TagType;
  content: string;
}

// 多模态响应结构
export interface MultiModalResponse {
  // 文本内容
  text: {
    scene?: string;
    action?: string;
    dialogue?: string;
  };

  // 图片生成
  image?: {
    prompt: string;
    url?: string; // 生成后的图片URL
  };

  // 音频效果
  audio?: {
    tags: string[];
    url?: string; // 音频文件URL
  };

  // 设备控制（预留）
  device?: {
    commands: string[];
  };
}

// 模态处理器接口
export interface ModalProcessor {
  process(content: string): Promise<any>;
  getType(): TagType;
}

// 多模态协调器选项
export interface CoordinatorOptions {
  enableImage: boolean;
  enableAudio: boolean;
  enableDevice: boolean;
  processors: Map<TagType, ModalProcessor>;
}

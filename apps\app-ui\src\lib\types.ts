// 聊天消息类型
export interface Message {
  role: 'user' | 'assistant';
  content: string;
}

// 聊天历史类型
export interface ChatHistory {
  id: string;
  roleId: string;
  title: string;
  lastMessage: string;
  createdAt: string;
  updatedAt: string;
  messageCount?: number;
  visibility?: 'public' | 'private';
  userId?: string;
  lastMessageAt?: string;
}

// API用户类型
export interface ApiUser {
  id: string;
  email: string;
  name?: string;
  image?: string;
}

// 角色类型
export interface Role {
  role: string;
  character: string;
  initialScene: string;
  description: string;
  age: number;
  avatar?: string;
}

// 自定义角色类型
export interface CharacterType {
  id: string;
  name: string;
  relationship?: string;
  ethnicity?: string;
  gender?: string;
  age?: number | string;
  eyeColor?: string;
  hairStyle?: string;
  hairColor?: string;
  bodyType?: string;
  breastSize?: string;
  buttSize?: string;
  personality?: string;
  clothing?: string;
  voice?: string;
  imageUrl?: string;
  userId?: string;
  createdAt?: string;
  updatedAt?: string;
  role?: string;
  character?: string;
  description?: string;
  avatar?: string;
  custom?: boolean;
  isNew?: boolean;
}

// UI显示用角色类型
export interface DisplayRole {
  id?: string;
  role: string;
  character: string;
  description?: string;
  avatar?: string;
  age?: number | string;
  isNew?: boolean;
  custom?: boolean;
}

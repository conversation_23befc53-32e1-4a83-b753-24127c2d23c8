import { useNavigate, useLocation } from 'react-router'
import { Button, Avatar, Skeleton, Navbar, NavbarContent, NavbarItem } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useRoleStore } from '@/stores/role-store'
import { Component, type ReactNode } from 'react'
import { useDeviceSafeArea } from '@/hooks/use-mobile-viewport'
import { motion } from 'framer-motion'

// 内部组件：处理角色头部UI
function RoleHeaderContent() {
  const navigate = useNavigate()
  const location = useLocation()
  // 获取设备安全区域信息
  const safeArea = useDeviceSafeArea()

  // 使用全局角色状态
  const { currentRole, isLoading, error } = useRoleStore()

  // 🚀 智能返回逻辑
  const handleBackClick = () => {
    // 检查是否是从角色页面重置历史的新聊天
    const state = location.state as any
    if (state?.resetHistory && state?.fromRoleProfile) {
      console.log('🔄 [RoleHeader] 检测到重置历史状态，返回到发现页面')
      navigate('/discover')
      return
    }

    // 检查当前页面类型，决定合适的返回目标
    const currentPath = location.pathname
    if (currentPath.includes('/chat/')) {
      // 在聊天页面，尝试返回到聊天历史或发现页面
      if (window.history.length > 1) {
        // 有历史记录，尝试返回
        navigate(-1)
      } else {
        // 没有历史记录，返回发现页面
        navigate('/discover')
      }
    } else {
      // 其他页面，默认返回发现页面
      navigate('/discover')
    }
  }

  // 处理头像点击，跳转到角色详情页面
  const handleAvatarClick = () => {
    if (currentRole?.id) {
      // 传递当前页面信息作为来源
      const currentPath = location.pathname
      let from = 'unknown'

      if (currentPath.includes('/chat')) {
        from = 'chat'
      } else if (currentPath.includes('/interactive')) {
        from = 'interactive'
      }

      navigate(`/role-profile/${currentRole.id}`, {
        state: {
          from,
          returnPath: currentPath
        }
      })
    }
  }

  return (
    <>
      <Navbar
        className="fixed top-0 inset-x-0 z-20 bg-background/80 backdrop-blur-md border-b border-divider"
        classNames={{
          wrapper: 'px-2 md:px-4',
          content: 'gap-2'
        }}
        style={{
          paddingTop: `${safeArea.top}px`,
          height: `calc(4rem + ${safeArea.top}px)`
        }}
        maxWidth="full"
      >
        {/* 左侧：返回按钮 */}
        <NavbarContent justify="start">
          <NavbarItem>
            <Button
              isIconOnly
              variant="light"
              size="sm"
              onPress={() => navigate('/discover')}
              aria-label="返回"
            >
              <Icon icon="solar:arrow-left-linear" width={20} />
            </Button>
          </NavbarItem>
        </NavbarContent>

        {/* 中间：角色名称 */}
        <NavbarContent justify="center">
          <NavbarItem>
            {isLoading ? (
              <Skeleton className="h-4 w-20 rounded" />
            ) : currentRole ? (
              <span className="text-sm font-medium">{currentRole.name}</span>
            ) : (
              <span className="text-sm font-medium text-default-500">
                {error ? `错误: ${error}` : '未知角色'}
              </span>
            )}
          </NavbarItem>
        </NavbarContent>

        {/* 右侧：角色头像 */}
        <NavbarContent justify="end">
          <NavbarItem>
            {isLoading ? (
              <Skeleton className="w-8 h-8 rounded-full" />
            ) : currentRole ? (
              <motion.div
                layoutId={`role-avatar-${currentRole.id}`}
                whileTap={{ scale: 0.95 }}
                className="cursor-pointer"
                onClick={handleAvatarClick}
              >
                <Avatar
                  src={currentRole.imageUrl}
                  alt={currentRole.name}
                  size="sm"
                  className="w-8 h-8"
                  fallback={
                    <Icon icon="solar:user-circle-linear" className="w-5 h-5 text-default-400" />
                  }
                />
              </motion.div>
            ) : (
              <Avatar
                size="sm"
                className="w-8 h-8"
                fallback={
                  <Icon icon="solar:user-circle-linear" className="w-5 h-5 text-default-400" />
                }
              />
            )}
          </NavbarItem>
        </NavbarContent>
      </Navbar>
      <div
        style={{
          paddingTop: `${safeArea.top}px`,
          height: `calc(4rem + ${safeArea.top}px)`
        }}
      ></div>
    </>
  )
}

// 错误回退UI组件
function FallbackHeader() {
  const navigate = useNavigate()
  const location = useLocation()
  const safeArea = useDeviceSafeArea()

  // 🚀 错误回退也使用智能返回逻辑
  const handleFallbackBackClick = () => {
    const state = location.state as any
    if (state?.resetHistory && state?.fromRoleProfile) {
      console.log('🔄 [FallbackHeader] 检测到重置历史状态，返回到发现页面')
      navigate('/discover')
      return
    }

    // 默认返回上一页
    navigate(-1)
  }

  return (
    <Navbar
      className="fixed top-0 inset-x-0 z-20 bg-background/80 backdrop-blur-md border-b border-divider"
      classNames={{
        wrapper: 'px-2 md:px-4'
      }}
      style={{
        paddingTop: `${safeArea.top}px`,
        height: `calc(4rem + ${safeArea.top}px)`
      }}
      maxWidth="full"
    >
      <NavbarContent justify="start" className="gap-2">
        <NavbarItem>
          <Button
            isIconOnly
            variant="light"
            size="sm"
            onPress={handleFallbackBackClick}
            aria-label="返回"
          >
            <Icon icon="solar:arrow-left-linear" width={20} />
          </Button>
        </NavbarItem>

        <NavbarItem className="flex items-center gap-3">
          <Skeleton className="w-8 h-8 rounded-full" />
          <span className="text-sm font-medium text-default-500">角色加载中...</span>
        </NavbarItem>
      </NavbarContent>
    </Navbar>
  )
}

// React错误边界类
class ErrorBoundary extends Component<{
  children: ReactNode
  fallback: ReactNode
}> {
  state = { hasError: false }

  static getDerivedStateFromError() {
    return { hasError: true }
  }

  componentDidCatch(error: Error) {
    console.error('角色头部组件错误:', error)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

// 主导出组件：包含错误处理
export function RoleHeader() {
  return (
    <ErrorBoundary fallback={<FallbackHeader />}>
      <RoleHeaderContent />
    </ErrorBoundary>
  )
}

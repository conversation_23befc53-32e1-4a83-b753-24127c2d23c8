import { useEffect, useState, useCallback, useRef } from 'react'
import { useParams, useNavigate, useSearchParams } from 'react-router'
import { generateUUID } from '@/lib/utils'
import { ChatV2 } from '@/components/chat-v2'
import type { VisibilityType } from '@/components/chat-v2/chat'
import { apiService } from '@/api/services'
import type { Message } from '@/api/services'
import { Card, CardBody, Spinner, Button } from '@heroui/react'
import { ArrowUpIcon } from '@/components/icons'
import { useRoleStore } from '@/stores/role-store'
import { useAudioPermission } from '@/hooks/useAudioPermission'
import { useDeviceStore } from '@/stores/device-store'
import { commandQueueManager } from '../interactive/utils/bluetoothUtils'
import { useChatInitialization } from '@/hooks/use-chat-initialization'
import { getGlobalChatDatabase } from '@/lib/chat-database'
import { useTranslation } from 'react-i18next'

// 转换消息格式 - 保留作为备用
function convertToLangChainMessages(messages: Array<Message>): Array<Message> {
  return messages.map(message => {
    // 从 parts 中提取文本内容
    const content = message.parts
      .filter(part => part.type === 'text' && part.text)
      .map(part => part.text)
      .join('')

    return {
      id: message.id,
      role: message.role,
      content: content || '', // 确保有 content 字段
      parts: message.parts
        .filter(part => part.type === 'text') // 过滤掉step-start类型的部分
        .map(part => {
          if (part.type === 'text' && part.text) {
            return { type: 'text' as const, text: part.text }
          }
          // 处理其他可能的类型
          return { type: 'text' as const, text: JSON.stringify(part) }
        }),
      attachments: message.attachments,
      createdAt: new Date(message.createdAt)
    }
  })
}

export default function ChatV2Page() {
  const { t } = useTranslation('chat')
  const { chatId } = useParams<{ chatId: string }>()
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const isLoadingRef = useRef(false)
  const { currentRole, setRole } = useRoleStore()
  const { requestPermission } = useAudioPermission()
  const { connectedDevice, disconnectDevice, exitFunction } = useDeviceStore()

  // 获取有效的角色ID
  const getEffectiveRoleId = useCallback(() => {
    const urlRoleId = searchParams.get('role')
    const globalRoleId = currentRole?.id || null

    // 优先使用URL中的角色ID
    if (urlRoleId) {
      return urlRoleId
    }

    // 如果URL中没有，使用全局角色ID
    if (globalRoleId) {
      return globalRoleId
    }

    // 默认角色
    return 'ruyun'
  }, [searchParams, currentRole])

  // 🚀 使用本地优先的聊天初始化策略
  const {
    isLoading: isInitializing,
    error: initError,
    messages: initialMessages,
    chatId: resolvedChatId,
    refresh
  } = useChatInitialization({
    chatId: chatId || undefined,
    roleId: getEffectiveRoleId(),
    chatDatabase: getGlobalChatDatabase()
  })

  // 聊天状态
  const [chat, setChat] = useState<{
    id: string
    messages: Message[]
    selectedVisibilityType: VisibilityType
    isReadonly: boolean
  } | null>(null)

  // 角色同步逻辑
  useEffect(() => {
    const urlRoleId = searchParams.get('role')
    const globalRoleId = currentRole?.id || null

    // 如果URL中有角色ID，但与全局角色不一致，更新全局角色
    if (urlRoleId && globalRoleId !== null && urlRoleId !== globalRoleId) {
      console.log(`🔍 [ChatV2Page] ${t('log.update_global_role')}`, urlRoleId)
      setRole(urlRoleId).catch(error => {
        console.error(`${t('log.role_setting_failed')}`, error)
      })
      return // 避免继续执行其他逻辑
    }

    // 如果都没有，设置默认角色（只在初始化时执行一次）
    if (!urlRoleId && !globalRoleId) {
      console.log(`🔍 [ChatV2Page] ${t('log.set_default_role')}`)
      setRole('ruyun')
        .then(() => {
          const newSearchParams = new URLSearchParams(searchParams)
          newSearchParams.set('role', 'ruyun')
          setSearchParams(newSearchParams, { replace: true })
        })
        .catch(error => {
          console.error(`${t('log.default_role_setting_failed')}`, error)
        })
    }
  }, [searchParams, currentRole, setRole, setSearchParams])

  // 🚀 使用本地优先初始化的结果来设置聊天状态
  useEffect(() => {
    console.log(`🔍 [ChatV2Page] ${t('log.chat_init_status')}`, {
      isInitializing,
      hasError: !!initError,
      messagesCount: initialMessages.length,
      resolvedChatId
    })

    if (!isInitializing && !initError) {
      // 聊天初始化完成且无错误
      if (resolvedChatId) {
        setChat({
          id: resolvedChatId,
          messages: initialMessages,
          selectedVisibilityType: 'private' as VisibilityType,
          isReadonly: false
        })

        // 如果是新生成的chatId且与URL不同，更新URL
        if (chatId !== resolvedChatId) {
          const currentSearch = searchParams.toString()
          const newUrl = `/chat/${resolvedChatId}${currentSearch ? `?${currentSearch}` : ''}`
          window.history.replaceState(null, '', newUrl)
        }
      }

      setIsLoading(false)
      setError(null)
    } else if (initError) {
      // 初始化失败
      console.error(`${t('log.chat_init_failed')}`, initError)
      setError(initError)
      setIsLoading(false)
    } else {
      // 仍在初始化中
      setIsLoading(true)
      setError(null)
    }
  }, [isInitializing, initError, initialMessages, resolvedChatId, chatId, searchParams])

  // 在页面加载完成后请求音频权限
  useEffect(() => {
    if (!isLoading && chat) {
      // 延迟一点时间再请求权限，让用户先看到界面
      const timer = setTimeout(() => {
        requestPermission().catch(error => {
          console.log(`${t('log.audio_permission_failed')}`, error)
          // 这里不需要显示错误，用户点击录音时会再次提示
        })
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [isLoading, chat, requestPermission])

  // 页面退出时的设备清理 - 双重保险
  useEffect(() => {
    const handlePageExit = () => {
      if (connectedDevice) {
        console.log(`🛑 ChatV2Page - ${t('log.page_exit_disconnect')}`)

        try {
          // 发送所有功能的停止命令
          const stopCommands: string[] = []
          connectedDevice.func.forEach(func => {
            const stopCommand = func.commands.find(cmd => cmd.intensity === -1)
            if (stopCommand) {
              stopCommands.push(stopCommand.command)
            }
          })

          if (stopCommands.length > 0) {
            console.log(`ChatV2Page ${t('log.send_stop_command')}`, stopCommands)
            commandQueueManager.addCommands(stopCommands)
          }

          // 强制断开设备连接
          exitFunction('chat')
          disconnectDevice()
        } catch (error) {
          console.error(`ChatV2Page ${t('log.page_exit_disconnect_failed')}`, error)
        }
      }
    }

    // 监听页面卸载事件
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      handlePageExit()
    }

    // 监听 visibilitychange 事件（页面切换、最小化等）
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && connectedDevice) {
        console.log(`🛑 ChatV2Page - ${t('log.page_hidden_stop_command')}`)
        try {
          const stopCommands: string[] = []
          connectedDevice.func.forEach(func => {
            const stopCommand = func.commands.find(cmd => cmd.intensity === -1)
            if (stopCommand) {
              stopCommands.push(stopCommand.command)
            }
          })

          if (stopCommands.length > 0) {
            commandQueueManager.addCommands(stopCommands)
          }
        } catch (error) {
          console.error(`ChatV2Page ${t('log.page_hidden_stop_command_failed')}`, error)
        }
      }
    }

    // 添加事件监听器
    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 组件卸载时的清理
    return () => {
      // 移除事件监听器
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)

      // 执行页面退出清理逻辑
      handlePageExit()
    }
  }, [connectedDevice, disconnectDevice, exitFunction])

  // 加载状态
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-dvh">
        <Card>
          <CardBody className="flex items-center gap-3 py-8 px-12">
            <Spinner size="sm" label={t('page.loading')} />
          </CardBody>
        </Card>
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <Card>
          <CardBody className="text-center py-8 px-12 space-y-4">
            <div className="text-danger text-lg">⚠️</div>
            <div className="text-default-600">{error}</div>
            <Button
              color="primary"
              variant="flat"
              onPress={() => navigate('/chat')}
              startContent={<ArrowUpIcon size={16} />}
            >
              {t('page.button.back_to_chat_list')}
            </Button>
          </CardBody>
        </Card>
      </div>
    )
  }

  // 正常渲染聊天界面
  if (!chat) {
    return (
      <div className="flex items-center justify-center h-full">
        <Card>
          <CardBody className="text-center py-8 px-12">
            <div className="text-default-600">{t('page.error.data_unavailable')}</div>
          </CardBody>
        </Card>
      </div>
    )
  }

  return (
    <>
      <ChatV2
        id={chat.id}
        initialMessages={chat.messages || []}
        selectedVisibilityType={chat.selectedVisibilityType || 'private'}
        isReadonly={chat.isReadonly || false}
        effectiveRoleId={getEffectiveRoleId()} // 传递有效的角色ID
      />
    </>
  )
}

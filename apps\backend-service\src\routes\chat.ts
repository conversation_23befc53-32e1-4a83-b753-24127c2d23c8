import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { SupportedLanguage } from '@/i18n/config'
import type { Env } from '@/types/env'
import { ChatHandlers, createChatSchema, updateChatSchema } from '@/lib/chat/handlers'
import { updateMessageAttachments } from '@/lib/db/queries/chat'
import { z } from 'zod'
import type { Context } from 'hono'

// 获取国际化函数的辅助函数
function getI18n(
  c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>
): (key: string, params?: Record<string, string | number>) => string {
  return c.get('t')
}

// 更新消息附件的schema - 静态版本
const updateMessageAttachmentsSchema = z.object({
  messageId: z.string().min(1),
  attachments: z.array(
    z.object({
      url: z.string().url(),
      name: z.string().min(1).max(200),
      contentType: z.enum(['image/png', 'image/jpg', 'image/jpeg'])
    })
  )
})

const chat = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// ==================== 获取用户聊天列表 ====================

chat.get('/conversations', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = getI18n(c)

    if (!user) {
      return c.json({ success: false, message: t('user_not_found') }, 401)
    }

    const limit = Number.parseInt(c.req.query('limit') || '20')
    const startingAfter = c.req.query('startingAfter') || undefined
    const endingBefore = c.req.query('endingBefore') || undefined

    const handlers = new ChatHandlers(env)
    const result = await handlers.getConversations(user.id, {
      limit,
      startingAfter,
      endingBefore
    })

    return c.json({ success: true, data: result.chats, ...result }, 200)
  } catch (error) {
    console.error('获取聊天列表失败:', error)
    const t = getI18n(c)
    return c.json(
      { success: false, message: (error as Error).message || t('chat_list_failed') },
      500
    )
  }
})

// ==================== 创建新聊天 ====================

chat.post(
  '/conversations',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createChatSchema),
  async c => {
    try {
      const user = c.get('user')
      const data = c.req.valid('json')
      const env = c.env
      const t = getI18n(c)

      if (!user) {
        return c.json({ success: false, message: t('user_not_found') }, 401)
      }

      const handlers = new ChatHandlers(env)
      const result = await handlers.createConversation(user.id, data)

      return c.json({ success: true, data: result }, 201)
    } catch (error) {
      console.error('创建聊天失败:', error)
      const t = getI18n(c)
      const statusCode =
        (error as Error).message.includes('不存在') ||
        (error as Error).message.includes('does not exist')
          ? 404
          : (error as Error).message.includes('无权限') ||
            (error as Error).message.includes('permission')
          ? 403
          : 500
      return c.json(
        { success: false, message: (error as Error).message || t('chat_create_failed') },
        statusCode
      )
    }
  }
)

// ==================== 获取聊天详情 ====================

chat.get('/conversations/:id', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const chatId = c.req.param('id')
    const env = c.env
    const t = getI18n(c)

    if (!user) {
      return c.json({ success: false, message: t('user_not_found') }, 401)
    }

    const handlers = new ChatHandlers(env)
    const result = await handlers.getConversation(user.id, chatId)

    return c.json({ success: true, data: result }, 200)
  } catch (error) {
    console.error('获取聊天详情失败:', error)
    const t = getI18n(c)
    const statusCode =
      (error as Error).message.includes('不存在') ||
      (error as Error).message.includes('does not exist')
        ? 404
        : (error as Error).message.includes('无权限') ||
          (error as Error).message.includes('permission')
        ? 403
        : 500
    return c.json(
      { success: false, message: (error as Error).message || t('chat_get_failed') },
      statusCode
    )
  }
})

// ==================== 更新聊天信息 ====================

chat.put(
  '/conversations/:id',
  authMiddleware,
  languageMiddleware,
  zValidator('json', updateChatSchema),
  async c => {
    try {
      const user = c.get('user')
      const chatId = c.req.param('id')
      const data = c.req.valid('json')
      const env = c.env
      const t = getI18n(c)

      if (!user) {
        return c.json({ success: false, message: t('user_not_found') }, 401)
      }

      const handlers = new ChatHandlers(env)
      const result = await handlers.updateConversation(user.id, chatId, data)

      return c.json({ success: true, data: result }, 200)
    } catch (error) {
      console.error('更新聊天失败:', error)
      const t = getI18n(c)
      const statusCode =
        (error as Error).message.includes('不存在') ||
        (error as Error).message.includes('does not exist')
          ? 404
          : (error as Error).message.includes('无权限') ||
            (error as Error).message.includes('permission')
          ? 403
          : 500
      return c.json(
        { success: false, message: (error as Error).message || t('chat_update_failed') },
        statusCode
      )
    }
  }
)

// ==================== 更新消息附件 ====================

chat.patch(
  '/messages',
  authMiddleware,
  languageMiddleware,
  zValidator('json', updateMessageAttachmentsSchema),
  async c => {
    try {
      const user = c.get('user')
      const data = c.req.valid('json')
      const env = c.env
      const t = getI18n(c)

      if (!user) {
        return c.json({ success: false, message: t('user_not_found') }, 401)
      }

      // 验证消息ID
      if (!data.messageId || data.messageId.trim().length === 0) {
        return c.json({ success: false, message: t('message_id_required') }, 400)
      }

      // 更新消息附件
      const result = await updateMessageAttachments(env, {
        messageId: data.messageId,
        attachments: data.attachments
      })

      if (result.length === 0) {
        return c.json({ success: false, message: t('message_update_failed') }, 404)
      }

      return c.json(
        {
          success: true,
          message: result[0],
          data: { messageId: data.messageId, attachments: data.attachments }
        },
        200
      )
    } catch (error) {
      console.error('更新消息附件失败:', error)
      const t = getI18n(c)
      return c.json(
        {
          success: false,
          message: (error as Error).message || t('message_attachments_update_failed')
        },
        500
      )
    }
  }
)

// ==================== 删除聊天 ====================

chat.delete('/conversations/:id', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const chatId = c.req.param('id')
    const env = c.env
    const t = getI18n(c)

    if (!user) {
      return c.json({ success: false, message: t('user_not_found') }, 401)
    }

    const handlers = new ChatHandlers(env)
    await handlers.deleteConversation(user.id, chatId)

    return c.json({ success: true, message: t('chat_delete_success') }, 200)
  } catch (error) {
    console.error('删除聊天失败:', error)
    const t = getI18n(c)
    const statusCode =
      (error as Error).message.includes('不存在') ||
      (error as Error).message.includes('does not exist')
        ? 404
        : (error as Error).message.includes('无权限') ||
          (error as Error).message.includes('permission')
        ? 403
        : 500
    return c.json(
      { success: false, message: (error as Error).message || t('chat_delete_failed') },
      statusCode
    )
  }
})

export default chat

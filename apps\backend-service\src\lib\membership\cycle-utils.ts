/**
 * 积分周期计算工具函数
 * 提供日期计算、周期判断等辅助功能
 */

/**
 * 计算会员周期的开始和结束日期
 */
export function calculateMembershipCycle(startDate: Date): { cycleStart: Date; cycleEnd: Date } {
  const cycleStart = new Date(startDate);
  const cycleEnd = new Date(startDate.getTime() + 30 * 24 * 60 * 60 * 1000); // 30天后

  return { cycleStart, cycleEnd };
}

/**
 * 计算两个日期之间的天数差
 */
export function calculateDaysBetween(startDate: Date, endDate: Date): number {
  const timeDiff = endDate.getTime() - startDate.getTime();
  return Math.floor(timeDiff / (1000 * 60 * 60 * 24));
}

/**
 * 计算剩余天数
 */
export function calculateRemainingDays(endDate: Date, currentDate?: Date): number {
  const now = currentDate || new Date();
  if (now >= endDate) {
    return 0;
  }
  return calculateDaysBetween(now, endDate);
}

/**
 * 检查日期是否已过期
 */
export function isDateExpired(date: Date, currentDate?: Date): boolean {
  const now = currentDate || new Date();
  return now >= date;
}

/**
 * 计算会员升级的积分补差
 */
export function calculateUpgradePointsBonus(
  fromPoints: number,
  toPoints: number,
  remainingDays: number,
  totalCycleDays = 30
): number {
  const pointsDiff = toPoints - fromPoints;
  const ratio = remainingDays / totalCycleDays;
  return Math.floor(pointsDiff * ratio);
}

/**
 * 格式化日期为本地时间字符串
 */
export function formatDateForDisplay(date: Date): string {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * 计算下一个周期的开始时间
 */
export function calculateNextCycleStart(currentCycleEnd: Date): Date {
  return new Date(currentCycleEnd.getTime() + 1); // 当前周期结束的下一毫秒
}

/**
 * 验证日期是否为有效的会员周期日期
 */
export function isValidCycleDate(date: Date): boolean {
  const now = new Date();
  const oneYearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
  const oneYearLater = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);

  return date >= oneYearAgo && date <= oneYearLater;
}

/**
 * 生成周期标识符（用于日志和调试）
 */
export function generateCycleId(userId: string, cycleStart: Date): string {
  const dateStr = cycleStart.toISOString().split('T')[0];
  return `${userId.substring(0, 8)}-${dateStr}`;
}

/**
 * 计算会员费用的日均价值（用于退款计算）
 */
export function calculateDailyValue(totalAmount: number, totalDays = 30): number {
  return totalAmount / totalDays;
}

/**
 * 计算按比例退款金额
 */
export function calculateProRatedRefund(
  totalAmount: number,
  remainingDays: number,
  totalDays = 30
): number {
  const dailyValue = calculateDailyValue(totalAmount, totalDays);
  return Math.round(dailyValue * remainingDays * 100) / 100; // 精确到分
}

/**
 * 获取周期状态描述
 */
export function getCycleStatusDescription(
  cycleStart: Date | null,
  cycleEnd: Date | null,
  currentDate?: Date
): string {
  const now = currentDate || new Date();

  if (!cycleStart || !cycleEnd) {
    return '未开始';
  }

  if (now < cycleStart) {
    return '未开始';
  }

  if (now > cycleEnd) {
    return '已过期';
  }

  const remainingDays = calculateRemainingDays(cycleEnd, now);
  return `进行中 (剩余${remainingDays}天)`;
}

/**
 * 周期时间范围类型
 */
export interface CycleTimeRange {
  start: Date;
  end: Date;
  durationDays: number;
  remainingDays: number;
  isActive: boolean;
  isExpired: boolean;
}

/**
 * 创建周期时间范围对象
 */
export function createCycleTimeRange(
  startDate: Date,
  endDate: Date,
  currentDate?: Date
): CycleTimeRange {
  const now = currentDate || new Date();
  const durationDays = calculateDaysBetween(startDate, endDate);
  const remainingDays = calculateRemainingDays(endDate, now);
  const isActive = now >= startDate && now <= endDate;
  const isExpired = now > endDate;

  return {
    start: startDate,
    end: endDate,
    durationDays,
    remainingDays,
    isActive,
    isExpired,
  };
}

/**
 * 会员等级枚举
 */
export enum MembershipLevel {
  FREE = 'FREE',
  PRO = 'PRO',
  ELITE = 'ELITE',
  ULTRA = 'ULTRA',
}

/**
 * 会员等级配置
 */
export const MEMBERSHIP_CONFIG = {
  [MembershipLevel.FREE]: {
    name: 'Free',
    points: 5,
    price: 0,
    features: ['基础聊天', '每日限额'],
  },
  [MembershipLevel.PRO]: {
    name: 'Pro',
    points: 400,
    price: 38,
    features: ['无限聊天', '图片生成', '语音生成'],
  },
  [MembershipLevel.ELITE]: {
    name: 'Elite',
    points: 1200,
    price: 98,
    features: ['Pro功能', '写真集生成', '优先支持'],
  },
  [MembershipLevel.ULTRA]: {
    name: 'Ultra',
    points: 3000,
    price: 188,
    features: ['Elite功能', '无限角色', '专属客服'],
  },
} as const;

/**
 * 获取会员等级配置
 */
export function getMembershipConfig(level: MembershipLevel) {
  return MEMBERSHIP_CONFIG[level];
}

/**
 * 验证会员等级升级路径
 */
export function isValidUpgradePath(fromLevel: MembershipLevel, toLevel: MembershipLevel): boolean {
  const levels = [
    MembershipLevel.FREE,
    MembershipLevel.PRO,
    MembershipLevel.ELITE,
    MembershipLevel.ULTRA,
  ];
  const fromIndex = levels.indexOf(fromLevel);
  const toIndex = levels.indexOf(toLevel);

  return toIndex > fromIndex;
}

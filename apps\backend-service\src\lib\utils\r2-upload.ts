// Cloudflare R2 上传工具类
// 兼容 S3 API 的文件上传服务

import type { Env } from '@/types/env';

// 配置接口
export interface R2UploadConfig {
  accountId: string;
  accessKeyId: string;
  secretAccessKey: string;
  bucketName: string;
  publicBaseUrl: string;
  region?: string; // R2 默认使用 'auto'
}

// 上传选项
export interface UploadOptions {
  fileName?: string; // 自定义文件名，如果不提供则自动生成
  folder?: string; // 上传到指定文件夹
  maxSize?: number; // 最大文件大小限制（字节）
  allowedTypes?: string[]; // 允许的文件类型
  makePublic?: boolean; // 是否设置为公开访问
}

// 上传结果
export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  error?: string;
  size?: number;
  type?: string;
}

// 生成唯一ID的辅助函数
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}

// 在 Cloudflare Workers 环境中创建 Buffer 兼容对象
function createBuffer(data: ArrayBuffer): Uint8Array {
  return new Uint8Array(data);
}

// 根据文件名推断 Content-Type
function getContentTypeFromFileName(fileName: string): string {
  const extension = fileName.toLowerCase().split('.').pop();
  switch (extension) {
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'png':
      return 'image/png';
    case 'gif':
      return 'image/gif';
    case 'webp':
      return 'image/webp';
    case 'pdf':
      return 'application/pdf';
    case 'txt':
      return 'text/plain';
    case 'json':
      return 'application/json';
    default:
      return 'application/octet-stream';
  }
}

/**
 * 使用 AWS SDK 的 Cloudflare R2 上传方法（推荐）
 * 需要先安装: npm install @aws-sdk/client-s3
 */
export async function uploadToR2(
  file: File | Uint8Array | ArrayBuffer,
  config: R2UploadConfig,
  options: UploadOptions = {}
): Promise<UploadResult> {
  try {
    // 验证配置
    if (!config.accountId || !config.accessKeyId || !config.secretAccessKey || !config.bucketName) {
      return {
        success: false,
        error: '缺少必要的 R2 配置信息',
      };
    }

    // 动态导入 AWS SDK（避免在不使用时加载）
    const { S3Client, PutObjectCommand } = await import('@aws-sdk/client-s3');

    let fileBuffer: Uint8Array;
    let fileName: string;
    let fileType: string;
    let fileSize: number;

    // 处理不同类型的文件输入
    if (file instanceof File) {
      fileBuffer = createBuffer(await file.arrayBuffer());
      fileName = options.fileName || file.name;
      fileType = file.type;
      fileSize = file.size;
    } else if (file instanceof Uint8Array) {
      fileBuffer = file;
      fileName = options.fileName || `file_${generateUUID()}`;
      // 根据文件名推断文件类型
      fileType = getContentTypeFromFileName(fileName);
      fileSize = fileBuffer.length;
    } else if (file instanceof ArrayBuffer) {
      fileBuffer = new Uint8Array(file);
      fileName = options.fileName || `file_${generateUUID()}`;
      // 根据文件名推断文件类型
      fileType = getContentTypeFromFileName(fileName);
      fileSize = fileBuffer.length;
    } else {
      // 其他类型，尝试转换
      fileBuffer = new Uint8Array(file as any);
      fileName = options.fileName || `file_${generateUUID()}`;
      fileType = getContentTypeFromFileName(fileName);
      fileSize = fileBuffer.length;
    }

    // 验证文件大小
    if (options.maxSize && fileSize > options.maxSize) {
      return {
        success: false,
        error: `文件大小超过限制 (${options.maxSize} 字节)`,
      };
    }

    // 验证文件类型
    if (options.allowedTypes && options.allowedTypes.length > 0) {
      const isAllowed = options.allowedTypes.some(
        (type) => fileType.includes(type) || fileName.toLowerCase().endsWith(type.toLowerCase())
      );
      if (!isAllowed) {
        return {
          success: false,
          error: `不支持的文件类型: ${fileType}`,
        };
      }
    }

    // 创建 S3 客户端（R2 兼容 S3 API）
    const s3Client = new S3Client({
      region: config.region || 'auto',
      endpoint: `https://${config.accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });

    // 构建文件键（路径）
    const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const uniqueId = generateUUID().substring(0, 8);
    const folder = options.folder ? `${options.folder}/` : '';
    const fileKey = `${folder}${timestamp}/${uniqueId}_${fileName}`;

    // 上传参数
    const uploadParams = {
      Bucket: config.bucketName,
      Key: fileKey,
      Body: fileBuffer,
      ContentType: fileType,
      ...(options.makePublic && { ACL: 'public-read' as const }),
    };

    // 执行上传
    const command = new PutObjectCommand(uploadParams);
    await s3Client.send(command);

    // 构建访问 URL
    const publicUrl = options.makePublic ? `${config.publicBaseUrl}/${fileKey}` : undefined;

    return {
      success: true,
      url: publicUrl,
      key: fileKey,
      size: fileSize,
      type: fileType,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '上传过程中发生未知错误',
    };
  }
}

/**
 * 预设的图片上传配置
 */
export const IMAGE_UPLOAD_OPTIONS: UploadOptions = {
  maxSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  folder: 'images',
  makePublic: true,
};

/**
 * 预设的文档上传配置
 */
export const DOCUMENT_UPLOAD_OPTIONS: UploadOptions = {
  maxSize: 50 * 1024 * 1024, // 50MB
  allowedTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  ],
  folder: 'documents',
  makePublic: false,
};

/**
 * 删除 R2 中的文件
 */
export async function deleteFromR2(
  fileKey: string,
  config: R2UploadConfig
): Promise<{ success: boolean; error?: string }> {
  try {
    const { S3Client, DeleteObjectCommand } = await import('@aws-sdk/client-s3');

    const s3Client = new S3Client({
      region: config.region || 'auto',
      endpoint: `https://${config.accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });

    const command = new DeleteObjectCommand({
      Bucket: config.bucketName,
      Key: fileKey,
    });

    await s3Client.send(command);

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '删除文件时发生错误',
    };
  }
}

/**
 * 获取文件的预签名 URL（用于私有文件的临时访问）
 */
export async function getSignedUrl(
  fileKey: string,
  config: R2UploadConfig,
  expiresIn = 3600 // 默认1小时
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    const { S3Client, GetObjectCommand } = await import('@aws-sdk/client-s3');
    const { getSignedUrl } = await import('@aws-sdk/s3-request-presigner');

    const s3Client = new S3Client({
      region: config.region || 'auto',
      endpoint: `https://${config.accountId}.r2.cloudflarestorage.com`,
      credentials: {
        accessKeyId: config.accessKeyId,
        secretAccessKey: config.secretAccessKey,
      },
    });

    const command = new GetObjectCommand({
      Bucket: config.bucketName,
      Key: fileKey,
    });

    const signedUrl = await getSignedUrl(s3Client, command, { expiresIn });

    return {
      success: true,
      url: signedUrl,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : '生成预签名URL时发生错误',
    };
  }
}

// 从环境变量获取 R2 配置（适配 Cloudflare Workers）
export function getR2ConfigFromEnv(env: Env): R2UploadConfig | null {
  // 注意：r2-upload.ts 中期望的环境变量名称与当前项目的可能不同
  const accountId = env.CLOUDFLARE_ACCOUNT_ID;
  const accessKeyId = env.R2_ACCESS_KEY_ID;
  const secretAccessKey = env.R2_SECRET_ACCESS_KEY;
  const bucketName = env.R2_BUCKET_NAME || 'pleasurehub'; // 默认bucket名称
  // 优先使用 R2_PUBLIC_BASE_URL（r2-upload.ts 期望的），如果没有则使用 R2_PUBLIC_URL
  const publicBaseUrl = env.R2_PUBLIC_BASE_URL || env.R2_PUBLIC_URL;

  if (!accountId || !accessKeyId || !secretAccessKey || !publicBaseUrl) {
    console.error('R2配置缺失:', {
      hasAccountId: !!accountId,
      hasAccessKeyId: !!accessKeyId,
      hasSecretAccessKey: !!secretAccessKey,
      hasPublicBaseUrl: !!publicBaseUrl,
      bucketName,
    });
    return null;
  }

  return {
    accountId,
    accessKeyId,
    secretAccessKey,
    bucketName,
    publicBaseUrl,
    region: 'auto',
  };
}

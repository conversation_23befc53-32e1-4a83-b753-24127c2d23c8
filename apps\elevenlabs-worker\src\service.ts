// ElevenLabs V3 独立 Worker 服务主类
import type {
  Env,
  V3ServiceConfig,
  V3GenerateRequest,
  TTSRequest,
  HealthCheckResponse,
  APIResponse
} from './types'
import { AccountManager } from './account-manager'
import { SessionManager } from './session-manager'
import { V3ApiClient } from './api-client'

export class ElevenLabsV3Service {
  private env: Env
  private accountManager: AccountManager
  private sessionManager: SessionManager
  private apiClient: V3ApiClient
  private config: V3ServiceConfig

  constructor(env: Env, config?: Partial<V3ServiceConfig>) {
    this.env = env
    this.accountManager = new AccountManager(env)
    this.sessionManager = new SessionManager(env)
    this.apiClient = new V3ApiClient()

    // 默认配置
    this.config = {
      maxRetries: 3,
      retryDelayMs: 2000,
      tokenRefreshThresholdMs: 5 * 60 * 1000, // 5分钟
      healthCheckIntervalMs: 30 * 60 * 1000, // 30分钟
      fallbackToOfficialApi: false, // 独立worker不需要降级
      ...config
    }
  }

  /**
   * 生成音频 (主入口)
   */
  async generateTTS(params: TTSRequest): Promise<APIResponse<{ audioBase64: string }>> {
    console.log('开始使用 ElevenLabs V3 API 生成音频:', params.text.substring(0, 100) + '...')

    try {
      const result = await this.generateWithV3API(params)
      return {
        success: true,
        data: result,
        message: '音频生成成功'
      }
    } catch (error) {
      console.error('V3 API 生成失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 生成音频流 (真正的流式接口)
   */
  async generateTTSStream(params: TTSRequest): Promise<Response> {
    console.log('开始使用 ElevenLabs V3 API 生成音频流:', params.text.substring(0, 100) + '...')

    let lastError: Error | null = null

    // 重试逻辑
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        console.log(`V3 API 流式尝试 ${attempt}/${this.config.maxRetries}`)

        // 获取可用账号
        const account = await this.accountManager.getNextAvailableAccount()
        if (!account) {
          throw new Error('没有可用的 ElevenLabs 账号')
        }

        // 获取有效会话
        const session = await this.sessionManager.getValidSession(account)
        if (!session) {
          await this.accountManager.markAccountFailed(account.id, '无法获取有效会话')
          throw new Error(`账号 ${account.email} 无法获取有效会话`)
        }

        // 更新账号会话信息
        await this.accountManager.updateAccountSession(
          account.id,
          session.token,
          session.refreshToken,
          session.expiry
        )

        // 获取 voice_id
        const voiceId = this.getVoiceId(params.voice_id)

        // 构建 V3 API 请求
        const v3Request: V3GenerateRequest = {
          inputs: [
            {
              text: params.text,
              voice_id: voiceId
            }
          ],
          model_id: params.model_id || 'eleven_v3',
          settings: {
            stability: params.stability || 0.5,
            use_speaker_boost: params.use_speaker_boost || true
          }
        }

        console.log('直接调用 ElevenLabs V3 流式 API...')

        // 直接调用 ElevenLabs V3 API
        const response = await fetch('https://api.elevenlabs.io/v1/text-to-dialogue/stream', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${session.token}`,
            'User-Agent':
              'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
          },
          body: JSON.stringify(v3Request)
        })

        console.log('ElevenLabs API 响应状态:', response.status)
        // console.log('ElevenLabs API 响应头:', response.headers) // 暂时注释掉，避免类型错误

        if (!response.ok) {
          const errorText = await response.text()
          console.error('V3 API 流式调用失败:', response.status, errorText)

          // 如果是账号相关错误，标记账号失败
          if (response.status === 401 || response.status === 403) {
            await this.accountManager.markAccountFailed(account.id, `API错误: ${response.status}`)
          }

          throw new Error(`ElevenLabs V3 API 错误: ${response.status} - ${errorText}`)
        }

        console.log('成功获取流式响应，开始流式转发...')

        // 直接返回 ElevenLabs 的流式响应，不做任何处理
        // 这是关键：我们不读取 response.body，而是直接转发它
        return new Response(response.body, {
          status: response.status,
          headers: {
            'Content-Type': 'audio/mpeg',
            'Transfer-Encoding': 'chunked',
            'X-Source': 'elevenlabs-v3-stream',
            'X-Account': account.email.substring(0, 3) + '***' // 脱敏显示使用的账号
          }
        })
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        console.error(`V3 API 流式尝试 ${attempt} 失败:`, lastError.message)

        // 如果是账号相关错误，标记账号失败
        if (
          lastError.message.includes('401') ||
          lastError.message.includes('403') ||
          lastError.message.includes('token')
        ) {
          const account = await this.accountManager.getNextAvailableAccount()
          if (account) {
            await this.accountManager.markAccountFailed(account.id, lastError.message)
          }
        }

        // 最后一次尝试失败，直接抛出错误
        if (attempt === this.config.maxRetries) {
          throw lastError
        }

        // 等待后重试
        await this.sleep(this.config.retryDelayMs * attempt)
      }
    }

    throw lastError || new Error('V3 API 流式重试失败')
  }

  /**
   * 使用 V3 API 生成音频
   */
  private async generateWithV3API(params: TTSRequest): Promise<{ audioBase64: string }> {
    let lastError: Error | null = null

    // 重试逻辑
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        console.log(`V3 API 尝试 ${attempt}/${this.config.maxRetries}`)

        // 获取可用账号
        const account = await this.accountManager.getNextAvailableAccount()
        if (!account) {
          throw new Error('没有可用的 ElevenLabs 账号')
        }

        // 获取有效会话
        const session = await this.sessionManager.getValidSession(account)
        if (!session) {
          await this.accountManager.markAccountFailed(account.id, '无法获取有效会话')
          throw new Error(`账号 ${account.email} 无法获取有效会话`)
        }

        // 更新账号会话信息
        await this.accountManager.updateAccountSession(
          account.id,
          session.token,
          session.refreshToken,
          session.expiry
        )

        // 获取 voice_id
        const voiceId = this.getVoiceId(params.voice_id)

        // 构建 V3 API 请求
        const v3Request: V3GenerateRequest = {
          inputs: [
            {
              text: params.text,
              voice_id: voiceId
            }
          ],
          model_id: params.model_id || 'eleven_v3',
          settings: {
            stability: params.stability || 0.5,
            use_speaker_boost: params.use_speaker_boost || true
          }
        }

        // 调用 V3 API
        const apiResponse = await this.apiClient.generateTTS(v3Request, session)

        if (apiResponse.status === 'completed' && apiResponse.audio_data) {
          // 转换为 Base64
          const audioBase64 = this.arrayBufferToBase64(apiResponse.audio_data)
          return { audioBase64 }
        } else if (apiResponse.status === 'processing' && apiResponse.task_id) {
          // 异步任务，需要轮询
          const pollResult = await this.apiClient.pollTaskStatus(apiResponse.task_id, session)

          if (pollResult.status === 'completed' && pollResult.audio_data) {
            const audioBase64 = this.arrayBufferToBase64(pollResult.audio_data)
            return { audioBase64 }
          } else {
            throw new Error(pollResult.error || '轮询任务失败')
          }
        } else {
          throw new Error(apiResponse.error || 'V3 API 返回未知状态')
        }
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        console.error(`V3 API 尝试 ${attempt} 失败:`, lastError.message)

        // 如果是账号相关错误，标记账号失败
        if (
          lastError.message.includes('401') ||
          lastError.message.includes('403') ||
          lastError.message.includes('token')
        ) {
          const account = await this.accountManager.getNextAvailableAccount()
          if (account) {
            await this.accountManager.markAccountFailed(account.id, lastError.message)
          }
        }

        // 最后一次尝试失败，直接抛出错误
        if (attempt === this.config.maxRetries) {
          throw lastError
        }

        // 等待后重试
        await this.sleep(this.config.retryDelayMs * attempt)
      }
    }

    throw lastError || new Error('V3 API 重试失败')
  }

  /**
   * 获取 voice_id（复用现有逻辑）
   */
  private getVoiceId(voiceModelId?: string): string {
    if (!voiceModelId) {
      return 'JBFqnCBsd6RMkjVDRZzb' // 默认声音
    }
    return voiceModelId
  }

  /**
   * ArrayBuffer 转 Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * 获取可用声音列表
   */
  async getAvailableVoices(): Promise<APIResponse<any[]>> {
    try {
      // 获取一个可用账号
      const account = await this.accountManager.getNextAvailableAccount()
      if (!account) {
        return {
          success: false,
          error: '没有可用的 ElevenLabs 账号'
        }
      }

      // 获取有效会话
      const session = await this.sessionManager.getValidSession(account)
      if (!session) {
        return {
          success: false,
          error: '无法获取有效会话'
        }
      }

      // 获取声音列表
      const voices = await this.apiClient.getVoices(session)

      return {
        success: true,
        data: voices,
        message: '获取声音列表成功'
      }
    } catch (error) {
      console.error('获取声音列表失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<APIResponse<HealthCheckResponse>> {
    try {
      const stats = await this.accountManager.getAccountStats()
      const accounts = await this.accountManager.getAllAccounts()

      const details = await Promise.all(
        accounts.map(async account => {
          const health = await this.accountManager.getAccountHealth(account.id)
          return (
            health || {
              accountId: account.id,
              isHealthy: account.isActive && account.failureCount < 3,
              lastCheck: new Date(),
              errorCount: account.failureCount
            }
          )
        })
      )

      const healthData: HealthCheckResponse = {
        healthy: stats.active > 0,
        total_accounts: stats.total,
        active_accounts: stats.active,
        failed_accounts: stats.failed,
        details
      }

      return {
        success: true,
        data: healthData,
        message: '健康检查完成'
      }
    } catch (error) {
      console.error('健康检查失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 等待函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

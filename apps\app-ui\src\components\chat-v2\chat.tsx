import { ChatRequestOptions, LangChainMessage, Attachment } from './type'

// 使用 LangChain 聊天 hook（需要实现）
import { useLangChainChat } from '@/hooks/use-langchain-chat'
import { useState, useEffect, useRef } from 'react'
import { useSearchParams } from 'react-router'
import { generateUUID } from '@/lib/utils'
import { MultimodalInputV2 } from './multimodal-input'
import { MessagesV2 } from './messages'
import { resetStreamingState } from '../data-stream-handler'
import { useDeviceSafeArea } from '@/hooks/use-mobile-viewport'
import { useChatBackgroundDb } from '@/hooks/use-chat-background-db'
import { Keyboard } from '@capacitor/keyboard'
import { Capacitor } from '@capacitor/core'

export type VisibilityType = 'private' | 'public'

// 定义扩展的ChatRequestOptions类型
interface ExtendedChatRequestOptions extends ChatRequestOptions {
  deviceInfo?: string
  isVoiceMessage?: boolean
  audioBlob?: Blob
  audioDuration?: number
}

export function ChatV2({
  id,
  initialMessages,
  isReadonly,
  effectiveRoleId,
  onMessagesUpdate
}: {
  id: string
  initialMessages: Array<LangChainMessage>
  selectedVisibilityType: VisibilityType
  isReadonly: boolean
  effectiveRoleId?: string
  onMessagesUpdate?: (messages: Array<LangChainMessage>) => void
}) {
  const [searchParams] = useSearchParams()
  const role = effectiveRoleId || searchParams.get('role')
  const safeArea = useDeviceSafeArea()
  const inputRef = useRef<HTMLDivElement>(null)
  const messagesContainerRef = useRef<HTMLDivElement>(null)
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)

  // 键盘适配相关状态
  const [keyboardHeight, setKeyboardHeight] = useState(0)
  const [webViewShrunk, setWebViewShrunk] = useState(false)
  const initialHeightRef = useRef<number>(window.innerHeight)

  // 背景图管理 - 使用新的数据库存储方案
  const {
    backgroundImageUrl,
    isBackgroundLoaded,
    isGenerating,
    generateBackground,
    loadingProgress,
    error
  } = useChatBackgroundDb({
    chatId: id,
    roleId: effectiveRoleId || 'ruyun', // 使用有效的角色ID，默认为 ruyun
    enabled: true, // 启用背景图功能
    initialMessages // 传入初始消息，用于判断是否为新聊天
  })

  // 调试：背景图状态变化时打印日志
  useEffect(() => {
    console.log('🖼️ [ChatV2] 背景图状态更新:', {
      hasUrl: !!backgroundImageUrl,
      isLoaded: isBackgroundLoaded,
      isGenerating,
      urlPrefix: backgroundImageUrl?.substring(0, 50),
      progress: loadingProgress,
      error
    })
  }, [backgroundImageUrl, isBackgroundLoaded, isGenerating, loadingProgress, error])

  // 只传端点路径，让 hook 内部使用 apiClient
  const apiEndpoint = role
    ? `/api/chatv2/stream?role=${encodeURIComponent(role)}`
    : `/api/chatv2/stream`

  const { messages, setMessages, handleSubmit, input, setInput, append, status, stop } =
    useLangChainChat({
      id,
      initialMessages,
      api: apiEndpoint,
      credentials: 'include',
      experimental_throttle: 50,
      sendExtraMessageFields: true,
      generateId: generateUUID,
      enableLocalStorage: true, // 🚀 启用本地存储
      onError: (error: any) => {
        console.error('聊天API错误:', error)
      },
      onFinish: () => {
        resetStreamingState()
      },
      onMessagesUpdate: messages => {
        // 🚀 同步到原有的缓存系统（保持兼容性）
        onMessagesUpdate?.(messages)
      }
    })

  const [attachments, setAttachments] = useState<Array<Attachment>>([])

  useEffect(() => {
    return () => {
      resetStreamingState()
    }
  }, [])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      resetStreamingState()
    }
  }, [])

  // 键盘避让 - 使用 Capacitor Keyboard API
  useEffect(() => {
    if (!Capacitor.isNativePlatform()) {
      // Web 平台回退到原有的监听方式
      let initialHeight = window.innerHeight
      let isInputFocused = false

      const handleFocus = () => {
        isInputFocused = true
        initialHeight = window.innerHeight
        setIsKeyboardVisible(true)
      }

      const handleBlur = () => {
        isInputFocused = false
        setIsKeyboardVisible(false)
      }

      const handleResize = () => {
        if (!isInputFocused) return

        const currentHeight = window.innerHeight
        const heightDiff = initialHeight - currentHeight

        // 如果高度减少超过100px，说明键盘弹出了
        if (heightDiff > 100) {
          setIsKeyboardVisible(true)
        } else {
          // 键盘收起时，自动失焦并重置状态
          setIsKeyboardVisible(false)
          isInputFocused = false

          // 自动失焦当前聚焦的输入框
          const activeElement = document.activeElement
          if (
            activeElement instanceof HTMLInputElement ||
            activeElement instanceof HTMLTextAreaElement
          ) {
            activeElement.blur()
          }
        }
      }

      // 监听所有输入框的焦点事件
      const handleGlobalFocus = (e: FocusEvent) => {
        if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
          handleFocus()
        }
      }

      const handleGlobalBlur = (e: FocusEvent) => {
        if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
          handleBlur()
        }
      }

      // 监听全局焦点事件和窗口大小变化
      document.addEventListener('focusin', handleGlobalFocus)
      document.addEventListener('focusout', handleGlobalBlur)
      window.addEventListener('resize', handleResize)

      return () => {
        document.removeEventListener('focusin', handleGlobalFocus)
        document.removeEventListener('focusout', handleGlobalBlur)
        window.removeEventListener('resize', handleResize)
      }
    }

    // 原生平台使用 Keyboard API
    let keyboardWillShowListener: any
    let keyboardWillHideListener: any

    const setupKeyboardListeners = async () => {
      keyboardWillShowListener = await Keyboard.addListener(
        'keyboardWillShow',
        (info: { keyboardHeight: number }) => {
          // 检测 WebView 是否自动缩放
          const afterKeyboardHeight = window.innerHeight
          const keyboardHeight = info.keyboardHeight
          const initialHeight = initialHeightRef.current

          const webViewShrunk = afterKeyboardHeight < initialHeight

          setWebViewShrunk(webViewShrunk)
          setIsKeyboardVisible(true)

          if (webViewShrunk) {
            // WebView 高度已经缩了，不要再移动元素
            setKeyboardHeight(0) // 贴合底部即可
          } else {
            // WebView 高度没缩，手动移动输入框
            setKeyboardHeight(keyboardHeight)
          }
        }
      )

      keyboardWillHideListener = await Keyboard.addListener('keyboardWillHide', () => {
        setIsKeyboardVisible(false)
        setKeyboardHeight(0)
        setWebViewShrunk(false)
        // 重新记录当前高度作为新的初始高度
        initialHeightRef.current = window.innerHeight
      })
    }

    setupKeyboardListeners()

    return () => {
      keyboardWillShowListener?.remove()
      keyboardWillHideListener?.remove()
    }
  }, [])

  // 消息变化时自动滚动到最新消息
  useEffect(() => {
    if (messagesContainerRef.current && messages.length > 0) {
      const lastMessage = messagesContainerRef.current.querySelector('.message:last-child')
      lastMessage?.scrollIntoView({ behavior: 'smooth', block: 'end' })
    }
  }, [messages])

  const wrappedHandleSubmit = (
    event?: React.FormEvent | { preventDefault?: () => void },
    chatRequestOptions?: ExtendedChatRequestOptions
  ) => {
    resetStreamingState()

    // 直接传递所有选项给handleSubmit，包括deviceInfo
    return handleSubmit(event, chatRequestOptions)
  }

  const bottomInset = safeArea.bottom + 10

  return (
    <div
      className="flex flex-1 flex-col h-full overflow-hidden relative"
      style={{
        height: `calc(100vh - ${safeArea.top}px - 4rem)`,
        position: 'relative',
        background: backgroundImageUrl ? 'none' : undefined
      }}
    >
      {/* 背景图层 - 使用更简单可靠的方式 */}
      {backgroundImageUrl && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 0,
            backgroundImage: `url(${backgroundImageUrl})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            opacity: isBackgroundLoaded ? 0.8 : 0.3
          }}
        />
      )}

      {/* 背景图遮罩层 */}
      {backgroundImageUrl && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 1,
            backgroundColor: 'rgba(255, 255, 255, 0.1)',
            backdropFilter: 'blur(0.5px)',
            opacity: isBackgroundLoaded ? 1 : 0
          }}
        />
      )}

      {/* 消息区域 - 占据所有剩余空间并可滚动 */}
      <div
        ref={messagesContainerRef}
        className="flex-1 min-h-0 overflow-y-auto relative z-10"
        style={{
          paddingBottom: isKeyboardVisible
            ? `${webViewShrunk ? 80 : 110}px` // 键盘弹出时预留更多空间
            : '80px', // 正常状态下预留输入区域的空间
          transition: 'padding-bottom 0.3s ease'
        }}
      >
        <MessagesV2 status={status} messages={messages} chatId={id} onScene={generateBackground} />
      </div>

      {/* 输入区域 */}
      <div
        ref={inputRef}
        className="w-full mt-auto border-t border-divider bg-background shadow-sm transition-all hover:shadow-md focus-within:border-primary focus-within:shadow-md rounded-t-2xl relative"
        style={{
          paddingBottom: bottomInset,
          position: 'fixed',
          bottom: webViewShrunk ? '0px' : `${keyboardHeight}px`, // 根据 WebView 是否缩放调整定位
          left: '0',
          right: '0',
          zIndex: 10,
          transition: 'bottom 0.3s ease'
        }}
      >
        <div className="flex mx-auto p-2 gap-2 w-full md:max-w-3xl shrink-0">
          {!isReadonly && (
            <MultimodalInputV2
              chatId={id}
              input={input}
              setInput={setInput}
              handleSubmit={wrappedHandleSubmit}
              status={status}
              stop={stop}
              attachments={attachments}
              setAttachments={setAttachments}
              messages={messages}
              setMessages={setMessages}
              append={append}
              role={role || ''}
            />
          )}
        </div>
      </div>
    </div>
  )
}

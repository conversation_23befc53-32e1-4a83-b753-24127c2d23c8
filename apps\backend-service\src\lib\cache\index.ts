/**
 * 缓存模块入口文件
 * 统一导出所有缓存相关的功能
 */

// 核心缓存管理器
export { CacheManager } from './cache-manager'

// 缓存工具函数
export {
  getCachedDbUserId,
  getCachedRealVoiceId,
  getCachedMembership,
  clearUserCaches,
  clearVoiceModelCache,
  clearMembershipCaches,
  getCacheStats
} from './cache-utils'

// 类型定义
export {
  CachePrefix,
  CacheTTL,
  type CacheResult,
  type CacheStats,
  type CachedUserMapping,
  type CachedVoiceModel,
  type CachedUserPoints,
  type CachedPermission
} from './types'

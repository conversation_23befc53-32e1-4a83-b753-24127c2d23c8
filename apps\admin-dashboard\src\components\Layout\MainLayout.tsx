import React, { useState, useEffect } from 'react'
import { Layout, Menu, Avatar, Dropdown, Space, Button, theme } from 'antd'
import {
  UserOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ShoppingOutlined,
  CrownOutlined,
  GiftOutlined,
  FileTextOutlined,
  BulbOutlined,
  TabletOutlined,
  SettingOutlined,
  MobileOutlined,
  CodeOutlined,
  ThunderboltOutlined,
  PlayCircleOutlined
} from '@ant-design/icons'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuthStore } from '@/stores/auth'

const { Header, Sider, Content } = Layout

interface MainLayoutProps {
  children: React.ReactNode
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()
  const { user, logout, getProfile } = useAuthStore()
  const {
    token: { colorBgContainer, borderRadiusLG }
  } = theme.useToken()

  useEffect(() => {
    if (!user) {
      getProfile()
    }
  }, [user, getProfile])

  const handleLogout = async () => {
    await logout()
    navigate('/login')
  }

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '数据概览'
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理'
    },
    {
      key: '/orders',
      icon: <ShoppingOutlined />,
      label: '订单管理'
    },
    {
      key: 'membership',
      icon: <CrownOutlined />,
      label: '会员管理',
      children: [
        {
          key: '/membership/plans',
          label: '会员套餐'
        },
        {
          key: '/membership/subscriptions',
          label: '用户订阅'
        }
      ]
    },
    {
      key: 'points',
      icon: <GiftOutlined />,
      label: '积分管理',
      children: [
        {
          key: '/points/packages',
          label: '积分套餐'
        },
        {
          key: '/points/users',
          label: '用户积分'
        },
        {
          key: '/points/config',
          label: '消耗配置'
        }
      ]
    },
    {
      key: 'content',
      icon: <FileTextOutlined />,
      label: '内容管理',
      children: [
        {
          key: '/content/characters',
          label: '角色管理'
        },
        {
          key: '/content/scripts',
          label: '剧本管理'
        },
        {
          key: '/content/templates',
          label: '写真集模板'
        }
      ]
    },
    {
      key: 'marketing',
      icon: <BulbOutlined />,
      label: '营销管理',
      children: [
        {
          key: '/marketing/invites',
          label: '邀请码管理'
        },
        {
          key: '/marketing/commission',
          label: '佣金管理'
        },
        {
          key: '/marketing/withdraw',
          label: '提现审核'
        },
        {
          key: '/marketing/activation',
          label: '激活码管理'
        }
      ]
    },
    {
      key: 'devices',
      icon: <TabletOutlined />,
      label: '设备管理',
      children: [
        {
          key: '/devices/command-sets',
          icon: <CodeOutlined />,
          label: '指令集管理'
        },
        {
          key: '/devices/functions',
          icon: <ThunderboltOutlined />,
          label: '功能管理'
        },
        {
          key: '/devices/modes',
          icon: <PlayCircleOutlined />,
          label: '模式管理'
        },
        {
          key: '/devices/management',
          icon: <TabletOutlined />,
          label: '设备管理'
        }
      ]
    },
    {
      key: '/app/versions',
      icon: <MobileOutlined />,
      label: '应用管理'
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置'
    }
  ]

  const userDropdownItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人信息'
    },
    {
      type: 'divider' as const
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key.startsWith('/')) {
      navigate(key)
    }
  }

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        style={{
          background: colorBgContainer
        }}
      >
        <div
          style={{
            padding: '16px',
            textAlign: 'center',
            borderBottom: '1px solid #f0f0f0'
          }}
        >
          <h2
            style={{
              margin: 0,
              fontSize: collapsed ? '14px' : '18px',
              color: '#1890ff'
            }}
          >
            {collapsed ? '灵犀' : '灵犀秘境管理后台'}
          </h2>
        </div>

        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{ border: 'none' }}
        />
      </Sider>

      <Layout>
        <Header
          style={{
            padding: '0 16px',
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid #f0f0f0'
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64
            }}
          />

          <Space>
            <Dropdown menu={{ items: userDropdownItems }} placement="bottomRight">
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <span>{user?.email || '管理员'}</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        <Content
          style={{
            margin: '16px',
            padding: '16px',
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG
          }}
        >
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout

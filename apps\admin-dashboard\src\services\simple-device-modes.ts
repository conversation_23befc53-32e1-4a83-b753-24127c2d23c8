import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

export interface SimpleDeviceMode {
  id: string
  name: string
  description?: string
  commandSetId: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  commandSet?: {
    id: string
    name: string
    command: string
    description?: string
    isActive: boolean
  } | null
}

export interface CreateSimpleModeRequest {
  name: string
  description?: string
  commandSetId: string
  isActive?: boolean
}

export class SimpleDeviceModeService {
  // 获取设备模式列表
  async getModes(params: {
    page?: number
    pageSize?: number
    keyword?: string
    isActive?: boolean
  }): Promise<ApiResponse<PaginatedResponse<SimpleDeviceMode>>> {
    return await apiService.get<PaginatedResponse<SimpleDeviceMode>>('/admin/devices/modes', { params })
  }

  // 获取设备模式详情
  async getMode(id: string): Promise<ApiResponse<SimpleDeviceMode>> {
    return await apiService.get<SimpleDeviceMode>(`/admin/devices/modes/${id}`)
  }

  // 创建设备模式
  async createMode(data: CreateSimpleModeRequest): Promise<ApiResponse<SimpleDeviceMode>> {
    return await apiService.post<SimpleDeviceMode>('/admin/devices/modes', data)
  }

  // 更新设备模式
  async updateMode(id: string, data: Partial<CreateSimpleModeRequest>): Promise<ApiResponse<SimpleDeviceMode>> {
    return await apiService.put<SimpleDeviceMode>(`/admin/devices/modes/${id}`, data)
  }

  // 删除设备模式
  async deleteMode(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/devices/modes/${id}`)
  }

  // 切换模式状态
  async toggleStatus(id: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/devices/modes/${id}/toggle-status`, { isActive })
  }
}

export const simpleModeService = new SimpleDeviceModeService()
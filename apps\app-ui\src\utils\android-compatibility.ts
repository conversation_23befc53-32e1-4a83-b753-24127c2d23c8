/**
 * Android 兼容性检测和修复工具
 * 只对 Android 12 及以下设备应用兼容性修复
 */

export interface AndroidCompatibilityInfo {
  isAndroid: boolean
  androidVersion: number | null
  chromeVersion: number | null
  needsCompatibility: boolean
  isWebViewLegacy: boolean
  cssFeatures: {
    colorMix: boolean
    customProperties: boolean
    gradients: boolean
  }
}

/**
 * 检测 Android 版本和兼容性需求
 */
export function detectAndroidCompatibility(): AndroidCompatibilityInfo {
  const userAgent = navigator.userAgent
  console.log('User Agent:', userAgent)

  // 检测 Android 版本
  const androidMatch = userAgent.match(/Android (\d+)/)
  const isAndroid = !!androidMatch
  const androidVersion = androidMatch ? parseInt(androidMatch[1]) : null

  // 检测 WebView Chrome 版本
  const webViewMatch = userAgent.match(/wv\).*Chrome\/(\d+)/)
  const chromeVersion = webViewMatch ? parseInt(webViewMatch[1]) : null

  // 检测 CSS 特性支持
  const cssFeatures = {
    colorMix: CSS.supports('color', 'color-mix(in srgb, red 50%, blue)'),
    customProperties: CSS.supports('color', 'var(--test)'),
    gradients: CSS.supports('background', 'linear-gradient(to right, red, blue)')
  }

  // 判断是否需要兼容性修复
  const needsCompatibility = isAndroid && androidVersion !== null && androidVersion <= 12
  const isWebViewLegacy = chromeVersion !== null && chromeVersion < 78

  return {
    isAndroid,
    androidVersion,
    chromeVersion,
    needsCompatibility,
    isWebViewLegacy,
    cssFeatures
  }
}

/**
 * 应用兼容性修复类名
 */
export function applyCompatibilityClasses(info: AndroidCompatibilityInfo): void {
  const { documentElement } = document

  if (info.isAndroid && info.androidVersion) {
    console.log(`检测到 Android ${info.androidVersion}`)

    if (info.needsCompatibility) {
      documentElement.classList.add('android-legacy')
      console.log(`✅ Android ${info.androidVersion} 启用兼容性修复`)

      if (info.chromeVersion) {
        console.log(`WebView Chrome 版本: ${info.chromeVersion}`)

        if (info.isWebViewLegacy) {
          documentElement.classList.add('webview-legacy')
          console.log('✅ 启用 WebView 兼容性修复')
        }
      }
    } else {
      console.log(`✅ Android ${info.androidVersion} 使用原生支持，无需兼容性修复`)
    }
  } else {
    console.log('未检测到 Android 系统')
  }

  console.log('CSS 特性支持:', info.cssFeatures)

  // 如果不支持关键特性，添加额外的兼容性类
  if (!info.cssFeatures.colorMix) {
    documentElement.classList.add('no-color-mix')
  }
  if (!info.cssFeatures.customProperties) {
    documentElement.classList.add('no-css-vars')
  }
}

/**
 * 初始化 Android 兼容性检测和修复
 */
export function setupAndroidCompatibility(): AndroidCompatibilityInfo {
  const info = detectAndroidCompatibility()
  applyCompatibilityClasses(info)
  return info
}

/**
 * 获取兼容性状态摘要
 */
export function getCompatibilitySummary(info: AndroidCompatibilityInfo): string {
  if (!info.isAndroid) {
    return '非 Android 设备'
  }

  const version = info.androidVersion || '未知'
  const status = info.needsCompatibility ? '需要兼容性修复' : '原生支持'
  const webView = info.chromeVersion ? ` (WebView Chrome ${info.chromeVersion})` : ''

  return `Android ${version}${webView} - ${status}`
}

/**
 * 检查是否需要特定的兼容性修复
 */
export function needsSpecificFix(
  info: AndroidCompatibilityInfo,
  fixType: 'gradients' | 'flexbox' | 'css-vars'
): boolean {
  if (!info.needsCompatibility) return false

  switch (fixType) {
    case 'gradients':
      return !info.cssFeatures.gradients || !info.cssFeatures.colorMix
    case 'flexbox':
      return info.isWebViewLegacy
    case 'css-vars':
      return !info.cssFeatures.customProperties
    default:
      return false
  }
}

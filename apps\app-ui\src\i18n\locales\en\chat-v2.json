{"message": {"ai_assistant": "AI Assistant"}, "multimodal_input": {"connected": "Connected", "stop_device_failed": "Failed to stop device", "device_disconnected": "<PERSON><PERSON> disconnected", "disconnect_device": "Disconnect device", "recognizing_voice": "Recognizing voice...", "message_sent": "Message sent...", "input_message": "Type a message...", "stop_generating": "Stop generating", "send_message": "Send message", "connect_device": "Connect device"}, "image": {"using_local_image": "Using local image:", "localization_success": "Image localization success:", "localization_failed": "Localization failed:", "checking_attachments": "Checking existing attachments:", "found_local_image": "Found local image:", "found_remote_image": "Found remote image, starting localization:", "need_generation": "Image generation needed", "no_image_no_generation": "No image and no generation needed", "image_not_found": "Image not found", "checking_failed": "Checking attachments failed:", "image_check_failed": "Image check failed", "checking_image": "Checking image...", "ai_generated_image": "AI Generated Image", "click_to_view": "Click to view full image", "generation_failed": "Generation failed", "retry": "Retry", "preview": "Image Preview", "loading": "Loading...", "load_failed": "Image loading failed", "generated_image": "Generated image", "download": "Download image", "download_failed": "Download failed", "download_error": "Download error"}, "video": {"save_failed": "Database save failed, but local file saved:", "localization_failed": "Localization failed:", "no_video": "No video to display", "ai_generated_video": "AI Generated Video", "processing": "Processing...", "thumbnail": "Video thumbnail", "thumbnail_load_failed": "⚠️ Thumbnail loading failed", "loading": "Loading video...", "load_failed": "Video loading failed", "generated_video": "Generated video", "playback": "Video Playback", "player_error": "Player error", "download_failed": "Download failed", "download_error": "Download error", "download": "Download video"}, "video_generation": {"success": "✅ Multimodal video generation completed:", "localization_complete": "✅ Video localization complete", "local_storage_failed": "⚠️ Local storage failed:", "failed": "❌ Multimodal video generation failed:", "progress": "📊 Multimodal video generation progress:", "image_complete": "🎨 Image generation complete:", "checking_attachments": "🔍 Checking existing attachments:", "completed_attachment_found": "Completed video attachment detected, skipping auto-generation:", "generating_status_found": "Generation status detected, starting polling", "generating": "Generating video...", "no_status_detected": "No existing status detected, preparing to start generation", "using_local_image": "Using local image:", "querying_message_status": "Querying message status in database:", "query_failed": "Query failed, message may not be saved to database yet:", "message_found": "Found message in database:", "image_attachment_found": "Image attachment found in database:", "generating_status_attachment_found": "Generation status attachment found in database, already generating", "check_db_status_failed": "Failed to check database status", "check_message_status_failed": "Failed to check message status", "permission_insufficient": "Insufficient permission for image generation", "no_permission": "Insufficient permission, cannot generate image", "permission_verified": "Image generation permission verified", "checking_permission": "Checking permission...", "starting_generation": "Starting image generation...", "generation_call_success": "Image generation call successful", "permission_verification_failed": "Permission verification failed, stopping generation", "edge_function_exception": "Edge Function exception", "generation_failed": "Image generation failed, please try again", "polling_condition_check": "Polling condition check:", "start_polling": "Starting to poll image generation status:", "status_update": "Status update:", "generation_complete": "Image generation complete:", "local_storage_failed_using_remote": "Local storage failed, using remote URL:", "generation_complete_short": "Image generation complete", "generation_failed_with_reason": "Image generation failed:", "generation_failed_short": "Image generation failed", "polling_failed": "Polling status failed:", "generation_timeout": "Image generation timeout, please try again", "video_generation_failed": "Video generation failed", "please_try_again": "Please try again later", "generation_usually_takes": "Video generation usually takes 3-5 minutes, please be patient", "preview": "Generation preview"}, "markdown": {"attachment": "Attachment", "loading_video_generator": "Loading video generator...", "video_generation_success": "✅ Video generation completed:", "video_generation_failed": "❌ Video generation failed:"}, "thinking_indicator": {"avatar": "Character avatar"}, "device_connect": {"connect_device": "Connect Device", "skip_connection": "Skip Connection", "please_enter_device_code": "Please enter device code", "invalid_device_code": "Invalid device code, please check and try again", "connection_failed": "Connection failed, please try again later", "connecting": "Connecting...", "device_code": "Device Code", "enter_6_digit_code": "Please enter 6-digit device code", "or": "or", "loading_device_list": "Loading device list...", "sample_device_codes": "Sample device codes:", "scan_qr_code": "Scan device QR code", "scanning": "Scanning...", "place_qr_code": "Place the device QR code in front of the camera", "scanned_code_invalid": "Scanned code is invalid", "no_available_devices": "No available devices", "scan_failed": "<PERSON><PERSON> failed, please try again"}, "voice_recorder": {"get_mic_permission_failed": "Failed to get microphone permission:", "recording_failed": "Recording failed", "recording_too_short": "Recording too short", "operation_failed": "Operation failed, please try again", "click_to_record": "Tap to record or hold to send quickly", "click_to_start_recording": "Tap to start voice recording", "click_to_stop": "Tap to stop recording", "release_to_cancel": "Release to cancel recording", "release_to_send": "Release to send immediately", "processing_audio": "Processing audio...", "ready": "Ready", "cancel": "Cancel", "need_microphone_permission": "Microphone Permission Required", "voice_recording_needs_mic": "Voice recording feature needs access to your microphone", "privacy_protection": "Privacy Protection", "privacy_description": "We don't save your recordings, all voice data is only used for text conversion", "allow_microphone_access": "Allow Microphone Access"}, "device_control": {"device_control_panel": "Device Control Panel", "connected": "Connected", "bluetooth_error": "Bluetooth error", "bluetooth_ready": "Bluetooth ready", "bluetooth_initializing": "Bluetooth initializing...", "intensity": "Intensity", "off": "Off", "level": "Level", "classic_mode": "Classic Mode", "disconnect": "Disconnect"}}
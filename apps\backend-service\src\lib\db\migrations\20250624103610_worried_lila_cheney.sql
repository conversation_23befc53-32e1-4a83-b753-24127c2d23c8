CREATE TABLE IF NOT EXISTS "PaymentOrder" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"plan_id" uuid,
	"points_package_id" uuid,
	"order_no" varchar(64) NOT NULL,
	"external_order_id" varchar(128),
	"amount" numeric(10, 2) NOT NULL,
	"currency" varchar(3) DEFAULT 'CNY' NOT NULL,
	"payment_method" varchar(20),
	"status" varchar DEFAULT 'pending' NOT NULL,
	"description" text NOT NULL,
	"is_upgrade" boolean DEFAULT false NOT NULL,
	"original_amount" numeric(10, 2),
	"current_subscription_id" uuid,
	"callback_url" text,
	"return_url" text,
	"notify_url" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"paid_at" timestamp,
	"expires_at" timestamp,
	"metadata" json DEFAULT '{}',
	CONSTRAINT "PaymentOrder_order_no_unique" UNIQUE("order_no")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PaymentOrder" ADD CONSTRAINT "PaymentOrder_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PaymentOrder" ADD CONSTRAINT "PaymentOrder_plan_id_MembershipPlan_id_fk" FOREIGN KEY ("plan_id") REFERENCES "public"."MembershipPlan"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "PaymentOrder" ADD CONSTRAINT "PaymentOrder_points_package_id_PointsPackage_id_fk" FOREIGN KEY ("points_package_id") REFERENCES "public"."PointsPackage"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_user_id" ON "PaymentOrder" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_order_no" ON "PaymentOrder" USING btree ("order_no");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_status" ON "PaymentOrder" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_external_id" ON "PaymentOrder" USING btree ("external_order_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_expires_at" ON "PaymentOrder" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_payment_method" ON "PaymentOrder" USING btree ("payment_method");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_created_at" ON "PaymentOrder" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_payment_order_user_created" ON "PaymentOrder" USING btree ("user_id","created_at" DESC NULLS LAST);
import { createClient } from '@supabase/supabase-js'

// Supabase 配置
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL || 'https://cqvzpxjidnwezcldzjpi.supabase.co'
// 🔧 TODO: 请从 Supabase Dashboard → Settings → API 获取正确的 anon key
const supabaseAnonKey =
  import.meta.env.VITE_SUPABASE_ANON_KEY ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNxdnpweGppZG53ZXpjbGR6anBpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzMzMDI4MTAsImV4cCI6MjA0ODg3ODgxMH0.YOUR_ACTUAL_ANON_KEY_HERE'

// 创建 Supabase 客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

export default supabase

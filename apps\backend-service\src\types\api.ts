import { z } from 'zod';

// ==================== 通用 API 响应类型 ====================
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// ==================== 认证相关类型 ====================
export const LoginSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6位'),
});

export const RegisterSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少6位'),
  nickname: z.string().min(1, '昵称不能为空').max(50, '昵称不能超过50字符'),
});

export type LoginRequest = z.infer<typeof LoginSchema>;
export type RegisterRequest = z.infer<typeof RegisterSchema>;

export interface AuthResponse {
  user: {
    id: string;
    email: string;
    nickname?: string;
  };
  access_token: string;
  refresh_token: string;
}

// ==================== 聊天相关类型 ====================
export const CreateChatSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(100, '标题不能超过100字符'),
  roleId: z.string().optional(),
});

export const SendMessageSchema = z.object({
  content: z.string().min(1, '消息内容不能为空'),
  attachments: z.array(z.string()).optional(),
});

export type CreateChatRequest = z.infer<typeof CreateChatSchema>;
export type SendMessageRequest = z.infer<typeof SendMessageSchema>;

// ==================== 文件上传类型 ====================
export interface UploadResponse {
  fileId: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  url: string;
}

// ==================== 错误类型 ====================
export class ApiError extends Error {
  constructor(
    public statusCode: number,
    message: string,
    public code?: string
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// ==================== 缓存相关类型 ====================
export interface CacheOptions {
  ttl?: number; // 缓存时间 (秒)
  key: string;
}

export interface CachedData<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// AI 相关类型定义
export interface CharacterType {
  id: string; // 角色ID
  name: string; // 名字
  gender: 'male' | 'female' | 'other'; // 性别
  age: string; // 年龄
  relationship: string; // 关系
  ethnicity: string; // 种族
  eyeColor: string; // 眼睛颜色
  hairStyle: string; // 发型
  hairColor: string; // 头发颜色
  bodyType: string; // 身材类型
  breastSize?: string; // 胸部大小
  buttSize?: string; // 臀部大小
  personality: string; // 个性
  clothing: string; // 穿着
  voice: string; // 声音
  imageUrl: string; // 图片URL
}

export type UserType = 'regular' | 'premium' | 'vip';

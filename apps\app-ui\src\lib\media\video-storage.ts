/**
 * 视频本地存储工具
 * 使用 Capacitor Filesystem 存储生成的视频文件
 */

import { Filesystem, Directory } from '@capacitor/filesystem'
import { Capacitor } from '@capacitor/core'
import { FileSystemDownloadService } from '@/services/filesystem-download'

const VIDEO_DIR = 'chat_videos'

export interface VideoFile {
  url: string
  path: string
  timestamp: number
  messageId?: string
  taskId?: string
}

export class VideoStorage {
  private static instance: VideoStorage

  static getInstance(): VideoStorage {
    if (!VideoStorage.instance) {
      VideoStorage.instance = new VideoStorage()
    }
    return VideoStorage.instance
  }

  private constructor() {}

  /**
   * 初始化视频存储目录
   */
  async initialize(): Promise<void> {
    try {
      await Filesystem.mkdir({
        path: VIDEO_DIR,
        directory: Directory.Data,
        recursive: true
      })
      console.log('🎬 [VideoStorage] 视频目录初始化完成')
    } catch (error) {
      console.warn('⚠️ [VideoStorage] 目录可能已存在:', error)
    }
  }

  /**
   * 保存视频文件并返回文件路径
   */
  async saveVideo(
    videoData: Uint8Array | Blob,
    messageId: string,
    taskId?: string
  ): Promise<string> {
    try {
      const timestamp = Date.now()
      const taskSuffix = taskId ? `_${taskId.substring(0, 8)}` : ''
      const filename = `video_${messageId}${taskSuffix}_${timestamp}.mp4`
      const path = `${VIDEO_DIR}/${filename}`

      // 确保目录存在
      await this.ensureDirectoryExists()

      let base64Data: string

      if (videoData instanceof Uint8Array) {
        // 直接从Uint8Array转换
        base64Data = this.uint8ArrayToBase64(videoData)
      } else {
        // 从Blob转换
        base64Data = await this.blobToBase64(videoData)
      }

      // 保存文件
      await Filesystem.writeFile({
        path,
        data: base64Data,
        directory: Directory.Data
      })

      console.log('💾 [VideoStorage] 视频已保存:', path, '大小:', base64Data.length, 'bytes')
      return path
    } catch (error) {
      console.error('❌ [VideoStorage] 保存视频失败:', error)
      throw error
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(): Promise<void> {
    try {
      await Filesystem.mkdir({
        path: VIDEO_DIR,
        directory: Directory.Data,
        recursive: true
      })
    } catch (error) {
      // 目录可能已存在，忽略错误
      console.log('📁 [VideoStorage] 目录检查:', error)
    }
  }

  /**
   * 从URL下载视频并保存到本地
   * 使用FileSystemDownloadService避免CORS问题
   */
  async saveVideoFromUrl(videoUrl: string, messageId: string, taskId?: string): Promise<string> {
    try {
      console.log('📥 [VideoStorage] 开始下载视频:', videoUrl.substring(0, 50))

      // 使用FileSystemDownloadService下载
      const downloadedFile = await FileSystemDownloadService.downloadFileToFileSystem(
        videoUrl,
        `chat_video_${messageId}` // 使用messageId作为scriptId
      )

      // 复制到我们自己的目录结构
      const timestamp = Date.now()
      const taskSuffix = taskId ? `_${taskId.substring(0, 8)}` : ''
      const filename = `video_${messageId}${taskSuffix}_${timestamp}.mp4`
      const targetPath = `${VIDEO_DIR}/${filename}`

      // 读取下载的文件
      const fileContent = await Filesystem.readFile({
        path: downloadedFile.filePath,
        directory: Directory.Data
      })

      // 保存到我们的目录
      await Filesystem.writeFile({
        path: targetPath,
        data: fileContent.data,
        directory: Directory.Data
      })

      console.log('✅ [VideoStorage] 视频下载并保存完成:', targetPath)
      return targetPath
    } catch (error) {
      console.error('❌ [VideoStorage] 从URL保存视频失败:', error)
      throw error
    }
  }

  /**
   * 获取视频的可用URL
   */
  async getVideoUrl(path: string): Promise<string | null> {
    try {
      console.log('🎬 [VideoStorage] 尝试使用本地视频文件:', path)

      // 在移动设备上，需要读取文件并转换为data URL
      if (Capacitor.isNativePlatform()) {
        try {
          const result = await Filesystem.readFile({
            path,
            directory: Directory.Data
          })

          // 检查返回的数据格式
          if (typeof result.data === 'string') {
            const dataUrl = `data:video/mp4;base64,${result.data}`
            console.log('✅ [VideoStorage] 成功转换为data URL，大小:', dataUrl.length, 'bytes')
            return dataUrl
          } else {
            console.error('❌ [VideoStorage] 文件读取返回格式错误')
            return null
          }
        } catch (readError) {
          console.error('❌ [VideoStorage] 文件读取失败:', readError)
          return null
        }
      } else {
        // Web环境，尝试使用getUri
        try {
          const uriResult = await Filesystem.getUri({
            path,
            directory: Directory.Data
          })
          console.log('✅ [VideoStorage] 使用文件URI:', uriResult.uri)
          return uriResult.uri
        } catch (uriError) {
          console.error('❌ [VideoStorage] 获取URI失败:', uriError)
          return null
        }
      }
    } catch (error) {
      console.error('❌ [VideoStorage] 获取视频URL失败:', error)
      return null
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(path: string): Promise<boolean> {
    try {
      await Filesystem.stat({
        path,
        directory: Directory.Data
      })
      return true
    } catch {
      return false
    }
  }

  /**
   * 根据messageId查找视频文件
   */
  async findVideoByMessageId(messageId: string): Promise<string | null> {
    try {
      const result = await Filesystem.readdir({
        path: VIDEO_DIR,
        directory: Directory.Data
      })

      const videoFile = result.files.find(
        file =>
          file.name.startsWith(`video_${messageId}`) &&
          (file.name.endsWith('.mp4') || file.name.endsWith('.webm'))
      )

      if (videoFile) {
        const path = `${VIDEO_DIR}/${videoFile.name}`
        console.log('🔍 [VideoStorage] 找到视频文件:', path)
        return path
      }

      return null
    } catch (error) {
      console.warn('⚠️ [VideoStorage] 查找视频文件失败:', error)
      return null
    }
  }

  /**
   * 删除视频文件
   */
  async deleteVideo(path: string): Promise<void> {
    try {
      await Filesystem.deleteFile({
        path,
        directory: Directory.Data
      })
      console.log('🗑️ [VideoStorage] 视频已删除:', path)
    } catch (error) {
      console.error('❌ [VideoStorage] 删除视频失败:', error)
      throw error
    }
  }

  /**
   * 将 Uint8Array 转换为 base64 字符串
   */
  private uint8ArrayToBase64(uint8Array: Uint8Array): string {
    const binaryString = Array.from(uint8Array, byte => String.fromCharCode(byte)).join('')
    return btoa(binaryString)
  }

  /**
   * 将 Blob 转换为 base64 字符串
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // 移除 data URL 前缀，只保留 base64 数据
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalFiles: number
    totalSize: number
  }> {
    try {
      const result = await Filesystem.readdir({
        path: VIDEO_DIR,
        directory: Directory.Data
      })

      const videoFiles = result.files.filter(
        file =>
          file.name.startsWith('video_') &&
          (file.name.endsWith('.mp4') || file.name.endsWith('.webm'))
      )

      let totalSize = 0
      for (const file of videoFiles) {
        try {
          const stat = await Filesystem.stat({
            path: `${VIDEO_DIR}/${file.name}`,
            directory: Directory.Data
          })
          totalSize += stat.size || 0
        } catch (error) {
          console.warn('⚠️ [VideoStorage] 获取文件大小失败:', file.name)
        }
      }

      return {
        totalFiles: videoFiles.length,
        totalSize
      }
    } catch (error) {
      console.error('❌ [VideoStorage] 获取存储统计失败:', error)
      return { totalFiles: 0, totalSize: 0 }
    }
  }
}

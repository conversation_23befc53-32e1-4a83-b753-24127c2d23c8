import { useEffect, useState } from 'react';
import { Capacitor } from '@capacitor/core';

// 判断当前是否运行在移动设备上
export const isMobileDevice = () => {
  return Capacitor.isNativePlatform();
};

// 自定义钩子用于获取视口尺寸和设备信息
export function useMobileViewport() {
  const [viewport, setViewport] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
    isNative: isMobileDevice(),
    platform: Capacitor.getPlatform(),
  });

  useEffect(() => {
    // 当视口尺寸变化时更新状态
    const handleResize = () => {
      setViewport({
        width: window.innerWidth,
        height: window.innerHeight,
        isNative: isMobileDevice(),
        platform: Capacitor.getPlatform(),
      });
    };

    window.addEventListener('resize', handleResize);

    // 注意：CSS变量的设置已移至App.tsx中统一处理
    // 避免重复设置相同功能的CSS变量

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return viewport;
}

// 获取移动设备的安全区域（例如刘海屏或底部导航栏的高度）
export function useDeviceSafeArea() {
  const [safeArea, setSafeArea] = useState({
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  });

  useEffect(() => {
    // 获取安全区域数值的函数
    const getSafeAreaValue = (property: string): number => {
      // 创建一个临时元素来获取计算后的CSS环境变量值
      const tempEl = document.createElement('div');
      tempEl.style.paddingTop = `env(${property}, 0px)`;
      document.body.appendChild(tempEl);

      const computed = getComputedStyle(tempEl).paddingTop;
      document.body.removeChild(tempEl);

      return Number.parseInt(computed, 10) || 0;
    };

    // 使用动态方法获取安全区域值
    const updateSafeArea = () => {
      // 只在移动设备上获取安全区域
      if (isMobileDevice() || window.navigator.userAgent.includes('iPhone')) {
        // 直接获取环境变量的值
        const top = getSafeAreaValue('safe-area-inset-top');
        const right = getSafeAreaValue('safe-area-inset-right');
        const bottom = getSafeAreaValue('safe-area-inset-bottom');
        const left = getSafeAreaValue('safe-area-inset-left');

        console.log('SafeArea-原始值:', { top, right, bottom, left });

        // 如果所有值都是0，则在iOS模拟器或某些设备上尝试使用固定值
        if (top === 0 && right === 0 && bottom === 0 && left === 0) {
          const platform = Capacitor.getPlatform();
          if (platform === 'ios') {
            // iOS设备上的默认安全区域估计值
            const isIPhoneWithNotch =
              window.screen.height >= 812 || window.screen.width >= 812;
            setSafeArea({
              top: isIPhoneWithNotch ? 47 : 20, // 刘海屏大约47px，传统iPhone约20px
              right: 0,
              bottom: isIPhoneWithNotch ? 34 : 0, // 刘海屏底部约34px
              left: 0,
            });
            console.log('SafeArea-使用估计值:', {
              top: isIPhoneWithNotch ? 47 : 20,
              bottom: isIPhoneWithNotch ? 34 : 0,
            });
            return;
          }
        }

        setSafeArea({ top, right, bottom, left });
      }
    };

    // 初始化时更新一次
    updateSafeArea();

    // 页面大小变化时更新
    window.addEventListener('resize', updateSafeArea);

    return () => {
      window.removeEventListener('resize', updateSafeArea);
    };
  }, []);

  return safeArea;
}

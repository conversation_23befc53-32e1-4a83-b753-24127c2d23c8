// 导出API客户端
export { apiClient, ApiError } from './client'

// 导出API服务及类型
export {
  apiService,
  type ChatHistory,
  type UserProfile,
  type CharacterData,
  type Role,
  type Message,
  type UIMessage,
  type TextUIPart,
  type UploadResponse,
  // 邀请码相关类型
  referralService,
  type InviteCode,
  type InviteStats,
  type CommissionAccount,
  type CommissionRecord,
  type InvitedUser,
  type WithdrawRequest,
  type WithdrawConfig,
  type ReferralApiResponse,
  type PaginatedResponse
} from './services'

// 导出认证相关API
export * from './auth'

// 导出上传函数
export { uploadFile } from './endpoints/upload'

// 导出语音转文本函数
export {
  speechToText,
  checkApiAvailability as checkSpeechToTextAvailability,
  getSupportedLanguages,
  estimateConversionTime,
  type SpeechToTextResponse,
  type SpeechToTextError,
  type SpeechToTextOptions,
  type ProgressCallback
} from './endpoints/speechToText'

// 导出支付服务
export {
  paymentService,
  type PaymentOrder,
  type PaymentStatus,
  type CreateOrderRequest,
  type SimulatePaymentRequest
} from './services/payment'

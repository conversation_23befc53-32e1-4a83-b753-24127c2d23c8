# 灵犀秘境管理后台

灵犀秘境 AI 女友聊天应用的管理后台系统，基于 React + Ant Design 构建的现代化管理平台。

## 📋 项目概述

这是一个功能完整的管理后台系统，用于管理灵犀秘境应用的各项业务数据，包括用户管理、订单管理、会员系统、积分系统、内容管理、营销管理等核心模块。

## 🛠 技术栈

- **前端框架**: React 19 + TypeScript
- **构建工具**: Vite 7
- **UI 组件库**: Ant Design 5.x
- **路由管理**: React Router DOM 7
- **状态管理**: Zustand
- **HTTP 客户端**: Axios
- **日期处理**: Day.js
- **开发语言**: TypeScript

## 🚀 快速开始

### 环境要求

- Node.js >= 16
- pnpm >= 8

### 安装依赖

```bash
pnpm install
```

### 启动开发服务器

```bash
pnpm dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用

### 构建生产版本

```bash
pnpm build
```

### 预览生产版本

```bash
pnpm preview
```

## 📁 项目结构

```
src/
├── components/          # 通用组件
│   └── Layout/         # 布局组件
│       └── MainLayout.tsx
├── pages/              # 页面组件
│   ├── Auth/           # 认证页面
│   │   └── LoginPage.tsx
│   ├── Dashboard/      # 仪表板
│   │   └── Dashboard.tsx
│   ├── Users/          # 用户管理
│   │   └── UserList.tsx
│   ├── Orders/         # 订单管理
│   │   └── OrderList.tsx
│   ├── Membership/     # 会员管理
│   │   ├── MembershipPlans.tsx
│   │   └── MembershipSubscriptions.tsx
│   ├── Points/         # 积分管理
│   │   ├── PointsPackages.tsx
│   │   └── UserPoints.tsx
│   ├── Marketing/      # 营销管理
│   │   ├── InviteCodes.tsx
│   │   ├── CommissionManagement.tsx
│   │   ├── WithdrawReview.tsx
│   │   └── ActivationCodes.tsx
│   └── Devices/        # 设备管理
│       └── DeviceManagement.tsx
├── services/           # API 服务
│   ├── api.ts          # API 客户端
│   ├── auth.ts         # 认证服务
│   ├── users.ts        # 用户服务
│   ├── membership.ts   # 会员服务
│   ├── points.ts       # 积分服务
│   ├── marketing.ts    # 营销服务
│   ├── activation.ts   # 激活码服务
│   └── devices.ts      # 设备服务
├── stores/             # 状态管理
│   └── auth.ts         # 认证状态
├── types/              # 类型定义
│   └── api.ts          # API 类型
├── constants/          # 常量配置
│   └── index.ts
├── utils/              # 工具函数
├── hooks/              # 自定义 Hooks
├── App.tsx             # 应用根组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

## 🎯 功能模块

### 🔐 认证系统
- [x] 管理员登录/登出
- [x] 权限验证
- [x] 路由守卫
- [x] Token 自动刷新

### 📊 数据概览
- [x] 用户统计数据
- [x] 订单统计数据
- [x] 收入统计数据
- [x] 系统状态监控

### 👥 用户管理
- [x] 用户列表查询
- [x] 高级搜索过滤
- [x] 用户详情查看
- [x] 用户状态管理
- [x] 数据导出功能

### 🛒 订单管理
- [x] 订单列表查询
- [x] 订单状态过滤
- [x] 订单详情查看
- [x] 统计数据展示
- [x] 数据导出功能

### ✅ 已完成功能

#### 会员管理系统
- [x] 会员套餐配置
- [x] 用户订阅管理
- [x] 会员权益管理
- [x] 订阅延长功能
- [x] 统计数据展示

#### 积分管理系统
- [x] 积分套餐配置
- [x] 用户积分查询
- [x] 积分交易记录
- [x] 积分调整功能
- [x] 统计数据展示

#### 营销管理系统
- [x] 邀请码管理
- [x] 佣金账户管理  
- [x] 提现申请审核
- [x] 激活码管理
- [x] 批量操作功能
- [x] 统计数据展示

#### 设备管理系统
- [x] 蓝牙设备管理
- [x] 设备连接监控
- [x] 设备使用统计
- [x] 远程设备控制
- [x] 连接记录追踪

### 🔮 待开发功能

#### 内容管理系统
- [ ] AI 角色管理
- [ ] 剧本库管理
- [ ] 多媒体内容审核

#### 系统设置
- [ ] 全局配置管理
- [ ] 系统参数配置
- [ ] 操作日志查看

## 🔧 开发指南

### API 配置

项目使用代理方式连接后端 API，开发环境默认代理到 `http://localhost:8787`。

如需修改 API 地址，请编辑 `vite.config.ts` 文件：

```typescript
server: {
  proxy: {
    '/api': {
      target: 'your-api-url',
      changeOrigin: true,
    },
  },
}
```

### 环境变量

创建 `.env.local` 文件配置环境变量：

```env
VITE_API_BASE_URL=http://localhost:8787/api
```

### 管理员账号配置

当前系统使用 Supabase 进行认证。要设置管理员账号，请按照以下步骤：

#### 步骤1：在 Supabase Dashboard 创建用户

1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择你的项目
3. 进入 **Authentication** > **Users**
4. 点击 **Add user** 按钮
5. 输入管理员邮箱和密码（建议使用强密码）
6. 点击创建用户

#### 步骤2：设置管理员权限

1. 进入 **SQL Editor**
2. 执行以下 SQL（替换邮箱为你的管理员邮箱）：

```sql
-- 设置用户为管理员
UPDATE auth.users 
SET user_metadata = COALESCE(user_metadata, '{}'::jsonb) || '{"role": "admin", "isAdmin": true}'::jsonb
WHERE email = '<EMAIL>';

-- 验证设置结果
SELECT 
    id, 
    email, 
    user_metadata->>'role' as role,
    (user_metadata->>'isAdmin')::boolean as is_admin
FROM auth.users 
WHERE email = '<EMAIL>';
```

#### 步骤3：更新后端管理员邮箱列表（可选）

在 `apps/backend-service/src/routes/auth.ts` 中更新管理员邮箱列表：

```typescript
const adminEmails = [
  '<EMAIL>',  // 替换为你的管理员邮箱
  // 可以添加多个管理员邮箱
]
```

#### 管理员登录

设置完成后，你可以使用管理员邮箱和密码登录管理后台。系统会：

1. 验证邮箱和密码
2. 检查用户的 `user_metadata` 中的管理员标识
3. 检查是否在管理员邮箱列表中
4. 只有管理员用户才能访问管理后台

**重要提醒**：
- 生产环境必须使用强密码
- 建议启用 Supabase 的多因素认证
- 定期检查和更新管理员权限

### 添加新页面

1. 在 `src/pages/` 下创建页面组件
2. 在 `src/App.tsx` 中添加路由配置
3. 在 `src/constants/index.ts` 中添加菜单项

### 添加新 API 服务

1. 在 `src/types/api.ts` 中定义类型
2. 在 `src/services/` 下创建服务文件
3. 使用 `apiService` 基础类进行 HTTP 请求

## 🎨 UI 设计规范

- 使用 Ant Design 设计规范
- 统一的颜色主题和字体
- 响应式设计支持移动端
- 无障碍访问支持

## 📝 开发规范

- 使用 TypeScript 严格模式
- 遵循 ESLint 代码规范
- 组件采用函数式编程
- 使用 Hooks 管理状态
- API 调用统一错误处理

## 🔨 构建和部署

### 生产构建

```bash
pnpm build
```

构建产物位于 `dist/` 目录。

### Docker 部署

```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

### 静态托管

支持部署到以下平台：
- Vercel
- Netlify
- GitHub Pages
- 阿里云 OSS

## 📄 许可证

Copyright © 2024 灵犀秘境. All rights reserved.

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📞 联系方式

如有问题或建议，请联系开发团队。
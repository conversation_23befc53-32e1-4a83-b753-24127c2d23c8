import React, { useState } from 'react';
import type { Device } from '../types';
import { useTranslation } from 'react-i18next';

// 模拟的设备数据映射
const deviceDatabase: Record<string, Device> = {
  '1234': {
    pic: 'https://cdn.pixabay.com/photo/2015/12/01/20/28/road-1072823_1280.jpg',
    name: '激情震动棒',
    func: [
      {
        name: '抽插',
        key: 'thrust',
        commands: [
          { intensity: 1, command: '6db643ce97fe427ce49c6c' },
          { intensity: 2, command: '6db643ce97fe427ce7075e' },
          { intensity: 3, command: '6db643ce97fe427ce68e4f' },
          { intensity: -1, command: '6db643ce97fe427ce5157d' },
        ],
      },
      {
        name: '吮吸',
        key: 'suction',
        commands: [
          { intensity: 1, command: '6db643ce97fe427ca4982e' },
          { intensity: 2, command: '6db643ce97fe427ca7031c' },
          { intensity: 3, command: '6db643ce97fe427ca68a0d' },
          { intensity: -1, command: '6db643ce97fe427ce5157d' },
        ],
      },
    ],
  },
  '5678': {
    pic: 'https://cdn.pixabay.com/photo/2013/10/02/23/03/mountains-190055_1280.jpg',
    name: '多功能按摩器',
    func: [
      {
        name: '震动',
        key: 'vibrate',
        commands: [
          { intensity: 1, command: '6db643ce97fe427ce49c6c' },
          { intensity: 2, command: '6db643ce97fe427ce7075e' },
          { intensity: 3, command: '6db643ce97fe427ce68e4f' },
          { intensity: -1, command: '6db643ce97fe427ce5157d' },
        ],
      },
      {
        name: '吮吸',
        key: 'suction',
        commands: [
          { intensity: 1, command: '6db643ce97fe427ca4982e' },
          { intensity: 2, command: '6db643ce97fe427ca7031c' },
          { intensity: 3, command: '6db643ce97fe427ca68a0d' },
          { intensity: -1, command: '6db643ce97fe427ce5157d' },
        ],
      },
    ],
  },
  '9012': {
    pic: 'https://cdn.pixabay.com/photo/2013/10/02/23/03/mountains-190055_1280.jpg',
    name: '飞机杯',
    func: [
      {
        name: '震动',
        key: 'vibrate',
        commands: [
          { intensity: 1, command: '6db643ce97fe427ce49c6c' },
          { intensity: 2, command: '6db643ce97fe427ce7075e' },
          { intensity: 3, command: '6db643ce97fe427ce68e4f' },
          { intensity: -1, command: '6db643ce97fe427ce5157d' },
        ],
      },
      {
        name: '吮吸',
        key: 'suction',
        commands: [
          { intensity: 1, command: '6db643ce97fe427ca4982e' },
          { intensity: 2, command: '6db643ce97fe427ca7031c' },
          { intensity: 3, command: '6db643ce97fe427ca13579' },
          { intensity: -1, command: '6db643ce97fe427ce5157d' },
        ],
      },
    ],
  },
};

interface DeviceConnectProps {
  onDeviceConnect: (device: Device) => void;
}

/**
 * 设备连接组件
 * 支持扫描二维码或手动输入设备码
 */
export const DeviceConnect: React.FC<DeviceConnectProps> = ({
  onDeviceConnect,
}) => {
  const { t } = useTranslation('interactive');
  const [deviceCode, setDeviceCode] = useState('');
  const [error, setError] = useState('');
  const [isScanning, setIsScanning] = useState(false);

  // 连接设备
  const connectDevice = () => {
    if (!deviceCode) {
      setError(t('deviceConnect.enterDeviceCodeError'));
       return;
     }

     const device = deviceDatabase[deviceCode];
     if (device) {
       onDeviceConnect(device);
       setError('');
     } else {
       setError(t('deviceConnect.invalidDeviceCodeError'));
    }
  };

  // 模拟扫描二维码
  const handleScan = () => {
    setIsScanning(true);

    // 模拟扫描过程
    setTimeout(() => {
      // 随机选择一个设备码
      const codes = Object.keys(deviceDatabase);
      const randomCode = codes[Math.floor(Math.random() * codes.length)];

      setDeviceCode(randomCode);
      setIsScanning(false);
      onDeviceConnect(deviceDatabase[randomCode]);
    }, 2000);
  };

  return (
    <div className="flex flex-col items-center justify-center p-6 rounded-lg shadow-lg max-w-md mx-auto max-h-[calc(100vh-8rem)] overflow-y-auto">
      <h2 className="text-2xl font-bold text-white mb-6">{t('deviceConnect.title')}</h2>

      {/* 设备码输入 */}
      <div className="w-full mb-6">
        <label
          className="block text-gray-300 text-sm font-bold mb-2"
          htmlFor="deviceCode"
        >
          {t('deviceConnect.inputLabel')}
        </label>
        <div className="flex">
          <input
            type="text"
            value={deviceCode}
            onChange={(e) => setDeviceCode(e.target.value)}
            placeholder={t('deviceConnect.inputPlaceholder')}
            className="flex-1 bg-gray-800 text-white px-4 py-2 rounded-l border border-gray-700 focus:outline-none focus:border-pink-500"
          />
          <button
            type="button"
            onClick={connectDevice}
            className="bg-pink-600 hover:bg-pink-700 text-white font-bold py-2 px-4 rounded-r shrink-0"
          >
            {t('deviceConnect.connectButton')}
          </button>
        </div>
        {error && <p className="text-red-500 text-sm mt-1">{error}</p>}
        <p className="text-gray-400 text-xs mt-2">{t('deviceConnect.exampleCodes')}</p>
      </div>

      {/* 分割线 */}
      <div className="w-full flex items-center justify-center mb-6">
        <div className="flex-1 h-px bg-gray-700" />
        <span className="px-4 text-gray-500 text-sm">{t('deviceConnect.or')}</span>
        <div className="flex-1 h-px bg-gray-700" />
      </div>

      {/* 扫描二维码 */}
      <div className="w-full text-center">
        <button
          type="button"
          onClick={handleScan}
          disabled={isScanning}
          className={`w-full bg-gray-800 hover:bg-gray-700 text-white font-bold p-4 rounded transition-all ${
            isScanning ? 'opacity-50 cursor-not-allowed' : ''
          }`}
        >
          {isScanning ? (
            <div className="flex items-center justify-center">
              <div className="animate-spin rounded-full size-5 border-y-2 border-pink-500 mr-2" />
              <span>{t('deviceConnect.scanning')}</span>
             </div>
           ) : (
             <span>{t('deviceConnect.scanQrCode')}</span>
           )}
         </button>
         <p className="text-gray-400 text-xs mt-2">
           {t('deviceConnect.scanInstructions')}
         </p>
      </div>
    </div>
  );
};

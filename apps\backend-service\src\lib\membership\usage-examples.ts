/**
 * 会员积分系统使用示例
 * 展示在各个具体服务中如何集成积分扣除
 */

import type { Env } from '@/types/env';
import { createServicePointsManager } from './service-points';
import { permissionMiddleware, PermissionType } from '@/middleware/permission';

/**
 * 示例1: TTS服务中的积分扣除
 */
export async function ttsServiceExample(
  env: Env,
  userId: string,
  text: string,
  voiceModel: string
) {
  const pointsManager = createServicePointsManager(env);

  // 扣除积分
  const result = await pointsManager.consumeTTSPoints(userId, {
    textLength: text.length,
    voiceModel,
    taskId: `tts-${Date.now()}`,
    customDescription: `TTS生成语音，文本长度${text.length}字符`,
  });

  if (!result.success) {
    throw new Error(result.error || 'TTS积分扣除失败');
  }

  console.log(`TTS积分扣除成功，剩余积分: ${result.remainingPoints}`);

  // 这里继续执行TTS生成逻辑...
  return {
    success: true,
    remainingPoints: result.remainingPoints,
    taskId: result.transactionId,
  };
}

/**
 * 示例2: 图片生成服务中的积分扣除
 */
export async function imageGenerationServiceExample(
  env: Env,
  userId: string,
  prompt: string,
  options: {
    count?: number;
    highRes?: boolean;
  }
) {
  const pointsManager = createServicePointsManager(env);

  // 扣除积分
  const result = await pointsManager.consumeImageGenerationPoints(userId, {
    imageCount: options.count || 1,
    isHighRes: options.highRes || false,
    generationId: `img-${Date.now()}`,
    customDescription: `生成图片，提示词: ${prompt.substring(0, 50)}...`,
  });

  if (!result.success) {
    throw new Error(result.error || '图片生成积分扣除失败');
  }

  console.log(`图片生成积分扣除成功，剩余积分: ${result.remainingPoints}`);

  // 这里继续执行图片生成逻辑...
  return {
    success: true,
    remainingPoints: result.remainingPoints,
    generationId: result.transactionId,
  };
}

/**
 * 示例3: 在Hono路由中使用权限中间件
 */
export function createTTSRouteWithPermission() {
  return [
    // 使用权限中间件
    permissionMiddleware(PermissionType.VOICE_GENERATION, {
      requireMembership: true,
    }),

    // 路由处理函数
    async (c: any) => {
      const user = c.get('user');
      const { text, voiceModel } = await c.req.json();

      try {
        // 执行TTS服务（已经通过权限检查和积分扣除）
        const result = await ttsServiceExample(c.env, user.id, text, voiceModel);

        return c.json({
          success: true,
          data: result,
        });
      } catch (error) {
        return c.json(
          {
            success: false,
            error: error instanceof Error ? error.message : 'TTS服务失败',
          },
          400
        );
      }
    },
  ];
}

/**
 * 示例4: 聊天服务中的权限检查
 */
export async function chatServiceExample(env: Env, userId: string, message: string) {
  const pointsManager = createServicePointsManager(env);

  // 检查聊天权限（免费用户有每日限制）
  const permission = await pointsManager.checkChatPermission(userId, {
    messageCount: 50, // 假设今日已发送50条消息
  });

  if (!permission.canChat) {
    throw new Error(permission.reason || '聊天权限不足');
  }

  console.log(`用户${permission.isMember ? '会员' : '免费'}聊天权限检查通过`);

  // 这里继续执行聊天逻辑...
  return {
    success: true,
    isMember: permission.isMember,
    canContinue: true,
  };
}

/**
 * 示例5: 角色创建服务中的权限检查
 */
export async function characterCreateServiceExample(env: Env, userId: string, characterData: any) {
  const pointsManager = createServicePointsManager(env);

  // 检查角色创建权限
  const permission = await pointsManager.checkCharacterCreatePermission(userId, {
    currentCharacterCount: 2, // 假设用户已创建2个角色
  });

  if (!permission.canCreate) {
    throw new Error(permission.reason || '角色创建权限不足');
  }

  console.log(`用户可创建角色，限制: ${permission.limit}个`);

  // 这里继续执行角色创建逻辑...
  return {
    success: true,
    limit: permission.limit,
    isMember: permission.isMember,
  };
}

/**
 * 示例6: 剧本购买服务
 */
export async function scriptPurchaseServiceExample(
  env: Env,
  userId: string,
  scriptId: string,
  scriptTitle: string
) {
  const pointsManager = createServicePointsManager(env);

  // 扣除积分购买剧本
  const result = await pointsManager.consumeScriptPurchasePoints(userId, {
    scriptId,
    scriptTitle,
    customDescription: `购买剧本《${scriptTitle}》`,
  });

  if (!result.success) {
    throw new Error(result.error || '剧本购买失败');
  }

  console.log(`剧本购买成功，剩余积分: ${result.remainingPoints}`);

  // 这里添加剧本到用户库的逻辑...
  return {
    success: true,
    remainingPoints: result.remainingPoints,
    purchaseId: result.transactionId,
  };
}

/**
 * 示例7: 批量操作的积分管理
 */
export async function batchImageGenerationExample(
  env: Env,
  userId: string,
  requests: Array<{
    prompt: string;
    count: number;
  }>
) {
  const pointsManager = createServicePointsManager(env);
  const results = [];

  for (const request of requests) {
    try {
      // 为每个请求单独扣除积分
      const result = await pointsManager.consumeImageGenerationPoints(userId, {
        imageCount: request.count,
        generationId: `batch-img-${Date.now()}-${Math.random()}`,
        customDescription: `批量生成图片: ${request.prompt.substring(0, 30)}...`,
      });

      if (!result.success) {
        results.push({
          prompt: request.prompt,
          success: false,
          error: result.error,
        });
        continue;
      }

      // 执行图片生成...
      results.push({
        prompt: request.prompt,
        success: true,
        remainingPoints: result.remainingPoints,
        generationId: result.transactionId,
      });
    } catch (error) {
      results.push({
        prompt: request.prompt,
        success: false,
        error: error instanceof Error ? error.message : '未知错误',
      });
    }
  }

  return results;
}

/**
 * 示例8: 错误处理和积分退还
 */
export async function serviceWithRefundExample(env: Env, userId: string, serviceId: string) {
  const pointsManager = createServicePointsManager(env);

  // 先扣除积分
  const consumeResult = await pointsManager.consumeImageGenerationPoints(userId, {
    generationId: serviceId,
  });

  if (!consumeResult.success) {
    throw new Error(consumeResult.error || '积分扣除失败');
  }

  try {
    // 执行服务逻辑...
    // 假设服务失败了
    throw new Error('服务执行失败');
  } catch (error) {
    // 服务失败，退还积分
    const refundResult = await createServicePointsManager(env);
    // 注意：这里需要通过基础的积分服务来退还
    // 具体实现需要调用 pointsService.refundPoints

    console.log('服务失败，积分已退还');
    throw error;
  }
}

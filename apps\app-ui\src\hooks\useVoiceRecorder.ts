/**
 * 语音录制 Hook
 * 管理录制状态、音频处理和用户交互
 */

import { useState, useCallback, useRef, useEffect } from 'react'
import {
  AudioRecorder,
  type AudioRecorderOptions,
  type AudioRecorderState
} from '../lib/audio/audioRecorder'
import { useAudioPermission } from './useAudioPermission'
import { compressAudio, getAudioInfo } from '../lib/audio/audioUtils'

export type VoiceRecorderStatus =
  | 'idle' // 空闲状态
  | 'initializing' // 初始化中
  | 'recording' // 录制中
  | 'processing' // 处理中
  | 'completed' // 完成
  | 'error' // 错误状态

export interface VoiceRecorderState {
  status: VoiceRecorderStatus
  duration: number
  volume: number
  audioBlob?: Blob
  audioInfo?: {
    duration: number
    size: number
    format: string
    sizeMB: number
  }
  error?: string
  isInitialized: boolean
}

export interface VoiceRecorderOptions extends AudioRecorderOptions {
  autoCompress?: boolean // 自动压缩音频
  onStatusChange?: (status: VoiceRecorderStatus) => void
  onAudioReady?: (audioBlob: Blob) => void
}

export interface VoiceRecorderActions {
  initialize: () => Promise<boolean>
  startRecording: () => Promise<boolean>
  stopRecording: () => Promise<Blob | null>
  pauseRecording: () => void
  resumeRecording: () => void
  cancelRecording: () => void
  reset: () => void
  destroy: () => void
}

export function useVoiceRecorder(
  options: VoiceRecorderOptions = {}
): VoiceRecorderState & VoiceRecorderActions {
  const audioRecorderRef = useRef<AudioRecorder | null>(null)
  const [state, setState] = useState<VoiceRecorderState>({
    status: 'idle',
    duration: 0,
    volume: 0,
    isInitialized: false
  })

  // 解构options以避免依赖数组问题
  const {
    quality = 'medium',
    maxDuration = 60,
    autoCompress = true,
    onStatusChange,
    onAudioReady
  } = options

  const { state: permissionState, requestPermission, isSupported } = useAudioPermission()

  // 状态更新辅助函数
  const updateState = useCallback(
    (updates: Partial<VoiceRecorderState>) => {
      setState(prev => {
        const newState = { ...prev, ...updates }
        // 触发状态变化回调
        if (updates.status && updates.status !== prev.status) {
          onStatusChange?.(updates.status)
        }
        return newState
      })
    },
    [onStatusChange]
  )

  // 设置错误状态
  const setError = useCallback((error: string) => {
    setState(prev => ({
      ...prev,
      status: 'error' as VoiceRecorderStatus,
      error,
      isInitialized: false
    }))
  }, [])

  // 初始化录制器
  const initialize = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      setError('浏览器不支持音频录制功能')
      return false
    }

    if (state.isInitialized && audioRecorderRef.current) {
      return true
    }

    updateState({ status: 'initializing', error: undefined })

    try {
      // 请求权限（如果需要）
      if (permissionState !== 'granted') {
        const granted = await requestPermission()
        if (!granted) {
          setError('需要麦克风权限才能进行语音录制')
          return false
        }
      }

      // 创建录制器
      const recorder = new AudioRecorder({
        quality,
        maxDuration,
        onVolumeChange: volume => {
          updateState({ volume })
        },
        onTimeUpdate: duration => {
          updateState({ duration })
        }
      })

      // 初始化录制器
      await recorder.initialize()
      audioRecorderRef.current = recorder

      updateState({
        status: 'idle',
        isInitialized: true,
        duration: 0,
        volume: 0
      })

      return true
    } catch (error) {
      console.error('初始化录制器失败:', error)
      setError(error instanceof Error ? error.message : '初始化失败')
      return false
    }
  }, [
    isSupported,
    permissionState,
    requestPermission,
    state.isInitialized,
    quality,
    maxDuration,
    updateState,
    setError
  ])

  // 开始录制
  const startRecording = useCallback(async (): Promise<boolean> => {
    if (!audioRecorderRef.current) {
      const initialized = await initialize()
      if (!initialized) return false
    }

    if (state.status === 'recording') {
      console.warn('已在录制中')
      return true
    }

    try {
      await audioRecorderRef.current!.start()
      updateState({
        status: 'recording',
        audioBlob: undefined,
        audioInfo: undefined,
        error: undefined
      })
      return true
    } catch (error) {
      console.error('开始录制失败:', error)
      setError(error instanceof Error ? error.message : '开始录制失败')
      return false
    }
  }, [initialize, state.status, updateState, setError])

  // 停止录制
  const stopRecording = useCallback(async (): Promise<Blob | null> => {
    if (!audioRecorderRef.current || state.status !== 'recording') {
      console.warn('当前未在录制')
      return null
    }

    updateState({ status: 'processing' })

    try {
      let audioBlob = await audioRecorderRef.current.stop()

      // 获取音频信息
      const audioInfo = await getAudioInfo(audioBlob)
      const sizeMB = audioBlob.size / (1024 * 1024)

      // 自动压缩（如果启用且文件较大）
      if (autoCompress && sizeMB > 1) {
        try {
          audioBlob = await compressAudio(audioBlob, quality)
        } catch (error) {
          console.warn('音频压缩失败，使用原始文件:', error)
        }
      }

      const finalAudioInfo = {
        duration: audioInfo.duration,
        size: audioBlob.size,
        format: audioInfo.format,
        sizeMB: audioBlob.size / (1024 * 1024)
      }

      updateState({
        status: 'completed',
        audioBlob,
        audioInfo: finalAudioInfo,
        volume: 0
      })

      // 触发音频就绪回调
      onAudioReady?.(audioBlob)

      return audioBlob
    } catch (error) {
      console.error('停止录制失败:', error)
      setError(error instanceof Error ? error.message : '停止录制失败')
      return null
    }
  }, [state.status, autoCompress, quality, onAudioReady, updateState, setError])

  // 暂停录制
  const pauseRecording = useCallback(() => {
    if (audioRecorderRef.current && state.status === 'recording') {
      audioRecorderRef.current.pause()
      // 注意：状态更新由 AudioRecorder 的事件处理
    }
  }, [state.status])

  // 恢复录制
  const resumeRecording = useCallback(() => {
    if (audioRecorderRef.current && state.status === 'recording') {
      audioRecorderRef.current.resume()
      // 注意：状态更新由 AudioRecorder 的事件处理
    }
  }, [state.status])

  // 取消录制
  const cancelRecording = useCallback(() => {
    if (audioRecorderRef.current) {
      // 强制停止录制器但不保存结果，无论当前状态如何
      audioRecorderRef.current.destroy()
      audioRecorderRef.current = null

      updateState({
        status: 'idle',
        duration: 0,
        volume: 0,
        audioBlob: undefined,
        audioInfo: undefined,
        isInitialized: false
      })
    }
  }, [updateState])

  // 重置状态
  const reset = useCallback(() => {
    updateState({
      status: 'idle',
      duration: 0,
      volume: 0,
      audioBlob: undefined,
      audioInfo: undefined,
      error: undefined
    })
  }, [updateState])

  // 销毁录制器
  const destroy = useCallback(() => {
    if (audioRecorderRef.current) {
      audioRecorderRef.current.destroy()
      audioRecorderRef.current = null
    }

    updateState({
      status: 'idle',
      duration: 0,
      volume: 0,
      audioBlob: undefined,
      audioInfo: undefined,
      error: undefined,
      isInitialized: false
    })
  }, [updateState])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      destroy()
    }
  }, [destroy])

  return {
    // 状态
    status: state.status,
    duration: state.duration,
    volume: state.volume,
    audioBlob: state.audioBlob,
    audioInfo: state.audioInfo,
    error: state.error,
    isInitialized: state.isInitialized,

    // 操作
    initialize,
    startRecording,
    stopRecording,
    pauseRecording,
    resumeRecording,
    cancelRecording,
    reset,
    destroy
  }
}

// 历史记录相关消息
export const historyMessages = {
  zh: {
    both_pagination_params: '只能提供 starting_after 或 ending_before 其中一个参数',
    unauthorized: '未授权',
    user_not_found_in_db: '数据库中未找到用户',
    get_chat_history_failed: '获取聊天记录失败'
  },
  'zh-TW': {
    both_pagination_params: '只能提供 starting_after 或 ending_before 其中一個參數',
    unauthorized: '未授權',
    user_not_found_in_db: '資料庫中未找到使用者',
    get_chat_history_failed: '取得聊天記錄失敗'
  },
  ja: {
    both_pagination_params: 'starting_after または ending_before のいずれか一方のみ提供できます',
    unauthorized: '未認証',
    user_not_found_in_db: 'データベースでユーザーが見つかりません',
    get_chat_history_failed: 'チャット履歴の取得に失敗しました'
  },
  es: {
    both_pagination_params: 'Solo se puede proporcionar uno de starting_after o ending_before',
    unauthorized: 'No autorizado',
    user_not_found_in_db: 'Usuario no encontrado en la base de datos',
    get_chat_history_failed: 'Error al obtener el historial de chat'
  },
  en: {
    both_pagination_params: 'Only one of starting_after or ending_before can be provided',
    unauthorized: 'Unauthorized',
    user_not_found_in_db: 'User not found in database',
    get_chat_history_failed: 'Failed to get chat history'
  }
}

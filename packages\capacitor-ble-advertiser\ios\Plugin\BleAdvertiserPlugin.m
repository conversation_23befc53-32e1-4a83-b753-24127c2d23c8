#import <Capacitor/Capacitor.h>
#import <Foundation/Foundation.h>

// 定义插件类
CAP_PLUGIN(BleAdvertiserPlugin, "BleAdvertiser",
           CAP_PLUGIN_METHOD(initialize, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(isBluetoothEnabled, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(startAdvertising, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(stopAdvertising, CAPPluginReturnPromise);
           CAP_PLUGIN_METHOD(stopAllAdvertising, CAPPluginReturnPromise);)
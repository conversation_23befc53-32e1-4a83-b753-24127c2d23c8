@import './mobile.css';
@import './status-bar.css';
@import './polyfills.css';

@import 'tailwindcss';

@plugin './hero.ts';

@source '../../../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';
@custom-variant dark (&:is(.dark *));
@config '../../tailwind.config.js';

/* 导入 tw-animate-css，但确保不覆盖基础工具类 */
@import 'tw-animate-css';

:root {
  /* 基础颜色系统 - 使用兼容性更好的 RGB 格式 */
  --background: #fdfbfc;
  --foreground: #1a1a1a;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --popover: #ffffff;
  --popover-foreground: #1a1a1a;
  --primary: #e91e63;
  --primary-foreground: #ffffff;
  --secondary: #9c27b0;
  --secondary-foreground: #ffffff;
  --muted: #f5f5f5;
  --muted-foreground: #6b7280;
  --accent: #f5f5f5;
  --accent-foreground: #1a1a1a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e5e7eb;
  --input: #e5e7eb;
  --ring: #e91e63;
  --chart-1: #e91e63;
  --chart-2: #9c27b0;
  --chart-3: #10b981;
  --chart-4: #f59e0b;
  --chart-5: #ef4444;
  --sidebar: #ffffff;
  --sidebar-foreground: #1a1a1a;
  --sidebar-primary: #e91e63;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f5f5f5;
  --sidebar-accent-foreground: #1a1a1a;
  --sidebar-border: #e5e7eb;
  --sidebar-ring: #e91e63;

  /* HeroUI 主题颜色 - 亮色模式 */
  --content1: #ffffff;
  --content1-foreground: #1a1a1a;
  --content2: #f9fafb;
  --content2-foreground: #1a1a1a;
  --content3: #f3f4f6;
  --content3-foreground: #1a1a1a;
  --content4: #e5e7eb;
  --content4-foreground: #1a1a1a;
  --focus: #e91e63;
  --overlay: #000000;

  /* 字体系统 */
  --font-sans: Geist, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-serif: Geist, Georgia, serif;
  --font-mono: 'Geist Mono', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas,
    'Courier New', monospace;

  /* 设计系统 */
  --radius: 0.5rem;
  --shadow-2xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 1px 0px 0px hsl(0 0% 0% / 0.1);
  --shadow-sm: 0px 1px 0px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0px 1px 0px 0px hsl(0 0% 0% / 0.1), 0px 1px 2px -1px hsl(0 0% 0% / 0.06);
  --shadow-md: 0px 1px 0px 0px hsl(0 0% 0% / 0.1), 0px 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg: 0px 1px 0px 0px hsl(0 0% 0% / 0.1), 0px 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.1), 0px 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0px 1px 0px 0px hsl(0 0% 0% / 0.25);
}

.dark {
  /* 深色模式 - 使用兼容性更好的 RGB 格式 */
  --background: #121521;
  --foreground: #ffffff;
  --card: #252237;
  --card-foreground: #ffffff;
  --popover: #2d2a41;
  --popover-foreground: #ffffff;
  --primary: #e23f9d;
  --primary-foreground: #ffffff;
  --secondary: #ba68c8;
  --secondary-foreground: #ffffff;
  --muted: #2d2a41;
  --muted-foreground: #a1a1aa;
  --accent: #3b3752;
  --accent-foreground: #ffffff;
  --destructive: #f87171;
  --destructive-foreground: #ffffff;
  --border: #3b3752;
  --input: #52495e;
  --ring: #e23f9d;
  --chart-1: #e23f9d;
  --chart-2: #ba68c8;
  --chart-3: #4ade80;
  --chart-4: #fbbf24;
  --chart-5: #f87171;
  --sidebar: #252237;
  --sidebar-foreground: #ffffff;
  --sidebar-primary: #e23f9d;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #2d2a41;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #3b3752;
  --sidebar-ring: #e23f9d;

  /* HeroUI 主题颜色 - 深色模式 */
  --content1: #3d2a42;
  --content1-foreground: #ffffff;
  --content2: #503a56;
  --content2-foreground: #ffffff;
  --content3: #634a6a;
  --content3-foreground: #ffffff;
  --content4: #765a7e;
  --content4-foreground: #ffffff;
  --focus: #e23f9d;
  --overlay: #ffffff;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    letter-spacing: var(--tracking-normal, normal);
  }
}

/* 自定义动画 */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px);
    opacity: 1;
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.bg-button-primary {
  background: linear-gradient(156deg, #f734aa 70%, #7e00ff 100%);
}

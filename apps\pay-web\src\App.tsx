/**
 * 支付页面主应用
 */

import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router'
import { HeroUIProvider } from '@heroui/react'
import PaymentStart from '@/pages/PaymentStart'
import PaymentResult from '@/pages/PaymentResult'

function App() {
  return (
    <HeroUIProvider>
      <Router>
        <Routes>
          <Route path="/pay/start" element={<PaymentStart />} />
          <Route path="/pay/result" element={<PaymentResult />} />
          <Route path="/" element={<Navigate to="/pay/start" replace />} />
          <Route path="*" element={<Navigate to="/pay/start" replace />} />
        </Routes>
      </Router>
    </HeroUIProvider>
  )
}

export default App

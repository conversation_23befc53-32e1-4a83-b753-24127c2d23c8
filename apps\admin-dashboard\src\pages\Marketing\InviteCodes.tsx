import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  InputNumber,
  DatePicker,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Badge
} from 'antd'
import {
  PlusOutlined,
  EyeOutlined,
  StopOutlined,
  CheckCircleOutlined,
  GiftOutlined,
  UserAddOutlined,
  SearchOutlined,
  ExportOutlined,
  CopyOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { InviteCode, User } from '@/types/api'
import type { InviteCodeParams } from '@/services/marketing'
import { marketingService } from '@/services/marketing'
import { userService } from '@/services/users'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title } = Typography
const { TextArea } = Input

const InviteCodes: React.FC = () => {
  const [codes, setCodes] = useState<InviteCode[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [batchModalVisible, setBatchModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [batchForm] = Form.useForm()
  const [users, setUsers] = useState<User[]>([])
  const [userLoading, setUserLoading] = useState(false)

  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    isActive: undefined as boolean | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    totalCodes: 0,
    activeCodes: 0,
    usedCodes: 0,
    totalInvites: 0,
    todayInvites: 0
  })

  useEffect(() => {
    loadInviteCodes()
    loadStats()
  }, [currentPage, pageSize, searchParams])

  const loadInviteCodes = async () => {
    try {
      setLoading(true)

      const response = await marketingService.getInviteCodes({
        page: currentPage,
        pageSize,
        isActive: searchParams.isActive,
        keyword: searchParams.keyword || undefined
      })

      if (response.success && response.data) {
        setCodes(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取邀请码列表失败')
      }
    } catch (error) {
      console.error('获取邀请码列表失败:', error)
      message.error('获取邀请码列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await marketingService.getInviteCodeStats()

      if (response.success && response.data) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const loadUsers = async (keyword?: string) => {
    try {
      setUserLoading(true)
      const response = await userService.getUsers({
        page: 1,
        pageSize: 50,
        keyword
      })

      if (response.success && response.data) {
        setUsers(response.data.data)
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
    } finally {
      setUserLoading(false)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      isActive: undefined
    })
    setCurrentPage(1)
  }

  const handleGenerate = () => {
    form.resetFields()
    setModalVisible(true)
    loadUsers() // 加载用户列表供选择
  }

  const handleBatchGenerate = () => {
    batchForm.resetFields()
    setBatchModalVisible(true)
    loadUsers() // 加载用户列表供选择
  }

  const handleGenerateSubmit = async (values: InviteCodeParams) => {
    try {
      const response = await marketingService.generateInviteCode(values)

      if (response.success) {
        message.success('邀请码生成成功')
        setModalVisible(false)
        loadInviteCodes()
        loadStats()
      } else {
        message.error(response.message || '生成失败')
      }
    } catch (error) {
      console.error('生成邀请码失败:', error)
      message.error('生成失败')
    }
  }

  const handleBatchGenerateSubmit = async (values: InviteCodeParams & { count: number }) => {
    try {
      const response = await marketingService.batchGenerateInviteCodes(values)

      if (response.success) {
        message.success(`批量生成${values.count}个邀请码成功`)
        setBatchModalVisible(false)
        loadInviteCodes()
        loadStats()
      } else {
        message.error(response.message || '批量生成失败')
      }
    } catch (error) {
      console.error('批量生成邀请码失败:', error)
      message.error('批量生成失败')
    }
  }

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = isActive
        ? await marketingService.enableInviteCode(id)
        : await marketingService.disableInviteCode(id)

      if (response.success) {
        message.success(isActive ? '邀请码已启用' : '邀请码已禁用')
        loadInviteCodes()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('切换邀请码状态失败:', error)
      message.error('操作失败')
    }
  }

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code)
    message.success('邀请码已复制到剪贴板')
  }

  const handleViewDetail = (inviteCode: InviteCode) => {
    Modal.info({
      title: '邀请码详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <strong>邀请码:</strong> {inviteCode.code}
            </div>
            <div>
              <strong>创建者:</strong> {inviteCode.userId}
            </div>
            <div>
              <strong>最大使用次数:</strong> {inviteCode.maxUses || '无限制'}
            </div>
            <div>
              <strong>已使用次数:</strong> {inviteCode.usedCount}
            </div>
            <div>
              <strong>剩余次数:</strong>{' '}
              {inviteCode.maxUses ? inviteCode.maxUses - inviteCode.usedCount : '无限制'}
            </div>
            <div>
              <strong>过期时间:</strong>{' '}
              {inviteCode.expiresAt
                ? dayjs(inviteCode.expiresAt).format('YYYY-MM-DD HH:mm:ss')
                : '永不过期'}
            </div>
            <div>
              <strong>状态:</strong>{' '}
              {inviteCode.isActive ? <Tag color="green">启用</Tag> : <Tag color="red">禁用</Tag>}
            </div>
            <div>
              <strong>创建时间:</strong> {dayjs(inviteCode.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </Space>
        </div>
      )
    })
  }

  const columns: ColumnsType<InviteCode> = [
    {
      title: '邀请码',
      dataIndex: 'code',
      render: code => (
        <Space>
          <span style={{ fontFamily: 'monospace', fontWeight: 500 }}>{code}</span>
          <Tooltip title="复制邀请码">
            <Button
              type="link"
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyCode(code)}
            />
          </Tooltip>
        </Space>
      )
    },
    {
      title: '创建者',
      dataIndex: 'userId',
      render: userId => (userId ? `用户${userId.slice(-3)}` : '未知用户')
    },
    {
      title: '使用情况',
      key: 'usage',
      render: (_, record) => (
        <div>
          <div>
            <Badge count={record.usedCount} style={{ backgroundColor: '#52c41a' }} />
            <span style={{ marginLeft: 8 }}>{record.maxUses ? `/ ${record.maxUses}` : '/ ∞'}</span>
          </div>
          <div style={{ color: '#999', fontSize: '12px' }}>
            {record.maxUses && record.usedCount >= record.maxUses ? '已用完' : '可用'}
          </div>
        </div>
      )
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      render: expiresAt => {
        if (!expiresAt) return <Tag color="blue">永不过期</Tag>

        const expireDate = dayjs(expiresAt)
        const now = dayjs()
        const isExpired = expireDate.isBefore(now)
        const daysLeft = expireDate.diff(now, 'day')

        return (
          <div>
            <div>{expireDate.format('YYYY-MM-DD')}</div>
            {!isExpired && (
              <div
                style={{
                  color: daysLeft <= 3 ? '#f50' : daysLeft <= 7 ? '#fa8c16' : '#52c41a',
                  fontSize: '12px'
                }}
              >
                {daysLeft}天后过期
              </div>
            )}
            {isExpired && <Tag color="red">已过期</Tag>}
          </div>
        )
      }
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: isActive => <Tag color={isActive ? 'green' : 'red'}>{isActive ? '启用' : '禁用'}</Tag>
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)} />
          </Tooltip>
          {record.isActive ? (
            <Popconfirm
              title="确定禁用这个邀请码吗？"
              onConfirm={() => handleToggleStatus(record.id, false)}
            >
              <Tooltip title="禁用">
                <Button type="link" danger icon={<StopOutlined />} />
              </Tooltip>
            </Popconfirm>
          ) : (
            <Tooltip title="启用">
              <Button
                type="link"
                icon={<CheckCircleOutlined />}
                onClick={() => handleToggleStatus(record.id, true)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        邀请码管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={5}>
          <Card>
            <Statistic
              title="总邀请码数"
              value={stats.totalCodes}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="有效邀请码"
              value={stats.activeCodes}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic title="已使用" value={stats.usedCodes} valueStyle={{ color: '#722ed1' }} />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="总邀请数"
              value={stats.totalInvites}
              prefix={<UserAddOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="今日邀请"
              value={stats.todayInvites}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索邀请码"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={value => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button icon={<ExportOutlined />}>导出</Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleGenerate}>
            生成邀请码
          </Button>

          <Button icon={<PlusOutlined />} onClick={handleBatchGenerate}>
            批量生成
          </Button>
        </Space>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={codes}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 生成邀请码模态框 */}
      <Modal
        title="生成邀请码"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form form={form} layout="vertical" onFinish={handleGenerateSubmit}>
          <Form.Item
            name="userId"
            label="创建用户"
            rules={[{ required: true, message: '请选择创建用户' }]}
          >
            <Select
              showSearch
              placeholder="选择用户"
              loading={userLoading}
              filterOption={false}
              onSearch={loadUsers}
              onFocus={() => loadUsers()}
            >
              {users.map(user => (
                <Select.Option key={user.id} value={user.id}>
                  {user.email}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item name="maxUses" label="最大使用次数" extra="留空则无限制">
            <InputNumber style={{ width: '100%' }} min={1} max={10000} placeholder="如：100" />
          </Form.Item>

          <Form.Item name="expiresAt" label="过期时间" extra="留空则永不过期">
            <DatePicker style={{ width: '100%' }} showTime placeholder="选择过期时间" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="邀请码用途说明..." />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                生成
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量生成模态框 */}
      <Modal
        title="批量生成邀请码"
        open={batchModalVisible}
        onCancel={() => setBatchModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form form={batchForm} layout="vertical" onFinish={handleBatchGenerateSubmit}>
          <Form.Item
            name="userId"
            label="创建用户"
            rules={[{ required: true, message: '请选择创建用户' }]}
          >
            <Select
              showSearch
              placeholder="选择用户"
              loading={userLoading}
              filterOption={false}
              onSearch={loadUsers}
              onFocus={() => loadUsers()}
            >
              {users.map(user => (
                <Select.Option key={user.id} value={user.id}>
                  {user.email}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="count"
            label="生成数量"
            rules={[{ required: true, message: '请输入生成数量' }]}
          >
            <InputNumber style={{ width: '100%' }} min={1} max={1000} placeholder="如：50" />
          </Form.Item>

          <Form.Item name="maxUses" label="每个邀请码最大使用次数" extra="留空则无限制">
            <InputNumber style={{ width: '100%' }} min={1} max={10000} placeholder="如：10" />
          </Form.Item>

          <Form.Item name="expiresAt" label="过期时间" extra="留空则永不过期">
            <DatePicker style={{ width: '100%' }} showTime placeholder="选择过期时间" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <TextArea rows={3} placeholder="批量邀请码用途说明..." />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setBatchModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                批量生成
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default InviteCodes

# ElevenLabs TTS2 服务集成

## 概述

本文档描述了如何将 ElevenLabs TTS 服务集成到应用中，替代之前效果不佳的 Fish Audio 服务。

## 后端实现

### 1. ElevenLabs 服务 (`elevenlabs-service.ts`)

- **位置**: `apps/backend-service/src/lib/tts/elevenlabs-service.ts`
- **功能**:
  - 使用 ElevenLabs API 进行文本转语音
  - 同步生成音频，无需任务状态管理
  - 支持音频文件上传到 R2 存储
  - 自动更新消息附件

### 2. TTS2 路由 (`tts2.ts`)

- **位置**: `apps/backend-service/src/routes/tts2.ts`
- **端点**: `/api/tts2/generate`
- **功能**:
  - 接收文本和声音参数
  - 调用 ElevenLabs 服务生成音频
  - 返回音频 URL

### 3. 环境配置

需要在环境变量中添加：

```
ELEVENLABS_API_KEY=your_elevenlabs_api_key
```

## 前端实现

### 1. API 服务更新

- **文件**: `apps/app-ui/src/api/services/tts.ts`
- **更改**: 将所有 API 调用从 `/api/tts/*` 改为 `/api/tts2/*`

### 2. 声音选择组件

- **文件**: `apps/app-ui/src/components/character-creator/steps/clothing-voice.tsx`
- **新功能**:
  - 点击声音卡片直接播放示例音频
  - 显示播放状态指示器
  - 有示例音频的声音显示音量图标
  - 组件卸载时自动停止音频播放

### 3. 音频播放钩子

- **文件**: `apps/app-ui/src/hooks/use-audio-generation.ts`
- **更新**: 使用 ElevenLabs 服务生成音频

## 声音模型映射

系统支持旧版本声音字符串到 ElevenLabs modelId 的映射：

```typescript
const LEGACY_VOICE_MAPPING = {
  soft: 'alloy', // 柔和的
  deep: 'echo', // 低沉的
  sweet: 'fable', // 甜美的
  husky: 'onyx', // 沙哑的
  energetic: 'nova', // 有活力的
  seductive: 'shimmer', // 诱惑的
  childish: 'fable', // 稚嫩的
  mature: 'echo' // 成熟的
}
```

## 使用流程

1. 用户在角色创建页面选择声音
2. 点击声音卡片播放示例音频
3. 在聊天中生成音频时，使用选择的声音模型
4. ElevenLabs 服务生成高质量音频
5. 音频上传到 R2 存储并返回 URL

## 技术特点

- **同步处理**: 无需复杂的任务状态管理
- **高质量音频**: 使用 ElevenLabs 先进的 TTS 技术
- **缓存机制**: 前端缓存声音模型数据
- **向后兼容**: 支持旧版本声音配置
- **用户体验**: 即时播放示例音频预览

## API 文档

### 生成音频

```http
POST /api/tts2/generate
Content-Type: application/json

{
  "text": "要转换的文本",
  "voice": "alloy",
  "messageId": "可选的消息ID",
  "chatId": "可选的聊天ID"
}
```

**响应**:

```json
{
  "success": true,
  "data": {
    "audioUrl": "https://r2.example.com/audio/xxx.mp3",
    "status": "completed"
  }
}
```

## 注意事项

1. 确保 ElevenLabs API Key 配置正确
2. R2 存储配置必须正确设置
3. 声音模型 ID 必须是有效的 ElevenLabs voice_id
4. 文本长度限制为 5000 字符
5. 组件卸载时会自动停止音频播放

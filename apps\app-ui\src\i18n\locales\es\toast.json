{"api": {"request_failed": "Solicitud API fallida", "unknown_error": "Error API desconocido", "unauthorized": "Sesión expirada", "unauthorized_description": "Inicia sesión nuevamente para continuar", "forbidden": "Acceso denegado", "forbidden_description": "No tienes permisos para realizar esta acción", "not_found": "Recurso no encontrado", "not_found_description": "El recurso solicitado no fue encontrado", "server_error": "Error del servidor", "server_error_description": "Servidor temporalmente no disponible, inténtalo más tarde", "network_error": "Error de conexión de red", "network_error_description": "Verifica tu conexión de red e inténtalo de nuevo", "timeout_error": "Tiempo de espera agotado", "timeout_error_description": "La solicitud tardó demasiado en procesarse, inténtalo de nuevo", "validation_error": "Error de validación de datos", "validation_error_description": "Verifica el formato de los datos ingresados", "rate_limit": "<PERSON><PERSON><PERSON><PERSON> solicitudes", "rate_limit_description": "Inténtalo más tarde"}, "avatar": {"select_image": "Selecciona un archivo de imagen", "size_limit": "El tamaño de la imagen no puede exceder 1MB", "uploading": "Subiendo...", "upload_avatar": "Subir avatar", "support_format": "Soporta formatos JPG, PNG,", "size_limit_note": "tamaño no mayor a 1MB"}, "permission": {"check_failed": "Error al verificar permisos", "check_failed_description": "El sistema no puede verificar tus permisos temporalmente, inténtalo más tarde", "points_insufficient": "Puntos insuficientes", "points_required": "Usar {{featureName}} requiere {{points}} puntos", "member_only": "Función exclusiva para miembros", "member_only_description": "{{featureName}} es una función exclusiva para miembros", "usage_limit": "Límite de uso alcanzado", "insufficient": "Permisos insuficientes"}, "user": {"profile_updated": "Perfil personal actualizado exitosamente", "profile_update_failed": "<PERSON><PERSON>r al actualizar perfil personal", "info_load_failed": "Error al cargar información del usuario", "status_load_failed": "Error al cargar estado del usuario"}, "membership": {"subscription_created": "Suscripción creada exitosamente", "subscription_failed": "Error al crear suscripción", "points_consumed": "Puntos consumidos exitosamente", "points_consume_failed": "Error al consumir puntos", "insufficient_points": "Puntos insuficientes", "member_required": "Se requiere membresía"}, "points": {"package_not_found": "Paquete de puntos no encontrado", "package_inactive": "<PERSON><PERSON>e de puntos ya no disponible", "order_create_failed": "Error al crear pedido"}}
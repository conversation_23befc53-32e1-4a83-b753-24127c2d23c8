import { useEffect, type ReactNode } from 'react'
import { useNavigate, Outlet } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { RoleStoreProvider } from '@/stores/role-store'

interface AuthedLayoutProps {
  children?: ReactNode
}

/**
 * 鉴权布局
 * 用于处理登录状态检查和重定向
 * 在用户认证成功后提供角色状态管理
 */
export function AuthedLayout({ children }: AuthedLayoutProps) {
  const { status, user, session } = useAuth()
  const navigate = useNavigate()

  useEffect(() => {
    console.log('AuthedLayout-当前认证状态:', status)
    console.log('AuthedLayout-用户信息:', user)
    console.log('AuthedLayout-会话信息:', session)
  }, [status, user, session])

  // 重定向未登录用户到登录页面
  useEffect(() => {
    if (status === 'unauthenticated') {
      console.log('AuthedLayout-检测到未认证状态，重定向到登录页面')
      navigate('/login')
    } else if (status === 'authenticated') {
      console.log('AuthedLayout-认证成功，用户:', user?.email)
    }
  }, [status, navigate, user])

  // 如果正在加载会话信息，显示加载状态
  if (status === 'loading') {
    console.log('AuthedLayout-显示加载状态')
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="animate-spin rounded-full size-8 border-y-2 border-primary" />
      </div>
    )
  }

  // 如果未登录，不显示内容
  if (status === 'unauthenticated') {
    console.log('AuthedLayout-未认证，不显示内容')
    return null
  }

  console.log('AuthedLayout-显示认证后内容')

  // 认证成功后，提供角色状态管理并渲染内容
  const content = children ? <>{children}</> : <Outlet />

  return <RoleStoreProvider>{content}</RoleStoreProvider>
}

import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { SupportedLanguage } from '@/i18n/config'
import type { Env } from '@/types/env'
import type { Context } from 'hono'
import { ChatHandlers } from '@/lib/chat/handlers'
import { createLangChainStreamResponse } from '@/lib/langchain/stream/stream-response'
import { validateLangChainEnv } from '@/lib/langchain/integration/hono-adapter'
import { getMessagesByChatId } from '@/lib/db/queries/chat'
import { z } from 'zod'

// 获取国际化函数的辅助函数
function getI18n(
  c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>
): (key: string, params?: Record<string, string | number>) => string {
  return c.get('t')
}

const chatv2 = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// ==================== LangChain 系统状态检查 ====================

// ==================== 获取聊天详情 ====================

chatv2.get('/conversations/:id', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const chatId = c.req.param('id')
    const env = c.env
    const t = getI18n(c)

    if (!user) {
      return c.json({ success: false, message: t('user_not_found') }, 401)
    }

    // 检查 LangChain 系统可用性
    if (!validateLangChainEnv(env)) {
      return c.json(
        {
          success: false,
          message: t('system_config_incomplete')
        },
        503
      )
    }

    const handlers = new ChatHandlers(env)
    const result = await handlers.getConversation(user.id, chatId)

    return c.json(
      {
        success: true,
        data: {
          ...result,
          meta: { engine: 'pleasurehub', version: '1.0.0' }
        }
      },
      200
    )
  } catch (error) {
    console.error('获取聊天详情失败:', error)
    const t = getI18n(c)
    const statusCode =
      (error as Error).message.includes('不存在') ||
      (error as Error).message.includes('does not exist')
        ? 404
        : (error as Error).message.includes('无权限') ||
          (error as Error).message.includes('permission')
        ? 403
        : 500
    return c.json(
      { success: false, message: (error as Error).message || t('chat_get_failed') },
      statusCode
    )
  }
})

// ==================== 获取聊天消息 ====================

chatv2.get('/conversations/:id/messages', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const chatId = c.req.param('id')
    const env = c.env
    const t = getI18n(c)

    if (!user) {
      return c.json({ success: false, message: t('user.info_not_found') }, 401)
    }

    // 检查 LangChain 系统可用性
    if (!validateLangChainEnv(env)) {
      return c.json(
        {
          success: false,
          message: t('system_config_incomplete')
        },
        503
      )
    }

    try {
      // 验证用户对聊天的访问权限
      const handlers = new ChatHandlers(env)
      await handlers.validateChatAccess(user.id, chatId, false)

      // 直接查询消息
      const messages = await getMessagesByChatId(env, { id: chatId })

      return c.json(
        {
          success: true,
          data: {
            messages,
            chatExists: true // 明确标识聊天存在
          },
          meta: { engine: 'pleasurehub', version: '1.0.0' }
        },
        200
      )
    } catch (error) {
      // 如果聊天不存在或无权限访问，返回空数据而不是报错
      if (
        (error as Error).message.includes('不存在') ||
        (error as Error).message.includes('无权限')
      ) {
        console.log(`ℹ️ ${t('chat_access_denied')} - chatId: ${chatId}`)

        return c.json(
          {
            success: true,
            data: {
              messages: [], // 返回空消息数组
              chatExists: false // 明确标识聊天不存在
            },
            meta: { engine: 'pleasurehub', version: '1.0.0' }
          },
          200
        )
      }

      // 其他错误继续抛出
      throw error
    }
  } catch (error) {
    console.error('获取聊天消息失败:', error)
    const t = getI18n(c)
    return c.json(
      { success: false, message: (error as Error).message || t('chat_messages_get_failed') },
      500
    )
  }
})

// ==================== 删除聊天 ====================

chatv2.delete('/conversations/:id', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const chatId = c.req.param('id')
    const env = c.env
    const t = getI18n(c)

    if (!user) {
      return c.json({ success: false, message: t('user_not_found') }, 401)
    }

    // 检查 LangChain 系统可用性
    if (!validateLangChainEnv(env)) {
      return c.json(
        {
          success: false,
          message: t('langchain_config_incomplete')
        },
        503
      )
    }

    const handlers = new ChatHandlers(env)
    await handlers.deleteConversation(user.id, chatId)

    return c.json({ success: true, message: t('chat_delete_success') }, 200)
  } catch (error) {
    console.error('删除聊天失败:', error)
    const t = getI18n(c)
    const statusCode =
      (error as Error).message.includes('不存在') ||
      (error as Error).message.includes('does not exist')
        ? 404
        : (error as Error).message.includes('无权限') ||
          (error as Error).message.includes('permission')
        ? 403
        : 500
    return c.json(
      { success: false, message: (error as Error).message || t('chat_delete_failed') },
      statusCode
    )
  }
})

// ==================== 纯 LangChain 流式聊天 API ====================

// 纯 LangChain 流式请求验证
const langchainStreamRequestSchema = z.object({
  chatId: z.string(),
  message: z.object({
    id: z.string(),
    role: z.enum(['user', 'assistant']),
    content: z.string(),
    createdAt: z.string().or(z.date()),
    parts: z
      .array(
        z.object({
          type: z.string(),
          text: z.string().optional()
        })
      )
      .optional(),
    attachments: z
      .array(
        z.object({
          url: z.string(),
          name: z.string(),
          contentType: z.string()
        })
      )
      .optional()
  }),
  // 🚀 新增：让前端传递这些信息，避免数据库查询
  userInfo: z
    .object({
      id: z.string(),
      email: z.string().optional(),
      nickname: z.string().optional(),
      gender: z.string().optional()
    })
    .optional(),

  // 聊天是否已存在
  chatExists: z.boolean().optional(),
  // 🚀 新增：角色信息（前端传递）
  characterInfo: z
    .object({
      id: z.string(),
      name: z.string(),
      gender: z.enum(['male', 'female', 'other']),
      age: z.string().optional(),
      relationship: z.string().optional(),
      ethnicity: z.string().optional(),
      eyeColor: z.string().optional(),
      hairStyle: z.string().optional(),
      hairColor: z.string().optional(),
      bodyType: z.string().optional(),
      breastSize: z.string().optional(),
      buttSize: z.string().optional(),
      personality: z.string().optional(),
      clothing: z.string().optional(),
      voice: z.string().optional(),
      imageUrl: z.string().optional()
    })
    .optional()
})

// 纯 LangChain 流式聊天 API
chatv2.post(
  '/stream',
  authMiddleware,
  languageMiddleware,
  zValidator('json', langchainStreamRequestSchema),
  async c => {
    try {
      const user = c.get('user')
      const data = c.req.valid('json')
      const env = c.env
      const t = getI18n(c)

      if (!user) {
        return c.json({ success: false, message: t('user_not_found') }, 401)
      }

      // 检查 LangChain 系统可用性
      if (!validateLangChainEnv(env)) {
        return c.json(
          {
            success: false,
            message: t('system_config_incomplete')
          },
          503
        )
      }

      const { chatId, message, userInfo, chatExists, characterInfo } = data

      // 从查询参数获取角色ID
      const roleId = c.req.query('role')

      // 🚀 优化：如果前端传递了用户信息，直接使用，避免数据库查询
      let dbUser, userProfile
      if (userInfo) {
        // 使用前端传递的用户信息
        dbUser = { id: userInfo.id }
        userProfile = {
          nickname: userInfo.nickname,
          gender: userInfo.gender
        }
      } else {
        // 回退到数据库查询
        const handlers = new ChatHandlers(env)
        const result = await handlers.validateChatAccess(user.id, chatId, true, roleId, {
          content: message.content,
          parts: message.parts
        })
        dbUser = result.dbUser
        userProfile = result.userProfile
      }

      // 🚀 优化：简化聊天验证，如果前端说聊天存在就相信
      let chatData
      if (chatExists === false || !chatExists) {
        // 需要创建新聊天 - 使用 validateChatAccess 确保使用正确的 chatId
        const handlers = new ChatHandlers(env)
        const result = await handlers.validateChatAccess(dbUser.id, chatId, true, roleId, {
          content: message.content,
          parts: message.parts
        })
        chatData = result.chatData
      } else {
        // 聊天已存在，直接使用
        chatData = { characterId: roleId }
      }

      // 🚀 优化：直接开始流式响应，使用前端传递的历史消息和角色信息
      const streamResponse = await createLangChainStreamResponse(env, {
        chatId,
        message: {
          id: message.id,
          role: 'user',
          content: message.content,
          parts: message.parts?.map(part => ({
            type: 'text' as const,
            text:
              part.text || part.type === 'text'
                ? (part as any).text || message.content
                : message.content
          })) || [{ type: 'text' as const, text: message.content }],
          attachments: message.attachments || [],
          createdAt: new Date(message.createdAt)
        },
        userId: dbUser.id,
        userType: 'regular',
        username: userProfile?.nickname || user.email?.split('@')[0] || t('user.default_name'),
        userGender: userProfile?.gender ?? undefined,
        characterId: chatData.characterId || undefined,
        character: characterInfo
          ? {
              id: characterInfo.id,
              name: characterInfo.name,
              gender: characterInfo.gender,
              age: characterInfo.age || '',
              relationship: characterInfo.relationship || '',
              ethnicity: characterInfo.ethnicity || '',
              eyeColor: characterInfo.eyeColor || '',
              hairStyle: characterInfo.hairStyle || '',
              hairColor: characterInfo.hairColor || '',
              bodyType: characterInfo.bodyType || '',
              breastSize: characterInfo.breastSize || '',
              buttSize: characterInfo.buttSize || '',
              personality: characterInfo.personality || '',
              clothing: characterInfo.clothing || '',
              voice: characterInfo.voice || '',
              imageUrl: characterInfo.imageUrl || ''
            }
          : undefined, // 🚀 转换为 CharacterType 格式
        isNewChat: chatExists === false || !chatExists, // 根据 chatExists 参数判断是否为新聊天
        language: c.get('language') // 传递用户语言
      })

      return streamResponse
    } catch (error) {
      console.error('LangChain 流式聊天 API 失败:', error)
      const t = getI18n(c)
      const statusCode =
        (error as Error).message.includes('不存在') ||
        (error as Error).message.includes('does not exist')
          ? 404
          : (error as Error).message.includes('无权限') ||
            (error as Error).message.includes('permission')
          ? 403
          : (error as Error).message.includes('上限') || (error as Error).message.includes('limit')
          ? 429
          : 500
      return c.json(
        { success: false, message: (error as Error).message || t('langchain_stream_failed') },
        statusCode
      )
    }
  }
)

export default chatv2

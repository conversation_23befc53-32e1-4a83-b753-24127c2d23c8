import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import {
  getUserProfile,
  getUserBySupabaseId,
  getUserActiveSubscription,
  getUserPoints,
  createUser,
  createUserProfile,
  initializeNewUserPointsAsync
} from '@/lib/db/queries'
import { createSupabaseServiceClient } from '@/lib/supabase'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { TABLE_NAMES } from '@/lib/db/supabase-types'

const adminUsers = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata = supabaseUser.user_metadata || supabaseUser.raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// 查询参数验证
const userListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  gender: z.string().optional(),
  isEmailVerified: z
    .string()
    .transform(val => val === 'true')
    .optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

// ==================== 获取用户统计数据 ====================

adminUsers.get('/stats', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    // 从 Supabase Auth 获取用户总数
    const supabase = createSupabaseServiceClient(env)
    const { data: allUsers, error } = await supabase.auth.admin.listUsers()

    if (error) {
      console.error('获取用户统计失败:', error)
      return c.json(
        {
          success: false,
          message: '获取统计数据失败'
        },
        500
      )
    }

    const now = new Date()
    const oneDayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000)

    const stats = {
      totalUsers: allUsers.users.length,
      activeUsers: allUsers.users.filter(user => !(user as any).banned_until).length,
      newUsersToday: allUsers.users.filter(user => new Date(user.created_at) > oneDayAgo).length,
      verifiedUsers: allUsers.users.filter(user => user.email_confirmed_at !== null).length
    }

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('获取用户统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取统计数据失败'
      },
      500
    )
  }
})

// ==================== 获取用户列表 ====================

adminUsers.get('/', authMiddleware, zValidator('query', userListSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { page, pageSize, keyword, isEmailVerified, startDate, endDate } = c.req.valid('query')
    const env = c.env

    // 从 Supabase Auth 获取用户列表
    const supabase = createSupabaseServiceClient(env)
    // 为了避免数据库连接问题，先获取较少的用户进行测试
    const { data: authData, error } = await supabase.auth.admin.listUsers({
      page: 1,
      perPage: Math.min(pageSize * 3, 100) // 获取适量数据以减少数据库压力
    })

    if (error) {
      console.error('获取用户列表失败:', error)
      return c.json(
        {
          success: false,
          message: '获取用户列表失败'
        },
        500
      )
    }

    // 优化：批量获取用户业务数据，避免 N+1 查询问题
    console.time('BatchUserDataQuery')

    // 首先批量获取所有用户的业务ID
    const authUserIds = authData.users.map(user => user.id)
    const dbUsersMap = new Map()

    try {
      // 批量获取业务用户数据
      const dbUsersResults = await Promise.allSettled(
        authUserIds.map(id => getUserBySupabaseId(env, id))
      )

      dbUsersResults.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          dbUsersMap.set(authUserIds[index], result.value)
        }
      })

      console.log(`批量获取了 ${dbUsersMap.size} 个用户的业务数据`)
    } catch (error) {
      console.error('批量获取用户业务数据失败:', error)
    }

    // 直接返回简化数据，不做详细查询以提高性能
    const usersWithData = authData.users.map(authUser => {
      const dbUser = dbUsersMap.get(authUser.id)

      return {
        id: authUser.id,
        email: authUser.email,
        isEmailVerified: authUser.email_confirmed_at !== null,
        createdAt: authUser.created_at,
        updatedAt: authUser.updated_at,
        profile: null, // 在列表页不获取详细资料，提高性能
        subscription: null,
        pointsBalance: 0,
        hasActiveMembership: false, // 默认值，可后续优化
        isActive: !(authUser as any).banned_until
      }
    })

    console.timeEnd('BatchUserDataQuery')

    // 应用搜索过滤
    let filteredUsers = usersWithData
    if (keyword) {
      const searchLower = keyword.toLowerCase()
      filteredUsers = usersWithData.filter(
        user => user.email?.toLowerCase().includes(searchLower)
        // 暂时不搜索nickname，因为没有获取profile数据
      )
    }

    // 应用性别过滤 - 暂时跳过，因为没有获取profile数据
    // if (gender) {
    //   filteredUsers = filteredUsers.filter(user =>
    //     user.profile?.gender === gender
    //   )
    // }

    // 应用邮箱验证状态过滤
    if (isEmailVerified !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.isEmailVerified === isEmailVerified)
    }

    // 应用日期过滤
    if (startDate || endDate) {
      const start = startDate ? new Date(startDate) : null
      const end = endDate ? new Date(endDate) : null

      filteredUsers = filteredUsers.filter(user => {
        const createdAt = new Date(user.createdAt)
        if (start && createdAt < start) return false
        if (end && createdAt > end) return false
        return true
      })
    }

    // 应用分页
    const startIndex = (page - 1) * pageSize
    const endIndex = startIndex + pageSize
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex)

    // 转换为前端期望的格式（简化版）
    const frontendUsers = paginatedUsers.map(user => ({
      id: user.id,
      email: user.email,
      phone: null,
      nickname: user.email?.split('@')[0] || null, // 使用邮箱前缀作为昵称
      gender: '', // 列表页不显示性别，提高性能
      avatar: '',
      isEmailVerified: user.isEmailVerified,
      hasActiveMembership: false, // 简化版不查询会员信息
      membershipStatus: null,
      membershipEndDate: null,
      pointsBalance: 0, // 简化版不查询积分
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }))

    return c.json({
      success: true,
      data: {
        data: frontendUsers,
        total: filteredUsers.length,
        page,
        pageSize,
        totalPages: Math.ceil(filteredUsers.length / pageSize)
      }
    })
  } catch (error) {
    console.error('获取用户列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取用户列表失败'
      },
      500
    )
  }
})

// ==================== 获取用户详情 ====================

adminUsers.get('/:userId', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const userId = c.req.param('userId')
    const env = c.env

    // 从 Supabase Auth 获取用户信息
    const supabase = createSupabaseServiceClient(env)
    const { data: authUser, error } = await supabase.auth.admin.getUserById(userId)

    if (error || !authUser.user) {
      return c.json(
        {
          success: false,
          message: '用户不存在'
        },
        404
      )
    }

    // 获取业务用户数据
    const dbUser = await getUserBySupabaseId(env, authUser.user.id)

    const userDetail: any = {
      id: authUser.user.id,
      email: authUser.user.email,
      phone: null,
      nickname: null,
      gender: '',
      avatar: '',
      isEmailVerified: authUser.user.email_confirmed_at !== null,
      createdAt: authUser.user.created_at,
      updatedAt: authUser.user.updated_at,
      profile: null,
      subscription: null,
      points: null
    }

    if (dbUser) {
      // 并行获取用户详细信息以提高性能
      const [profile, subscription, pointsData] = await Promise.allSettled([
        getUserProfile(env, dbUser.id),
        getUserActiveSubscription(env, dbUser.id),
        getUserPoints(env, dbUser.id)
      ])

      const profileData = profile.status === 'fulfilled' ? profile.value : null
      const subscriptionData = subscription.status === 'fulfilled' ? subscription.value : null
      const pointsInfo =
        pointsData.status === 'fulfilled'
          ? pointsData.value
          : { totalPoints: 0, usedPoints: 0, availablePoints: 0 }

      if (profile.status === 'rejected') {
        console.log(`获取用户 ${dbUser.id} 配置文件失败，使用默认值`)
      }
      if (subscription.status === 'rejected') {
        console.log(`获取用户 ${dbUser.id} 订阅信息失败，使用默认值`)
      }
      if (pointsData.status === 'rejected') {
        console.log(`获取用户 ${dbUser.id} 积分信息失败，使用默认值`)
      }

      if (profileData) {
        userDetail.nickname = profileData.nickname
        userDetail.gender = profileData.gender || ''
        userDetail.avatar = profileData.avatarUrl || ''
        userDetail.profile = {
          nickname: profileData.nickname,
          gender: profileData.gender,
          avatarUrl: profileData.avatarUrl,
          bio: profileData.bio,
          createdAt: profileData.createdAt,
          updatedAt: profileData.updatedAt
        }
      }

      userDetail.subscription = subscriptionData
        ? {
            status: subscriptionData.status,
            planName: subscriptionData.planId, // TODO: 获取计划名称
            endDate: subscriptionData.endDate
          }
        : null

      userDetail.points = {
        balance: pointsInfo.availablePoints,
        totalUsed: pointsInfo.usedPoints
      }
    }

    return c.json({
      success: true,
      data: userDetail
    })
  } catch (error) {
    console.error('获取用户详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取用户详情失败'
      },
      500
    )
  }
})

// ==================== 用户状态管理 ====================

adminUsers.put('/:userId/status', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const userId = c.req.param('userId')
    const { isActive } = await c.req.json()
    const env = c.env

    // 禁用/启用用户
    const supabase = createSupabaseServiceClient(env)
    const { error } = await supabase.auth.admin.updateUserById(userId, {
      ban_duration: isActive ? 'none' : '876000h' // 100年，相当于永久禁用
    })

    if (error) {
      console.error('更新用户状态失败:', error)
      return c.json(
        {
          success: false,
          message: '更新用户状态失败'
        },
        500
      )
    }

    return c.json({
      success: true,
      message: isActive ? '用户已启用' : '用户已禁用'
    })
  } catch (error) {
    console.error('更新用户状态失败:', error)
    return c.json(
      {
        success: false,
        message: '更新用户状态失败'
      },
      500
    )
  }
})

// ==================== 创建用户 ====================

const createUserSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少6位').optional(),
  nickname: z.string().optional(),
  generatePassword: z.boolean().default(false)
})

adminUsers.post('/', authMiddleware, zValidator('json', createUserSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { email, password, nickname, generatePassword } = c.req.valid('json')
    const env = c.env

    console.log('➕ [ADMIN-USERS] 创建用户:', email)

    // 生成密码（如果需要）
    const userPassword =
      password ||
      (generatePassword ? 'temp_' + Math.random().toString(36).substring(2, 15) : 'temp123456')

    // 使用 Supabase Service Client 创建用户
    const supabase = createSupabaseServiceClient(env)
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email,
      password: userPassword,
      email_confirm: true, // 跳过邮箱验证
      user_metadata: {
        name: nickname || email.split('@')[0],
        hasPassword: !!password,
        createdByAdmin: true
      }
    })

    if (authError) {
      console.error('❌ [ADMIN-USERS] Supabase 创建用户失败:', authError)

      if (authError.message.includes('already registered')) {
        return c.json(
          {
            success: false,
            message: '该邮箱已被注册'
          },
          409
        )
      }

      return c.json(
        {
          success: false,
          message: authError.message || '创建用户失败'
        },
        400
      )
    }

    if (!authData.user) {
      return c.json(
        {
          success: false,
          message: '创建用户失败，未能获取用户信息'
        },
        500
      )
    }

    try {
      // 在数据库中创建用户记录
      const [dbUser] = await createUser(env, email, authData.user.id)
      console.log('✅ [ADMIN-USERS] 数据库用户创建成功:', dbUser.id)

      // 创建用户资料
      if (nickname) {
        await createUserProfile(env, {
          userId: dbUser.id,
          nickname,
          gender: undefined,
          avatarUrl: ''
        })
        console.log('✅ [ADMIN-USERS] 用户资料创建成功')
      }

      // 初始化用户积分
      await initializeNewUserPointsAsync(env, dbUser.id)
      console.log('✅ [ADMIN-USERS] 用户积分初始化成功')

      return c.json({
        success: true,
        message: '用户创建成功',
        data: {
          id: authData.user.id,
          email: authData.user.email,
          nickname: nickname || email.split('@')[0],
          tempPassword: !password ? userPassword : undefined
        }
      })
    } catch (dbError) {
      console.error('❌ [ADMIN-USERS] 数据库操作失败:', dbError)

      // 如果数据库操作失败，尝试删除 Supabase 用户
      try {
        await supabase.auth.admin.deleteUser(authData.user.id)
        console.log('🔄 [ADMIN-USERS] 已回滚 Supabase 用户')
      } catch (rollbackError) {
        console.error('❌ [ADMIN-USERS] 回滚失败:', rollbackError)
      }

      return c.json(
        {
          success: false,
          message: '创建用户失败，请重试'
        },
        500
      )
    }
  } catch (error) {
    console.error('❌ [ADMIN-USERS] 创建用户失败:', error)
    return c.json(
      {
        success: false,
        message: '创建用户失败'
      },
      500
    )
  }
})

// ==================== 设为会员 ====================

const setMembershipSchema = z.object({
  planId: z.string().min(1, '会员计划ID不能为空'),
  duration: z.number().min(1, '订阅天数必须大于0').max(365, '订阅天数不能超过365天')
})

adminUsers.post(
  '/:userId/membership',
  authMiddleware,
  zValidator('json', setMembershipSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const userId = c.req.param('userId')
      const { planId, duration } = c.req.valid('json')
      const env = c.env

      console.log('🔍 [ADMIN-USERS] 设为会员:', { userId, planId, duration })

      // 验证用户是否存在
      const supabaseService = createSupabaseServiceClient(env)
      const { data: authUser, error: authError } = await supabaseService.auth.admin.getUserById(
        userId
      )

      if (authError || !authUser.user) {
        return c.json({ success: false, message: '用户不存在' }, 404)
      }

      // 获取业务用户数据
      const dbUser = await getUserBySupabaseId(env, userId)
      if (!dbUser) {
        return c.json({ success: false, message: '业务用户数据不存在' }, 404)
      }

      const supabase = getSupabase(env)

      // 验证会员计划是否存在
      const { data: plan, error: planError } = await supabase
        .from(TABLE_NAMES.membershipPlan)
        .select('*')
        .eq('id', planId)
        .eq('is_active', true)
        .single()

      if (planError || !plan) {
        return c.json({ success: false, message: '会员计划不存在或已停用' }, 400)
      }

      // 计算开始和结束时间
      const now = new Date()
      const startDate = now
      const endDate = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000)

      // 检查用户是否已有有效订阅
      const { data: existingSubscription } = await supabase
        .from(TABLE_NAMES.userSubscription)
        .select('*')
        .eq('user_id', dbUser.id)
        .eq('status', 'active')
        .single()

      if (existingSubscription) {
        // 更新现有订阅的结束时间
        const currentEndDate = new Date(existingSubscription.end_date)
        const newEndDate = new Date(Math.max(currentEndDate.getTime(), endDate.getTime()))

        const { error: updateError } = await supabase
          .from(TABLE_NAMES.userSubscription)
          .update({
            end_date: newEndDate.toISOString(),
            plan_id: planId
          })
          .eq('id', existingSubscription.id)

        if (updateError) {
          console.error('❌ [ADMIN-USERS] 更新订阅失败:', updateError)
          return c.json({ success: false, message: '更新订阅失败' }, 500)
        }

        console.log('✅ [ADMIN-USERS] 订阅更新成功')
      } else {
        // 创建新订阅
        const { error: insertError } = await supabase.from(TABLE_NAMES.userSubscription).insert({
          user_id: dbUser.id,
          plan_id: planId,
          start_date: startDate.toISOString(),
          end_date: endDate.toISOString(),
          status: 'active',
          auto_renew: false,
          payment_id: null // 管理员手动设置，无需支付记录
        })

        if (insertError) {
          console.error('❌ [ADMIN-USERS] 创建订阅失败:', insertError)
          return c.json({ success: false, message: '创建订阅失败' }, 500)
        }

        console.log('✅ [ADMIN-USERS] 订阅创建成功')
      }

      // 如果计划包含积分，给用户增加积分
      if (plan.points_included > 0) {
        const { error: pointsError } = await supabase.from(TABLE_NAMES.userPoints).upsert(
          {
            user_id: dbUser.id,
            total_points: plan.points_included,
            used_points: 0,
            available_points: plan.points_included
          },
          {
            onConflict: 'user_id',
            ignoreDuplicates: false
          }
        )

        if (pointsError) {
          console.error('❌ [ADMIN-USERS] 积分更新失败:', pointsError)
          // 不中断操作，只记录警告
        } else {
          console.log('✅ [ADMIN-USERS] 积分更新成功')
        }
      }

      return c.json({
        success: true,
        message: `用户已成功设为会员，订阅 ${duration} 天`
      })
    } catch (error) {
      console.error('❌ [ADMIN-USERS] 设为会员失败:', error)
      return c.json(
        {
          success: false,
          message: '设为会员失败'
        },
        500
      )
    }
  }
)

export default adminUsers

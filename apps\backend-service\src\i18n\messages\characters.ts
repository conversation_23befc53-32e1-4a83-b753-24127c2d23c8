// 角色相关消息
export const charactersMessages = {
  zh: {
    // 基础错误消息
    'characters.user_not_found': '未找到用户信息',
    'characters.user_not_exist': '用户数据不存在',
    'characters.character_not_exist': '角色不存在',
    'characters.no_permission_modify': '无权限修改该角色',
    'characters.no_permission_delete': '无权限删除该角色',
    'characters.private_character_login_required': '私有角色需要登录访问',
    'characters.cannot_create_more': '无法创建更多角色',

    // 获取相关消息
    'characters.get_system_failed': '获取系统角色列表失败',
    'characters.get_public_failed': '获取角色列表失败',
    'characters.get_stats_failed': '获取角色统计失败',
    'characters.get_user_characters_failed': '获取角色列表失败',
    'characters.get_character_details_failed': '获取角色详情失败',

    // 操作相关消息
    'characters.create_failed': '创建角色失败',
    'characters.update_failed': '更新角色失败',
    'characters.delete_failed': '删除角色失败',

    // 成功消息
    'characters.get_stats_success': '获取角色统计成功',
    'characters.delete_success': '角色删除成功',

    // 验证错误消息
    'characters.name_required': '角色名称不能为空',
    'characters.name_too_long': '角色名称不能超过50个字符',
    'characters.description_too_long': '角色描述不能超过500个字符',
    'characters.relationship_too_long': '关系描述不能超过50个字符',
    'characters.ethnicity_too_long': '种族描述不能超过30个字符',
    'characters.invalid_gender': '请选择有效的性别',
    'characters.age_too_long': '年龄描述不能超过20个字符',
    'characters.eye_color_too_long': '眼睛颜色不能超过30个字符',
    'characters.hair_style_too_long': '发型描述不能超过50个字符',
    'characters.hair_color_too_long': '头发颜色不能超过30个字符',
    'characters.body_type_too_long': '体型描述不能超过50个字符',
    'characters.breast_size_too_long': '胸部大小不能超过20个字符',
    'characters.butt_size_too_long': '臀部大小不能超过20个字符',
    'characters.personality_too_long': '性格描述不能超过500个字符',
    'characters.clothing_too_long': '服装描述不能超过200个字符',
    'characters.voice_too_long': '声音描述不能超过100个字符',
    'characters.invalid_voice_model_id': '声音模型ID格式不正确',
    'characters.keywords_too_long': '关键词不能超过200个字符',
    'characters.prompt_too_long': '提示词不能超过2000个字符',
    'characters.invalid_image_url': '请提供有效的图片URL',
    'characters.category_too_long': '分类不能超过50个字符'
  },
  'zh-TW': {
    // 基礎錯誤訊息
    'characters.user_not_found': '未找到使用者資訊',
    'characters.user_not_exist': '使用者資料不存在',
    'characters.character_not_exist': '角色不存在',
    'characters.no_permission_modify': '無權限修改該角色',
    'characters.no_permission_delete': '無權限刪除該角色',
    'characters.private_character_login_required': '私人角色需要登入存取',
    'characters.cannot_create_more': '無法建立更多角色',

    // 取得相關訊息
    'characters.get_system_failed': '取得系統角色列表失敗',
    'characters.get_public_failed': '取得角色列表失敗',
    'characters.get_stats_failed': '取得角色統計失敗',
    'characters.get_user_characters_failed': '取得角色列表失敗',
    'characters.get_character_details_failed': '取得角色詳情失敗',

    // 操作相關訊息
    'characters.create_failed': '建立角色失敗',
    'characters.update_failed': '更新角色失敗',
    'characters.delete_failed': '刪除角色失敗',

    // 成功訊息
    'characters.get_stats_success': '取得角色統計成功',
    'characters.delete_success': '角色刪除成功',

    // 驗證錯誤訊息
    'characters.name_required': '角色名稱不能為空',
    'characters.name_too_long': '角色名稱不能超過50個字元',
    'characters.description_too_long': '角色描述不能超過500個字元',
    'characters.relationship_too_long': '關係描述不能超過50個字元',
    'characters.ethnicity_too_long': '種族描述不能超過30個字元',
    'characters.invalid_gender': '請選擇有效的性別',
    'characters.age_too_long': '年齡描述不能超過20個字元',
    'characters.eye_color_too_long': '眼睛顏色不能超過30個字元',
    'characters.hair_style_too_long': '髮型描述不能超過50個字元',
    'characters.hair_color_too_long': '頭髮顏色不能超過30個字元',
    'characters.body_type_too_long': '體型描述不能超過50個字元',
    'characters.breast_size_too_long': '胸部大小不能超過20個字元',
    'characters.butt_size_too_long': '臀部大小不能超過20個字元',
    'characters.personality_too_long': '性格描述不能超過500個字元',
    'characters.clothing_too_long': '服裝描述不能超過200個字元',
    'characters.voice_too_long': '聲音描述不能超過100個字元',
    'characters.invalid_voice_model_id': '聲音模型ID格式不正確',
    'characters.keywords_too_long': '關鍵字不能超過200個字元',
    'characters.prompt_too_long': '提示詞不能超過2000個字元',
    'characters.invalid_image_url': '請提供有效的圖片URL',
    'characters.category_too_long': '分類不能超過50個字元'
  },
  ja: {
    // 基本エラーメッセージ
    'characters.user_not_found': 'ユーザー情報が見つかりません',
    'characters.user_not_exist': 'ユーザーデータが存在しません',
    'characters.character_not_exist': 'キャラクターが存在しません',
    'characters.no_permission_modify': 'このキャラクターを変更する権限がありません',
    'characters.no_permission_delete': 'このキャラクターを削除する権限がありません',
    'characters.private_character_login_required': 'プライベートキャラクターにアクセスするにはログインが必要です',
    'characters.cannot_create_more': 'これ以上キャラクターを作成することはできません',

    // 取得関連メッセージ
    'characters.get_system_failed': 'システムキャラクターリストの取得に失敗しました',
    'characters.get_public_failed': 'キャラクターリストの取得に失敗しました',
    'characters.get_stats_failed': 'キャラクター統計の取得に失敗しました',
    'characters.get_user_characters_failed': 'キャラクターリストの取得に失敗しました',
    'characters.get_character_details_failed': 'キャラクター詳細の取得に失敗しました',

    // 操作関連メッセージ
    'characters.create_failed': 'キャラクターの作成に失敗しました',
    'characters.update_failed': 'キャラクターの更新に失敗しました',
    'characters.delete_failed': 'キャラクターの削除に失敗しました',

    // 成功メッセージ
    'characters.get_stats_success': 'キャラクター統計の取得に成功しました',
    'characters.delete_success': 'キャラクターの削除に成功しました',

    // 検証エラーメッセージ
    'characters.name_required': 'キャラクター名を入力してください',
    'characters.name_too_long': 'キャラクター名は50文字以内で入力してください',
    'characters.description_too_long': 'キャラクターの説明は500文字以内で入力してください',
    'characters.relationship_too_long': '関係の説明は50文字以内で入力してください',
    'characters.ethnicity_too_long': '民族の説明は30文字以内で入力してください',
    'characters.invalid_gender': '有効な性別を選択してください',
    'characters.age_too_long': '年齢の説明は20文字以内で入力してください',
    'characters.eye_color_too_long': '目の色は30文字以内で入力してください',
    'characters.hair_style_too_long': '髪型の説明は50文字以内で入力してください',
    'characters.hair_color_too_long': '髪の色は30文字以内で入力してください',
    'characters.body_type_too_long': '体型の説明は50文字以内で入力してください',
    'characters.breast_size_too_long': '胸のサイズは20文字以内で入力してください',
    'characters.butt_size_too_long': '臀部のサイズは20文字以内で入力してください',
    'characters.personality_too_long': '性格の説明は500文字以内で入力してください',
    'characters.clothing_too_long': '服装の説明は200文字以内で入力してください',
    'characters.voice_too_long': '音声の説明は100文字以内で入力してください',
    'characters.invalid_voice_model_id': '音声モデルIDの形式が正しくありません',
    'characters.keywords_too_long': 'キーワードは200文字以内で入力してください',
    'characters.prompt_too_long': 'プロンプトは2000文字以内で入力してください',
    'characters.invalid_image_url': '有効な画像URLを入力してください',
    'characters.category_too_long': 'カテゴリは50文字以内で入力してください'
  },
  es: {
    // Mensajes de error básicos
    'characters.user_not_found': 'No se encontró la información del usuario',
    'characters.user_not_exist': 'Los datos del usuario no existen',
    'characters.character_not_exist': 'El personaje no existe',
    'characters.no_permission_modify': 'No tiene permisos para modificar este personaje',
    'characters.no_permission_delete': 'No tiene permisos para eliminar este personaje',
    'characters.private_character_login_required': 'Se requiere iniciar sesión para acceder al personaje privado',
    'characters.cannot_create_more': 'No se pueden crear más personajes',

    // Mensajes relacionados con obtención
    'characters.get_system_failed': 'Error al obtener la lista de personajes del sistema',
    'characters.get_public_failed': 'Error al obtener la lista de personajes',
    'characters.get_stats_failed': 'Error al obtener las estadísticas del personaje',
    'characters.get_user_characters_failed': 'Error al obtener la lista de personajes',
    'characters.get_character_details_failed': 'Error al obtener los detalles del personaje',

    // Mensajes relacionados con operaciones
    'characters.create_failed': 'Error al crear el personaje',
    'characters.update_failed': 'Error al actualizar el personaje',
    'characters.delete_failed': 'Error al eliminar el personaje',

    // Mensajes de éxito
    'characters.get_stats_success': 'Estadísticas del personaje obtenidas correctamente',
    'characters.delete_success': 'Personaje eliminado correctamente',

    // Mensajes de error de validación
    'characters.name_required': 'El nombre del personaje no puede estar vacío',
    'characters.name_too_long': 'El nombre del personaje no puede exceder los 50 caracteres',
    'characters.description_too_long': 'La descripción del personaje no puede exceder los 500 caracteres',
    'characters.relationship_too_long': 'La descripción de la relación no puede exceder los 50 caracteres',
    'characters.ethnicity_too_long': 'La descripción de la etnia no puede exceder los 30 caracteres',
    'characters.invalid_gender': 'Por favor, seleccione un género válido',
    'characters.age_too_long': 'La descripción de la edad no puede exceder los 20 caracteres',
    'characters.eye_color_too_long': 'El color de ojos no puede exceder los 30 caracteres',
    'characters.hair_style_too_long': 'La descripción del peinado no puede exceder los 50 caracteres',
    'characters.hair_color_too_long': 'El color del cabello no puede exceder los 30 caracteres',
    'characters.body_type_too_long': 'La descripción del tipo de cuerpo no puede exceder los 50 caracteres',
    'characters.breast_size_too_long': 'El tamaño del pecho no puede exceder los 20 caracteres',
    'characters.butt_size_too_long': 'El tamaño de las caderas no puede exceder los 20 caracteres',
    'characters.personality_too_long': 'La descripción de la personalidad no puede exceder los 500 caracteres',
    'characters.clothing_too_long': 'La descripción de la ropa no puede exceder los 200 caracteres',
    'characters.voice_too_long': 'La descripción de la voz no puede exceder los 100 caracteres',
    'characters.invalid_voice_model_id': 'El formato del ID del modelo de voz es incorrecto',
    'characters.keywords_too_long': 'Las palabras clave no pueden exceder los 200 caracteres',
    'characters.prompt_too_long': 'El prompt no puede exceder los 2000 caracteres',
    'characters.invalid_image_url': 'Por favor, proporcione una URL de imagen válida',
    'characters.category_too_long': 'La categoría no puede exceder los 50 caracteres'
  },
  en: {
    // Basic error messages
    'characters.user_not_found': 'User information not found',
    'characters.user_not_exist': 'User data does not exist',
    'characters.character_not_exist': 'Character does not exist',
    'characters.no_permission_modify': 'No permission to modify this character',
    'characters.no_permission_delete': 'No permission to delete this character',
    'characters.private_character_login_required': 'Login required to access private character',
    'characters.cannot_create_more': 'Cannot create more characters',

    // Get related messages
    'characters.get_system_failed': 'Failed to get system character list',
    'characters.get_public_failed': 'Failed to get character list',
    'characters.get_stats_failed': 'Failed to get character statistics',
    'characters.get_user_characters_failed': 'Failed to get character list',
    'characters.get_character_details_failed': 'Failed to get character details',

    // Operation related messages
    'characters.create_failed': 'Failed to create character',
    'characters.update_failed': 'Failed to update character',
    'characters.delete_failed': 'Failed to delete character',

    // Success messages
    'characters.get_stats_success': 'Character statistics retrieved successfully',
    'characters.delete_success': 'Character deleted successfully',

    // Validation error messages
    'characters.name_required': 'Character name cannot be empty',
    'characters.name_too_long': 'Character name cannot exceed 50 characters',
    'characters.description_too_long': 'Character description cannot exceed 500 characters',
    'characters.relationship_too_long': 'Relationship description cannot exceed 50 characters',
    'characters.ethnicity_too_long': 'Ethnicity description cannot exceed 30 characters',
    'characters.invalid_gender': 'Please select a valid gender',
    'characters.age_too_long': 'Age description cannot exceed 20 characters',
    'characters.eye_color_too_long': 'Eye color cannot exceed 30 characters',
    'characters.hair_style_too_long': 'Hair style description cannot exceed 50 characters',
    'characters.hair_color_too_long': 'Hair color cannot exceed 30 characters',
    'characters.body_type_too_long': 'Body type description cannot exceed 50 characters',
    'characters.breast_size_too_long': 'Chest size description cannot exceed 20 characters',
    'characters.butt_size_too_long': 'Hip size description cannot exceed 20 characters',
    'characters.personality_too_long': 'Personality description cannot exceed 500 characters',
    'characters.clothing_too_long': 'Clothing description cannot exceed 200 characters',
    'characters.voice_too_long': 'Voice description cannot exceed 100 characters',
    'characters.invalid_voice_model_id': 'Invalid voice model ID format',
    'characters.keywords_too_long': 'Keywords cannot exceed 200 characters',
    'characters.prompt_too_long': 'Prompt cannot exceed 2000 characters',
    'characters.invalid_image_url': 'Please provide a valid image URL',
    'characters.category_too_long': 'Category cannot exceed 50 characters'
  }
}

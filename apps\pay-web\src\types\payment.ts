/**
 * 支付相关类型定义
 */

export interface PaymentOrderInfo {
  orderId: string
  amount: number
  currency: string
  description: string
  paymentMethod: 'alipay' | 'wechat'
  status: 'pending' | 'paid' | 'failed' | 'cancelled' | 'expired'
  isUpgrade: boolean
  originalAmount?: number
  planInfo?: {
    id: string
    name: string
    description: string
    pointsIncluded: number
    durationDays: number
  }
  createdAt: string
  expiresAt: string
  metadata?: any
}

export interface PaymentStatus {
  success: boolean
  orderId: string
  paymentId: string
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'expired'
  amount: number
  paidAt?: string
}

export interface PaymentResult {
  success: boolean
  orderId: string
  paymentId?: string
  redirectUrl?: string
  amount: number
  originalAmount: number
  planName: string
  description: string
  paymentMethod: string
  isUpgrade: boolean
  savings: number
  expiresAt: string
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}
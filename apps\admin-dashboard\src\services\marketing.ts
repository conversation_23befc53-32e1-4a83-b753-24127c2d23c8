import { apiService } from './api'
import type { ApiResponse, PaginatedResponse, InviteCode, CommissionAccount, WithdrawRequest } from '@/types/api'

export interface InviteCodeParams {
  userId: string
  maxUses?: number
  expiresAt?: string
  description?: string
}

export interface CommissionListParams {
  page?: number
  pageSize?: number
  keyword?: string
  startDate?: string
  endDate?: string
}

export interface WithdrawListParams {
  page?: number
  pageSize?: number
  status?: string
  keyword?: string
  startDate?: string
  endDate?: string
}

export interface WithdrawReviewParams {
  status: 'approved' | 'rejected'
  remark?: string
}

// 营销管理服务
export class MarketingService {
  // ==================== 邀请码管理 ====================
  
  // 获取邀请码列表
  async getInviteCodes(params?: {
    page?: number
    pageSize?: number
    isActive?: boolean
    keyword?: string
  }): Promise<ApiResponse<PaginatedResponse<InviteCode>>> {
    return await apiService.get<PaginatedResponse<InviteCode>>('/admin/marketing/invite-codes', { params })
  }

  // 生成邀请码
  async generateInviteCode(params: InviteCodeParams): Promise<ApiResponse<InviteCode>> {
    return await apiService.post<InviteCode>('/admin/marketing/invite-codes/generate', params)
  }

  // 批量生成邀请码
  async batchGenerateInviteCodes(params: InviteCodeParams & { count: number }): Promise<ApiResponse<InviteCode[]>> {
    return await apiService.post<InviteCode[]>('/admin/marketing/invite-codes/batch', params)
  }

  // 禁用邀请码
  async disableInviteCode(id: string): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/marketing/invite-codes/${id}/disable`)
  }

  // 启用邀请码
  async enableInviteCode(id: string): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/marketing/invite-codes/${id}/enable`)
  }

  // 获取邀请码统计
  async getInviteCodeStats(): Promise<ApiResponse<{
    totalCodes: number
    activeCodes: number
    usedCodes: number
    totalInvites: number
    todayInvites: number
  }>> {
    return await apiService.get<any>('/admin/marketing/invite-codes/stats')
  }

  // ==================== 佣金管理 ====================

  // 获取佣金账户列表
  async getCommissionAccounts(params: CommissionListParams): Promise<ApiResponse<PaginatedResponse<CommissionAccount>>> {
    return await apiService.get<PaginatedResponse<CommissionAccount>>('/admin/marketing/commissions', { params })
  }

  // 获取佣金记录
  async getCommissionRecords(params: {
    page?: number
    pageSize?: number
    userId?: string
    type?: string
    startDate?: string
    endDate?: string
  }): Promise<ApiResponse<PaginatedResponse<any>>> {
    return await apiService.get<PaginatedResponse<any>>('/admin/marketing/commission-records', { params })
  }

  // 调整佣金
  async adjustCommission(params: {
    userId: string
    amount: number
    type: 'add' | 'deduct'
    reason: string
  }): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/admin/marketing/commissions/adjust', params)
  }

  // 获取佣金统计
  async getCommissionStats(): Promise<ApiResponse<{
    totalAccounts: number
    totalBalance: number
    totalEarned: number
    totalWithdrawn: number
    monthlyCommission: number
  }>> {
    return await apiService.get<any>('/admin/marketing/commissions/stats')
  }

  // ==================== 提现管理 ====================

  // 获取提现申请列表
  async getWithdrawRequests(params: WithdrawListParams): Promise<ApiResponse<PaginatedResponse<WithdrawRequest>>> {
    return await apiService.get<PaginatedResponse<WithdrawRequest>>('/admin/marketing/withdrawals', { params })
  }

  // 审核提现申请
  async reviewWithdrawRequest(id: string, params: WithdrawReviewParams): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/marketing/withdrawals/${id}/review`, params)
  }

  // 完成提现
  async completeWithdraw(id: string, params: { transactionNo: string }): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/marketing/withdrawals/${id}/complete`, params)
  }

  // 获取提现统计
  async getWithdrawStats(): Promise<ApiResponse<{
    pendingCount: number
    pendingAmount: number
    todayCount: number
    todayAmount: number
    monthlyAmount: number
  }>> {
    return await apiService.get<any>('/admin/marketing/withdrawals/stats')
  }
}

export const marketingService = new MarketingService()
import { createSupabaseClient, createSupabaseServiceClient } from '../../supabase';
import type { SupabaseClient } from '@supabase/supabase-js';
import type { Env } from '@/types/env';

// 导出 Supabase 客户端供其他查询文件使用
export { createSupabaseClient, createSupabaseServiceClient };

// 创建 Supabase 客户端的辅助函数（用于一般用户操作）
export function getSupabase(env: Env): SupabaseClient {
  return createSupabaseClient(env);
}

// 创建 Supabase 服务端客户端的辅助函数（用于管理员操作）
export function getSupabaseService(env: Env): SupabaseClient {
  return createSupabaseServiceClient(env);
}

// 为了向后兼容，保留 getDb 函数但重定向到 Supabase
// TODO: 这个函数将在所有查询文件迁移完成后移除
export function getDb(env: Env): SupabaseClient {
  console.warn('getDb() is deprecated, use getSupabase() instead');
  return getSupabase(env);
}

// 为了向后兼容，添加 createDbConnection 函数
export function createDbConnection(env: Env): SupabaseClient {
  console.warn('createDbConnection() is deprecated, use getSupabase() instead');
  return getSupabase(env);
}

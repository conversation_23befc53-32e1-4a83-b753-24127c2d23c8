{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ESNext", "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "strict": true, "noEmit": true, "skipLibCheck": true, "resolveJsonModule": true, "isolatedModules": true, "declaration": false, "declarationMap": false, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/lib/*": ["./src/lib/*"], "@/types/*": ["./src/types/*"], "@/routes/*": ["./src/routes/*"], "@/middleware/*": ["./src/middleware/*"]}, "types": ["@cloudflare/workers-types", "node", "vitest/globals"]}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", ".wrangler"]}
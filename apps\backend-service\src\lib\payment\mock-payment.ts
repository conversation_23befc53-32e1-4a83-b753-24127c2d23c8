/**
 * 模拟支付服务
 * 用于开发和测试环境，模拟真实的支付流程
 */

// 支付订单接口
export interface PaymentOrder {
  id: string;
  amount: number;
  currency: string;
  description: string;
  userId: string;
  planId?: string;
  pointsPackageId?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
}

// 支付结果接口
export interface PaymentResult {
  success: boolean;
  orderId: string;
  paymentId?: string;
  error?: string;
  redirectUrl?: string;
}

// 支付验证结果
export interface PaymentVerification {
  success: boolean;
  orderId: string;
  paymentId: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  amount: number;
  paidAt?: Date;
}

// 模拟支付订单存储（生产环境应使用数据库）
const mockOrders = new Map<string, PaymentOrder>();
const mockPayments = new Map<string, PaymentVerification>();

/**
 * 模拟支付服务类
 */
export class MockPaymentService {
  /**
   * 创建支付订单
   */
  async createOrder(orderInfo: {
    amount: number;
    currency?: string;
    description: string;
    userId: string;
    planId?: string;
    pointsPackageId?: string;
    metadata?: Record<string, any>;
  }): Promise<PaymentResult> {
    try {
      const orderId = `order_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const paymentId = `pay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 创建订单
      const order: PaymentOrder = {
        id: orderId,
        amount: orderInfo.amount,
        currency: orderInfo.currency || 'CNY',
        description: orderInfo.description,
        userId: orderInfo.userId,
        planId: orderInfo.planId,
        pointsPackageId: orderInfo.pointsPackageId,
        metadata: orderInfo.metadata,
        createdAt: new Date(),
      };

      // 存储订单
      mockOrders.set(orderId, order);

      // 模拟支付创建成功
      console.log('🏦 [MOCK-PAYMENT] 创建支付订单:', {
        orderId,
        paymentId,
        amount: orderInfo.amount,
        description: orderInfo.description,
      });

      return {
        success: true,
        orderId,
        paymentId,
        redirectUrl: `/mock-payment?orderId=${orderId}&paymentId=${paymentId}`,
      };
    } catch (error) {
      console.error('❌ [MOCK-PAYMENT] 创建订单失败:', error);
      return {
        success: false,
        orderId: '',
        error: '创建支付订单失败',
      };
    }
  }

  /**
   * 模拟支付成功（开发测试用）
   */
  async simulatePaymentSuccess(paymentId: string, orderId: string): Promise<PaymentVerification> {
    const order = mockOrders.get(orderId);
    if (!order) {
      throw new Error(`订单不存在: ${orderId}`);
    }

    const verification: PaymentVerification = {
      success: true,
      orderId,
      paymentId,
      status: 'completed',
      amount: order.amount,
      paidAt: new Date(),
    };

    mockPayments.set(paymentId, verification);

    console.log('✅ [MOCK-PAYMENT] 模拟支付成功:', {
      paymentId,
      orderId,
      amount: order.amount,
    });

    return verification;
  }

  /**
   * 模拟支付失败（开发测试用）
   */
  async simulatePaymentFailure(paymentId: string, orderId: string): Promise<PaymentVerification> {
    const order = mockOrders.get(orderId);
    if (!order) {
      throw new Error(`订单不存在: ${orderId}`);
    }

    const verification: PaymentVerification = {
      success: false,
      orderId,
      paymentId,
      status: 'failed',
      amount: order.amount,
    };

    mockPayments.set(paymentId, verification);

    console.log('❌ [MOCK-PAYMENT] 模拟支付失败:', {
      paymentId,
      orderId,
      amount: order.amount,
    });

    return verification;
  }

  /**
   * 验证支付状态
   */
  async verifyPayment(paymentId: string): Promise<PaymentVerification> {
    const payment = mockPayments.get(paymentId);
    if (!payment) {
      // 如果没有支付记录，默认为待处理状态
      return {
        success: false,
        orderId: '',
        paymentId,
        status: 'pending',
        amount: 0,
      };
    }

    return payment;
  }

  /**
   * 获取订单信息
   */
  async getOrder(orderId: string): Promise<PaymentOrder | null> {
    return mockOrders.get(orderId) || null;
  }

  /**
   * 处理支付回调（模拟微信/支付宝回调）
   */
  async processCallback(callbackData: {
    orderId: string;
    paymentId: string;
    status: 'success' | 'failed';
    amount: number;
    timestamp: number;
    signature?: string;
  }): Promise<{ success: boolean; message: string }> {
    try {
      const { orderId, paymentId, status, amount } = callbackData;

      const order = mockOrders.get(orderId);
      if (!order) {
        return { success: false, message: '订单不存在' };
      }

      // 验证金额（真实环境还需要验证签名）
      if (Math.abs(order.amount - amount) > 0.01) {
        return { success: false, message: '金额不匹配' };
      }

      // 更新支付状态
      const verification: PaymentVerification = {
        success: status === 'success',
        orderId,
        paymentId,
        status: status === 'success' ? 'completed' : 'failed',
        amount,
        paidAt: status === 'success' ? new Date() : undefined,
      };

      mockPayments.set(paymentId, verification);

      console.log('🔄 [MOCK-PAYMENT] 处理支付回调:', {
        orderId,
        paymentId,
        status,
        amount,
      });

      return { success: true, message: '回调处理成功' };
    } catch (error) {
      console.error('❌ [MOCK-PAYMENT] 回调处理失败:', error);
      return { success: false, message: '回调处理失败' };
    }
  }

  /**
   * 获取所有模拟数据（调试用）
   */
  getDebugInfo() {
    return {
      orders: Array.from(mockOrders.entries()),
      payments: Array.from(mockPayments.entries()),
    };
  }

  /**
   * 清空模拟数据（测试用）
   */
  clearMockData() {
    mockOrders.clear();
    mockPayments.clear();
    console.log('🧹 [MOCK-PAYMENT] 已清空模拟数据');
  }
}

// 单例实例
export const mockPaymentService = new MockPaymentService();

// 环境配置工厂函数
export function createPaymentService(env?: any): MockPaymentService | any {
  // 根据环境变量决定使用真实支付服务还是Mock服务
  const useRealPayment = env?.USE_REAL_PAYMENT === 'true' || env?.NODE_ENV === 'production';

  if (useRealPayment && env) {
    console.log('🏦 [PAYMENT-FACTORY] 使用真实支付服务');
    const { RealPaymentService } = require('./real-payment-service');
    return new RealPaymentService(env);
  } else {
    console.log('🧪 [PAYMENT-FACTORY] 使用Mock支付服务');
    return mockPaymentService;
  }
}

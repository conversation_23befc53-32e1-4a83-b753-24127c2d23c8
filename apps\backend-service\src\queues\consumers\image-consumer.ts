import type { Env } from '@/types/env'
import type {
  ImageGenerationTask,
  ImageGenerationProgress,
  Insa3DTaskRequest,
  Insa3DTaskResponse,
  Insa3DTaskStatus,
  ImageAttachment,
  GeneratingStatusAttachment,
  CompletedImageAttachment
} from '@/types/image'
import type {
  ReplicatePredictionRequest,
  ReplicatePredictionResponse,
  ReplicatePredictionStatus
} from '@/types/face-swap'
import { getMessageById, updateMessageAttachments } from '@/lib/db/queries/chat'
import { QueueErrorHandler } from '../utils/queue-handler'
import { updateMediaGeneration, getMediaGenerationById } from '@/lib/db/queries/media-generation'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { uploadToR2, getR2ConfigFromEnv, IMAGE_UPLOAD_OPTIONS } from '@/lib/utils/r2-upload'

const CONFIG = {
  endpoint: 'c2a3we84tjaoip',
  token: 'SD_2RGdTZtC0WcZku0bJjA'
}

/**
 * 图片队列消费者
 * 处理图片生成任务，使用 InstantSD + Replicate 换脸流程
 */
export class ImageQueueConsumer {
  private readonly REPLICATE_MODEL_VERSION =
    'd1d6ea8c8be89d664a07a457526f7128109dee7030fdac424788d762c71ed111'

  constructor(private env: Env) {}

  /**
   * 处理单个图片生成任务
   */
  async process(task: ImageGenerationTask): Promise<void> {
    console.log('🎨 开始生成:', task.taskId)

    // 获取媒体生成记录ID
    const mediaGenerationId = task.metadata?.mediaGenerationId

    if (mediaGenerationId) {
      // 更新媒体生成记录为处理中状态
      try {
        await updateMediaGeneration(this.env, mediaGenerationId, {
          status: 'processing'
        })
      } catch (error) {
        console.error('❌ 更新失败:', error)
      }
    }

    try {
      // 第一步：使用 InstantSD 生成图片
      console.log('🎨 第一步：生成图片')
      await this.updateGenerationProgress(
        task.messageId,
        {
          status: 'starting',
          progress: 10,
          message: '正在生成图片...'
        },
        mediaGenerationId
      )

      const generatedImageUrl = await this.generateImageWithInstantSD(task, mediaGenerationId)

      await this.updateGenerationProgress(
        task.messageId,
        {
          status: 'processing',
          progress: 50,
          // message: '图片生成完成，开始换脸...'
          message: '进度 50%...'
        },
        mediaGenerationId
      )

      // 第二步：使用 Replicate 进行换脸（如果有角色头像）
      let finalImageUrl = generatedImageUrl
      if (task.characterAvatar) {
        console.log('🔄 第二步：换脸处理')
        const replicatePredictionId = await this.startReplicateFaceSwap(task, generatedImageUrl)

        await this.updateGenerationProgress(
          task.messageId,
          {
            status: 'processing',
            progress: 70,
            message: '正在渲染...'
          },
          mediaGenerationId
        )

        const faceSwapResult = await this.waitForReplicateCompletion(
          replicatePredictionId,
          task.messageId,
          mediaGenerationId
        )

        if (faceSwapResult.status === 'succeeded' && faceSwapResult.imageUrl) {
          finalImageUrl = faceSwapResult.imageUrl
        } else {
          throw new Error(faceSwapResult.errorMessage || '换脸失败')
        }
      }

      // 先上传到 R2 获取永久链接
      const r2ImageUrl = await this.downloadAndUploadToR2(finalImageUrl, task.taskId)

      // 更新完成状态，使用 R2 URL
      await this.updateCompletedImage(task.messageId, r2ImageUrl, task.taskId)

      // 更新媒体生成记录为完成状态，使用 R2 URL
      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'completed',
            outputUrls: [r2ImageUrl], // 使用 R2 URL 而不是临时 URL
            completedAt: new Date()
          })
        } catch (error) {
          console.error('❌ 更新失败:', error)
        }
      }

      console.log('✅ 生成完成')
    } catch (error) {
      const errorMessage = QueueErrorHandler.handleTaskError(task.taskId, error)
      await this.updateFailureStatus(task.messageId, errorMessage)

      // 更新媒体生成记录为失败状态
      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'failed',
            errorMessage
          })
        } catch (updateError) {
          console.error('❌ 更新失败:', updateError)
        }
      }

      // 生成失败，退还积分
      await this.refundPointsForFailedGeneration(task.userId, task.taskId)

      throw new Error(`生成失败: ${errorMessage}`)
    }
  }

  /**
   * 启动 Insa3D 图片生成
   */
  private async startInsa3DGeneration(task: ImageGenerationTask): Promise<string> {
    const request: Insa3DTaskRequest = {
      inputs: {
        '96b9fc105f305520': {
          title: 'width',
          value: task.metadata?.width || 720
        },
        '577750b1e716b191': {
          title: 'height',
          value: task.metadata?.height || 1280
        },
        '6307d2aa9851566f': {
          title: 'prompt',
          value: task.prompt
        },
        ...(task.characterAvatar && {
          '4bee95674812740b': {
            title: 'Load Image',
            value: task.characterAvatar
          }
        })
      }
    }

    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${this.env.INSA3D_API_ENDPOINT}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.env.INSA3D_API_TOKEN}`
        },
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(60000) // 60秒超时
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`API请求失败: ${response.status} ${errorText}`)
    }

    const result: Insa3DTaskResponse = await response.json()
    console.log('✅ 任务启动:', result.task_id)

    return result.task_id
  }

  /**
   * 等待 Insa3D 任务完成
   */
  private async waitForTaskCompletion(
    taskId: string,
    messageId: string
  ): Promise<{ status: string; imageUrl?: string; errorMessage?: string }> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ 轮询状态:', taskId)

    let progressStep = 40 // 从40%开始
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(
          `https://api.instasd.com/api_endpoints/${this.env.INSA3D_API_ENDPOINT}/task_status/${taskId}`,
          {
            headers: {
              Authorization: `Bearer ${this.env.INSA3D_API_TOKEN}`
            }
          }
        )

        if (!response.ok) {
          consecutiveErrors++
          console.warn(`⚠️ 查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`, response.status)

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const task: Insa3DTaskStatus = await response.json()

        // 更新进度
        if (task.status === 'IN_PROGRESS' || task.status === 'EXECUTING') {
          progressStep = Math.min(progressStep + 5, 90)
          await this.updateGenerationProgress(messageId, {
            status: 'processing',
            progress: progressStep,
            message: '生成中...'
          })
        }

        // 检查完成状态
        if (task.status === 'COMPLETED') {
          return {
            status: 'completed',
            imageUrl: task.image_urls?.[0],
            errorMessage: undefined
          }
        }

        if (task.status === 'FAILED') {
          return {
            status: 'failed',
            imageUrl: undefined,
            errorMessage: task.error_message || '生成失败'
          }
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(`❌ 轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`, error)

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('等待超时')
  }

  /**
   * 更新生成进度
   */
  private async updateGenerationProgress(
    messageId: string,
    progress: Omit<ImageGenerationProgress, 'messageId' | 'timestamp'>,
    mediaGenerationId?: string
  ): Promise<void> {
    console.log('📊 更新进度:', progress.progress + '%')

    // 如果有媒体生成记录ID，更新进度到数据库
    if (mediaGenerationId) {
      try {
        // 先获取现有的媒体生成记录，保留原有的 metadata
        const currentRecord = await getMediaGenerationById(this.env, mediaGenerationId)
        const existingMetadata = currentRecord?.metadata || {}

        await updateMediaGeneration(this.env, mediaGenerationId, {
          metadata: {
            ...existingMetadata, // 保留原有的 metadata
            progress: progress.progress,
            status: progress.status,
            message: progress.message,
            timestamp: new Date().toISOString()
          }
        })
        console.log('📊 媒体生成记录进度已更新:', progress.progress + '%')
      } catch (error) {
        console.warn('⚠️ 更新媒体生成记录进度失败:', error)
      }
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除之前的生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 添加新的状态附件
      const statusAttachment: GeneratingStatusAttachment = {
        url: `generating://${progress.status}`,
        name: progress.message,
        contentType: 'image/generating',
        metadata: {
          status: progress.status,
          progress: progress.progress,
          timestamp: new Date().toISOString(),
          taskId: messageId // 使用 messageId 作为 taskId
        }
      }

      const updatedAttachments = [...filteredAttachments, statusAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.warn('⚠️ 更新进度失败')
    }
  }

  /**
   * 更新完成的图片
   */
  private async updateCompletedImage(
    messageId: string,
    imageUrl: string,
    taskId: string
  ): Promise<void> {
    console.log('🎉 图片完成')

    // imageUrl 已经是 R2 URL，直接使用
    const finalImageUrl = imageUrl

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件，添加真实图片附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      const imageAttachment: CompletedImageAttachment = {
        url: finalImageUrl,
        name: `generated-image-${Date.now()}.png`,
        contentType: 'image/png',
        metadata: {
          taskId,
          generatedAt: new Date().toISOString()
        }
      }

      const updatedAttachments = [...filteredAttachments, imageAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.error('❌ 更新图片失败')
      throw error
    }
  }

  /**
   * 更新失败状态
   */
  private async updateFailureStatus(messageId: string, error: string): Promise<void> {
    console.log('❌ 失败:', error)

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: filteredAttachments
      })
    } catch (updateError) {
      console.warn('⚠️ 更新状态失败')
    }
  }

  /**
   * 退还积分（生成失败时）
   */
  private async refundPointsForFailedGeneration(userId: string, taskId: string): Promise<void> {
    try {
      const pointsManager = createServicePointsManager(this.env)

      // 图片生成固定消费10积分
      const pointsToRefund = 10

      // 使用积分退还方法
      const refundResult = await pointsManager.refundPoints(userId, {
        amount: pointsToRefund,
        source: 'refund',
        sourceId: taskId,
        description: `图片生成失败退还 - 任务ID: ${taskId}`
      })

      if (refundResult.success) {
        console.log(`✅ 退还积分 ${pointsToRefund}`)
      } else {
        console.error(`❌ 退还失败: ${refundResult.error}`)
      }
    } catch (error) {
      console.error('退还异常:', error)
    }
  }

  /**
   * 使用 InstantSD 生成图片（第一步）
   */
  private async generateImageWithInstantSD(
    task: ImageGenerationTask,
    mediaGenerationId?: string
  ): Promise<string> {
    const request = {
      inputs: {
        '79facdf6ca0558a0': {
          title: 'width',
          value: task.metadata?.width || 1024
        },
        a582029f7614ff66: {
          title: 'height',
          value: task.metadata?.height || 1440
        },
        '8bab993458e5b954': {
          title: 'prompt',
          value: task.prompt
        },
        '8bded2ca4cae6ff6': {
          title: 'seed',
          value: this.generateRandomSeed()
        }
      }
    }

    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${CONFIG.endpoint}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${CONFIG.token}`
        },
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(60000)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`InstantSD API请求失败: ${response.status} ${errorText}`)
    }

    const result = (await response.json()) as { task_id: string }
    console.log('📋 [DEBUG] InstantSD API 返回结果:', result)

    const taskId = result.task_id
    console.log('📋 [DEBUG] 提取的 task_id:', taskId)
    console.log('📋 [DEBUG] task_id 类型:', typeof taskId)

    if (!taskId) {
      throw new Error(`InstantSD API 未返回有效的 task_id: ${JSON.stringify(result)}`)
    }

    console.log('✅ InstantSD任务启动:', taskId)

    // 等待图片生成完成
    return await this.waitForInstantSDCompletion(taskId, task.messageId, mediaGenerationId)
  }

  /**
   * 等待 InstantSD 任务完成
   */
  private async waitForInstantSDCompletion(
    taskId: string,
    messageId: string,
    mediaGenerationId?: string
  ): Promise<string> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ 轮询InstantSD状态:', taskId)
    console.log(
      '📋 [DEBUG] 轮询URL:',
      `https://api.instasd.com/api_endpoints/${CONFIG.endpoint}/task_status/${taskId}`
    )

    let progressStep = 20 // 从20%开始
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(
          `https://api.instasd.com/api_endpoints/${CONFIG.endpoint}/task_status/${taskId}`,
          {
            headers: {
              Authorization: `Bearer ${CONFIG.token}`
            }
          }
        )

        if (!response.ok) {
          consecutiveErrors++
          console.warn(
            `⚠️ InstantSD查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`,
            response.status
          )

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('InstantSD查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const task = (await response.json()) as {
          status: string
          image_urls?: string[]
          error_message?: string
        }

        // 更新进度
        if (task.status === 'IN_PROGRESS' || task.status === 'EXECUTING') {
          progressStep = Math.min(progressStep + 5, 45)
          await this.updateGenerationProgress(
            messageId,
            {
              status: 'processing',
              progress: progressStep,
              message: '生成图片中...'
            },
            mediaGenerationId
          )
        }

        // 检查完成状态
        if (task.status === 'COMPLETED') {
          const imageUrl = task.image_urls?.[0]
          if (!imageUrl) {
            throw new Error('InstantSD未返回图片URL')
          }
          console.log('✅ InstantSD图片生成完成:', imageUrl)
          return imageUrl
        }

        if (task.status === 'FAILED') {
          throw new Error(task.error_message || 'InstantSD图片生成失败')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(`❌ InstantSD轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`, error)

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('InstantSD轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('InstantSD等待超时')
  }

  /**
   * 启动 Replicate 换脸任务
   */
  private async startReplicateFaceSwap(
    task: ImageGenerationTask,
    inputImageUrl: string
  ): Promise<string> {
    const request: ReplicatePredictionRequest = {
      version: this.REPLICATE_MODEL_VERSION,
      input: {
        swap_image: task.characterAvatar || '',
        input_image: inputImageUrl
      }
    }

    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.env.REPLICATE_API_TOKEN}`,
        Prefer: 'wait'
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(60000) // 60秒超时
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Replicate API请求失败: ${response.status} ${errorText}`)
    }

    const result: ReplicatePredictionResponse = await response.json()
    console.log('✅ Replicate任务启动:', result.id)

    return result.id
  }

  /**
   * 等待 Replicate 任务完成
   */
  private async waitForReplicateCompletion(
    predictionId: string,
    messageId: string,
    mediaGenerationId?: string
  ): Promise<{ status: string; imageUrl?: string; errorMessage?: string }> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ 轮询Replicate状态:', predictionId)

    let progressStep = 75 // 从75%开始
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(`https://api.replicate.com/v1/predictions/${predictionId}`, {
          headers: {
            Authorization: `Bearer ${this.env.REPLICATE_API_TOKEN}`
          }
        })

        if (!response.ok) {
          consecutiveErrors++
          console.warn(`⚠️ 查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`, response.status)

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const prediction: ReplicatePredictionStatus = await response.json()

        // 更新进度
        if (prediction.status === 'starting' || prediction.status === 'processing') {
          progressStep = Math.min(progressStep + 5, 90)
          await this.updateGenerationProgress(
            messageId,
            {
              status: 'processing',
              progress: progressStep,
              message: '换脸中...'
            },
            mediaGenerationId
          )
        }

        // 检查完成状态
        if (prediction.status === 'succeeded') {
          const imageUrl = Array.isArray(prediction.output)
            ? prediction.output[0]
            : prediction.output

          return {
            status: 'succeeded',
            imageUrl: imageUrl as string,
            errorMessage: undefined
          }
        }

        if (prediction.status === 'failed' || prediction.status === 'canceled') {
          return {
            status: 'failed',
            imageUrl: undefined,
            errorMessage: prediction.error || '换脸失败'
          }
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(`❌ 轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`, error)

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('等待超时')
  }

  /**
   * 生成随机 seed
   */
  private generateRandomSeed(): number {
    // 生成一个 10-13 位的随机数字作为 seed
    return Math.floor(Math.random() * 9000000000000) + 1000000000000
  }

  /**
   * 下载图片并上传到 R2
   */
  private async downloadAndUploadToR2(imageUrl: string, taskId: string): Promise<string> {
    try {
      console.log('📸 [R2上传] 开始下载图片:', imageUrl)

      // 1. 下载图片
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
      }

      const imageBuffer = await response.arrayBuffer()
      console.log('📸 [R2上传] 图片下载完成，大小:', imageBuffer.byteLength, '字节')

      // 2. 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        console.error('❌ [R2上传] R2 配置缺失，使用原始 URL')
        return imageUrl
      }

      console.log('📸 [R2上传] R2 配置获取成功')

      // 3. 生成文件名
      const timestamp = new Date().toISOString().split('T')[0]
      const uniqueId = taskId.split('-').pop() || 'unknown'
      const fileName = `generated-image-${uniqueId}.png`

      // 4. 上传到 R2
      const uploadResult = await uploadToR2(imageBuffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName,
        folder: `generated-images/${timestamp}`
      })

      if (!uploadResult.success) {
        console.error('❌ [R2上传] 失败，使用原始 URL:', uploadResult.error)
        return imageUrl
      }

      console.log('✅ [R2上传] 上传成功:', {
        originalUrl: imageUrl,
        r2Url: uploadResult.url,
        key: uploadResult.key,
        size: uploadResult.size
      })

      return uploadResult.url!
    } catch (error) {
      console.error('❌ [R2上传] 异常，使用原始 URL:', error)
      return imageUrl
    }
  }
}

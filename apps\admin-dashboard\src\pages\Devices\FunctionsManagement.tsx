import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Badge,
  InputNumber,
  Divider,
  List,
  Empty
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  <PERSON>boltOutlined,
  SettingOutlined,
  LinkOutlined,
  DisconnectOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { DeviceFunction, DeviceFunctionDetail } from '@/services/device-functions'
import { functionService } from '@/services/device-functions'
import { commandSetService } from '@/services/device-command-sets'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input

const FunctionsManagement: React.FC = () => {
  const [functions, setFunctions] = useState<DeviceFunction[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [commandModalVisible, setCommandModalVisible] = useState(false)
  const [editingFunction, setEditingFunction] = useState<DeviceFunction | null>(null)
  const [selectedFunction, setSelectedFunction] = useState<DeviceFunctionDetail | null>(null)
  const [commandSets, setCommandSets] = useState<any[]>([])
  const [form] = Form.useForm()
  const [commandForm] = Form.useForm()
  
  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    isActive: undefined as boolean | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0
  })

  useEffect(() => {
    loadFunctions()
    loadCommandSets()
  }, [currentPage, pageSize, searchParams])

  const loadFunctions = async () => {
    try {
      setLoading(true)
      
      const response = await functionService.getFunctions({
        page: currentPage,
        pageSize,
        ...searchParams
      })
      
      if (response.success && response.data) {
        setFunctions(response.data.data)
        setTotal(response.data.total)
        
        // 计算统计数据
        const activeCount = response.data.data.filter(item => item.isActive).length
        setStats({
          total: response.data.total,
          active: activeCount,
          inactive: response.data.total - activeCount
        })
      } else {
        message.error(response.message || '获取设备功能列表失败')
      }
    } catch (error) {
      console.error('获取设备功能列表失败:', error)
      message.error('获取设备功能列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadCommandSets = async () => {
    try {
      const response = await commandSetService.getCommandSets({ 
        page: 1, 
        pageSize: 100,
        isActive: true 
      })
      if (response.success && response.data) {
        setCommandSets(response.data.data)
      }
    } catch (error) {
      console.error('获取指令集失败:', error)
    }
  }

  const loadFunctionDetail = async (functionId: string) => {
    try {
      const response = await functionService.getFunction(functionId)
      if (response.success && response.data) {
        setSelectedFunction(response.data)
      } else {
        message.error('获取功能详情失败')
      }
    } catch (error) {
      console.error('获取功能详情失败:', error)
      message.error('获取功能详情失败')
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      isActive: undefined
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingFunction(null)
    form.resetFields()
    form.setFieldsValue({ isActive: true, maxIntensity: 3 })
    setModalVisible(true)
  }

  const handleEdit = (func: DeviceFunction) => {
    setEditingFunction(func)
    form.setFieldsValue({
      name: func.name,
      key: func.key,
      description: func.description,
      maxIntensity: func.maxIntensity,
      isActive: func.isActive
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingFunction) {
        const response = await functionService.updateFunction(editingFunction.id, values)
        if (response.success) {
          message.success('设备功能更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await functionService.createFunction(values)
        if (response.success) {
          message.success('设备功能创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }

      setModalVisible(false)
      loadFunctions()
    } catch (error) {
      console.error('操作失败:', error)
      message.error(editingFunction ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await functionService.deleteFunction(id)
      if (response.success) {
        message.success('设备功能删除成功')
        loadFunctions()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleManageCommands = async (func: DeviceFunction) => {
    await loadFunctionDetail(func.id)
    setCommandModalVisible(true)
  }

  const handleAddCommand = async (values: any) => {
    if (!selectedFunction) return

    try {
      const response = await functionService.addFunctionCommand(selectedFunction.id, values)
      if (response.success) {
        message.success('指令关联添加成功')
        commandForm.resetFields()
        await loadFunctionDetail(selectedFunction.id)
      } else {
        message.error(response.message || '添加失败')
      }
    } catch (error) {
      console.error('添加指令关联失败:', error)
      message.error('添加指令关联失败')
    }
  }

  const handleRemoveCommand = async (commandId: string) => {
    if (!selectedFunction) return

    try {
      const response = await functionService.removeFunctionCommand(selectedFunction.id, commandId)
      if (response.success) {
        message.success('指令关联删除成功')
        await loadFunctionDetail(selectedFunction.id)
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除指令关联失败:', error)
      message.error('删除指令关联失败')
    }
  }

  const handleViewDetail = async (func: DeviceFunction) => {
    try {
      // 获取功能详情，包含关联的指令
      const response = await functionService.getFunction(func.id)
      if (response.success && response.data) {
        const functionDetail = response.data
        
        Modal.info({
          title: '设备功能详情',
          width: 800,
          content: (
            <div style={{ marginTop: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                <div>
                  <strong>基本信息</strong>
                  <div style={{ marginTop: 8, marginLeft: 16 }}>
                    <div><strong>功能名称:</strong> {functionDetail.name}</div>
                    <div><strong>功能键:</strong> <Text code>{functionDetail.key}</Text></div>
                    <div><strong>描述:</strong> {functionDetail.description || '无'}</div>
                    <div><strong>最大强度:</strong> {functionDetail.maxIntensity} 级</div>
                    <div><strong>状态:</strong> {
                      functionDetail.isActive ? 
                        <Tag color="green">启用</Tag> : 
                        <Tag color="red">禁用</Tag>
                    }</div>
                  </div>
                </div>

                <div>
                  <strong>关联指令 ({functionDetail.commands?.length || 0} 个)</strong>
                  <div style={{ marginTop: 8, marginLeft: 16 }}>
                    {functionDetail.commands && functionDetail.commands.length > 0 ? (
                      <List
                        size="small"
                        dataSource={functionDetail.commands}
                        renderItem={(command) => (
                          <List.Item>
                            <div style={{ width: '100%' }}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <div>
                                  <strong>{command.commandSet?.name || '未知指令集'}</strong>
                                  <Tag color="blue" style={{ marginLeft: 8 }}>强度 {command.intensity}</Tag>
                                </div>
                              </div>
                              <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                                <div>指令: <Text code>{command.commandSet?.command}</Text></div>
                                {command.description && <div>描述: {command.description}</div>}
                              </div>
                            </div>
                          </List.Item>
                        )}
                      />
                    ) : (
                      <Empty description="暂无关联指令" image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                  </div>
                </div>

                <div>
                  <strong>时间信息</strong>
                  <div style={{ marginTop: 8, marginLeft: 16 }}>
                    <div><strong>创建时间:</strong> {dayjs(functionDetail.createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
                    <div><strong>更新时间:</strong> {dayjs(functionDetail.updatedAt).format('YYYY-MM-DD HH:mm:ss')}</div>
                  </div>
                </div>
              </Space>
            </div>
          )
        })
      } else {
        message.error('获取功能详情失败')
      }
    } catch (error) {
      console.error('获取功能详情失败:', error)
      message.error('获取功能详情失败')
    }
  }

  const columns: ColumnsType<DeviceFunction> = [
    {
      title: '功能信息',
      key: 'functionInfo',
      render: (_, record) => (
        <Space>
          <ThunderboltOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              键: <Text code>{record.key}</Text>
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              {record.description || '无描述'}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '最大强度',
      dataIndex: 'maxIntensity',
      render: (maxIntensity) => (
        <Tag color="orange">{maxIntensity} 级</Tag>
      ),
    },
    {
      title: '关联指令',
      key: 'commands',
      render: (_, record) => (
        <div>
          {record.commandCount > 0 ? (
            <div>
              <Tag color="blue">{record.commandCount} 个指令</Tag>
              <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                <Button 
                  type="link" 
                  size="small"
                  style={{ padding: 0, height: 'auto' }}
                  onClick={() => handleViewDetail(record)}
                >
                  查看详情
                </Button>
                <span style={{ margin: '0 4px' }}>|</span>
                <Button 
                  type="link" 
                  size="small"
                  style={{ padding: 0, height: 'auto' }}
                  onClick={() => handleManageCommands(record)}
                >
                  管理指令
                </Button>
              </div>
            </div>
          ) : (
            <div>
              <Tag color="default">无指令</Tag>
              <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                <Button 
                  type="link" 
                  size="small"
                  style={{ padding: 0, height: 'auto' }}
                  onClick={() => handleManageCommands(record)}
                >
                  添加指令
                </Button>
              </div>
            </div>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="指令管理">
            <Button 
              type="link" 
              icon={<SettingOutlined />}
              onClick={() => handleManageCommands(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个设备功能吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        <ThunderboltOutlined /> 设备功能管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总功能数"
              value={stats.total}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="启用功能"
              value={stats.active}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="禁用功能"
              value={stats.inactive}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索功能名称、键或描述"
            style={{ width: 240 }}
            value={searchParams.keyword}
            onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={(value) => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          
          <Button onClick={handleReset}>
            重置
          </Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增功能
          </Button>
        </Space>
      </Card>

      {/* 功能列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={functions}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 创建/编辑功能模态框 */}
      <Modal
        title={editingFunction ? '编辑设备功能' : '新增设备功能'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="功能名称"
            rules={[{ required: true, message: '请输入功能名称' }]}
          >
            <Input placeholder="例如：震动" />
          </Form.Item>

          <Form.Item
            name="key"
            label="功能键"
            rules={[{ required: true, message: '请输入功能键' }]}
          >
            <Input placeholder="例如：vibrate" disabled={!!editingFunction} />
          </Form.Item>

          <Form.Item
            name="description"
            label="功能描述"
          >
            <TextArea 
              rows={2}
              placeholder="描述这个功能的用途..."
            />
          </Form.Item>

          <Form.Item
            name="maxIntensity"
            label="最大强度等级"
            rules={[{ required: true, message: '请输入最大强度等级' }]}
          >
            <InputNumber min={1} max={10} placeholder="例如：3" style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingFunction ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 指令管理模态框 */}
      <Modal
        title={`指令管理 - ${selectedFunction?.name}`}
        open={commandModalVisible}
        onCancel={() => setCommandModalVisible(false)}
        footer={null}
        width={800}
      >
        <div>
          <Card size="small" style={{ marginBottom: 16 }}>
            <Title level={5}>添加指令关联</Title>
            <Form
              form={commandForm}
              layout="inline"
              onFinish={handleAddCommand}
            >
              <Form.Item
                name="commandSetId"
                rules={[{ required: true, message: '请选择指令集' }]}
              >
                <Select 
                  placeholder="选择指令集" 
                  style={{ width: 400 }}
                  showSearch
                  filterOption={(input, option) => {
                    const commandSet = commandSets.find(cs => cs.id === option?.value)
                    if (!commandSet) return false
                    
                    const searchText = input.toLowerCase()
                    return (
                      commandSet.name.toLowerCase().includes(searchText) ||
                      (commandSet.description || '').toLowerCase().includes(searchText) ||
                      commandSet.command.toLowerCase().includes(searchText)
                    )
                  }}
                >
                  {commandSets.map(cs => (
                    <Select.Option key={cs.id} value={cs.id} title={cs.description}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <div>
                          <div style={{ fontWeight: 500 }}>{cs.name}</div>
                          <div style={{ fontSize: '12px', color: '#666' }}>
                            {cs.description || '无描述'}
                          </div>
                        </div>
                        <div style={{ fontSize: '12px', color: '#999', fontFamily: 'monospace' }}>
                          {cs.command?.substring(0, 20)}{cs.command?.length > 20 ? '...' : ''}
                        </div>
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>

              <Form.Item
                name="intensity"
                rules={[{ required: true, message: '请输入强度等级' }]}
                extra="-1表示停止，0表示关闭，1及以上表示强度等级"
              >
                <InputNumber 
                  min={-1} 
                  max={selectedFunction?.maxIntensity || 10}
                  placeholder="强度等级 (-1停止)" 
                  style={{ width: 150 }} 
                />
              </Form.Item>

              <Form.Item name="description">
                <Input placeholder="描述（可选）" style={{ width: 150 }} />
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<LinkOutlined />}>
                  添加关联
                </Button>
              </Form.Item>
            </Form>
          </Card>

          <Divider />

          <Title level={5}>已关联指令</Title>
          {selectedFunction?.commands && selectedFunction.commands.length > 0 ? (
            <List
              dataSource={selectedFunction.commands}
              renderItem={(command) => (
                <List.Item
                  actions={[
                    <Popconfirm
                      key="delete"
                      title="确定删除这个指令关联吗？"
                      onConfirm={() => handleRemoveCommand(command.id)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button 
                        type="link" 
                        danger 
                        icon={<DisconnectOutlined />}
                      >
                        删除关联
                      </Button>
                    </Popconfirm>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<Badge count={command.intensity} />}
                    title={command.commandSet?.name || '未知指令集'}
                    description={
                      <div>
                        <div>指令: <Text code>{command.commandSet?.command}</Text></div>
                        {command.description && <div>描述: {command.description}</div>}
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          ) : (
            <Empty description="暂无关联指令" />
          )}
        </div>
      </Modal>
    </div>
  )
}

export default FunctionsManagement
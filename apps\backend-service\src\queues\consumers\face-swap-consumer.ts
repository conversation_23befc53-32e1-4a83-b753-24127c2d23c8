import type { Env } from '@/types/env'
import type {
  FaceSwapTask,
  FaceSwapProgress,
  ReplicatePredictionRequest,
  ReplicatePredictionResponse,
  ReplicatePredictionStatus
} from '@/types/face-swap'
import type {
  ImageAttachment,
  GeneratingStatusAttachment,
  CompletedImageAttachment
} from '@/types/image'
import { getMessageById, updateMessageAttachments } from '@/lib/db/queries/chat'
import { QueueErrorHandler } from '../utils/queue-handler'
import { updateMediaGeneration, getMediaGenerationById } from '@/lib/db/queries/media-generation'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { uploadToR2, getR2ConfigFromEnv, IMAGE_UPLOAD_OPTIONS } from '@/lib/utils/r2-upload'

const CONFIG = {
  endpoint: 'c2a3we84tjaoip',
  token: 'SD_2RGdTZtC0WcZku0bJjA'
}

/**
 * 换脸队列消费者
 * 处理换脸任务，调用 Replicate API，更新消息状态
 */
export class FaceSwapQueueConsumer {
  private readonly REPLICATE_MODEL_VERSION =
    'd1d6ea8c8be89d664a07a457526f7128109dee7030fdac424788d762c71ed111'

  constructor(private env: Env) {}

  /**
   * 生成随机 seed
   */
  private generateRandomSeed(): number {
    // 生成一个 10-13 位的随机数字作为 seed
    return Math.floor(Math.random() * 9000000000000) + 1000000000000
  }

  /**
   * 检查字符串是否是有效的 UUID 格式
   */
  private isValidUUID(str: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
    return uuidRegex.test(str)
  }

  /**
   * 处理单个换脸任务
   */
  async process(task: FaceSwapTask): Promise<void> {
    console.log('🔄 开始处理任务:', task.taskId)

    // 获取媒体生成记录ID
    const mediaGenerationId = task.metadata?.mediaGenerationId

    if (mediaGenerationId) {
      // 更新媒体生成记录为处理中状态
      try {
        await updateMediaGeneration(this.env, mediaGenerationId, {
          status: 'processing'
        })
      } catch (error) {
        console.error('❌ 更新失败:', error)
      }
    }

    try {
      // 检查是否需要先生成图片（如果 inputImageUrl 是 placeholder）
      let finalInputImageUrl = task.inputImageUrl

      if (task.inputImageUrl === 'placeholder' && task.metadata?.originalPrompt) {
        // 第一步：生成图片
        console.log('🎨 第一步：生成图片')
        await this.updateFaceSwapProgress(
          task.messageId,
          {
            status: 'starting',
            progress: 10,
            message: '正在生成图片...'
          },
          mediaGenerationId
        )

        finalInputImageUrl = await this.generateImageWithInstaSD(task, mediaGenerationId)

        await this.updateFaceSwapProgress(
          task.messageId,
          {
            status: 'processing',
            progress: 50,
            // message: '图片生成完成，开始换脸...'
            message: '进度 50%...'
          },
          mediaGenerationId
        )
      } else {
        // 直接开始换脸
        await this.updateFaceSwapProgress(
          task.messageId,
          {
            status: 'starting',
            progress: 10,
            message: '正在连接换脸服务...'
          },
          mediaGenerationId
        )
      }

      // 第二步：换脸处理
      if (task.swapImageUrl && finalInputImageUrl !== 'placeholder') {
        console.log('🔄 第二步：换脸处理')
        const replicatePredictionId = await this.startReplicateFaceSwap({
          ...task,
          inputImageUrl: finalInputImageUrl
        })

        await this.updateFaceSwapProgress(
          task.messageId,
          {
            status: 'processing',
            progress: 70,
            message: '正在渲染...'
          },
          mediaGenerationId
        )

        const result = await this.waitForReplicateCompletion(
          replicatePredictionId,
          task.messageId,
          mediaGenerationId
        )

        if (result.status === 'succeeded' && result.imageUrl) {
          // 先上传到 R2
          const finalImageUrl = await this.downloadAndUploadToR2(result.imageUrl, task.taskId)

          await this.updateCompletedImage(task.messageId, finalImageUrl, task.taskId)

          // 更新媒体生成记录为完成状态
          if (mediaGenerationId) {
            try {
              await updateMediaGeneration(this.env, mediaGenerationId, {
                status: 'completed',
                outputUrls: [finalImageUrl],
                completedAt: new Date()
              })
            } catch (error) {
              console.error('❌ 更新失败:', error)
            }
          }

          console.log('✅ 换脸完成')
        } else {
          await this.updateFailureStatus(task.messageId, result.errorMessage || '换脸失败')

          // 更新媒体生成记录为失败状态
          if (mediaGenerationId) {
            try {
              await updateMediaGeneration(this.env, mediaGenerationId, {
                status: 'failed',
                errorMessage: result.errorMessage || '换脸失败'
              })
            } catch (error) {
              console.error('❌ 更新失败:', error)
            }
          }

          throw new Error(result.errorMessage || '换脸失败')
        }
      } else {
        // 只生成图片，不换脸
        // 先上传到 R2
        const finalImageUrl = await this.downloadAndUploadToR2(finalInputImageUrl, task.taskId)

        await this.updateCompletedImage(task.messageId, finalImageUrl, task.taskId)

        // 更新媒体生成记录为完成状态
        if (mediaGenerationId) {
          try {
            await updateMediaGeneration(this.env, mediaGenerationId, {
              status: 'completed',
              outputUrls: [finalImageUrl],
              completedAt: new Date()
            })
          } catch (error) {
            console.error('❌ 更新失败:', error)
          }
        }

        console.log('✅ 图片生成完成')
      }
    } catch (error) {
      const errorMessage = QueueErrorHandler.handleTaskError(task.taskId, error)
      await this.updateFailureStatus(task.messageId, errorMessage)

      // 更新媒体生成记录为失败状态
      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'failed',
            errorMessage
          })
        } catch (updateError) {
          console.error('❌ 更新失败:', updateError)
        }
      }

      // 换脸失败，退还积分
      await this.refundPointsForFailedGeneration(task.userId, task.taskId)

      throw new Error(`换脸失败: ${errorMessage}`)
    }
  }

  /**
   * 启动 Replicate 换脸任务
   */
  private async startReplicateFaceSwap(task: FaceSwapTask): Promise<string> {
    const request: ReplicatePredictionRequest = {
      version: this.REPLICATE_MODEL_VERSION,
      input: {
        swap_image: task.swapImageUrl,
        input_image: task.inputImageUrl
      }
    }

    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.env.REPLICATE_API_TOKEN}`,
        Prefer: 'wait'
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(60000) // 60秒超时
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Replicate API请求失败: ${response.status} ${errorText}`)
    }

    const result: ReplicatePredictionResponse = await response.json()
    console.log('✅ Replicate任务启动:', result.id)

    return result.id
  }

  /**
   * 等待 Replicate 任务完成
   */
  private async waitForReplicateCompletion(
    predictionId: string,
    messageId: string,
    mediaGenerationId?: string
  ): Promise<{ status: string; imageUrl?: string; errorMessage?: string }> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ 轮询Replicate状态:', predictionId)

    let progressStep = 40 // 从40%开始
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(`https://api.replicate.com/v1/predictions/${predictionId}`, {
          headers: {
            Authorization: `Bearer ${this.env.REPLICATE_API_TOKEN}`
          }
        })

        if (!response.ok) {
          consecutiveErrors++
          console.warn(`⚠️ 查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`, response.status)

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const prediction: ReplicatePredictionStatus = await response.json()

        // 更新进度
        if (prediction.status === 'starting' || prediction.status === 'processing') {
          progressStep = Math.min(progressStep + 5, 90)
          await this.updateFaceSwapProgress(
            messageId,
            {
              status: 'processing',
              progress: progressStep,
              message: '换脸中...'
            },
            mediaGenerationId
          )
        }

        // 检查完成状态
        if (prediction.status === 'succeeded') {
          const imageUrl = Array.isArray(prediction.output)
            ? prediction.output[0]
            : prediction.output

          return {
            status: 'succeeded',
            imageUrl: imageUrl as string,
            errorMessage: undefined
          }
        }

        if (prediction.status === 'failed' || prediction.status === 'canceled') {
          return {
            status: 'failed',
            imageUrl: undefined,
            errorMessage: prediction.error || '换脸失败'
          }
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(`❌ 轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`, error)

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('等待超时')
  }

  /**
   * 使用 InstantSD 生成图片（第一步）
   */
  private async generateImageWithInstaSD(
    task: FaceSwapTask,
    mediaGenerationId?: string
  ): Promise<string> {
    const request = {
      inputs: {
        '79facdf6ca0558a0': {
          title: 'width',
          value: task.metadata?.width || 1024
        },
        a582029f7614ff66: {
          title: 'height',
          value: task.metadata?.height || 1440
        },
        '8bab993458e5b954': {
          title: 'prompt',
          value: task.metadata?.originalPrompt || ''
        },
        '8bded2ca4cae6ff6': {
          title: 'seed',
          value: this.generateRandomSeed()
        }
      }
    }

    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${CONFIG.endpoint}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${CONFIG.token}`
        },
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(60000)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`InstantSD API请求失败: ${response.status} ${errorText}`)
    }

    const result = (await response.json()) as { task_id: string }
    const taskId = result.task_id
    console.log('✅ InstantSD任务启动:', taskId)

    // 等待图片生成完成
    return await this.waitForInstantSDCompletion(taskId, task.messageId, mediaGenerationId)
  }

  /**
   * 等待 InstantSD 任务完成
   */
  private async waitForInstantSDCompletion(
    taskId: string,
    messageId: string,
    mediaGenerationId?: string
  ): Promise<string> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ 轮询InstantSD状态:', taskId)

    let progressStep = 20 // 从20%开始
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(
          `https://api.instasd.com/api_endpoints/${CONFIG.endpoint}/task_status/${taskId}`,
          {
            headers: {
              Authorization: `Bearer ${CONFIG.token}`
            }
          }
        )

        if (!response.ok) {
          consecutiveErrors++
          console.warn(
            `⚠️ InstantSD查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`,
            response.status
          )

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('InstantSD查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const task = (await response.json()) as {
          status: string
          image_urls?: string[]
          error_message?: string
        }

        // 更新进度
        if (task.status === 'IN_PROGRESS' || task.status === 'EXECUTING') {
          progressStep = Math.min(progressStep + 5, 45)
          await this.updateFaceSwapProgress(
            messageId,
            {
              status: 'processing',
              progress: progressStep,
              message: '生成图片中...'
            },
            mediaGenerationId
          )
        }

        // 检查完成状态
        if (task.status === 'COMPLETED') {
          const imageUrl = task.image_urls?.[0]
          if (!imageUrl) {
            throw new Error('InstantSD未返回图片URL')
          }
          console.log('✅ InstantSD图片生成完成:', imageUrl)
          return imageUrl
        }

        if (task.status === 'FAILED') {
          throw new Error(task.error_message || 'InstantSD图片生成失败')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(`❌ InstantSD轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`, error)

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('InstantSD轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('InstantSD等待超时')
  }

  /**
   * 更新换脸进度
   */
  private async updateFaceSwapProgress(
    messageId: string,
    progress: Omit<FaceSwapProgress, 'messageId' | 'timestamp'>,
    mediaGenerationId?: string
  ): Promise<void> {
    console.log('📊 更新进度:', progress.progress + '%')

    // 如果有媒体生成记录ID，更新进度到数据库
    if (mediaGenerationId) {
      try {
        // 先获取现有的媒体生成记录，保留原有的 metadata
        const currentRecord = await getMediaGenerationById(this.env, mediaGenerationId)
        const existingMetadata = currentRecord?.metadata || {}

        await updateMediaGeneration(this.env, mediaGenerationId, {
          metadata: {
            ...existingMetadata, // 保留原有的 metadata
            progress: progress.progress,
            status: progress.status,
            message: progress.message,
            timestamp: new Date().toISOString()
          }
        })
        console.log('📊 媒体生成记录进度已更新:', progress.progress + '%')
      } catch (error) {
        console.warn('⚠️ 更新媒体生成记录进度失败:', error)
      }
    }

    // 检查是否是 UUID 格式，如果不是则跳过消息更新
    if (!this.isValidUUID(messageId)) {
      console.log('📊 非UUID格式的messageId，跳过消息更新（可能是写真集生成任务）')
      return
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在，跳过消息更新')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除之前的生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 添加新的状态附件
      const statusAttachment: GeneratingStatusAttachment = {
        url: `generating://${progress.status}`,
        name: progress.message,
        contentType: 'image/generating',
        metadata: {
          status: progress.status as any,
          progress: progress.progress,
          timestamp: new Date().toISOString(),
          taskId: messageId // 使用 messageId 作为 taskId
        }
      }

      const updatedAttachments = [...filteredAttachments, statusAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.warn('⚠️ 更新进度失败')
    }
  }

  /**
   * 更新完成的图片
   */
  private async updateCompletedImage(
    messageId: string,
    imageUrl: string,
    taskId: string
  ): Promise<void> {
    console.log('🎉 图片生成完成')

    // 检查是否是 UUID 格式，如果不是则跳过消息更新
    if (!this.isValidUUID(messageId)) {
      console.log('🎉 非UUID格式的messageId，跳过消息更新（可能是写真集生成任务）')
      return
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在，跳过消息更新')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件，添加真实图片附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      const imageAttachment: CompletedImageAttachment = {
        url: imageUrl,
        name: `face-swap-${Date.now()}.png`,
        contentType: 'image/png',
        metadata: {
          taskId,
          generatedAt: new Date().toISOString()
        }
      }

      const updatedAttachments = [...filteredAttachments, imageAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })
    } catch (error) {
      console.error('❌ 更新图片失败')
      throw error
    }
  }

  /**
   * 更新失败状态
   */
  private async updateFailureStatus(messageId: string, error: string): Promise<void> {
    console.log('❌ 失败:', error)

    // 检查是否是 UUID 格式，如果不是则跳过消息更新
    if (!this.isValidUUID(messageId)) {
      console.log('❌ 非UUID格式的messageId，跳过消息更新（可能是写真集生成任务）')
      return
    }

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在，跳过消息更新')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: ImageAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as ImageAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: ImageAttachment) => !att.contentType?.startsWith('image/generating')
      )

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: filteredAttachments
      })
    } catch (updateError) {
      console.warn('⚠️ 更新状态失败')
    }
  }

  /**
   * 退还积分（换脸失败时）
   */
  private async refundPointsForFailedGeneration(userId: string, taskId: string): Promise<void> {
    try {
      const pointsManager = createServicePointsManager(this.env)

      // 换脸固定消费10积分
      const pointsToRefund = 10

      // 使用积分退还方法
      const refundResult = await pointsManager.refundPoints(userId, {
        amount: pointsToRefund,
        source: 'refund',
        sourceId: taskId,
        description: `换脸失败退还 - 任务ID: ${taskId}`
      })

      if (refundResult.success) {
        console.log(`✅ 退还积分 ${pointsToRefund}`)
      } else {
        console.error(`❌ 退还失败: ${refundResult.error}`)
      }
    } catch (error) {
      console.error('退还异常:', error)
    }
  }

  /**
   * 下载图片并上传到 R2
   */
  private async downloadAndUploadToR2(imageUrl: string, taskId: string): Promise<string> {
    try {
      console.log('📸 [R2上传] 开始下载图片:', imageUrl)

      // 1. 下载图片
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
      }

      const imageBuffer = await response.arrayBuffer()
      console.log('📸 [R2上传] 图片下载完成，大小:', imageBuffer.byteLength, '字节')

      // 2. 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        console.error('❌ [R2上传] R2 配置缺失，使用原始 URL')
        return imageUrl
      }

      console.log('📸 [R2上传] R2 配置获取成功')

      // 3. 生成文件名
      const timestamp = new Date().toISOString().split('T')[0]
      const uniqueId = taskId.split('-').pop() || 'unknown'
      const fileName = `face-swap-${uniqueId}.png`

      // 4. 上传到 R2
      const uploadResult = await uploadToR2(imageBuffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName,
        folder: `face-swap/${timestamp}`
      })

      if (!uploadResult.success) {
        console.error('❌ [R2上传] 失败，使用原始 URL:', uploadResult.error)
        return imageUrl
      }

      console.log('✅ [R2上传] 上传成功:', {
        originalUrl: imageUrl,
        r2Url: uploadResult.url,
        key: uploadResult.key,
        size: uploadResult.size
      })

      return uploadResult.url!
    } catch (error) {
      console.error('❌ [R2上传] 异常，使用原始 URL:', error)
      return imageUrl
    }
  }
}

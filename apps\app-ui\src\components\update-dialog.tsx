import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Progress,
  Card,
  CardBody
} from '@heroui/react'
import type { UpdateCheckResult, UpdateProgress } from '@/services/update-manager'
import { apkUpdater } from '@/services/apk-updater'

interface UpdateDialogProps {
  isOpen: boolean
  updateInfo: UpdateCheckResult | null
  updateProgress: UpdateProgress | null
  isForced: boolean
  onUpdate: () => void
  onCancel: () => void
  onDismiss: () => void
}

export const UpdateDialog: React.FC<UpdateDialogProps> = ({
  isOpen,
  updateInfo,
  updateProgress,
  isForced,
  onUpdate,
  onCancel,
  onDismiss
}) => {
  if (!updateInfo) return null

  // 确定显示哪种更新
  const primaryUpdate = updateInfo.hasApkUpdate ? updateInfo.apkUpdate : updateInfo.hotfixUpdate
  const updateType = updateInfo.hasApkUpdate ? 'APK' : '热更新'

  const renderProgressContent = () => {
    if (!updateProgress) return null

    const canCancel =
      updateProgress.type === 'apk' &&
      updateProgress.status === 'downloading' &&
      apkUpdater.isDownloading()

    return (
      <div className="text-center space-y-4">
        <h2 className="text-xl font-semibold">正在更新...</h2>
        <p className="text-default-600">{updateProgress.message}</p>

        <Progress
          value={updateProgress.progress}
          className="w-full"
          color="primary"
          showValueLabel
        />

        <p className="text-sm text-default-500">{Math.round(updateProgress.progress)}% 完成</p>

        {canCancel && (
          <Button
            variant="light"
            color="danger"
            size="sm"
            onPress={() => {
              apkUpdater.cancelDownload()
              onCancel()
            }}
          >
            取消下载
          </Button>
        )}
      </div>
    )
  }

  const renderUpdateContent = () => {
    return (
      <div className="space-y-4">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-2">发现新版本</h2>
          <p className="text-default-600">
            {isForced
              ? '检测到重要更新，需要立即更新以继续使用应用。'
              : '发现新版本，建议立即更新以获得更好的体验。'}
          </p>
        </div>

        {primaryUpdate && (
          <Card>
            <CardBody className="space-y-3">
              <div className="flex justify-between">
                <span className="text-default-600">更新类型:</span>
                <span className="font-medium">{updateType}</span>
              </div>

              <div className="flex justify-between">
                <span className="text-default-600">版本号:</span>
                <span className="font-medium">{primaryUpdate.version.versionName}</span>
              </div>

              {primaryUpdate.version.fileSize && (
                <div className="flex justify-between">
                  <span className="text-default-600">文件大小:</span>
                  <span className="font-medium">
                    {(primaryUpdate.version.fileSize / 1024 / 1024).toFixed(1)} MB
                  </span>
                </div>
              )}

              {primaryUpdate.version.releaseNotes && (
                <div>
                  <div className="text-default-600 mb-1">更新说明:</div>
                  <p className="text-sm">{primaryUpdate.version.releaseNotes}</p>
                </div>
              )}
            </CardBody>
          </Card>
        )}
      </div>
    )
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={isForced ? undefined : onDismiss}
      isDismissable={!isForced}
      placement="center"
      backdrop="blur"
      size="md"
    >
      <ModalContent>
        <ModalHeader>
          {updateProgress ? '正在更新' : isForced ? '强制更新' : '版本更新'}
        </ModalHeader>

        <ModalBody>{updateProgress ? renderProgressContent() : renderUpdateContent()}</ModalBody>

        {!updateProgress && (
          <ModalFooter>
            <div className="flex gap-2 w-full">
              {!isForced && (
                <Button variant="light" onPress={onCancel} className="flex-1">
                  稍后提醒
                </Button>
              )}

              <Button color="primary" onPress={onUpdate} className="flex-1">
                立即更新
              </Button>
            </div>
          </ModalFooter>
        )}
      </ModalContent>
    </Modal>
  )
}

DROP INDEX IF EXISTS "idx_user_points_cycle_end";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "cycle_start_date";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "cycle_end_date";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "membership_level";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "monthly_allocation";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "cycle_consumed";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "cycle_gifted";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "cycle_received";--> statement-breakpoint
ALTER TABLE "UserPoints" DROP COLUMN IF EXISTS "last_cycle_check";
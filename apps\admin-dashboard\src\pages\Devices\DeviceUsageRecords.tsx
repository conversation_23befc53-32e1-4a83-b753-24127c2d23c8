import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  DatePicker,
  Typography,
  Avatar,
  Row,
  Col,
  Statistic,
  Tooltip
} from 'antd'
import {
  SearchOutlined,
  ExportOutlined,
  UserOutlined,
  TabletOutlined,
  PlayCircleOutlined,
  Clock<PERSON>ircleOutlined,
  <PERSON>boltOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { DeviceUsage } from '@/services/devices'
import { deviceService } from '@/services/devices'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'
import duration from 'dayjs/plugin/duration'

dayjs.extend(duration)

const { Title } = Typography
const { RangePicker } = DatePicker

const DeviceUsageRecords: React.FC = () => {
  const [usageRecords, setUsageRecords] = useState<DeviceUsage[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)

  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    deviceId: undefined as string | undefined,
    userId: undefined as string | undefined,
    startDate: undefined as string | undefined,
    endDate: undefined as string | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    totalDevices: 0,
    activeDevices: 0,
    onlineDevices: 0,
    todayUsage: 0,
    monthlyUsage: 0
  })

  useEffect(() => {
    loadUsageRecords()
    loadStats()
  }, [currentPage, pageSize, searchParams])

  const loadUsageRecords = async () => {
    try {
      setLoading(true)

      const response = await deviceService.getDeviceUsage({
        page: currentPage,
        pageSize,
        ...searchParams
      })

      if (response.success && response.data) {
        setUsageRecords(response.data.data)
        setTotal(response.data.total)
      }
    } catch (error) {
      console.error('获取使用记录失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await deviceService.getDeviceStats()

      if (response.success && response.data) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      deviceId: undefined,
      userId: undefined,
      startDate: undefined,
      endDate: undefined
    })
    setCurrentPage(1)
  }

  const handleDateRangeChange = (dates: any) => {
    if (dates && dates.length === 2) {
      setSearchParams({
        ...searchParams,
        startDate: dates[0]?.toISOString(),
        endDate: dates[1]?.toISOString()
      })
    } else {
      setSearchParams({
        ...searchParams,
        startDate: undefined,
        endDate: undefined
      })
    }
  }

  const formatDuration = (seconds?: number) => {
    if (!seconds) return '-'
    const d = dayjs.duration(seconds, 'seconds')
    const hours = d.hours()
    const minutes = d.minutes()
    const secs = d.seconds()

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  const getIntensityColor = (intensity: number) => {
    if (intensity <= 2) return '#52c41a' // 绿色 - 低强度
    if (intensity <= 5) return '#faad14' // 橙色 - 中强度
    if (intensity <= 8) return '#fa8c16' // 深橙色 - 高强度
    return '#f5222d' // 红色 - 极高强度
  }

  const getIntensityText = (intensity: number) => {
    if (intensity <= 2) return '低强度'
    if (intensity <= 5) return '中强度'
    if (intensity <= 8) return '高强度'
    return '极高强度'
  }

  const columns: ColumnsType<DeviceUsage> = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <div style={{ fontWeight: 500 }}>
              {record.user?.email || `用户${record.userId ? record.userId.slice(-6) : '未知'}`}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              ID: {record.userId ? record.userId.slice(-8) : '未知'}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '设备信息',
      key: 'deviceInfo',
      render: (_, record) => (
        <Space>
          <TabletOutlined style={{ color: '#1890ff' }} />
          <div>
            <div style={{ fontWeight: 500 }}>
              {record.device?.name || `设备${record.deviceId.slice(-6)}`}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              码: {record.device?.deviceCode || record.deviceId.slice(-8)}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              {record.device?.brand} {record.device?.model}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '使用功能',
      dataIndex: 'functionKey',
      render: functionKey => (
        <Space>
          <ThunderboltOutlined style={{ color: '#722ed1' }} />
          <Tag color="purple">{functionKey}</Tag>
        </Space>
      )
    },
    {
      title: '强度等级',
      dataIndex: 'intensity',
      render: intensity => (
        <Tooltip title={getIntensityText(intensity)}>
          <Tag color={getIntensityColor(intensity)} style={{ fontWeight: 500 }}>
            {intensity} 级
          </Tag>
        </Tooltip>
      ),
      sorter: (a, b) => a.intensity - b.intensity
    },
    {
      title: '使用时长',
      dataIndex: 'duration',
      render: duration => (
        <Space>
          <ClockCircleOutlined style={{ color: '#52c41a' }} />
          <span style={{ fontWeight: 500 }}>{formatDuration(duration)}</span>
        </Space>
      ),
      sorter: (a, b) => (a.duration || 0) - (b.duration || 0)
    },
    {
      title: '关联剧本',
      key: 'script',
      render: (_, record) =>
        record.script ? (
          <Space>
            <PlayCircleOutlined style={{ color: '#fa8c16' }} />
            <span>{record.script.title}</span>
          </Space>
        ) : (
          <Tag color="default">手动使用</Tag>
        )
    },
    {
      title: '开始时间',
      dataIndex: 'startedAt',
      render: date => (
        <div>
          <div>{dayjs(date).format('YYYY-MM-DD')}</div>
          <div style={{ color: '#999', fontSize: '12px' }}>{dayjs(date).format('HH:mm:ss')}</div>
        </div>
      ),
      sorter: (a, b) => dayjs(a.startedAt).unix() - dayjs(b.startedAt).unix()
    },
    {
      title: '结束时间',
      dataIndex: 'endedAt',
      render: date =>
        date ? (
          <div>
            <div>{dayjs(date).format('YYYY-MM-DD')}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>{dayjs(date).format('HH:mm:ss')}</div>
          </div>
        ) : (
          <Tag color="processing">进行中</Tag>
        )
    },
    {
      title: '会话ID',
      dataIndex: 'sessionId',
      render: sessionId =>
        sessionId ? (
          <Tooltip title={sessionId}>
            <span style={{ fontFamily: 'monospace', color: '#666' }}>{sessionId.slice(-8)}</span>
          </Tooltip>
        ) : (
          '-'
        )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        设备使用记录
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总设备数"
              value={stats.totalDevices}
              prefix={<TabletOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="在线设备"
              value={stats.onlineDevices}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日使用次数"
              value={stats.todayUsage}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="本月使用次数"
              value={stats.monthlyUsage}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索用户ID"
            style={{ width: 150 }}
            value={searchParams.userId}
            onChange={e => setSearchParams({ ...searchParams, userId: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Input
            placeholder="搜索设备ID"
            style={{ width: 150 }}
            value={searchParams.deviceId}
            onChange={e => setSearchParams({ ...searchParams, deviceId: e.target.value })}
            onPressEnter={handleSearch}
          />

          <RangePicker
            placeholder={['开始时间', '结束时间']}
            showTime
            onChange={handleDateRangeChange}
            style={{ width: 300 }}
          />

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button icon={<ExportOutlined />}>导出记录</Button>
        </Space>
      </Card>

      {/* 使用记录列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={usageRecords}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
          scroll={{ x: 1200 }}
        />
      </Card>
    </div>
  )
}

export default DeviceUsageRecords

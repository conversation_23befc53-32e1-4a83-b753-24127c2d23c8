/**
 * ElevenLabs 集成服务类型定义
 * 优化版本，专为 backend-service 设计
 */

export interface ElevenLabsAccount {
  id: string
  email: string
  password: string
  sessionToken?: string
  refreshToken?: string
  tokenExpiry?: Date
  isActive: boolean
  lastUsed?: Date
  failureCount: number
  // 新增：性能优化字段
  consecutiveSuccesses?: number
  avgResponseTime?: number
  lastHealthCheck?: Date
}

export interface SessionInfo {
  token: string
  refreshToken?: string
  expiry: Date
  userId: string
  // 新增：缓存优化
  createdAt: Date
  lastValidated?: Date
}

export interface V3GenerateRequest {
  inputs: Array<{
    text: string
    voice_id: string
  }>
  model_id: string
  settings: {
    stability: number
    use_speaker_boost: boolean
  }
}

export interface V3GenerateResponse {
  audio_url?: string
  audio_data?: ArrayBuffer
  task_id?: string
  status: 'completed' | 'processing' | 'failed'
  error?: string
}

export interface LoginRequest {
  returnSecureToken: boolean
  email: string
  password: string
  clientType: string
}

export interface LoginResponse {
  kind: string
  localId: string
  email: string
  displayName: string
  idToken: string
  registered: boolean
  profilePicture?: string
  refreshToken: string
  expiresIn: string
}

export interface RefreshTokenRequest {
  grant_type: string
  refresh_token: string
  [key: string]: string
}

export interface RefreshTokenResponse {
  access_token: string
  expires_in: string
  token_type: string
  refresh_token: string
  id_token: string
  user_id: string
  project_id: string
}

export interface AccountHealth {
  accountId: string
  isHealthy: boolean
  lastCheck: Date
  errorCount: number
  lastError?: string
  responseTime?: number
  successRate?: number
}

export interface ElevenLabsServiceConfig {
  maxRetries: number
  retryDelayMs: number
  tokenRefreshThresholdMs: number
  healthCheckIntervalMs: number
  // 新增：性能优化配置
  accountCacheTimeMs: number
  sessionCacheTimeMs: number
  maxConcurrentRequests: number
  connectionTimeoutMs: number
  requestTimeoutMs: number
  enableRequestDeduplication: boolean
  enableConnectionPooling: boolean
}

export interface TTSRequest {
  text: string
  voice_id?: string
  model_id?: string
  stability?: number
  use_speaker_boost?: boolean
}

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
  // 新增：性能指标
  responseTime?: number
  fromCache?: boolean
  accountUsed?: string
}

export interface HealthCheckResponse {
  healthy: boolean
  total_accounts: number
  active_accounts: number
  failed_accounts: number
  details: AccountHealth[]
  // 新增：性能指标
  avgResponseTime?: number
  cacheHitRate?: number
  totalRequests?: number
}

// 新增：缓存相关类型
export interface CachedAccountSelection {
  accountId: string
  selectedAt: Date
  validUntil: Date
}

export interface RequestMetrics {
  startTime: number
  endTime?: number
  accountId?: string
  success?: boolean
  error?: string
  fromCache?: boolean
}

// 新增：连接池配置
export interface ConnectionPoolConfig {
  maxConnections: number
  maxIdleTime: number
  connectionTimeout: number
  retryAttempts: number
}

// 新增：请求去重配置
export interface RequestDeduplicationConfig {
  enabled: boolean
  keyGenerator: (request: TTSRequest) => string
  cacheTimeMs: number
}

export type AccountStatus = 'active' | 'inactive' | 'failed' | 'rate_limited' | 'maintenance'

// 新增：性能监控事件
export interface PerformanceEvent {
  type: 'account_selected' | 'session_created' | 'api_call' | 'cache_hit' | 'cache_miss'
  timestamp: Date
  accountId?: string
  duration?: number
  success?: boolean
  error?: string
  metadata?: Record<string, any>
}

// 新增：批量操作接口
export interface BatchAccountUpdate {
  accountId: string
  updates: Partial<ElevenLabsAccount>
}

export interface BatchSessionUpdate {
  accountId: string
  session: SessionInfo
}

import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  InputNumber,
  Switch,
  Upload,
  Image,
  Tooltip,
  Popconfirm,
  Typography,
  Row,
  Col,
  Statistic,
  Badge
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  UploadOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StarOutlined,
  UserOutlined,
  ClockCircleOutlined,
  SearchOutlined,
  ReloadOutlined,
  FileTextOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import { scriptService } from '@/services/script'
import type { Script, ScriptListParams, ScriptCreateParams, ScriptStats } from '@/types/script'
import { ScriptEditor } from '@/pages/Content'
import dayjs from 'dayjs'

const { Title } = Typography
const { TextArea } = Input

const ScriptManagement: React.FC = () => {
  const [scripts, setScripts] = useState<Script[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingScript, setEditingScript] = useState<Script | null>(null)
  const [contentEditorVisible, setContentEditorVisible] = useState(false)
  const [form] = Form.useForm()
  
  // 搜索条件
  const [searchParams, setSearchParams] = useState<ScriptListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState<ScriptStats>({
    totalScripts: 0,
    publicScripts: 0,
    privateScripts: 0,
    activeScripts: 0,
    premiumScripts: 0,
    totalUsage: 0,
    averageRating: 0
  })

  // 分类选项
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    loadScripts()
    loadStats()
    loadCategories()
  }, [currentPage, pageSize])

  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    } else {
      loadScripts()
    }
  }, [searchParams])

  const loadScripts = async () => {
    try {
      setLoading(true)
      
      const params = {
        page: currentPage,
        pageSize,
        ...searchParams
      }
      
      const response = await scriptService.getScripts(params)
      
      if (response.success && response.data) {
        setScripts(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取剧本列表失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取剧本列表失败:', error)
      message.error('获取剧本列表失败')
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await scriptService.getStats()
      
      if (response.success && response.data) {
        setStats(response.data)
      } else {
        console.error('获取统计数据失败:', response.message)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await scriptService.getCategories()
      
      if (response.success && response.data) {
        setCategories(response.data)
      } else {
        console.error('获取分类失败:', response.message)
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    setSearchParams({
      ...searchParams,
      page: 1
    })
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingScript(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = async (script: Script) => {
    try {
      setLoading(true)
      // 获取完整的剧本数据包括 content
      const response = await scriptService.getScript(script.id)
      
      if (response.success && response.data) {
        const fullScript = response.data
        setEditingScript(fullScript)
        form.setFieldsValue({
          ...fullScript,
          tags: fullScript.tags.join(', ')
        })
        setModalVisible(true)
      } else {
        message.error(response.message || '获取剧本详情失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取剧本详情失败:', error)
      message.error('获取剧本详情失败')
      setLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await scriptService.deleteScript(id)
      
      if (response.success) {
        message.success('删除成功')
        loadScripts()
        loadStats()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleToggleStatus = async (id: string, field: 'isActive' | 'isPublic', value: boolean) => {
    try {
      let response
      if (field === 'isActive') {
        response = await scriptService.toggleScriptStatus(id, value)
      } else {
        response = await scriptService.toggleScriptPublic(id, value)
      }
      
      if (response.success) {
        message.success(response.message || '状态更新成功')
        loadScripts()
        loadStats()
      } else {
        message.error(response.message || '状态更新失败')
      }
    } catch (error) {
      console.error('状态更新失败:', error)
      message.error('状态更新失败')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      const submitData: ScriptCreateParams = {
        ...values,
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()) : [],
        content: editingScript?.content || { commands: [], stages: [] }
      }

      let response
      if (editingScript) {
        response = await scriptService.updateScript({
          id: editingScript.id,
          ...submitData
        })
      } else {
        response = await scriptService.createScript(submitData)
      }
      
      if (response.success) {
        message.success(response.message || (editingScript ? '更新成功' : '创建成功'))
        setModalVisible(false)
        loadScripts()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('操作失败:', error)
      message.error('操作失败')
    }
  }

  const columns: ColumnsType<Script> = [
    {
      title: '封面',
      dataIndex: 'coverImage',
      width: 80,
      render: (coverImage) => (
        <Image
          src={coverImage}
          width={60}
          height={60}
          style={{ objectFit: 'cover', borderRadius: 4 }}
          fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG8XccyMnFMTuAd3BlOAC6HjgcMXFOKHVsgA1RSkGEGjGhFwSF4D2Q4IBcAPaEaGaVMrsQYGNnZfr4lNcm2"
        />
      )
    },
    {
      title: '剧本信息',
      key: 'info',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
            {record.title}
          </div>
          <div style={{ color: '#666', fontSize: '12px', marginBottom: 4 }}>
            {record.description.length > 50 
              ? `${record.description.substring(0, 50)}...` 
              : record.description}
          </div>
          <Space size="small">
            <Tag color="blue" icon={<ClockCircleOutlined />}>
              {record.duration}
            </Tag>
            <Tag color="green">
              {record.stageCount}阶段
            </Tag>
            <Tag color="purple">
              {record.category}
            </Tag>
          </Space>
        </div>
      )
    },
    {
      title: '标签',
      dataIndex: 'tags',
      width: 150,
      render: (tags: string[]) => (
        <div>
          {tags.map(tag => (
            <Tag key={tag} style={{ margin: '2px', fontSize: '12px' }}>
              {tag}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ marginBottom: 4 }}>
            <Badge 
              status={record.isActive ? 'success' : 'error'} 
              text={record.isActive ? '启用' : '禁用'}
            />
          </div>
          <div style={{ marginBottom: 4 }}>
            <Badge 
              status={record.isPublic ? 'processing' : 'default'} 
              text={record.isPublic ? '公开' : '私有'}
            />
          </div>
          {record.isPremium && (
            <Tag color="gold" style={{ fontSize: '12px' }}>
              会员专享
            </Tag>
          )}
        </div>
      )
    },
    {
      title: '统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>
            <UserOutlined /> {record.usageCount}次使用
          </div>
          <div style={{ marginTop: 4 }}>
            <StarOutlined /> {record.rating.toFixed(1)} 
            ({record.ratingCount}评分)
          </div>
          {record.pointsCost > 0 && (
            <div style={{ marginTop: 4, color: '#f50' }}>
              {record.pointsCost} 积分
            </div>
          )}
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      width: 120,
      render: (date) => dayjs(date).format('MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'actions',
      width: 250,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="编辑内容">
            <Button 
              type="link" 
              icon={<FileTextOutlined />} 
              size="small"
              onClick={async () => {
                try {
                  setLoading(true)
                  const response = await scriptService.getScript(record.id)
                  if (response.success && response.data) {
                    setEditingScript(response.data)
                    setContentEditorVisible(true)
                  } else {
                    message.error(response.message || '获取剧本详情失败')
                  }
                  setLoading(false)
                } catch (error) {
                  console.error('获取剧本详情失败:', error)
                  message.error('获取剧本详情失败')
                  setLoading(false)
                }
              }}
            />
          </Tooltip>
          <Tooltip title={record.isActive ? '禁用' : '启用'}>
            <Button 
              type="link" 
              icon={record.isActive ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              size="small"
              onClick={() => handleToggleStatus(record.id, 'isActive', !record.isActive)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个剧本吗？"
            onConfirm={() => handleDelete(record.id)}
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />} 
                size="small"
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        剧本管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="总剧本数"
              value={stats.totalScripts}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="公开剧本"
              value={stats.publicScripts}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="启用剧本"
              value={stats.activeScripts}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="会员专享"
              value={stats.premiumScripts}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="总使用次数"
              value={stats.totalUsage}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="平均评分"
              value={stats.averageRating}
              precision={1}
              suffix="/ 5.0"
              valueStyle={{ color: '#eb2f96' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索和筛选 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索剧本标题或描述"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />
          
          <Select
            placeholder="选择分类"
            style={{ width: 120 }}
            allowClear
            value={searchParams.category}
            onChange={(value) => setSearchParams({ ...searchParams, category: value })}
          >
            {categories.map(category => (
              <Select.Option key={category} value={category}>
                {category}
              </Select.Option>
            ))}
          </Select>

          <Select
            placeholder="公开状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isPublic}
            onChange={(value) => setSearchParams({ ...searchParams, isPublic: value })}
          >
            <Select.Option value={true}>公开</Select.Option>
            <Select.Option value={false}>私有</Select.Option>
          </Select>

          <Select
            placeholder="启用状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={(value) => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          
          <Button icon={<ReloadOutlined />} onClick={handleReset}>
            重置
          </Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增剧本
          </Button>
        </Space>
      </Card>

      {/* 剧本列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={scripts}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 新增/编辑剧本模态框 */}
      <Modal
        title={editingScript ? '编辑剧本' : '新增剧本'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" onClick={() => form.submit()}>
            {editingScript ? '更新' : '创建'}
          </Button>
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="title"
                label="剧本标题"
                rules={[{ required: true, message: '请输入剧本标题' }]}
              >
                <Input placeholder="请输入剧本标题" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category"
                label="剧本分类"
                rules={[{ required: true, message: '请选择剧本分类' }]}
              >
                <Select placeholder="请选择剧本分类">
                  {categories.map(category => (
                    <Select.Option key={category} value={category}>
                      {category}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="剧本描述"
            rules={[{ required: true, message: '请输入剧本描述' }]}
          >
            <TextArea rows={3} placeholder="请输入剧本描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="duration"
                label="剧本时长"
                rules={[{ required: true, message: '请输入剧本时长' }]}
              >
                <Input placeholder="如：15分钟" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="tags"
                label="标签"
                extra="多个标签用逗号分隔"
              >
                <Input placeholder="如：护士,医院,制服" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="coverImage"
            label="封面图片"
          >
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传封面</div>
              </div>
            </Upload>
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="pointsCost"
                label="积分消耗"
                initialValue={0}
              >
                <InputNumber 
                  min={0} 
                  style={{ width: '100%' }}
                  placeholder="免费请输入0"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="isPublic"
                label="公开状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="公开" unCheckedChildren="私有" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="isPremium"
                label="会员专享"
                valuePropName="checked"
                initialValue={false}
              >
                <Switch checkedChildren="是" unCheckedChildren="否" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="audioUrl"
            label="音频文件URL"
          >
            <Input placeholder="请输入音频文件URL（可选）" />
          </Form.Item>

          {editingScript && (
            <Form.Item
              label="剧本内容"
              extra="点击按钮编辑剧本的详细内容（指令、阶段、图片等）"
            >
              <Button 
                type="dashed" 
                icon={<EditOutlined />}
                onClick={() => setContentEditorVisible(true)}
                block
              >
                编辑剧本内容
              </Button>
            </Form.Item>
          )}
        </Form>
      </Modal>

      {/* 剧本内容编辑器 */}
      <Modal
        title="编辑剧本内容"
        open={contentEditorVisible}
        onCancel={() => setContentEditorVisible(false)}
        width="90%"
        style={{ top: 20 }}
        footer={null}
        destroyOnClose
      >
        {editingScript && (
          <ScriptEditor
            scriptId={editingScript.id}
            initialContent={editingScript.content}
            onSave={async (content) => {
              try {
                const response = await scriptService.updateScriptContent(editingScript.id, content)
                if (response.success) {
                  message.success('剧本内容保存成功')
                  setContentEditorVisible(false)
                  // 更新当前编辑的剧本对象
                  setEditingScript({
                    ...editingScript,
                    content
                  })
                } else {
                  message.error(response.message || '保存失败')
                }
              } catch (error) {
                console.error('保存失败:', error)
                message.error('保存失败')
              }
            }}
            onCancel={() => setContentEditorVisible(false)}
          />
        )}
      </Modal>
    </div>
  )
}

export default ScriptManagement
import { createMiddleware } from 'hono/factory';
import type { Env } from '@/types/env';

// CORS 中间件
export const corsMiddleware = createMiddleware<{ Bindings: Env }>(async (c, next) => {
  const origin = c.req.header('Origin');

  // 允许的源列表
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:3000',
    'https://localhost',
    'https://localhost:5173',
    'https://localhost:3000',
    'capacitor://localhost',
    'ionic://localhost',
    'file://',
    // 生产环境域名
    'https://pleasurehub.app',
    'https://www.pleasurehub.app',
    'https://demo.pleasurehub.app',
    'https://api.pleasurehub.app',
  ];

  // 确定允许的 origin
  let allowedOrigin = '*';
  if (origin) {
    if (allowedOrigins.includes(origin) || origin.match(/^https?:\/\/localhost(:\d+)?$/)) {
      allowedOrigin = origin;
    }
  }

  // 处理预检请求
  if (c.req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': allowedOrigin,
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Credentials': 'true',
        'Access-Control-Max-Age': '86400',
      },
    });
  }

  await next();

  // 添加 CORS 头
  c.res.headers.set('Access-Control-Allow-Origin', allowedOrigin);
  c.res.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  c.res.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  c.res.headers.set('Access-Control-Allow-Credentials', 'true');
});

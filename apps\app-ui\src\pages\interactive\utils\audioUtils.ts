/**
 * 音频工具函数
 * 用于处理音频初始化和解锁
 */

import { NetworkUtils } from '../../../utils/networkUtils'

/**
 * 检测当前浏览器是否支持自动播放
 * @returns Promise<boolean>
 */
export const checkAutoplaySupport = async (): Promise<boolean> => {
  try {
    // 创建一个临时的静音音频元素
    const audio = document.createElement('audio')
    audio.setAttribute(
      'src',
      'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA'
    )
    audio.setAttribute('playsinline', 'true')
    audio.muted = true
    audio.volume = 0

    // 尝试播放
    const playPromise = audio.play()
    if (playPromise !== undefined) {
      try {
        await playPromise
        return true // 自动播放成功
      } catch (err) {
        console.info('浏览器不支持静音自动播放:', err)
        return false
      }
    }
    return false // 浏览器不返回播放Promise，可能有兼容性问题
  } catch (e) {
    console.error('检测自动播放支持失败:', e)
    return false
  }
}

/**
 * 尝试解锁音频播放
 * 在用户交互后调用
 */
export const unlockAudio = (): Promise<boolean> => {
  return new Promise(resolve => {
    try {
      // 在 Web 环境下创建AudioContext并尝试恢复
      if (!NetworkUtils.isCapacitor()) {
        const AudioContext = window.AudioContext || (window as any).webkitAudioContext
        if (AudioContext) {
          const audioContext = new AudioContext()
          if (audioContext.state === 'suspended') {
            audioContext.resume().catch(err => {
              console.warn('恢复音频上下文失败:', err)
            })
          }
        }
      }

      // 尝试播放一个静音音频片段
      const tempAudio = new Audio()
      tempAudio.src =
        'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA'
      tempAudio.muted = true
      tempAudio.volume = 0
      tempAudio.setAttribute('playsinline', 'true')

      const playPromise = tempAudio.play()
      if (playPromise !== undefined) {
        playPromise
          .then(() => {
            tempAudio.pause()
            tempAudio.src = ''
            resolve(true)
          })
          .catch(err => {
            console.warn('音频解锁失败:', err)
            resolve(false)
          })
      } else {
        // 旧浏览器可能不返回Promise
        resolve(true)
      }

      // 设置超时，避免长时间等待
      setTimeout(() => {
        resolve(false)
      }, 3000)
    } catch (e) {
      console.error('解锁音频失败:', e)
      resolve(false)
    }
  })
}

/**
 * 预加载音频
 * @param audioSrc 音频URL
 * @returns Promise<HTMLAudioElement | null>
 */
export const preloadAudio = (audioSrc: string): Promise<HTMLAudioElement | null> => {
  return new Promise(resolve => {
    if (!audioSrc) {
      resolve(null)
      return
    }

    try {
      const audio = new Audio()
      audio.preload = 'auto'
      // 只在非 Capacitor 环境中设置 crossOrigin
      if (!NetworkUtils.isCapacitor()) {
        audio.crossOrigin = 'anonymous'
      }
      audio.setAttribute('playsinline', 'true')

      // 监听加载状态
      const onCanPlay = () => {
        cleanup()
        resolve(audio)
      }

      const onError = (e: Event) => {
        console.error('音频加载失败:', e.type, e.target?.constructor.name)
        console.error('音频加载错误:', { type: e.type, target: (e.target as HTMLElement)?.tagName })
        console.error('音频错误详情:', {
          type: e.type,
          timestamp: Date.now(),
          target: (e.target as HTMLElement)?.tagName,
          message: (e as any).message || '未知错误'
        })
        cleanup()
        resolve(null)
      }

      // 清理函数
      const cleanup = () => {
        audio.removeEventListener('canplaythrough', onCanPlay)
        audio.removeEventListener('error', onError)
      }

      // 添加事件监听器
      audio.addEventListener('canplaythrough', onCanPlay)
      audio.addEventListener('error', onError)

      // 设置源并开始加载
      audio.src = audioSrc
      audio.load()

      // 超时处理
      setTimeout(() => {
        if (audio.readyState < 4) {
          console.warn('音频加载超时:', audioSrc)
          cleanup()
          resolve(audio) // 仍然返回音频元素，即使它可能未完全加载
        }
      }, 10000)
    } catch (e) {
      console.error('预加载音频失败:', e)
      resolve(null)
    }
  })
}

/**
 * 创建一个不会被浏览器阻止的播放函数
 * @param audio 音频元素
 * @param onSuccess 播放成功回调
 * @param onError 播放失败回调
 */
export const createSafePlayFunction = (
  audio: HTMLAudioElement | null,
  onSuccess: () => void,
  onError: (error: any) => void
) => {
  return () => {
    if (!audio) {
      onError(new Error('音频元素不存在'))
      return
    }

    // 强制设置音量和确保不是静音
    audio.volume = 1.0
    audio.muted = false

    // 强制重新加载并检查网络状态
    if (audio.readyState < 2 || audio.networkState !== 1) {
      try {
        audio.load()
        // 短暂延迟播放以确保加载
        setTimeout(() => attemptPlay(audio, onSuccess, onError), 500)
        return
      } catch (e) {
        console.error('重新加载音频失败:', e)
      }
    }

    // 尝试播放
    attemptPlay(audio, onSuccess, onError)
  }
}

// 辅助函数：尝试播放并处理可能的错误
const attemptPlay = (
  audio: HTMLAudioElement,
  onSuccess: () => void,
  onError: (error: any) => void
) => {
  try {
    // 注意：不要重置时间位置，保持当前播放位置
    // 之前的代码会导致播放时总是从0开始
    // if (audio.currentTime > 0) {
    //   audio.currentTime = 0
    // }

    // 移除可能影响播放的属性
    audio.removeAttribute('muted')

    const playPromise = audio.play()
    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          // 确保播放器真的在播放，否则重新尝试
          if (audio.paused) {
            console.warn('虽然播放承诺成功，但播放器仍处于暂停状态')
            audio.play().catch(e => console.error('再次播放失败:', e))
          }

          // 在 Web 环境下创建音频上下文并连接到音频元素
          if (!NetworkUtils.isCapacitor()) {
            try {
              const AudioContext = window.AudioContext || (window as any).webkitAudioContext
              if (AudioContext) {
                const audioCtx = new AudioContext()
                const source = audioCtx.createMediaElementSource(audio)
                source.connect(audioCtx.destination)
              }
            } catch (e) {
              console.warn('创建音频上下文失败，这可能不会影响播放:', e)
            }
          }

          onSuccess()
        })
        .catch(err => {
          console.error('播放失败:', err)

          // 检查是否因为需要用户交互而被阻止
          if (err.name === 'NotAllowedError') {
            console.info('需要用户交互来播放音频')

            // 尝试静音播放
            audio.muted = true
            audio
              .play()
              .then(() => {
                // 在用户首次交互时取消静音
                const unmute = () => {
                  audio.muted = false
                  document.removeEventListener('click', unmute)
                  document.removeEventListener('touchstart', unmute)
                }

                document.addEventListener('click', unmute)
                document.addEventListener('touchstart', unmute)

                onSuccess()
              })
              .catch(muteErr => {
                console.error('静音播放也失败:', muteErr)

                // 最后尝试：切换音频源
                const currentSrc = audio.src

                // 尝试使用不同文件格式
                const tryAlternateFormat = () => {
                  if (currentSrc.endsWith('.mp3')) {
                    const wavSrc = currentSrc.replace('.mp3', '.wav')
                    audio.src = wavSrc
                  } else if (currentSrc.endsWith('.wav')) {
                    const mp3Src = currentSrc.replace('.wav', '.mp3')
                    audio.src = mp3Src
                  }

                  audio.load()
                  audio.play().catch(e => {
                    console.error('尝试替代格式也失败:', e)
                    // 恢复原始源
                    audio.src = currentSrc
                    audio.load()
                    onError(muteErr)
                  })
                }

                tryAlternateFormat()
              })
          } else {
            onError(err)
          }
        })
    } else {
      // 旧浏览器兼容
      onSuccess()
    }
  } catch (e) {
    console.error('播放抛出异常:', e)
    onError(e)
  }
}

/**
 * 安全地播放音频并在播放开始后强制设置时间点
 * 专门用于解决暂停切换阶段再播放时，音频从0秒开始的问题
 */
export const playWithTimestamp = (
  audio: HTMLAudioElement | null,
  timestamp: number,
  onTimeSet: (time: number) => void,
  onSuccess: () => void,
  onError: (error: any) => void
) => {
  if (!audio) {
    onError(new Error('音频元素不存在'))
    return
  }

  // 首先尝试设置时间点
  try {
    audio.currentTime = timestamp
  } catch (e) {
    console.warn('预设时间点失败，将在播放后设置:', e)
  }

  // 监听播放开始事件
  const onPlayingHandler = () => {
    // 播放开始后，立即设置时间点

    // 设置多个延时器确保时间点被设置
    ;[0, 10, 50, 100, 200, 500, 1000].forEach(delay => {
      setTimeout(() => {
        try {
          if (audio && !audio.paused) {
            const currentT = audio.currentTime
            // 只有当当前时间与目标时间相差较大时才设置
            if (Math.abs(currentT - timestamp) > 0.5) {
              audio.currentTime = timestamp
              onTimeSet(timestamp)
            }
          }
        } catch (e) {
          console.error(`${delay}ms后设置时间点失败:`, e)
        }
      }, delay)
    })

    // 移除事件监听器
    audio.removeEventListener('playing', onPlayingHandler)
  }

  // 添加事件监听器
  audio.addEventListener('playing', onPlayingHandler)

  // 确保音量设置正确
  audio.volume = 1.0
  audio.muted = false

  // 开始播放
  const playPromise = audio.play()
  if (playPromise !== undefined) {
    playPromise
      .then(() => {
        onSuccess()
      })
      .catch(err => {
        console.error('播放失败:', err)
        audio.removeEventListener('playing', onPlayingHandler)
        onError(err)
      })
  } else {
    // 对于不返回Promise的老浏览器
    onSuccess()
  }
}

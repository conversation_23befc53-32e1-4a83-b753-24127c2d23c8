import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import type { FaceSwapTask } from '@/types/face-swap'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { createPointsService } from '@/lib/membership/points'
import { getTemplateById, updateTemplateUsageCount } from '@/lib/db/queries/template'
import { getCachedDbUserId, getCachedTemplateById } from '@/lib/cache/cache-utils'
import {
  createMediaGeneration,
  getUserMediaGenerations,
  getMediaGenerationByGenerationId,
  getCharacterMediaGenerations
} from '@/lib/db/queries/media-generation'
import type { GenerateImageRequest } from '@/types/image-generation'
import { ensureUserExists } from '@/lib/db/queries/membership'

const app = new Hono<{ Bindings: Env }>()

// 辅助函数：从Supabase用户上下文解析本地用户ID（优化版本）
async function resolveLocalUserId(c: any): Promise<string> {
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    const t = c.get('t')
    throw new Error(t('user_not_authenticated'))
  }

  // 使用带缓存的用户ID获取
  const localUserId = await getCachedDbUserId(c.env, supabaseUser.id)
  if (!localUserId) {
    // 降级：确保用户在本地数据库中存在
    console.log('🔍 缓存未命中，使用ensureUserExists创建用户')
    const fallbackUserId = await ensureUserExists(c.env, supabaseUser.id, supabaseUser.email || '')
    return fallbackUserId
  }

  return localUserId
}

// 角色信息接口
interface CharacterInfo {
  gender?: 'male' | 'female'
  ethnicity?: string
  age?: string | number
  eyeColor?: string
  hairStyle?: string
  hairColor?: string
  bodyType?: string
  breastSize?: string
  buttSize?: string
}

// 生成角色描述的函数
function generateCharacterDescription(characterInfo: CharacterInfo): string {
  const {
    gender = 'female',
    ethnicity = 'Asian',
    age = 25,
    eyeColor = 'brown',
    hairStyle = 'long',
    hairColor = 'black',
    bodyType = 'slim',
    breastSize,
    buttSize
  } = characterInfo

  // 处理年龄（可能是字符串或数字）
  const ageNum = typeof age === 'string' ? Number.parseInt(age) || 25 : age
  const ageDescription =
    ageNum < 25 ? 'early 20s' : ageNum < 35 ? 'late 20s' : ageNum < 45 ? '30s' : '40s'

  // 基础描述
  let description = `A ${ageDescription} ${ethnicity} ${gender}, over 18 years old`

  // 头发描述
  if (hairStyle && hairColor) {
    description += `, with ${hairStyle} ${hairColor} hair`
  }

  // 眼睛描述
  if (eyeColor) {
    description += `, ${eyeColor} eyes`
  }

  // 身材描述
  const bodyDescriptions: string[] = []
  if (bodyType) {
    bodyDescriptions.push(bodyType)
  }

  // 女性特有描述
  if (gender === 'female') {
    if (breastSize) {
      bodyDescriptions.push(`${breastSize} chest`)
    }
    if (buttSize) {
      bodyDescriptions.push(`${buttSize} hips`)
    }
    if (bodyDescriptions.length === 0) {
      bodyDescriptions.push('well-proportioned', 'attractive figure')
    }
  } else {
    // 男性描述
    if (bodyDescriptions.length === 0) {
      bodyDescriptions.push('athletic', 'well-built physique')
    }
  }

  if (bodyDescriptions.length > 0) {
    description += `, and a ${bodyDescriptions.join(', ')} figure`
  }

  return description + '.'
}

// 写真集生成请求验证
const generatePhotoAlbumSchema = z.object({
  templateId: z.string().min(1),
  characterId: z.string().min(1),
  characterName: z.string().min(1),
  characterImageUrl: z.string().url(),
  waitForCompletion: z.boolean().default(false),
  // 角色详细信息
  gender: z.enum(['male', 'female']).optional(),
  ethnicity: z.string().optional(),
  age: z.string().optional(),
  eyeColor: z.string().optional(),
  hairStyle: z.string().optional(),
  hairColor: z.string().optional(),
  bodyType: z.string().optional(),
  breastSize: z.string().optional(), // 仅女性
  buttSize: z.string().optional()
})

// 任务状态查询验证
const taskStatusSchema = z.object({
  taskId: z.string().min(1)
})

/**
 * POST /api/photo-album-generation/generate
 * 生成写真集
 */
app.post('/generate', languageMiddleware, authMiddleware, zValidator('json', generatePhotoAlbumSchema), async c => {
  try {
    const {
      templateId,
      characterId,
      characterName,
      characterImageUrl,
      waitForCompletion,
      gender,
      ethnicity,
      age,
      eyeColor,
      hairStyle,
      hairColor,
      bodyType,
      breastSize,
      buttSize
    } = c.req.valid('json')
    const t = c.get('t')

    // 验证必要参数
    if (!templateId || templateId.trim().length === 0) {
      return c.json({ error: t('template_id_required') }, 400)
    }
    if (!characterId || characterId.trim().length === 0) {
      return c.json({ error: t('character_id_required') }, 400)
    }
    if (!characterName || characterName.trim().length === 0) {
      return c.json({ error: t('character_name_required') }, 400)
    }
    try {
      new URL(characterImageUrl)
    } catch {
      return c.json({ error: t('character_image_url_invalid') }, 400)
    }

    // 获取本地用户ID
    const localUserId = await resolveLocalUserId(c)

    console.log('🎨 [写真集生成] 开始处理请求:', {
      templateId,
      characterId,
      characterName,
      localUserId,
      characterInfo: { gender, ethnicity, age, eyeColor, hairStyle, hairColor, bodyType }
    })

    // 1. 获取模板信息（带缓存）
    const template = await getCachedTemplateById(c.env, templateId)
    if (!template) {
      return c.json({ error: t('template_not_found') }, 404)
    }

    if (!template.isActive) {
      return c.json({ error: t('template_disabled') }, 400)
    }

    // 2. 检查会员权限和积分
    const pointsService = createPointsService(c.env)
    const generationId = `photo-album-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`

    // 使用模板的实际积分消耗，而不是固定的系统配置
    const pointsResult = await pointsService.consumePoints(
      localUserId,
      template.pointsCost, // 使用模板的 points_cost 字段
      'generation',
      generationId,
      `使用模板"${template.name}"生成写真 (${template.pointsCost}积分)`
    )

    if (!pointsResult.success) {
      return c.json(
        {
          success: false,
          error: pointsResult.error,
          errorCode: pointsResult.error?.includes('会员')
            ? 'MEMBERSHIP_REQUIRED'
            : 'INSUFFICIENT_POINTS'
        },
        400
      )
    }

    // 3. 构建生成请求
    // 生成角色描述
    const characterDescription = generateCharacterDescription({
      gender,
      ethnicity,
      age,
      eyeColor,
      hairStyle,
      hairColor,
      bodyType,
      breastSize,
      buttSize
    })

    // 组合最终 prompt：角色描述 + 模板 prompt
    const finalPrompt = `${characterDescription} ${template.prompt || ''}`.trim()

    const generateRequest: GenerateImageRequest = {
      prompt: finalPrompt,
      imageUrl: characterImageUrl
    }

    console.log('🎨 [写真集生成] 生成参数:', {
      prompt: generateRequest.prompt,
      imageUrl: generateRequest.imageUrl,
      templateName: template.name
    })

    // 4. 创建生成历史记录（状态：pending）
    console.log('🎨 [写真集生成] 创建数据库记录:', {
      userId: localUserId,
      characterId,
      generationId,
      templateId: template.id
    })

    const mediaGeneration = await createMediaGeneration(c.env, {
      userId: localUserId,
      characterId,
      mediaType: 'image',
      generationType: 'template_based',
      prompt: finalPrompt,
      inputImageUrl: characterImageUrl,
      pointsUsed: pointsResult.pointsConsumed || 0,
      metadata: {
        templateId: template.id,
        templateName: template.name,
        characterName,
        generationId,
        characterDescription,
        originalTemplatePrompt: template.prompt,
        characterInfo: {
          gender,
          ethnicity,
          age,
          eyeColor,
          hairStyle,
          hairColor,
          bodyType,
          breastSize,
          buttSize
        }
      }
    })

    console.log('✅ [写真集生成] 数据库记录已创建:', {
      id: mediaGeneration.id,
      generationId: (mediaGeneration.metadata as any)?.generationId,
      status: mediaGeneration.status
    })

    // 5. 暂时使用换脸队列替代写真集生成队列
    // 注释：原来使用 PHOTO_ALBUM_GENERATION_QUEUE
    // await c.env.PHOTO_ALBUM_GENERATION_QUEUE.send({...})

    const faceSwapTask: FaceSwapTask = {
      taskId: generationId,
      messageId: generationId,
      chatId: 'photo-album-generation',
      userId: localUserId,
      inputImageUrl: 'placeholder', // 需要先生成图片
      swapImageUrl: characterImageUrl, // 角色头像
      timestamp: Date.now(),
      metadata: {
        templateId: template.id,
        originalPrompt: finalPrompt,
        templateName: template.name,
        characterId,
        characterName,
        mediaGenerationId: mediaGeneration.id,
        characterDescription,
        originalTemplatePrompt: template.prompt,
        characterInfo: {
          gender,
          ethnicity,
          age,
          eyeColor,
          hairStyle,
          hairColor,
          bodyType,
          breastSize,
          buttSize
        }
      }
    }

    await c.env.FACE_SWAP_QUEUE.send(faceSwapTask)

    console.log('🎨 [写真集生成] 任务已发送到队列:', {
      generationId,
      mediaGenerationId: mediaGeneration.id
    })

    // 6. 异步更新模板使用次数（不阻塞主流程）
    Promise.resolve().then(async () => {
      try {
        await updateTemplateUsageCount(c.env, templateId)
        console.log('✅ [写真集生成] 模板使用次数更新成功:', {
          templateId,
          templateName: template.name
        })
      } catch (error: any) {
        console.error('❌ [写真集生成] 模板使用次数更新失败:', { templateId, error: error.message })
        // 不抛出错误，避免影响主要功能
      }
    })

    // 7. 返回任务信息（队列模式，异步处理）
    const responseData = {
      success: true,
      data: {
        taskId: generationId, // 使用 generationId 作为 taskId
        status: 'pending',
        estimatedSteps: null, // 队列模式下暂时未知
        prompt: generateRequest.prompt,
        templateName: template.name,
        pointsConsumed: pointsResult.pointsConsumed,
        remainingPoints: pointsResult.remainingPoints,
        generationId,
        message: t('task_submitted')
      }
    }

    console.log('🎨 [写真集生成] 返回响应数据:', JSON.stringify(responseData, null, 2))
    return c.json(responseData)
  } catch (error) {
    const t = c.get('t')
    console.error('写真集生成失败:', error)
    return c.json({ error: t('generation_failed') }, 500)
  }
})

/**
 * GET /api/photo-album-generation/status/:taskId
 * 查询生成任务状态（从数据库查询）
 */
app.get('/status/:taskId', languageMiddleware, authMiddleware, async c => {
  try {
    const taskId = c.req.param('taskId')
    const t = c.get('t')

    if (!taskId) {
      return c.json({ error: t('task_id_required') }, 400)
    }

    // 获取本地用户ID
    const localUserId = await resolveLocalUserId(c)

    console.log(`🔍 [状态查询] 开始查询 taskId: ${taskId}, userId: ${localUserId}`)

    // 方法1：直接按 generationId 查询
    let mediaGeneration = await getMediaGenerationByGenerationId(c.env, localUserId, taskId)
    let allMediaGenerations: any[] = []

    // 如果第一次查询失败，等待一下再试（可能是数据库事务延迟）
    if (!mediaGeneration) {
      console.log(`🔍 [状态查询] 第一次查询失败，等待 500ms 后重试...`)
      await new Promise(resolve => setTimeout(resolve, 500))
      mediaGeneration = await getMediaGenerationByGenerationId(c.env, localUserId, taskId)
    }

    if (!mediaGeneration) {
      console.log(`🔍 [状态查询] 直接查询未找到，尝试遍历查询...`)

      // 方法2：查询所有记录并遍历查找（备用方案）
      allMediaGenerations = await getUserMediaGenerations(c.env, localUserId, {
        limit: 50
      })

      console.log(
        `🔍 [状态查询] 查询到 ${allMediaGenerations.length} 条记录，查找 taskId: ${taskId}`
      )

      mediaGeneration =
        allMediaGenerations.find((mg: any) => {
          const generationId = mg.metadata?.generationId
          console.log(`🔍 [状态查询] 检查记录 ${mg.id}:`, {
            generationId,
            mediaType: mg.mediaType,
            generationType: mg.generationType,
            status: mg.status,
            createdAt: mg.createdAt,
            metadata: mg.metadata
          })
          return generationId === taskId
        }) || null
    }

    // 方法3：如果还是没找到，查询最近的记录（可能是时序问题）
    if (!mediaGeneration && allMediaGenerations.length === 0) {
      console.log(`🔍 [状态查询] 尝试查询最近的记录...`)
      allMediaGenerations = await getUserMediaGenerations(c.env, localUserId, {
        limit: 10,
        mediaType: 'image',
        generationType: 'template_based'
      })

      console.log(`🔍 [状态查询] 最近的 ${allMediaGenerations.length} 条记录:`)
      allMediaGenerations.forEach((mg: any, index) => {
        console.log(`  ${index + 1}. ${mg.id} - ${mg.metadata?.generationId} - ${mg.createdAt}`)
      })
    }

    if (!mediaGeneration) {
      console.log(`❌ [状态查询] 未找到 taskId: ${taskId}`)
      console.log(
        `📋 [状态查询] 可用的 generationId 列表:`,
        allMediaGenerations.map((mg: any) => mg.metadata?.generationId).filter(Boolean)
      )
      return c.json(
        {
          error: t('task_not_found'),
          debug: {
            searchedTaskId: taskId,
            foundGenerationIds: allMediaGenerations
              .map((mg: any) => mg.metadata?.generationId)
              .filter(Boolean),
            totalRecords: allMediaGenerations.length
          }
        },
        404
      )
    }

    // 从 outputUrls 数组中获取第一个图片URL
    const imageUrl =
      Array.isArray(mediaGeneration.outputUrls) && mediaGeneration.outputUrls.length > 0
        ? mediaGeneration.outputUrls[0]
        : null

    return c.json({
      success: true,
      data: {
        taskId: taskId,
        status: mediaGeneration.status || 'pending',
        progress: (mediaGeneration.metadata as any)?.progress || 0,
        estimatedSteps: (mediaGeneration.metadata as any)?.estimatedSteps || null,
        completedSteps: (mediaGeneration.metadata as any)?.completedSteps || 0,
        imageUrl: imageUrl,
        errorMessage:
          mediaGeneration.errorMessage || (mediaGeneration.metadata as any)?.errorMessage
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('查询写真集生成状态失败:', error)
    return c.json({ error: t('query_status_failed') }, 500)
  }
})

// 获取写真集生成历史记录
app.get(
  '/history',
  languageMiddleware,
  authMiddleware,
  zValidator(
    'query',
    z.object({
      characterId: z.string().min(1),
      limit: z
        .string()
        .optional()
        .transform(val => (val ? Number.parseInt(val) : 20)),
      offset: z
        .string()
        .optional()
        .transform(val => (val ? Number.parseInt(val) : 0))
    })
  ),
  async c => {
    try {
      const { characterId, limit, offset } = c.req.valid('query')
      const t = c.get('t')

      // 验证参数
      if (!characterId || characterId.trim().length === 0) {
        return c.json({ error: t('character_id_required') }, 400)
      }

      // 获取本地用户ID
      const localUserId = await resolveLocalUserId(c)

      console.log(`📋 [历史记录] 查询参数:`, {
        userId: localUserId,
        characterId,
        limit,
        offset
      })

      // 查询角色的媒体生成记录
      const mediaGenerations = await getCharacterMediaGenerations(c.env, localUserId, characterId, {
        mediaType: 'image',
        limit,
        offset
      })

      // 转换为前端需要的格式
      const history = mediaGenerations.map(mg => ({
        id: mg.id,
        templateId: (mg.metadata as any)?.templateId,
        templateName: (mg.metadata as any)?.templateName,
        characterId: mg.characterId,
        characterName: (mg.metadata as any)?.characterName,
        originalImageUrl: mg.inputImageUrl || '',
        generatedImageUrl:
          Array.isArray(mg.outputUrls) && mg.outputUrls.length > 0 ? mg.outputUrls[0] : null,
        status: mg.status,
        progress: (mg.metadata as any)?.progress || 0,
        pointsUsed: mg.pointsUsed,
        generationTime: mg.generationTime,
        createdAt: mg.createdAt,
        updatedAt: mg.updatedAt,
        completedAt: mg.completedAt,
        errorMessage: mg.errorMessage,
        metadata: {
          characterId: mg.characterId,
          characterName: (mg.metadata as any)?.characterName,
          taskId: (mg.metadata as any)?.taskId,
          generationId: (mg.metadata as any)?.generationId
        }
      }))

      console.log(`✅ [历史记录] 查询成功:`, {
        userId: localUserId,
        characterId,
        count: history.length,
        // 添加 URL 类型统计
        urlTypes: history.map(h => ({
          id: h.id,
          isR2: h.generatedImageUrl?.includes('assets.pleasurehub.app') || false,
          isReplicate: h.generatedImageUrl?.includes('replicate.delivery') || false,
          url: h.generatedImageUrl?.substring(0, 50) + '...'
        }))
      })

      return c.json({
        success: true,
        data: history,
        pagination: {
          limit,
          offset,
          total: history.length
        }
      })
    } catch (error) {
      const t = c.get('t')
      console.error('获取写真集生成历史失败:', error)
      return c.json({ error: t('get_history_failed') }, 500)
    }
  }
)

export default app

/**
 * 测试订阅创建的辅助函数
 */

import { createUserSubscription } from '@/lib/db/queries/membership';
import type { Env } from '@/types/env';

export async function testCreateSubscription(env: Env) {
  try {
    // 使用固定的测试数据
    const testData = {
      userId: '01234567-89ab-cdef-0123-456789abcdef', // 测试用UUID
      planId: '01234567-89ab-cdef-0123-456789abcdef', // 测试用UUID
      startDate: new Date(),
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      status: 'active' as const,
      paymentId: 'test_payment_123',
      autoRenew: false,
    };

    console.log('🧪 [TEST] 测试数据:', testData);

    const result = await createUserSubscription(env, testData);

    console.log('✅ [TEST] 订阅创建成功:', result);
    return result;
  } catch (error) {
    console.error('❌ [TEST] 订阅创建失败:', error);
    throw error;
  }
}

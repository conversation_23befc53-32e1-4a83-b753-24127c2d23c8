<svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M30.1358 14.4757C28.6125 18.0405 25.7369 20.855 22.1403 22.3014C18.5436 23.7478 14.52 23.7077 10.9529 22.19" stroke="#F90094" stroke-width="1.92857" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M15.6458 11.9171L17.6515 16.2757" stroke="#F90094" stroke-width="1.92857" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M21.11 9.41003L23.1157 13.7686" stroke="#F90094" stroke-width="1.92857" stroke-miterlimit="10" stroke-linecap="round"/>
<path d="M26.7671 20.0172C27.0078 20.3613 27.1781 20.7495 27.2683 21.1597C27.3585 21.5698 27.3668 21.9937 27.2928 22.4071C27.2187 22.8204 27.0637 23.2151 26.8367 23.5683C26.6097 23.9216 26.3151 24.2266 25.9699 24.4657C25.2722 24.9472 24.4121 25.1322 23.5782 24.9804C22.7443 24.8285 22.0046 24.3522 21.5214 23.6557" stroke="#F90094" stroke-width="1.92857" stroke-miterlimit="10" stroke-linecap="round"/>
<foreignObject x="2" y="2" width="32" height="32"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1px);clip-path:url(#bgblur_0_496_710_clip_path);height:100%;width:100%"></div></foreignObject><path data-figma-bg-blur-radius="2" d="M18 4.25C25.5939 4.25 31.75 10.4061 31.75 18C31.75 25.5939 25.5939 31.75 18 31.75C10.4061 31.75 4.25 25.5939 4.25 18C4.25 10.4061 10.4061 4.25 18 4.25Z" fill="url(#paint0_linear_496_710)" fill-opacity="0.4" stroke="url(#paint1_linear_496_710)" stroke-width="0.5"/>
<defs>
<clipPath id="bgblur_0_496_710_clip_path" transform="translate(-2 -2)"><path d="M18 4.25C25.5939 4.25 31.75 10.4061 31.75 18C31.75 25.5939 25.5939 31.75 18 31.75C10.4061 31.75 4.25 25.5939 4.25 18C4.25 10.4061 10.4061 4.25 18 4.25Z"/>
</clipPath><linearGradient id="paint0_linear_496_710" x1="10.196" y1="5.91646" x2="27.5087" y2="24.5754" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#FF7CCA"/>
</linearGradient>
<linearGradient id="paint1_linear_496_710" x1="9.7037" y1="-6" x2="42.3127" y2="13.1933" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.528846" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>

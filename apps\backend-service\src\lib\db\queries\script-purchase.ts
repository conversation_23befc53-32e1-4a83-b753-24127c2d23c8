import type { Env } from '@/types/env'
import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type { ScriptPurchase } from '../schema'

/**
 * 检查用户是否已购买剧本
 */
export async function checkScriptPurchase(
  env: Env,
  userId: string,
  scriptId: string
): Promise<ScriptPurchase | null> {
  try {
    const supabase = getSupabase(env)

    console.log('🔍 [SCRIPT-PURCHASE] 检查剧本购买状态:', { userId, scriptId })

    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .select('*')
      .eq('user_id', userId)
      .eq('script_id', scriptId)
      .eq('status', 'completed')
      .gt('expires_at', new Date().toISOString()) // 检查是否过期
      .limit(1)
      .single()

    const { data: purchase, error } = handleSupabaseSingleResult(result)
    if (error) return null

    console.log('📋 [SCRIPT-PURCHASE] 购买状态查询结果:', purchase ? '已购买' : '未购买')
    return purchase
  } catch (error) {
    console.error('❌ [SCRIPT-PURCHASE] 检查购买状态失败:', error)
    return null
  }
}

/**
 * 创建剧本购买记录
 */
export async function createScriptPurchase(
  env: Env,
  data: {
    userId: string
    scriptId: string
    pointsCost: number
    transactionId?: string
  }
): Promise<ScriptPurchase[]> {
  try {
    const supabase = getSupabase(env)

    console.log('💳 [SCRIPT-PURCHASE] 创建购买记录:', {
      userId: data.userId,
      scriptId: data.scriptId,
      pointsCost: data.pointsCost,
    })

    // 计算一年后的过期时间
    const expiresAt = new Date()
    expiresAt.setFullYear(expiresAt.getFullYear() + 1)

    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .insert({
        user_id: data.userId,
        script_id: data.scriptId,
        points_cost: data.pointsCost,
        transaction_id: data.transactionId,
        expires_at: expiresAt.toISOString(),
        status: 'completed'
      })
      .select()

    const { data: purchases, error } = handleSupabaseResult(result)
    if (error) throw error

    console.log('✅ [SCRIPT-PURCHASE] 购买记录创建成功:', purchases?.[0]?.id)
    return purchases || []
  } catch (error) {
    console.error('❌ [SCRIPT-PURCHASE] 创建购买记录失败:', error)
    throw error
  }
}

/**
 * 获取用户已购买的剧本ID列表（用于前端判断购买状态）
 */
export async function getUserPurchasedScriptIds(env: Env, userId: string): Promise<string[]> {
  try {
    const supabase = getSupabase(env)

    console.log('📋 [SCRIPT-PURCHASE] 获取用户购买的剧本ID列表:', userId)

    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .select('script_id')
      .eq('user_id', userId)
      .eq('status', 'completed')
      .gt('expires_at', new Date().toISOString()) // 只返回未过期的

    // 直接使用原始数据，不通过 handleSupabaseResult 转换
    if (result.error) {
      console.error('❌ [SCRIPT-PURCHASE] Supabase 查询错误:', result.error)
      throw result.error
    }

    const purchases = result.data || []
    const scriptIds = purchases.map((r: any) => r.script_id as string).filter(Boolean)
    console.log('📊 [SCRIPT-PURCHASE] 找到购买的剧本数量:', scriptIds.length)
    return scriptIds
  } catch (error) {
    console.error('❌ [SCRIPT-PURCHASE] 获取购买剧本ID列表失败:', error)
    return []
  }
}

/**
 * 标记剧本内容已下载
 */
export async function markScriptDownloaded(
  env: Env,
  userId: string,
  scriptId: string
): Promise<boolean> {
  try {
    const supabase = getSupabase(env)

    console.log('📥 [SCRIPT-PURCHASE] 标记剧本已下载:', { userId, scriptId })

    const now = new Date()
    const result = await supabase
      .from(TABLE_NAMES.scriptPurchase)
      .update({
        is_downloaded: true,
        downloaded_at: now.toISOString(),
        updated_at: now.toISOString()
      })
      .eq('user_id', userId)
      .eq('script_id', scriptId)
      .eq('status', 'completed')
      .select()

    const { data: updates, error } = handleSupabaseResult(result)
    if (error) throw error

    const success = (updates || []).length > 0
    console.log('📥 [SCRIPT-PURCHASE] 下载标记更新结果:', success ? '成功' : '失败')
    return success
  } catch (error) {
    console.error('❌ [SCRIPT-PURCHASE] 标记下载失败:', error)
    return false
  }
}

/**
 * 高效的剧本购买：积分扣除 + 购买记录创建（优化版本）
 * 注意: Supabase 不支持传统的事务，这里采用分步操作并在失败时回滚
 */
export async function purchaseScriptWithPoints(
  env: Env,
  data: {
    userId: string
    scriptId: string
    pointsCost: number
    scriptTitle: string
  }
): Promise<{
  success: boolean
  purchase?: ScriptPurchase
  remainingPoints?: number
  error?: string
}> {
  try {
    const supabase = getSupabase(env)

    console.log('💰 [SCRIPT-PURCHASE] 开始购买剧本:', {
      userId: data.userId,
      scriptId: data.scriptId,
      pointsCost: data.pointsCost,
      scriptTitle: data.scriptTitle
    })

    // 1. 检查用户积分
    const userPointsResult = await supabase
      .from(TABLE_NAMES.userPoints)
      .select('*')
      .eq('user_id', data.userId)
      .single()

    const { data: userPointsRecord, error: pointsError } = handleSupabaseSingleResult(userPointsResult)
    if (pointsError || !userPointsRecord) {
      return {
        success: false,
        error: '用户积分记录不存在'
      }
    }

    if (userPointsRecord.availablePoints < data.pointsCost) {
      return {
        success: false,
        error: `积分不足，需要${data.pointsCost}积分，当前可用${userPointsRecord.availablePoints}积分`,
        remainingPoints: userPointsRecord.availablePoints
      }
    }

    // 2. 计算新的积分值
    const newAvailablePoints = userPointsRecord.availablePoints - data.pointsCost
    const newUsedPoints = userPointsRecord.usedPoints + data.pointsCost

    // 3. 更新积分 (使用乐观锁机制)
    const pointsUpdateResult = await supabase
      .from(TABLE_NAMES.userPoints)
      .update({
        available_points: newAvailablePoints,
        used_points: newUsedPoints,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', data.userId)
      .eq('available_points', userPointsRecord.availablePoints) // 乐观锁：确保积分没有被其他操作修改
      .select()

    const { data: updatedPoints, error: updateError } = handleSupabaseResult(pointsUpdateResult)
    if (updateError || !updatedPoints?.length) {
      return {
        success: false,
        error: '积分更新失败，可能有并发操作'
      }
    }

    let transactionId: string | undefined

    try {
      // 4. 创建积分交易记录
      const transactionResult = await supabase
        .from(TABLE_NAMES.pointsTransaction)
        .insert({
          user_id: data.userId,
          transaction_type: 'spend',
          amount: data.pointsCost,
          source: 'purchase',
          source_id: data.scriptId,
          description: `购买剧本《${data.scriptTitle}》`,
          balance_after: newAvailablePoints
        })
        .select()
        .single()

      const { data: transaction, error: transactionError } = handleSupabaseSingleResult(transactionResult)
      if (transactionError || !transaction) {
        throw new Error('创建积分交易记录失败')
      }

      transactionId = transaction.id

      // 5. 创建购买记录
      const expiresAt = new Date()
      expiresAt.setFullYear(expiresAt.getFullYear() + 1)

      const purchaseResult = await supabase
        .from(TABLE_NAMES.scriptPurchase)
        .insert({
          user_id: data.userId,
          script_id: data.scriptId,
          points_cost: data.pointsCost,
          transaction_id: transactionId,
          expires_at: expiresAt.toISOString(),
          status: 'completed'
        })
        .select()
        .single()

      const { data: purchase, error: purchaseError } = handleSupabaseSingleResult(purchaseResult)
      if (purchaseError || !purchase) {
        throw new Error('创建购买记录失败')
      }

      console.log('✅ [SCRIPT-PURCHASE] 剧本购买成功:', {
        purchaseId: purchase.id,
        remainingPoints: newAvailablePoints
      })

      return {
        success: true,
        purchase,
        remainingPoints: newAvailablePoints
      }

    } catch (operationError) {
      // 回滚积分更新
      console.error('❌ [SCRIPT-PURCHASE] 购买操作失败，正在回滚积分:', operationError)
      
      try {
        await supabase
          .from(TABLE_NAMES.userPoints)
          .update({
            available_points: userPointsRecord.availablePoints,
            used_points: userPointsRecord.usedPoints,
            last_updated: new Date().toISOString()
          })
          .eq('user_id', data.userId)

        // 如果创建了交易记录，也要删除
        if (transactionId) {
          await supabase
            .from(TABLE_NAMES.pointsTransaction)
            .delete()
            .eq('id', transactionId)
        }

        console.log('✅ [SCRIPT-PURCHASE] 积分回滚成功')
      } catch (rollbackError) {
        console.error('❌ [SCRIPT-PURCHASE] 积分回滚失败:', rollbackError)
      }

      return {
        success: false,
        error: operationError instanceof Error ? operationError.message : '剧本购买失败'
      }
    }

  } catch (error) {
    console.error('❌ [SCRIPT-PURCHASE] 剧本购买异常:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '剧本购买失败'
    }
  }
}

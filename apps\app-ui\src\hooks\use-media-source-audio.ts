import { useState, useCallback, useRef } from 'react'
import { tts3Service } from '@/api/services/tts3'
import { useRoleStore } from '@/stores/role-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'

export type MediaSourceStatus =
  | 'idle'
  | 'connecting'
  | 'buffering'
  | 'playing'
  | 'paused'
  | 'completed'
  | 'failed'

export interface MediaSourceState {
  status: MediaSourceStatus
  error: string | null
  bufferProgress: number // 缓冲进度 0-100
  playbackProgress: number // 播放进度 0-100
  duration: number // 总时长（秒）
  currentTime: number // 当前播放时间（秒）
  canPlay: boolean // 是否有足够缓冲可以播放
}

/**
 * 使用 MediaSource API 实现真正的流式音频播放
 */
export const useMediaSourceAudio = () => {
  const [state, setState] = useState<MediaSourceState>({
    status: 'idle',
    error: null,
    bufferProgress: 0,
    playbackProgress: 0,
    duration: 0,
    currentTime: 0,
    canPlay: false
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const audioElementRef = useRef<HTMLAudioElement | null>(null)
  const mediaSourceRef = useRef<MediaSource | null>(null)
  const sourceBufferRef = useRef<SourceBuffer | null>(null)
  const audioChunksRef = useRef<Uint8Array[]>([])
  const isAppendingRef = useRef(false)
  const pendingChunksRef = useRef<Uint8Array[]>([])

  // 获取当前角色信息
  const { currentRole } = useRoleStore()
  const { getUserCharacterById } = useUserCharactersStore()

  // 检查 MediaSource 支持
  const isMediaSourceSupported = useCallback(() => {
    if (typeof MediaSource === 'undefined') {
      console.log('❌ MediaSource API 不可用')
      return false
    }

    // 检查多种音频格式支持
    const supportedFormats = [
      'audio/mpeg',
      'audio/mp4; codecs="mp4a.40.2"',
      'audio/webm; codecs="opus"'
    ]

    for (const format of supportedFormats) {
      if (MediaSource.isTypeSupported(format)) {
        return true
      }
    }

    console.log('❌ MediaSource 不支持任何音频格式')
    return false
  }, [])

  // 重置状态
  const reset = useCallback(() => {
    // 取消正在进行的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    // 清理音频元素
    if (audioElementRef.current) {
      audioElementRef.current.pause()
      audioElementRef.current.src = ''
      audioElementRef.current = null
    }

    // 清理 MediaSource
    if (sourceBufferRef.current) {
      try {
        if (mediaSourceRef.current && mediaSourceRef.current.readyState === 'open') {
          mediaSourceRef.current.removeSourceBuffer(sourceBufferRef.current)
        }
      } catch (error) {
        console.warn('清理 SourceBuffer 失败:', error)
      }
      sourceBufferRef.current = null
    }

    if (mediaSourceRef.current) {
      try {
        if (mediaSourceRef.current.readyState === 'open') {
          mediaSourceRef.current.endOfStream()
        }
      } catch (error) {
        console.warn('结束 MediaSource 失败:', error)
      }
      mediaSourceRef.current = null
    }

    // 清理缓存数据
    audioChunksRef.current = []
    pendingChunksRef.current = []
    isAppendingRef.current = false

    setState({
      status: 'idle',
      error: null,
      bufferProgress: 0,
      playbackProgress: 0,
      duration: 0,
      currentTime: 0,
      canPlay: false
    })
  }, [])

  // 获取当前角色的声音模型ID
  const getCurrentVoice = useCallback(async (): Promise<string | undefined> => {
    if (!currentRole) {
      console.warn('当前没有选择角色，使用默认声音')
      return undefined
    }
    return currentRole.voiceModelId
  }, [currentRole, getUserCharacterById])

  // 缓冲配置常量
  const BUFFER_CONFIG = {
    INITIAL_BUFFER_THRESHOLD: 2.5, // 初始播放需要2.5秒缓冲
    RESUME_BUFFER_THRESHOLD: 1.5, // 恢复播放需要1.5秒缓冲
    LOW_BUFFER_THRESHOLD: 0.5 // 低于0.5秒认为缓冲不足
  }

  // 处理待处理的音频块
  const processPendingChunks = useCallback(() => {
    if (
      isAppendingRef.current ||
      !sourceBufferRef.current ||
      pendingChunksRef.current.length === 0
    ) {
      return
    }

    const sourceBuffer = sourceBufferRef.current
    if (sourceBuffer.updating) {
      return
    }

    try {
      isAppendingRef.current = true
      const chunk = pendingChunksRef.current.shift()!

      sourceBuffer.appendBuffer(chunk)

      // 更新缓冲进度
      const bufferedEnd = sourceBuffer.buffered.length > 0 ? sourceBuffer.buffered.end(0) : 0
      const audio = audioElementRef.current

      if (audio && audio.duration > 0) {
        const progress = (bufferedEnd / audio.duration) * 100
        const currentTime = audio.currentTime || 0
        const remainingBuffer = bufferedEnd - currentTime

        // 动态判断是否可以播放
        let canPlay = false
        if (!audio.currentTime) {
          // 初始播放：需要更多缓冲
          canPlay = bufferedEnd >= BUFFER_CONFIG.INITIAL_BUFFER_THRESHOLD
        } else {
          // 播放中：根据剩余缓冲判断
          canPlay = remainingBuffer >= BUFFER_CONFIG.LOW_BUFFER_THRESHOLD
        }

        setState(prev => ({
          ...prev,
          bufferProgress: Math.min(progress, 100),
          canPlay
        }))
      }
    } catch (error) {
      console.error('添加音频块失败:', error)
      isAppendingRef.current = false
    }
  }, [])

  // 添加音频数据到 SourceBuffer
  const appendAudioData = useCallback(
    (chunk: Uint8Array) => {
      if (!sourceBufferRef.current) {
        console.warn('SourceBuffer 未准备好，缓存音频块')
        pendingChunksRef.current.push(chunk)
        return
      }

      pendingChunksRef.current.push(chunk)
      processPendingChunks()
    },
    [processPendingChunks]
  )

  // 流式生成并播放音频
  const generateAndPlayStream = useCallback(
    async (text: string, messageId?: string, chatId?: string, voiceOverride?: string) => {
      try {
        // 检查 MediaSource 支持
        if (!isMediaSourceSupported()) {
          throw new Error('浏览器不支持 MediaSource API')
        }

        // 防止重复请求
        if (state.status === 'connecting' || state.status === 'buffering') {
          console.log('⚠️ 请求正在进行中，忽略重复请求')
          return null
        }

        // 重置状态
        reset()

        setState({
          status: 'connecting',
          error: null,
          bufferProgress: 0,
          playbackProgress: 0,
          duration: 0,
          currentTime: 0,
          canPlay: false
        })

        // 获取声音参数
        let voiceModelId = voiceOverride
        if (!voiceModelId) {
          voiceModelId = await getCurrentVoice()
        }

        console.log('🎵 开始 MediaSource 流式音频生成:', {
          text: text.substring(0, 50),
          voiceModelId
        })

        // 创建 MediaSource
        const mediaSource = new MediaSource()
        mediaSourceRef.current = mediaSource

        // 创建音频元素
        const audio = new Audio()
        audioElementRef.current = audio
        audio.src = URL.createObjectURL(mediaSource)

        // 设置音频事件监听器
        audio.addEventListener('timeupdate', () => {
          const currentTime = audio.currentTime
          const duration = audio.duration || 0

          setState(prev => ({
            ...prev,
            currentTime,
            playbackProgress: duration > 0 ? (currentTime / duration) * 100 : 0
          }))
        })

        audio.addEventListener('ended', () => {
          setState(prev => ({ ...prev, status: 'completed' }))
        })

        audio.addEventListener('error', error => {
          console.error('音频播放错误:', error)
          setState(prev => ({
            ...prev,
            status: 'failed',
            error: '音频播放失败'
          }))
        })

        // 监听缓冲不足事件
        audio.addEventListener('waiting', () => {
          console.log('🔄 音频缓冲不足，暂停播放等待更多数据')
          setState(prev => ({
            ...prev,
            status: 'buffering'
          }))
        })

        // 监听可以播放事件
        audio.addEventListener('canplay', () => {
          const sourceBuffer = sourceBufferRef.current
          if (sourceBuffer && sourceBuffer.buffered.length > 0) {
            const bufferedEnd = sourceBuffer.buffered.end(0)
            const currentTime = audio.currentTime || 0
            const remainingBuffer = bufferedEnd - currentTime

            console.log(`✅ 音频可以播放，剩余缓冲: ${remainingBuffer.toFixed(1)}s`)

            // 如果当前是buffering状态且有足够缓冲，恢复播放
            setState(prev => {
              if (
                prev.status === 'buffering' &&
                remainingBuffer >= BUFFER_CONFIG.RESUME_BUFFER_THRESHOLD
              ) {
                console.log('🎵 缓冲充足，恢复播放')
                audio.play().catch(console.error)
                return { ...prev, status: 'playing' }
              }
              return prev
            })
          }
        })

        // 等待 MediaSource 打开
        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('MediaSource 打开超时'))
          }, 10000) // 10秒超时

          mediaSource.addEventListener('sourceopen', () => {
            clearTimeout(timeout)
            try {
              // 尝试不同的音频格式
              let sourceBuffer: SourceBuffer | null = null
              const formats = ['audio/mpeg', 'audio/mp4; codecs="mp4a.40.2"']

              for (const format of formats) {
                try {
                  if (MediaSource.isTypeSupported(format)) {
                    sourceBuffer = mediaSource.addSourceBuffer(format)
                    console.log('✅ 使用音频格式:', format)
                    break
                  }
                } catch (error) {
                  console.warn('⚠️ 格式不支持:', format, error)
                }
              }

              if (!sourceBuffer) {
                throw new Error('没有支持的音频格式')
              }

              sourceBufferRef.current = sourceBuffer

              // 设置 SourceBuffer 事件监听器
              sourceBuffer.addEventListener('updateend', () => {
                isAppendingRef.current = false
                processPendingChunks()

                // 检查是否可以开始播放
                const bufferedEnd =
                  sourceBuffer!.buffered.length > 0 ? sourceBuffer!.buffered.end(0) : 0

                // 只在初始状态且有足够缓冲时自动播放
                if (bufferedEnd >= BUFFER_CONFIG.INITIAL_BUFFER_THRESHOLD && !audio.currentTime) {
                  setState(prev => {
                    if (prev.status === 'buffering') {
                      console.log(`🎵 初始缓冲完成(${bufferedEnd.toFixed(1)}s)，开始播放`)
                      audio.play().catch(error => {
                        console.error('自动播放失败:', error)
                        setState(prev => ({ ...prev, status: 'failed', error: '播放失败' }))
                      })
                      return { ...prev, status: 'playing', canPlay: true }
                    }
                    return prev
                  })
                }
              })

              sourceBuffer.addEventListener('error', error => {
                console.error('SourceBuffer 错误:', error)
                setState(prev => ({ ...prev, status: 'failed', error: 'SourceBuffer 错误' }))
                reject(error)
              })

              resolve()
            } catch (error) {
              clearTimeout(timeout)
              reject(error)
            }
          })

          mediaSource.addEventListener('error', error => {
            clearTimeout(timeout)
            console.error('MediaSource 错误:', error)
            reject(error)
          })
        })

        setState(prev => ({ ...prev, status: 'buffering' }))

        // 创建 AbortController
        const abortController = new AbortController()
        abortControllerRef.current = abortController

        // 获取流式音频数据
        const response = await tts3Service.generateStreamAudio(
          {
            text,
            messageId,
            chatId,
            voiceModelId
          },
          abortController.signal
        )

        if (!response.ok) {
          const contentType = response.headers.get('content-type')
          if (contentType?.includes('application/json')) {
            const errorData = await response.json()
            throw new Error(errorData.message || `HTTP ${response.status}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        }

        // 处理流式响应
        console.log('📥 开始接收 MediaSource 音频流...')
        const reader = response.body!.getReader()

        try {
          while (true) {
            const { done, value } = await reader.read()

            if (done) break

            if (value) {
              // 直接添加到 SourceBuffer
              appendAudioData(value)
              audioChunksRef.current.push(value)
            }
          }

          // 流结束，标记 MediaSource 完成
          if (mediaSourceRef.current && mediaSourceRef.current.readyState === 'open') {
            mediaSourceRef.current.endOfStream()
          }

          setState(prev => ({ ...prev, bufferProgress: 100 }))
          console.log('✅ MediaSource 音频流接收完成')
        } finally {
          reader.releaseLock()
        }

        // 返回音频元素和原始数据
        return { audioElement: audio, audioChunks: audioChunksRef.current }
      } catch (error) {
        console.error('MediaSource 流式播放失败:', error)

        // 如果是用户取消，不更新为失败状态
        if (error instanceof Error && error.name === 'AbortError') {
          return null
        }

        setState({
          status: 'failed',
          error: error instanceof Error ? error.message : '播放失败',
          bufferProgress: 0,
          playbackProgress: 0,
          duration: 0,
          currentTime: 0,
          canPlay: false
        })
        throw error
      } finally {
        abortControllerRef.current = null
      }
    },
    [getCurrentVoice, reset, isMediaSourceSupported, appendAudioData, processPendingChunks]
  )

  // 暂停播放
  const pause = useCallback(() => {
    if (audioElementRef.current) {
      audioElementRef.current.pause()
      setState(prev => ({ ...prev, status: 'paused' }))
    }
  }, [])

  // 继续播放
  const play = useCallback(() => {
    if (audioElementRef.current) {
      audioElementRef.current
        .play()
        .then(() => {
          setState(prev => ({ ...prev, status: 'playing' }))
        })
        .catch(console.error)
    }
  }, [])

  // 取消生成
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    reset()
  }, [reset])

  return {
    state,
    isMediaSourceSupported,
    generateAndPlayStream,
    pause,
    play,
    cancel,
    reset,
    currentVoice: currentRole?.name
  }
}

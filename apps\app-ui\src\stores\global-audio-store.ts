import { create } from 'zustand'

// 监听器引用接口
interface AudioListeners {
  handleEnded: () => void
  handlePause: () => void
  handlePlay: () => void
}

export interface GlobalAudioState {
  // 当前播放的音频信息
  currentPlayingId: string | null
  currentAudio: HTMLAudioElement | null
  isPlaying: boolean
  isCleaningUp: boolean // 添加清理状态标记
  currentListeners: AudioListeners | null // 🔧 添加当前监听器引用存储

  // 操作方法
  setCurrentPlaying: (id: string, audio: HTMLAudioElement) => void
  stopCurrent: () => void
  pauseCurrent: () => void
  resumeCurrent: () => void
  clearCurrent: () => void
  cleanup: () => void

  // 检查是否是当前播放的音频
  isCurrentPlaying: (id: string) => boolean
}

/**
 * 全局音频播放管理器
 * 确保同时只有一个音频在播放
 */
export const useGlobalAudioStore = create<GlobalAudioState>((set, get) => ({
  currentPlayingId: null,
  currentAudio: null,
  isPlaying: false,
  isCleaningUp: false,
  currentListeners: null, // 🔧 初始化监听器引用

  setCurrentPlaying: (id: string, audio: HTMLAudioElement) => {
    const state = get()

    // 🔧 先清理当前音频的监听器（如果存在）
    if (state.currentAudio && state.currentListeners) {
      console.log('🎵 清理旧音频的事件监听器:', state.currentPlayingId)
      state.currentAudio.removeEventListener('ended', state.currentListeners.handleEnded)
      state.currentAudio.removeEventListener('pause', state.currentListeners.handlePause)
      state.currentAudio.removeEventListener('play', state.currentListeners.handlePlay)
    }

    // 如果有其他音频在播放，先停止
    if (state.currentAudio && state.currentPlayingId !== id) {
      console.log('🎵 停止其他音频播放:', state.currentPlayingId)
      state.currentAudio.pause()
      state.currentAudio.currentTime = 0
    }

    // 🔧 创建新的监听器函数
    const listeners: AudioListeners = {
      handleEnded: () => {
        console.log('🎵 音频播放结束:', id)
        const currentState = get()
        // 🔧 确保只清理当前音频的状态
        if (currentState.currentPlayingId === id && currentState.currentAudio === audio) {
          set({
            currentPlayingId: null,
            currentAudio: null,
            isPlaying: false,
            currentListeners: null
          })
          // 🔧 移除监听器（防止内存泄漏）
          audio.removeEventListener('ended', listeners.handleEnded)
          audio.removeEventListener('pause', listeners.handlePause)
          audio.removeEventListener('play', listeners.handlePlay)
        }
      },

      handlePause: () => {
        console.log('🎵 音频暂停:', id)
        const currentState = get()
        // 🔧 只更新当前音频的状态
        if (currentState.currentPlayingId === id && currentState.currentAudio === audio) {
          set({ isPlaying: false })
        }
      },

      handlePlay: () => {
        console.log('🎵 音频开始播放:', id)
        const currentState = get()
        // 🔧 只更新当前音频的状态
        if (currentState.currentPlayingId === id && currentState.currentAudio === audio) {
          set({ isPlaying: true })
        }
      }
    }

    // 设置新的播放音频
    console.log('🎵 设置当前播放音频:', id)
    set({
      currentPlayingId: id,
      currentAudio: audio,
      isPlaying: true,
      currentListeners: listeners // 🔧 存储监听器引用
    })

    // 🔧 添加新的事件监听器
    audio.addEventListener('ended', listeners.handleEnded)
    audio.addEventListener('pause', listeners.handlePause)
    audio.addEventListener('play', listeners.handlePlay)
  },

  stopCurrent: () => {
    const state = get()
    if (state.currentAudio) {
      console.log('🎵 停止当前音频播放:', state.currentPlayingId)

      // 🔧 清理监听器
      if (state.currentListeners) {
        state.currentAudio.removeEventListener('ended', state.currentListeners.handleEnded)
        state.currentAudio.removeEventListener('pause', state.currentListeners.handlePause)
        state.currentAudio.removeEventListener('play', state.currentListeners.handlePlay)
      }

      state.currentAudio.pause()
      state.currentAudio.currentTime = 0
      set({
        currentPlayingId: null,
        currentAudio: null,
        isPlaying: false,
        currentListeners: null // 🔧 清理监听器引用
      })
    }
  },

  pauseCurrent: () => {
    const state = get()
    if (state.currentAudio && state.isPlaying) {
      console.log('🎵 暂停当前音频:', state.currentPlayingId)
      state.currentAudio.pause()
      set({ isPlaying: false })
    }
  },

  resumeCurrent: () => {
    const state = get()
    if (state.currentAudio && !state.isPlaying) {
      console.log('🎵 恢复当前音频播放:', state.currentPlayingId)
      state.currentAudio.play().catch(console.error)
      set({ isPlaying: true })
    }
  },

  clearCurrent: () => {
    console.log('🎵 清理当前音频状态')
    const state = get()

    // 🔧 清理监听器
    if (state.currentAudio && state.currentListeners) {
      state.currentAudio.removeEventListener('ended', state.currentListeners.handleEnded)
      state.currentAudio.removeEventListener('pause', state.currentListeners.handlePause)
      state.currentAudio.removeEventListener('play', state.currentListeners.handlePlay)
    }

    set({
      currentPlayingId: null,
      currentAudio: null,
      isPlaying: false,
      currentListeners: null // 🔧 清理监听器引用
    })
  },

  cleanup: () => {
    const state = get()

    // 防止重复清理
    if (state.isCleaningUp) {
      console.log('🎵 已在清理中，跳过重复调用')
      return
    }

    console.log('🎵 页面卸载，强制停止所有音频播放')

    // 设置清理状态
    set({ isCleaningUp: true })

    if (state.currentAudio) {
      try {
        // 🔧 清理监听器
        if (state.currentListeners) {
          state.currentAudio.removeEventListener('ended', state.currentListeners.handleEnded)
          state.currentAudio.removeEventListener('pause', state.currentListeners.handlePause)
          state.currentAudio.removeEventListener('play', state.currentListeners.handlePlay)
        }

        // 停止播放
        state.currentAudio.pause()
        state.currentAudio.currentTime = 0

        // 清理音频源
        state.currentAudio.src = ''
        state.currentAudio.load()

        console.log('🎵 音频已强制停止和清理')
      } catch (error) {
        console.warn('清理音频时出错:', error)
      }
    }

    // 重置状态（使用 setTimeout 避免同步状态更新冲突）
    setTimeout(() => {
      set({
        currentPlayingId: null,
        currentAudio: null,
        isPlaying: false,
        isCleaningUp: false,
        currentListeners: null // 🔧 清理监听器引用
      })
    }, 0)
  },

  isCurrentPlaying: (id: string) => {
    const state = get()
    return state.currentPlayingId === id && state.isPlaying
  }
}))

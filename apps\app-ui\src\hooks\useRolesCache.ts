import { useEffect } from 'react'
import { getDescription } from '@/components/character-creator/mapping'
import type { CharacterType, DisplayRole } from '@/lib/types'
import { useUserCharactersStore, useUserCharactersData } from '@/stores/user-characters-store'

export function useRolesCache() {
  // 使用Zustand store获取用户角色数据
  const { userCharacters, isLoading, isRefreshing } = useUserCharactersData()
  const { fetchUserCharacters, refreshUserCharacters } = useUserCharactersStore()

  // 格式化角色数据
  const formatRoles = (characters: CharacterType[]): DisplayRole[] => {
    return characters.map(char => ({
      role: char.id,
      character: char.name,
      description: getDescription(char as any),
      avatar: char.imageUrl || '/images/roles/default.jpg',
      age: char.age || '?',
      custom: true
    }))
  }

  // 格式化后的角色数据
  const roles = formatRoles(userCharacters as any[])

  // 获取角色数据（兼容原有接口）
  const fetchRoles = async (silent = false) => {
    try {
      await fetchUserCharacters(!silent) // silent=false表示forceRefresh=true
    } catch (error) {
      console.error('获取角色出错:', error)
    }
  }

  // 初始化时获取数据
  useEffect(() => {
    fetchUserCharacters()
  }, [])

  return {
    roles,
    isLoading,
    isRefreshing,
    refreshRoles: refreshUserCharacters
  }
}

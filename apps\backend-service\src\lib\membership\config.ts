import type { Env } from '@/types/env';
import { getSupabase } from '@/lib/db/queries/base';
import {
  handleSupabaseResult,
  handleSupabaseSingleResult,
  TABLE_NAMES,
} from '@/lib/db/supabase-types';

/**
 * 积分配置服务
 * 从数据库动态获取积分消费配置，支持后台管理
 */

// 默认积分配置（作为后备）
const DEFAULT_POINTS_CONFIG = {
  IMAGE_GENERATION: 10,
  VOICE_GENERATION: 5,
  SCRIPT_PURCHASE: 50,
  GALLERY_GENERATION: 15,
  VIDEO_GENERATION: 20,
};

// 会员等级配置
const DEFAULT_MEMBERSHIP_CONFIG = {
  FREE: {
    name: 'Free',
    characterLimit: 1,
    textChatLimit: 100, // 每日限制
    imageGenerationLimit: 1, // 每日限制
    monthlyPoints: 5,
  },
  PRO: {
    name: 'Pro',
    characterLimit: 5,
    textChatLimit: -1, // 无限制
    imageGenerationLimit: -1, // 无限制（消耗积分）
    monthlyPoints: 400,
  },
  ELITE: {
    name: 'Elite',
    characterLimit: 20,
    textChatLimit: -1,
    imageGenerationLimit: -1,
    monthlyPoints: 1200,
  },
  ULTRA: {
    name: 'Ultra',
    characterLimit: -1, // 无限制
    textChatLimit: -1,
    imageGenerationLimit: -1,
    monthlyPoints: 3000,
  },
};

/**
 * 积分配置管理器
 */
export class PointsConfigManager {
  private cache = new Map<string, any>();
  private cacheTimeout = 10 * 60 * 1000; // 10分钟缓存

  constructor(private env: Env) {}

  /**
   * 获取功能积分消费配置
   */
  async getPointsCost(feature: string): Promise<number> {
    const cacheKey = `points_cost_${feature}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.value;
    }

    try {
      const supabase = getSupabase(this.env);
      const result = await supabase
        .from(TABLE_NAMES.systemConfig)
        .select('*')
        .eq('key', `points_cost_${feature.toLowerCase()}`)
        .limit(1)
        .single();

      const { data: config } = handleSupabaseSingleResult(result);

      let pointsCost = DEFAULT_POINTS_CONFIG[feature as keyof typeof DEFAULT_POINTS_CONFIG] || 0;

      if (config?.value) {
        pointsCost =
          typeof config.value === 'number'
            ? config.value
            : Number.parseInt(config.value as string, 10) || pointsCost;
      }

      // 缓存结果
      this.cache.set(cacheKey, { value: pointsCost, timestamp: Date.now() });

      return pointsCost;
    } catch (error) {
      console.error(`获取积分配置失败 (${feature}):`, error);
      // 失败时返回默认值
      return DEFAULT_POINTS_CONFIG[feature as keyof typeof DEFAULT_POINTS_CONFIG] || 0;
    }
  }

  /**
   * 获取所有积分配置
   */
  async getAllPointsCosts(): Promise<Record<string, number>> {
    const features = Object.keys(DEFAULT_POINTS_CONFIG);
    const costs: Record<string, number> = {};

    for (const feature of features) {
      costs[feature] = await this.getPointsCost(feature);
    }

    return costs;
  }

  /**
   * 获取会员等级配置
   */
  async getMembershipConfig(level: string): Promise<any> {
    const cacheKey = `membership_config_${level}`;
    const cached = this.cache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.value;
    }

    try {
      const supabase = getSupabase(this.env);
      const result = await supabase
        .from(TABLE_NAMES.systemConfig)
        .select('*')
        .eq('key', `membership_${level.toLowerCase()}`)
        .limit(1)
        .single();

      const { data: config } = handleSupabaseSingleResult(result);

      let membershipConfig =
        DEFAULT_MEMBERSHIP_CONFIG[level as keyof typeof DEFAULT_MEMBERSHIP_CONFIG];

      if (config?.value) {
        membershipConfig = { ...membershipConfig, ...config.value };
      }

      // 缓存结果
      this.cache.set(cacheKey, { value: membershipConfig, timestamp: Date.now() });

      return membershipConfig;
    } catch (error) {
      console.error(`获取会员配置失败 (${level}):`, error);
      // 失败时返回默认值
      return DEFAULT_MEMBERSHIP_CONFIG[level as keyof typeof DEFAULT_MEMBERSHIP_CONFIG];
    }
  }

  /**
   * 更新积分配置（管理后台使用）
   */
  async updatePointsCost(feature: string, cost: number): Promise<boolean> {
    try {
      const supabase = getSupabase(this.env);
      const configKey = `points_cost_${feature.toLowerCase()}`;

      // 先尝试查询是否存在
      const existingResult = await supabase
        .from(TABLE_NAMES.systemConfig)
        .select('*')
        .eq('key', configKey)
        .limit(1)
        .single();

      const { data: existing } = handleSupabaseSingleResult(existingResult);

      if (existing) {
        // 更新现有配置
        await supabase
          .from(TABLE_NAMES.systemConfig)
          .update({
            value: cost,
            updated_at: new Date().toISOString(),
          })
          .eq('key', configKey);
      } else {
        // 插入新配置
        await supabase.from(TABLE_NAMES.systemConfig).insert({
          key: configKey,
          value: cost,
          description: `${feature}功能积分消费配置`,
          is_public: false,
          updated_at: new Date().toISOString(),
        });
      }

      // 清除缓存
      this.cache.delete(`points_cost_${feature}`);

      return true;
    } catch (error) {
      console.error(`更新积分配置失败 (${feature}):`, error);
      return false;
    }
  }

  /**
   * 更新会员等级配置（管理后台使用）
   */
  async updateMembershipConfig(level: string, config: any): Promise<boolean> {
    try {
      const supabase = getSupabase(this.env);
      const configKey = `membership_${level.toLowerCase()}`;

      // 先尝试查询是否存在
      const existingResult = await supabase
        .from(TABLE_NAMES.systemConfig)
        .select('*')
        .eq('key', configKey)
        .limit(1)
        .single();

      const { data: existing } = handleSupabaseSingleResult(existingResult);

      if (existing) {
        // 更新现有配置
        await supabase
          .from(TABLE_NAMES.systemConfig)
          .update({
            value: config,
            updated_at: new Date().toISOString(),
          })
          .eq('key', configKey);
      } else {
        // 插入新配置
        await supabase.from(TABLE_NAMES.systemConfig).insert({
          key: configKey,
          value: config,
          description: `${level}等级会员配置`,
          is_public: false,
          updated_at: new Date().toISOString(),
        });
      }

      // 清除缓存
      this.cache.delete(`membership_config_${level}`);

      return true;
    } catch (error) {
      console.error(`更新会员配置失败 (${level}):`, error);
      return false;
    }
  }

  /**
   * 清除所有缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取默认配置（用于初始化数据库）
   */
  getDefaultConfig() {
    return {
      points: DEFAULT_POINTS_CONFIG,
      membership: DEFAULT_MEMBERSHIP_CONFIG,
    };
  }
}

/**
 * 创建积分配置管理器实例
 */
export function createPointsConfigManager(env: Env): PointsConfigManager {
  return new PointsConfigManager(env);
}

/**
 * 便捷函数：获取功能积分消费
 */
export async function getFeaturePointsCost(env: Env, feature: string): Promise<number> {
  const configManager = createPointsConfigManager(env);
  return configManager.getPointsCost(feature);
}

/**
 * 便捷函数：获取所有积分配置
 */
export async function getAllPointsConfig(env: Env): Promise<Record<string, number>> {
  const configManager = createPointsConfigManager(env);
  return configManager.getAllPointsCosts();
}

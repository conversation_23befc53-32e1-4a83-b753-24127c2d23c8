import { useState, useEffect, useCallback } from 'react'
import { audioService, type AudioEffect, type AudioLibraryResponse } from '@/api/services/audio'

// 全局音效库缓存管理
class GlobalAudioLibraryCache {
  private static instance: GlobalAudioLibraryCache
  private allAudioEffects: AudioEffect[] = []
  private isLoaded = false
  private isLoading = false
  private loadPromise: Promise<AudioEffect[]> | null = null
  private lastFetchTime = 0
  private readonly CACHE_DURATION = 30 * 60 * 1000 // 30分钟缓存

  static getInstance(): GlobalAudioLibraryCache {
    if (!GlobalAudioLibraryCache.instance) {
      GlobalAudioLibraryCache.instance = new GlobalAudioLibraryCache()
    }
    return GlobalAudioLibraryCache.instance
  }

  // 获取所有音效数据（单例模式，全局只加载一次）
  async getAllAudioEffects(): Promise<AudioEffect[]> {
    const now = Date.now()

    // 如果已经加载且缓存有效，直接返回
    if (
      this.isLoaded &&
      this.allAudioEffects.length > 0 &&
      now - this.lastFetchTime < this.CACHE_DURATION
    ) {
      return this.allAudioEffects
    }

    // 如果正在加载，返回现有的Promise
    if (this.isLoading && this.loadPromise) {
      return this.loadPromise
    }

    // 开始新的加载
    this.isLoading = true
    this.loadPromise = this.fetchAllAudioEffects()

    try {
      const effects = await this.loadPromise
      this.allAudioEffects = effects
      this.isLoaded = true
      this.lastFetchTime = now
      console.log(`🎵 音效库加载完成，共 ${effects.length} 个音效`)
      return effects
    } catch (error) {
      console.error('🎵 音效库加载失败:', error)
      // 如果加载失败但有缓存数据，返回缓存数据
      return this.allAudioEffects
    } finally {
      this.isLoading = false
      this.loadPromise = null
    }
  }

  // 实际的数据获取逻辑
  private async fetchAllAudioEffects(): Promise<AudioEffect[]> {
    let allEffects: AudioEffect[] = []
    let page = 1
    let hasMore = true

    while (hasMore) {
      const result = await audioService.getAudioEffects({
        page,
        pageSize: 200,
        isActive: true
      })

      allEffects = [...allEffects, ...result.data]

      // 检查是否还有更多数据
      if (result.pagination) {
        hasMore = page < result.pagination.totalPages
        page++
      } else {
        hasMore = false
      }
    }

    return allEffects
  }

  // 根据标签搜索音效（本地搜索，不发请求）
  async searchByTags(tags: string[]): Promise<AudioEffect[]> {
    const allEffects = await this.getAllAudioEffects()
    return allEffects.filter(effect => tags.some(tag => effect.tags.includes(tag)))
  }

  // 清除缓存
  clearCache() {
    this.allAudioEffects = []
    this.isLoaded = false
    this.lastFetchTime = 0
  }

  // 获取加载状态
  getLoadingState() {
    return {
      isLoaded: this.isLoaded,
      isLoading: this.isLoading,
      count: this.allAudioEffects.length
    }
  }
}

// 全局缓存实例
const globalAudioCache = GlobalAudioLibraryCache.getInstance()

// 音效匹配算法
export class AudioMatcher {
  // 标签权重配置
  private static readonly TAG_WEIGHTS = {
    // 最高权重标签 - 动作类型 (权重: 15) - 最具体的音效特征
    action: [
      'moan',
      'breathing',
      'orgasm',
      'blowjob',
      'biting',
      'kisses',
      'laughter',
      'licking',
      'sighs',
      'teasing'
    ],

    // 高权重标签 - 风格标签 (权重: 10) - 音效的表现风格
    style: ['cute', 'soft', 'breathy', 'sexy', 'seductive', 'playful', 'raspy'],

    // 中权重标签 - 角色类型 (权重: 8) - 角色特征
    role: ['dom', 'sub'],

    // 中低权重标签 - 强度等级 (权重: 5) - 音效强度
    intensity: ['low', 'medium', 'high', 'intense', 'extreme'],

    // 低权重标签 - 身体部位 (权重: 3) - 具体部位
    bodyPart: ['mouth', 'nose', 'back'],

    // 最低权重标签 - 性别标签 (权重: 2) - 基础属性
    gender: ['female']
  }

  // 根据标签和音频特征选择最佳音效
  static selectBestAudio(effects: AudioEffect[], tags: string[]): AudioEffect | null {
    if (effects.length === 0) return null
    if (effects.length === 1) return effects[0]

    // 1. 首先提取请求的动作类型
    const requestedActions = tags.filter(tag => this.TAG_WEIGHTS.action.includes(tag))

    // 2. 如果有动作类型要求，优先按动作类型筛选
    if (requestedActions.length > 0) {
      // 按每个动作类型分组筛选
      for (const actionTag of requestedActions) {
        const actionMatchedEffects = effects.filter(effect => effect.tags.includes(actionTag))

        if (actionMatchedEffects.length > 0) {
          console.log(
            `🎯 优先匹配动作类型: "${actionTag}"，找到 ${actionMatchedEffects.length} 个音效`
          )

          // 在匹配的动作类型中，用其他标签排序
          const bestInAction = this.selectBestFromSameAction(actionMatchedEffects, tags)
          if (bestInAction) {
            return bestInAction
          }
        }
      }
    }

    // 3. 如果没有动作类型匹配，或者没有动作类型要求，使用原有的加权匹配
    console.log(`🔄 未找到动作类型匹配，使用综合匹配`)
    return this.selectByWeightedScore(effects, tags)
  }

  // 在相同动作类型中选择最佳音效（用其他标签排序）
  private static selectBestFromSameAction(
    effects: AudioEffect[],
    tags: string[]
  ): AudioEffect | null {
    if (effects.length === 1) return effects[0]

    // 计算非动作类型标签的匹配分数
    const scoredEffects = effects.map(effect => {
      const auxiliaryScore = this.calculateAuxiliaryTagScore(effect, tags)
      const qualityScore = this.calculateQualityScore(effect)

      return {
        effect,
        auxiliaryScore,
        qualityScore,
        totalScore: auxiliaryScore * 10 + qualityScore
      }
    })

    // 按总分排序
    scoredEffects.sort((a, b) => b.totalScore - a.totalScore)

    const best = scoredEffects[0]
    console.log(
      `🎵 选择音效 #${best.effect.id}，辅助标签分数: ${
        best.auxiliaryScore
      }，质量分数: ${best.qualityScore.toFixed(2)}`
    )

    return best.effect
  }

  // 计算辅助标签分数（排除动作类型）
  private static calculateAuxiliaryTagScore(effect: AudioEffect, requestedTags: string[]): number {
    let totalScore = 0

    requestedTags.forEach(requestedTag => {
      // 跳过动作类型标签
      if (this.TAG_WEIGHTS.action.includes(requestedTag)) {
        return
      }

      if (effect.tags.includes(requestedTag)) {
        const weight = this.getTagWeight(requestedTag)
        totalScore += weight
      }
    })

    return totalScore
  }

  // 使用加权分数选择（原有逻辑，作为备用）
  private static selectByWeightedScore(effects: AudioEffect[], tags: string[]): AudioEffect | null {
    // 计算每个音效的匹配分数
    const scoredEffects = effects.map(effect => {
      const weightedScore = this.calculateWeightedTagScore(effect, tags)
      const qualityScore = this.calculateQualityScore(effect)

      return {
        effect,
        weightedScore,
        qualityScore,
        totalScore: weightedScore * 10 + qualityScore
      }
    })

    // 只保留有匹配标签的音效
    const validEffects = scoredEffects.filter(item => item.weightedScore > 0)

    if (validEffects.length === 0) return null

    // 按总分排序，选择最高分的
    validEffects.sort((a, b) => b.totalScore - a.totalScore)

    const best = validEffects[0]
    console.log(
      `🎵 选择音效 #${best.effect.id}，加权分数: ${
        best.weightedScore
      }，质量分数: ${best.qualityScore.toFixed(2)}`
    )

    return best.effect
  }

  // 计算加权标签匹配分数
  private static calculateWeightedTagScore(effect: AudioEffect, requestedTags: string[]): number {
    let totalScore = 0

    requestedTags.forEach(requestedTag => {
      if (effect.tags.includes(requestedTag)) {
        const weight = this.getTagWeight(requestedTag)
        totalScore += weight
      }
    })

    return totalScore
  }

  // 获取标签权重
  private static getTagWeight(tag: string): number {
    // 检查最高权重标签 - 动作类型
    if (this.TAG_WEIGHTS.action.includes(tag)) {
      return 15
    }

    // 检查高权重标签 - 风格标签
    if (this.TAG_WEIGHTS.style.includes(tag)) {
      return 10
    }

    // 检查中权重标签 - 角色类型
    if (this.TAG_WEIGHTS.role.includes(tag)) {
      return 8
    }

    // 检查中低权重标签 - 强度等级
    if (this.TAG_WEIGHTS.intensity.includes(tag)) {
      return 5
    }

    // 检查低权重标签 - 身体部位
    if (this.TAG_WEIGHTS.bodyPart.includes(tag)) {
      return 3
    }

    // 检查最低权重标签 - 性别标签
    if (this.TAG_WEIGHTS.gender.includes(tag)) {
      return 2
    }

    // 未知标签默认权重
    return 4
  }

  // 计算音效质量分数（不包含标签匹配，只考虑音频质量）
  private static calculateQualityScore(effect: AudioEffect): number {
    let score = 0

    // 确保数字字段为数字类型，并提供默认值
    const duration = this.parseNumericField(effect.duration, 4) // 默认4秒
    const avgLoudness = this.parseNumericField(effect.avg_loudness, -30) // 默认-30db
    const energyVariation = this.parseNumericField(effect.energy_variation, 0.01) // 默认0.01

    // 调试信息
    if (isNaN(duration) || isNaN(avgLoudness) || isNaN(energyVariation)) {
      console.warn(`⚠️ 音效 #${effect.id} 数据异常:`, {
        duration: effect.duration,
        avgLoudness: effect.avg_loudness,
        energyVariation: effect.energy_variation,
        parsed: { duration, avgLoudness, energyVariation }
      })
    }

    // 1. 音效时长偏好 (权重: 40%) - 偏好2-6秒的音效
    const idealDuration = 4 // 理想时长4秒
    const durationScore = Math.max(0, 40 - Math.abs(duration - idealDuration) * 5)
    score += durationScore

    // 2. 音频响度质量 (权重: 30%) - 理想响度约-20到-40db
    const idealLoudness = -30
    const loudnessScore = Math.max(0, 30 - Math.abs(avgLoudness - idealLoudness) / 2)
    score += loudnessScore

    // 3. 能量变化 (权重: 20%) - 能量变化适中为好
    const energyScore = Math.min(20, energyVariation * 1000)
    score += energyScore

    // 4. 特殊标签偏好 (权重: 10%) - 只在匹配标签数量相同时作为细微调整
    const specialTags = ['high', 'medium', 'low', 'intense', 'soft']
    const hasSpecialTag = effect.tags.some(tag => specialTags.includes(tag))
    if (hasSpecialTag) score += 10

    // 确保最终分数不是 NaN
    const finalScore = isNaN(score) ? 50 : score // 如果计算出错，给个默认分数

    return finalScore
  }

  // 安全解析数字字段
  private static parseNumericField(
    value: number | string | null | undefined,
    defaultValue: number
  ): number {
    if (value === null || value === undefined) {
      return defaultValue
    }

    if (typeof value === 'number') {
      return isNaN(value) ? defaultValue : value
    }

    if (typeof value === 'string') {
      const parsed = parseFloat(value)
      return isNaN(parsed) ? defaultValue : parsed
    }

    return defaultValue
  }
}

// 自定义Hook
export const useAudioLibrary = () => {
  const [error, setError] = useState<string | null>(null)

  // 预加载音效库（应用启动时调用）
  const preloadAudioLibrary = useCallback(async () => {
    setError(null)
    try {
      await globalAudioCache.getAllAudioEffects()
    } catch (err) {
      setError(err instanceof Error ? err.message : '加载音效库失败')
    }
  }, [])

  // 根据标签获取最佳音效（本地搜索，不发请求）
  const getBestAudioByTags = useCallback(async (tags: string[]): Promise<AudioEffect | null> => {
    if (!tags || tags.length === 0) return null

    setError(null)
    try {
      // 本地搜索匹配的音效
      const matchedEffects = await globalAudioCache.searchByTags(tags)

      // 选择最佳音效
      const bestAudio = AudioMatcher.selectBestAudio(matchedEffects, tags)

      if (bestAudio) {
        console.log(`🎵 标签 [${tags.join(', ')}] 匹配到音效 #${bestAudio.id}`)
      }

      return bestAudio
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取音效失败')
      return null
    }
  }, [])

  // 清除缓存
  const clearCache = useCallback(() => {
    globalAudioCache.clearCache()
  }, [])

  // 获取加载状态
  const getLoadingState = useCallback(() => {
    return globalAudioCache.getLoadingState()
  }, [])

  return {
    error,
    getBestAudioByTags,
    preloadAudioLibrary,
    clearCache,
    getLoadingState
  }
}

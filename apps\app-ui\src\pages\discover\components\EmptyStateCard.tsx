import { motion } from 'framer-motion'

interface EmptyStateCardProps {
  title: string
  buttonText: string
  onButtonClick: () => void
  type?: 'create' | 'hot'
}

export default function EmptyStateCard({ title, buttonText, onButtonClick }: EmptyStateCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: 'easeOut' }}
      className="flex justify-center px-4 pt-20"
    >
      <div className="relative w-[90%]">
        {/* 主角色图像 */}
        <div className="absolute -top-10 left-0 w-full z-10">
          <img src="/images/discover/empty-main.png" alt="character" className="size-full" />
        </div>

        {/* 主卡片容器 */}
        <div className="relative w-full h-[350px] rounded-[32px] overflow-hidden">
          {/* 背景渐变 */}
          <div
            className="absolute inset-0 rounded-[32px]"
            style={{
              background: 'linear-gradient(135deg, #1a1d2e 0%, #2d1b69 50%, #1a1d2e 100%)'
            }}
          />

          {/* 边框光效 */}
          <div
            className="absolute inset-0 rounded-[32px] p-[1px]"
            style={{
              background:
                'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0.1) 100%)'
            }}
          >
            <div className="w-full h-full rounded-[32px] bg-transparent" />
          </div>

          <div className="absolute top-0 left-0 w-full">
            {/* 背景装饰圆圈 */}
            <div className="absolute top-0 left-0 w-full">
              <img
                src="/images/discover/empty-bg.svg"
                alt="background decoration"
                className="size-full opacity-30"
              />
            </div>

            {/* 装饰星星 */}
            <div className="absolute top-0 left-0 w-full">
              <img
                src="/images/discover/empty-star.png"
                alt="star decoration"
                className="size-full"
              />
            </div>
          </div>

          {/* 内容区域 */}
          <div className="relative z-10 flex flex-col items-center justify-center h-full px-8 text-center mt-20">
            {/* 标题 */}
            <h3
              className="text-white text-xl font-semibold mb-8 tracking-wide"
              style={{ fontFamily: "'PingFang SC', sans-serif" }}
            >
              {title}
            </h3>

            {/* 按钮 */}
            <motion.button
              onClick={onButtonClick}
              className="relatxive w-full max-w-xs h-14 rounded-full overflow-hidden font-semibold text-white text-lg"
              style={{
                background: 'linear-gradient(135deg, #ff2d97 0%, #8b2fff 100%)',
                fontFamily: "'PingFang SC', sans-serif"
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: 'spring', stiffness: 400, damping: 17 }}
            >
              {/* 按钮光效 */}
              <div
                className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300"
                style={{
                  background:
                    'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.2) 100%)'
                }}
              />

              <span className="relative z-10">{buttonText}</span>
            </motion.button>
          </div>

          {/* 浮动粒子效果 */}
          <div className="absolute inset-0 pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white rounded-full opacity-30"
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${30 + (i % 3) * 20}%`
                }}
                animate={{
                  y: [-10, 10, -10],
                  opacity: [0.3, 0.7, 0.3]
                }}
                transition={{
                  duration: 3 + i * 0.5,
                  repeat: Infinity,
                  ease: 'easeInOut',
                  delay: i * 0.5
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

// Device mode related types
export interface DeviceModePatternStep {
  duration: number // 持续时间（毫秒）
  intensity: number // 强度等级，0表示停止
  description?: string // 该步骤的描述
}

export interface DeviceMode {
  id: string
  name: string
  description?: string
  pattern: DeviceModePatternStep[]
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateModeRequest {
  name: string
  description?: string
  pattern: DeviceModePatternStep[]
  isActive?: boolean
}

export interface ModePreview {
  id: string
  name: string
  description?: string
  pattern: DeviceModePatternStep[]
  statistics: {
    totalDuration: number
    totalSteps: number
    maxIntensity: number
    minIntensity: number
    avgIntensity: number
  }
  isActive: boolean
}

export interface ModeTemplate {
  id: string
  name: string
  description?: string
  pattern: DeviceModePatternStep[]
}
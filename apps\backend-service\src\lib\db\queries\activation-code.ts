import { getSupabase, getSupabaseService } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type {
  ActivationCode,
  ActivationCodeUsage,
  MembershipPlan,
  PointsPackage,
  UserSubscription,
  UserPoints,
  PointsTransaction,
} from '../schema';
import type { Env } from '@/types/env';
import { nanoid } from 'nanoid';

// ==================== 激活码生成 ====================

/**
 * 生成唯一的激活码字符串
 */
export function generateActivationCode(): string {
  // 使用 nanoid 生成 16 位激活码，只包含大写字母和数字
  return nanoid(16)
    .toUpperCase()
    .replace(/[0O1IL]/g, (match) => {
      // 替换容易混淆的字符
      const replacements: Record<string, string> = {
        '0': '2',
        O: '3',
        '1': '4',
        I: '5',
        L: '6',
      };
      return replacements[match] || match;
    });
}

/**
 * 生成批次ID
 */
export function generateBatchId(): string {
  return `BATCH_${Date.now()}_${nanoid(8)}`;
}

// ==================== 激活码管理 ====================

/**
 * 创建会员激活码
 */
export async function createMembershipActivationCode(
  env: Env,
  params: {
    membershipPlanId: string;
    description?: string;
    expiresAt?: Date;
    batchId?: string;
    createdBy: string;
  }
): Promise<ActivationCode> {
  try {
    const supabase = getSupabaseService(env);
    const code = generateActivationCode();

    const result = await supabase
      .from(TABLE_NAMES.activationCode)
      .insert({
        code,
        type: 'membership',
        membership_plan_id: params.membershipPlanId,
        description: params.description,
        expires_at: params.expiresAt?.toISOString(),
        batch_id: params.batchId,
        created_by: params.createdBy,
      })
      .select()
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('创建会员激活码失败', error);
    throw error;
  }
}

/**
 * 创建积分包激活码
 */
export async function createPointsActivationCode(
  env: Env,
  params: {
    pointsPackageId: string;
    description?: string;
    expiresAt?: Date;
    batchId?: string;
    createdBy: string;
  }
): Promise<ActivationCode> {
  try {
    const supabase = getSupabaseService(env);
    const code = generateActivationCode();

    const result = await supabase
      .from(TABLE_NAMES.activationCode)
      .insert({
        code,
        type: 'points',
        points_package_id: params.pointsPackageId,
        description: params.description,
        expires_at: params.expiresAt?.toISOString(),
        batch_id: params.batchId,
        created_by: params.createdBy,
      })
      .select()
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) throw error;
    return data;
  } catch (error) {
    console.error('创建积分包激活码失败', error);
    throw error;
  }
}

/**
 * 批量创建激活码
 */
export async function createActivationCodesBatch(
  env: Env,
  params: {
    type: 'membership' | 'points';
    membershipPlanId?: string;
    pointsPackageId?: string;
    count: number;
    description?: string;
    expiresAt?: Date;
    createdBy: string;
  }
): Promise<ActivationCode[]> {
  try {
    const batchId = generateBatchId();
    const codes: ActivationCode[] = [];

    for (let i = 0; i < params.count; i++) {
      if (params.type === 'membership' && params.membershipPlanId) {
        const code = await createMembershipActivationCode(env, {
          membershipPlanId: params.membershipPlanId,
          description: params.description,
          expiresAt: params.expiresAt,
          batchId,
          createdBy: params.createdBy,
        });
        codes.push(code);
      } else if (params.type === 'points' && params.pointsPackageId) {
        const code = await createPointsActivationCode(env, {
          pointsPackageId: params.pointsPackageId,
          description: params.description,
          expiresAt: params.expiresAt,
          batchId,
          createdBy: params.createdBy,
        });
        codes.push(code);
      }
    }

    return codes;
  } catch (error) {
    console.error('批量创建激活码失败', error);
    throw error;
  }
}

// ==================== 激活码查询 ====================

/**
 * 根据激活码字符串查找激活码
 */
export async function getActivationCodeByCode(
  env: Env,
  code: string
): Promise<ActivationCode | null> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.activationCode)
      .select(
        `
        *,
        membership_plan:membership_plan_id(*),
        points_package:points_package_id(*)
      `
      )
      .eq('code', code)
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) {
      if (error.code === 'PGRST116') return null; // 未找到
      throw error;
    }
    return data;
  } catch (error) {
    console.error('查找激活码失败', error);
    throw error;
  }
}

/**
 * 检查激活码是否可用
 */
export async function validateActivationCode(
  env: Env,
  code: string
): Promise<{
  valid: boolean;
  reason?: string;
  activationCode?: ActivationCode;
}> {
  try {
    const activationCode = await getActivationCodeByCode(env, code);

    if (!activationCode) {
      return { valid: false, reason: '激活码不存在' };
    }

    if (!activationCode.isActive) {
      return { valid: false, reason: '激活码已被禁用' };
    }

    if (activationCode.isUsed) {
      return { valid: false, reason: '激活码已被使用' };
    }

    if (activationCode.expiresAt && new Date(activationCode.expiresAt) < new Date()) {
      return { valid: false, reason: '激活码已过期' };
    }

    return { valid: true, activationCode };
  } catch (error) {
    console.error('验证激活码失败', error);
    throw error;
  }
}

/**
 * 获取用户的激活码使用历史
 */
export async function getUserActivationHistory(
  env: Env,
  userId: string,
  limit = 20,
  offset = 0
): Promise<ActivationCodeUsage[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.activationCodeUsage)
      .select(
        `
        *,
        activation_code:activation_code_id(
          code,
          type,
          description,
          membership_plan:membership_plan_id(name),
          points_package:points_package_id(name, points)
        )
      `
      )
      .eq('user_id', userId)
      .order('used_at', { ascending: false })
      .range(offset, offset + limit - 1);

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('获取用户激活历史失败', error);
    throw error;
  }
}

/**
 * 获取激活码列表（管理员用）
 */
export async function getActivationCodes(
  env: Env,
  params: {
    type?: 'membership' | 'points';
    isUsed?: boolean;
    isActive?: boolean;
    batchId?: string;
    limit?: number;
    offset?: number;
  } = {}
): Promise<ActivationCode[]> {
  try {
    const supabase = getSupabaseService(env);
    let query = supabase.from(TABLE_NAMES.activationCode).select(`
        *,
        membership_plan:membership_plan_id(name),
        points_package:points_package_id(name, points),
        created_by_user:created_by(email)
      `);

    if (params.type) {
      query = query.eq('type', params.type);
    }
    if (params.isUsed !== undefined) {
      query = query.eq('is_used', params.isUsed);
    }
    if (params.isActive !== undefined) {
      query = query.eq('is_active', params.isActive);
    }
    if (params.batchId) {
      query = query.eq('batch_id', params.batchId);
    }

    query = query.order('created_at', { ascending: false });

    if (params.limit) {
      query = query.range(params.offset || 0, (params.offset || 0) + params.limit - 1);
    }

    const { data, error } = handleSupabaseResult(await query);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('获取激活码列表失败', error);
    throw error;
  }
}

// ==================== 激活码使用逻辑 ====================

/**
 * 获取用户当前有效的会员订阅
 */
async function getCurrentUserSubscription(
  env: Env,
  userId: string
): Promise<UserSubscription | null> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .select(
        `
        *,
        plan:plan_id(*)
      `
      )
      .eq('user_id', userId)
      .eq('status', 'active')
      .gte('end_date', new Date().toISOString())
      .order('end_date', { ascending: false })
      .limit(1)
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) {
      if (error.code === 'PGRST116') return null; // 未找到
      throw error;
    }
    return data;
  } catch (error) {
    console.error('获取用户当前订阅失败', error);
    throw error;
  }
}

/**
 * 激活会员激活码
 */
async function activateMembershipCode(
  env: Env,
  activationCode: ActivationCode,
  userId: string,
  metadata: any
): Promise<{
  resultType: string;
  resultId: string;
  conflictResolution: string;
  originalMembershipId?: string;
}> {
  const supabase = getSupabase(env);

  // 获取当前用户订阅
  const currentSubscription = await getCurrentUserSubscription(env, userId);
  const membershipPlanId = activationCode.membershipPlanId;

  if (!membershipPlanId) {
    throw new Error('激活码关联的会员套餐不存在');
  }

  let resultType: string;
  let conflictResolution: string;
  let originalMembershipId: string | undefined;
  let newSubscription: UserSubscription;

  if (!currentSubscription) {
    // 用户无会员，直接创建新订阅
    const startDate = new Date();
    const endDate = new Date(
      startDate.getTime() + 30 * 24 * 60 * 60 * 1000 // 默认30天
    );

    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .insert({
        user_id: userId,
        plan_id: membershipPlanId,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        status: 'active',
      })
      .select()
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) throw error;

    newSubscription = data;
    resultType = 'membership_created';
    conflictResolution = 'no_conflict';
  } else {
    // 用户有会员，根据等级决定处理方式
    originalMembershipId = currentSubscription.id;
    // Get the current plan ID - no need to access plan object since we just need planId
    const currentPlanId = currentSubscription.planId;

    if (!currentPlanId) {
      throw new Error('当前订阅关联的套餐不存在');
    }

    // 简化策略：直接替换
    // 1. 取消当前订阅
    await supabase
      .from(TABLE_NAMES.userSubscription)
      .update({
        status: 'cancelled',
        updated_at: new Date().toISOString(),
      })
      .eq('id', currentSubscription.id);

    // 2. 创建新订阅
    const startDate = new Date();
    const endDate = new Date(
      startDate.getTime() + 30 * 24 * 60 * 60 * 1000 // 默认30天
    );

    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .insert({
        user_id: userId,
        plan_id: membershipPlanId,
        start_date: startDate.toISOString(),
        end_date: endDate.toISOString(),
        status: 'active',
      })
      .select()
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) throw error;

    newSubscription = data;

    // 简化结果类型
    resultType = 'membership_replaced';
    conflictResolution = 'replaced';
  }

  return {
    resultType,
    resultId: newSubscription.id,
    conflictResolution,
    originalMembershipId,
  };
}

/**
 * 激活积分包激活码
 */
async function activatePointsCode(
  env: Env,
  activationCode: ActivationCode,
  userId: string,
  metadata: any
): Promise<{
  resultType: string;
  resultId: string;
  conflictResolution: string;
}> {
  const pointsPackage = activationCode.pointsPackageId;

  if (!pointsPackage) {
    throw new Error('激活码关联的积分包不存在');
  }

  // 导入积分相关函数
  const { addUserPoints } = await import('./membership');

  // 添加积分到用户账户 (默认100积分)
  const transaction = await addUserPoints(env, {
    userId,
    amount: 100, // TODO: 从pointsPackageId获取实际积分
    source: 'activation_code',
    sourceId: activationCode.id,
    description: `激活码激活：积分包`,
  });

  return {
    resultType: 'points_added',
    resultId: transaction.id,
    conflictResolution: 'no_conflict',
  };
}

/**
 * 使用激活码（主要入口函数）
 */
export async function useActivationCode(
  env: Env,
  params: {
    code: string;
    userId: string;
    ipAddress?: string;
    userAgent?: string;
  }
): Promise<{
  success: boolean;
  message: string;
  data?: {
    resultType: string;
    resultId: string;
    conflictResolution: string;
    activationCode: ActivationCode;
    originalMembershipId?: string;
  };
}> {
  try {
    const supabase = getSupabase(env);

    // 1. 验证激活码
    const validation = await validateActivationCode(env, params.code);
    if (!validation.valid) {
      return {
        success: false,
        message: validation.reason || '激活码无效',
      };
    }

    const activationCode = validation.activationCode!;

    // 2. 检查用户是否已经使用过此激活码
    const existingUsage = await supabase
      .from(TABLE_NAMES.activationCodeUsage)
      .select('id')
      .eq('activation_code_id', activationCode.id)
      .eq('user_id', params.userId)
      .single();

    if (existingUsage.data) {
      return {
        success: false,
        message: '您已经使用过此激活码',
      };
    }

    // 3. 开始事务处理
    let activationResult: {
      resultType: string;
      resultId: string;
      conflictResolution: string;
      originalMembershipId?: string;
    };

    const metadata = {
      ipAddress: params.ipAddress,
      userAgent: params.userAgent,
      activatedAt: new Date().toISOString(),
    };

    // 4. 根据激活码类型执行相应的激活逻辑
    if (activationCode.type === 'membership') {
      activationResult = await activateMembershipCode(env, activationCode, params.userId, metadata);
    } else if (activationCode.type === 'points') {
      activationResult = await activatePointsCode(env, activationCode, params.userId, metadata);
    } else {
      throw new Error('不支持的激活码类型');
    }

    // 5. 标记激活码为已使用
    await supabase
      .from(TABLE_NAMES.activationCode)
      .update({
        is_used: true,
        used_at: new Date().toISOString(),
        used_by: params.userId,
        updated_at: new Date().toISOString(),
      })
      .eq('id', activationCode.id);

    // 6. 记录使用历史
    await supabase.from(TABLE_NAMES.activationCodeUsage).insert({
      activation_code_id: activationCode.id,
      user_id: params.userId,
      result_type: activationResult.resultType,
      result_id: activationResult.resultId,
      original_membership_id: activationResult.originalMembershipId,
      conflict_resolution: activationResult.conflictResolution,
      metadata: metadata,
      ip_address: params.ipAddress,
      user_agent: params.userAgent,
    });

    // 7. 返回成功结果
    return {
      success: true,
      message: getSuccessMessage(activationResult.resultType, activationCode),
      data: {
        ...activationResult,
        activationCode,
      },
    };
  } catch (error) {
    console.error('使用激活码失败', error);
    return {
      success: false,
      message: '激活失败，请稍后重试',
    };
  }
}

/**
 * 获取成功消息
 */
function getSuccessMessage(resultType: string, activationCode: ActivationCode): string {
  switch (resultType) {
    case 'membership_created':
      return `恭喜！成功激活会员套餐`;
    case 'membership_extended':
      return `恭喜！成功延长会员有效期`;
    case 'membership_upgraded':
      return `恭喜！成功升级会员套餐`;
    case 'points_added':
      const points = 0; // TODO: get from pointsPackageId
      const bonus = 0;
      return `恭喜！成功获得${points + bonus}积分`;
    default:
      return '激活成功！';
  }
}

// ==================== 管理员操作 ====================

/**
 * 禁用激活码
 */
export async function disableActivationCode(
  env: Env,
  codeId: string,
  adminUserId: string
): Promise<void> {
  try {
    const supabase = getSupabaseService(env);
    await supabase
      .from(TABLE_NAMES.activationCode)
      .update({
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('id', codeId);
  } catch (error) {
    console.error('禁用激活码失败', error);
    throw error;
  }
}

/**
 * 启用激活码
 */
export async function enableActivationCode(
  env: Env,
  codeId: string,
  adminUserId: string
): Promise<void> {
  try {
    const supabase = getSupabaseService(env);
    await supabase
      .from(TABLE_NAMES.activationCode)
      .update({
        is_active: true,
        updated_at: new Date().toISOString(),
      })
      .eq('id', codeId);
  } catch (error) {
    console.error('启用激活码失败', error);
    throw error;
  }
}

/**
 * 获取激活码统计信息
 */
export async function getActivationCodeStats(
  env: Env,
  params: {
    batchId?: string;
    type?: 'membership' | 'points';
    dateFrom?: Date;
    dateTo?: Date;
  } = {}
): Promise<{
  total: number;
  used: number;
  active: number;
  expired: number;
}> {
  try {
    const supabase = getSupabaseService(env);
    let query = supabase.from(TABLE_NAMES.activationCode).select('is_used, is_active, expires_at');

    if (params.batchId) {
      query = query.eq('batch_id', params.batchId);
    }
    if (params.type) {
      query = query.eq('type', params.type);
    }
    if (params.dateFrom) {
      query = query.gte('created_at', params.dateFrom.toISOString());
    }
    if (params.dateTo) {
      query = query.lte('created_at', params.dateTo.toISOString());
    }

    const { data, error } = handleSupabaseResult(await query);
    if (error) throw error;

    const now = new Date();
    const stats = {
      total: data?.length || 0,
      used: 0,
      active: 0,
      expired: 0,
    };

    data?.forEach((code: any) => {
      if (code.is_used) {
        stats.used++;
      } else if (!code.is_active) {
        // 已禁用的不算在active中
      } else if (code.expires_at && new Date(code.expires_at) < now) {
        stats.expired++;
      } else {
        stats.active++;
      }
    });

    return stats;
  } catch (error) {
    console.error('获取激活码统计失败', error);
    throw error;
  }
}

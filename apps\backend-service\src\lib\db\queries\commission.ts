import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { CommissionAccount, CommissionRecord, WithdrawRequest } from '../schema';
import type { Env } from '@/types/env';

// ==================== 佣金账户管理 ====================

/**
 * 获取或创建用户佣金账户
 */
export async function getOrCreateCommissionAccount(
  env: Env,
  userId: string
): Promise<CommissionAccount> {
  const supabase = getSupabase(env);

  // 先尝试获取现有账户
  const existingResult = await supabase
    .from(TABLE_NAMES.commissionAccount)
    .select('*')
    .eq('user_id', userId)
    .single();

  const { data: existing, error: existingError } = handleSupabaseSingleResult(existingResult);
  if (!existingError && existing) {
    return existing as CommissionAccount;
  }

  // 创建新账户
  const result = await supabase
    .from(TABLE_NAMES.commissionAccount)
    .insert({
      user_id: userId,
      total_earned: '0.00',
      available_balance: '0.00',
      frozen_balance: '0.00',
      total_withdrawn: '0.00',
    })
    .select()
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return data as CommissionAccount;
}

/**
 * 获取用户佣金账户
 */
export async function getUserCommissionAccount(
  env: Env,
  userId: string
): Promise<CommissionAccount | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.commissionAccount)
    .select('*')
    .eq('user_id', userId)
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data as CommissionAccount;
}

/**
 * 更新佣金账户余额
 */
export async function updateCommissionBalance(
  env: Env,
  userId: string,
  commissionAmount: number
): Promise<void> {
  const supabase = getSupabase(env);

  // 获取当前账户信息
  const account = await getOrCreateCommissionAccount(env, userId);

  const newTotalEarned = Number.parseFloat(account.totalEarned) + commissionAmount;
  const newAvailableBalance = Number.parseFloat(account.availableBalance) + commissionAmount;

  const result = await supabase
    .from(TABLE_NAMES.commissionAccount)
    .update({
      total_earned: newTotalEarned.toFixed(2),
      available_balance: newAvailableBalance.toFixed(2),
      updated_at: new Date().toISOString(),
    })
    .eq('user_id', userId);

  const { error } = handleSupabaseResult(result);
  if (error) throw error;
}

/**
 * 冻结佣金余额（用于提现申请）
 */
export async function freezeCommissionBalance(
  env: Env,
  userId: string,
  amount: number
): Promise<void> {
  const supabase = getSupabase(env);

  const account = await getUserCommissionAccount(env, userId);
  if (!account) throw new Error('佣金账户不存在');

  const currentAvailable = Number.parseFloat(account.availableBalance);
  const currentFrozen = Number.parseFloat(account.frozenBalance);

  if (amount > currentAvailable) {
    throw new Error('可提现余额不足');
  }

  const result = await supabase
    .from(TABLE_NAMES.commissionAccount)
    .update({
      available_balance: (currentAvailable - amount).toFixed(2),
      frozen_balance: (currentFrozen + amount).toFixed(2),
      updated_at: new Date().toISOString(),
    })
    .eq('user_id', userId);

  const { error } = handleSupabaseResult(result);
  if (error) throw error;
}

/**
 * 解冻佣金余额（用于提现拒绝）
 */
export async function unfreezeCommissionBalance(
  env: Env,
  userId: string,
  amount: number
): Promise<void> {
  const supabase = getSupabase(env);

  const account = await getUserCommissionAccount(env, userId);
  if (!account) throw new Error('佣金账户不存在');

  const currentAvailable = Number.parseFloat(account.availableBalance);
  const currentFrozen = Number.parseFloat(account.frozenBalance);

  const result = await supabase
    .from(TABLE_NAMES.commissionAccount)
    .update({
      available_balance: (currentAvailable + amount).toFixed(2),
      frozen_balance: Math.max(0, currentFrozen - amount).toFixed(2),
      updated_at: new Date().toISOString(),
    })
    .eq('user_id', userId);

  const { error } = handleSupabaseResult(result);
  if (error) throw error;
}

/**
 * 完成提现（从冻结余额转为已提现）
 */
export async function completeWithdraw(env: Env, userId: string, amount: number): Promise<void> {
  const supabase = getSupabase(env);

  const account = await getUserCommissionAccount(env, userId);
  if (!account) throw new Error('佣金账户不存在');

  const currentFrozen = Number.parseFloat(account.frozenBalance);
  const currentWithdrawn = Number.parseFloat(account.totalWithdrawn);

  const result = await supabase
    .from(TABLE_NAMES.commissionAccount)
    .update({
      frozen_balance: Math.max(0, currentFrozen - amount).toFixed(2),
      total_withdrawn: (currentWithdrawn + amount).toFixed(2),
      updated_at: new Date().toISOString(),
    })
    .eq('user_id', userId);

  const { error } = handleSupabaseResult(result);
  if (error) throw error;
}

// ==================== 佣金记录管理 ====================

/**
 * 创建佣金记录
 */
export async function createCommissionRecord(
  env: Env,
  data: {
    inviterId: string;
    inviteeId: string;
    orderId: string;
    commissionAmount: number;
    sourceType: 'membership' | 'points_package';
    sourceAmount: number;
    commissionRate: number;
  }
): Promise<CommissionRecord> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.commissionRecord)
    .insert({
      inviter_id: data.inviterId,
      invitee_id: data.inviteeId,
      order_id: data.orderId,
      commission_amount: data.commissionAmount.toFixed(2),
      source_type: data.sourceType,
      source_amount: data.sourceAmount.toFixed(2),
      commission_rate: data.commissionRate.toFixed(4),
      status: 'settled',
      settled_at: new Date().toISOString(),
    })
    .select()
    .single();

  const { data: record, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return record as CommissionRecord;
}

/**
 * 获取用户佣金记录列表
 */
export async function getUserCommissionRecords(
  env: Env,
  userId: string,
  page = 1,
  limit = 10
): Promise<{
  list: Array<CommissionRecord & { inviteeEmail: string; orderNo: string }>;
  total: number;
}> {
  const supabase = getSupabase(env);
  const offset = (page - 1) * limit;

  // 获取佣金记录列表
  const listResult = await supabase
    .from(TABLE_NAMES.commissionRecord)
    .select(`
      *,
      user:invitee_id (
        email
      ),
      payment_order:order_id (
        order_no
      )
    `)
    .eq('inviter_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  const { data: listData } = handleSupabaseResult(listResult);

  // 获取总数
  const countResult = await supabase
    .from(TABLE_NAMES.commissionRecord)
    .select('id', { count: 'exact' })
    .eq('inviter_id', userId);

  const { count } = countResult;

  const list = (listData || []).map((item: any) => ({
    ...item,
    inviteeEmail: item.user?.email || '',
    orderNo: item.payment_order?.order_no || '',
  })) as Array<CommissionRecord & { inviteeEmail: string; orderNo: string }>;

  return {
    list,
    total: count || 0,
  };
}

/**
 * 获取用户佣金统计
 */
export async function getUserCommissionStats(
  env: Env,
  userId: string
): Promise<{
  totalEarned: number;
  availableBalance: number;
  totalWithdrawn: number;
  totalCommissions: number;
  thisMonthCommissions: number;
}> {
  const supabase = getSupabase(env);

  // 获取账户信息
  const account = await getUserCommissionAccount(env, userId);

  // 获取佣金记录统计
  const recordsResult = await supabase
    .from(TABLE_NAMES.commissionRecord)
    .select('commission_amount, settled_at')
    .eq('inviter_id', userId)
    .eq('status', 'settled');

  const { data: records } = handleSupabaseResult(recordsResult);

  // 计算本月佣金
  const thisMonth = new Date();
  thisMonth.setDate(1);
  thisMonth.setHours(0, 0, 0, 0);

  const thisMonthCommissions = (records || [])
    .filter((record: any) => record.settled_at && new Date(record.settled_at) >= thisMonth)
    .reduce((sum: number, record: any) => sum + Number.parseFloat(record.commission_amount), 0);

  return {
    totalEarned: account ? Number.parseFloat(account.totalEarned) : 0,
    availableBalance: account ? Number.parseFloat(account.availableBalance) : 0,
    totalWithdrawn: account ? Number.parseFloat(account.totalWithdrawn) : 0,
    totalCommissions: records ? records.length : 0,
    thisMonthCommissions: Math.round(thisMonthCommissions * 100) / 100,
  };
}

// ==================== 提现申请管理 ====================

/**
 * 创建提现申请
 */
export async function createWithdrawRequest(
  env: Env,
  data: {
    userId: string;
    amount: number;
    feeAmount: number;
    actualAmount: number;
    bankInfo: any;
  }
): Promise<WithdrawRequest> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .insert({
      user_id: data.userId,
      amount: data.amount.toFixed(2),
      fee_amount: data.feeAmount.toFixed(2),
      actual_amount: data.actualAmount.toFixed(2),
      status: 'pending',
      bank_info: data.bankInfo,
    })
    .select()
    .single();

  const { data: request, error } = handleSupabaseSingleResult(result);
  if (error) throw error;
  return request as WithdrawRequest;
}

/**
 * 获取用户提现记录
 */
export async function getUserWithdrawRequests(
  env: Env,
  userId: string,
  page = 1,
  limit = 10
): Promise<{
  list: WithdrawRequest[];
  total: number;
}> {
  const supabase = getSupabase(env);
  const offset = (page - 1) * limit;

  // 获取提现记录列表
  const listResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  const { data: listData } = handleSupabaseResult(listResult);

  // 获取总数
  const countResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select('id', { count: 'exact' })
    .eq('user_id', userId);

  const { count } = countResult;

  return {
    list: (listData || []) as WithdrawRequest[],
    total: count || 0,
  };
}

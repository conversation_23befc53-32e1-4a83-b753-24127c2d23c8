import React, { useState, useEffect, useRef, useCallback } from 'react'
import { motion } from 'framer-motion'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'
import { DraggableSlider } from '../ui/draggable-slider'
import { type Device } from '@/api/services/devices'
import { useDeviceStore } from '../../stores/device-store'
import { commandQueueManager } from '../../pages/interactive/utils/bluetoothUtils'
import { modes } from '../../pages/interactive/utils/deviceModes'
import { addToast } from '@heroui/react'

interface DeviceControlProps {
  device: Device
  onBack?: () => void
}

export const DeviceControl: React.FC<DeviceControlProps> = ({ device, onBack }) => {
  const { t } = useTranslation('device')
  const [selectedMode, setSelectedMode] = useState<number | null>(null)
  const [localIntensity, setLocalIntensity] = useState<{ [key: string]: number }>({})
  const debounceTimerRef = useRef<{ [key: string]: NodeJS.Timeout }>({})
  const isInitializedRef = useRef(false)

  // 使用全局设备状态
  const {
    currentIntensity,
    isBluetoothInitialized,
    bluetoothError,
    setIntensity,
    initializeBluetooth,
    disconnectDevice
  } = useDeviceStore()

  // 仅更新状态而不发送蓝牙命令的函数
  const updateIntensityStateOnly = useCallback((key: string, intensity: number) => {
    const state = useDeviceStore.getState()
    const { connectedDevice, currentIntensity } = state

    if (connectedDevice) {
      useDeviceStore.setState({
        currentIntensity: {
          ...currentIntensity,
          [key]: intensity
        }
      })
      console.log(`仅更新状态: ${key} = ${intensity}`)
    }
  }, [])

  // 发送停止指令到所有设备功能
  const sendStopCommands = () => {
    if (!device?.func) return

    try {
      console.log('🛑 发送停止命令到所有设备功能')
      const stopCommands: string[] = []

      device.func.forEach(func => {
        const stopCommand = func.commands.find(cmd => cmd.intensity === -1)
        if (stopCommand) {
          stopCommands.push(stopCommand.command)
        }
      })

      if (stopCommands.length > 0) {
        commandQueueManager.addCommands(stopCommands)
        console.log('已发送停止命令:', stopCommands)
      }
    } catch (error) {
      console.error('发送停止命令失败:', error)
    }
  }

  const handleModeSelect = async (modeId: number) => {
    // 检查蓝牙是否已初始化
    if (!isBluetoothInitialized) {
      addToast({
        title: t('control.bluetooth_not_initialized'),
        description: t('control.wait_bluetooth_init'),
        color: 'warning'
      })
      return
    }

    // 检查是否有蓝牙错误
    if (bluetoothError) {
      addToast({
        title: t('control.bluetooth_connection_error'),
        description: bluetoothError,
        color: 'danger'
      })
      return
    }

    // 如果重复点击同一个模式，则取消选中并发送停止指令
    if (selectedMode === modeId) {
      console.log(`取消选中模式: ${modeId}`)
      setSelectedMode(null)
      sendStopCommands()
      return
    }

    // 查找对应的模式
    const selectedModeData = modes.find(mode => mode.id === modeId)
    if (!selectedModeData) {
      console.warn(t('control.mode_not_found', { modeId }))
      return
    }

    try {
      console.log(`${t('control.select_classic_mode')}: ${selectedModeData.name}, 命令: ${selectedModeData.command}`)

      // 设置选中状态
      setSelectedMode(modeId)

      // 发送蓝牙广播命令
      commandQueueManager.addCommands([selectedModeData.command])
    } catch (error) {
      console.error(t('control.send_classic_mode_failed'), error)
    }
  }

  // 初始化本地强度状态
  useEffect(() => {
    if (!isInitializedRef.current && Object.keys(currentIntensity).length > 0) {
      setLocalIntensity(currentIntensity)
      isInitializedRef.current = true
    }
  }, [currentIntensity])

  // 初始化蓝牙服务
  useEffect(() => {
    // 蓝牙初始化由全局store管理，这里只需要触发一次
    if (!isBluetoothInitialized) {
      initializeBluetooth()
    }
  }, [isBluetoothInitialized, initializeBluetooth])

  // 清理定时器
  useEffect(() => {
    return () => {
      Object.values(debounceTimerRef.current).forEach(timer => {
        if (timer) clearTimeout(timer)
      })
    }
  }, [])

  // 处理强度变化（带防抖）
  const handleIntensityChange = useCallback(
    (functionKey: string, intensity: number) => {
      const statusText = intensity === 0 ? t('control.off') : t('control.level', { level: intensity })
    console.log(t('control.device_function_set', { functionKey, statusText, intensity }))

      // 立即更新本地状态，提供即时的视觉反馈
      setLocalIntensity(prev => ({
        ...prev,
        [functionKey]: intensity
      }))

      // 清除之前的定时器
      if (debounceTimerRef.current[functionKey]) {
        clearTimeout(debounceTimerRef.current[functionKey])
      }

      // 设置新的防抖定时器，300ms后发送蓝牙命令
      debounceTimerRef.current[functionKey] = setTimeout(() => {
        console.log(`🔄 ${t('control.debounce_delay')}: ${functionKey} = ${intensity}`)

        // 如果强度为0，需要发送停止指令（intensity: -1）
        if (intensity === 0) {
          console.log(`🛑 ${t('control.intensity_zero')}: ${functionKey}`)
          // 查找停止命令并直接发送
          const targetFunction = device.func.find(func => func.key === functionKey)
          if (targetFunction) {
            const stopCommand = targetFunction.commands.find(cmd => cmd.intensity === -1)
            if (stopCommand) {
              commandQueueManager.addCommands([stopCommand.command])
              console.log(`${t('control.stop_command_sent')}: ${stopCommand.command}`)
            } else {
              console.warn(t('control.stop_command_not_found', { functionKey }))
            }
          }
          // 仅更新全局状态为0，不发送额外的蓝牙命令
          updateIntensityStateOnly(functionKey, 0)
        } else {
          // 正常强度，使用原有逻辑
          setIntensity(functionKey, intensity)
        }

        delete debounceTimerRef.current[functionKey]
      }, 300) // 300ms 防抖延迟
    },
    [setIntensity, device.func, updateIntensityStateOnly]
  )

  return (
    <div className="min-h-screen bg-[#121521] relative overflow-hidden">
      {/* 背景装饰元素 */}
      <div className="absolute top-0 left-0 w-screen pointer-events-none">
        {/* 装饰元素1 - 紫色渐变 */}
        <img src="/images/device/bg-2.svg" alt="" className="w-screen" />
      </div>

      {/* 顶部标题栏 */}
      <div className="relative z-10 pt-16 pb-4">
        <div className="text-center">
        <h1 className="text-white text-xl font-semibold">{t('control.title')}</h1>
      </div>
      </div>

      {/* 设备信息区域 */}
      <div className="relative z-10 pl-7 -mr-7">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h2 className="text-white text-2xl font-semibold mb-1.5">{device.name}</h2>
            <div className="flex flex-col gap-1">
              <p className="text-[#7c85b6] text-sm">{t('control.connected')}</p>
              <div className="flex items-center gap-1">
                <div
                  className={`w-2 h-2 rounded-full ${
                    bluetoothError
                      ? 'bg-red-400'
                      : isBluetoothInitialized
                      ? 'bg-green-400'
                      : 'bg-yellow-400'
                  }`}
                ></div>
                <span className="text-[#7c85b6] text-xs">
                  {bluetoothError
                    ? t('control.bluetooth_error')
                    : isBluetoothInitialized
                    ? t('control.bluetooth_ready')
                    : t('control.bluetooth_initializing')}
                </span>
              </div>
              {bluetoothError && <p className="text-red-400 text-xs max-w-48">{bluetoothError}</p>}
            </div>
          </div>
          <div className="w-50 h-50 flex items-center justify-center">
            {device.pic ? (
              <img src={device.pic} alt={device.name} className="w-50 h-50 object-contain" />
            ) : (
              <div className="w-32 h-32 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center">
                <div className="w-24 h-24 bg-gradient-to-br from-yellow-300 to-green-400 rounded-full transform rotate-45"></div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 控制卡片区域 */}
      <div
        className={`relative z-10 px-2 mt-8 grid gap-4 ${
          device.func.length === 1 ? 'grid-cols-1' : 'grid-cols-2'
        }`}
      >
        {device.func.map((func, index) => {
          // 为不同功能分配不同颜色
          const colors = ['#ff2d97', '#892fff', '#00d4ff', '#ff9500', '#00ff88']
          const color = colors[index % colors.length]
          // 使用本地状态提供即时反馈，如果本地状态不存在则使用全局状态
          const functionIntensity = localIntensity[func.key] ?? currentIntensity[func.key] ?? 0

          return (
            <motion.div
              key={func.key}
              className="bg-[#1d2135] rounded-3xl p-5"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <h3 className="text-white text-xl font-semibold text-center mb-8">{func.name}</h3>

              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-[#7c85b6] text-sm">{t('control.intensity')}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-xl font-semibold" style={{ color }}>
                      {functionIntensity === 0 ? t('control.off') : t('control.level', { level: functionIntensity })}
                    </span>
                  </div>
                </div>

                {/* 可拖动滑块 */}
                <DraggableSlider
                  value={functionIntensity}
                  onChange={value => handleIntensityChange(func.key, value)}
                  color={color}
                  min={0}
                  max={3}
                />
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* 经典模式区域 */}
      <div className="relative z-10 mt-8 bg-[#1d2135] rounded-t-[32px] flex-1 min-h-[340px]">
        <div className="px-5 pt-6">
          <div className="flex items-center gap-3 mb-6">
            <h3 className="text-white text-sm">{t('control.classic_mode')}</h3>
            {/* <div className="w-5 h-1 bg-[#ff2d97] rounded-md"></div> */}
          </div>

          {/* 模式网格 */}
          <div className="grid grid-cols-3 gap-2">
            {modes.map(mode => (
              <motion.div
                key={mode.id}
                className={`bg-[#161928] rounded-3xl h-32 flex flex-col items-center justify-center cursor-pointer transition-all duration-200 ${
                  selectedMode === mode.id ? 'ring-2 ring-[#ff2d97]' : ''
                }`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => handleModeSelect(mode.id)}
              >
                <div className="w-10 h-10 rounded-lg mb-1.5 flex items-center justify-center text-xl">
                  <img
                    src={`/images/device/modes/${mode.id}.svg`}
                    alt={mode.name}
                    className="w-10 h-10"
                  />
                </div>
                <span className="text-[#9ca3c9] text-base">{mode.name}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </div>

      {/* 底部断开连接按钮 */}
      <div className="relative z-10 bg-[#1d2135] px-5 pb-8 pt-4">
        <motion.button
          className="w-full h-12 bg-red-500 hover:bg-red-600 rounded-full text-white text-base font-medium flex items-center justify-center gap-2 transition-colors"
          onClick={async () => {
            try {
              // 先发送停止指令
              sendStopCommands()

              // 清除选中的模式
              setSelectedMode(null)

              // 等待一小段时间确保停止指令发送完成
              await new Promise(resolve => setTimeout(resolve, 500))

              // 断开设备连接（这会调用 device-store 中的 disconnectDevice，已经包含了 resetAllIntensity）
              await disconnectDevice()

              // 调用父组件的回调
              onBack?.()
            } catch (error) {
              console.error(t('control.disconnect_failed'), error)
              addToast({
                title: t('control.disconnect_failed'),
                description: t('connection_form.connect_failed'),
                color: 'danger'
              })
            }
          }}
          whileTap={{ scale: 0.98 }}
        >
          <Icon icon="solar:power-bold" width={20} />
          {t('control.disconnect_button')}
        </motion.button>

        {/* 安全区域指示器 */}
        <div className="flex items-center justify-center mt-4">
          <div className="w-[134px] h-[5px] bg-white rounded-full"></div>
        </div>
      </div>
    </div>
  )
}

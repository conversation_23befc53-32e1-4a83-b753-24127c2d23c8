// 图片生成任务状态
export type TaskStatus = 'pending' | 'processing' | 'completed' | 'failed';

// 图片生成请求参数
export interface GenerateImageRequest {
  prompt: string;
  imageUrl?: string; // 可选的输入图片URL
}

// 图片生成任务信息
export interface ImageGenerationTask {
  taskId: string;
  status: TaskStatus;
  progress?: number;
  estimatedSteps?: number;
  completedSteps?: number;
  imageUrl?: string;
  errorMessage?: string;
  prompt: string;
  inputImageUrl?: string;
}

// 云雾AI API 响应
export interface YunwuApiResponse {
  data: Array<{
    url?: string;
    b64_json?: string;
  }>;
}

// Insa3D API 响应类型
export interface Insa3DRunTaskResponse {
  task_id: string;
  estimated_steps: number;
  status: string;
}

export interface Insa3DTaskStatusResponse {
  task_id: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'EXECUTING' | 'COMPLETED' | 'FAILED';
  estimated_steps: number;
  completed_steps: number;
  image_urls?: string[];
  error_message?: string;
}

// API 配置接口
export interface ApiConfig {
  baseUrl: string;
  token: string;
}

// 生成器类型
export type GeneratorType = 'yunwu' | 'insa3d-v2' | 'insa3d-v3';

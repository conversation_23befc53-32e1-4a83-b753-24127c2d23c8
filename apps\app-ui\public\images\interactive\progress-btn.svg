<svg width="27" height="14" viewBox="0 0 27 14" fill="none" xmlns="http://www.w3.org/2000/svg">
<foreignObject x="-8" y="-8" width="43" height="30"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4px);clip-path:url(#bgblur_0_410_1599_clip_path);height:100%;width:100%"></div></foreignObject><rect data-figma-bg-blur-radius="8" x="0.25" y="0.25" width="26.5" height="13.5" rx="6.75" fill="white" fill-opacity="0.7" stroke="url(#paint0_linear_410_1599)" stroke-width="0.5"/>
<rect x="8" y="5" width="1" height="4" rx="0.5" fill="white"/>
<rect x="13" y="4" width="1" height="6" rx="0.5" fill="white"/>
<rect x="18" y="5" width="1" height="4" rx="0.5" fill="white"/>
<defs>
<clipPath id="bgblur_0_410_1599_clip_path" transform="translate(8 8)"><rect x="0.25" y="0.25" width="26.5" height="13.5" rx="6.75"/>
</clipPath><linearGradient id="paint0_linear_410_1599" x1="5.5" y1="-5" x2="24" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="0.528846" stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>

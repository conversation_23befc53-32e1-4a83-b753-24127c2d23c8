// 会员相关消息
export const membershipMessages = {
  zh: {
    // 基础会员消息
    'membership.upgraded': '会员升级成功',
    'membership.expired': '会员已过期',
    'membership.not_found': '用户暂无活跃订阅',
    'membership.active': '会员状态正常',
    'membership.inactive': '会员状态非活跃',
    'membership.plan_not_found': '会员计划不存在',
    'membership.subscription_created': '订阅创建成功',
    'membership.subscription_cancelled': '订阅取消成功',

    // 错误消息
    'membership.get_plans_failed': '获取会员计划失败',
    'membership.get_plan_details_failed': '获取会员计划详情失败',
    'membership.get_subscription_failed': '获取订阅状态失败',
    'membership.debug_subscription_failed': '调试失败',
    'membership.create_subscription_failed': '创建订阅失败',
    'membership.get_subscription_history_failed': '获取订阅历史失败',
    'membership.get_points_failed': '获取积分信息失败',
    'membership.get_transactions_failed': '获取交易记录失败',
    'membership.spend_points_failed': '消费积分失败',
    'membership.get_packages_failed': '获取积分套餐失败',
    'membership.get_package_details_failed': '获取积分套餐详情失败',
    'membership.package_not_found': '积分套餐不存在',
    'membership.get_status_failed': '获取会员状态失败',
    'membership.permission_check_failed': '权限检查失败',
    'membership.verify_access_failed': '功能访问验证失败',
    'membership.consume_points_failed': '积分消费失败',
    'membership.get_config_failed': '获取功能配置失败',
    'membership.user_not_authenticated': '用户未认证',
    'membership.unknown_feature': '未知的功能类型',
    'membership.insufficient_points': '积分不足，需要{required}积分，当前{available}积分',
    'membership.member_required': '需要会员权限',
    'membership.points_sufficient': '积分充足',
    'membership.member_points_sufficient': '会员用户，积分充足',
    'membership.can_create_character': '可以创建角色',
    'membership.character_limit_reached': '已达角色创建上限',
    'membership.permission_check_error': '权限检查失败',

    // 成功消息
    'membership.points_consumed': '成功消费{amount}积分',
    'membership.points_spent': '成功消费{amount}积分'
  },
  'zh-TW': {
    // 基礎會員訊息
    'membership.upgraded': '會員升級成功',
    'membership.expired': '會員已過期',
    'membership.not_found': '使用者暫無活躍訂閱',
    'membership.active': '會員狀態正常',
    'membership.inactive': '會員狀態非活躍',
    'membership.plan_not_found': '會員方案不存在',
    'membership.subscription_created': '訂閱建立成功',
    'membership.subscription_cancelled': '訂閱取消成功',

    // 錯誤訊息
    'membership.get_plans_failed': '取得會員方案失敗',
    'membership.get_plan_details_failed': '取得會員方案詳情失敗',
    'membership.get_subscription_failed': '取得訂閱狀態失敗',
    'membership.debug_subscription_failed': '偵錯失敗',
    'membership.create_subscription_failed': '建立訂閱失敗',
    'membership.get_subscription_history_failed': '取得訂閱歷史失敗',
    'membership.get_points_failed': '取得積分資訊失敗',
    'membership.get_transactions_failed': '取得交易記錄失敗',
    'membership.spend_points_failed': '消費積分失敗',
    'membership.get_packages_failed': '取得積分套餐失敗',
    'membership.get_package_details_failed': '取得積分套餐詳情失敗',
    'membership.package_not_found': '積分套餐不存在',
    'membership.get_status_failed': '取得會員狀態失敗',
    'membership.permission_check_failed': '權限檢查失敗',
    'membership.verify_access_failed': '功能存取驗證失敗',
    'membership.consume_points_failed': '積分消費失敗',
    'membership.get_config_failed': '取得功能設定失敗',
    'membership.user_not_authenticated': '使用者未驗證',
    'membership.unknown_feature': '未知的功能類型',
    'membership.insufficient_points': '積分不足，需要{required}積分，目前{available}積分',
    'membership.member_required': '需要會員權限',
    'membership.points_sufficient': '積分充足',
    'membership.member_points_sufficient': '會員使用者，積分充足',
    'membership.can_create_character': '可以建立角色',
    'membership.character_limit_reached': '已達角色建立上限',
    'membership.permission_check_error': '權限檢查失敗',

    // 成功訊息
    'membership.points_consumed': '成功消費{amount}積分',
    'membership.points_spent': '成功消費{amount}積分'
  },
  ja: {
    // 基本メンバーシップメッセージ
    'membership.upgraded': 'メンバーシップのアップグレードに成功しました',
    'membership.expired': 'メンバーシップの有効期限が切れています',
    'membership.not_found': 'ユーザーにアクティブなサブスクリプションがありません',
    'membership.active': 'メンバーシップは正常です',
    'membership.inactive': 'メンバーシップは非アクティブです',
    'membership.plan_not_found': 'メンバーシッププランが存在しません',
    'membership.subscription_created': 'サブスクリプションの作成に成功しました',
    'membership.subscription_cancelled': 'サブスクリプションのキャンセルに成功しました',

    // エラーメッセージ
    'membership.get_plans_failed': 'メンバーシッププランの取得に失敗しました',
    'membership.get_plan_details_failed': 'メンバーシッププラン詳細の取得に失敗しました',
    'membership.get_subscription_failed': 'サブスクリプション状態の取得に失敗しました',
    'membership.debug_subscription_failed': 'デバッグに失敗しました',
    'membership.create_subscription_failed': 'サブスクリプションの作成に失敗しました',
    'membership.get_subscription_history_failed': 'サブスクリプション履歴の取得に失敗しました',
    'membership.get_points_failed': 'ポイント情報の取得に失敗しました',
    'membership.get_transactions_failed': '取引履歴の取得に失敗しました',
    'membership.spend_points_failed': 'ポイントの消費に失敗しました',
    'membership.get_packages_failed': 'ポイントパッケージの取得に失敗しました',
    'membership.get_package_details_failed': 'ポイントパッケージ詳細の取得に失敗しました',
    'membership.package_not_found': 'ポイントパッケージが存在しません',
    'membership.get_status_failed': 'メンバーシップ状態の取得に失敗しました',
    'membership.permission_check_failed': '権限の確認に失敗しました',
    'membership.verify_access_failed': '機能アクセスの検証に失敗しました',
    'membership.consume_points_failed': 'ポイントの消費に失敗しました',
    'membership.get_config_failed': '機能設定の取得に失敗しました',
    'membership.user_not_authenticated': 'ユーザーが認証されていません',
    'membership.unknown_feature': '不明な機能タイプです',
    'membership.insufficient_points': 'ポイントが不足しています。{required}ポイントが必要ですが、現在{available}ポイントです',
    'membership.member_required': 'メンバーシップが必要です',
    'membership.points_sufficient': 'ポイントは十分です',
    'membership.member_points_sufficient': 'メンバーユーザーです。ポイントは十分です',
    'membership.can_create_character': 'キャラクターを作成できます',
    'membership.character_limit_reached': 'キャラクター作成の上限に達しました',
    'membership.permission_check_error': '権限の確認に失敗しました',

    // 成功メッセージ
    'membership.points_consumed': '{amount}ポイントの消費に成功しました',
    'membership.points_spent': '{amount}ポイントの消費に成功しました'
  },
  es: {
    // Mensajes básicos de membresía
    'membership.upgraded': 'Membresía actualizada correctamente',
    'membership.expired': 'La membresía ha expirado',
    'membership.not_found': 'El usuario no tiene una suscripción activa',
    'membership.active': 'Estado de membresía activo',
    'membership.inactive': 'Estado de membresía inactivo',
    'membership.plan_not_found': 'El plan de membresía no existe',
    'membership.subscription_created': 'Suscripción creada correctamente',
    'membership.subscription_cancelled': 'Suscripción cancelada correctamente',

    // Mensajes de error
    'membership.get_plans_failed': 'Error al obtener los planes de membresía',
    'membership.get_plan_details_failed': 'Error al obtener los detalles del plan de membresía',
    'membership.get_subscription_failed': 'Error al obtener el estado de la suscripción',
    'membership.debug_subscription_failed': 'Error en la depuración',
    'membership.create_subscription_failed': 'Error al crear la suscripción',
    'membership.get_subscription_history_failed': 'Error al obtener el historial de suscripciones',
    'membership.get_points_failed': 'Error al obtener la información de puntos',
    'membership.get_transactions_failed': 'Error al obtener el historial de transacciones',
    'membership.spend_points_failed': 'Error al gastar puntos',
    'membership.get_packages_failed': 'Error al obtener los paquetes de puntos',
    'membership.get_package_details_failed': 'Error al obtener los detalles del paquete de puntos',
    'membership.package_not_found': 'El paquete de puntos no existe',
    'membership.get_status_failed': 'Error al obtener el estado de la membresía',
    'membership.permission_check_failed': 'Error al verificar los permisos',
    'membership.verify_access_failed': 'Error al verificar el acceso a la función',
    'membership.consume_points_failed': 'Error al consumir puntos',
    'membership.get_config_failed': 'Error al obtener la configuración de funciones',
    'membership.user_not_authenticated': 'Usuario no autenticado',
    'membership.unknown_feature': 'Tipo de función desconocido',
    'membership.insufficient_points': 'Puntos insuficientes, se necesitan {required} puntos, actualmente tiene {available} puntos',
    'membership.member_required': 'Se requiere membresía',
    'membership.points_sufficient': 'Puntos suficientes',
    'membership.member_points_sufficient': 'Usuario miembro, puntos suficientes',
    'membership.can_create_character': 'Puede crear personaje',
    'membership.character_limit_reached': 'Se alcanzó el límite de creación de personajes',
    'membership.permission_check_error': 'Error al verificar permisos',

    // Mensajes de éxito
    'membership.points_consumed': 'Se consumieron {amount} puntos correctamente',
    'membership.points_spent': 'Se gastaron {amount} puntos correctamente'
  },
  en: {
    // Basic membership messages
    'membership.upgraded': 'Membership upgraded successfully',
    'membership.expired': 'Membership has expired',
    'membership.not_found': 'You don\'t have an active subscription',
    'membership.active': 'Membership is active',
    'membership.inactive': 'Membership is inactive',
    'membership.plan_not_found': 'Membership plan not found',
    'membership.subscription_created': 'Subscription created successfully',
    'membership.subscription_cancelled': 'Subscription cancelled successfully',

    // Error messages
    'membership.get_plans_failed': 'Failed to get membership plans',
    'membership.get_plan_details_failed': 'Failed to get membership plan details',
    'membership.get_subscription_failed': 'Failed to get subscription status',
    'membership.debug_subscription_failed': 'Debug failed',
    'membership.create_subscription_failed': 'Failed to create subscription',
    'membership.get_subscription_history_failed': 'Failed to get subscription history',
    'membership.get_points_failed': 'Failed to get points information',
    'membership.get_transactions_failed': 'Failed to get transaction records',
    'membership.spend_points_failed': 'Failed to spend points',
    'membership.get_packages_failed': 'Failed to get points packages',
    'membership.get_package_details_failed': 'Failed to get points package details',
    'membership.package_not_found': 'Points package not found',
    'membership.get_status_failed': 'Failed to get membership status',
    'membership.permission_check_failed': 'Permission check failed',
    'membership.verify_access_failed': 'Feature access verification failed',
    'membership.consume_points_failed': 'Points consumption failed',
    'membership.get_config_failed': 'Failed to get feature configuration',
    'membership.user_not_authenticated': 'User not authenticated',
    'membership.unknown_feature': 'Unknown feature type',
    'membership.insufficient_points':
      'Insufficient points, need {required} points, current {available} points',
    'membership.member_required': 'Membership required',
    'membership.points_sufficient': 'Sufficient points',
    'membership.member_points_sufficient': 'You have sufficient points as a member',
    'membership.can_create_character': 'Can create character',
    'membership.character_limit_reached': 'Character creation limit reached',
    'membership.permission_check_error': 'Permission check failed',

    // Success messages
    'membership.points_consumed': 'Successfully consumed {amount} points',
    'membership.points_spent': 'Successfully spent {amount} points'
  }
}

import { Button } from '@heroui/react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'

interface RoleActionsProps {
  isFromChat: boolean
  isFromInteractive: boolean
  hasExistingChat: boolean
  isChatLoading: boolean
  isInteractiveLoading: boolean
  isNewChatLoading: boolean
  onChatMode: () => void
  onInteractiveMode: () => void
  onNewChat: () => void
}

export function RoleActions({
  isFromChat,
  isFromInteractive,
  hasExistingChat,
  isChatLoading,
  isInteractiveLoading,
  isNewChatLoading,
  onChatMode,
  onInteractiveMode,
  onNewChat
}: RoleActionsProps) {
  return (
    <motion.div
      className="flex gap-3 px-4 mb-4"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.8, duration: 0.4 }}
    >
      {/* 静谧私语 */}
      {!isFromChat && (
        <Button
          color="primary"
          variant="solid"
          className="flex-1 h-12"
          onPress={onChatMode}
          isLoading={isChatLoading}
          startContent={
            !isChatLoading && <Icon icon="solar:chat-round-line-linear" width={20} />
          }
        >
          {hasExistingChat ? '继续对话' : '静谧私语'}
        </Button>
      )}

      {/* 激情互动 */}
      {!isFromInteractive && (
        <Button
          color="secondary"
          variant="solid"
          className="flex-1 h-12"
          onPress={onInteractiveMode}
          isLoading={isInteractiveLoading}
          startContent={
            !isInteractiveLoading && <Icon icon="solar:gameboy-linear" width={20} />
          }
        >
          激情互动
        </Button>
      )}

      {/* 新对话 */}
      <Button
        variant="bordered"
        className="flex-1 h-12"
        onPress={onNewChat}
        isLoading={isNewChatLoading}
        startContent={!isNewChatLoading && <Icon icon="solar:refresh-linear" width={20} />}
      >
        新对话
      </Button>
    </motion.div>
  )
}

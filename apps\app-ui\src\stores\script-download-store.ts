import { create } from 'zustand'
import { scriptDownloadService, type DownloadProgress } from '@/services/script-download'
import { type ScriptContent } from '@/lib/database'

interface ScriptDownloadState {
  // 下载进度状态
  downloadProgresses: Map<string, DownloadProgress>

  // 已下载的剧本缓存
  downloadedScripts: Map<string, ScriptContent>

  // 动作方法
  downloadScript: (scriptId: string, scriptTitle?: string) => Promise<ScriptContent>
  cancelDownload: (scriptId: string) => Promise<void>
  getDownloadProgress: (scriptId: string) => DownloadProgress | null
  isScriptDownloaded: (scriptId: string) => Promise<boolean>
  getDownloadedScript: (scriptId: string) => Promise<ScriptContent | null>
  deleteScript: (scriptId: string) => Promise<void>

  // 内部方法
  setDownloadProgress: (progress: DownloadProgress) => void
  removeDownloadProgress: (scriptId: string) => void
  clearDownloadedScript: (scriptId: string) => void
}

export const useScriptDownloadStore = create<ScriptDownloadState>((set, get) => {
  // 监听下载进度
  const unsubscribe = scriptDownloadService.onProgress(progress => {
    get().setDownloadProgress(progress)
  })

  return {
    downloadProgresses: new Map(),
    downloadedScripts: new Map(),

    // 下载剧本
    downloadScript: async (scriptId: string, scriptTitle?: string) => {
      try {
        console.log(`🚀 开始下载剧本: ${scriptId}`)

        // 检查是否已经在下载中
        const currentProgress = get().downloadProgresses.get(scriptId)
        if (
          currentProgress &&
          (currentProgress.status === 'downloading' || currentProgress.status === 'pending')
        ) {
          throw new Error('剧本正在下载中，请稍候')
        }

        // 执行下载
        const scriptContent = await scriptDownloadService.downloadScript(scriptId, scriptTitle)

        // 缓存到store中
        set(state => ({
          downloadedScripts: new Map(state.downloadedScripts).set(scriptId, scriptContent)
        }))

        // 清理下载进度
        get().removeDownloadProgress(scriptId)

        console.log(`✅ 剧本下载完成: ${scriptId}`)
        return scriptContent
      } catch (error) {
        console.error(`❌ 剧本下载失败: ${scriptId}`, error)
        throw error
      }
    },

    // 取消下载
    cancelDownload: async (scriptId: string) => {
      try {
        await scriptDownloadService.cancelDownload(scriptId)
        get().removeDownloadProgress(scriptId)
        console.log(`🚫 取消下载: ${scriptId}`)
      } catch (error) {
        console.error(`取消下载失败: ${scriptId}`, error)
        throw error
      }
    },

    // 获取下载进度
    getDownloadProgress: (scriptId: string) => {
      return get().downloadProgresses.get(scriptId) || null
    },

    // 检查是否已下载
    isScriptDownloaded: async (scriptId: string) => {
      // 先检查缓存
      if (get().downloadedScripts.has(scriptId)) {
        return true
      }

      // 检查数据库
      return await scriptDownloadService.isScriptDownloaded(scriptId)
    },

    // 获取已下载的剧本
    getDownloadedScript: async (scriptId: string) => {
      // 先检查缓存
      const cached = get().downloadedScripts.get(scriptId)
      if (cached) {
        return cached
      }

      // 从数据库获取
      const scriptContent = await scriptDownloadService.getDownloadedScript(scriptId)
      if (scriptContent) {
        // 更新缓存
        set(state => ({
          downloadedScripts: new Map(state.downloadedScripts).set(scriptId, scriptContent)
        }))
      }

      return scriptContent
    },

    // 删除剧本
    deleteScript: async (scriptId: string) => {
      try {
        await scriptDownloadService.deleteDownloadedScript(scriptId)

        // 从缓存中移除
        get().clearDownloadedScript(scriptId)
        get().removeDownloadProgress(scriptId)

        console.log(`🗑️ 删除剧本: ${scriptId}`)
      } catch (error) {
        console.error(`删除剧本失败: ${scriptId}`, error)
        throw error
      }
    },

    // 设置下载进度
    setDownloadProgress: (progress: DownloadProgress) => {
      set(state => ({
        downloadProgresses: new Map(state.downloadProgresses).set(progress.scriptId, progress)
      }))
    },

    // 移除下载进度
    removeDownloadProgress: (scriptId: string) => {
      set(state => {
        const newProgresses = new Map(state.downloadProgresses)
        newProgresses.delete(scriptId)
        return { downloadProgresses: newProgresses }
      })
    },

    // 清理已下载剧本缓存
    clearDownloadedScript: (scriptId: string) => {
      set(state => {
        const newDownloaded = new Map(state.downloadedScripts)
        newDownloaded.delete(scriptId)
        return { downloadedScripts: newDownloaded }
      })
    }
  }
})

// 导出下载进度类型
export type { DownloadProgress }

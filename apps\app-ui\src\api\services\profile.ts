import { apiClient } from '../client'

// 用户资料类型
export interface UserProfile {
  id: string
  userId: string
  nickname: string | null
  gender: string | null
  avatarUrl: string | null
  createdAt: string
  updatedAt: string
}

// 用户资料 API 服务
export const profileService = {
  // 获取用户资料
  async get() {
    try {
      console.log('API调用: 获取用户资料')
      const response = await apiClient.get<{ success: boolean; userProfile: UserProfile }>(
        '/api/users/profile'
      )
      console.log('API响应: 获取用户资料', response)
      return response
    } catch (error) {
      console.error('API错误: 获取用户资料失败', error)
      return null
    }
  },

  // 更新用户资料
  async update(data: Partial<UserProfile>) {
    try {
      console.log('API调用: 更新用户资料', data)
      // 使用PUT方法更新用户资料
      const response = await apiClient.put<{ success: boolean; userProfile: UserProfile }>(
        '/api/users/profile',
        data
      )
      console.log('API响应: 更新用户资料', response)
      return response
    } catch (error) {
      console.error('API错误: 更新用户资料失败', error)
      return null
    }
  }
}

import { ChatDatabaseError } from './types'

/**
 * 数据库工具类
 * 包含清理、统计、维护等实用功能
 */
export class DatabaseUtils {
  private db: any

  constructor(database: any) {
    this.db = database
  }

  /**
   * 清理旧数据
   */
  async cleanupOldData(
    daysToKeep: number
  ): Promise<{ deletedSessions: number; deletedMessages: number; deletedAttachments: number }> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
    const cutoffISOString = cutoffDate.toISOString()

    let transactionStarted = false

    try {
      console.log(`🧹 [DB Utils] 开始清理 ${daysToKeep} 天前的数据，截止时间: ${cutoffISOString}`)

      // 确保没有活动事务
      try {
        await this.db.execute('COMMIT;')
      } catch {
        // 忽略没有活动事务的错误
      }

      await this.db.execute('BEGIN TRANSACTION;')
      transactionStarted = true

      // 删除旧附件
      const deletedAttachments = await this.db.run(
        'DELETE FROM message_attachments WHERE createdAt < ?',
        [cutoffISOString]
      )

      // 删除旧消息
      const deletedMessages = await this.db.run('DELETE FROM chat_messages WHERE createdAt < ?', [
        cutoffISOString
      ])

      // 删除没有消息的会话
      const deletedSessions = await this.db.run(
        `DELETE FROM chat_sessions WHERE id NOT IN (
          SELECT DISTINCT chatId FROM chat_messages
        )`
      )

      await this.db.execute('COMMIT;')
      transactionStarted = false

      const result = {
        deletedSessions: deletedSessions.changes?.changes || 0,
        deletedMessages: deletedMessages.changes?.changes || 0,
        deletedAttachments: deletedAttachments.changes?.changes || 0
      }

      console.log('✅ [DB Utils] 数据清理完成:', result)
      return result
    } catch (error) {
      // 只在事务活跃时才回滚
      if (transactionStarted) {
        try {
          await this.db.execute('ROLLBACK;')
        } catch (rollbackError) {
          console.warn('⚠️ [DB Utils] 清理事务回滚失败:', rollbackError)
        }
      }

      throw new ChatDatabaseError('数据清理失败', 'CLEANUP_FAILED', error as Error)
    }
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    sessionCount: number
    messageCount: number
    attachmentCount: number
    totalSize: number
    databaseSize: string
  }> {
    try {
      const [sessionResult, messageResult, attachmentResult] = await Promise.all([
        this.db.query('SELECT COUNT(*) as count FROM chat_sessions'),
        this.db.query('SELECT COUNT(*) as count FROM chat_messages'),
        this.db.query(
          'SELECT COUNT(*) as count, SUM(COALESCE(fileSize, 0)) as totalSize FROM message_attachments'
        )
      ])

      // 获取数据库大小 (SQLite)
      let databaseSize = '未知'
      try {
        const sizeResult = await this.db.query('PRAGMA page_count; PRAGMA page_size;')
        if (sizeResult.values && sizeResult.values.length >= 2) {
          const pageCount = sizeResult.values[0]?.page_count || 0
          const pageSize = sizeResult.values[1]?.page_size || 0
          const totalBytes = pageCount * pageSize
          databaseSize = this.formatBytes(totalBytes)
        }
      } catch (error) {
        console.warn('⚠️ [DB Utils] 获取数据库大小失败:', error)
      }

      return {
        sessionCount: sessionResult.values?.[0]?.count || 0,
        messageCount: messageResult.values?.[0]?.count || 0,
        attachmentCount: attachmentResult.values?.[0]?.count || 0,
        totalSize: attachmentResult.values?.[0]?.totalSize || 0,
        databaseSize
      }
    } catch (error) {
      throw new ChatDatabaseError('获取存储统计失败', 'GET_STORAGE_STATS_FAILED', error as Error)
    }
  }

  /**
   * 格式化字节数为可读字符串
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B'

    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`
  }

  /**
   * 检查数据库完整性
   */
  async checkIntegrity(): Promise<{ isHealthy: boolean; issues: string[] }> {
    const issues: string[] = []

    try {
      console.log('🔍 [DB Utils] 开始数据库完整性检查')

      // SQLite 完整性检查
      try {
        const integrityResult = await this.db.query('PRAGMA integrity_check;')
        const integrityStatus = integrityResult.values?.[0]?.integrity_check
        if (integrityStatus !== 'ok') {
          issues.push(`数据库完整性检查失败: ${integrityStatus}`)
        }
      } catch (error) {
        issues.push(`无法执行完整性检查: ${error}`)
      }

      // 检查外键约束
      try {
        const foreignKeyResult = await this.db.query('PRAGMA foreign_key_check;')
        if (foreignKeyResult.values && foreignKeyResult.values.length > 0) {
          issues.push(`发现外键约束违规: ${foreignKeyResult.values.length} 个`)
        }
      } catch (error) {
        issues.push(`无法检查外键约束: ${error}`)
      }

      // 检查孤儿消息
      try {
        const orphanMessagesResult = await this.db.query(
          `SELECT COUNT(*) as count FROM chat_messages 
           WHERE chatId NOT IN (SELECT id FROM chat_sessions)`
        )
        const orphanMessages = orphanMessagesResult.values?.[0]?.count || 0
        if (orphanMessages > 0) {
          issues.push(`发现孤儿消息: ${orphanMessages} 条`)
        }
      } catch (error) {
        issues.push(`无法检查孤儿消息: ${error}`)
      }

      // 检查孤儿附件
      try {
        const orphanAttachmentsResult = await this.db.query(
          `SELECT COUNT(*) as count FROM message_attachments 
           WHERE messageId NOT IN (SELECT id FROM chat_messages)`
        )
        const orphanAttachments = orphanAttachmentsResult.values?.[0]?.count || 0
        if (orphanAttachments > 0) {
          issues.push(`发现孤儿附件: ${orphanAttachments} 个`)
        }
      } catch (error) {
        issues.push(`无法检查孤儿附件: ${error}`)
      }

      const isHealthy = issues.length === 0
      console.log(
        isHealthy
          ? '✅ [DB Utils] 数据库完整性检查通过'
          : `⚠️ [DB Utils] 数据库完整性检查发现问题: ${issues.length} 个`
      )

      return { isHealthy, issues }
    } catch (error) {
      throw new ChatDatabaseError('数据库完整性检查失败', 'INTEGRITY_CHECK_FAILED', error as Error)
    }
  }

  /**
   * 修复数据库问题
   */
  async repairDatabase(): Promise<{ repairedIssues: number; failedRepairs: string[] }> {
    const failedRepairs: string[] = []
    let repairedIssues = 0

    try {
      console.log('🔧 [DB Utils] 开始修复数据库问题')

      // 清理孤儿消息
      try {
        const orphanMessagesResult = await this.db.run(
          `DELETE FROM chat_messages 
           WHERE chatId NOT IN (SELECT id FROM chat_sessions)`
        )
        const deletedMessages = orphanMessagesResult.changes?.changes || 0
        if (deletedMessages > 0) {
          console.log(`🗑️ [DB Utils] 清理孤儿消息: ${deletedMessages} 条`)
          repairedIssues++
        }
      } catch (error) {
        failedRepairs.push(`清理孤儿消息失败: ${error}`)
      }

      // 清理孤儿附件
      try {
        const orphanAttachmentsResult = await this.db.run(
          `DELETE FROM message_attachments 
           WHERE messageId NOT IN (SELECT id FROM chat_messages)`
        )
        const deletedAttachments = orphanAttachmentsResult.changes?.changes || 0
        if (deletedAttachments > 0) {
          console.log(`🗑️ [DB Utils] 清理孤儿附件: ${deletedAttachments} 个`)
          repairedIssues++
        }
      } catch (error) {
        failedRepairs.push(`清理孤儿附件失败: ${error}`)
      }

      // 重建索引
      try {
        await this.db.execute('REINDEX;')
        console.log('🔄 [DB Utils] 重建索引完成')
        repairedIssues++
      } catch (error) {
        failedRepairs.push(`重建索引失败: ${error}`)
      }

      // 优化数据库
      try {
        await this.db.execute('VACUUM;')
        console.log('🗜️ [DB Utils] 数据库优化完成')
        repairedIssues++
      } catch (error) {
        failedRepairs.push(`数据库优化失败: ${error}`)
      }

      console.log(`✅ [DB Utils] 数据库修复完成: ${repairedIssues} 个问题已修复`)

      return { repairedIssues, failedRepairs }
    } catch (error) {
      throw new ChatDatabaseError('数据库修复失败', 'REPAIR_DATABASE_FAILED', error as Error)
    }
  }

  /**
   * 优化数据库性能
   */
  async optimizeDatabase(): Promise<{ sizeBefore: string; sizeAfter: string; timeTaken: number }> {
    const startTime = Date.now()

    try {
      console.log('⚡ [DB Utils] 开始优化数据库性能')

      // 获取优化前大小
      const statsBefore = await this.getStorageStats()
      const sizeBefore = statsBefore.databaseSize

      // 分析查询计划（收集统计信息）
      try {
        await this.db.execute('ANALYZE;')
        console.log('📊 [DB Utils] 查询统计信息收集完成')
      } catch (error) {
        console.warn('⚠️ [DB Utils] 收集统计信息失败:', error)
      }

      // 重建索引
      try {
        await this.db.execute('REINDEX;')
        console.log('🔄 [DB Utils] 索引重建完成')
      } catch (error) {
        console.warn('⚠️ [DB Utils] 索引重建失败:', error)
      }

      // 压缩数据库
      try {
        await this.db.execute('VACUUM;')
        console.log('🗜️ [DB Utils] 数据库压缩完成')
      } catch (error) {
        console.warn('⚠️ [DB Utils] 数据库压缩失败:', error)
      }

      // 获取优化后大小
      const statsAfter = await this.getStorageStats()
      const sizeAfter = statsAfter.databaseSize

      const timeTaken = Date.now() - startTime

      console.log(`✅ [DB Utils] 数据库优化完成，耗时: ${timeTaken}ms`)
      console.log(`📏 [DB Utils] 大小变化: ${sizeBefore} → ${sizeAfter}`)

      return { sizeBefore, sizeAfter, timeTaken }
    } catch (error) {
      throw new ChatDatabaseError('数据库优化失败', 'OPTIMIZE_DATABASE_FAILED', error as Error)
    }
  }

  /**
   * 导出数据库数据（JSON格式）
   */
  async exportData(): Promise<{
    sessions: any[]
    messages: any[]
    attachments: any[]
    exportTime: string
    version: string
  }> {
    try {
      console.log('📤 [DB Utils] 开始导出数据库数据')

      const [sessions, messages, attachments] = await Promise.all([
        this.db.query('SELECT * FROM chat_sessions ORDER BY createdAt ASC'),
        this.db.query('SELECT * FROM chat_messages ORDER BY createdAt ASC'),
        this.db.query('SELECT * FROM message_attachments ORDER BY createdAt ASC')
      ])

      const exportData = {
        sessions: sessions.values || [],
        messages: messages.values || [],
        attachments: attachments.values || [],
        exportTime: new Date().toISOString(),
        version: '1.0'
      }

      console.log(
        `✅ [DB Utils] 数据导出完成: ${exportData.sessions.length} 会话, ${exportData.messages.length} 消息, ${exportData.attachments.length} 附件`
      )

      return exportData
    } catch (error) {
      throw new ChatDatabaseError('数据导出失败', 'EXPORT_DATA_FAILED', error as Error)
    }
  }

  /**
   * 获取数据库模式信息
   */
  async getSchemaInfo(): Promise<{
    tables: Array<{ name: string; columns: number; rows: number }>
    indexes: string[]
    version: string
  }> {
    try {
      // 获取表信息
      const tablesResult = await this.db.query(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
      )
      const tableNames = tablesResult.values?.map((row: any) => row.name) || []

      const tables = []
      for (const tableName of tableNames) {
        try {
          // 获取列数
          const columnsResult = await this.db.query(`PRAGMA table_info(${tableName})`)
          const columnCount = columnsResult.values?.length || 0

          // 获取行数
          const rowsResult = await this.db.query(`SELECT COUNT(*) as count FROM ${tableName}`)
          const rowCount = rowsResult.values?.[0]?.count || 0

          tables.push({
            name: tableName,
            columns: columnCount,
            rows: rowCount
          })
        } catch (error) {
          console.warn(`⚠️ [DB Utils] 获取表信息失败: ${tableName}`, error)
        }
      }

      // 获取索引信息
      const indexesResult = await this.db.query(
        "SELECT name FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'"
      )
      const indexes = indexesResult.values?.map((row: any) => row.name) || []

      // 获取数据库版本
      const versionResult = await this.db.query('PRAGMA user_version;')
      const version = versionResult.values?.[0]?.user_version?.toString() || '0'

      return { tables, indexes, version }
    } catch (error) {
      throw new ChatDatabaseError('获取模式信息失败', 'GET_SCHEMA_INFO_FAILED', error as Error)
    }
  }
}

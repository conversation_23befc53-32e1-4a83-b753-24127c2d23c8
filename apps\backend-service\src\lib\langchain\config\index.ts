// LangChain 配置管理

import type { LangChainConfig, LangChainEnv } from '../types'

// 模型配置常量
export const MODEL_CONFIG = {
  defaultModel: 'x-ai/grok-3'
  // defaultModel: 'xai/fast'
  // defaultModel: 'grok-3'
  // defaultModel: 'grok-4-0709'
} as const

// 默认配置
export const DEFAULT_CONFIG: LangChainConfig = {
  // 模型配置
  defaultModel: MODEL_CONFIG.defaultModel,

  // 记忆配置（预留）
  memory: {
    bufferSize: 10,
    vectorDimension: 1536,
    similarityThreshold: 0.7
  },

  // 多模态配置
  multimodal: {
    enableImage: true,
    enableAudio: true,
    enableDevice: false // 暂时禁用设备控制
  },

  // 流式配置
  streaming: {
    chunkSize: 1024,
    delayMs: 0
  }
}

// 配置管理器
export class ConfigManager {
  private config: LangChainConfig

  constructor(customConfig?: Partial<LangChainConfig>) {
    this.config = {
      ...DEFAULT_CONFIG,
      ...customConfig
    }
  }

  /**
   * 获取完整配置
   */
  getConfig(): LangChainConfig {
    return { ...this.config }
  }

  /**
   * 获取多模态配置
   */
  getMultiModalConfig() {
    return { ...this.config.multimodal }
  }

  /**
   * 获取流式配置
   */
  getStreamingConfig() {
    return { ...this.config.streaming }
  }

  /**
   * 获取记忆配置（预留）
   */
  getMemoryConfig() {
    return this.config.memory ? { ...this.config.memory } : undefined
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<LangChainConfig>) {
    this.config = {
      ...this.config,
      ...updates
    }
  }

  /**
   * 验证环境变量
   */
  validateEnv(env: any): env is LangChainEnv {
    if (!env.XAI_API_KEY) {
      throw new Error('XAI_API_KEY is required but not found in environment variables')
    }
    return true
  }

  /**
   * 获取默认模型
   */
  getDefaultModel(): string {
    return this.config.defaultModel
  }
}

export function getDefaultModel(): string {
  return MODEL_CONFIG.defaultModel
}

// 创建默认配置管理器实例
export const defaultConfigManager = new ConfigManager()

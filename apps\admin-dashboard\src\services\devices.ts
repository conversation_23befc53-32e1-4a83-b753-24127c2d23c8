import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

// 设备类型定义
export interface Device {
  id: string
  deviceCode: string
  name: string
  pic?: string
  brand?: string
  model?: string
  category?: string
  description?: string
  userId?: string
  isActive: boolean
  lastConnectedAt?: string
  createdAt: string
  updatedAt: string
  user?: {
    id: string
    email: string
  }
  functions?: DeviceFunctionBinding[]
}

// 设备功能绑定类型定义
export interface DeviceFunctionBinding {
  id: string
  deviceId: string
  functionId: string
  isActive: boolean
  createdAt: string
  function: {
    id: string
    key: string
    name: string
    is_active: boolean
    description?: string
    max_intensity: number
  }
}

// 原始功能定义（用于功能列表）
export interface DeviceFunction {
  id: string
  key: string
  name: string
  description?: string
  maxIntensity: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 设备指令类型定义
export interface DeviceCommand {
  id: string
  functionId: string
  intensity: number
  command: string
  description?: string
  createdAt: string
  updatedAt: string
}

// 设备使用记录类型定义
export interface DeviceUsage {
  id: string
  userId: string
  deviceId: string
  scriptId?: string
  sessionId?: string
  functionKey: string
  intensity: number
  duration?: number
  startedAt: string
  endedAt?: string
  metadata?: any
  user?: {
    id: string
    email: string
  }
  device?: {
    id: string
    name: string
    deviceCode: string
    brand?: string
    model?: string
  }
  script?: {
    id: string
    title: string
  }
}

// 设备统计类型定义
export interface DeviceStats {
  totalDevices: number
  activeDevices: number
  onlineDevices: number
  todayUsage: number
  monthlyUsage: number
}

// 查询参数类型
export interface DeviceListParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  isActive?: boolean
  userId?: string
}

export interface DeviceFunctionParams {
  name: string
  key: string
  description?: string
  maxIntensity?: number
  isActive?: boolean
}

export interface UsageListParams {
  page?: number
  pageSize?: number
  deviceId?: string
  userId?: string
  startDate?: string
  endDate?: string
}

// 设备管理服务
export class DeviceService {
  // ==================== 设备管理 ====================
  
  // 获取设备列表
  async getDevices(params?: DeviceListParams): Promise<ApiResponse<PaginatedResponse<Device>>> {
    return await apiService.get<PaginatedResponse<Device>>('/admin/devices', { params })
  }

  // 创建设备
  async createDevice(data: {
    deviceCode: string
    name: string
    pic?: string
    brand?: string
    model?: string
    category?: string
    description?: string
    userId?: string
    isActive?: boolean
  }): Promise<ApiResponse<Device>> {
    return await apiService.post<Device>('/admin/devices', data)
  }

  // 更新设备
  async updateDevice(id: string, data: {
    name?: string
    pic?: string
    brand?: string
    model?: string
    category?: string
    description?: string
    userId?: string
    isActive?: boolean
  }): Promise<ApiResponse<Device>> {
    return await apiService.put<Device>(`/admin/devices/${id}`, data)
  }

  // 删除设备
  async deleteDevice(id: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/devices/${id}`)
  }

  // ==================== 设备功能管理 ====================

  // 获取设备功能绑定列表
  async getDeviceFunctions(deviceId: string): Promise<ApiResponse<DeviceFunctionBinding[]>> {
    return await apiService.get<DeviceFunctionBinding[]>(`/admin/devices/${deviceId}/functions`)
  }

  // 绑定设备功能
  async bindDeviceFunction(deviceId: string, functionId: string): Promise<ApiResponse<DeviceFunctionBinding>> {
    return await apiService.post<DeviceFunctionBinding>(`/admin/devices/${deviceId}/functions`, { functionId })
  }

  // 删除设备功能绑定
  async unbindDeviceFunction(deviceId: string, bindingId: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/devices/${deviceId}/functions/${bindingId}`)
  }

  // 获取所有可用功能列表（使用功能管理的API）
  async getAvailableFunctions(): Promise<ApiResponse<DeviceFunction[]>> {
    // 需要从device-functions服务获取
    return { success: false, message: '请使用functionService.getFunctions()获取功能列表' } as ApiResponse<DeviceFunction[]>
  }

  // ==================== 设备使用记录 ====================

  // 获取设备使用记录
  async getDeviceUsage(params?: UsageListParams): Promise<ApiResponse<PaginatedResponse<DeviceUsage>>> {
    return await apiService.get<PaginatedResponse<DeviceUsage>>('/admin/devices/usage', { params })
  }

  // 获取设备统计
  async getDeviceStats(): Promise<ApiResponse<DeviceStats>> {
    return await apiService.get<DeviceStats>('/admin/devices/usage/stats')
  }
}

export const deviceService = new DeviceService()
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem'
import { Capacitor } from '@capacitor/core'

// 媒体文件信息
interface MediaFile {
  id: string
  messageId: string
  type: 'audio' | 'image' | 'video'
  remoteUrl: string
  localPath?: string
  fileName: string
  status: 'pending' | 'downloading' | 'completed' | 'failed'
  downloadedAt?: Date
  fileSize?: number
  mimeType?: string
  metadata?: any
}

// 下载进度信息
interface DownloadProgress {
  mediaId: string
  loaded: number
  total: number
  percentage: number
  status: 'downloading' | 'completed' | 'failed'
  error?: string
}

// 下载管理器配置
interface MediaDownloadConfig {
  enabled?: boolean
  maxConcurrentDownloads?: number
  retryAttempts?: number
  retryDelay?: number
  timeout?: number
  downloadDirectory?: string
}

// 事件监听器
type DownloadEventListener = (progress: DownloadProgress) => void
type MediaEventListener = (mediaFile: MediaFile) => void

/**
 * 媒体下载管理器
 * 负责管理媒体文件的下载、存储和状态跟踪
 */
export class MediaDownloadManager {
  private config: Required<MediaDownloadConfig>
  private downloadQueue: MediaFile[] = []
  private activeDownloads = new Map<string, AbortController>()
  private mediaFiles = new Map<string, MediaFile>()
  private downloadListeners: DownloadEventListener[] = []
  private mediaListeners: MediaEventListener[] = []
  private isProcessing = false

  constructor(config: MediaDownloadConfig = {}) {
    this.config = {
      enabled: config.enabled ?? true,
      maxConcurrentDownloads: config.maxConcurrentDownloads ?? 2,
      retryAttempts: config.retryAttempts ?? 3,
      retryDelay: config.retryDelay ?? 2000,
      timeout: config.timeout ?? 60000,
      downloadDirectory: config.downloadDirectory ?? 'media_cache'
    }

    console.log('📦 [媒体下载] 管理器已初始化:', this.config)
  }

  /**
   * 添加媒体文件到下载队列
   */
  async addToDownloadQueue(
    messageId: string,
    mediaType: 'audio' | 'image' | 'video',
    remoteUrl: string,
    metadata?: any
  ): Promise<string> {
    const mediaId = `${messageId}_${mediaType}_${Date.now()}`
    const fileName = this.generateFileName(mediaId, mediaType, remoteUrl)

    const mediaFile: MediaFile = {
      id: mediaId,
      messageId,
      type: mediaType,
      remoteUrl,
      fileName,
      status: 'pending',
      metadata
    }

    this.mediaFiles.set(mediaId, mediaFile)
    this.downloadQueue.push(mediaFile)

    console.log(`📦 [媒体下载] 添加到队列: ${mediaId}`)

    // 触发媒体文件添加事件
    this.notifyMediaListeners(mediaFile)

    // 开始处理队列
    this.processDownloadQueue()

    return mediaId
  }

  /**
   * 获取媒体文件本地路径
   */
  async getLocalPath(mediaId: string): Promise<string | null> {
    const mediaFile = this.mediaFiles.get(mediaId)
    if (!mediaFile || !mediaFile.localPath) {
      return null
    }

    try {
      // 检查文件是否存在
      const fileExists = await this.checkFileExists(mediaFile.localPath)
      return fileExists ? mediaFile.localPath : null
    } catch (error) {
      console.error(`📦 [媒体下载] 检查文件失败: ${mediaId}`, error)
      return null
    }
  }

  /**
   * 获取媒体文件信息
   */
  getMediaFile(mediaId: string): MediaFile | undefined {
    return this.mediaFiles.get(mediaId)
  }

  /**
   * 获取消息的所有媒体文件
   */
  getMediaFilesByMessage(messageId: string): MediaFile[] {
    return Array.from(this.mediaFiles.values()).filter(file => file.messageId === messageId)
  }

  /**
   * 删除媒体文件
   */
  async deleteMediaFile(mediaId: string): Promise<boolean> {
    const mediaFile = this.mediaFiles.get(mediaId)
    if (!mediaFile) {
      return false
    }

    try {
      // 取消下载（如果正在下载）
      const controller = this.activeDownloads.get(mediaId)
      if (controller) {
        controller.abort()
        this.activeDownloads.delete(mediaId)
      }

      // 删除本地文件
      if (mediaFile.localPath) {
        await this.deleteLocalFile(mediaFile.localPath)
      }

      // 从内存中移除
      this.mediaFiles.delete(mediaId)
      this.downloadQueue = this.downloadQueue.filter(file => file.id !== mediaId)

      console.log(`📦 [媒体下载] 已删除媒体文件: ${mediaId}`)
      return true
    } catch (error) {
      console.error(`📦 [媒体下载] 删除媒体文件失败: ${mediaId}`, error)
      return false
    }
  }

  /**
   * 清理过期媒体文件
   */
  async cleanupExpiredFiles(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<number> {
    const now = Date.now()
    let deletedCount = 0

    for (const [mediaId, mediaFile] of this.mediaFiles) {
      if (mediaFile.downloadedAt) {
        const age = now - mediaFile.downloadedAt.getTime()
        if (age > maxAge) {
          const deleted = await this.deleteMediaFile(mediaId)
          if (deleted) {
            deletedCount++
          }
        }
      }
    }

    console.log(`📦 [媒体下载] 清理过期文件: ${deletedCount} 个`)
    return deletedCount
  }

  /**
   * 获取存储统计信息
   */
  async getStorageStats(): Promise<{
    totalFiles: number
    totalSize: number
    typeBreakdown: Record<string, { count: number; size: number }>
  }> {
    let totalSize = 0
    const typeBreakdown: Record<string, { count: number; size: number }> = {
      audio: { count: 0, size: 0 },
      image: { count: 0, size: 0 },
      video: { count: 0, size: 0 }
    }

    for (const mediaFile of this.mediaFiles.values()) {
      if (mediaFile.status === 'completed' && mediaFile.fileSize) {
        totalSize += mediaFile.fileSize
        typeBreakdown[mediaFile.type].count++
        typeBreakdown[mediaFile.type].size += mediaFile.fileSize
      }
    }

    return {
      totalFiles: this.mediaFiles.size,
      totalSize,
      typeBreakdown
    }
  }

  /**
   * 添加下载进度监听器
   */
  addDownloadListener(listener: DownloadEventListener): () => void {
    this.downloadListeners.push(listener)
    return () => {
      const index = this.downloadListeners.indexOf(listener)
      if (index > -1) {
        this.downloadListeners.splice(index, 1)
      }
    }
  }

  /**
   * 添加媒体文件监听器
   */
  addMediaListener(listener: MediaEventListener): () => void {
    this.mediaListeners.push(listener)
    return () => {
      const index = this.mediaListeners.indexOf(listener)
      if (index > -1) {
        this.mediaListeners.splice(index, 1)
      }
    }
  }

  /**
   * 处理下载队列
   */
  private async processDownloadQueue(): Promise<void> {
    if (this.isProcessing || !this.config.enabled) {
      return
    }

    this.isProcessing = true

    try {
      while (
        this.downloadQueue.length > 0 &&
        this.activeDownloads.size < this.config.maxConcurrentDownloads
      ) {
        const mediaFile = this.downloadQueue.shift()
        if (mediaFile && mediaFile.status === 'pending') {
          this.downloadMediaFile(mediaFile)
        }
      }
    } finally {
      this.isProcessing = false
    }
  }

  /**
   * 下载单个媒体文件
   */
  private async downloadMediaFile(mediaFile: MediaFile): Promise<void> {
    const { id: mediaId, remoteUrl, fileName, type } = mediaFile

    try {
      console.log(`📦 [媒体下载] 开始下载: ${mediaId}`)

      // 更新状态为下载中
      mediaFile.status = 'downloading'
      this.notifyMediaListeners(mediaFile)

      // 创建中止控制器
      const controller = new AbortController()
      this.activeDownloads.set(mediaId, controller)

      // 执行下载
      const result = await this.performDownload(
        remoteUrl,
        fileName,
        type,
        controller.signal,
        mediaId
      )

      // 更新媒体文件信息
      mediaFile.localPath = result.localPath
      mediaFile.fileSize = result.fileSize
      mediaFile.mimeType = result.mimeType
      mediaFile.status = 'completed'
      mediaFile.downloadedAt = new Date()

      console.log(`📦 [媒体下载] 下载完成: ${mediaId} -> ${result.localPath}`)

      // 通知下载完成
      this.notifyDownloadListeners({
        mediaId,
        loaded: result.fileSize || 0,
        total: result.fileSize || 0,
        percentage: 100,
        status: 'completed'
      })

      this.notifyMediaListeners(mediaFile)
    } catch (error) {
      console.error(`📦 [媒体下载] 下载失败: ${mediaId}`, error)

      mediaFile.status = 'failed'
      this.notifyDownloadListeners({
        mediaId,
        loaded: 0,
        total: 0,
        percentage: 0,
        status: 'failed',
        error: error instanceof Error ? error.message : '下载失败'
      })

      this.notifyMediaListeners(mediaFile)

      // 重试逻辑
      if (mediaFile.metadata?.retryCount < this.config.retryAttempts) {
        mediaFile.metadata = {
          ...mediaFile.metadata,
          retryCount: (mediaFile.metadata?.retryCount || 0) + 1
        }
        mediaFile.status = 'pending'

        setTimeout(() => {
          this.downloadQueue.push(mediaFile)
          this.processDownloadQueue()
        }, this.config.retryDelay)
      }
    } finally {
      this.activeDownloads.delete(mediaId)

      // 继续处理队列
      this.processDownloadQueue()
    }
  }

  /**
   * 执行实际的文件下载
   */
  private async performDownload(
    url: string,
    fileName: string,
    type: string,
    signal: AbortSignal,
    mediaId: string
  ): Promise<{
    localPath: string
    fileSize: number
    mimeType: string
  }> {
    // 根据平台选择下载方式
    if (Capacitor.isNativePlatform()) {
      return this.downloadForNative(url, fileName, type, signal, mediaId)
    } else {
      return this.downloadForWeb(url, fileName, type, signal, mediaId)
    }
  }

  /**
   * 原生平台下载（iOS/Android）
   */
  private async downloadForNative(
    url: string,
    fileName: string,
    type: string,
    signal: AbortSignal,
    mediaId: string
  ): Promise<{
    localPath: string
    fileSize: number
    mimeType: string
  }> {
    // 使用fetch下载到内存，然后保存到文件系统
    const response = await fetch(url, { signal })

    if (!response.ok) {
      throw new Error(`下载失败: ${response.status} ${response.statusText}`)
    }

    const contentType = response.headers.get('content-type') || `${type}/*`
    const contentLength = parseInt(response.headers.get('content-length') || '0', 10)

    // 读取响应数据
    const arrayBuffer = await response.arrayBuffer()
    const base64Data = this.arrayBufferToBase64(arrayBuffer)

    // 确保目录存在
    await this.ensureDirectoryExists(this.config.downloadDirectory)

    // 保存文件
    const filePath = `${this.config.downloadDirectory}/${fileName}`
    await Filesystem.writeFile({
      path: filePath,
      data: base64Data,
      directory: Directory.Data
    })

    console.log(`📦 [媒体下载] 原生平台保存完成: ${filePath}`)

    return {
      localPath: filePath,
      fileSize: arrayBuffer.byteLength,
      mimeType: contentType
    }
  }

  /**
   * Web平台下载（浏览器）
   */
  private async downloadForWeb(
    url: string,
    fileName: string,
    type: string,
    signal: AbortSignal,
    mediaId: string
  ): Promise<{
    localPath: string
    fileSize: number
    mimeType: string
  }> {
    // Web平台使用URL直接引用（不下载到本地）
    // 或者可以使用IndexedDB存储

    // 这里简化处理，直接返回远程URL
    // 实际项目中可以考虑使用IndexedDB等Web存储API

    const response = await fetch(url, { signal, method: 'HEAD' })
    const contentType = response.headers.get('content-type') || `${type}/*`
    const contentLength = parseInt(response.headers.get('content-length') || '0', 10)

    console.log(`📦 [媒体下载] Web平台使用远程URL: ${url}`)

    return {
      localPath: url, // Web平台直接使用远程URL
      fileSize: contentLength,
      mimeType: contentType
    }
  }

  /**
   * 生成文件名
   */
  private generateFileName(mediaId: string, type: string, url: string): string {
    const timestamp = new Date().toISOString().split('T')[0]
    const extension = this.getFileExtension(type, url)
    const cleanId = mediaId.replace(/[^a-zA-Z0-9_-]/g, '_')

    return `${timestamp}_${cleanId}.${extension}`
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(type: string, url: string): string {
    // 首先尝试从URL中提取扩展名
    const urlExtension = url.split('.').pop()?.split('?')[0]
    if (
      urlExtension &&
      ['mp3', 'wav', 'ogg', 'png', 'jpg', 'jpeg', 'gif', 'webp', 'mp4', 'webm', 'avi'].includes(
        urlExtension.toLowerCase()
      )
    ) {
      return urlExtension.toLowerCase()
    }

    // 根据类型返回默认扩展名
    switch (type) {
      case 'audio':
        return 'mp3'
      case 'image':
        return 'png'
      case 'video':
        return 'mp4'
      default:
        return 'bin'
    }
  }

  /**
   * 检查文件是否存在
   */
  private async checkFileExists(filePath: string): Promise<boolean> {
    if (!Capacitor.isNativePlatform()) {
      // Web平台假设文件总是存在（使用远程URL）
      return true
    }

    try {
      await Filesystem.stat({
        path: filePath,
        directory: Directory.Data
      })
      return true
    } catch {
      return false
    }
  }

  /**
   * 删除本地文件
   */
  private async deleteLocalFile(filePath: string): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      // Web平台不需要删除本地文件
      return
    }

    try {
      await Filesystem.deleteFile({
        path: filePath,
        directory: Directory.Data
      })
    } catch (error) {
      console.warn(`📦 [媒体下载] 删除文件失败: ${filePath}`, error)
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    if (!Capacitor.isNativePlatform()) {
      return
    }

    try {
      await Filesystem.mkdir({
        path: dirPath,
        directory: Directory.Data,
        recursive: true
      })
    } catch (error) {
      // 目录可能已存在，忽略错误
    }
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * 通知下载进度监听器
   */
  private notifyDownloadListeners(progress: DownloadProgress): void {
    this.downloadListeners.forEach(listener => {
      try {
        listener(progress)
      } catch (error) {
        console.error('📦 [媒体下载] 监听器错误:', error)
      }
    })
  }

  /**
   * 通知媒体文件监听器
   */
  private notifyMediaListeners(mediaFile: MediaFile): void {
    this.mediaListeners.forEach(listener => {
      try {
        listener(mediaFile)
      } catch (error) {
        console.error('📦 [媒体下载] 监听器错误:', error)
      }
    })
  }
}

// 导出类型
export type { MediaFile, DownloadProgress, MediaDownloadConfig }

// 全局单例实例
let globalMediaDownloadManager: MediaDownloadManager | null = null

/**
 * 获取全局媒体下载管理器实例
 */
export function getMediaDownloadManager(config?: MediaDownloadConfig): MediaDownloadManager {
  if (!globalMediaDownloadManager) {
    globalMediaDownloadManager = new MediaDownloadManager(config)
  }
  return globalMediaDownloadManager
}

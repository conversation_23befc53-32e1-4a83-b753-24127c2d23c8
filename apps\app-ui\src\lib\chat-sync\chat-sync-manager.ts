// 简单的防抖实现
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
  options: { leading?: boolean; trailing?: boolean } = {}
): T & { cancel(): void } {
  let timeoutId: NodeJS.Timeout | undefined
  let lastCallTime: number | undefined
  let lastInvokeTime = 0
  let lastArgs: Parameters<T> | undefined
  let lastThis: any
  let result: ReturnType<T>

  const { leading = false, trailing = true } = options

  function invokeFunc(time: number) {
    const args = lastArgs!
    const thisArg = lastThis
    lastArgs = undefined
    lastThis = undefined
    lastInvokeTime = time
    result = func.apply(thisArg, args)
    return result
  }

  function leadingEdge(time: number) {
    lastInvokeTime = time
    timeoutId = setTimeout(timerExpired, wait)
    return leading ? invokeFunc(time) : result
  }

  function remainingWait(time: number) {
    const timeSinceLastCall = time - (lastCallTime || 0)
    const timeSinceLastInvoke = time - lastInvokeTime
    const timeWaiting = wait - timeSinceLastCall
    return timeWaiting
  }

  function shouldInvoke(time: number) {
    const timeSinceLastCall = time - (lastCallTime || 0)
    const timeSinceLastInvoke = time - lastInvokeTime
    return (
      lastCallTime === undefined ||
      timeSinceLastCall >= wait ||
      timeSinceLastCall < 0 ||
      timeSinceLastInvoke >= wait
    )
  }

  function timerExpired() {
    const time = Date.now()
    if (shouldInvoke(time)) {
      return trailingEdge(time)
    }
    timeoutId = setTimeout(timerExpired, remainingWait(time))
  }

  function trailingEdge(time: number) {
    timeoutId = undefined
    if (trailing && lastArgs) {
      return invokeFunc(time)
    }
    lastArgs = undefined
    lastThis = undefined
    return result
  }

  function cancel() {
    if (timeoutId !== undefined) {
      clearTimeout(timeoutId)
    }
    lastInvokeTime = 0
    lastArgs = undefined
    lastCallTime = undefined
    lastThis = undefined
    timeoutId = undefined
  }

  const debounced = function (this: any, ...args: Parameters<T>) {
    const time = Date.now()
    const isInvoking = shouldInvoke(time)

    lastArgs = args
    lastThis = this
    lastCallTime = time

    if (isInvoking) {
      if (timeoutId === undefined) {
        return leadingEdge(lastCallTime)
      }
      if (trailing) {
        timeoutId = setTimeout(timerExpired, wait)
        return invokeFunc(lastCallTime)
      }
    }
    if (timeoutId === undefined) {
      timeoutId = setTimeout(timerExpired, wait)
    }
    return result
  } as T & { cancel(): void }

  debounced.cancel = cancel
  return debounced
}

import type { Message as LangChainMessage } from '@/api/services'
import type { SyncResult, SyncOptions } from '../chat-database/types'
import { chatDatabase } from '../chat-database/chat-database'
import { messageConverter, generateSessionTitle } from '../chat-database/message-converter'

/**
 * 聊天同步管理器
 * 负责将useLangChainChat的消息变化同步到本地数据库
 */
export class ChatSyncManager {
  private syncInProgress = false
  private lastSyncedMessageCount = 0
  private lastSyncedMessageId: string | null = null
  private messageHashes = new Map<string, string>()

  // 防抖函数
  private debouncedSync: ReturnType<typeof debounce>

  // 同步选项
  private options: Required<SyncOptions>

  constructor(options: SyncOptions = {}) {
    this.options = {
      enableDebounce: true,
      debounceMs: 5000, // 🔧 改为5秒
      batchSize: 20, // 🔧 修复：增加批量大小，减少批次数量
      ...options
    }

    // 创建防抖同步函数
    this.debouncedSync = debounce(this.performSync.bind(this), this.options.debounceMs, {
      leading: false,
      trailing: true
    })
  }

  /**
   * 🔧 简化的同步方法，带智能防抖
   */
  async syncMessages(
    chatId: string,
    messages: LangChainMessage[],
    roleId: string
  ): Promise<SyncResult> {
    try {
      // 🔧 简化的同步判断
      const needsSync = this.isSimpleStructuralChange(messages)

      if (!needsSync) {
        return {
          success: true,
          skipped: true,
          reason: '无需同步'
        }
      }

      // 🔧 检查最后一条消息是否是流式进行中的assistant消息
      const lastMessage = messages[messages.length - 1]
      if (lastMessage?.role === 'assistant') {
        const content = lastMessage.content.trim()

        // 🎯 简单的完成检测：标签闭合或标点符号
        const isComplete = content.endsWith('>')

        if (!isComplete && content.length > 0) {
          // 🔧 使用防抖机制，2秒后同步
          console.log('🔄 [ChatSync] 检测到流式消息，使用防抖同步')
          this.debouncedSync(chatId, messages, roleId)
          return { success: true }
        }
      }

      // 立即同步的情况：新消息、用户消息、或assistant消息已完成
      console.log('🔄 [ChatSync] 立即同步')
      return await this.performSync(chatId, messages, roleId)
    } catch (error) {
      console.error('❌ [ChatSync] 同步失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 🔧 简化的结构性变化检测
   */
  private isSimpleStructuralChange(messages: LangChainMessage[]): boolean {
    // 1. 消息数量变化
    if (messages.length !== this.lastSyncedMessageCount) {
      console.log('🔄 [ChatSync] 消息数量变化', {
        当前: messages.length,
        缓存: this.lastSyncedMessageCount
      })
      return true
    }

    // 2. 最后一条消息ID变化
    const lastMessageId = messages[messages.length - 1]?.id
    if (lastMessageId !== this.lastSyncedMessageId) {
      console.log('🔄 [ChatSync] 最后消息ID变化', {
        当前: lastMessageId,
        缓存: this.lastSyncedMessageId
      })
      return true
    }

    // 3. 🔧 检测内容变化（特别是流式消息）
    const lastMessage = messages[messages.length - 1]
    if (lastMessage) {
      const currentHash = this.hashMessage(lastMessage)
      const lastHash = this.messageHashes.get(lastMessage.id)

      if (lastHash && currentHash !== lastHash) {
        console.log('🔄 [ChatSync] 最后消息内容变化', {
          消息ID: lastMessage.id,
          角色: lastMessage.role,
          内容长度: lastMessage.content.length
        })
        return true
      }
    }

    return false
  }

  /**
   * 执行实际同步
   */
  private async performSync(
    chatId: string,
    messages: LangChainMessage[],
    roleId: string
  ): Promise<SyncResult> {
    if (this.syncInProgress) {
      console.log('⏳ [ChatSync] 同步正在进行中，跳过')
      return { success: true }
    }

    this.syncInProgress = true

    try {
      console.log(`🔄 [ChatSync] 开始同步聊天 ${chatId}，消息数量: ${messages.length}，批次大小: ${this.options.batchSize}`)

      // 检查数据库是否已初始化
      if (!this.isDatabaseInitialized()) {
        console.log('⚠️ [ChatSync] 数据库未初始化，跳过同步')
        this.updateLocalCache(messages)
        return {
          success: true,
          skipped: true,
          reason: '数据库未初始化（Web平台降级处理）'
        }
      }

      console.log('✅ [ChatSync] 数据库已初始化，可以同步')

      // 确保会话存在
      await this.ensureSessionExists(chatId, roleId, messages)

      // 处理消息变化
      const result = await this.processMessageChanges(chatId, messages)

      // 更新本地缓存
      this.updateLocalCache(messages)

      console.log(`✅ [ChatSync] 同步完成: 更新了 ${result.updatedMessages} 条消息`)

      return result
    } catch (error) {
      console.error('❌ [ChatSync] 同步失败:', error)

      // 🔧 即使同步失败也更新缓存，防止无限重试
      this.updateLocalCache(messages)

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      }
    } finally {
      this.syncInProgress = false
    }
  }

  /**
   * 确保聊天会话存在
   */
  private async ensureSessionExists(
    chatId: string,
    roleId: string,
    messages: LangChainMessage[]
  ): Promise<void> {
    try {
      const existingSession = await chatDatabase.getSession(chatId)

      if (!existingSession) {
        // 创建新会话
        const firstUserMessage = messages.find(m => m.role === 'user')
        const title = firstUserMessage ? generateSessionTitle(firstUserMessage.content) : '新对话'

        await chatDatabase.createSession({
          id: chatId,
          roleId,
          title,
          messageCount: 0,
          lastMessageAt: new Date().toISOString()
        })

        console.log(`📝 [ChatSync] 创建新会话: ${chatId}`)
      }
    } catch (error) {
      console.error('❌ [ChatSync] 确保会话存在失败:', error)
      throw error
    }
  }

  /**
   * 处理消息变化
   */
  private async processMessageChanges(
    chatId: string,
    messages: LangChainMessage[]
  ): Promise<SyncResult> {
    let updatedMessages = 0
    let updatedAttachments = 0

    try {
      // 批量处理消息，增加错误容错机制
      for (let i = 0; i < messages.length; i += this.options.batchSize) {
        const batch = messages.slice(i, i + this.options.batchSize)

        for (const message of batch) {
          try {
            const result = await this.processMessage(chatId, message)
            updatedMessages += result.messageUpdated ? 1 : 0
            updatedAttachments += result.attachmentsUpdated
          } catch (messageError) {
            // 🔧 修复：单个消息处理失败不影响其他消息
            console.error(`❌ [ChatSync] 处理消息失败 ${message.id}，继续处理其他消息:`, messageError)
            // 继续处理下一条消息，不中断整个批次
          }
        }
      }

      return {
        success: true,
        updatedMessages,
        updatedAttachments
      }
    } catch (error) {
      console.error('❌ [ChatSync] 处理消息变化失败:', error)
      throw error
    }
  }

  /**
   * 处理单个消息
   */
  private async processMessage(
    chatId: string,
    message: LangChainMessage
  ): Promise<{ messageUpdated: boolean; attachmentsUpdated: number }> {
    try {
      // 检查消息是否已存在
      const existingMessage = await chatDatabase.getMessage(message.id)

      // 计算消息哈希以检测内容变化
      const currentHash = this.hashMessage(message)
      const lastHash = this.messageHashes.get(message.id)

      let messageUpdated = false
      let attachmentsUpdated = 0

      if (!existingMessage) {
        // 🚀 新增：检查是否是助手消息的ID更新情况
        if (message.role === 'assistant') {
          // 查找可能存在的临时ID消息（空内容的助手消息）
          const allMessages = await chatDatabase.getMessagesByChat(chatId)
          const emptyAssistantMessages = allMessages.filter(
            msg =>
              msg.role === 'assistant' &&
              (!msg.content || msg.content.trim().length < 5) &&
              msg.id !== message.id // 不是当前消息ID
          )

          // 如果找到空的助手消息，删除它们（这些可能是临时ID的消息）
          for (const emptyMsg of emptyAssistantMessages) {
            console.log(`🗑️ [ChatSync] 删除临时ID的空助手消息: ${emptyMsg.id}`)

            // 删除附件
            const attachments = await chatDatabase.getAttachmentsByMessage(emptyMsg.id)
            for (const attachment of attachments) {
              await chatDatabase.deleteAttachment(attachment.id)
            }

            // 删除消息
            await chatDatabase.deleteMessage(emptyMsg.id)

            // 清理哈希缓存
            this.messageHashes.delete(emptyMsg.id)
          }
        }

        // 创建新消息
        await this.createNewMessage(chatId, message)
        messageUpdated = true
        attachmentsUpdated = message.attachments?.length || 0
      } else if (currentHash !== lastHash) {
        // 更新现有消息
        const result = await this.updateExistingMessage(existingMessage, message)
        messageUpdated = result.messageUpdated
        attachmentsUpdated = result.attachmentsUpdated
      }

      // 更新哈希缓存
      this.messageHashes.set(message.id, currentHash)

      return { messageUpdated, attachmentsUpdated }
    } catch (error) {
      console.error(`❌ [ChatSync] 处理消息失败 ${message.id}:`, error)
      throw error
    }
  }

  /**
   * 创建新消息
   */
  private async createNewMessage(chatId: string, message: LangChainMessage): Promise<void> {
    const { message: dbMessage, attachments } = messageConverter.fromLangChain(message, chatId)

    // 设置流式状态
    dbMessage.isStreaming = messageConverter.isStreamingMessage(message)
    dbMessage.streamingVersion = 1

    await chatDatabase.saveCompleteMessage(chatId, dbMessage, attachments)

    console.log(`✅ [ChatSync] 创建新消息: ${message.id}`)
  }

  /**
   * 更新现有消息
   */
  private async updateExistingMessage(
    existingMessage: any,
    newMessage: LangChainMessage
  ): Promise<{ messageUpdated: boolean; attachmentsUpdated: number }> {
    let messageUpdated = false
    let attachmentsUpdated = 0

    try {
      // 更新消息内容
      if (existingMessage.content !== newMessage.content) {
        const isStreaming = messageConverter.isStreamingMessage(newMessage)

        await chatDatabase.updateMessage(existingMessage.id, {
          content: newMessage.content,
          isStreaming,
          streamingVersion: existingMessage.streamingVersion + 1
        })

        messageUpdated = true
        console.log(`📝 [ChatSync] 更新消息内容: ${existingMessage.id}`)
      }

      // 处理附件更新
      if (newMessage.attachments) {
        const result = await this.updateMessageAttachments(
          existingMessage.id,
          newMessage.attachments
        )
        attachmentsUpdated = result.updated
      }

      return { messageUpdated, attachmentsUpdated }
    } catch (error) {
      console.error('❌ [ChatSync] 更新现有消息失败:', error)
      throw error
    }
  }

  /**
   * 更新消息附件
   */
  private async updateMessageAttachments(
    messageId: string,
    newAttachments: LangChainMessage['attachments']
  ): Promise<{ updated: number }> {
    if (!newAttachments || newAttachments.length === 0) {
      return { updated: 0 }
    }

    try {
      const existingAttachments = await chatDatabase.getAttachmentsByMessage(messageId)
      const { message: _, attachments: convertedAttachments } = messageConverter.fromLangChain(
        { attachments: newAttachments } as LangChainMessage,
        ''
      )

      let updated = 0

      for (const newAttachment of convertedAttachments) {
        const existing = existingAttachments.find(a => a.id === newAttachment.id)

        if (!existing) {
          // 创建新附件
          await chatDatabase.createAttachment({
            ...newAttachment,
            messageId
          })
          updated++
        } else if (existing.originalUrl !== newAttachment.originalUrl) {
          // 更新附件URL（媒体生成完成）
          await chatDatabase.updateAttachment(existing.id, {
            originalUrl: newAttachment.originalUrl,
            status: newAttachment.status
          })
          updated++
        }
      }

      if (updated > 0) {
        console.log(`📎 [ChatSync] 更新附件数量: ${updated}`)
      }

      return { updated }
    } catch (error) {
      console.error('❌ [ChatSync] 更新附件失败:', error)
      throw error
    }
  }

  /**
   * 生成消息的哈希值
   */
  private hashMessage(message: LangChainMessage): string {
    // 简单的哈希实现，基于消息的关键内容
    const content = JSON.stringify({
      id: message.id,
      content: message.content,
      attachments: message.attachments
    })

    // 使用简单的字符串哈希算法
    let hash = 0
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // 转为32位整数
    }

    return hash.toString()
  }

  /**
   * 检查数据库是否已初始化
   */
  private isDatabaseInitialized(): boolean {
    try {
      // 检查数据库实例是否存在
      if (!chatDatabase) {
        console.log('⏸️ [ChatSync] 数据库实例不存在')
        return false
      }

      // 使用ChatDatabase的公共API检查连接状态
      try {
        const connection = chatDatabase.getDatabaseConnection()
        const isInitialized = connection.isInitialized()

        if (!isInitialized) {
          console.log('⏸️ [ChatSync] 数据库连接未初始化')
          return false
        }

        console.log('✅ [ChatSync] 数据库已初始化，可以同步')
        return true
      } catch (connectionError) {
        console.log('⏸️ [ChatSync] 获取数据库连接失败:', connectionError)
        return false
      }
    } catch (error) {
      console.warn('⚠️ [ChatSync] 数据库状态检查失败:', error)
      return false
    }
  }

  /**
   * 更新本地缓存
   */
  private updateLocalCache(messages: LangChainMessage[]): void {
    const oldCount = this.lastSyncedMessageCount
    const oldId = this.lastSyncedMessageId

    this.lastSyncedMessageCount = messages.length
    this.lastSyncedMessageId = messages[messages.length - 1]?.id || null

    // 🐛 调试日志：跟踪缓存更新
    console.log('💾 [ChatSync] 缓存已更新:', {
      旧数量: oldCount,
      新数量: this.lastSyncedMessageCount,
      旧ID: oldId,
      新ID: this.lastSyncedMessageId
    })
  }

  /**
   * 清理缓存（在切换聊天时调用）
   */
  clearCache(): void {
    this.lastSyncedMessageCount = 0
    this.lastSyncedMessageId = null
    this.messageHashes.clear()

    // 取消任何待执行的防抖函数
    this.debouncedSync.cancel()

    console.log('🧹 [ChatSync] 缓存已清理')
  }

  /**
   * 强制立即同步（跳过防抖）
   */
  async forceSyncNow(
    chatId: string,
    messages: LangChainMessage[],
    roleId: string
  ): Promise<SyncResult> {
    // 取消防抖
    this.debouncedSync.cancel()

    // 立即执行同步
    return await this.performSync(chatId, messages, roleId)
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): {
    inProgress: boolean
    lastSyncedCount: number
    lastSyncedId: string | null
    cacheSize: number
  } {
    return {
      inProgress: this.syncInProgress,
      lastSyncedCount: this.lastSyncedMessageCount,
      lastSyncedId: this.lastSyncedMessageId,
      cacheSize: this.messageHashes.size
    }
  }

  /**
   * 销毁管理器（清理资源）
   */
  destroy(): void {
    this.debouncedSync.cancel()
    this.clearCache()
    console.log('🗑️ [ChatSync] 同步管理器已销毁')
  }
}

// 创建默认实例
export const defaultChatSyncManager = new ChatSyncManager()

// 导出工厂函数
export const createChatSyncManager = (options?: SyncOptions) => new ChatSyncManager(options)

import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  PlayCircleOutlined,
  CodeOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import { commandSetService } from '@/services/device-command-sets'
import type { DeviceCommandSet } from '@/services/device-command-sets'
import { simpleModeService } from '@/services/simple-device-modes'
import type { SimpleDeviceMode } from '@/services/simple-device-modes'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input


const SimpleModesManagement: React.FC = () => {
  const [modes, setModes] = useState<SimpleDeviceMode[]>([])
  const [commandSets, setCommandSets] = useState<DeviceCommandSet[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingMode, setEditingMode] = useState<SimpleDeviceMode | null>(null)
  const [form] = Form.useForm()
  
  // 搜索条件
  const [searchParams, setSearchParams] = useState({
    keyword: '',
    isActive: undefined as boolean | undefined
  })

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    inactive: 0
  })

  useEffect(() => {
    loadModes()
    loadCommandSets()
  }, [currentPage, pageSize, searchParams])

  const loadModes = async () => {
    try {
      setLoading(true)
      
      const response = await simpleModeService.getModes({
        page: currentPage,
        pageSize,
        ...searchParams
      })
      
      if (response.success && response.data) {
        setModes(response.data.data)
        setTotal(response.data.total)
        
        // 计算统计数据
        const activeCount = response.data.data.filter(item => item.isActive).length
        setStats({
          total: response.data.total,
          active: activeCount,
          inactive: response.data.total - activeCount
        })
      } else {
        message.error(response.message || '获取设备模式列表失败')
      }
    } catch (error) {
      console.error('获取模式列表失败:', error)
      message.error('获取模式列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadCommandSets = async () => {
    try {
      const response = await commandSetService.getCommandSets({ 
        page: 1, 
        pageSize: 100,
        isActive: true 
      })
      if (response.success && response.data) {
        setCommandSets(response.data.data)
      }
    } catch (error) {
      console.error('获取指令集失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      keyword: '',
      isActive: undefined
    })
    setCurrentPage(1)
  }

  const handleCreate = () => {
    setEditingMode(null)
    form.resetFields()
    form.setFieldsValue({ isActive: true })
    setModalVisible(true)
  }

  const handleEdit = (mode: SimpleDeviceMode) => {
    setEditingMode(mode)
    form.setFieldsValue({
      name: mode.name,
      description: mode.description,
      commandSetId: mode.commandSetId,
      isActive: mode.isActive
    })
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingMode) {
        const response = await simpleModeService.updateMode(editingMode.id, values)
        if (response.success) {
          message.success('模式更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await simpleModeService.createMode(values)
        if (response.success) {
          message.success('模式创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }
      
      setModalVisible(false)
      loadModes()
    } catch (error) {
      console.error('操作失败:', error)
      message.error(editingMode ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await simpleModeService.deleteMode(id)
      if (response.success) {
        message.success('模式删除成功')
        loadModes()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = await simpleModeService.toggleStatus(id, isActive)
      if (response.success) {
        message.success(isActive ? '模式已启用' : '模式已禁用')
        loadModes()
      } else {
        message.error(response.message || '状态切换失败')
      }
    } catch (error) {
      console.error('状态切换失败:', error)
      message.error('状态切换失败')
    }
  }

  const columns: ColumnsType<SimpleDeviceMode> = [
    {
      title: '模式信息',
      key: 'modeInfo',
      render: (_, record) => (
        <Space>
          <PlayCircleOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.name}</div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              {record.description || '无描述'}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '关联指令',
      key: 'commandSet',
      render: (_, record) => {
        return record.commandSet ? (
          <div>
            <div style={{ fontWeight: 500 }}>{record.commandSet.name}</div>
            <div style={{ fontSize: '12px', color: '#666', fontFamily: 'monospace' }}>
              {record.commandSet.command.length > 30 
                ? `${record.commandSet.command.substring(0, 30)}...`
                : record.commandSet.command
              }
            </div>
            {record.commandSet.description && (
              <div style={{ fontSize: '11px', color: '#999', marginTop: 2 }}>
                {record.commandSet.description}
              </div>
            )}
          </div>
        ) : (
          <Text type="secondary">未找到指令集</Text>
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: (isActive, record) => (
        <Switch
          checked={isActive}
          onChange={checked => handleToggleStatus(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个模式吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        <PlayCircleOutlined /> 模式管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总模式数"
              value={stats.total}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="启用模式"
              value={stats.active}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="禁用模式"
              value={stats.inactive}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索模式名称或描述"
            style={{ width: 240 }}
            value={searchParams.keyword}
            onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={(value) => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          
          <Button onClick={handleReset}>
            重置
          </Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            新增模式
          </Button>
        </Space>
      </Card>

      {/* 模式列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={modes}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 创建/编辑模式模态框 */}
      <Modal
        title={editingMode ? '编辑模式' : '新增模式'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="模式名称"
            rules={[{ required: true, message: '请输入模式名称' }]}
          >
            <Input placeholder="例如：连续模式" />
          </Form.Item>

          <Form.Item
            name="description"
            label="模式描述"
          >
            <TextArea 
              rows={2}
              placeholder="描述这个模式的特点和效果..."
            />
          </Form.Item>

          <Form.Item
            name="commandSetId"
            label="关联指令集"
            rules={[{ required: true, message: '请选择指令集' }]}
          >
            <Select 
              placeholder="选择要绑定的指令集" 
              style={{ width: '100%' }}
              showSearch
              optionFilterProp="children"
            >
              {commandSets.map(cs => (
                <Select.Option key={cs.id} value={cs.id} title={cs.description}>
                  <div>
                    <div style={{ fontWeight: 500 }}>{cs.name}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      {cs.description || '无描述'}
                    </div>
                  </div>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingMode ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default SimpleModesManagement
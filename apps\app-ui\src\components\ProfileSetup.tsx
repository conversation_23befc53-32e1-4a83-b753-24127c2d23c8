import { useState, useRef, useEffect } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useTranslation } from 'react-i18next'
import { z } from 'zod'
import { uploadService } from '@/api/services/upload'

// HeroUI 组件
import { Button, Input, Avatar, addToast } from '@heroui/react'

// Lucide 图标
import { Camera, User, Mail, Check, Loader2 } from 'lucide-react'

// Stores
import { useUserProfileStore, useUserProfileData } from '@/stores/user-profile-store'

interface ProfileSetupProps {
  showSkip?: boolean
  onComplete?: (data: any) => void
  onSkip?: () => void
  title?: string
  subtitle?: string
}

export default function ProfileSetup({
  showSkip = false,
  onComplete,
  onSkip,
  title,
  subtitle
}: ProfileSetupProps) {
  const { t } = useTranslation('profile')
  const { user } = useAuth()
  
  // 如果没有传入标题和副标题，使用默认值
  title = title || t('setup.title')
  subtitle = subtitle || t('setup.subtitle')

  // 使用Zustand store
  const { userProfile, isLoading, isUpdating } = useUserProfileData()
  const { fetchUserProfile, updateUserProfile, updateAvatar } = useUserProfileStore()

  const [isUploading, setIsUploading] = useState(false)
  const [nickname, setNickname] = useState('')
  const [gender, setGender] = useState<'male' | 'female' | 'other'>('other')
  const [avatarUrl, setAvatarUrl] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 获取现有用户资料
  useEffect(() => {
    const loadUserProfile = async () => {
      if (!user) return

      try {
        const profile = await fetchUserProfile()

        if (profile) {
          // 使用现有的用户资料数据
          setNickname(profile.nickname || '')
          setGender((profile.gender as 'male' | 'female' | 'other') || 'other')
          setAvatarUrl(profile.avatarUrl || '')
        } else {
          // 如果没有现有资料，使用邮箱前缀作为默认昵称
          setNickname(user?.email?.split('@')[0] || '')
        }
      } catch (error) {
        console.error('获取用户资料失败:', error)
        // 如果获取失败，使用邮箱前缀作为默认昵称
        setNickname(user?.email?.split('@')[0] || '')
      }
    }

    loadUserProfile()
  }, [user, fetchUserProfile])

  const handleSubmit = async () => {
    try {
      // 表单验证
      const profileSchema = z.object({
        nickname: z.string().min(2, { message: t('setup.validation.nickname_min') }).max(50),
        gender: z.enum(['male', 'female', 'other']),
        avatarUrl: z.string().optional()
      })

      const validatedData = profileSchema.parse({
        nickname,
        gender,
        avatarUrl
      })

      // 使用Zustand store更新用户资料
      const updatedProfile = await updateUserProfile(validatedData)

      if (updatedProfile) {
        addToast({
          title: t('setup.toast.save_success'),
          color: 'success'
        })
        onComplete?.(updatedProfile)
      } else {
        throw new Error('更新失败')
      }
    } catch (error) {
      console.error('更新个人资料失败:', error)
      if (error instanceof z.ZodError) {
        addToast({
          title: t('setup.validation.check_input'),
          color: 'danger'
        })
      } else {
        addToast({
          title: t('setup.toast.save_failed'),
          color: 'danger'
        })
      }
    }
  }

  const handleSkip = () => {
    addToast({
      title: t('setup.toast.skipped'),
      color: 'primary'
    })
    onSkip?.()
  }

  // 处理头像上传
  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // 使用 uploadService 的验证函数
    const validation = uploadService.validateFile(file, 'avatar')
    if (!validation.valid) {
      addToast({
        title: validation.error || t('setup.avatar.validation_failed'),
        color: 'danger'
      })
      return
    }

    try {
      setIsUploading(true)
      console.log('开始上传头像:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
      })

      const response = await uploadService.uploadAvatar(file)
      console.log('头像上传响应:', response)

      const newAvatarUrl = response.avatarUrl

      // 更新本地状态
      setAvatarUrl(newAvatarUrl)

      // 更新store中的头像
      updateAvatar(newAvatarUrl)

      addToast({
        title: t('setup.avatar.upload_success'),
        color: 'primary'
      })
    } catch (error) {
      console.error('上传头像失败:', error)

      // 更详细的错误信息
      let errorMessage = '上传头像失败'
      if (error instanceof Error) {
        errorMessage = error.message
      }

      addToast({
        title: t('setup.avatar.upload_failed'),
        description: errorMessage,
        color: 'danger'
      })
    } finally {
      setIsUploading(false)
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  // 如果正在加载用户资料，显示加载状态
  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-2xl font-light text-foreground mb-2">{title}</h1>
          <p className="text-default-500 text-sm">{subtitle}</p>
        </div>

        <div className="flex justify-center">
          <Loader2 className="w-8 h-8 animate-spin text-primary" />
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* 标题 */}
      <div className="text-center">
        <h1 className="text-2xl font-light text-foreground mb-2">{title}</h1>
        <p className="text-default-500 text-sm">{subtitle}</p>
      </div>

      {/* 头像上传 */}
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <Avatar
            src={avatarUrl}
            icon={<User className="w-8 h-8" />}
            className="w-20 h-20 text-large cursor-pointer"
            isBordered
            color="primary"
            onClick={handleUploadClick}
          />
          <Button
            isIconOnly
            size="sm"
            className="absolute -bottom-1 -right-1 bg-foreground text-background min-w-6 h-6"
            onPress={handleUploadClick}
            isLoading={isUploading}
          >
            {isUploading ? (
              <Loader2 className="w-3 h-3 animate-spin" />
            ) : (
              <Camera className="w-3 h-3" />
            )}
          </Button>
        </div>

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleAvatarUpload}
          accept="image/jpeg,image/jpg,image/png,image/webp"
          className="hidden"
        />
      </div>

      {/* 表单字段 */}
      <div className="space-y-6">
        {/* 邮箱显示 */}
        <div className="space-y-2">
          <label className="text-sm text-default-600 font-medium">{t('setup.form.email')}</label>
          <Input
            type="email"
            value={user?.email || ''}
            isReadOnly
            variant="flat"
            size="lg"
            startContent={<Mail className="w-4 h-4 text-default-400 mr-2" />}
            classNames={{
              input: 'text-default-500',
              inputWrapper: 'bg-content2/50 border-default-300'
            }}
          />
        </div>

        {/* 昵称输入 */}
        <div className="space-y-2">
          <label className="text-sm text-default-600 font-medium">{t('setup.form.nickname')}</label>
          <Input
            type="text"
            placeholder={t('setup.form.nickname_placeholder')}
            value={nickname}
            onChange={e => setNickname(e.target.value)}
            variant="flat"
            size="lg"
            isRequired
            classNames={{
              input: 'text-foreground placeholder:text-default-400',
              inputWrapper:
                'bg-content2/50 border-default-300 hover:border-default-400 focus-within:border-primary backdrop-blur-sm'
            }}
          />
        </div>

        {/* 性别选择 */}
        <div className="space-y-3">
          <label className="text-sm text-default-600 font-medium">{t('setup.form.gender')}</label>
          <div className="grid grid-cols-3 gap-3">
            <Button
              variant={gender === 'male' ? 'solid' : 'bordered'}
              color={gender === 'male' ? 'primary' : 'default'}
              onPress={() => setGender('male')}
              className="h-12"
              startContent={gender === 'male' ? <Check className="w-4 h-4" /> : undefined}
            >
              {t('setup.form.gender_male')}
            </Button>
            <Button
              variant={gender === 'female' ? 'solid' : 'bordered'}
              color={gender === 'female' ? 'primary' : 'default'}
              onPress={() => setGender('female')}
              className="h-12"
              startContent={gender === 'female' ? <Check className="w-4 h-4" /> : undefined}
            >
              {t('setup.form.gender_female')}
            </Button>
            <Button
              variant={gender === 'other' ? 'solid' : 'bordered'}
              color={gender === 'other' ? 'primary' : 'default'}
              onPress={() => setGender('other')}
              className="h-12"
              startContent={gender === 'other' ? <Check className="w-4 h-4" /> : undefined}
            >
              {t('setup.form.gender_other')}
            </Button>
          </div>
        </div>
      </div>

      {/* 按钮组 */}
      <div className="space-y-4">
        <Button
          color="primary"
          size="lg"
          className="w-full bg-button-primary"
          onPress={handleSubmit}
          isLoading={isUpdating}
          isDisabled={!nickname.trim() || nickname.length < 2}
          radius="full"
        >
          {isUpdating ? t('setup.button.saving') : t('setup.button.save')}
        </Button>

        {showSkip && (
          <Button
            variant="light"
            size="lg"
            className="w-full text-default-500 hover:text-foreground"
            onPress={handleSkip}
          >
            {t('setup.button.skip')}
          </Button>
        )}
      </div>
    </div>
  )
}

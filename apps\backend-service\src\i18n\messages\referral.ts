// 推荐相关消息
export const referralMessages = {
  zh: {
    // 用户认证相关
    user_not_authenticated: '用户未认证',
    user_data_not_found: '用户数据不存在',
    
    // 邀请码相关
    invite_code_required: '邀请码不能为空',
    invite_code_generate_success: '邀请码生成成功',
    invite_code_generate_failed: '生成邀请码失败',
    invite_code_info_get_success: '获取邀请码信息成功',
    invite_code_info_get_failed: '获取邀请码信息失败',
    invite_code_validate_success: '邀请码验证成功',
    invite_code_validate_failed: '验证邀请码失败',
    
    // 邀请列表相关
    invites_list_get_success: '获取邀请列表成功',
    invites_list_get_failed: '获取邀请列表失败',
    
    // 佣金账户相关
    commission_account_get_success: '获取佣金账户信息成功',
    commission_account_get_failed: '获取佣金账户信息失败',
    commission_account_not_found: '佣金账户不存在',
    commission_records_get_success: '获取佣金记录成功',
    commission_records_get_failed: '获取佣金记录失败',
    
    // 统计数据相关
    statistics_get_success: '获取统计数据成功',
    statistics_get_failed: '获取统计数据失败',
    
    // 提现相关
    withdraw_amount_required: '提现金额必须大于0',
    withdraw_bank_name_required: '银行名称不能为空',
    withdraw_account_name_required: '账户名不能为空',
    withdraw_account_number_required: '账户号码不能为空',
    withdraw_config_get_success: '获取提现配置成功',
    withdraw_config_get_failed: '获取提现配置失败',
    withdraw_amount_too_low: '提现金额不能少于 {minAmount} 元',
    withdraw_insufficient_balance: '可提现余额不足，当前余额: {balance} 元',
    withdraw_apply_success: '提现申请提交成功，请等待审核',
    withdraw_apply_failed: '提交提现申请失败',
    withdraw_records_get_success: '获取提现记录成功',
    withdraw_records_get_failed: '获取提现记录失败',
    withdraw_fee_description: '提现手续费 {rate}%'
  },
  'zh-TW': {
    // 使用者驗證相關
    user_not_authenticated: '使用者未驗證',
    user_data_not_found: '使用者資料不存在',
    
    // 邀請碼相關
    invite_code_required: '邀請碼不能為空',
    invite_code_generate_success: '邀請碼產生成功',
    invite_code_generate_failed: '產生邀請碼失敗',
    invite_code_info_get_success: '取得邀請碼資訊成功',
    invite_code_info_get_failed: '取得邀請碼資訊失敗',
    invite_code_validate_success: '邀請碼驗證成功',
    invite_code_validate_failed: '驗證邀請碼失敗',
    
    // 邀請清單相關
    invites_list_get_success: '取得邀請清單成功',
    invites_list_get_failed: '取得邀請清單失敗',
    
    // 佣金帳戶相關
    commission_account_get_success: '取得佣金帳戶資訊成功',
    commission_account_get_failed: '取得佣金帳戶資訊失敗',
    commission_account_not_found: '佣金帳戶不存在',
    commission_records_get_success: '取得佣金記錄成功',
    commission_records_get_failed: '取得佣金記錄失敗',
    
    // 統計資料相關
    statistics_get_success: '取得統計資料成功',
    statistics_get_failed: '取得統計資料失敗',
    
    // 提現相關
    withdraw_amount_required: '提現金額必須大於0',
    withdraw_bank_name_required: '銀行名稱不能為空',
    withdraw_account_name_required: '帳戶名稱不能為空',
    withdraw_account_number_required: '帳戶號碼不能為空',
    withdraw_config_get_success: '取得提現設定成功',
    withdraw_config_get_failed: '取得提現設定失敗',
    withdraw_amount_too_low: '提現金額不能少於 {minAmount} 元',
    withdraw_insufficient_balance: '可提現餘額不足，目前餘額：{balance} 元',
    withdraw_apply_success: '提現申請提交成功，請等待審核',
    withdraw_apply_failed: '提交提現申請失敗',
    withdraw_records_get_success: '取得提現記錄成功',
    withdraw_records_get_failed: '取得提現記錄失敗',
    withdraw_fee_description: '提現手續費 {rate}%'
  },
  ja: {
    // ユーザー認証関連
    user_not_authenticated: 'ユーザーが認証されていません',
    user_data_not_found: 'ユーザーデータが存在しません',
    
    // 招待コード関連
    invite_code_required: '招待コードを入力してください',
    invite_code_generate_success: '招待コードの生成に成功しました',
    invite_code_generate_failed: '招待コードの生成に失敗しました',
    invite_code_info_get_success: '招待コード情報の取得に成功しました',
    invite_code_info_get_failed: '招待コード情報の取得に失敗しました',
    invite_code_validate_success: '招待コードの検証に成功しました',
    invite_code_validate_failed: '招待コードの検証に失敗しました',
    
    // 招待リスト関連
    invites_list_get_success: '招待リストの取得に成功しました',
    invites_list_get_failed: '招待リストの取得に失敗しました',
    
    // 手数料アカウント関連
    commission_account_get_success: '手数料アカウント情報の取得に成功しました',
    commission_account_get_failed: '手数料アカウント情報の取得に失敗しました',
    commission_account_not_found: '手数料アカウントが存在しません',
    commission_records_get_success: '手数料記録の取得に成功しました',
    commission_records_get_failed: '手数料記録の取得に失敗しました',
    
    // 統計データ関連
    statistics_get_success: '統計データの取得に成功しました',
    statistics_get_failed: '統計データの取得に失敗しました',
    
    // 出金関連
    withdraw_amount_required: '出金金額は0より大きい必要があります',
    withdraw_bank_name_required: '銀行名を入力してください',
    withdraw_account_name_required: 'アカウント名を入力してください',
    withdraw_account_number_required: 'アカウント番号を入力してください',
    withdraw_config_get_success: '出金設定の取得に成功しました',
    withdraw_config_get_failed: '出金設定の取得に失敗しました',
    withdraw_amount_too_low: '出金金額は{minAmount}円以上である必要があります',
    withdraw_insufficient_balance: '出金可能残高が不足しています。現在の残高：{balance}円',
    withdraw_apply_success: '出金申請を正常に提出しました。審査をお待ちください',
    withdraw_apply_failed: '出金申請の提出に失敗しました',
    withdraw_records_get_success: '出金記録の取得に成功しました',
    withdraw_records_get_failed: '出金記録の取得に失敗しました',
    withdraw_fee_description: '出金手数料 {rate}%'
  },
  es: {
    // Relacionado con autenticación de usuario
    user_not_authenticated: 'Usuario no autenticado',
    user_data_not_found: 'Los datos del usuario no existen',
    
    // Relacionado con códigos de invitación
    invite_code_required: 'El código de invitación no puede estar vacío',
    invite_code_generate_success: 'Código de invitación generado correctamente',
    invite_code_generate_failed: 'Error al generar el código de invitación',
    invite_code_info_get_success: 'Información del código de invitación obtenida correctamente',
    invite_code_info_get_failed: 'Error al obtener la información del código de invitación',
    invite_code_validate_success: 'Código de invitación validado correctamente',
    invite_code_validate_failed: 'Error al validar el código de invitación',
    
    // Relacionado con lista de invitaciones
    invites_list_get_success: 'Lista de invitaciones obtenida correctamente',
    invites_list_get_failed: 'Error al obtener la lista de invitaciones',
    
    // Relacionado con cuenta de comisiones
    commission_account_get_success: 'Información de la cuenta de comisiones obtenida correctamente',
    commission_account_get_failed: 'Error al obtener la información de la cuenta de comisiones',
    commission_account_not_found: 'La cuenta de comisiones no existe',
    commission_records_get_success: 'Registros de comisiones obtenidos correctamente',
    commission_records_get_failed: 'Error al obtener los registros de comisiones',
    
    // Relacionado con datos estadísticos
    statistics_get_success: 'Datos estadísticos obtenidos correctamente',
    statistics_get_failed: 'Error al obtener los datos estadísticos',
    
    // Relacionado con retiros
    withdraw_amount_required: 'El monto del retiro debe ser mayor que 0',
    withdraw_bank_name_required: 'El nombre del banco no puede estar vacío',
    withdraw_account_name_required: 'El nombre de la cuenta no puede estar vacío',
    withdraw_account_number_required: 'El número de cuenta no puede estar vacío',
    withdraw_config_get_success: 'Configuración de retiro obtenida correctamente',
    withdraw_config_get_failed: 'Error al obtener la configuración de retiro',
    withdraw_amount_too_low: 'El monto del retiro no puede ser menor a {minAmount}',
    withdraw_insufficient_balance: 'Saldo insuficiente para retiro, saldo actual: {balance}',
    withdraw_apply_success: 'Solicitud de retiro enviada correctamente, por favor espere la revisión',
    withdraw_apply_failed: 'Error al enviar la solicitud de retiro',
    withdraw_records_get_success: 'Registros de retiros obtenidos correctamente',
    withdraw_records_get_failed: 'Error al obtener los registros de retiros',
    withdraw_fee_description: 'Comisión de retiro {rate}%'
  },
  en: {
    // User authentication related
    user_not_authenticated: 'User not authenticated',
    user_data_not_found: 'User data does not exist',
    
    // Invite code related
    invite_code_required: 'Invite code cannot be empty',
    invite_code_generate_success: 'Invite code generated successfully',
    invite_code_generate_failed: 'Failed to generate invite code',
    invite_code_info_get_success: 'Invite code information retrieved successfully',
    invite_code_info_get_failed: 'Failed to get invite code information',
    invite_code_validate_success: 'Invite code validated successfully',
    invite_code_validate_failed: 'Failed to validate invite code',
    
    // Invite list related
    invites_list_get_success: 'Invite list retrieved successfully',
    invites_list_get_failed: 'Failed to get invite list',
    
    // Commission account related
    commission_account_get_success: 'Commission account information retrieved successfully',
    commission_account_get_failed: 'Failed to get commission account information',
    commission_account_not_found: 'Commission account does not exist',
    commission_records_get_success: 'Commission records retrieved successfully',
    commission_records_get_failed: 'Failed to get commission records',
    
    // Statistics related
    statistics_get_success: 'Statistics retrieved successfully',
    statistics_get_failed: 'Failed to get statistics',
    
    // Withdrawal related
    withdraw_amount_required: 'Withdrawal amount must be greater than 0',
    withdraw_bank_name_required: 'Bank name cannot be empty',
    withdraw_account_name_required: 'Account name cannot be empty',
    withdraw_account_number_required: 'Account number cannot be empty',
    withdraw_config_get_success: 'Withdrawal configuration retrieved successfully',
    withdraw_config_get_failed: 'Failed to get withdrawal configuration',
    withdraw_amount_too_low: 'Minimum withdrawal amount is {minAmount}',
    withdraw_insufficient_balance: 'Insufficient balance. Current balance: {balance}',
    withdraw_apply_success: 'Withdrawal request submitted successfully, please wait for review',
    withdraw_apply_failed: 'Failed to submit withdrawal request',
    withdraw_records_get_success: 'Withdrawal records retrieved successfully',
    withdraw_records_get_failed: 'Failed to get withdrawal records',
    withdraw_fee_description: 'Withdrawal fee {rate}%'
  }
}

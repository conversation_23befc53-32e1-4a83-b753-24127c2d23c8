import { useState, useEffect, useCallback } from 'react'
import { apiService } from '@/api'
import { getGlobalChatDatabase } from '@/lib/chat-database'
import type { ChatDatabaseInterface, ChatSession } from '@/lib/chat-database/types'
import type { ChatHistory } from '@/lib/types'

interface ChatHistoryResult {
  chatHistory: ChatHistory[]
  isLoading: boolean
  error: string | null
  refresh: () => Promise<void>
  deleteChat: (chatId: string) => Promise<void>
  cleanupEmptySessions: () => Promise<number>
}

/**
 * SQLite 优先的聊天历史 Hook
 * 实现本地优先 + 后台同步策略
 */
export function useChatHistory(): ChatHistoryResult {
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  /**
   * 转换数据库会话到历史记录格式
   */
  const convertSessionToHistory = (session: ChatSession): ChatHistory => ({
    id: session.id,
    roleId: session.roleId,
    title: session.title,
    lastMessage: '', // 这里可以考虑从最后一条消息获取
    createdAt: session.createdAt,
    updatedAt: session.updatedAt,
    lastMessageAt: session.lastMessageAt,
    messageCount: session.messageCount,
    visibility: 'private' as const
  })

  /**
   * 从本地 SQLite 加载聊天历史
   */
  const loadFromLocal = useCallback(async (): Promise<ChatHistory[]> => {
    try {
      const chatDatabase = getGlobalChatDatabase()
      const sessions = await chatDatabase.getAllSessions()

      console.log('📦 [ChatHistory] 从本地数据库加载会话:', sessions.length)

      // 🚀 过滤掉空的会话记录（messageCount为0且title为空的会话）
      const validSessions = sessions.filter(session => {
        const isEmpty =
          session.messageCount === 0 && (!session.title || session.title.trim() === '')
        if (isEmpty) {
          console.log('🗑️ [ChatHistory] 过滤空会话:', session.id)
        }
        return !isEmpty
      })

      console.log('📦 [ChatHistory] 有效会话数量:', validSessions.length)

      // 转换格式并按最后消息时间排序
      const histories = validSessions
        .map(convertSessionToHistory)
        .sort(
          (a, b) =>
            new Date(b.lastMessageAt || b.updatedAt).getTime() -
            new Date(a.lastMessageAt || a.updatedAt).getTime()
        )

      return histories
    } catch (error) {
      console.warn('📦 [ChatHistory] 本地加载失败:', error)
      return []
    }
  }, [])

  /**
   * 从服务器同步聊天历史
   */
  const syncFromServer = useCallback(async (): Promise<ChatHistory[]> => {
    try {
      const response = await apiService.history.getList({ limit: 50 })
      const serverChats = response.chats || []

      console.log('🌐 [ChatHistory] 从服务器获取会话:', serverChats.length)

      return serverChats
    } catch (error) {
      console.warn('🌐 [ChatHistory] 服务器同步失败:', error)
      throw error
    }
  }, [])

  /**
   * 合并本地和服务器数据，并更新本地数据库
   */
  const mergeAndUpdateLocal = useCallback(
    async (localChats: ChatHistory[], serverChats: ChatHistory[]): Promise<ChatHistory[]> => {
      try {
        const chatDatabase = getGlobalChatDatabase()

        // 以服务器数据为准，但保留本地独有的会话
        const serverChatIds = new Set(serverChats.map(chat => chat.id))
        const localOnlyChats = localChats.filter(chat => !serverChatIds.has(chat.id))

        console.log(
          '🔄 [ChatHistory] 合并数据 - 服务器:',
          serverChats.length,
          '本地独有:',
          localOnlyChats.length
        )

        // 合并数据
        const mergedChats = [...serverChats, ...localOnlyChats].sort(
          (a, b) =>
            new Date(b.lastMessageAt || b.updatedAt).getTime() -
            new Date(a.lastMessageAt || a.updatedAt).getTime()
        )

        // 更新本地数据库
        for (const chat of serverChats) {
          const existingSession = await chatDatabase.getSession(chat.id)
          if (!existingSession) {
            // 创建新会话
            await chatDatabase.createSession({
              id: chat.id,
              roleId: chat.roleId,
              title: chat.title,
              messageCount: chat.messageCount || 0,
              lastMessageAt: chat.lastMessageAt || chat.updatedAt
            })
          } else {
            // 更新现有会话
            await chatDatabase.updateSession(chat.id, {
              title: chat.title,
              messageCount: chat.messageCount || existingSession.messageCount,
              lastMessageAt: chat.lastMessageAt || chat.updatedAt
            })
          }
        }

        console.log('✅ [ChatHistory] 本地数据库已更新')
        return mergedChats
      } catch (error) {
        console.error('❌ [ChatHistory] 合并数据失败:', error)
        // 合并失败时返回服务器数据
        return serverChats
      }
    },
    []
  )

  /**
   * 清理空的会话记录
   */
  const cleanupEmptySessions = useCallback(async (): Promise<number> => {
    try {
      const chatDatabase = getGlobalChatDatabase()
      const sessions = await chatDatabase.getAllSessions()

      // 找出空的会话
      const emptySessions = sessions.filter(session => {
        return session.messageCount === 0 && (!session.title || session.title.trim() === '')
      })

      if (emptySessions.length === 0) {
        console.log('🧹 [ChatHistory] 没有空会话需要清理')
        return 0
      }

      console.log('🧹 [ChatHistory] 清理空会话:', emptySessions.length)

      // 删除空会话
      for (const session of emptySessions) {
        await chatDatabase.deleteSession(session.id)
      }

      console.log('✅ [ChatHistory] 空会话清理完成')
      return emptySessions.length
    } catch (error) {
      console.error('❌ [ChatHistory] 清理空会话失败:', error)
      return 0
    }
  }, [])

  /**
   * 主要的数据加载逻辑
   */
  const loadChatHistory = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      // 🧹 步骤0: 清理空会话记录（异步执行，不阻塞主流程）
      cleanupEmptySessions().catch(error => {
        console.warn('⚠️ [ChatHistory] 清理空会话失败:', error)
      })

      // 🚀 步骤1: 优先从本地加载，立即显示
      const localChats = await loadFromLocal()

      if (localChats.length > 0) {
        console.log('⚡ [ChatHistory] 立即显示本地数据')
        setChatHistory(localChats)
        setIsLoading(false) // 有本地数据时立即停止加载状态
      }

      // 🔄 步骤2: 后台同步服务器数据
      try {
        const serverChats = await syncFromServer()
        const mergedChats = await mergeAndUpdateLocal(localChats, serverChats)

        // 只有当数据发生变化时才更新UI
        if (JSON.stringify(mergedChats) !== JSON.stringify(localChats)) {
          console.log('🔄 [ChatHistory] 数据已更新，刷新UI')
          setChatHistory(mergedChats)
        }
      } catch (syncError) {
        // 后台同步失败，但不影响本地数据的显示
        if (localChats.length === 0) {
          // 只有在没有本地数据时才显示同步错误
          throw syncError
        }
        console.warn('⚠️ [ChatHistory] 后台同步失败，使用本地数据')
      }
    } catch (error) {
      console.error('❌ [ChatHistory] 加载聊天历史失败:', error)
      setError('加载聊天历史失败，请检查网络连接')
    } finally {
      setIsLoading(false)
    }
  }, [loadFromLocal, syncFromServer, mergeAndUpdateLocal, cleanupEmptySessions])

  /**
   * 删除聊天记录
   */
  const deleteChat = useCallback(async (chatId: string) => {
    try {
      // 同时删除服务器和本地数据
      await apiService.history.delete(chatId)

      const chatDatabase = getGlobalChatDatabase()
      await chatDatabase.deleteSession(chatId)

      // 更新UI
      setChatHistory(prev => prev.filter(chat => chat.id !== chatId))

      console.log('✅ [ChatHistory] 会话已删除:', chatId)
    } catch (error) {
      console.error('❌ [ChatHistory] 删除会话失败:', error)
      throw error
    }
  }, [])

  /**
   * 刷新数据
   */
  const refresh = useCallback(() => {
    return loadChatHistory()
  }, [loadChatHistory])

  // 初始化加载
  useEffect(() => {
    loadChatHistory()
  }, [loadChatHistory])

  return {
    chatHistory,
    isLoading,
    error,
    refresh,
    deleteChat,
    cleanupEmptySessions
  }
}

/**
 * 媒体生成轮询管理器
 * 基于数据库附件状态的纯轮询方案，移除SSE依赖
 */

import { getGlobalChatDatabase } from '@/lib/chat-database'
import { ImageStorage } from './image-storage'
import { AudioStorage } from './audio-storage'

interface MediaTask {
  taskId: string
  messageId: string
  chatId: string
  mediaType: 'image' | 'video' | 'audio'
  status: 'generating' | 'downloading' | 'completed' | 'failed'
  startTime: number
  lastCheckTime: number
}

export class MediaGenerationManager {
  private static instance: MediaGenerationManager
  private activeTasks = new Map<string, MediaTask>()
  private pollingIntervals = new Map<string, NodeJS.Timeout>()
  private isPageVisible = true
  private currentChatId: string | null = null

  // 轮询策略
  private readonly POLLING_INTERVALS = {
    ACTIVE_CHAT: 3000, // 当前聊天：3秒
    INACTIVE: 0, // 非当前聊天：不轮询
    MAX_DURATION: 600000 // 最大轮询时长：10分钟
  }

  static getInstance(): MediaGenerationManager {
    if (!MediaGenerationManager.instance) {
      MediaGenerationManager.instance = new MediaGenerationManager()
    }
    return MediaGenerationManager.instance
  }

  private constructor() {
    this.initializePageVisibilityListener()
  }

  /**
   * 初始化页面可见性监听
   */
  private initializePageVisibilityListener() {
    if (typeof document !== 'undefined') {
      document.addEventListener('visibilitychange', () => {
        this.isPageVisible = !document.hidden
        this.adjustAllPollingStrategies()
      })
    }
  }

  /**
   * 设置当前聊天ID
   */
  setCurrentChatId(chatId: string | null) {
    if (this.currentChatId !== chatId) {
      this.currentChatId = chatId
      this.adjustAllPollingStrategies()
    }
  }

  /**
   * 开始监控媒体生成任务
   */
  async startMonitoring(messageId: string, chatId: string, mediaType: 'image' | 'video' | 'audio') {
    const taskId = `${mediaType}_${messageId}`

    // 检查是否已经在监控
    if (this.activeTasks.has(taskId)) {
      console.log(`📊 [MediaManager] 任务已在监控中: ${taskId}`)
      return
    }

    // 创建任务记录
    const task: MediaTask = {
      taskId,
      messageId,
      chatId,
      mediaType,
      status: 'generating',
      startTime: Date.now(),
      lastCheckTime: 0
    }

    this.activeTasks.set(taskId, task)
    console.log(`📊 [MediaManager] 开始监控任务: ${taskId}`)

    // 立即检查一次状态
    await this.checkTaskStatus(task)

    // 根据当前条件决定是否开始轮询
    this.adjustPollingStrategy(task)
  }

  /**
   * 停止监控任务
   */
  stopMonitoring(messageId: string, mediaType: 'image' | 'video' | 'audio') {
    const taskId = `${mediaType}_${messageId}`

    // 清理轮询
    const interval = this.pollingIntervals.get(taskId)
    if (interval) {
      clearInterval(interval)
      this.pollingIntervals.delete(taskId)
    }

    // 移除任务
    this.activeTasks.delete(taskId)
    console.log(`📊 [MediaManager] 停止监控任务: ${taskId}`)
  }

  /**
   * 恢复页面进入时的任务检查
   */
  async resumeTasksForChat(chatId: string) {
    console.log(`📊 [MediaManager] 恢复聊天任务检查: ${chatId}`)

    const chatDatabase = getGlobalChatDatabase()
    if (!chatDatabase) return

    try {
      // 查询该聊天中所有生成中的附件
      const messages = await chatDatabase.getMessagesByChat(chatId)

      for (const message of messages) {
        const attachments = await chatDatabase.getAttachmentsByMessage(message.id)

        const generatingAttachments = attachments.filter(
          att => att.status === 'generating' || att.status === 'downloading'
        )

        for (const attachment of generatingAttachments) {
          // 恢复监控
          await this.startMonitoring(message.id, chatId, attachment.type as any)
        }
      }
    } catch (error) {
      console.error('❌ [MediaManager] 恢复任务失败:', error)
    }
  }

  /**
   * 检查任务状态
   */
  private async checkTaskStatus(task: MediaTask): Promise<boolean> {
    const chatDatabase = getGlobalChatDatabase()
    if (!chatDatabase) return false

    try {
      // 从数据库获取最新附件状态
      const attachments = await chatDatabase.getAttachmentsByMessage(task.messageId)
      const mediaAttachment = attachments.find(att => att.type === task.mediaType)

      if (!mediaAttachment) {
        console.warn(`⚠️ [MediaManager] 未找到附件: ${task.taskId}`)
        return false
      }

      // 解析元数据获取进度
      let metadata = {}
      try {
        metadata = JSON.parse(mediaAttachment.metadata || '{}')
      } catch (e) {
        console.warn('⚠️ [MediaManager] 元数据解析失败')
      }

      task.lastCheckTime = Date.now()

      // 检查是否完成
      if (mediaAttachment.status === 'completed' && mediaAttachment.originalUrl) {
        console.log(`✅ [MediaManager] 任务完成: ${task.taskId}`)

        // 下载并本地化媒体文件
        await this.localizeMediaFile(task, mediaAttachment.originalUrl)

        // 任务完成，停止监控
        this.stopMonitoring(task.messageId, task.mediaType)
        return true
      }

      // 检查是否失败
      if (mediaAttachment.status === 'failed') {
        console.log(`❌ [MediaManager] 任务失败: ${task.taskId}`)
        this.stopMonitoring(task.messageId, task.mediaType)
        return true
      }

      // 检查是否超时
      const duration = Date.now() - task.startTime
      if (duration > this.POLLING_INTERVALS.MAX_DURATION) {
        console.log(`⏰ [MediaManager] 任务超时: ${task.taskId}`)
        this.stopMonitoring(task.messageId, task.mediaType)
        return true
      }

      // 任务仍在进行中
      task.status = mediaAttachment.status as any
      console.log(`📊 [MediaManager] 任务进行中: ${task.taskId}, 状态: ${task.status}`)
      return false
    } catch (error) {
      console.error(`❌ [MediaManager] 检查任务状态失败: ${task.taskId}`, error)
      return false
    }
  }

  /**
   * 本地化媒体文件
   */
  private async localizeMediaFile(task: MediaTask, remoteUrl: string) {
    const chatDatabase = getGlobalChatDatabase()
    if (!chatDatabase) return

    try {
      let localPath: string | null = null

      if (task.mediaType === 'image') {
        const imageStorage = ImageStorage.getInstance()

        // 检查是否已有本地文件
        const existingPath = await imageStorage.findImageByMessageId(task.messageId)
        if (existingPath && (await imageStorage.fileExists(existingPath))) {
          localPath = existingPath
        } else {
          // 下载并保存
          localPath = await imageStorage.saveImageFromUrl(remoteUrl, task.messageId, task.taskId)
        }

        // 更新数据库附件记录的本地路径
        const attachments = await chatDatabase.getAttachmentsByMessage(task.messageId)
        const imageAttachment = attachments.find(att => att.type === 'image')

        if (imageAttachment) {
          await chatDatabase.updateAttachment(imageAttachment.id, {
            localPath,
            status: 'completed'
          })
          console.log(`💾 [MediaManager] 图片已本地化: ${localPath}`)
        }
      } else if (task.mediaType === 'audio') {
        const audioStorage = AudioStorage.getInstance()

        // 检查是否已有本地文件
        const existingPath = await audioStorage.findAudioByMessageId(task.messageId)
        if (existingPath && (await audioStorage.fileExists(existingPath))) {
          localPath = existingPath
        } else {
          // 下载并保存
          localPath = await audioStorage.saveAudioFromUrl(remoteUrl, task.messageId)
        }

        // 更新数据库附件记录的本地路径
        const attachments = await chatDatabase.getAttachmentsByMessage(task.messageId)
        const audioAttachment = attachments.find(att => att.type === 'audio')

        if (audioAttachment) {
          await chatDatabase.updateAttachment(audioAttachment.id, {
            localPath,
            status: 'completed'
          })
          console.log(`💾 [MediaManager] 音频已本地化: ${localPath}`)
        }
      }
    } catch (error) {
      console.error(`❌ [MediaManager] 本地化媒体文件失败: ${task.taskId}`, error)
    }
  }

  /**
   * 调整单个任务的轮询策略
   */
  private adjustPollingStrategy(task: MediaTask) {
    const shouldPoll = this.shouldPollTask(task)
    const interval = this.pollingIntervals.get(task.taskId)

    if (shouldPoll && !interval) {
      // 开始轮询
      const polling = setInterval(async () => {
        const completed = await this.checkTaskStatus(task)
        if (completed) {
          clearInterval(polling)
          this.pollingIntervals.delete(task.taskId)
        }
      }, this.POLLING_INTERVALS.ACTIVE_CHAT)

      this.pollingIntervals.set(task.taskId, polling)
      console.log(`▶️ [MediaManager] 开始轮询: ${task.taskId}`)
    } else if (!shouldPoll && interval) {
      // 停止轮询
      clearInterval(interval)
      this.pollingIntervals.delete(task.taskId)
      console.log(`⏸️ [MediaManager] 停止轮询: ${task.taskId}`)
    }
  }

  /**
   * 调整所有任务的轮询策略
   */
  private adjustAllPollingStrategies() {
    for (const task of this.activeTasks.values()) {
      this.adjustPollingStrategy(task)
    }
  }

  /**
   * 判断任务是否应该轮询
   */
  private shouldPollTask(task: MediaTask): boolean {
    // 只有在当前聊天且页面可见时才轮询
    return task.chatId === this.currentChatId && this.isPageVisible
  }

  /**
   * 获取任务状态（供UI组件查询）
   */
  getTaskStatus(messageId: string, mediaType: 'image' | 'video' | 'audio'): MediaTask | null {
    const taskId = `${mediaType}_${messageId}`
    return this.activeTasks.get(taskId) || null
  }

  /**
   * 清理所有任务（页面卸载时调用）
   */
  cleanup() {
    // 清理所有轮询
    for (const interval of this.pollingIntervals.values()) {
      clearInterval(interval)
    }

    this.pollingIntervals.clear()
    this.activeTasks.clear()
    console.log(`🧹 [MediaManager] 已清理所有任务`)
  }
}

// 导出单例实例
export const mediaGenerationManager = MediaGenerationManager.getInstance()

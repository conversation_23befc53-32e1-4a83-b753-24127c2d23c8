import { useState, useEffect, useRef } from 'react'
import { PlayerState } from '../types'
import { unlockAudio, createSafePlayFunction } from '../utils/audioUtils'

interface UseAudioPlayerProps {
  audioSrc?: string // 音频文件路径
  onTimeUpdate?: (currentTime: number) => void // 时间更新回调
  onEnded?: () => void // 播放结束回调
}

interface UseAudioPlayerReturn {
  audioRef: React.RefObject<HTMLAudioElement>
  currentTime: number
  duration: number
  playerState: PlayerState
  play: () => void
  pause: () => void
  seek: (time: number) => void
  setVolume: (volume: number) => void
  setCurrentTime: (time: number) => void
}

/**
 * 音频播放器Hook
 */
export const useAudioPlayer = ({
  audioSrc,
  onTimeUpdate,
  onEnded
}: UseAudioPlayerProps): UseAudioPlayerReturn => {
  const audioRef = useRef<HTMLAudioElement>(null)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(0)
  const [playerState, setPlayerState] = useState<PlayerState>(PlayerState.IDLE)
  const [audioUnlocked, setAudioUnlocked] = useState(false)

  // 尝试解锁音频
  useEffect(() => {
    // 监听用户交互以解锁音频
    const handleUserInteraction = async () => {
      if (!audioUnlocked) {
        const unlocked = await unlockAudio()
        setAudioUnlocked(unlocked)
        document.removeEventListener('click', handleUserInteraction)
        document.removeEventListener('touchstart', handleUserInteraction)
      }
    }

    document.addEventListener('click', handleUserInteraction)
    document.addEventListener('touchstart', handleUserInteraction)

    return () => {
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('touchstart', handleUserInteraction)
    }
  }, [audioUnlocked])

  // 初始化音频元素
  useEffect(() => {
    if (!audioSrc || !audioRef.current) return

    // 加载音频
    try {
      audioRef.current.src = audioSrc
      audioRef.current.preload = 'auto'
      audioRef.current.volume = 1.0
      audioRef.current.muted = false
      audioRef.current.setAttribute('playsinline', 'true')
      audioRef.current.setAttribute('webkit-playsinline', 'true')
      audioRef.current.load()
      setPlayerState(PlayerState.LOADING)

      // 尝试预加载
      const preloadAudio = () => {
        if (audioRef.current && audioRef.current.readyState < 3) {
          const loadPromise = new Promise<void>(resolve => {
            audioRef.current?.addEventListener('canplaythrough', () => resolve(), { once: true })
          })

          // 如果10秒内没有加载完成，继续执行
          Promise.race([loadPromise, new Promise<void>(resolve => setTimeout(resolve, 10000))])
        }
      }

      preloadAudio()
    } catch (e) {
      console.error('音频初始化失败:', e)
      setPlayerState(PlayerState.ERROR)
    }

    // 音频加载失败时的错误处理
    const handleLoadError = (e: Event) => {
      console.error('音频加载失败:', e)
      setPlayerState(PlayerState.ERROR)
    }

    audioRef.current.addEventListener('error', handleLoadError)

    return () => {
      if (audioRef.current) {
        audioRef.current.removeEventListener('error', handleLoadError)
        audioRef.current.pause()
        audioRef.current.src = ''
      }
    }
  }, [audioSrc])

  // 事件监听
  useEffect(() => {
    const audio = audioRef.current
    if (!audio) return

    const handleTimeUpdate = () => {
      const newTime = audio.currentTime
      setCurrentTime(newTime)
      onTimeUpdate?.(newTime)
    }

    const handleLoadedMetadata = () => {
      setDuration(audio.duration)
      setPlayerState(PlayerState.PAUSED)
    }

    const handlePlay = () => setPlayerState(PlayerState.PLAYING)
    const handlePause = () => setPlayerState(PlayerState.PAUSED)
    const handleEnded = () => {
      setPlayerState(PlayerState.PAUSED)
      onEnded?.()
    }
    const handleError = () => setPlayerState(PlayerState.ERROR)

    // 添加事件监听器
    audio.addEventListener('timeupdate', handleTimeUpdate)
    audio.addEventListener('loadedmetadata', handleLoadedMetadata)
    audio.addEventListener('play', handlePlay)
    audio.addEventListener('pause', handlePause)
    audio.addEventListener('ended', handleEnded)
    audio.addEventListener('error', handleError)

    // 清理函数
    return () => {
      audio.removeEventListener('timeupdate', handleTimeUpdate)
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata)
      audio.removeEventListener('play', handlePlay)
      audio.removeEventListener('pause', handlePause)
      audio.removeEventListener('ended', handleEnded)
      audio.removeEventListener('error', handleError)
    }
  }, [onTimeUpdate, onEnded])

  // 使用安全播放函数
  const safePlay = createSafePlayFunction(
    audioRef.current,
    () => setPlayerState(PlayerState.PLAYING),
    err => {
      console.error('安全播放失败:', err)
      setPlayerState(PlayerState.ERROR)

      // 通知UI播放被阻止
      const event = new CustomEvent('playbackBlocked')
      document.dispatchEvent(event)
    }
  )

  // 播放控制方法
  const play = () => {
    if (audioRef.current) {
      if (!audioRef.current.src || audioRef.current.src === window.location.href) {
        console.error('音频源无效，尝试重新设置')
        if (audioSrc) {
          // 保存当前时间点，因为load()会重置currentTime
          const savedTime = audioRef.current.currentTime
          audioRef.current.src = audioSrc
          audioRef.current.load()

          // 重新设置时间点
          if (savedTime > 0) {
            setTimeout(() => {
              if (audioRef.current) {
                audioRef.current.currentTime = savedTime
              }
            }, 100)
          }
        } else {
          console.error('没有可用的音频源')
          return
        }
      }

      // 确保音量设置正确
      audioRef.current.volume = 1.0
      audioRef.current.muted = false

      // 使用安全播放函数
      safePlay()
    } else {
      console.error('音频元素引用丢失')
    }
  }

  const pause = () => {
    if (audioRef.current) {
      audioRef.current.pause()
    }
  }

  const seek = (time: number) => {
    if (audioRef.current) {
      // 设置新的时间点
      const safeTime = Math.max(0, Math.min(time, duration || Number.MAX_VALUE))
      audioRef.current.currentTime = safeTime

      // 立即更新状态中的当前时间
      setCurrentTime(safeTime)

      // 将时间点保存到会话存储中，以便在必要时恢复
      if (typeof window !== 'undefined') {
        try {
          window.sessionStorage.setItem('last_audio_time', safeTime.toString())
        } catch (e) {
          console.warn('无法保存音频时间点到会话存储:', e)
        }
      }

      // 主动发送一个时间更新事件，确保UI能立即反应
      const timeUpdateEvent = new CustomEvent('manualTimeUpdate', {
        detail: { time: safeTime }
      })
      document.dispatchEvent(timeUpdateEvent)
    }
  }

  const setVolume = (volume: number) => {
    if (audioRef.current) {
      audioRef.current.volume = Math.max(0, Math.min(volume, 1))
    }
  }

  return {
    audioRef,
    currentTime,
    duration,
    playerState,
    play,
    pause,
    seek,
    setVolume,
    setCurrentTime
  }
}

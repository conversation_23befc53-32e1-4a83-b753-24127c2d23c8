import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { permissionMiddleware, PermissionType } from '@/middleware/permission'
import type { Env } from '@/types/env'
import { z } from 'zod'
import { generateUUID } from '@/lib/utils'
import type { ImageGenerationTask, ImageGenerationResponse } from '@/types/image'
import { getMessageById, updateMessageAttachments } from '@/lib/db/queries/chat'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { createMediaGeneration } from '@/lib/db/queries/media-generation'
import { getCachedDbUserId } from '@/lib/cache/cache-utils'

const imageGenerationV2 = new Hono<{ Bindings: Env }>()

// 注释：积分消费现在通过 consumeImageGenerationPoints 从数据库获取

// 图片生成请求验证 schema
const generateImageSchema = z.object({
  messageId: z.string().min(1),
  chatId: z.string().min(1),
  prompt: z.string().min(1).max(2000),
  characterAvatar: z.string().url().optional(),
  characterId: z.string().optional(), // 添加 characterId 参数
  metadata: z
    .object({
      width: z.number().min(256).max(2048).optional().default(1024),
      height: z.number().min(256).max(2048).optional().default(1440)
    })
    .optional()
})

/**
 * POST /api/image-generation-v2/generate
 * 图片生成主接口 - 发送任务到队列
 */
imageGenerationV2.post(
  '/generate',
  languageMiddleware,
  authMiddleware,
  permissionMiddleware(PermissionType.IMAGE_GENERATION),
  zValidator('json', generateImageSchema),
  async c => {
    try {
      const supabaseUser = c.get('user')
      const env = c.env
      const t = c.get('t')
      const { messageId, chatId, prompt, characterAvatar, characterId, metadata } =
        c.req.valid('json')

      // 验证参数
      if (!messageId || messageId.trim().length === 0) {
        return c.json({ success: false, message: t('message_id_required') }, 400)
      }
      if (!chatId || chatId.trim().length === 0) {
        return c.json({ success: false, message: t('chat_id_required') }, 400)
      }
      if (!prompt || prompt.trim().length === 0) {
        return c.json({ success: false, message: t('prompt_required') }, 400)
      }
      if (prompt.length > 2000) {
        return c.json({ success: false, message: t('prompt_too_long') }, 400)
      }

      if (!supabaseUser) {
        return c.json({ success: false, message: t('user_info_not_found') }, 401)
      }

      // 获取数据库用户ID（带缓存）
      const dbUserId = await getCachedDbUserId(env, supabaseUser.id)
      if (!dbUserId) {
        return c.json({ success: false, message: t('user_not_exist') }, 404)
      }

      console.log('🎨 接收图片生成请求:', {
        messageId,
        chatId,
        promptLength: prompt.length,
        hasCharacterAvatar: !!characterAvatar,
        userId: dbUserId
      })

      // 创建积分管理器
      const pointsManager = createServicePointsManager(env)

      const generationId = generateUUID()

      // 扣除积分（使用数据库配置的固定积分消费）
      const pointsResult = await pointsManager.consumeImageGenerationPoints(dbUserId, {
        imageCount: 1,
        generationId,
        customDescription: `图片生成 - 提示词: ${prompt.substring(0, 50)}...`
      })

      // 获取实际消费的积分数量
      const totalPoints = pointsResult.pointsConsumed || 0

      if (!pointsResult.success) {
        return c.json(
          {
            success: false,
            error: pointsResult.error,
            errorCode: 'INSUFFICIENT_POINTS',
            data: {
              requiredPoints: totalPoints,
              remainingPoints: pointsResult.remainingPoints || 0
            }
          },
          400
        )
      }

      console.log('✅ 积分扣除成功:', {
        pointsConsumed: pointsResult.pointsConsumed,
        remainingPoints: pointsResult.remainingPoints
      })

      // 验证环境变量
      if (!env.INSA3D_API_ENDPOINT || !env.INSA3D_API_TOKEN) {
        return c.json({ success: false, message: t('image_service_config_incomplete') }, 500)
      }

      // 验证消息是否存在且属于用户
      const messageRecords = await getMessageById(env, { id: messageId })

      if (!messageRecords.length) {
        return c.json({ success: false, message: t('message_not_exist') }, 404)
      }

      const messageRecord = messageRecords[0]
      if (messageRecord.chatId !== chatId) {
        return c.json({ success: false, message: t('message_chat_mismatch') }, 400)
      }

      // 使用生成ID作为任务ID
      const taskId = generationId

      // 创建媒体生成记录
      const mediaGenerationRecord = await createMediaGeneration(env, {
        userId: dbUserId,
        characterId, // 直接使用前端传递的 characterId
        chatId,
        messageId,
        mediaType: 'image',
        generationType: 'multimodal_chat',
        prompt,
        inputImageUrl: characterAvatar,
        pointsUsed: totalPoints,
        metadata: {
          taskId,
          insa3dEndpoint: env.INSA3D_API_ENDPOINT,
          ...metadata
        }
      })

      console.log('✅ 创建媒体生成记录:', mediaGenerationRecord.id)

      // 创建初始的生成状态附件
      await createInitialGeneratingAttachment(env, messageId, t)

      // 创建队列任务
      const task: ImageGenerationTask = {
        taskId,
        messageId,
        chatId,
        prompt,
        characterAvatar,
        userId: dbUserId,
        timestamp: Date.now(),
        metadata: {
          ...metadata,
          mediaGenerationId: mediaGenerationRecord.id,
          pointsUsed: totalPoints
        }
      }

      // 发送任务到图片生成队列
      try {
        await env.IMAGE_GENERATION_QUEUE.send(task)
        console.log('✅ 图片生成任务已发送到队列:', taskId)
      } catch (queueError) {
        console.error('❌ 发送图片生成任务到队列失败:', queueError)

        // 队列发送失败，退还积分
        try {
          await refundPointsForFailedGeneration(env, dbUserId, totalPoints, generationId)
          console.log('✅ 积分已退还:', totalPoints)
        } catch (refundError) {
          console.error('❌ 积分退还失败:', refundError)
        }

        // 清理初始附件
        await cleanupGeneratingAttachment(env, messageId)
        return c.json(
          {
            success: false,
            message: t('queue_send_failed'),
            data: {
              pointsRefunded: totalPoints
            }
          },
          500
        )
      }

      // 立即返回成功响应，包含积分信息
      const response: ImageGenerationResponse = {
        success: true,
        taskId,
        message: `${t('image_generation_task_started')}，${t('generation_task_points_consumed', {
          points: totalPoints.toString()
        })}，请等待完成`
      }

      return c.json({
        ...response,
        data: {
          taskId,
          pointsConsumed: pointsResult.pointsConsumed || totalPoints,
          remainingPoints: pointsResult.remainingPoints || 0,
          generation: {
            prompt,
            characterAvatar,
            metadata: metadata || {
              width: 720,
              height: 1280,
              steps: 30,
              cfg: 7
            },
            createdAt: new Date().toISOString()
          }
        }
      })
    } catch (error) {
      console.error('图片生成 API 错误:', error)
      const t = c.get('t') // 获取翻译函数

      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : t('generation_failed'),
          message: t('service_internal_error')
        },
        500
      )
    }
  }
)

/**
 * GET /api/image-generation-v2/status/:taskId
 * 查询图片生成任务状态
 */
imageGenerationV2.get('/status/:taskId', languageMiddleware, authMiddleware, async c => {
  try {
    const taskId = c.req.param('taskId')
    const t = c.get('t')

    if (!taskId) {
      return c.json({ success: false, message: t('task_id_required') }, 400)
    }

    // 这里可以实现任务状态查询逻辑
    // 由于我们使用 Realtime 监听，前端通常不需要主动查询状态
    // 但保留这个接口以备需要

    return c.json({
      success: true,
      data: {
        taskId,
        status: 'processing',
        message: t('realtime_status_message')
      }
    })
  } catch (error) {
    console.error('查询图片生成状态错误:', error)
    const t = c.get('t')
    return c.json({ success: false, message: t('query_status_failed') }, 500)
  }
})

/**
 * GET /api/image-generation-v2/health
 * 健康检查接口
 */
imageGenerationV2.get('/health', languageMiddleware, async c => {
  try {
    const env = c.env
    const t = c.get('t')

    // 检查必要的环境变量
    const requiredEnvVars = ['INSA3D_API_ENDPOINT', 'INSA3D_API_TOKEN']
    const missingVars = requiredEnvVars.filter(varName => !env[varName as keyof Env])

    if (missingVars.length > 0) {
      return c.json(
        {
          success: false,
          data: {
            service: 'image-generation-v2',
            status: 'unhealthy',
            error: `${t('missing_env_vars')}: ${missingVars.join(', ')}`
          }
        },
        500
      )
    }

    // 检查队列绑定
    const hasImageQueue = !!env.IMAGE_GENERATION_QUEUE

    return c.json({
      success: true,
      data: {
        service: 'image-generation-v2',
        status: 'healthy',
        insa3dApi: {
          endpoint: env.INSA3D_API_ENDPOINT,
          configured: true
        },
        queue: {
          bound: hasImageQueue,
          name: 'IMAGE_GENERATION_QUEUE'
        },
        timestamp: new Date().toISOString()
      }
    })
  } catch (error) {
    const t = c.get('t')
    console.error('图片生成健康检查失败:', error)
    return c.json(
      {
        success: false,
        message: t('health_check_failed'),
        error: error instanceof Error ? error.message : String(error)
      },
      500
    )
  }
})

/**
 * 创建初始的生成状态附件
 */
async function createInitialGeneratingAttachment(
  env: Env,
  messageId: string,
  t: (key: string, params?: Record<string, string>) => string
): Promise<void> {
  try {
    // 查询现有附件
    const currentMessages = await getMessageById(env, { id: messageId })

    if (!currentMessages.length) {
      console.warn('⚠️ 消息不存在，无法创建初始附件:', messageId)
      return
    }

    const currentMessage = currentMessages[0]

    // 解析现有附件
    let existingAttachments = []
    if (currentMessage.attachments) {
      existingAttachments = Array.isArray(currentMessage.attachments)
        ? currentMessage.attachments
        : JSON.parse(currentMessage.attachments as string)
    }

    // 移除之前的生成状态附件（如果有）
    const filteredAttachments = existingAttachments.filter(
      (att: any) => !att.contentType?.startsWith('image/generating')
    )

    // 添加初始状态附件
    const statusAttachment = {
      url: 'generating://pending',
      name: t('preparing_image_generation'),
      contentType: 'image/generating',
      metadata: {
        status: 'pending',
        progress: 5,
        timestamp: new Date().toISOString()
      }
    }

    const updatedAttachments = [...filteredAttachments, statusAttachment]

    // 更新数据库
    await updateMessageAttachments(env, { messageId, attachments: updatedAttachments })
  } catch (error) {
    console.warn('⚠️ 创建初始生成状态附件失败:', error)
  }
}

/**
 * 清理生成状态附件（发生错误时）
 */
async function cleanupGeneratingAttachment(env: Env, messageId: string): Promise<void> {
  try {
    // 查询现有附件
    const currentMessages = await getMessageById(env, { id: messageId })

    if (!currentMessages.length) {
      return
    }

    const currentMessage = currentMessages[0]

    // 解析现有附件
    let existingAttachments = []
    if (currentMessage.attachments) {
      existingAttachments = Array.isArray(currentMessage.attachments)
        ? currentMessage.attachments
        : JSON.parse(currentMessage.attachments as string)
    }

    // 移除生成状态附件
    const filteredAttachments = existingAttachments.filter(
      (att: any) => !att.contentType?.startsWith('image/generating')
    )

    // 更新数据库
    await updateMessageAttachments(env, { messageId, attachments: filteredAttachments })
  } catch (error) {
    console.warn('⚠️ 清理生成状态附件失败:', error)
  }
}

/**
 * 退还积分（生成失败时）
 */
async function refundPointsForFailedGeneration(
  env: Env,
  userId: string,
  points: number,
  generationId: string
): Promise<void> {
  try {
    const pointsManager = createServicePointsManager(env)

    // 执行积分退还
    const refundResult = await pointsManager.refundPoints(userId, {
      amount: points,
      source: 'refund',
      sourceId: generationId,
      description: `图片生成失败退还 - 生成ID: ${generationId}`
    })

    if (refundResult.success) {
      console.log(`✅ 为用户 ${userId} 退还 ${points} 积分，生成ID: ${generationId}`)
    } else {
      console.error(`❌ 积分退还失败: ${refundResult.error}`)
      throw new Error(refundResult.error || '积分退还失败')
    }
  } catch (error) {
    console.error('积分退还异常:', error)
    throw error
  }
}

export default imageGenerationV2

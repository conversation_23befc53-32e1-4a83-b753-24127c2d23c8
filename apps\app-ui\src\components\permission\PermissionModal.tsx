import { <PERSON><PERSON>, Card, CardBody } from '@heroui/react'
import { Crown, Coins, Zap, AlertTriangle, Users } from 'lucide-react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import type { PermissionResult } from '@/hooks/use-permission-guard'
import GradientModal from '@/components/common/gradient-modal'

interface PermissionModalProps {
  isOpen: boolean
  onClose: () => void
  permission: PermissionResult
  featureName: string
  onUpgrade?: () => void
  onPurchasePoints?: () => void
}

export function PermissionModal({
  isOpen,
  onClose,
  permission,
  featureName,
  onUpgrade,
  onPurchasePoints
}: PermissionModalProps) {
  const navigate = useNavigate()
  const { t } = useTranslation('permission')

  // 根据权限结果确定提示类型
  const getModalContent = () => {
    if (!permission.reason) {
      return {
        icon: <AlertTriangle className="w-12 h-12 text-warning" />,
        title: t('error.title'),
        description: t('error.description'),
        type: 'error' as const
      }
    }

    // 角色创建限制
    if (
      featureName === t('features.create_character') ||
      permission.reason.includes('角色创建') ||
      permission.reason.includes('角色数量') ||
      permission.reason.includes('角色创建上限')
    ) {
      return {
        icon: <Users className="w-12 h-12 text-secondary" />,
        title: t('character_limit.title'),
        description: permission.reason,
        type: 'character_limit' as const
      }
    }

    // 积分不足
    if (permission.reason.includes('积分不足') || permission.reason.includes('积分余额不足')) {
      return {
        icon: <Coins className="w-12 h-12 text-primary" />,
        title: t('points.title'),
        description: t('points.description', {
          feature: featureName,
          required: permission.pointsRequired,
          available: permission.pointsAvailable
        }),
        type: 'points' as const
      }
    }

    // 需要会员权限
    if (permission.reason.includes('需要会员') || permission.reason.includes('会员专享')) {
      return {
        icon: <Crown className="w-12 h-12 text-warning" />,
        title: t('membership.title'),
        description: t('membership.description', { feature: featureName }),
        type: 'membership' as const
      }
    }

    // 使用次数限制
    if (permission.reason.includes('使用次数') || permission.reason.includes('达到限制')) {
      return {
        icon: <Zap className="w-12 h-12 text-secondary" />,
        title: t('limit.title'),
        description: permission.reason,
        type: 'limit' as const
      }
    }

    // 通用权限不足
    return {
      icon: <AlertTriangle className="w-12 h-12 text-danger" />,
      title: t('general.title'),
      description: permission.reason,
      type: 'general' as const
    }
  }

  const content = getModalContent()

  const handleUpgrade = () => {
    onClose()
    if (onUpgrade) {
      onUpgrade()
    } else {
      navigate('/membership')
    }
  }

  const handlePurchasePoints = () => {
    onClose()
    if (onPurchasePoints) {
      onPurchasePoints()
    } else {
      navigate('/points-store')
    }
  }

  // 根据类型确定按钮配置
  const getButtonConfig = () => {
    if (content.type === 'points') {
      return {
        cancelText: t('buttons.cancel'),
        confirmText: t('buttons.purchase_points'),
        onConfirm: handlePurchasePoints
      }
    }

    if (
      content.type === 'membership' ||
      content.type === 'limit' ||
      content.type === 'character_limit'
    ) {
      return {
        cancelText: t('buttons.cancel'),
        confirmText:
          content.type === 'character_limit' && permission.isMember
            ? t('buttons.upgrade_plan')
            : t('buttons.upgrade_membership'),
        onConfirm: handleUpgrade
      }
    }

    if (content.type === 'general') {
      return {
        cancelText: t('buttons.cancel'),
        confirmText: t('buttons.learn_more'),
        onConfirm: handleUpgrade
      }
    }

    // 错误类型只显示关闭按钮
    return {
      cancelText: t('buttons.close'),
      confirmText: undefined,
      onConfirm: undefined
    }
  }

  const buttonConfig = getButtonConfig()

  return (
    <GradientModal
      isOpen={isOpen}
      onClose={onClose}
      title={content.title}
      cancelText={buttonConfig.cancelText}
      confirmText={buttonConfig.confirmText}
      onCancel={onClose}
      onConfirm={buttonConfig.onConfirm}
      showFooter={true}
    >
      <div className="space-y-4">
        {/* 图标和描述 */}
        <div className="flex flex-col items-center gap-4">
          <div className="flex items-center justify-center">{content.icon}</div>
          <p className="text-center text-white/80 text-sm">{content.description}</p>
        </div>

        {/* 根据类型显示不同的信息卡片 */}
        {content.type === 'character_limit' && permission.characterLimitInfo && (
          <Card className="bg-white/10 border-white/20 backdrop-blur-sm">
            <CardBody className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5 text-blue-300" />
                  <span className="text-sm text-white/80">{t('info.created_characters')}</span>
                </div>
                <span className="font-semibold text-white">
                  {permission.characterLimitInfo.current}
                </span>
              </div>
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-blue-300" />
                  <span className="text-sm text-white/80">{t('info.max_limit')}</span>
                </div>
                <span className="font-semibold text-white">
                  {permission.characterLimitInfo.max === -1
                    ? t('info.unlimited')
                    : permission.characterLimitInfo.max}
                </span>
              </div>
            </CardBody>
          </Card>
        )}

        {content.type === 'points' && (
          <Card className="bg-white/10 border-white/20 backdrop-blur-sm">
            <CardBody className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Coins className="w-5 h-5 text-yellow-300" />
                  <span className="text-sm text-white/80">{t('info.current_points')}</span>
                </div>
                <span className="font-semibold text-white">{permission.pointsAvailable || 0}</span>
              </div>
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center gap-2">
                  <Zap className="w-5 h-5 text-yellow-300" />
                  <span className="text-sm text-white/80">{t('info.required_points')}</span>
                </div>
                <span className="font-semibold text-white">{permission.pointsRequired || 0}</span>
              </div>
            </CardBody>
          </Card>
        )}

        {content.type === 'membership' && (
          <Card className="bg-white/10 border-white/20 backdrop-blur-sm">
            <CardBody className="p-4">
              <div className="flex items-center gap-2 mb-2">
                <Crown className="w-5 h-5 text-orange-300" />
                <span className="text-sm text-white/80">{t('info.current_status')}</span>
              </div>
              <p className="text-white font-medium">
                {permission.isMember ? t('info.member_user') : t('info.regular_user')}
              </p>
            </CardBody>
          </Card>
        )}
      </div>
    </GradientModal>
  )
}

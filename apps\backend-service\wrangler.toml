name = "pleasurehub-backend-service"
main = "src/index.ts"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

# 环境变量 (敏感信息通过 wrangler secret 设置)
[vars]
NODE_ENV = "development"
WORKER_ENV = "dev"

# 支付配置 - 非敏感信息
ALIPAY_GATEWAY = "https://openapi-sandbox.dl.alipaydev.com/gateway.do"  # 沙箱环境
PAY_WEB_DOMAIN = "http://***********:3001"  # 开发环境支付页面域名
USE_REAL_PAYMENT = "true"  # 启用真实支付服务
R2_BUCKET_NAME = "pleasurehub"
R2_PUBLIC_BASE_URL = "https://assets.pleasurehub.app"

# AI 绑定
[ai]
binding = "AI"

# KV 命名空间 - 需要填写实际的命名空间 ID
[[kv_namespaces]]
binding = "CACHE"
id = "ed2eb463294e4ab8bde19a52a5783177"                    # 🔧 填写: pnpm wrangler kv namespace create "CACHE" 返回的 ID
preview_id = "9e6f414fd94f4fa7b8e694dc9c68d22b"    # 🔧 填写: pnpm wrangler kv namespace create "CACHE" --preview 返回的 ID

# R2 存储桶绑定
[[r2_buckets]]
binding = "BUCKET"
bucket_name = "pleasurehub"  # 使用已有的bucket
preview_bucket_name = "pleasurehub"  # 开发环境也使用同一个bucket

# Queue 绑定 - 用于后台任务处理
[[queues.producers]]
binding = "AUDIO_PROCESSING_QUEUE"
queue = "audio-processing-queue"

[[queues.producers]]
binding = "IMAGE_GENERATION_QUEUE"
queue = "image-generation-queue"

[[queues.producers]]
binding = "VIDEO_GENERATION_QUEUE"
queue = "video-generation-queue"

[[queues.producers]]
binding = "PHOTO_ALBUM_GENERATION_QUEUE"
queue = "photo-album-generation-queue"

[[queues.producers]]
binding = "FACE_SWAP_QUEUE"
queue = "face-swap-queue"

[[queues.consumers]]
queue = "audio-processing-queue"
max_batch_size = 1
max_batch_timeout = 1  # 1秒超时，快速处理

[[queues.consumers]]
queue = "image-generation-queue"
max_batch_size = 5
max_batch_timeout = 3  # 3秒超时，快速处理

[[queues.consumers]]
queue = "video-generation-queue"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，视频生成较慢

[[queues.consumers]]
queue = "photo-album-generation-queue"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，写真集生成需要更多处理时间

[[queues.consumers]]
queue = "face-swap-queue"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，换脸处理需要更多处理时间

# 开发环境配置
[env.dev]
name = "pleasurehub-backend-service-dev"
vars = { NODE_ENV = "development", WORKER_ENV = "dev", ALIPAY_GATEWAY = "https://openapi-sandbox.dl.alipaydev.com/gateway.do", PAY_WEB_DOMAIN = "http://***********:3001", USE_REAL_PAYMENT = "true", R2_BUCKET_NAME = "pleasurehub", R2_PUBLIC_BASE_URL = "https://assets.pleasurehub.app" }

[env.dev.ai]
binding = "AI"

# 开发环境需要显式配置资源绑定
[[env.dev.kv_namespaces]]
binding = "CACHE"
id = "ed2eb463294e4ab8bde19a52a5783177"
preview_id = "9e6f414fd94f4fa7b8e694dc9c68d22b"

[[env.dev.r2_buckets]]
binding = "BUCKET"
bucket_name = "pleasurehub"
preview_bucket_name = "pleasurehub"

[[env.dev.queues.producers]]
binding = "AUDIO_PROCESSING_QUEUE"
queue = "audio-processing-queue-dev"

[[env.dev.queues.producers]]
binding = "IMAGE_GENERATION_QUEUE"
queue = "image-generation-queue-dev"

[[env.dev.queues.producers]]
binding = "VIDEO_GENERATION_QUEUE"
queue = "video-generation-queue-dev"

[[env.dev.queues.producers]]
binding = "PHOTO_ALBUM_GENERATION_QUEUE"
queue = "photo-album-generation-queue-dev"

[[env.dev.queues.producers]]
binding = "FACE_SWAP_QUEUE"
queue = "face-swap-queue-dev"

[[env.dev.queues.consumers]]
queue = "audio-processing-queue-dev"
max_batch_size = 1
max_batch_timeout = 1  # 1秒超时，快速处理

[[env.dev.queues.consumers]]
queue = "image-generation-queue-dev"
max_batch_size = 5
max_batch_timeout = 3  # 3秒超时，快速处理

[[env.dev.queues.consumers]]
queue = "video-generation-queue-dev"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，视频生成较慢

[[env.dev.queues.consumers]]
queue = "photo-album-generation-queue-dev"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，写真集生成需要更多处理时间

[[env.dev.queues.consumers]]
queue = "face-swap-queue-dev"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，换脸处理需要更多处理时间

# Cron 触发器配置 - 积分周期管理
[triggers]
crons = ["0 2 * * *"]  # 每天凌晨2点执行积分周期检查

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true

# Global 环境配置
[env.global]
name = "pleasurehub-backend-service-global"
vars = { NODE_ENV = "production", WORKER_ENV = "global", ALIPAY_GATEWAY = "https://openapi.alipay.com/gateway.do", PAY_WEB_DOMAIN = "https://pay.pleasurehub.app", USE_REAL_PAYMENT = "true", R2_BUCKET_NAME = "pleasurehub-global", R2_PUBLIC_BASE_URL = "https://assets-global.pleasurehub.app" }

[env.global.ai]
binding = "AI"

# Global 环境需要显式配置资源绑定
[[env.global.kv_namespaces]]
binding = "CACHE"
id = "552318634b604bd09ab298facf4df8ad"
preview_id = "bbf08372b45942fda5a341365c625b9b"

[[env.global.r2_buckets]]
binding = "BUCKET"
bucket_name = "pleasurehub"
preview_bucket_name = "pleasurehub"

[[env.global.queues.producers]]
binding = "AUDIO_PROCESSING_QUEUE"
queue = "audio-processing-queue-global"

[[env.global.queues.producers]]
binding = "IMAGE_GENERATION_QUEUE"
queue = "image-generation-queue-global"

[[env.global.queues.producers]]
binding = "VIDEO_GENERATION_QUEUE"
queue = "video-generation-queue-global"

[[env.global.queues.producers]]
binding = "PHOTO_ALBUM_GENERATION_QUEUE"
queue = "photo-album-generation-queue-global"

[[env.global.queues.producers]]
binding = "FACE_SWAP_QUEUE"
queue = "face-swap-queue-global"

[[env.global.queues.consumers]]
queue = "audio-processing-queue-global"
max_batch_size = 1
max_batch_timeout = 1  # 1秒超时，快速处理

[[env.global.queues.consumers]]
queue = "image-generation-queue-global"
max_batch_size = 5
max_batch_timeout = 3  # 3秒超时，快速处理

[[env.global.queues.consumers]]
queue = "video-generation-queue-global"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，视频生成较慢

[[env.global.queues.consumers]]
queue = "photo-album-generation-queue-global"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，写真集生成需要更多处理时间

[[env.global.queues.consumers]]
queue = "face-swap-queue-global"
max_batch_size = 3
max_batch_timeout = 5  # 5秒超时，换脸处理需要更多处理时间

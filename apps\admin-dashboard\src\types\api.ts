// API 响应的基础类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

// 用户相关类型
export interface User {
  id: string
  email: string
  phone?: string
  gender?: string
  nickname?: string
  avatar?: string
  isEmailVerified: boolean
  hasActiveMembership?: boolean
  membershipStatus?: string
  membershipEndDate?: string
  pointsBalance?: number
  createdAt: string
  updatedAt: string
}

export interface UserProfile {
  id: string
  userId: string
  nickname?: string
  gender?: string
  avatar?: string
  createdAt: string
  updatedAt: string
}

// 会员相关类型
export interface MembershipPlan {
  id: string
  name: string
  description?: string
  price: number
  durationDays: number
  pointsIncluded: number
  isActive: boolean
  features?: {
    maxCharacters?: number
    canCreatePublicCharacters?: boolean
    canUseCustomVoices?: boolean
    canAccessPremiumTemplates?: boolean
  }
  createdAt: string
  updatedAt: string
}

export interface UserSubscription {
  id: string
  userId: string
  planId: string
  status: 'active' | 'expired' | 'cancelled' | 'pending'
  startDate: string
  endDate: string
  autoRenew?: boolean
  paymentId?: string
  createdAt: string
  // 扩展字段（从后端关联查询获取）
  userEmail?: string
  planName?: string
  planPrice?: string | number
  planDuration?: number
  planPoints?: number
}

// 订单相关类型
export interface PaymentOrder {
  id: string
  userId: string
  type: 'membership' | 'points'
  amount: number
  status: 'pending' | 'completed' | 'failed' | 'cancelled'
  paymentMethod: 'alipay' | 'wechat'
  tradeNo?: string
  createdAt: string
  updatedAt: string
}

// 积分相关类型
export interface PointsPackage {
  id: string
  name: string
  description?: string
  price: number
  points: number
  bonusPoints: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface UserPoints {
  id: string
  userId: string
  balance: number
  cycleStartDate: string
  cycleEndDate: string
  createdAt: string
}

// 角色相关类型
export interface Character {
  id: string
  name: string
  description?: string
  avatar?: string
  isPublic: boolean
  isSystem: boolean
  createdBy: string
  createdAt: string
}

// 聊天相关类型
export interface Chat {
  id: string
  userId: string
  characterId: string
  title?: string
  background?: string
  createdAt: string
  updatedAt: string
}

export interface Message {
  id: string
  chatId: string
  content: string
  role: 'user' | 'assistant'
  attachments?: any[]
  createdAt: string
}

// 营销相关类型
export interface InviteCode {
  id: string
  code: string
  userId: string
  maxUses?: number
  usedCount: number
  expiresAt?: string
  isActive: boolean
  createdAt: string
}

export interface CommissionAccount {
  id: string
  userId: string
  balance: number
  totalEarned: number
  totalWithdrawn: number
  createdAt: string
}

export interface WithdrawRequest {
  id: string
  userId: string
  amount: number
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  bankInfo?: any
  createdAt: string
  reviewedAt?: string
  completedAt?: string
}

// 设备相关类型  
export interface Device {
  id: string
  name: string
  type: string
  description?: string
  isActive: boolean
  createdAt: string
}

// 剧本相关类型
export interface Script {
  id: string
  title: string
  description?: string
  content: any
  isActive: boolean
  createdAt: string
}
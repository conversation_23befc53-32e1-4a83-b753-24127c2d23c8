/**
 * 背景图本地存储工具
 * 使用 Capacitor Filesystem 存储背景图片
 */

import { Filesystem, Directory } from '@capacitor/filesystem'
import { Capacitor } from '@capacitor/core'

const BACKGROUND_DIR = 'chat_backgrounds'

export interface BackgroundImage {
  url: string
  path: string
  timestamp: number
}

export class BackgroundStorage {
  private static instance: BackgroundStorage

  static getInstance(): BackgroundStorage {
    if (!BackgroundStorage.instance) {
      BackgroundStorage.instance = new BackgroundStorage()
    }
    return BackgroundStorage.instance
  }

  private constructor() {}

  /**
   * 初始化背景图存储目录
   */
  async initialize(): Promise<void> {
    try {
      await Filesystem.mkdir({
        path: BACKGROUND_DIR,
        directory: Directory.Data,
        recursive: true
      })
      console.log('📁 [BackgroundStorage] 背景图目录初始化完成')
    } catch (error) {
      console.warn('⚠️ [BackgroundStorage] 目录可能已存在:', error)
    }
  }

  /**
   * 保存背景图并返回文件路径
   */
  async saveBackground(imageBlob: Blob, sessionId: string): Promise<string> {
    try {
      const timestamp = Date.now()
      const filename = `bg_${sessionId}_${timestamp}.jpg`
      const path = `${BACKGROUND_DIR}/${filename}`

      // 确保目录存在
      await this.ensureDirectoryExists()

      // 将 Blob 转换为 base64
      const base64Data = await this.blobToBase64(imageBlob)

      // 保存文件
      await Filesystem.writeFile({
        path,
        data: base64Data,
        directory: Directory.Data
      })

      console.log('💾 [BackgroundStorage] 背景图已保存:', path)
      return path
    } catch (error) {
      console.error('❌ [BackgroundStorage] 保存背景图失败:', error)
      throw error
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(): Promise<void> {
    try {
      await Filesystem.mkdir({
        path: BACKGROUND_DIR,
        directory: Directory.Data,
        recursive: true
      })
    } catch (error) {
      // 目录可能已存在，忽略错误
      console.log('📁 [BackgroundStorage] 目录检查:', error)
    }
  }

  /**
   * 获取背景图的可用URL
   */
  async getBackgroundUrl(path: string): Promise<string | null> {
    try {
      console.log('🖼️ [Background-DB] 尝试使用本地文件:', path)

      // 在移动设备上，需要读取文件并转换为data URL
      if (Capacitor.isNativePlatform()) {
        try {
          const result = await Filesystem.readFile({
            path,
            directory: Directory.Data
          })

          // 检查返回的数据格式
          if (typeof result.data === 'string') {
            const dataUrl = `data:image/jpeg;base64,${result.data}`
            console.log('✅ [BackgroundStorage] 成功转换为data URL，大小:', dataUrl.length, 'bytes')
            return dataUrl
          } else {
            console.error('❌ [BackgroundStorage] 文件读取返回格式错误')
            return null
          }
        } catch (readError) {
          console.error('❌ [BackgroundStorage] 文件读取失败:', readError)
          return null
        }
      } else {
        // Web环境，尝试使用getUri
        try {
          const uriResult = await Filesystem.getUri({
            path,
            directory: Directory.Data
          })
          console.log('✅ [BackgroundStorage] 使用文件URI:', uriResult.uri)
          return uriResult.uri
        } catch (uriError) {
          console.error('❌ [BackgroundStorage] 获取URI失败:', uriError)
          return null
        }
      }
    } catch (error) {
      console.error('❌ [BackgroundStorage] 获取背景图URL失败:', error)
      return null
    }
  }

  /**
   * 检查文件是否存在
   */
  async fileExists(path: string): Promise<boolean> {
    try {
      await Filesystem.stat({
        path,
        directory: Directory.Data
      })
      return true
    } catch {
      return false
    }
  }

  /**
   * 删除背景图文件
   */
  async deleteBackground(path: string): Promise<void> {
    try {
      await Filesystem.deleteFile({
        path,
        directory: Directory.Data
      })
      console.log('🗑️ [BackgroundStorage] 背景图已删除:', path)
    } catch (error) {
      console.error('❌ [BackgroundStorage] 删除背景图失败:', error)
      throw error
    }
  }

  /**
   * 清理所有背景图文件
   */
  async clearAllBackgrounds(): Promise<void> {
    try {
      await Filesystem.rmdir({
        path: BACKGROUND_DIR,
        directory: Directory.Data,
        recursive: true
      })

      // 重新创建目录
      await this.initialize()
      console.log('🧹 [BackgroundStorage] 所有背景图已清理')
    } catch (error) {
      console.error('❌ [BackgroundStorage] 清理背景图失败:', error)
      throw error
    }
  }

  /**
   * 将 Blob 转换为 base64 字符串
   */
  private blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => {
        const result = reader.result as string
        // 移除 data URL 前缀，只保留 base64 数据
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 获取目录中的所有背景图文件
   */
  async listAllBackgrounds(): Promise<string[]> {
    try {
      const result = await Filesystem.readdir({
        path: BACKGROUND_DIR,
        directory: Directory.Data
      })

      return result.files
        .filter(file => file.name.startsWith('bg_') && file.name.endsWith('.jpg'))
        .map(file => `${BACKGROUND_DIR}/${file.name}`)
    } catch (error) {
      console.error('❌ [BackgroundStorage] 列出背景图失败:', error)
      return []
    }
  }
}

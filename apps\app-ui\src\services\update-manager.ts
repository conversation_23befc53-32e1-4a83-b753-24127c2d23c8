import { App } from '@capacitor/app'
import { Device } from '@capacitor/device'
import { Preferences } from '@capacitor/preferences'
import { LiveUpdate } from '@capawesome/capacitor-live-update'
import { apiClient } from '@/api/client'
import { apkUpdater, type ApkDownloadProgress } from './apk-updater'

// ==================== 类型定义 ====================

export interface UpdateCheckResult {
  hasApkUpdate: boolean
  hasHotfixUpdate: boolean
  apkUpdate?: {
    version: {
      id: string
      versionName: string
      versionCode: number
      fileUrl: string
      fileSize?: number
      releaseNotes?: string
    }
    policy?: {
      updateStrategy: 'force' | 'optional' | 'silent'
      rolloutPercentage: number
    }
  }
  hotfixUpdate?: {
    version: {
      id: string
      versionName: string
      versionCode: number
      fileUrl: string
      fileSize?: number
      releaseNotes?: string
    }
    policy?: {
      updateStrategy: 'force' | 'optional' | 'silent'
      rolloutPercentage: number
    }
  }
}

export interface UpdateProgress {
  type: 'apk' | 'hotfix'
  status: 'downloading' | 'installing' | 'completed' | 'failed'
  progress: number // 0-100
  message: string
}

export type UpdateEventCallback = (progress: UpdateProgress) => void

// ==================== 更新管理器类 ====================

export class UpdateManager {
  private callbacks: UpdateEventCallback[] = []
  private isChecking = false
  private deviceId?: string
  private currentVersion?: string

  constructor() {
    this.init()
  }

  private async init() {
    try {
      // 获取设备ID
      const deviceInfo = await Device.getId()
      this.deviceId = deviceInfo.identifier

      // 获取当前应用版本
      const appInfo = await App.getInfo()
      this.currentVersion = appInfo.version

      console.log('更新管理器初始化完成:', {
        deviceId: this.deviceId,
        currentVersion: this.currentVersion
      })
    } catch (error) {
      console.error('更新管理器初始化失败:', error)
    }
  }

  /**
   * 添加更新进度回调
   */
  onUpdateProgress(callback: UpdateEventCallback) {
    this.callbacks.push(callback)
    return () => {
      const index = this.callbacks.indexOf(callback)
      if (index > -1) {
        this.callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 通知更新进度
   */
  private notifyProgress(progress: UpdateProgress) {
    this.callbacks.forEach(callback => {
      try {
        callback(progress)
      } catch (error) {
        console.error('更新回调执行失败:', error)
      }
    })
  }

  /**
   * 记录更新事件到服务器
   */
  private async logUpdateEvent(
    updateType: 'apk' | 'hotfix',
    status:
      | 'started'
      | 'downloading'
      | 'downloaded'
      | 'installing'
      | 'installed'
      | 'failed'
      | 'cancelled',
    targetVersion?: string,
    errorMessage?: string
  ) {
    try {
      await apiClient.post('/app-update/log', {
        deviceId: this.deviceId,
        currentVersion: this.currentVersion,
        targetVersion,
        updateType,
        updateStatus: status,
        errorMessage,
        metadata: {
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('记录更新日志失败:', error)
    }
  }

  /**
   * 检查更新
   */
  async checkForUpdates(
    options: {
      channel?: string
      force?: boolean
    } = {}
  ): Promise<UpdateCheckResult | null> {
    if (this.isChecking && !options.force) {
      console.log('正在检查更新中，跳过重复检查')
      return null
    }

    this.isChecking = true

    try {
      const { channel = 'production' } = options

      if (!this.currentVersion || !this.deviceId) {
        throw new Error('应用信息未初始化')
      }

      console.log('开始检查更新...', {
        currentVersion: this.currentVersion,
        channel,
        deviceId: this.deviceId
      })

      const appInfo = await App.getInfo()
      const versionCode = parseInt(appInfo.build)

      // 构建查询参数
      const queryParams = new URLSearchParams({
        platform: 'android',
        currentVersion: this.currentVersion,
        versionCode: versionCode.toString(),
        channel,
        deviceId: this.deviceId
      })

      const response = await apiClient.get<{
        success: boolean
        data: UpdateCheckResult
        message?: string
      }>(`/app-update/check?${queryParams.toString()}`)

      if (!response.success) {
        throw new Error(response.message || '检查更新失败')
      }

      const result: UpdateCheckResult = response.data

      console.log('更新检查结果:', result)

      // 记录检查更新事件
      await this.logUpdateEvent('apk', 'started')

      return result
    } catch (error) {
      console.error('检查更新失败:', error)
      await this.logUpdateEvent('apk', 'failed', undefined, String(error))
      return null
    } finally {
      this.isChecking = false
    }
  }

  /**
   * 执行热更新
   */
  async performHotfixUpdate(updateInfo: UpdateCheckResult['hotfixUpdate']): Promise<boolean> {
    if (!updateInfo) {
      return false
    }

    try {
      const { version, policy } = updateInfo

      console.log('开始热更新:', version)

      this.notifyProgress({
        type: 'hotfix',
        status: 'downloading',
        progress: 0,
        message: '正在下载热更新包...'
      })

      await this.logUpdateEvent('hotfix', 'downloading', version.versionName)

      // 使用 LiveUpdate 下载并应用热更新
      const bundle = {
        bundleId: version.id,
        url: version.fileUrl,
        checksum: '' // 如果有文件哈希可以在这里使用
      }

      // 下载热更新包
      await LiveUpdate.downloadBundle(bundle)

      this.notifyProgress({
        type: 'hotfix',
        status: 'downloading',
        progress: 50,
        message: '热更新包下载完成，正在安装...'
      })

      await this.logUpdateEvent('hotfix', 'downloaded', version.versionName)

      // 应用热更新（设置为下次启动时使用的bundle）
      // 注意：LiveUpdate的API可能根据版本不同，这里使用通用的方式
      try {
        // 使用 @capawesome/capacitor-live-update 的 API
        // 实际的方法可能需要根据具体插件版本调整
        console.log('设置bundle为:', version.id)
        // 这里可能需要调用不同的方法，比如 sync() 或其他
      } catch (error) {
        console.warn('设置bundle失败，继续使用下载的bundle:', error)
      }

      this.notifyProgress({
        type: 'hotfix',
        status: 'installing',
        progress: 80,
        message: '正在应用热更新...'
      })

      await this.logUpdateEvent('hotfix', 'installing', version.versionName)

      // 如果是强制更新，立即重新加载应用
      if (policy?.updateStrategy === 'force') {
        await LiveUpdate.reload()
      }

      this.notifyProgress({
        type: 'hotfix',
        status: 'completed',
        progress: 100,
        message: '热更新完成'
      })

      await this.logUpdateEvent('hotfix', 'installed', version.versionName)

      // 保存更新成功的记录
      await Preferences.set({
        key: 'last_hotfix_version',
        value: version.versionName
      })

      return true
    } catch (error) {
      console.error('热更新失败:', error)

      this.notifyProgress({
        type: 'hotfix',
        status: 'failed',
        progress: 0,
        message: `热更新失败: ${error}`
      })

      await this.logUpdateEvent('hotfix', 'failed', updateInfo.version.versionName, String(error))

      return false
    }
  }

  /**
   * 执行APK更新（下载并提示安装）
   */
  async performApkUpdate(updateInfo: UpdateCheckResult['apkUpdate']): Promise<boolean> {
    if (!updateInfo) {
      return false
    }

    try {
      const { version } = updateInfo

      console.log('开始APK更新:', version)

      this.notifyProgress({
        type: 'apk',
        status: 'downloading',
        progress: 0,
        message: '正在下载APK文件...'
      })

      await this.logUpdateEvent('apk', 'downloading', version.versionName)

      // 这里需要实现APK下载逻辑
      // 由于Capacitor的限制，我们需要使用自定义的下载逻辑
      const success = await this.downloadAndInstallApk(version.fileUrl, version.versionName)

      if (success) {
        this.notifyProgress({
          type: 'apk',
          status: 'completed',
          progress: 100,
          message: 'APK下载完成，请安装'
        })

        await this.logUpdateEvent('apk', 'downloaded', version.versionName)

        // 保存更新成功的记录
        await Preferences.set({
          key: 'last_apk_version',
          value: version.versionName
        })

        return true
      } else {
        throw new Error('APK下载失败')
      }
    } catch (error) {
      console.error('APK更新失败:', error)

      this.notifyProgress({
        type: 'apk',
        status: 'failed',
        progress: 0,
        message: `APK更新失败: ${error}`
      })

      await this.logUpdateEvent('apk', 'failed', updateInfo.version.versionName, String(error))

      return false
    }
  }

  /**
   * 下载并安装APK（使用原生下载和安装）
   */
  private async downloadAndInstallApk(url: string, version: string): Promise<boolean> {
    try {
      console.log('开始下载APK:', { url, version })

      // 生成文件名
      const fileName = `app_${version}_${Date.now()}.apk`

      // 下载APK文件
      const downloadResult = await apkUpdater.downloadApk(
        url,
        fileName,
        (progress: ApkDownloadProgress) => {
          // 报告下载进度
          this.notifyProgress({
            type: 'apk',
            status: 'downloading',
            progress: progress.percentage,
            message: `下载中... ${progress.percentage}% (${Math.round(
              progress.loaded / 1024 / 1024
            )}MB/${Math.round(progress.total / 1024 / 1024)}MB)`
          })
        }
      )

      if (!downloadResult.success) {
        throw new Error(downloadResult.error || '下载失败')
      }

      console.log('APK下载完成:', downloadResult.filePath)

      this.notifyProgress({
        type: 'apk',
        status: 'installing',
        progress: 95,
        message: '准备安装APK...'
      })

      // 验证文件完整性
      if (downloadResult.filePath) {
        const isValid = await apkUpdater.verifyApkFile(downloadResult.filePath)
        if (!isValid) {
          throw new Error('APK文件校验失败')
        }

        // 安装APK
        await this.logUpdateEvent('apk', 'installing', version)

        const installResult = await apkUpdater.installApk(downloadResult.filePath)

        if (!installResult.success) {
          throw new Error(installResult.error || '安装失败')
        }

        console.log('APK安装启动成功')

        this.notifyProgress({
          type: 'apk',
          status: 'completed',
          progress: 100,
          message: 'APK下载完成，请按照系统提示安装'
        })

        await this.logUpdateEvent('apk', 'installed', version)

        // 清理临时文件（延迟执行）
        setTimeout(() => {
          apkUpdater.cleanupDownloadedFiles()
        }, 30000) // 30秒后清理

        return true
      }

      throw new Error('下载文件路径为空')
    } catch (error) {
      console.error('APK下载和安装失败:', error)

      // 清理可能的临时文件
      setTimeout(() => {
        apkUpdater.cleanupDownloadedFiles()
      }, 5000)

      throw error
    }
  }

  /**
   * 检查是否应该显示更新提示
   */
  shouldShowUpdatePrompt(updateResult: UpdateCheckResult): boolean {
    // 如果有强制更新，一定要显示
    if (
      updateResult.apkUpdate?.policy?.updateStrategy === 'force' ||
      updateResult.hotfixUpdate?.policy?.updateStrategy === 'force'
    ) {
      return true
    }

    // 如果有可选更新，也显示
    if (updateResult.hasApkUpdate || updateResult.hasHotfixUpdate) {
      return true
    }

    return false
  }

  /**
   * 检查是否为强制更新
   */
  isForcedUpdate(updateResult: UpdateCheckResult): boolean {
    return (
      updateResult.apkUpdate?.policy?.updateStrategy === 'force' ||
      updateResult.hotfixUpdate?.policy?.updateStrategy === 'force'
    )
  }

  /**
   * 获取更新优先级（APK > 热更新）
   */
  getUpdatePriority(updateResult: UpdateCheckResult): 'apk' | 'hotfix' | null {
    if (updateResult.hasApkUpdate) {
      return 'apk'
    }
    if (updateResult.hasHotfixUpdate) {
      return 'hotfix'
    }
    return null
  }

  /**
   * 启动时自动检查更新
   */
  async checkOnStartup() {
    try {
      // 检查上次检查时间，避免频繁检查
      const lastCheck = await Preferences.get({ key: 'last_update_check' })
      const now = Date.now()
      const oneHour = 60 * 60 * 1000

      if (lastCheck.value && now - parseInt(lastCheck.value) < oneHour) {
        console.log('跳过更新检查，距离上次检查不足1小时')
        return null
      }

      const result = await this.checkForUpdates()

      // 记录检查时间
      await Preferences.set({
        key: 'last_update_check',
        value: now.toString()
      })

      return result
    } catch (error) {
      console.error('启动时检查更新失败:', error)
      return null
    }
  }

  /**
   * 重置LiveUpdate到基础版本
   */
  async resetToBaseVersion(): Promise<boolean> {
    try {
      await LiveUpdate.reset()
      console.log('已重置到基础版本')
      return true
    } catch (error) {
      console.error('重置到基础版本失败:', error)
      return false
    }
  }

  /**
   * 获取当前bundle信息
   */
  async getCurrentBundleInfo() {
    try {
      const bundle = await LiveUpdate.getCurrentBundle()
      return bundle
    } catch (error) {
      console.error('获取当前bundle信息失败:', error)
      return null
    }
  }
}

// 单例导出
export const updateManager = new UpdateManager()

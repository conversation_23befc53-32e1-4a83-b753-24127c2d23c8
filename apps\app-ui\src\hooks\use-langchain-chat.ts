import { useState, useCallback, useEffect, useRef } from 'react'
import { generateUUID } from '@/lib/utils'
import { apiClient } from '@/api/client'
import { useAuth } from '@/contexts/auth-context'
import { type UserProfile } from '@/api/services/profile'
import { type CharacterData } from '@/api/services/characters'
import { useRoleStore } from '@/stores/role-store'
import { useUserProfileStore } from '@/stores/user-profile-store'

// 导入聊天同步管理器
import { defaultChatSyncManager } from '@/lib/chat-database'
import type { Message as LangChainMessage } from '@/api/services'
interface ChatRequestOptions {
  headers?: Record<string, string>
  body?: any
  voiceText?: string // 语音识别的文本
}

type ChatStatus = 'idle' | 'streaming' | 'submitted' | 'error'

interface UseLangChainChatOptions {
  id: string
  initialMessages: Array<LangChainMessage>
  api: string // API 端点路径，如 '/api/chatv2/stream'
  credentials?: string
  experimental_throttle?: number
  sendExtraMessageFields?: boolean
  generateId?: () => string
  experimental_prepareRequestBody?: (body: any) => any
  onError?: (error: any) => void
  onFinish?: () => void
  enableLocalStorage?: boolean // 是否启用本地存储同步
  onMessagesUpdate?: (messages: LangChainMessage[]) => void // 消息更新回调
}

export function useLangChainChat(options: UseLangChainChatOptions) {
  const [messages, setMessages] = useState<LangChainMessage[]>(options.initialMessages || [])
  const [input, setInput] = useState('')
  const [status, setStatus] = useState<ChatStatus>('idle')
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null)
  const [characterInfo, setCharacterInfo] = useState<CharacterData | null>(null)

  // 添加助手消息同步状态跟踪
  const [assistantMessageSynced, setAssistantMessageSynced] = useState(false)

  // 用于防抖同步的ref
  const lastMessagesLength = useRef(0)
  const lastMessageId = useRef<string | null>(null)

  // 获取真实的用户信息
  const { user, session } = useAuth()

  // 获取当前角色信息
  const { currentRole } = useRoleStore()

  // 获取用户资料缓存
  const { getUserProfile, fetchUserProfile } = useUserProfileStore()

  // 获取用户资料信息
  useEffect(() => {
    const loadUserProfile = async () => {
      if (user && session) {
        try {
          // 优先从缓存获取
          const cachedProfile = getUserProfile()

          if (cachedProfile) {
            console.log('💬 [LangChain] 从缓存获取用户资料:', cachedProfile.nickname)
            setUserProfile(cachedProfile)
          } else {
            // 缓存中没有数据，从服务器获取
            console.log('💬 [LangChain] 缓存中无用户资料，从服务器获取')
            const profile = await fetchUserProfile()
            if (profile) {
              setUserProfile(profile)
            }
          }
        } catch (error) {
          console.error('获取用户资料失败:', error)
        }
      }
    }

    loadUserProfile()
  }, [user, session, getUserProfile, fetchUserProfile])

  // 🚀 获取完整的角色信息
  useEffect(() => {
    setCharacterInfo(currentRole || null)
  }, [currentRole])

  // 🚀 稳定的消息更新回调
  const stableOnMessagesUpdate = useCallback((messages: LangChainMessage[]) => {
    options.onMessagesUpdate?.(messages)
  }, []) // 空依赖数组，使用最新的闭包值

  // 🔧 手动同步消息的函数
  const syncMessagesToDatabase = useCallback(
    async (messagesToSync: LangChainMessage[], reason: string) => {
      if (!options.enableLocalStorage || !currentRole || messagesToSync.length === 0) {
        return
      }

      // 转换消息格式以兼容数据库类型
      const dbMessages: LangChainMessage[] = messagesToSync.map(msg => ({
        ...msg,
        parts: msg.parts || [{ type: 'text', text: msg.content }],
        attachments: msg.attachments || []
      }))

      console.log(`🔄 [useLangChainChat] ${reason}，同步消息数量:`, messagesToSync.length)

      try {
        const result = await defaultChatSyncManager.syncMessages(
          options.id,
          dbMessages,
          currentRole.id
        )

        if (result.skipped) {
          console.log('⏸️ [useLangChainChat] 同步被跳过:', result.reason)
        } else {
          console.log('✅ [useLangChainChat] 同步完成:', result)
        }

        // 同步 useLangChainChat 的状态与 ChatSyncManager
        const syncStatus = defaultChatSyncManager.getSyncStatus()
        lastMessagesLength.current = syncStatus.lastSyncedCount
        lastMessageId.current = syncStatus.lastSyncedId
      } catch (error) {
        console.error('❌ [useLangChainChat] 同步失败:', error)
      }
    },
    [options.enableLocalStorage, options.id, currentRole?.id]
  )

  // 🚀 清理同步缓存（在切换聊天或卸载时）
  useEffect(() => {
    return () => {
      if (options.enableLocalStorage && messages.length > 0 && currentRole?.id) {
        // 🔧 修复：确保退出时强制完成同步，防止消息丢失
        console.log('🔄 [useLangChainChat] 退出时强制完成同步，消息数量:', messages.length)
        defaultChatSyncManager.forceSyncNow(options.id, messages, currentRole.id)
          .then(() => {
            console.log('✅ [useLangChainChat] 退出同步完成')
          })
          .catch((error) => {
            console.error('❌ [useLangChainChat] 退出同步失败:', error)
          })
          .finally(() => {
            defaultChatSyncManager.clearCache()
            console.log('🧹 [useLangChainChat] 已清理同步缓存')
          })
      } else {
        // 如果没有消息或角色，直接清理缓存
        defaultChatSyncManager.clearCache()
        console.log('🧹 [useLangChainChat] 已清理同步缓存（无消息或角色）')
      }
    }
  }, [options.id, options.enableLocalStorage, messages, currentRole?.id])

  const handleSubmit = useCallback(
    async (
      event?: React.FormEvent | { preventDefault?: () => void },
      chatRequestOptions?: ChatRequestOptions & { deviceInfo?: string }
    ) => {
      if (event?.preventDefault) {
        event.preventDefault()
      }

      // 优先使用语音文本，如果没有则使用input
      const messageText = chatRequestOptions?.voiceText || input

      if (!messageText.trim() || status === 'streaming') return

      // 构建消息内容，如果有设备信息则添加
      let messageContent = messageText
      if (chatRequestOptions?.deviceInfo) {
        messageContent = messageText + '\n' + chatRequestOptions.deviceInfo
      }

      const userMessage: LangChainMessage = {
        id: options.generateId?.() || generateUUID(),
        role: 'user',
        content: messageContent,
        parts: [{ type: 'text', text: messageContent }],
        createdAt: new Date()
      }

      // 添加用户消息
      setMessages(prev => [...prev, userMessage])

      // 🔧 用户消息添加后手动同步
      await syncMessagesToDatabase([...messages, userMessage], '用户发送消息')

      // 如果是语音消息，不清空input（让外部组件处理），否则清空
      if (!chatRequestOptions?.voiceText) {
        setInput('')
      }

      setStatus('submitted')

      try {
        // 准备请求体 - 使用后端期望的格式
        const requestBody = options.experimental_prepareRequestBody?.({
          messages: [...messages, userMessage]
        }) || {
          chatId: options.id,
          message: userMessage,
          // 🚀 传递真实的用户信息，避免后端数据库查询
          userInfo: {
            id: user?.id || 'anonymous',
            email: user?.email || undefined,
            nickname: userProfile?.nickname || user?.email?.split('@')[0] || '用户',
            gender: userProfile?.gender || undefined
          },
          // 🚀 告诉后端聊天是否已存在
          chatExists: messages.length > 0,
          // 🚀 传递完整的角色信息，避免后端数据库查询
          characterInfo: characterInfo
            ? {
                id: characterInfo.id,
                name: characterInfo.name,
                gender: (characterInfo.gender || 'female') as 'male' | 'female' | 'other',
                age: characterInfo.age?.toString() || '',
                relationship: characterInfo.relationship || '',
                ethnicity: characterInfo.ethnicity || '',
                eyeColor: characterInfo.eyeColor || '',
                hairStyle: characterInfo.hairStyle || '',
                hairColor: characterInfo.hairColor || '',
                bodyType: characterInfo.bodyType || '',
                breastSize: characterInfo.breastSize || '',
                buttSize: characterInfo.buttSize || '',
                personality: characterInfo.personality || '',
                clothing: characterInfo.clothing || '',
                voice: characterInfo.voice || '',
                imageUrl: characterInfo.imageUrl || ''
              }
            : undefined
        }

        // 创建清理后的选项对象（移除自定义属性）
        const cleanedOptions = { ...chatRequestOptions }
        delete cleanedOptions.deviceInfo

        // 直接使用传入的端点路径
        const response = await apiClient.streamPost(options.api, requestBody, {
          headers: cleanedOptions?.headers
        })

        setStatus('streaming')

        // 创建助手消息
        const assistantMessage: LangChainMessage = {
          id: options.generateId?.() || generateUUID(),
          role: 'assistant',
          content: '',
          parts: [{ type: 'text', text: '' }],
          createdAt: new Date()
        }

        setMessages(prev => [...prev, assistantMessage])

        // 🚀 重置助手消息同步状态
        setAssistantMessageSynced(false)

        // 🚀 不要立即同步空的助手消息，等收到真实ID或有内容时再同步

        // 处理流式响应
        const reader = response.body?.getReader()
        const decoder = new TextDecoder()

        if (reader) {
          let accumulatedText = ''
          let currentMessages = [...messages, userMessage, assistantMessage]

          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            const chunk = decoder.decode(value, { stream: true })
            let processedChunk = chunk

            // 检查是否包含消息ID信息
            if (chunk.includes('"type":"message_id"')) {
              try {
                const lines = chunk.split('\n')
                let foundMessageId = false

                for (const line of lines) {
                  if (line.trim().startsWith('{') && line.includes('"type":"message_id"')) {
                    const messageInfo = JSON.parse(line.trim())
                    if (messageInfo.type === 'message_id' && messageInfo.assistantMessageId) {
                      console.log(`🔄 [useLangChainChat] 收到服务器消息ID:`, {
                        临时ID: assistantMessage.id,
                        真实ID: messageInfo.assistantMessageId,
                        已同步: assistantMessageSynced
                      })

                      // 更新助手消息的真实ID
                      setMessages(prev => {
                        const newMessages = [...prev]
                        const lastMessage = newMessages[newMessages.length - 1]
                        if (lastMessage && lastMessage.role === 'assistant') {
                          const updatedMessage = {
                            ...lastMessage,
                            id: messageInfo.assistantMessageId
                          }
                          newMessages[newMessages.length - 1] = updatedMessage
                          currentMessages = newMessages // 更新本地引用

                          console.log(`✅ [useLangChainChat] 助手消息ID已更新:`, {
                            旧ID: lastMessage.id,
                            新ID: messageInfo.assistantMessageId
                          })
                        }
                        return newMessages
                      })

                      // 🚀 收到真实ID后进行第一次同步（如果还没同步过）
                      if (!assistantMessageSynced) {
                        console.log(`🔄 [useLangChainChat] 开始首次同步助手消息`)
                        await syncMessagesToDatabase(currentMessages, '助手消息ID更新')
                        setAssistantMessageSynced(true)
                        console.log(`✅ [useLangChainChat] 助手消息首次同步完成`)
                      } else {
                        console.log(`⏭️ [useLangChainChat] 助手消息已同步过，跳过`)
                      }

                      // 从chunk中移除JSON行，保留其他内容
                      processedChunk = chunk.replace(line, '').replace(/^\n+/, '')
                      foundMessageId = true
                      break
                    }
                  }
                }

                // 如果找到了消息ID但没有其他内容，跳过这个chunk
                if (foundMessageId && !processedChunk.trim()) {
                  continue
                }
              } catch (e) {
                // 如果解析失败，使用原始chunk
                processedChunk = chunk
              }
            }

            // 累加实际内容
            if (processedChunk.trim()) {
              accumulatedText += processedChunk
            }

            // 🔧 只更新UI，不触发同步
            setMessages(prev => {
              const newMessages = [...prev]
              const lastMessage = newMessages[newMessages.length - 1]
              if (lastMessage && lastMessage.role === 'assistant') {
                // 创建新的消息对象，确保React能检测到变化
                const updatedMessage: LangChainMessage = {
                  ...lastMessage,
                  content: accumulatedText,
                  parts: [{ type: 'text' as const, text: accumulatedText }]
                }
                newMessages[newMessages.length - 1] = updatedMessage
                currentMessages = newMessages // 更新本地引用
              }

              // 🔧 调用外部回调（每次更新都调用）
              stableOnMessagesUpdate(newMessages)

              return newMessages
            })
          }

          // 🚀 流式完成后确保消息已同步（如果还没同步过）
          if (!assistantMessageSynced) {
            console.log(`🔄 [useLangChainChat] 流式完成，首次同步助手消息`)
            await syncMessagesToDatabase(currentMessages, '流式回复完成（首次同步）')
            setAssistantMessageSynced(true)
            console.log(`✅ [useLangChainChat] 流式完成首次同步完成`)
          } else {
            // 🔧 如果已经同步过，只更新最终内容
            console.log(`🔄 [useLangChainChat] 流式完成，更新最终内容`)
            await syncMessagesToDatabase(currentMessages, '流式回复完成（更新内容）')
            console.log(`✅ [useLangChainChat] 流式完成内容更新完成`)
          }
        }

        setStatus('idle')
        options.onFinish?.()
      } catch (error) {
        console.error('聊天错误:', error)
        setStatus('error')
        options.onError?.(error)
      }
    },
    [input, messages, options, user, userProfile, characterInfo, assistantMessageSynced]
  )

  const append = useCallback(
    (message: LangChainMessage, chatRequestOptions?: ChatRequestOptions) => {
      setMessages(prev => [...prev, message])
    },
    []
  )

  const stop = useCallback(() => {
    setStatus('idle')
  }, [])

  return {
    messages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    status,
    stop
  }
}

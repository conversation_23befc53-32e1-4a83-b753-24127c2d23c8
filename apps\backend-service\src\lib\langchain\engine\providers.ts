// LangChain 提供商配置

import type { LangChainEnv } from '../types'
import { getDefaultModel } from '../config'

/**
 * 提供商配置接口
 */
export interface ProviderConfig {
  apiKey: string
  baseURL: string
  modelName: string
  temperature: number
  search_parameters: { mode: string }
  frequency_penalty: number
  presence_penalty: number
  topP: number
}

/**
 * 创建 xAI 提供商配置
 */
export function createXaiProviderConfig(env: LangChainEnv): ProviderConfig {
  if (!env.XAI_API_KEY) {
    throw new Error('XAI_API_KEY is required but not found in environment variables')
  }

  return {
    apiKey: env.XAI_API_KEY,
    baseURL: 'https://openrouter.ai/api/v1',
    // baseURL: 'https://api.x.ai/v1',
    modelName: getDefaultModel(),

    // 禁用实时搜索
    search_parameters: { mode: 'off' },

    // 控制模型输出随机性
    topP: 0.85,

    // 温度
    temperature: 1,

    // 频率惩罚（控制模型语言重复性）
    frequency_penalty: 0.5,

    // 存在惩罚（控制模型探索新话题和词汇的倾向）
    presence_penalty: 0.4
  }
}

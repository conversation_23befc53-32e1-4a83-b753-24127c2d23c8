import { useState, useCallback, useRef, useEffect } from 'react'
import { apiService } from '@/api/services'

// 图片生成状态接口
interface ImageGenerationState {
  status: 'idle' | 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  imageUrl: string | null
  error: string | null
  taskId: string | null
  estimatedSteps: number
  completedSteps: number
}

// Hook 返回值接口
interface UseImageGenerationReturn {
  state: ImageGenerationState
  generateImage: (prompt: string, characterAvatar?: string | null) => Promise<void>
  reset: () => void
}

// API 响应接口
interface GenerateImageResponse {
  success: boolean
  data: {
    taskId: string
    status: string
    estimatedSteps: number
    prompt: string
    imageUrl: string | null
  }
}

interface TaskStatusResponse {
  success: boolean
  data: {
    taskId: string
    status: string
    progress: number
    estimatedSteps: number
    completedSteps: number
    imageUrl: string | null
    errorMessage?: string
  }
}

export function useImageGeneration(): UseImageGenerationReturn {
  const [state, setState] = useState<ImageGenerationState>({
    status: 'idle',
    progress: 0,
    imageUrl: null,
    error: null,
    taskId: null,
    estimatedSteps: 0,
    completedSteps: 0
  })

  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const isPollingRef = useRef(false)
  const currentPromptRef = useRef<string | null>(null) // 记录当前正在处理的prompt

  // 清理轮询
  const clearPolling = useCallback(() => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current)
      pollIntervalRef.current = null
    }
    isPollingRef.current = false
  }, [])

  // 轮询任务状态
  const pollTaskStatus = useCallback(
    async (taskId: string) => {
      if (isPollingRef.current) return // 防止重复轮询

      isPollingRef.current = true

      const poll = async () => {
        try {
          const result = await apiService.image.getImageGenerationStatus(taskId)

          if (!result.success) {
            throw new Error('API 返回失败状态')
          }

          const { data } = result

          setState(prev => ({
            ...prev,
            status: data.status as ImageGenerationState['status'],
            progress: data.progress,
            estimatedSteps: data.estimatedSteps,
            completedSteps: data.completedSteps,
            imageUrl: data.imageUrl,
            error: data.errorMessage || null
          }))

          // 如果任务完成或失败，停止轮询
          if (data.status === 'completed' || data.status === 'failed') {
            clearPolling()
            // 清除当前prompt，允许新的生成请求
            currentPromptRef.current = null
          }
        } catch (error) {
          console.error('轮询任务状态失败:', error)
          setState(prev => ({
            ...prev,
            status: 'failed',
            error: error instanceof Error ? error.message : '查询状态失败'
          }))
          clearPolling()
        }
      }

      // 立即执行一次
      await poll()

      // 如果任务还在进行中，设置定时轮询
      if (isPollingRef.current) {
        pollIntervalRef.current = setInterval(poll, 2000) // 每2秒轮询一次
      }
    },
    [clearPolling]
  )

  // 生成图片
  const generateImage = useCallback(
    async (prompt: string, characterAvatar?: string | null) => {
      try {
        // 防止重复生成相同的prompt
        const promptKey = `${prompt}-${characterAvatar || 'no-avatar'}`
        if (currentPromptRef.current === promptKey) {
          console.log('图片已经在生成中，跳过重复请求:', prompt)
          return
        }

        // 如果当前状态不是idle，也跳过
        if (state.status !== 'idle') {
          console.log('当前状态不是idle，跳过生成请求:', state.status)
          return
        }

        // 标记为当前处理的prompt
        currentPromptRef.current = promptKey
        console.log('开始生成图片，prompt:', prompt)

        // 重置状态
        setState({
          status: 'pending',
          progress: 0,
          imageUrl: null,
          error: null,
          taskId: null,
          estimatedSteps: 0,
          completedSteps: 0
        })

        // 调用生成API
        const result = await apiService.image.generateImageV2({
          prompt,
          imageUrl: characterAvatar || undefined // 使用角色头像作为参考图片
        })

        if (!result.success) {
          throw new Error('API 返回失败状态')
        }

        const { data } = result

        setState(prev => ({
          ...prev,
          status: 'processing',
          taskId: data.taskId,
          estimatedSteps: data.estimatedSteps
        }))

        // 开始轮询任务状态
        await pollTaskStatus(data.taskId)
      } catch (error) {
        console.error('生成图片失败:', error)
        // 失败时清除当前prompt，允许重试
        currentPromptRef.current = null

        setState(prev => ({
          ...prev,
          status: 'failed',
          error: error instanceof Error ? error.message : '生成图片失败'
        }))
      }
    },
    [pollTaskStatus, state.status]
  ) // 添加state.status依赖

  // 重置状态
  const reset = useCallback(() => {
    clearPolling()
    // 清空当前prompt记录，允许重新生成
    currentPromptRef.current = null
    setState({
      status: 'idle',
      progress: 0,
      imageUrl: null,
      error: null,
      taskId: null,
      estimatedSteps: 0,
      completedSteps: 0
    })
  }, [clearPolling])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      clearPolling()
    }
  }, [clearPolling])

  return {
    state,
    generateImage,
    reset
  }
}

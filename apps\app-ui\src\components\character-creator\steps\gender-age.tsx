import React, { useEffect } from 'react'
import { RadioGroup, Radio, Chip, Card, CardBody, Image } from '@heroui/react'
import type { CharacterData } from '..'
import { characterMapping } from '../mapping'
import { useTranslation } from 'react-i18next'

// 性别选项
const getGenderOptions = (t: any) => [
  { value: 'female', label: t('customRole:gender_age.female') },
  { value: 'male', label: t('customRole:gender_age.male') }
]

// 年龄选项
const getAgeOptions = () => Object.entries(characterMapping.age).map(([value, label]) => ({
  value,
  label: label.split(' ')[0],
  description: label.includes(' ') ? label.split(' ')[1] : ''
}))

interface GenderAgeProps {
  data: CharacterData
  onUpdate: (data: Partial<CharacterData>) => void
}

// 对应每个section的组件
const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-6">
    <h3 className="font-medium text-lg text-center mb-4 text-primary">{title}</h3>
    {children}
  </div>
)

export default function GenderAge({ data, onUpdate }: GenderAgeProps) {
  const { t } = useTranslation(['customRole'])
  const genderOptions = getGenderOptions(t)
  const ageOptions = getAgeOptions()
  
  // 直接使用gender字段，如果没有则默认为female
  const [gender, setGender] = React.useState<'male' | 'female'>(
    (data.gender as 'male' | 'female') || 'female'
  )

  // 当gender从外部更新时，同步更新内部gender状态
  useEffect(() => {
    const currentGender = (data.gender as 'male' | 'female') || 'female'
    if (gender !== currentGender) {
      setGender(currentGender)
    }
  }, [data.gender])

  // 处理性别变更 - 修复：使用正确的gender字段
  const handleGenderChange = (value: 'male' | 'female') => {
    setGender(value)

    // 更新gender字段和依赖性别的其他字段
    onUpdate({
      gender: value, // 使用正确的gender字段
      ethnicity: `${value}-caucasian`, // 设置默认人种（保持现有逻辑）
      hairStyle: '', // 重置发型
      bodyType: '', // 重置体型
      breastSize: value === 'female' ? '' : undefined, // 只有女性有胸部尺寸
      buttSize: value === 'female' ? '' : undefined, // 只有女性有臀部尺寸
      voice: '', // 重置声音选择
      voiceModelId: '' // 重置声音模型ID
    })

    // 记录到控制台便于调试
    console.log(`已将性别改为: ${value}, 设置gender为: ${value}`)
  }

  return (
    <div className="space-y-6">
      {/* 性别选择 */}
      <Section title={t('customRole:gender_age.select_gender')}>
        <RadioGroup
          value={gender}
          onValueChange={value => handleGenderChange(value as 'male' | 'female')}
          aria-label={t('customRole:gender_age.select_gender_aria')}
          classNames={{
            wrapper: 'grid grid-cols-2 gap-4'
          }}
        >
          {genderOptions.map(option => (
            <Card
              key={option.value}
              isPressable
              className={`
                cursor-pointer transition-all duration-200 border-2 overflow-hidden
                ${
                  gender === option.value
                    ? 'border-primary ring-2 ring-primary/20 bg-primary/5'
                    : 'border-transparent hover:border-primary/30'
                }
              `}
              onPress={() => handleGenderChange(option.value as 'male' | 'female')}
            >
              <CardBody className="p-0 relative">
                <Radio value={option.value} className="hidden" aria-label={t('customRole:gender_age.select_specific_gender_aria', { label: option.label })} />
                <div className="aspect-[1/1] bg-gradient-to-b from-default-100/30 to-transparent">
                  <Image
                    src={`/images/custom/${
                      option.value === 'female' ? 'female_01' : 'male_01'
                    }.png`}
                    alt={option.label}
                    className="w-full h-full object-cover"
                    classNames={{
                      wrapper: 'w-full h-full'
                    }}
                  />
                </div>
                <Chip
                  color={gender === option.value ? 'primary' : 'default'}
                  variant={gender === option.value ? 'solid' : 'flat'}
                  className="absolute top-2 right-2 z-10"
                  size="sm"
                  classNames={{
                    content: gender === option.value ? 'text-white font-medium' : 'text-foreground'
                  }}
                >
                  {option.label}
                </Chip>
              </CardBody>
            </Card>
          ))}
        </RadioGroup>
      </Section>

      {/* 年龄选择 */}
      <Section title={t('customRole:gender_age.select_age')}>
        <RadioGroup
          value={data.age || ''}
          onValueChange={value => onUpdate({ age: value })}
          aria-label={t('customRole:gender_age.select_age_aria')}
          classNames={{
            wrapper: 'grid grid-cols-4 gap-2'
          }}
        >
          {ageOptions.map(option => (
            <Card
              key={option.value}
              isPressable
              className={`
                cursor-pointer transition-all duration-200 border-2
                ${
                  data.age === option.value
                    ? 'border-primary ring-2 ring-primary/20 bg-primary/5'
                    : 'border-transparent hover:border-primary/30 hover:bg-default-100/50'
                }
              `}
              onPress={() => onUpdate({ age: option.value })}
            >
              <CardBody className="p-3">
                <Radio
                  value={option.value}
                  className="hidden"
                  aria-label={t('customRole:gender_age.select_specific_age_aria', { label: option.label })}
                />
                <div className="text-center w-full">
                  <div className="font-medium text-sm">{option.label}</div>
                  {option.description && (
                    <div className="text-xs text-default-500 mt-1">{option.description}</div>
                  )}
                </div>
              </CardBody>
            </Card>
          ))}
        </RadioGroup>
      </Section>
    </div>
  )
}

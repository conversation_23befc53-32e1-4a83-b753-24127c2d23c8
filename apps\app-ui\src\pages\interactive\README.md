角色激情互动模块

大致逻辑：用户连接了硬件，选择剧本，根据对应剧本，剧本在对应的时间有语音，对应剧本的文案，以及对应某个时间节点所触发的蓝牙指令。

以上是大致的流程。
以下是详细的说明。

1. 连接硬件
   扫描二维码或者输入设备码，得到设备码之后，从而拿到设备的对象：

```json
device = {
  // 设备图片
  "pic": "",
  // 设备名称
  "name": "",
  // 设备功能及对应模块
  "func": [
    {
      "name": "抽插",
      "key": "thrust",
      // 蓝牙指令列表
      "commands": [
        {
          // 强度
          "intensity": 1,
          // 指令
          "command": "6db643ce97fe427ce49c6c"
        },
        {
          "intensity": 2,
          "command": "6db643ce97fe427ce49c6c"
        },
        {
          "intensity": 3,
          "command": "6db643ce97fe427ce49c6c"
        },
        {
          // -1 为停止命令
          "intensity": -1,
          "command": "6db643ce97fe427ce49c6c"
        }
      ]
    },
    {
      "name": "吮吸",
      "key": "suction",
      // 蓝牙指令列表
      "commands": [
        {
          // 强度
          "intensity": 1,
          // 指令
          "command": "6db643ce97fe427ce49c6c"
        },
        {
          "intensity": 2,
          "command": "6db643ce97fe427ce49c6c"
        },
        {
          "intensity": 3,
          "command": "6db643ce97fe427ce49c6c"
        },
        {
          // -1 为停止命令
          "intensity": -1,
          "command": "6db643ce97fe427ce49c6c"
        }
      ]
    },
    ...
  ]
}
```

(这一步暂时用假数据)
最主要是：command，暂时用 6db643ce97fe427ce49c6c。

2. 得到硬件之后，根据硬件和角色，得到对应的剧本。
   剧本包含一下信息：

- 剧本封面
- 剧本简介
- 剧本音频文件（目前暂时是本地文件，后续可能是 url）
- 剧本的对话及配置 json：这个的意思整个剧本当中，有旁白和对话，这个 json 意思就是单独把对话抽出来（包含阶段 阶段标题,场景图片数组，角色，对应的时间，对话内容, 设备强度）。文件在 [components/devices/dialogue_with_multi_intensity.json]
  格式如下：

```json
[
  {
    "stage": 1,
    "stageTitle": "Escalating Desire",
    "pics": [
      {
        "name": "场景1",
        "pic": "https://cdn.pixabay.com/photo/2015/12/01/20/28/road-1072823_1280.jpg"
      },
      {
        "name": "场景2",
        "pic": "https://cdn.pixabay.com/photo/2015/12/01/20/28/road-1072823_1280.jpg"
      }
    ],
    "dialogues": [
      {
        "role": "Professor",
        "time": "00:01:08",
        "dialogue": "Who’s there?",
        "pics": [],
        "intensity": {
          "thrust": 3,
          "suction": 2
        }
      }
    ]
  }
]
```

3. 播放剧本

- 背景播放剧本音频
- 在对应时间节点展示对话的文案内容
- 在对应阶段页面背景轮播展示角色的场景图
- 在对应场景进行蓝牙广播对应的模块的蓝牙指令

  3.1
  页面需要展示阶段

  - 支持点击切换阶段
    页面需要有快进按钮，点击快进剧本的语音

    3.2
    页面需要展示设备包含的功能，用户可以手动切换功能的开关和强度
    这里用户的手动控制的优先级是比剧本当中所定义的强度是要高的。

import { apiClient } from '../client'
import type { ApiResponse } from '../client'

// ==================== 类型定义 ====================

// 激活码信息
export interface ActivationCode {
  id: string
  code: string
  type: 'membership' | 'points'
  description?: string
  membershipPlan?: {
    name: string
    durationDays: number
    pointsIncluded: number
  }
  pointsPackage?: {
    name: string
    points: number
    bonusPoints: number
  }
  isUsed: boolean
  usedAt?: string
  usedBy?: string
  expiresAt?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

// 激活码验证结果
export interface ActivationCodeValidation {
  valid: boolean
  reason?: string
  activationCode?: ActivationCode
}

// 激活码使用结果
export interface ActivationCodeUsageResult {
  success: boolean
  message: string
  data?: {
    resultType: 'membership_created' | 'membership_extended' | 'membership_upgraded' | 'points_added'
    resultId: string
    conflictResolution: 'no_conflict' | 'extended' | 'upgraded' | 'replaced' | 'queued' | 'rejected'
    activationCode: ActivationCode
    originalMembershipId?: string
  }
}

// 激活码使用历史
export interface ActivationCodeUsage {
  id: string
  activationCodeId: string
  userId: string
  resultType: string
  resultId: string
  originalMembershipId?: string
  conflictResolution: string
  metadata: Record<string, any>
  ipAddress?: string
  userAgent?: string
  usedAt: string
  activationCode?: {
    code: string
    type: string
    description?: string
    membershipPlan?: {
      name: string
    }
    pointsPackage?: {
      name: string
      points: number
    }
  }
}

// 激活码统计
export interface ActivationCodeStats {
  total: number
  used: number
  active: number
  expired: number
}

// ==================== API 服务 ====================

export const activationCodeService = {
  /**
   * 验证激活码
   */
  async validateCode(code: string): Promise<ApiResponse<ActivationCodeValidation>> {
    try {
      return await apiClient.get<ApiResponse<ActivationCodeValidation>>(
        `/api/activation-codes/validate/${encodeURIComponent(code.trim().toUpperCase())}`
      )
    } catch (error) {
      console.error('验证激活码失败:', error)
      return {
        success: false,
        data: {
          valid: false,
          reason: error instanceof Error ? error.message : '验证失败'
        }
      }
    }
  },

  /**
   * 使用激活码
   */
  async useCode(code: string): Promise<ActivationCodeUsageResult> {
    try {
      return await apiClient.post<ActivationCodeUsageResult>('/api/activation-codes/use', {
        code: code.trim().toUpperCase()
      })
    } catch (error) {
      console.error('使用激活码失败:', error)
      return {
        success: false,
        message: error instanceof Error ? error.message : '激活失败，请稍后重试'
      }
    }
  },

  /**
   * 获取用户激活历史
   */
  async getActivationHistory(
    limit: number = 20,
    offset: number = 0
  ): Promise<ApiResponse<ActivationCodeUsage[]>> {
    try {
      return await apiClient.get<ApiResponse<ActivationCodeUsage[]>>(
        `/api/activation-codes/history?limit=${limit}&offset=${offset}`
      )
    } catch (error) {
      console.error('获取激活历史失败:', error)
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : '获取历史记录失败'
      }
    }
  },

  // ==================== 管理员功能 ====================

  /**
   * 创建会员激活码
   */
  async createMembershipCode(params: {
    membershipPlanId: string
    description?: string
    expiresAt?: string
    count?: number
  }): Promise<ApiResponse<ActivationCode[]>> {
    try {
      return await apiClient.post<ApiResponse<ActivationCode[]>>(
        '/api/activation-codes/admin/membership',
        params
      )
    } catch (error) {
      console.error('创建会员激活码失败:', error)
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : '创建失败'
      }
    }
  },

  /**
   * 创建积分包激活码
   */
  async createPointsCode(params: {
    pointsPackageId: string
    description?: string
    expiresAt?: string
    count?: number
  }): Promise<ApiResponse<ActivationCode[]>> {
    try {
      return await apiClient.post<ApiResponse<ActivationCode[]>>(
        '/api/activation-codes/admin/points',
        params
      )
    } catch (error) {
      console.error('创建积分包激活码失败:', error)
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : '创建失败'
      }
    }
  },

  /**
   * 获取激活码列表（管理员）
   */
  async getActivationCodes(params: {
    type?: 'membership' | 'points'
    isUsed?: boolean
    isActive?: boolean
    batchId?: string
    limit?: number
    offset?: number
  } = {}): Promise<ApiResponse<ActivationCode[]>> {
    try {
      const queryParams = new URLSearchParams()
      
      if (params.type) queryParams.append('type', params.type)
      if (params.isUsed !== undefined) queryParams.append('isUsed', params.isUsed.toString())
      if (params.isActive !== undefined) queryParams.append('isActive', params.isActive.toString())
      if (params.batchId) queryParams.append('batchId', params.batchId)
      if (params.limit) queryParams.append('limit', params.limit.toString())
      if (params.offset) queryParams.append('offset', params.offset.toString())

      const queryString = queryParams.toString()
      const endpoint = `/api/activation-codes/admin/list${queryString ? `?${queryString}` : ''}`

      return await apiClient.get<ApiResponse<ActivationCode[]>>(endpoint)
    } catch (error) {
      console.error('获取激活码列表失败:', error)
      return {
        success: false,
        data: [],
        error: error instanceof Error ? error.message : '获取列表失败'
      }
    }
  },

  /**
   * 获取激活码统计（管理员）
   */
  async getActivationStats(params: {
    type?: 'membership' | 'points'
    batchId?: string
    dateFrom?: string
    dateTo?: string
  } = {}): Promise<ApiResponse<ActivationCodeStats>> {
    try {
      const queryParams = new URLSearchParams()
      
      if (params.type) queryParams.append('type', params.type)
      if (params.batchId) queryParams.append('batchId', params.batchId)
      if (params.dateFrom) queryParams.append('dateFrom', params.dateFrom)
      if (params.dateTo) queryParams.append('dateTo', params.dateTo)

      const queryString = queryParams.toString()
      const endpoint = `/api/activation-codes/admin/stats${queryString ? `?${queryString}` : ''}`

      return await apiClient.get<ApiResponse<ActivationCodeStats>>(endpoint)
    } catch (error) {
      console.error('获取激活码统计失败:', error)
      return {
        success: false,
        data: { total: 0, used: 0, active: 0, expired: 0 },
        error: error instanceof Error ? error.message : '获取统计失败'
      }
    }
  },

  /**
   * 禁用激活码（管理员）
   */
  async disableCode(codeId: string): Promise<ApiResponse<{ message: string }>> {
    try {
      return await apiClient.post<ApiResponse<{ message: string }>>(
        `/api/activation-codes/admin/${codeId}/disable`
      )
    } catch (error) {
      console.error('禁用激活码失败:', error)
      return {
        success: false,
        data: { message: '' },
        error: error instanceof Error ? error.message : '禁用失败'
      }
    }
  },

  /**
   * 启用激活码（管理员）
   */
  async enableCode(codeId: string): Promise<ApiResponse<{ message: string }>> {
    try {
      return await apiClient.post<ApiResponse<{ message: string }>>(
        `/api/activation-codes/admin/${codeId}/enable`
      )
    } catch (error) {
      console.error('启用激活码失败:', error)
      return {
        success: false,
        data: { message: '' },
        error: error instanceof Error ? error.message : '启用失败'
      }
    }
  }
}

// 默认导出
export default activationCodeService

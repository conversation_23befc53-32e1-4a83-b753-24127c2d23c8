import { useState, useEffect, useCallback } from 'react'
import { apiService } from '@/api'
import { getGlobalChatDatabase } from '@/lib/chat-database'
import type { ChatDatabaseInterface } from '@/lib/chat-database/types'

interface SmartChatInitResult {
  chatId: string | null
  isLoading: boolean
  error: string | null
  dataSource: 'local' | 'server' | 'new' | null
  initializeChat: (
    roleId: string,
    options?: { enableBackgroundSync?: boolean }
  ) => Promise<string | null>
}

/**
 * 智能聊天初始化 Hook
 * 实现本地优先 + 服务器验证的策略
 */
export function useSmartChatInit(roleId: string | undefined): SmartChatInitResult {
  const [chatId, setChatId] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [dataSource, setDataSource] = useState<'local' | 'server' | 'new' | null>(null)

  const initializeChat = useCallback(
    async (
      roleId: string,
      options?: { enableBackgroundSync?: boolean }
    ): Promise<string | null> => {
      if (!roleId) return null

      setIsLoading(true)
      setError(null)

      try {
        const chatDatabase = getGlobalChatDatabase()

        // 🚀 真正的本地优先策略：先查本地
        console.log('📦 [SmartChatInit] 优先查询本地数据...')
        const localResult = await queryLocalLatestChat(chatDatabase, roleId)

        if (localResult) {
          // 本地有数据 - 直接使用，无需查询服务器 🎉
          console.log('✅ [SmartChatInit] 使用本地数据，无需服务器请求:', localResult.id)
          setChatId(localResult.id)
          setDataSource('local')

          // 可选的后台同步（不阻塞返回）
          if (options?.enableBackgroundSync) {
            console.log('🔄 [SmartChatInit] 开始后台同步服务器数据...')
            queryServerLatestChat(roleId)
              .then(serverResult => {
                if (serverResult && serverResult.id !== localResult.id) {
                  console.log('💡 [SmartChatInit] 后台发现服务器有不同的数据:', serverResult.id)
                  // 这里可以添加通知用户有新数据的逻辑
                }
              })
              .catch(error => {
                console.warn('⚠️ [SmartChatInit] 后台同步失败:', error)
              })
          }

          return localResult.id
        }

        // 本地没有数据，才查询服务器
        console.log('🌐 [SmartChatInit] 本地无数据，查询服务器...')
        const serverResult = await queryServerLatestChat(roleId)

        if (serverResult) {
          // 服务器有数据 - 使用服务器数据
          console.log('✅ [SmartChatInit] 使用服务器数据:', serverResult.id)
          setChatId(serverResult.id)
          setDataSource('server')
          return serverResult.id
        }

        // 本地和服务器都没有数据 - 需要创建新聊天
        console.log('✨ [SmartChatInit] 本地和服务器都无数据，需要创建新聊天')
        setChatId(null)
        setDataSource('new')
        return null
      } catch (error) {
        console.error('❌ [SmartChatInit] 初始化失败:', error)
        setError('聊天初始化失败，请稍后重试')
        setChatId(null)
        setDataSource(null)
        return null
      } finally {
        setIsLoading(false)
      }
    },
    []
  )

  return {
    chatId,
    isLoading,
    error,
    dataSource,
    initializeChat
  }
}

/**
 * 查询本地最新聊天
 */
async function queryLocalLatestChat(chatDatabase: ChatDatabaseInterface, roleId: string) {
  try {
    const latestSession = await chatDatabase.getLatestSessionByRole(roleId)

    // 🚀 过滤掉空的会话记录
    if (latestSession) {
      const isEmpty =
        latestSession.messageCount === 0 &&
        (!latestSession.title || latestSession.title.trim() === '')
      if (isEmpty) {
        console.log('🗑️ [SmartChatInit] 跳过空会话:', latestSession.id)
        return null
      }
      return { id: latestSession.id, title: latestSession.title }
    }

    return null
  } catch (error) {
    console.warn('📦 [SmartChatInit] 本地查询失败:', error)
    return null
  }
}

/**
 * 查询服务器最新聊天
 */
async function queryServerLatestChat(roleId: string) {
  try {
    const response = await apiService.history.getByRole(roleId, 1)
    if (response.chats && response.chats.length > 0) {
      const latestChat = response.chats[0]
      return { id: latestChat.id, title: latestChat.title }
    }
    return null
  } catch (error) {
    console.warn('🌐 [SmartChatInit] 服务器查询失败:', error)
    return null
  }
}

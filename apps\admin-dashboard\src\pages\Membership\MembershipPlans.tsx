import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  message,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Divider,
  Checkbox
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CrownOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { MembershipPlan } from '@/types/api'
import { membershipService } from '@/services/membership'
import { TABLE_CONFIG } from '@/constants'
import dayjs from 'dayjs'

const { Title } = Typography
const { TextArea } = Input

const MembershipPlans: React.FC = () => {
  const [plans, setPlans] = useState<MembershipPlan[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingPlan, setEditingPlan] = useState<MembershipPlan | null>(null)
  const [form] = Form.useForm()
  
  // 统计数据
  const [stats, setStats] = useState({
    totalPlans: 0,
    activePlans: 0,
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    monthlyRevenue: 0,
    todayRevenue: 0
  })

  useEffect(() => {
    loadPlans()
    loadStats()
  }, [])

  const loadPlans = async () => {
    try {
      setLoading(true)
      
      const response = await membershipService.getPlans()
      
      if (response.success && response.data) {
        setPlans(response.data)
      } else {
        message.error(response.message || '获取套餐列表失败')
      }
    } catch (error) {
      console.error('获取套餐列表失败:', error)
      message.error('获取套餐列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await membershipService.getMembershipStats()
      
      if (response.success && response.data) {
        setStats(response.data)
      } else {
        console.error('获取统计数据失败:', response.message)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const handleCreate = () => {
    setEditingPlan(null)
    form.resetFields()
    // 设置默认的功能配置
    form.setFieldsValue({
      features: {
        maxCharacters: 5, // 会员默认5个角色
        canCreatePublicCharacters: true,
        canUseCustomVoices: true,
        canAccessPremiumTemplates: true
      },
      isActive: true
    })
    setModalVisible(true)
  }

  const handleEdit = (plan: MembershipPlan) => {
    setEditingPlan(plan)
    // 设置表单值，确保features对象正确展开
    const formValues = {
      ...plan,
      features: {
        maxCharacters: 1, // 默认1个角色（免费用户水平）
        canCreatePublicCharacters: false,
        canUseCustomVoices: false,
        canAccessPremiumTemplates: false,
        ...plan.features // 覆盖现有配置
      }
    }
    form.setFieldsValue(formValues)
    setModalVisible(true)
  }

  const handleSubmit = async (values: any) => {
    try {
      if (editingPlan) {
        const response = await membershipService.updatePlan(editingPlan.id, values)
        if (response.success) {
          message.success('套餐更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await membershipService.createPlan(values)
        if (response.success) {
          message.success('套餐创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }

      setModalVisible(false)
      loadPlans()
      loadStats()
    } catch (error) {
      console.error(editingPlan ? '更新失败:' : '创建失败:', error)
      message.error(editingPlan ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await membershipService.deletePlan(id)
      if (response.success) {
        message.success('套餐删除成功')
        loadPlans()
        loadStats()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleViewDetail = (plan: MembershipPlan) => {
    Modal.info({
      title: '套餐详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div><strong>套餐名称:</strong> {plan.name}</div>
            <div><strong>价格:</strong> ¥{plan.price}</div>
            <div><strong>有效期:</strong> {plan.durationDays}天</div>
            <div><strong>包含积分:</strong> {plan.pointsIncluded}积分</div>
            <div><strong>状态:</strong> {
              plan.isActive ? 
                <Tag color="green">启用</Tag> : 
                <Tag color="red">禁用</Tag>
            }</div>
            <div><strong>描述:</strong> {plan.description || '无'}</div>
            <div><strong>创建时间:</strong> {dayjs(plan.createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
          </Space>
        </div>
      )
    })
  }

  const columns: ColumnsType<MembershipPlan> = [
    {
      title: '套餐名称',
      dataIndex: 'name',
      render: (name) => (
        <Space>
          <CrownOutlined style={{ color: '#faad14' }} />
          <span style={{ fontWeight: 500 }}>{name}</span>
        </Space>
      ),
    },
    {
      title: '价格',
      dataIndex: 'price',
      render: (price) => (
        <span style={{ color: '#f50', fontWeight: 500 }}>
          ¥{price.toFixed(2)}
        </span>
      ),
    },
    {
      title: '有效期',
      dataIndex: 'durationDays',
      render: (duration) => `${duration}天`,
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '包含积分',
      dataIndex: 'pointsIncluded',
      render: (points) => `${points}积分`,
    },
    {
      title: '角色限制',
      dataIndex: 'features',
      render: (features) => {
        const maxCharacters = features?.maxCharacters;
        if (maxCharacters === undefined || maxCharacters === null) {
          return (
            <Tag color="gray">
              未设置
            </Tag>
          );
        }
        return (
          <Tag color="blue">
            {maxCharacters}个角色
          </Tag>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个套餐吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        会员套餐管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总套餐数"
              value={stats.totalPlans}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="启用套餐"
              value={stats.activePlans}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃订阅"
              value={stats.activeSubscriptions}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="月收入"
              value={stats.monthlyRevenue}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建套餐
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={plans}
          rowKey="id"
          loading={loading}
          pagination={false}
          {...TABLE_CONFIG}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingPlan ? '编辑套餐' : '新建套餐'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="套餐名称"
            rules={[{ required: true, message: '请输入套餐名称' }]}
          >
            <Input placeholder="例如：月度会员" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price"
                label="价格（元）"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  placeholder="29.90"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="durationDays"
                label="有效期（天）"
                rules={[{ required: true, message: '请输入有效期' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  placeholder="30"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="pointsIncluded"
            label="包含积分"
            rules={[{ required: true, message: '请输入包含的积分数量' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="200"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="套餐描述"
          >
            <TextArea 
              rows={3}
              placeholder="详细描述套餐的特色和优势..."
            />
          </Form.Item>

          <Divider orientation="left">功能权限配置</Divider>

          <Form.Item
            name={['features', 'maxCharacters']}
            label="最大角色数量"
            rules={[{ required: true, message: '请设置最大角色数量' }]}
            tooltip="用户可创建的角色数量上限"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              max={50}
              placeholder="5"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['features', 'canCreatePublicCharacters']}
                valuePropName="checked"
                tooltip="是否允许创建公开角色"
              >
                <Checkbox>可创建公开角色</Checkbox>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['features', 'canUseCustomVoices']}
                valuePropName="checked"
                tooltip="是否可以使用自定义声音"
              >
                <Checkbox>可使用自定义声音</Checkbox>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['features', 'canAccessPremiumTemplates']}
                valuePropName="checked"
                tooltip="是否可以使用付费模板"
              >
                <Checkbox>可使用付费模板</Checkbox>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingPlan ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default MembershipPlans
import { apiClient } from '../client'
// 设备类型定义
export interface DeviceCommand {
  intensity: number // -1代表停止
  command: string
}

export interface DeviceFunction {
  name: string
  key: string
  commands: DeviceCommand[]
}

export interface Device {
  pic: string
  name: string
  func: DeviceFunction[]
}

// 设备连接请求参数
export interface DeviceConnectRequest {
  deviceCode: string
}

// 设备连接响应
export interface DeviceConnectResponse {
  success: boolean
  device: Device
  message: string
}

// 支持设备响应
export interface SupportedDevicesResponse {
  success: boolean
  devices: Array<
    Device & {
      deviceCode: string
      brand?: string
      model?: string
      category?: string
      description?: string
    }
  >
  message: string
}

// 设备 API 服务
export const deviceService = {
  /**
   * 根据设备码连接设备
   * @param deviceCode 设备码
   * @returns 设备信息
   */
  async connectDevice(deviceCode: string): Promise<Device> {
    try {
      const response = await apiClient.post<DeviceConnectResponse>('/api/devices/connect', {
        deviceCode
      })

      if (!response.success) {
        throw new Error(response.message || '连接设备失败')
      }

      return response.device
    } catch (error) {
      console.error('连接设备失败:', error)
      throw error
    }
  },

  /**
   * 获取系统支持的设备列表
   * @returns 支持的设备列表
   */
  async getSupportedDevices(): Promise<SupportedDevicesResponse['devices']> {
    try {
      const response = await apiClient.get<SupportedDevicesResponse>('/api/devices/supported')

      if (!response.success) {
        throw new Error(response.message || '获取设备列表失败')
      }

      return response.devices
    } catch (error) {
      console.error('获取支持设备列表失败:', error)
      throw error
    }
  }
}

import { useState, useEffect, useRef, useCallback } from 'react'
import { generateChatBackground, getChatBackground } from '@/api/services/background'
import { getGlobalChatDatabase } from '@/lib/chat-database'
import { BackgroundStorage } from '@/lib/media/background-storage'

interface UseChatBackgroundDbOptions {
  chatId: string
  roleId: string
  enabled?: boolean
  initialMessages?: any[] // 添加初始消息参数来判断是否为新聊天
}

export const useChatBackgroundDb = ({
  chatId,
  roleId,
  enabled = true,
  initialMessages = []
}: UseChatBackgroundDbOptions) => {
  const [backgroundImageUrl, setBackgroundImageUrl] = useState<string | null>(null)
  const [isBackgroundLoaded, setIsBackgroundLoaded] = useState(false)
  const [isGenerating, setIsGenerating] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [loadingProgress, setLoadingProgress] = useState<string>('')

  // 防抖相关
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null)
  const pendingSceneRef = useRef<string | null>(null)
  const generatingRef = useRef(false)
  const imageLoadTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const currentBlobUrlRef = useRef<string | null>(null)

  // 获取数据库实例
  const getChatDatabase = useCallback(() => {
    const db = getGlobalChatDatabase()
    if (!db) {
      throw new Error('聊天数据库未初始化')
    }
    return db
  }, [])

  // 获取BackgroundStorage实例
  const getBackgroundStorage = useCallback(() => {
    return BackgroundStorage.getInstance()
  }, [])

  // 判断是否为新聊天
  const isNewChat = useCallback(() => {
    // 如果没有初始消息，认为是新聊天
    return !initialMessages || initialMessages.length === 0
  }, [initialMessages])

  // 清理blob URL的函数
  const cleanupBlobUrl = useCallback(() => {
    if (currentBlobUrlRef.current && currentBlobUrlRef.current.startsWith('blob:')) {
      URL.revokeObjectURL(currentBlobUrlRef.current)
      currentBlobUrlRef.current = null
    }
  }, [])

  // 图片预加载函数
  const preloadImage = useCallback((imageUrl: string): Promise<boolean> => {
    return new Promise(resolve => {
      const img = new Image()
      let timeoutId: NodeJS.Timeout

      const cleanup = () => {
        if (timeoutId) clearTimeout(timeoutId)
        if (imageLoadTimeoutRef.current) {
          clearTimeout(imageLoadTimeoutRef.current)
          imageLoadTimeoutRef.current = null
        }
      }

      img.onload = () => {
        cleanup()
        console.log('✅ [Background-DB] 背景图加载成功:', imageUrl.substring(0, 50))
        console.log('🎨 [Background-DB] 图片尺寸:', img.naturalWidth, 'x', img.naturalHeight)
        setIsBackgroundLoaded(true)
        setLoadingProgress('')
        resolve(true)
      }

      img.onerror = () => {
        cleanup()
        console.warn('⚠️ [Background-DB] 背景图加载失败:', imageUrl.substring(0, 50))
        // 即使加载失败，也显示背景图，只是透明度较低
        setIsBackgroundLoaded(true)
        setLoadingProgress('背景图加载失败，使用默认显示')
        resolve(false)
      }

      // 设置10秒超时
      timeoutId = setTimeout(() => {
        cleanup()
        console.warn('⏰ [Background-DB] 背景图加载超时:', imageUrl.substring(0, 50))
        setIsBackgroundLoaded(true)
        setLoadingProgress('背景图加载超时')
        resolve(false)
      }, 10000)

      img.src = imageUrl
    })
  }, [])

  // 从本地路径获取可用的URL
  const getDisplayableUrl = useCallback(
    async (backgroundInfo: {
      backgroundImagePath: string
      backgroundImageUrl: string
    }): Promise<string | null> => {
      // 优先使用本地文件
      if (backgroundInfo.backgroundImagePath) {
        console.log('🔍 [Background-DB] 尝试使用本地文件:', backgroundInfo.backgroundImagePath)
        const backgroundStorage = getBackgroundStorage()
        const localUrl = await backgroundStorage.getBackgroundUrl(
          backgroundInfo.backgroundImagePath
        )
        if (localUrl) {
          // 清理旧的blob URL
          cleanupBlobUrl()
          currentBlobUrlRef.current = localUrl
          return localUrl
        } else {
          console.warn('⚠️ [Background-DB] 本地文件不可用，尝试重新下载')
        }
      }

      // 回退到服务器URL
      if (backgroundInfo.backgroundImageUrl) {
        console.log('🌐 [Background-DB] 使用服务器URL:', backgroundInfo.backgroundImageUrl)
        return backgroundInfo.backgroundImageUrl
      }

      return null
    },
    [cleanupBlobUrl, getBackgroundStorage]
  )

  // 下载图片并保存为本地文件
  const downloadAndSaveImage = useCallback(
    async (
      imageUrl: string,
      sessionId: string
    ): Promise<{
      success: boolean
      localPath?: string
      error?: string
    }> => {
      try {
        console.log(`📥 [Background-DB] 开始下载背景图: ${imageUrl}`)

        // 下载图片
        const response = await fetch(imageUrl)
        if (!response.ok) {
          throw new Error(`下载失败: ${response.status} ${response.statusText}`)
        }

        const imageBlob = await response.blob()

        // 保存到本地
        const backgroundStorage = getBackgroundStorage()
        const localPath = await backgroundStorage.saveBackground(imageBlob, sessionId)

        return {
          success: true,
          localPath
        }
      } catch (error) {
        console.error('❌ [Background-DB] 下载保存背景图失败:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : '下载保存失败'
        }
      }
    },
    [getBackgroundStorage]
  )

  // 初始化时从数据库加载背景图
  useEffect(() => {
    if (!enabled || !chatId || !roleId) return

    const loadBackgroundFromDb = async () => {
      try {
        console.log(`🔍 [Background-DB] 从数据库加载背景图，chatId: ${chatId}`)
        setLoadingProgress('检查背景图...')

        // 首先检查数据库中是否有背景图
        const chatDatabase = getChatDatabase()
        const backgroundInfo = await chatDatabase.getSessionBackground(chatId)

        if (
          backgroundInfo &&
          (backgroundInfo.backgroundImagePath || backgroundInfo.backgroundImageUrl)
        ) {
          console.log(`✅ [Background-DB] 数据库中找到背景图信息`)

          const displayUrl = await getDisplayableUrl(backgroundInfo)
          if (displayUrl) {
            setBackgroundImageUrl(displayUrl)
            setLoadingProgress('加载背景图...')
            await preloadImage(displayUrl)
            return
          }
        }

        // 新聊天判断：如果是新聊天，跳过服务器请求
        if (isNewChat()) {
          console.log(`🆕 [Background-DB] 新聊天，跳过服务器背景图检查`)
          setLoadingProgress('')
          return
        }

        // 数据库中没有，且不是新聊天，尝试从服务器获取
        console.log(`🌐 [Background-DB] 数据库中无背景图，从服务器获取`)
        const serverBackgroundInfo = await getChatBackground(chatId)

        if (serverBackgroundInfo?.backgroundImageUrl) {
          console.log(`✅ [Background-DB] 服务器找到背景图，开始下载到本地`)
          setLoadingProgress('下载背景图到本地...')

          // 下载并保存到本地
          const storageResult = await downloadAndSaveImage(
            serverBackgroundInfo.backgroundImageUrl,
            chatId
          )

          if (storageResult.success && storageResult.localPath) {
            // 保存到数据库
            await chatDatabase.updateSessionBackground(
              chatId,
              serverBackgroundInfo.backgroundImageUrl,
              storageResult.localPath,
              serverBackgroundInfo.backgroundSceneDescription || ''
            )

            // 获取可显示的URL
            const displayUrl = await getDisplayableUrl({
              backgroundImagePath: storageResult.localPath,
              backgroundImageUrl: serverBackgroundInfo.backgroundImageUrl
            })

            if (displayUrl) {
              setBackgroundImageUrl(displayUrl)
              setLoadingProgress('加载背景图...')
              await preloadImage(displayUrl)
            }
          } else {
            // 下载失败，直接使用服务器URL
            console.warn('⚠️ [Background-DB] 本地保存失败，使用服务器URL')
            setBackgroundImageUrl(serverBackgroundInfo.backgroundImageUrl)
            setLoadingProgress('加载背景图...')
            await preloadImage(serverBackgroundInfo.backgroundImageUrl)
          }
        } else {
          console.log(`📝 [Background-DB] 无背景图`)
          setLoadingProgress('')
        }
      } catch (error) {
        console.error('获取背景图失败:', error)
        setError(error instanceof Error ? error.message : '获取背景图失败')
        setLoadingProgress('')
      }
    }

    loadBackgroundFromDb()
  }, [
    chatId,
    roleId,
    enabled,
    preloadImage,
    getChatDatabase,
    getDisplayableUrl,
    downloadAndSaveImage,
    isNewChat
  ])

  // 生成背景图函数
  const executeGeneration = useCallback(
    async (sceneDescription: string) => {
      if (!enabled || !chatId || !sceneDescription || generatingRef.current) return

      try {
        // 先检查数据库中是否已有背景图
        const chatDatabase = getChatDatabase()
        const existingBackground = await chatDatabase.getSessionBackground(chatId)

        if (
          existingBackground &&
          (existingBackground.backgroundImagePath || existingBackground.backgroundImageUrl) &&
          existingBackground.backgroundSceneDescription === sceneDescription
        ) {
          console.log('✅ [Background-DB] 场景相同且已有背景图，跳过生成')
          return
        }

        setIsGenerating(true)
        setError(null)
        setLoadingProgress('正在生成背景图...')
        generatingRef.current = true

        console.log('🎨 [Background-DB] 开始生成背景图:', sceneDescription.slice(0, 50) + '...')

        const newBackgroundUrl = await generateChatBackground(chatId, sceneDescription)

        if (newBackgroundUrl) {
          setLoadingProgress('下载新背景图到本地...')

          // 下载并保存新背景图到本地
          const storageResult = await downloadAndSaveImage(newBackgroundUrl, chatId)

          if (storageResult.success && storageResult.localPath) {
            // 保存到数据库（使用已获取的实例）
            await chatDatabase.updateSessionBackground(
              chatId,
              newBackgroundUrl,
              storageResult.localPath,
              sceneDescription
            )

            // 获取可显示的URL
            const displayUrl = await getDisplayableUrl({
              backgroundImagePath: storageResult.localPath,
              backgroundImageUrl: newBackgroundUrl
            })

            if (displayUrl) {
              setBackgroundImageUrl(displayUrl)
              setLoadingProgress('加载新背景图...')
              const success = await preloadImage(displayUrl)

              if (success) {
                console.log('✅ [Background-DB] 背景图生成并加载完成')
              } else {
                console.log('⚠️ [Background-DB] 背景图生成完成但加载有问题')
              }
            }
          } else {
            // 本地保存失败，直接使用服务器URL
            console.warn('⚠️ [Background-DB] 本地保存失败，使用服务器URL')
            setBackgroundImageUrl(newBackgroundUrl)
            setLoadingProgress('加载新背景图...')
            await preloadImage(newBackgroundUrl)
          }
        }
      } catch (error) {
        console.error('❌ [Background-DB] 背景图生成失败:', error)
        setError(error instanceof Error ? error.message : '背景图生成失败')
        setLoadingProgress('')
      } finally {
        setIsGenerating(false)
        generatingRef.current = false
      }
    },
    [chatId, enabled, preloadImage, getChatDatabase, getDisplayableUrl, downloadAndSaveImage]
  )

  // 带防抖的背景图生成函数
  const generateBackground = useCallback(
    (sceneDescription: string) => {
      if (!enabled || !chatId || !sceneDescription) return

      const trimmedScene = sceneDescription.trim()
      if (trimmedScene.length < 10) return

      // 检查是否包含明显的不完整标记
      if (trimmedScene.includes('<') || trimmedScene.includes('>')) return

      pendingSceneRef.current = trimmedScene

      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }

      // 1秒防抖
      debounceTimerRef.current = setTimeout(() => {
        const finalScene = pendingSceneRef.current
        if (finalScene && finalScene === trimmedScene) {
          executeGeneration(finalScene)
        }
        debounceTimerRef.current = null
        pendingSceneRef.current = null
      }, 1000)
    },
    [executeGeneration, chatId, enabled]
  )

  // 清除背景图
  const clearBackground = useCallback(async () => {
    try {
      if (chatId) {
        const chatDatabase = getChatDatabase()

        // 获取当前背景图信息以清理本地文件
        const backgroundInfo = await chatDatabase.getSessionBackground(chatId)
        if (backgroundInfo?.backgroundImagePath) {
          const backgroundStorage = getBackgroundStorage()
          await backgroundStorage.deleteBackground(backgroundInfo.backgroundImagePath)
        }

        // 清除数据库记录
        await chatDatabase.clearSessionBackground(chatId)
      }

      // 清理blob URL
      cleanupBlobUrl()

      setBackgroundImageUrl(null)
      setIsBackgroundLoaded(false)
      setError(null)
      setLoadingProgress('')
      pendingSceneRef.current = null

      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
        debounceTimerRef.current = null
      }

      if (imageLoadTimeoutRef.current) {
        clearTimeout(imageLoadTimeoutRef.current)
        imageLoadTimeoutRef.current = null
      }
    } catch (error) {
      console.error('清除背景图失败:', error)
    }
  }, [chatId, getChatDatabase, cleanupBlobUrl, getBackgroundStorage])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current)
      }
      if (imageLoadTimeoutRef.current) {
        clearTimeout(imageLoadTimeoutRef.current)
      }
      // 清理blob URL
      cleanupBlobUrl()
    }
  }, [cleanupBlobUrl])

  return {
    backgroundImageUrl,
    isBackgroundLoaded,
    isGenerating,
    error,
    loadingProgress,
    generateBackground,
    clearBackground
  }
}

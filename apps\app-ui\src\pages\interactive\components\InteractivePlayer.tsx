import React, { useEffect, useRef } from 'react'
import { usePlayer } from '../context/PlayerContext'
import { SceneDisplay } from './SceneDisplay'
import { ControlPanel } from './ControlPanel'

interface InteractivePlayerProps {
  onBackToScriptSelect: () => void
}

/**
 * 互动播放器组件
 * 整合所有子组件
 */
export const InteractivePlayer: React.FC<InteractivePlayerProps> = ({ onBackToScriptSelect }) => {
  const {
    script,
    device,
    currentTime,
    duration,
    currentStageIndex,
    playerState,
    play,
    pause,
    seek,
    setStage
  } = usePlayer()

  // 调试信息
  useEffect(() => {
    console.log('🔍 InteractivePlayer - 设备状态:', device)
    console.log('🔍 InteractivePlayer - 设备是否存在:', !!device)
    console.log('🔍 InteractivePlayer - 设备功能:', device?.func)
  }, [device])

  // 获取当前阶段
  const currentStage =
    script.length > 0 && currentStageIndex < script.length ? script[currentStageIndex] : null

  // 快进指定秒数
  const handleForward = (seconds: number) => {
    seek(currentTime + seconds)
  }

  // 获取阶段标题列表
  const stageTitles = script.map(stage => stage.stageTitle)

  // 页面退出时的额外清理逻辑
  useEffect(() => {
    const handlePageExit = () => {
      if (device) {
        console.log('🛑 InteractivePlayer - 页面退出，停止所有设备')
        // 确保所有设备功能都停止
        device.func.forEach(func => {
          // 发送停止命令 -1
          try {
            // 这里直接调用蓝牙命令，不依赖队列
            import('../utils/bluetoothUtils').then(({ sendBluetoothCommand }) => {
              sendBluetoothCommand(func.key, -1, device.func)
            })
          } catch (error) {
            console.error('发送停止命令失败:', error)
          }
        })
      }
    }

    // 组件卸载时的清理
    return () => {
      handlePageExit()
    }
  }, [device])

  return (
    <div className="relative size-full flex flex-col bg-black h-screen" data-swipe-disabled="true">
      {/* 场景显示区域 */}
      <div className="relative size-full flex-1 overflow-hidden">
        <SceneDisplay scenePics={currentStage?.pics} />

        {/* 控制面板 - 确保在底部且z-index适当 */}
        <div className="absolute bottom-0 inset-x-0 z-30">
          <ControlPanel
            currentTime={currentTime}
            duration={duration}
            playerState={playerState}
            currentStageIndex={currentStageIndex}
            stageCount={script.length}
            stageTitles={stageTitles}
            onPlay={play}
            onPause={pause}
            onSeek={seek}
            onStageChange={setStage}
            onForward={handleForward}
            hideStageControls={true} // 隐藏阶段控制
          />
        </div>
      </div>
    </div>
  )
}

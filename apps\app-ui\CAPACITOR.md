# RC Demo App - Capacitor 移动应用配置指南

本文档介绍如何使用 Capacitor 将 RC Demo App 前端打包为 iOS 和 Android 原生应用。

## 已完成的配置

项目已经完成了以下 Capacitor 配置：

1. 安装了 Capacitor 核心库和 CLI
2. 添加了 iOS 和 Android 平台支持
3. 配置了项目构建流程，使前端代码可以打包成移动应用

## 常用命令

### 构建和同步

每次对前端代码进行更改后，需要重新构建并同步到原生应用：

```bash
# 构建前端代码
pnpm run build

# 将构建后的代码同步到原生应用
npx cap sync
```

### 打开原生项目

```bash
# 打开iOS项目
npx cap open ios

# 打开Android项目
npx cap open android
```

### 添加 Capacitor 插件

如果需要访问设备原生功能（如相机、地理位置等），需要添加相应的 Capacitor 插件：

```bash
# 安装插件（例如相机插件）
pnpm add @capacitor/camera

# 同步插件到原生平台
npx cap sync
```

## 配置文件

Capacitor 的主要配置文件为`capacitor.config.ts`，可以在其中修改应用名称、ID 以及其他配置项。

## 开发流程

1. 在 web 环境中开发和测试应用功能
2. 构建前端代码：`pnpm run build`
3. 同步到原生平台：`npx cap sync`
4. 使用原生 IDE（Xcode 或 Android Studio）构建和测试应用
5. 使用原生 IDE 发布应用到应用商店

## 常见问题

### 构建失败

如果构建失败，请检查：

- TypeScript 错误
- 依赖项版本兼容性
- 构建配置

### 与后端 API 连接

在移动应用中，需要确保 API 请求正确配置：

- 使用 HTTPS 而非 HTTP
- 考虑 CORS 和安全策略
- 移动设备上的网络请求可能需要特殊权限

## 参考文档

- [Capacitor 官方文档](https://capacitorjs.com/docs)
- [Capacitor 插件列表](https://capacitorjs.com/docs/plugins)

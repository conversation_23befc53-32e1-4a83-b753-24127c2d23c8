{"connection": {"title": "连接设备", "failed": "设备连接失败", "disconnect_failed": "设备断开失败", "bluetooth_device": "蓝牙设备"}, "connection_form": {"input_placeholder": "请输入设备码", "invalid_code": "设备码无效，请检查后重试", "connect_failed": "连接设备失败，请稍后重试", "qr_not_supported": "当前设备不支持二维码扫描", "invalid_qr_code": "扫描到的设备码无效", "qr_unrecognized": "未能识别二维码内容", "camera_permission_denied": "相机权限被拒绝，请在设置中开启相机权限", "scan_not_supported": "当前设备不支持二维码扫描功能", "scan_failed": "扫描失败，请重试", "input_label": "输入设备码", "input_example": "示例设备码:1583，6842，2137", "connecting": "连接中...", "connect_button": "连接设备", "or": "或者", "scanning": "扫描中...", "scan_button": "扫描设备二维码", "scan_instruction": "将设备二维码置于摄像头前进行扫描"}, "control": {"title": "设备控制台", "connected": "已连接", "bluetooth_error": "蓝牙异常", "bluetooth_ready": "蓝牙就绪", "bluetooth_initializing": "蓝牙初始化中...", "bluetooth_not_initialized": "蓝牙未初始化", "wait_bluetooth_init": "请等待蓝牙初始化完成后再试", "bluetooth_connection_error": "蓝牙连接异常", "mode_not_found": "找不到模式 ID", "select_classic_mode": "选择经典模式", "send_classic_mode_failed": "发送经典模式命令失败", "device_function_set": "设备功能 {{functionKey}} 强度设置为 {{statusText}} (数值: {{intensity}})", "debounce_delay": "防抖延迟后发送蓝牙命令", "intensity_zero": "强度为0，发送停止指令", "stop_command_sent": "已发送停止命令", "stop_command_not_found": "找不到功能 {{functionKey}} 的停止命令", "intensity": "强度", "off": "关闭", "level": "{{level}}档", "classic_mode": "经典模式", "disconnect_failed": "断开连接失败", "disconnect_button": "断开连接"}}
import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { Device } from '@/api/services/devices'
import { deviceService, type SupportedDevicesResponse } from '@/api/services/devices'
import { bluetoothService } from '../pages/interactive/utils/BluetoothService'
import { sendBluetoothCommand } from '../pages/interactive/utils/bluetoothUtils'
import { addToast } from '@heroui/react'

// 连接来源类型
type ConnectionSource = 'home' | 'chat' | 'script'

// 控制模式类型
type ControlMode = 'manual' | 'auto' | 'script'

interface DeviceState {
  // 设备连接状态
  connectedDevice: Device | null
  connectionSource: ConnectionSource | null
  isBluetoothInitialized: boolean
  bluetoothError: string | null

  // 强度控制状态（当前实时强度）
  currentIntensity: { [key: string]: number }

  // 控制模式
  controlMode: ControlMode
  currentController: ConnectionSource | null

  // 设备列表缓存状态
  supportedDevices: SupportedDevicesResponse['devices']
  isLoadingDevices: boolean
  deviceListError: string | null
  deviceListLastFetched: number | null // 最后获取时间戳
}

interface DeviceActions {
  // 设备连接管理
  connectDevice: (device: Device, source: ConnectionSource) => Promise<void>
  disconnectDevice: () => Promise<void>

  // 强度控制
  setIntensity: (key: string, intensity: number) => Promise<void>
  resetAllIntensity: () => Promise<void>

  // 页面生命周期管理
  enterFunction: (functionType: 'chat' | 'script') => Promise<void>
  exitFunction: (functionType: 'chat' | 'script') => Promise<void>

  // 蓝牙管理
  initializeBluetooth: () => Promise<boolean>
  setBluetoothError: (error: string | null) => void

  // 内部状态管理
  setControlMode: (mode: ControlMode, controller: ConnectionSource | null) => void

  // 设备列表缓存管理
  getSupportedDevices: (forceRefresh?: boolean) => Promise<SupportedDevicesResponse['devices']>
  findDeviceByCode: (deviceCode: string) => Promise<Device | null>
  refreshSupportedDevices: () => Promise<void>
  clearDeviceListCache: () => void
}

type DeviceStore = DeviceState & DeviceActions

// 缓存过期时间（60分钟）
const CACHE_EXPIRY_TIME = 60 * 60 * 1000

export const useDeviceStore = create<DeviceStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      connectedDevice: null,
      connectionSource: null,
      isBluetoothInitialized: false,
      bluetoothError: null,
      currentIntensity: {},
      controlMode: 'manual',
      currentController: null,

      // 设备列表缓存初始状态
      supportedDevices: [],
      isLoadingDevices: false,
      deviceListError: null,
      deviceListLastFetched: null,

      // 设备连接管理
      connectDevice: async (device: Device, source: ConnectionSource) => {
        try {
          console.log(`连接设备: ${device.name}, 来源: ${source}`)

          // 初始化设备强度为0
          const initialIntensity: { [key: string]: number } = {}
          device.func.forEach(func => {
            initialIntensity[func.key] = 0
          })

          set({
            connectedDevice: device,
            connectionSource: source,
            currentIntensity: initialIntensity,
            controlMode: 'manual',
            currentController: source,
            bluetoothError: null
          })

          // 初始化蓝牙服务
          await get().initializeBluetooth()

          addToast({
            title: `成功连接设备: ${device.name}`,
            color: 'success'
          })
        } catch (error) {
          console.error('连接设备失败:', error)
          addToast({
            title: '设备连接失败',
            color: 'danger'
          })
          throw error
        }
      },

      disconnectDevice: async () => {
        const { connectedDevice } = get()

        if (connectedDevice) {
          try {
            console.log(`断开设备: ${connectedDevice.name}`)

            // 发送所有功能的关闭命令
            await get().resetAllIntensity()

            set({
              connectedDevice: null,
              connectionSource: null,
              currentIntensity: {},
              controlMode: 'manual',
              currentController: null,
              bluetoothError: null
            })

            addToast({
              title: '设备已断开连接',
              color: 'success'
            })
          } catch (error) {
            console.error('断开设备失败:', error)
            addToast({
              title: '断开设备失败',
              color: 'danger'
            })
          }
        }
      },

      // 强度控制
      setIntensity: async (key: string, intensity: number) => {
        const { connectedDevice, currentIntensity } = get()

        if (!connectedDevice) {
          console.warn('没有连接的设备')
          return
        }

        try {
          console.log(`设置设备强度: ${key} = ${intensity}`)

          // 更新状态
          set({
            currentIntensity: {
              ...currentIntensity,
              [key]: intensity
            }
          })

          // 发送蓝牙命令
          await sendBluetoothCommand(key, intensity, connectedDevice.func)
        } catch (error) {
          console.error('设置强度失败:', error)
          addToast({
            title: '设备控制失败',
            color: 'danger'
          })
        }
      },

      resetAllIntensity: async () => {
        const { connectedDevice, currentIntensity } = get()

        if (!connectedDevice) return

        try {
          console.log('重置所有设备功能强度为0')

          // 发送关闭命令给所有非零强度的功能
          const resetPromises = Object.entries(currentIntensity)
            .filter(([_, intensity]) => intensity > 0)
            .map(([key, _]) => sendBluetoothCommand(key, 0, connectedDevice.func))

          await Promise.all(resetPromises)

          // 更新状态
          const resetIntensity: { [key: string]: number } = {}
          connectedDevice.func.forEach(func => {
            resetIntensity[func.key] = 0
          })

          set({
            currentIntensity: resetIntensity
          })
        } catch (error) {
          console.error('重置设备强度失败:', error)
        }
      },

      // 页面生命周期管理
      enterFunction: async (functionType: 'chat' | 'script') => {
        const { connectedDevice, controlMode } = get()

        if (!connectedDevice) return

        console.log(`进入功能: ${functionType}`)

        try {
          // 重置所有强度为0
          await get().resetAllIntensity()

          // 切换控制模式
          const newMode: ControlMode = functionType === 'chat' ? 'auto' : 'script'
          set({
            controlMode: newMode,
            currentController: functionType
          })
        } catch (error) {
          console.error('进入功能模式失败:', error)
        }
      },

      exitFunction: async (functionType: 'chat' | 'script') => {
        const { connectedDevice, connectionSource } = get()

        if (!connectedDevice) return

        console.log(`退出功能: ${functionType}, 连接来源: ${connectionSource}`)

        try {
          // 重置所有强度为0
          await get().resetAllIntensity()

          // 判断是否需要断开设备
          if (connectionSource === functionType) {
            // 如果设备是在当前功能内连接的，则断开
            console.log('设备在当前功能内连接，执行断开')
            await get().disconnectDevice()
          } else {
            // 如果设备是在其他地方连接的，保持连接但切换回手动控制
            console.log('设备在其他地方连接，保持连接并切换回手动控制')
            set({
              controlMode: 'manual',
              currentController: connectionSource
            })
          }
        } catch (error) {
          console.error('退出功能模式失败:', error)
        }
      },

      // 蓝牙管理
      initializeBluetooth: async () => {
        try {
          console.log('初始化蓝牙服务...')
          const success = await bluetoothService.initialize()

          set({ isBluetoothInitialized: success })

          if (success) {
            console.log('蓝牙服务初始化成功')
            // 检查蓝牙状态
            const bluetoothEnabled = await bluetoothService.checkBluetooth()
            if (!bluetoothEnabled) {
              set({ bluetoothError: '蓝牙未启用，请在设备设置中启用蓝牙' })
            } else {
              set({ bluetoothError: null })
            }
          } else {
            set({ bluetoothError: '蓝牙服务初始化失败，请检查应用权限' })
          }

          return success
        } catch (error) {
          console.error('蓝牙初始化出错:', error)
          set({
            isBluetoothInitialized: false,
            bluetoothError: '蓝牙初始化出错，请重新启动应用'
          })
          return false
        }
      },

      setBluetoothError: (error: string | null) => {
        set({ bluetoothError: error })
      },

      // 内部状态管理
      setControlMode: (mode: ControlMode, controller: ConnectionSource | null) => {
        set({ controlMode: mode, currentController: controller })
      },

      // 设备列表缓存管理
      getSupportedDevices: async (forceRefresh = false) => {
        const { supportedDevices, deviceListLastFetched, isLoadingDevices } = get()

        // 如果正在加载中，直接返回当前缓存的数据
        if (isLoadingDevices) {
          console.log('设备列表正在加载中，返回缓存数据')
          return supportedDevices
        }

        // 检查缓存是否有效
        const now = Date.now()
        const cacheIsValid =
          deviceListLastFetched && now - deviceListLastFetched < CACHE_EXPIRY_TIME

        // 如果缓存有效且不强制刷新，返回缓存数据
        if (!forceRefresh && cacheIsValid && supportedDevices.length > 0) {
          console.log('使用缓存的设备列表数据')
          return supportedDevices
        }

        // 获取新数据
        try {
          console.log('开始获取设备列表...')
          set({
            isLoadingDevices: true,
            deviceListError: null
          })

          const devices = await deviceService.getSupportedDevices()

          set({
            supportedDevices: devices,
            deviceListLastFetched: now,
            deviceListError: null,
            isLoadingDevices: false
          })

          console.log(`✅ 设备列表获取成功，共 ${devices.length} 个设备`)
          return devices
        } catch (error) {
          console.error('❌ 获取支持设备列表失败:', error)

          const errorMessage = error instanceof Error ? error.message : '获取设备列表失败'

          set({
            deviceListError: errorMessage,
            isLoadingDevices: false
          })

          // 如果有缓存数据，返回缓存数据，否则返回空数组
          if (supportedDevices.length > 0) {
            console.log('获取失败，使用缓存数据')
            return supportedDevices
          }

          throw error
        }
      },

      findDeviceByCode: async (deviceCode: string) => {
        console.log('从缓存中查找设备:', deviceCode)
        
        try {
          // 先获取设备列表（使用缓存）
          const devices = await get().getSupportedDevices()
          
          // 在缓存中查找设备
          const device = devices.find(d => d.deviceCode === deviceCode)
          
          if (device) {
            console.log('✅ 在缓存中找到设备:', device.name)
            return device
          } else {
            console.log('❌ 设备码无效:', deviceCode)
            return null
          }
        } catch (error) {
          console.error('❌ 查找设备失败:', error)
          return null
        }
      },

      refreshSupportedDevices: async () => {
        console.log('手动刷新设备列表')
        await get().getSupportedDevices(true)
      },

      clearDeviceListCache: () => {
        console.log('清除设备列表缓存')
        set({
          supportedDevices: [],
          deviceListLastFetched: null,
          deviceListError: null
        })
      }
    }),
    {
      name: 'device-store',
      // 持久化连接状态和设备列表缓存
      partialize: state => ({
        connectedDevice: state.connectedDevice,
        connectionSource: state.connectionSource,
        supportedDevices: state.supportedDevices,
        deviceListLastFetched: state.deviceListLastFetched
      })
    }
  )
)

{"api": {"request_failed": "API请求失败", "unknown_error": "未知API错误", "unauthorized": "登录已过期", "unauthorized_description": "请重新登录后继续使用", "forbidden": "权限不足", "forbidden_description": "您没有权限执行此操作", "not_found": "资源不存在", "not_found_description": "请求的资源未找到", "server_error": "服务器错误", "server_error_description": "服务器暂时不可用，请稍后重试", "network_error": "网络连接失败", "network_error_description": "请检查网络连接后重试", "timeout_error": "请求超时", "timeout_error_description": "请求处理时间过长，请重试", "validation_error": "数据验证失败", "validation_error_description": "请检查输入的数据格式", "rate_limit": "请求过于频繁", "rate_limit_description": "请稍后再试"}, "avatar": {"select_image": "请选择图片文件", "size_limit": "图片大小不能超过1MB", "uploading": "上传中...", "upload_avatar": "上传头像", "support_format": "支持JPG、PNG格式，", "size_limit_note": "大小不超过1MB"}, "permission": {"check_failed": "权限检查失败", "check_failed_description": "系统暂时无法验证您的权限，请稍后重试", "points_insufficient": "积分不足", "points_required": "使用{{featureName}}需要 {{points}} 积分", "member_only": "会员专享功能", "member_only_description": "{{featureName}}是会员专享功能", "usage_limit": "使用次数已达上限", "insufficient": "权限不足"}, "user": {"profile_updated": "个人资料更新成功", "profile_update_failed": "个人资料更新失败", "info_load_failed": "用户信息加载失败", "status_load_failed": "用户状态加载失败"}, "membership": {"subscription_created": "订阅创建成功", "subscription_failed": "订阅创建失败", "points_consumed": "积分消费成功", "points_consume_failed": "积分消费失败", "insufficient_points": "积分不足", "member_required": "需要会员权限"}, "points": {"package_not_found": "积分包不存在", "package_inactive": "积分包已下架", "order_create_failed": "订单创建失败"}}
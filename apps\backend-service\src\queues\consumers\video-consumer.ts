import type { Env } from '@/types/env'
import type {
  VideoGenerationTask,
  VideoGenerationProgress,
  Insa3DVideoTaskRequest,
  Insa3DVideoTaskResponse,
  Insa3DVideoTaskStatus,
  VideoAttachment,
  GeneratingVideoStatusAttachment,
  CompletedVideoAttachment
} from '@/types/video'
import { getMessageById, updateMessageAttachments } from '@/lib/db/queries/chat'
import { QueueErrorHandler } from '../utils/queue-handler'
import { updateMediaGeneration } from '@/lib/db/queries/media-generation'
import { createServicePointsManager } from '@/lib/membership/service-points'
import { uploadToR2, getR2ConfigFromEnv, IMAGE_UPLOAD_OPTIONS } from '@/lib/utils/r2-upload'

import type {
  ReplicatePredictionRequest,
  ReplicatePredictionResponse,
  ReplicatePredictionStatus
} from '@/types/face-swap'

const CONFIG = {
  endpoint: 'c2a3we84tjaoip',
  token: 'SD_2RGdTZtC0WcZku0bJjA'
}

const VIDEO_CONFIG = {
  endpoint: 'bfnim7uitkaftc',
  token: '9ppbEMhaaS_W0ARxhnrC5A'
}

/**
 * 视频队列消费者
 * 处理视频生成任务，使用 InstantSD + Replicate 换脸 + InstantSD 生视频流程
 */
export class VideoQueueConsumer {
  private readonly REPLICATE_MODEL_VERSION =
    'd1d6ea8c8be89d664a07a457526f7128109dee7030fdac424788d762c71ed111'

  constructor(private env: Env) {}

  /**
   * 处理单个视频生成任务
   */
  async process(task: VideoGenerationTask): Promise<void> {
    console.log('🎬 开始生成:', task.taskId, '类型:', task.taskType || 'direct_video')

    // 根据任务类型选择处理方式
    if (task.taskType === 'multimodal_video') {
      await this.processMultimodalVideo(task)
    } else {
      await this.processDirectVideo(task)
    }
  }

  /**
   * 处理直接视频生成（原有逻辑）
   */
  private async processDirectVideo(task: VideoGenerationTask): Promise<void> {
    // 获取媒体生成记录ID
    const mediaGenerationId = task.metadata?.mediaGenerationId

    if (mediaGenerationId) {
      // 更新媒体生成记录为处理中状态
      try {
        await updateMediaGeneration(this.env, mediaGenerationId, {
          status: 'processing'
        })
      } catch (error) {
        console.error('❌ 更新失败:', error)
      }
    }

    try {
      // 1. 更新状态为开始生成
      await this.updateGenerationProgress(task.messageId, {
        status: 'starting',
        progress: 10,
        message: '正在连接服务...'
      })

      // 2. 调用 Insa3D API 启动任务
      const insa3dTaskId = await this.startInsa3DGeneration(task)

      // 3. 更新状态为处理中
      await this.updateGenerationProgress(task.messageId, {
        status: 'processing',
        progress: 30,
        message: '正在处理...'
      })

      // 4. 等待任务完成
      const result = await this.waitForTaskCompletion(insa3dTaskId, task.messageId)

      // 5. 处理结果
      if (result.status === 'completed' && result.videoUrl) {
        // 先上传到 R2 获取永久链接
        const finalVideoUrl = await this.downloadAndUploadVideoToR2(result.videoUrl, task.taskId)
        await this.updateCompletedVideo(task.messageId, finalVideoUrl, task.taskId)

        // 更新媒体生成记录为完成状态，使用 R2 URL
        if (mediaGenerationId) {
          try {
            await updateMediaGeneration(this.env, mediaGenerationId, {
              status: 'completed',
              outputUrls: [finalVideoUrl], // 使用 R2 URL 而不是临时 URL
              completedAt: new Date()
            })
          } catch (error) {
            console.error('❌ 更新失败:', error)
          }
        }

        console.log('✅ 生成完成')
      } else {
        await this.updateFailureStatus(task.messageId, result.errorMessage || '视频生成失败')

        // 更新媒体生成记录为失败状态
        if (mediaGenerationId) {
          try {
            await updateMediaGeneration(this.env, mediaGenerationId, {
              status: 'failed',
              errorMessage: result.errorMessage || '生成失败'
            })
          } catch (error) {
            console.error('❌ 更新失败:', error)
          }
        }

        throw new Error(result.errorMessage || '生成失败')
      }
    } catch (error) {
      const errorMessage = QueueErrorHandler.handleTaskError(task.taskId, error)
      await this.updateFailureStatus(task.messageId, errorMessage)

      // 更新媒体生成记录为失败状态
      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'failed',
            errorMessage
          })
        } catch (updateError) {
          console.error('❌ 更新失败:', updateError)
        }
      }

      // 生成失败，退还积分
      await this.refundPointsForFailedGeneration(task.userId, task.taskId)

      throw new Error(`生成失败: ${errorMessage}`)
    }
  }

  /**
   * @NOTE 提示词语法不兼容
   * 清理视频生成的 prompt，移除图片生成的权重语法
   * 例如：(medium shot:1.1) -> medium shot
   */
  private cleanPromptForVideo(prompt: string): string {
    // 移除权重语法 (text:number) -> text
    return prompt.replace(/\(([^:)]+):\d+(?:\.\d+)?\)/g, '$1').trim()
  }

  /**
   * 启动 Insa3D 视频生成
   */
  private async startInsa3DGeneration(task: VideoGenerationTask): Promise<string> {
    // 清理 prompt 中的权重语法，视频生成不需要这些
    const cleanedPrompt = this.cleanPromptForVideo(task.prompt)

    const request: Insa3DVideoTaskRequest = {
      inputs: {
        '6d3ea6aff3e601d5': {
          title: 'Prompt',
          value: cleanedPrompt
        },
        ...(task.characterAvatar && {
          f49f6ee1c1463ed1: {
            title: 'Load Image',
            value: task.characterAvatar
          }
        })
      }
    }

    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${VIDEO_CONFIG.endpoint}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${VIDEO_CONFIG.token}`
        },
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(80000) // 80秒超时
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`API请求失败: ${response.status} ${errorText}`)
    }

    const result: Insa3DVideoTaskResponse = await response.json()
    console.log('✅ 任务启动:', result.task_id)

    return result.task_id
  }

  /**
   * 等待 Insa3D 任务完成
   */
  private async waitForTaskCompletion(
    taskId: string,
    messageId: string
  ): Promise<{ status: string; videoUrl?: string; errorMessage?: string }> {
    const maxWaitTime = 900000 // 15分钟 (视频生成通常比图片更久)
    const pollInterval = 5000 // 5秒
    const startTime = Date.now()

    console.log('⏳ 轮询状态:', taskId)

    let progressStep = 70 // 从70%开始（视频生成阶段）
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(
          `https://api.instasd.com/api_endpoints/${VIDEO_CONFIG.endpoint}/task_status/${taskId}`,
          {
            headers: {
              Authorization: `Bearer ${VIDEO_CONFIG.token}`
            }
          }
        )

        if (!response.ok) {
          consecutiveErrors++
          console.warn(`⚠️ 查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`, response.status)

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const task: Insa3DVideoTaskStatus = await response.json()

        // 更新进度
        if (task.status === 'IN_PROGRESS' || task.status === 'EXECUTING') {
          progressStep = Math.min(progressStep + 2, 95) // 视频生成进度：70% -> 95%
          await this.updateGenerationProgress(messageId, {
            status: 'processing',
            progress: progressStep,
            message: '生成中...'
          })
        }

        // 检查完成状态
        if (task.status === 'COMPLETED') {
          return {
            status: 'completed',
            videoUrl: task.video_urls?.[0],
            errorMessage: undefined
          }
        }

        if (task.status === 'FAILED') {
          return {
            status: 'failed',
            videoUrl: undefined,
            errorMessage: task.error_message || '生成失败'
          }
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(`❌ 轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`, error)

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('等待超时')
  }

  /**
   * 更新生成进度
   */
  private async updateGenerationProgress(
    messageId: string,
    progress: Omit<VideoGenerationProgress, 'messageId' | 'timestamp'>
  ): Promise<void> {
    console.log('📊 更新进度:', progress.progress + '%')

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: VideoAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as VideoAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除所有生成状态附件（包括多模态生成状态）
      const filteredAttachments = existingAttachments.filter(
        (att: VideoAttachment) =>
          !att.contentType?.includes('generating') &&
          !att.contentType?.startsWith('video/generating')
      )

      // 检查是否存在之前的多模态生成状态，保持 intermediateImageUrl
      const existingMultimodalAttachment = existingAttachments.find(
        (att: VideoAttachment) => att.contentType === 'video/multimodal-generating'
      )

      // 如果当前更新没有 intermediateImageUrl，但之前的状态有，就保持之前的
      const finalIntermediateImageUrl =
        progress.intermediateImageUrl ||
        existingMultimodalAttachment?.metadata?.intermediateImageUrl

      // 添加新的状态附件
      const statusAttachment: GeneratingVideoStatusAttachment = {
        url: `generating://${progress.status}`,
        name: progress.message,
        contentType: 'video/multimodal-generating' as const, // 使用多模态专用的 contentType
        metadata: {
          status: progress.status,
          progress: progress.progress,
          timestamp: new Date().toISOString(),
          taskId: messageId, // 使用 messageId 作为 taskId
          stage: progress.stage || 'image_generation',
          ...(finalIntermediateImageUrl && {
            intermediateImageUrl: finalIntermediateImageUrl
          })
        }
      }

      const updatedAttachments = [...filteredAttachments, statusAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })

      console.log('📊 [PROGRESS] 进度更新成功:', {
        messageId,
        progress: progress.progress,
        status: progress.status,
        contentType: statusAttachment.contentType,
        hasIntermediateImageUrl: !!finalIntermediateImageUrl
      })
    } catch (error) {
      console.error('⚠️ [PROGRESS] 更新进度失败:', {
        messageId,
        progress: progress.progress,
        error: error instanceof Error ? error.message : String(error)
      })
    }
  }

  /**
   * 更新完成的视频
   */
  private async updateCompletedVideo(
    messageId: string,
    videoUrl: string,
    taskId: string
  ): Promise<void> {
    console.log('🎉 视频完成')

    // videoUrl 已经是 R2 URL，直接使用
    const finalVideoUrl = videoUrl

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: VideoAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as VideoAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除所有生成状态附件，添加真实视频附件
      const filteredAttachments = existingAttachments.filter(
        (att: VideoAttachment) =>
          !att.contentType?.includes('generating') &&
          !att.contentType?.startsWith('video/generating')
      )

      const videoAttachment: CompletedVideoAttachment = {
        url: finalVideoUrl,
        name: `generated-video-${Date.now()}.mp4`,
        contentType: 'video/mp4',
        metadata: {
          taskId,
          generatedAt: new Date().toISOString()
        }
      }

      const updatedAttachments = [...filteredAttachments, videoAttachment]

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: updatedAttachments
      })

      console.log('🎉 [COMPLETED] 视频附件更新成功:', {
        messageId,
        taskId,
        videoUrl: finalVideoUrl,
        contentType: videoAttachment.contentType
      })
    } catch (error) {
      console.error('❌ [COMPLETED] 更新视频失败:', {
        messageId,
        taskId,
        error: error instanceof Error ? error.message : String(error)
      })
      throw error
    }
  }

  /**
   * 更新失败状态
   */
  private async updateFailureStatus(messageId: string, error: string): Promise<void> {
    console.log('❌ 失败:', error)

    try {
      // 查询现有附件
      const currentMessages = await getMessageById(this.env, { id: messageId })

      if (!currentMessages.length) {
        console.warn('⚠️ 消息不存在')
        return
      }

      const currentMessage = currentMessages[0]

      // 解析现有附件
      let existingAttachments: VideoAttachment[] = []
      if (currentMessage.attachments) {
        existingAttachments = Array.isArray(currentMessage.attachments)
          ? (currentMessage.attachments as VideoAttachment[])
          : JSON.parse(currentMessage.attachments as string)
      }

      // 移除生成状态附件
      const filteredAttachments = existingAttachments.filter(
        (att: VideoAttachment) => !att.contentType?.startsWith('video/generating')
      )

      // 更新数据库
      await updateMessageAttachments(this.env, {
        messageId,
        attachments: filteredAttachments
      })
    } catch (updateError) {
      console.warn('⚠️ 更新状态失败')
    }
  }

  /**
   * 退还积分（生成失败时）
   */
  private async refundPointsForFailedGeneration(userId: string, taskId: string): Promise<void> {
    try {
      const pointsManager = createServicePointsManager(this.env)

      // 视频生成固定消费20积分
      const pointsToRefund = 20

      // 使用积分退还方法
      const refundResult = await pointsManager.refundPoints(userId, {
        amount: pointsToRefund,
        source: 'refund',
        sourceId: taskId,
        description: `视频生成失败退还 - 任务ID: ${taskId}`
      })

      if (refundResult.success) {
        console.log(`✅ 退还积分 ${pointsToRefund}`)
      } else {
        console.error(`❌ 退还失败: ${refundResult.error}`)
      }
    } catch (error) {
      console.error('退还异常:', error)
    }
  }

  /**
   * 处理多模态视频生成（先生图，再生视频）
   */
  private async processMultimodalVideo(task: VideoGenerationTask): Promise<void> {
    const mediaGenerationId = task.metadata?.mediaGenerationId

    if (mediaGenerationId) {
      try {
        await updateMediaGeneration(this.env, mediaGenerationId, {
          status: 'processing'
        })
      } catch (error) {
        console.error('❌ 更新媒体生成记录失败:', error)
      }
    }

    try {
      // 阶段1：图片生成
      console.log('🎨 第一步：生成图片')
      await this.updateGenerationProgress(task.messageId, {
        status: 'starting',
        progress: 5,
        message: '正在生成图片...'
      })

      const imageResult = await this.generateImageForVideo(task)

      // 先上传图片到 R2
      const intermediateImageUrl = await this.downloadAndUploadImageToR2(
        imageResult.imageUrl,
        `${task.taskId}-image`
      )

      await this.updateGenerationProgress(task.messageId, {
        status: 'processing',
        progress: 30,
        // message: '图片生成完成，开始换脸...',
        message: '进度 50%...',
        stage: 'video_generation',
        intermediateImageUrl: intermediateImageUrl
      })

      // 阶段2：换脸处理（如果有角色头像）
      let finalImageUrl = intermediateImageUrl
      if (task.characterAvatar) {
        console.log('🔄 第二步：换脸处理')
        const replicatePredictionId = await this.startReplicateFaceSwap(task, imageResult.imageUrl)

        await this.updateGenerationProgress(task.messageId, {
          status: 'processing',
          progress: 40,
          message: '正在渲染...',
          stage: 'video_generation'
        })

        const faceSwapResult = await this.waitForReplicateCompletion(
          replicatePredictionId,
          task.messageId
        )

        if (faceSwapResult.status === 'succeeded' && faceSwapResult.imageUrl) {
          finalImageUrl = faceSwapResult.imageUrl
        } else {
          throw new Error(faceSwapResult.errorMessage || '换脸失败')
        }
      }

      // 阶段2/3完成，准备生成视频
      await this.updateGenerationProgress(task.messageId, {
        status: 'processing',
        progress: 60,
        message: '正在渲染视频...',
        stage: 'video_generation'
      })

      // 更新中间结果到数据库
      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'processing',
            metadata: {
              stage: 'video_generation',
              intermediateImageUrl: finalImageUrl
            }
          })
        } catch (error) {
          console.error('❌ 更新中间结果失败:', error)
        }
      }

      // 阶段3：视频生成
      console.log('🎬 第三步：生成视频')
      await this.updateGenerationProgress(task.messageId, {
        status: 'processing',
        progress: 70,
        message: '正在渲染视频...'
      })

      const videoResult = await this.generateVideoFromImage(task, finalImageUrl)

      // 完成 - 先获取R2 URL，然后统一使用
      const finalVideoUrl = await this.downloadAndUploadVideoToR2(videoResult.videoUrl, task.taskId)
      await this.updateCompletedVideo(task.messageId, finalVideoUrl, task.taskId)

      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'completed',
            outputUrls: [finalVideoUrl], // 使用R2 URL保持一致性
            completedAt: new Date()
          })
        } catch (error) {
          console.error('❌ 更新完成状态失败:', error)
        }
      }

      console.log('✅ 多模态视频生成完成:', videoResult.videoUrl)
    } catch (error) {
      const errorMessage = QueueErrorHandler.handleTaskError(task.taskId, error)
      await this.updateFailureStatus(task.messageId, errorMessage)

      if (mediaGenerationId) {
        try {
          await updateMediaGeneration(this.env, mediaGenerationId, {
            status: 'failed',
            errorMessage
          })
        } catch (updateError) {
          console.error('❌ 更新失败状态失败:', updateError)
        }
      }

      // 退还积分
      await this.refundPointsForFailedGeneration(task.userId, task.taskId)
      throw new Error(`多模态视频生成失败: ${errorMessage}`)
    }
  }

  /**
   * 为视频生成图片（第一阶段）
   */
  private async generateImageForVideo(task: VideoGenerationTask): Promise<{ imageUrl: string }> {
    // 使用统一的图片生成配置
    const imageRequest = {
      inputs: {
        '79facdf6ca0558a0': {
          title: 'width',
          value: task.metadata?.width || 1024
        },
        a582029f7614ff66: {
          title: 'height',
          value: task.metadata?.height || 1440
        },
        '8bab993458e5b954': {
          title: 'prompt',
          value: task.prompt
        },
        '8bded2ca4cae6ff6': {
          title: 'seed',
          value: this.generateRandomSeed()
        }
      }
    }

    // 调用图片生成 API
    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${CONFIG.endpoint}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${CONFIG.token}`
        },
        body: JSON.stringify(imageRequest),
        signal: AbortSignal.timeout(60000)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`图片生成API请求失败: ${response.status} ${errorText}`)
    }

    const result: any = await response.json()
    const imageTaskId = result.task_id

    // 等待图片生成完成
    const imageResult = await this.waitForImageCompletion(imageTaskId, task.messageId)

    if (imageResult.status !== 'completed' || !imageResult.imageUrl) {
      throw new Error(imageResult.errorMessage || '图片生成失败')
    }

    return { imageUrl: imageResult.imageUrl }
  }

  /**
   * 从图片生成视频（第二阶段）
   */
  private async generateVideoFromImage(
    task: VideoGenerationTask,
    imageUrl: string
  ): Promise<{ videoUrl: string }> {
    // 清理 prompt 中的权重语法，视频生成不需要这些
    const cleanedPrompt = this.cleanPromptForVideo(task.prompt)

    // 使用生成的图片作为输入，调用视频生成 API
    const videoRequest: Insa3DVideoTaskRequest = {
      inputs: {
        '6d3ea6aff3e601d5': {
          title: 'Prompt',
          value: cleanedPrompt
        },
        f49f6ee1c1463ed1: {
          title: 'Load Image',
          value: imageUrl // 使用第一阶段生成的图片
        }
      }
    }

    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${VIDEO_CONFIG.endpoint}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${VIDEO_CONFIG.token}`
        },
        body: JSON.stringify(videoRequest),
        signal: AbortSignal.timeout(60000)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`视频生成API请求失败: ${response.status} ${errorText}`)
    }

    const result: Insa3DVideoTaskResponse = await response.json()
    const videoTaskId = result.task_id

    // 等待视频生成完成
    const videoResult = await this.waitForTaskCompletion(videoTaskId, task.messageId)

    if (videoResult.status !== 'completed' || !videoResult.videoUrl) {
      throw new Error(videoResult.errorMessage || '视频生成失败')
    }

    return { videoUrl: videoResult.videoUrl }
  }

  /**
   * 等待图片生成完成
   */
  private async waitForImageCompletion(
    taskId: string,
    messageId: string
  ): Promise<{ status: string; imageUrl?: string; errorMessage?: string }> {
    const maxWaitTime = 300000 // 5分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    let progressStep = 10

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(
          `https://api.instasd.com/api_endpoints/${CONFIG.endpoint}/task_status/${taskId}`,
          {
            headers: {
              Authorization: `Bearer ${CONFIG.token}`
            },
            signal: AbortSignal.timeout(10000)
          }
        )

        if (!response.ok) {
          throw new Error(`状态查询失败: ${response.status}`)
        }

        const task: any = await response.json()

        // 更新进度
        if (task.status === 'IN_PROGRESS' || task.status === 'EXECUTING') {
          progressStep = Math.min(progressStep + 2, 45)
          await this.updateGenerationProgress(messageId, {
            status: 'processing',
            progress: progressStep,
            message: '正在生成视频内容...'
          })
        }

        // 检查完成状态
        if (task.status === 'COMPLETED') {
          return {
            status: 'completed',
            imageUrl: task.image_urls?.[0],
            errorMessage: undefined
          }
        }

        if (task.status === 'FAILED') {
          return {
            status: 'failed',
            imageUrl: undefined,
            errorMessage: task.error_message || '图片生成失败'
          }
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        console.error('图片状态查询错误:', error)
        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    return {
      status: 'failed',
      errorMessage: '图片生成超时'
    }
  }

  /**
   * 生成随机 seed
   */
  private generateRandomSeed(): number {
    // 生成一个 10-13 位的随机数字作为 seed
    return Math.floor(Math.random() * 9000000000000) + 1000000000000
  }

  /**
   * 启动 Replicate 换脸任务
   */
  private async startReplicateFaceSwap(
    task: VideoGenerationTask,
    inputImageUrl: string
  ): Promise<string> {
    const request: ReplicatePredictionRequest = {
      version: this.REPLICATE_MODEL_VERSION,
      input: {
        swap_image: task.characterAvatar || '',
        input_image: inputImageUrl
      }
    }

    const response = await fetch('https://api.replicate.com/v1/predictions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${this.env.REPLICATE_API_TOKEN}`,
        Prefer: 'wait'
      },
      body: JSON.stringify(request),
      signal: AbortSignal.timeout(60000) // 60秒超时
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Replicate API请求失败: ${response.status} ${errorText}`)
    }

    const result: ReplicatePredictionResponse = await response.json()
    console.log('✅ Replicate任务启动:', result.id)

    return result.id
  }

  /**
   * 等待 Replicate 任务完成
   */
  private async waitForReplicateCompletion(
    predictionId: string,
    messageId: string
  ): Promise<{ status: string; imageUrl?: string; errorMessage?: string }> {
    const maxWaitTime = 600000 // 10分钟
    const pollInterval = 3000 // 3秒
    const startTime = Date.now()

    console.log('⏳ 轮询Replicate状态:', predictionId)

    let progressStep = 45 // 从45%开始
    let consecutiveErrors = 0
    const maxConsecutiveErrors = 5

    while (Date.now() - startTime < maxWaitTime) {
      try {
        const response = await fetch(`https://api.replicate.com/v1/predictions/${predictionId}`, {
          headers: {
            Authorization: `Bearer ${this.env.REPLICATE_API_TOKEN}`
          }
        })

        if (!response.ok) {
          consecutiveErrors++
          console.warn(`⚠️ 查询失败 ${consecutiveErrors}/${maxConsecutiveErrors}:`, response.status)

          if (consecutiveErrors >= maxConsecutiveErrors) {
            throw new Error('查询失败次数过多')
          }

          await new Promise(resolve => setTimeout(resolve, pollInterval))
          continue
        }

        consecutiveErrors = 0
        const prediction: ReplicatePredictionStatus = await response.json()

        // 更新进度
        if (prediction.status === 'starting' || prediction.status === 'processing') {
          progressStep = Math.min(progressStep + 3, 55)
          await this.updateGenerationProgress(messageId, {
            status: 'processing',
            progress: progressStep,
            message: '换脸中...'
          })
        }

        // 检查完成状态
        if (prediction.status === 'succeeded') {
          const imageUrl = Array.isArray(prediction.output)
            ? prediction.output[0]
            : prediction.output

          return {
            status: 'succeeded',
            imageUrl: imageUrl as string,
            errorMessage: undefined
          }
        }

        if (prediction.status === 'failed' || prediction.status === 'canceled') {
          return {
            status: 'failed',
            imageUrl: undefined,
            errorMessage: prediction.error || '换脸失败'
          }
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      } catch (error) {
        consecutiveErrors++
        console.error(`❌ 轮询异常 ${consecutiveErrors}/${maxConsecutiveErrors}:`, error)

        if (consecutiveErrors >= maxConsecutiveErrors) {
          throw new Error('轮询异常过多')
        }

        await new Promise(resolve => setTimeout(resolve, pollInterval))
      }
    }

    // 超时
    throw new Error('等待超时')
  }

  /**
   * 下载图片并上传到 R2
   */
  private async downloadAndUploadImageToR2(imageUrl: string, taskId: string): Promise<string> {
    try {
      console.log('🖼️ [R2上传] 开始下载图片:', imageUrl)

      // 1. 下载图片
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
      }

      const imageBuffer = await response.arrayBuffer()
      console.log('🖼️ [R2上传] 图片下载完成，大小:', imageBuffer.byteLength, '字节')

      // 2. 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        console.error('❌ [R2上传] R2 配置缺失，使用原始 URL')
        return imageUrl
      }

      console.log('🖼️ [R2上传] R2 配置获取成功')

      // 3. 生成文件名（图片格式）
      const timestamp = new Date().toISOString().split('T')[0]
      const uniqueId = taskId.split('-').pop() || 'unknown'
      const fileName = `multimodal-image-${uniqueId}.png`

      // 4. 上传到 R2
      const uploadResult = await uploadToR2(imageBuffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName,
        folder: `multimodal-images/${timestamp}`
      })

      if (!uploadResult.success) {
        console.error('❌ [R2上传] 失败，使用原始 URL:', uploadResult.error)
        return imageUrl
      }

      console.log('✅ [R2上传] 图片上传成功:', {
        originalUrl: imageUrl,
        r2Url: uploadResult.url,
        key: uploadResult.key,
        size: uploadResult.size
      })

      return uploadResult.url!
    } catch (error) {
      console.error('❌ [R2上传] 图片上传异常，使用原始 URL:', error)
      return imageUrl
    }
  }

  /**
   * 下载视频并上传到 R2
   */
  private async downloadAndUploadVideoToR2(videoUrl: string, taskId: string): Promise<string> {
    try {
      console.log('🎬 [R2上传] 开始下载视频:', videoUrl)

      // 1. 下载视频
      const response = await fetch(videoUrl)
      if (!response.ok) {
        throw new Error(`下载视频失败: ${response.status} ${response.statusText}`)
      }

      const videoBuffer = await response.arrayBuffer()
      console.log('🎬 [R2上传] 视频下载完成，大小:', videoBuffer.byteLength, '字节')

      // 2. 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        console.error('❌ [R2上传] R2 配置缺失，使用原始 URL')
        return videoUrl
      }

      console.log('🎬 [R2上传] R2 配置获取成功')

      // 3. 生成文件名
      const timestamp = new Date().toISOString().split('T')[0]
      const uniqueId = taskId.split('-').pop() || 'unknown'
      const fileName = `generated-video-${uniqueId}.mp4`

      // 4. 上传到 R2（使用视频专用配置）
      const uploadResult = await uploadToR2(videoBuffer, r2Config, {
        fileName,
        folder: `generated-videos/${timestamp}`,
        maxSize: 100 * 1024 * 1024, // 100MB 视频文件限制
        allowedTypes: ['video/mp4', 'video/webm', 'video/avi', 'application/octet-stream'],
        makePublic: true
      })

      if (!uploadResult.success) {
        console.error('❌ [R2上传] 失败，使用原始 URL:', uploadResult.error)
        return videoUrl
      }

      console.log('✅ [R2上传] 上传成功:', {
        originalUrl: videoUrl,
        r2Url: uploadResult.url,
        key: uploadResult.key,
        size: uploadResult.size
      })

      return uploadResult.url!
    } catch (error) {
      console.error('❌ [R2上传] 异常，使用原始 URL:', error)
      return videoUrl
    }
  }
}

import type { Env } from '@/types/env'

/**
 * 根据用户消息生成聊天标题
 */
export async function generateTitleFromUserMessage(
  env: Env,
  { message }: { message: { content: string; parts?: any[] } }
) {
  try {
    const messages = [
      {
        role: 'system',
        content: `你是一个专门生成聊天标题的助手。请根据用户的第一条消息生成一个简短的标题：
          - 标题长度不超过80个字符
          - 标题应该是用户消息的简洁总结
          - 不要使用引号或冒号
          - 根据内容语言确定标题语言，通常应该是中文或英文
          - 只返回标题文本，不要其他内容`
      },
      {
        role: 'user',
        content: `请为以下消息生成一个标题：${message.content}`
      }
    ]

    // 使用 Cloudflare Workers AI
    const response = (await env.AI.run('@cf/meta/llama-3.1-8b-instruct-fast' as any, {
      messages
    })) as { response: string }

    // 提取生成的标题，去除可能的多余内容
    const title = response.response.trim().replace(/^["']|["']$/g, '')

    return title || `新对话 ${new Date().toLocaleDateString()}`
  } catch (error) {
    console.error('生成标题失败:', error)
    // 如果生成失败，返回默认标题
    return `新对话 ${new Date().toLocaleDateString()}`
  }
}

export async function generateScenePrompt(
  env: Env,
  { sceneDescription }: { sceneDescription: string }
) {
  try {
    const response = await env.AI.run('@cf/meta/llama-4-scout-17b-16e-instruct' as any, {
      messages: [
        {
          role: 'system',
          content: `
          你是一个专业的图像生成提示词专家，专门将中文场景描述转换为高质量的英文摄影风格提示词。

          目标：生成纯净的环境背景图，适用于聊天应用背景，不包含任何人物。

          **严格要求：**
          1. **禁止文字元素**：绝对不能包含任何文字、标志、招牌、标识、符号、字母、数字
          2. **自然摄影风格**：模拟真实相机拍摄效果，避免过度渲染的CG风格
          3. **环境纯净性**：专注于自然环境，避免商业化元素

          **生成规则：**
          - 描述具体的自然环境元素（天空、地面、植物、建筑轮廓）
          - 强调光线和氛围（natural lighting、soft shadows、ambient light）
          - 使用摄影术语（depth of field、bokeh、natural exposure）
          - 保持简洁直接的描述，避免过度修饰
          - 添加手机竖屏比例适配（9:16 aspect ratio）

          **摄影风格关键词：**
          - "natural photography"、"soft natural lighting"
          - "clean composition"、"minimal elements"
          - "no text"、"no signs"、"no letters"

          只输出一段简洁的英文提示词，确保生成干净、自然的环境背景。
          `
        },
        { role: 'user', content: sceneDescription }
      ]
    })

    const message = response.response.trim().replace(/^["']|["']$/g, '')
    return message
  } catch (error) {
    console.error('优化场景提示词失败，使用fallback机制:', error)

    // 如果优化失败，使用改进的基本提示词构建逻辑
    const optimizedPrompt = `${sceneDescription}, natural photography, soft natural lighting, clean composition, minimal elements, no text, no signs, no letters, depth of field, 9:16 aspect ratio, environmental background, peaceful atmosphere, high quality`

    console.log('使用fallback场景提示词:', optimizedPrompt)
    return optimizedPrompt
  }
}

export async function generatePrompt(env: Env, { keywords }: { keywords: string }) {
  try {
    const response = await env.AI.run('@cf/meta/llama-4-scout-17b-16e-instruct' as any, {
      messages: [
        {
          role: 'system',
          content: `你是一个专业的图像提示词生成器。你的任务是根据用户提供的角色关键词，生成一个详细、丰富且适合图像生成的英文的提示词。以下是生成提示词时需要遵循的原则：
  
  1. **保留核心特征**：确保用户提供的所有关键词都被包含在最终的提示词中，不遗漏任何细节。
  2. **细节增强**：
     - 对于外貌特征（如头发、眼睛、身材），添加具体的形容词和短语，增强视觉表现力。
     - 对于服装和配饰，描述材质、颜色、样式，增加画面质感。
     - 加入背景或环境描述，提供场景和氛围。
  3. **艺术风格**：默认加入"detailed textures, vibrant colors, cinematic composition"来保证图像的高质量。
  4. **语言要求**：使用生动、具体的语言，避免模糊或笼统的表达，不要超过 1000 字符。
  5. **人物构图**：
     - 不要出现遮挡，比如闭眼，挡住头部等行为
     - 不要出现NSFW内容
     - 人物五官要清晰，不要挡住头部部分，或者闭眼等行为
     - 人物只需出现上半身，不要只出现头部
     - 只能出现一个人！
     - 一定要面对镜头！
  6. **结构**：最终提示词应包含以下部分：
     - 角色描述（外貌、服装等）
     - 背景或环境
     - 光线和氛围
     - 艺术风格和质量描述
     - 需要能看到清晰的五官，不能挡住头部部分，或者闭眼等行为
  必须遵守以上。
  最终输出格式：
  - 直接是 Prompt 内容，不需要分段，不需要带："Prompt："等，直接输出提示词即可。
  - prompt 为全英文。
  - 不要有敏感词，不要有NSFW内容
  - 不需要加粗，不需要特殊格式，直接就是纯文本。`
        },
        { role: 'user', content: keywords }
      ]
    })

    const message = response.response.trim().replace(/^["']|["']$/g, '')
    return message
  } catch (error) {
    console.error('优化提示词失败，使用fallback机制:', error)

    // 如果优化失败，使用基本的提示词构建逻辑
    const gender = keywords.includes('男性') || keywords.includes('male') ? 'male' : 'female'
    const ageGroup = keywords.includes('20多岁')
      ? 'in their 20s'
      : keywords.includes('30多岁')
      ? 'in their 30s'
      : keywords.includes('40多岁')
      ? 'in their 40s'
      : 'young adult'

    // 基于关键词构建更智能的fallback提示词
    const optimizedPrompt = `A beautiful ${gender} ${ageGroup}, ${keywords.replace(
      /，/g,
      ', '
    )}, detailed portrait, professional photography, high quality, 4k resolution, detailed textures, vibrant colors, cinematic composition, facing camera, clear facial features, upper body visible`

    console.log('使用fallback提示词:', optimizedPrompt)
    return optimizedPrompt
  }
}

import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type {
  InviteCode,
  ReferralRelation,
  CommissionAccount,
  CommissionRecord,
  WithdrawRequest
} from '../schema'
import type { Env } from '@/types/env'
import { nanoid } from 'nanoid'

// ==================== 邀请码管理 ====================

/**
 * 生成唯一邀请码
 */
export async function generateUniqueInviteCode(env: Env): Promise<string> {
  const supabase = getSupabase(env)
  let code: string
  let isUnique = false
  let attempts = 0
  const maxAttempts = 10

  do {
    // 生成6位字母数字组合，排除容易混淆的字符
    code = nanoid(6)
      .toUpperCase()
      .replace(/[0O1IL]/g, () => {
        const chars = 'ABCDEFGHJKMNPQRSTUVWXYZ23456789'
        return chars[Math.floor(Math.random() * chars.length)]
      })

    // 检查是否唯一
    const result = await supabase
      .from(TABLE_NAMES.inviteCode)
      .select('id')
      .eq('code', code)
      .limit(1)

    const { data } = handleSupabaseResult(result)
    isUnique = !data || data.length === 0
    attempts++
  } while (!isUnique && attempts < maxAttempts)

  if (!isUnique) {
    throw new Error('生成邀请码失败，请重试')
  }

  return code
}

/**
 * 创建邀请码
 */
export async function createInviteCode(env: Env, userId: string): Promise<InviteCode> {
  const supabase = getSupabase(env)

  // 检查用户是否已有邀请码
  const existingResult = await supabase
    .from(TABLE_NAMES.inviteCode)
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .limit(1)

  const { data: existing } = handleSupabaseResult(existingResult)
  if (existing && existing.length > 0) {
    return existing[0] as InviteCode
  }

  // 生成新邀请码
  const code = await generateUniqueInviteCode(env)

  const result = await supabase
    .from(TABLE_NAMES.inviteCode)
    .insert({
      user_id: userId,
      code,
      max_uses: null,
      used_count: 0,
      expires_at: null,
      is_active: true
    })
    .select()
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) throw error
  return data as InviteCode
}

/**
 * 获取用户的邀请码
 */
export async function getUserInviteCode(env: Env, userId: string): Promise<InviteCode | null> {
  const supabase = getSupabase(env)

  const result = await supabase
    .from(TABLE_NAMES.inviteCode)
    .select('*')
    .eq('user_id', userId)
    .eq('is_active', true)
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) return null
  return data as InviteCode
}

/**
 * 验证邀请码
 */
export async function validateInviteCode(
  env: Env,
  code: string
): Promise<{
  valid: boolean
  inviteCode?: InviteCode & { inviterEmail: string }
  error?: string
}> {
  const supabase = getSupabase(env)

  const result = await supabase
    .from(TABLE_NAMES.inviteCode)
    .select(
      `
      *,
      user:user_id (
        email
      )
    `
    )
    .eq('code', code.toUpperCase())
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) {
    return { valid: false, error: '邀请码不存在' }
  }

  const inviteCodeData = data as any

  // 检查邀请码是否有效
  if (!inviteCodeData.is_active) {
    return { valid: false, error: '邀请码已失效' }
  }

  // 检查是否过期
  if (inviteCodeData.expires_at && new Date() > new Date(inviteCodeData.expires_at)) {
    return { valid: false, error: '邀请码已过期' }
  }

  // 检查使用次数限制
  if (inviteCodeData.max_uses && inviteCodeData.used_count >= inviteCodeData.max_uses) {
    return { valid: false, error: '邀请码使用次数已达上限' }
  }

  return {
    valid: true,
    inviteCode: {
      ...inviteCodeData,
      inviterEmail: inviteCodeData.user?.email || ''
    } as InviteCode & { inviterEmail: string }
  }
}

/**
 * 更新邀请码使用次数
 */
export async function incrementInviteCodeUsage(env: Env, inviteCodeId: string): Promise<void> {
  const supabase = getSupabase(env)

  // 先查询当前使用次数，然后更新
  const currentResult = await supabase
    .from(TABLE_NAMES.inviteCode)
    .select('used_count')
    .eq('id', inviteCodeId)
    .single()

  const { data: current, error: selectError } = handleSupabaseSingleResult(currentResult)
  if (selectError) throw selectError

  const newUsedCount = (current?.used_count || 0) + 1

  const result = await supabase
    .from(TABLE_NAMES.inviteCode)
    .update({
      used_count: newUsedCount,
      updated_at: new Date().toISOString()
    })
    .eq('id', inviteCodeId)

  const { error } = handleSupabaseResult(result)
  if (error) throw error
}

// ==================== 推荐关系管理 ====================

/**
 * 创建推荐关系
 */
export async function createReferralRelation(
  env: Env,
  inviterId: string,
  inviteeId: string,
  inviteCodeId: string
): Promise<ReferralRelation> {
  const supabase = getSupabase(env)

  // 检查是否已存在推荐关系
  const existingResult = await supabase
    .from(TABLE_NAMES.referralRelation)
    .select('id')
    .eq('invitee_id', inviteeId)
    .limit(1)

  const { data: existing } = handleSupabaseResult(existingResult)
  if (existing && existing.length > 0) {
    throw new Error('该用户已经被邀请过')
  }

  // 检查是否自己邀请自己
  if (inviterId === inviteeId) {
    throw new Error('不能使用自己的邀请码')
  }

  const result = await supabase
    .from(TABLE_NAMES.referralRelation)
    .insert({
      inviter_id: inviterId,
      invitee_id: inviteeId,
      invite_code_id: inviteCodeId
    })
    .select()
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) throw error
  return data as ReferralRelation
}

/**
 * 获取用户的邀请人信息
 */
export async function getUserInviter(
  env: Env,
  userId: string
): Promise<{
  inviterId: string
  inviterEmail: string
  inviteCode: string
  createdAt: string
} | null> {
  const supabase = getSupabase(env)

  const result = await supabase
    .from(TABLE_NAMES.referralRelation)
    .select(
      `
      inviter_id,
      created_at,
      user:inviter_id (
        email
      ),
      invite_code:invite_code_id (
        code
      )
    `
    )
    .eq('invitee_id', userId)
    .single()

  const { data, error } = handleSupabaseSingleResult(result)
  if (error) return null

  const relationData = data as any
  return {
    inviterId: relationData.inviter_id,
    inviterEmail: relationData.user?.email || '',
    inviteCode: relationData.invite_code?.code || '',
    createdAt: relationData.created_at
  }
}

/**
 * 获取用户邀请的人员列表
 */
export async function getUserInvitees(
  env: Env,
  userId: string,
  page = 1,
  limit = 10
): Promise<{
  list: Array<{
    id: string
    inviteeId: string
    inviteeEmail: string
    inviteCode: string
    createdAt: string
    totalCommission: number
  }>
  total: number
}> {
  const supabase = getSupabase(env)
  const offset = (page - 1) * limit

  // 获取邀请列表
  const listResult = await supabase
    .from(TABLE_NAMES.referralRelation)
    .select(
      `
      id,
      invitee_id,
      created_at,
      user:invitee_id (
        email
      ),
      invite_code:invite_code_id (
        code
      )
    `
    )
    .eq('inviter_id', userId)
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1)

  const { data: listData } = handleSupabaseResult(listResult)

  // 获取总数
  const countResult = await supabase
    .from(TABLE_NAMES.referralRelation)
    .select('id', { count: 'exact' })
    .eq('inviter_id', userId)

  const { count } = countResult

  // TODO: 添加佣金统计查询
  const list = (listData || []).map((item: any) => ({
    id: item.id,
    inviteeId: item.invitee_id,
    inviteeEmail: item.user?.email || '',
    inviteCode: item.invite_code?.code || '',
    createdAt: item.created_at,
    totalCommission: 0 // 暂时设为0，后续添加佣金统计
  }))

  return {
    list,
    total: count || 0
  }
}

/**
 * 获取邀请统计
 */
export async function getInviteStats(
  env: Env,
  userId: string
): Promise<{
  totalInvites: number
  thisMonthInvites: number
  totalCommission: number
}> {
  const supabase = getSupabase(env)

  // 获取总邀请数
  const totalResult = await supabase
    .from(TABLE_NAMES.referralRelation)
    .select('id', { count: 'exact' })
    .eq('inviter_id', userId)

  // 获取本月邀请数
  const thisMonth = new Date()
  thisMonth.setDate(1)
  thisMonth.setHours(0, 0, 0, 0)

  const monthResult = await supabase
    .from(TABLE_NAMES.referralRelation)
    .select('id', { count: 'exact' })
    .eq('inviter_id', userId)
    .gte('created_at', thisMonth.toISOString())

  // TODO: 添加佣金统计查询

  return {
    totalInvites: totalResult.count || 0,
    thisMonthInvites: monthResult.count || 0,
    totalCommission: 0 // 暂时设为0，后续添加佣金统计
  }
}

import React, { useState, useEffect, useCallback } from 'react'
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  Tag,
  DatePicker,
  message,
  Modal,
  Typography,
  Avatar,
  Radio,
  InputNumber,
  Form,
  Divider,
  Row,
  Col,
  Tooltip
} from 'antd'
import {
  SearchOutlined,
  ExportOutlined,
  UserOutlined,
  CrownOutlined,
  Exclamation<PERSON>ircleOutlined,
  EyeOutlined,
  ManOutlined,
  WomanOutlined,
  QuestionCircleOutlined,
  DollarOutlined,
  PlusOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { User, MembershipPlan } from '@/types/api'
import type { UserListParams, UserDetailResponse, CreateUserRequest } from '@/services/users'
import { userService } from '@/services/users'
import { membershipService } from '@/services/membership'
import { DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { Title } = Typography

const UserList: React.FC = () => {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)

  // 搜索条件
  const [searchParams, setSearchParams] = useState<UserListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  const loadUsers = useCallback(async () => {
    try {
      setLoading(true)

      const params: UserListParams = {
        page: currentPage,
        pageSize,
        ...searchParams
      }

      const response = await userService.getUsers(params)

      if (response.success && response.data) {
        // 使用标准的分页格式
        setUsers(response.data.data || [])
        setTotal(response.data.total || 0)
      } else {
        message.error(response.message || '获取用户列表失败')
      }
    } catch (error) {
      console.error('获取用户列表失败:', error)
      message.error('获取用户列表失败')
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchParams])

  useEffect(() => {
    loadUsers()
  }, [loadUsers])

  const handleSearch = () => {
    setCurrentPage(1)
    // loadUsers will be called automatically due to useEffect dependency
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
    // loadUsers 会由 useEffect 自动调用，不需要手动调用
  }

  const handleDeleteUser = async (userId: string, userEmail: string) => {
    Modal.confirm({
      title: '确认删除用户',
      content: `确定要删除用户 "${userEmail}" 吗？此操作不可恢复。`,
      okText: '确认删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await userService.deleteUser(userId)
          if (response.success) {
            message.success('用户删除成功')
            loadUsers()
          } else {
            message.error(response.message || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          message.error('删除失败')
        }
      }
    })
  }

  // 会员设置相关状态
  const [membershipPlans, setMembershipPlans] = useState<MembershipPlan[]>([])
  const [membershipModalVisible, setMembershipModalVisible] = useState(false)
  const [selectedUserForMembership, setSelectedUserForMembership] = useState<User | null>(null)
  const [membershipLoading, setMembershipLoading] = useState(false)
  const [selectedPlanId, setSelectedPlanId] = useState<string>('')
  const [membershipDuration, setMembershipDuration] = useState<number>(30)
  const [membershipForm] = Form.useForm()

  // 加载会员计划列表
  const loadMembershipPlans = async () => {
    try {
      console.log('正在加载会员计划...')
      const response = await membershipService.getPlans({ isActive: true })
      console.log('会员计划API响应:', response)

      if (response.success && response.data) {
        // membershipService.getPlans 直接返回 MembershipPlan[]
        const plans = Array.isArray(response.data) ? response.data : []
        console.log('设置会员计划:', plans)
        setMembershipPlans(plans)
      } else {
        console.error('获取会员计划失败:', response.message)
        message.error(response.message || '获取会员计划失败')
      }
    } catch (error) {
      console.error('获取会员计划失败:', error)
      message.error('获取会员计划失败')
    }
  }

  // 打开设为会员弹窗
  const handleSetMembership = async (user: User) => {
    setSelectedUserForMembership(user)
    setMembershipModalVisible(true)
    setSelectedPlanId('')
    setMembershipDuration(30)
    membershipForm.resetFields()
    if (membershipPlans.length === 0) {
      await loadMembershipPlans()
    }
  }

  // 确认设为会员
  const handleConfirmMembership = async () => {
    if (!selectedUserForMembership || !selectedPlanId) {
      message.error('请选择会员计划')
      return
    }

    setMembershipLoading(true)
    try {
      const response = await userService.setUserMembership(
        selectedUserForMembership.id,
        selectedPlanId,
        membershipDuration
      )
      if (response.success) {
        message.success('设为会员成功')
        setMembershipModalVisible(false)
        setSelectedUserForMembership(null)
        setSelectedPlanId('')
        setMembershipDuration(30)
        loadUsers() // 刷新用户列表
      } else {
        message.error(response.message || '设为会员失败')
      }
    } catch (error) {
      console.error('设为会员失败:', error)
      message.error('设为会员失败')
    } finally {
      setMembershipLoading(false)
    }
  }

  // 用户详情相关状态
  const [selectedUser, setSelectedUser] = useState<UserDetailResponse | null>(null)
  const [userDetailVisible, setUserDetailVisible] = useState(false)
  const [detailLoading, setDetailLoading] = useState(false)

  // 创建用户相关状态
  const [createUserVisible, setCreateUserVisible] = useState(false)
  const [createUserLoading, setCreateUserLoading] = useState(false)
  const [createUserForm] = Form.useForm()

  const copyUserId = (userId: string) => {
    navigator.clipboard.writeText(userId)
    message.success('用户ID已复制到剪贴板')
  }

  // 查看用户详情
  const handleViewDetail = async (user: User) => {
    setDetailLoading(true)
    setUserDetailVisible(true)
    setSelectedUser(null)

    try {
      const response = await userService.getUserDetail(user.id)
      if (response.success && response.data) {
        setSelectedUser(response.data)
      } else {
        message.error('获取用户详情失败')
        setUserDetailVisible(false)
      }
    } catch (error) {
      console.error('获取用户详情失败:', error)
      message.error('获取用户详情失败')
      setUserDetailVisible(false)
    } finally {
      setDetailLoading(false)
    }
  }

  const getGenderDisplay = (gender?: string) => {
    switch (gender) {
      case 'male':
      case '男':
        return {
          icon: <ManOutlined style={{ color: '#52c41a' }} />,
          text: '男'
        }
      case 'female':
      case '女':
        return {
          icon: <WomanOutlined style={{ color: '#f759ab' }} />,
          text: '女'
        }
      default:
        return {
          icon: <QuestionCircleOutlined style={{ color: '#d9d9d9' }} />,
          text: '未设置'
        }
    }
  }

  // 创建用户
  const handleCreateUser = async (values: CreateUserRequest) => {
    setCreateUserLoading(true)
    try {
      const response = await userService.createUser(values)
      if (response.success && response.data) {
        message.success('用户创建成功')

        // 如果生成了临时密码，显示给管理员
        if (response.data.tempPassword) {
          Modal.info({
            title: '用户创建成功',
            content: (
              <div>
                <p>
                  用户 <strong>{response.data.email}</strong> 创建成功！
                </p>
                <p>
                  临时密码：
                  <code
                    style={{
                      backgroundColor: '#f5f5f5',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      fontSize: '14px'
                    }}
                  >
                    {response.data.tempPassword}
                  </code>
                </p>
                <p style={{ color: '#666', fontSize: '12px' }}>
                  请将此密码提供给用户，建议用户首次登录后修改密码。
                </p>
              </div>
            ),
            width: 500
          })
        }

        setCreateUserVisible(false)
        createUserForm.resetFields()
        loadUsers() // 刷新用户列表
      } else {
        message.error(response.message || '创建用户失败')
      }
    } catch (error) {
      console.error('创建用户失败:', error)
      message.error('创建用户失败')
    } finally {
      setCreateUserLoading(false)
    }
  }

  const columns: ColumnsType<User> = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 350,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <Avatar
            icon={<UserOutlined />}
            size={40}
            style={{
              backgroundColor: '#f5f5f5',
              color: '#999'
            }}
          />
          <div>
            <div
              style={{
                fontWeight: 500,
                fontSize: '14px',
                color: '#262626',
                marginBottom: 4
              }}
            >
              {record.nickname || record.email?.split('@')[0] || '未命名'}
            </div>
            <div
              style={{
                color: '#8c8c8c',
                fontSize: '12px',
                marginBottom: 4
              }}
            >
              {record.email}
            </div>
            <Tooltip title="点击复制用户ID">
              <code
                style={{
                  fontSize: '11px',
                  color: '#595959',
                  cursor: 'pointer',
                  padding: '2px 6px',
                  backgroundColor: '#f0f0f0',
                  borderRadius: 4,
                  border: '1px solid #e8e8e8'
                }}
                onClick={() => copyUserId(record.id)}
              >
                ID: {record.id.slice(0, 8)}...
              </code>
            </Tooltip>
          </div>
        </div>
      )
    },
    {
      title: '邮箱验证',
      key: 'emailVerified',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <Tag color={record.isEmailVerified ? 'green' : 'orange'} style={{ fontSize: '12px' }}>
          {record.isEmailVerified ? '已验证' : '未验证'}
        </Tag>
      )
    },
    {
      title: '注册时间',
      dataIndex: 'createdAt',
      width: 150,
      render: date => (
        <div style={{ fontSize: '13px', color: '#595959' }}>
          {dayjs(date).format('YYYY-MM-DD HH:mm')}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 220,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px'
            }}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<CrownOutlined />}
            onClick={() => handleSetMembership(record)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px',
              color: '#faad14'
            }}
          >
            设为会员
          </Button>
          <Button
            type="link"
            size="small"
            danger
            onClick={() => handleDeleteUser(record.id, record.email)}
            style={{
              padding: '2px 8px',
              height: 'auto',
              fontSize: '12px'
            }}
          >
            删除
          </Button>
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        用户管理
      </Title>

      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索邮箱或昵称"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={dates => {
              if (dates) {
                setSearchParams({
                  ...searchParams,
                  startDate: dates[0]?.toISOString(),
                  endDate: dates[1]?.toISOString()
                })
              } else {
                setSearchParams({
                  ...searchParams,
                  startDate: undefined,
                  endDate: undefined
                })
              }
            }}
          />

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateUserVisible(true)}>
            创建用户
          </Button>

          <Button icon={<ExportOutlined />}>导出</Button>
        </Space>
      </Card>

      <Card style={{ borderRadius: 8, border: '1px solid #f0f0f0' }}>
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          size="middle"
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `显示 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50'],
            style: { marginTop: 16 }
          }}
        />
      </Card>

      {/* 用户详情弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <EyeOutlined />
            <span>用户详情</span>
          </div>
        }
        open={userDetailVisible}
        onCancel={() => {
          setUserDetailVisible(false)
          setSelectedUser(null)
        }}
        footer={[
          <Button
            key="close"
            onClick={() => {
              setUserDetailVisible(false)
              setSelectedUser(null)
            }}
          >
            关闭
          </Button>
        ]}
        width={700}
      >
        {detailLoading ? (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '40px',
              color: '#999'
            }}
          >
            加载中...
          </div>
        ) : selectedUser ? (
          <div style={{ padding: '16px 0' }}>
            {/* 用户基本信息 */}
            <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: 16, marginBottom: 16 }}>
                    <Avatar
                      src={selectedUser.profile?.avatar || null}
                      icon={<UserOutlined />}
                      size={64}
                    />
                    <div>
                      <h3 style={{ margin: 0, marginBottom: 4 }}>
                        {selectedUser.profile?.nickname ||
                          selectedUser.email?.split('@')[0] ||
                          '未命名'}
                      </h3>
                      <p style={{ margin: 0, color: '#666' }}>{selectedUser.email}</p>
                      <div style={{ marginTop: 8, display: 'flex', alignItems: 'center', gap: 8 }}>
                        {getGenderDisplay(selectedUser.profile?.gender).icon}
                        <span style={{ color: '#666' }}>
                          {getGenderDisplay(selectedUser.profile?.gender).text}
                        </span>
                      </div>
                    </div>
                  </div>
                </Col>

                <Col span={12}>
                  <div>
                    <strong>用户ID：</strong>
                  </div>
                  <code
                    style={{
                      fontSize: '12px',
                      padding: '4px 8px',
                      backgroundColor: '#f5f5f5',
                      borderRadius: 4
                    }}
                  >
                    {selectedUser.id}
                  </code>
                </Col>

                <Col span={12}>
                  <div>
                    <strong>邮箱验证：</strong>
                  </div>
                  <Tag color={selectedUser.isEmailVerified ? 'green' : 'orange'}>
                    {selectedUser.isEmailVerified ? '已验证' : '未验证'}
                  </Tag>
                </Col>

                <Col span={12}>
                  <div>
                    <strong>注册时间：</strong>
                  </div>
                  <span>{dayjs(selectedUser.createdAt).format('YYYY-MM-DD HH:mm:ss')}</span>
                </Col>

                <Col span={12}>
                  <div>
                    <strong>最后更新：</strong>
                  </div>
                  <span>{dayjs(selectedUser.updatedAt).format('YYYY-MM-DD HH:mm:ss')}</span>
                </Col>
              </Row>
            </Card>

            {/* 会员信息 */}
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <CrownOutlined style={{ color: '#faad14' }} />
                  <span>会员信息</span>
                </div>
              }
              size="small"
              style={{ marginBottom: 16 }}
            >
              {selectedUser.subscription ? (
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div>
                      <strong>会员状态：</strong>
                    </div>
                    <Tag color={selectedUser.subscription.status === 'active' ? 'gold' : 'default'}>
                      <CrownOutlined style={{ marginRight: 4 }} />
                      {selectedUser.subscription.status === 'active' ? 'VIP会员' : '已过期'}
                    </Tag>
                  </Col>
                  <Col span={12}>
                    <div>
                      <strong>到期时间：</strong>
                    </div>
                    <span>
                      {selectedUser.subscription.endDate
                        ? dayjs(selectedUser.subscription.endDate).format('YYYY-MM-DD HH:mm:ss')
                        : '未设置'}
                    </span>
                  </Col>
                </Row>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                  <CrownOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                  <div>该用户尚未开通会员</div>
                </div>
              )}
            </Card>

            {/* 积分信息 */}
            <Card
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <DollarOutlined style={{ color: '#52c41a' }} />
                  <span>积分信息</span>
                </div>
              }
              size="small"
            >
              {selectedUser.points ? (
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <div>
                      <strong>可用积分：</strong>
                    </div>
                    <span style={{ fontSize: '18px', fontWeight: 600, color: '#52c41a' }}>
                      {selectedUser.points.balance?.toLocaleString() || 0}
                    </span>
                  </Col>
                  <Col span={12}>
                    <div>
                      <strong>已使用积分：</strong>
                    </div>
                    <span style={{ fontSize: '16px', color: '#999' }}>
                      {selectedUser.points.totalUsed?.toLocaleString() || 0}
                    </span>
                  </Col>
                </Row>
              ) : (
                <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                  <DollarOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                  <div>暂无积分记录</div>
                </div>
              )}
            </Card>
          </div>
        ) : (
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '40px',
              color: '#999'
            }}
          >
            加载失败，请重试
          </div>
        )}
      </Modal>

      {/* 设为会员弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <CrownOutlined style={{ color: '#faad14' }} />
            <span>设为会员</span>
          </div>
        }
        open={membershipModalVisible}
        onCancel={() => {
          setMembershipModalVisible(false)
          setSelectedUserForMembership(null)
          setSelectedPlanId('')
          setMembershipDuration(30)
        }}
        footer={null}
        width={700}
      >
        {selectedUserForMembership && (
          <div>
            {/* 用户信息卡片 */}
            <Card
              size="small"
              style={{
                marginBottom: 24,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none'
              }}
            >
              <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
                <Avatar
                  src={selectedUserForMembership.avatar || null}
                  icon={<UserOutlined />}
                  size={48}
                  style={{ flexShrink: 0 }}
                />
                <div style={{ color: 'white' }}>
                  <div style={{ fontSize: 16, fontWeight: 600, marginBottom: 4 }}>
                    {selectedUserForMembership.nickname || selectedUserForMembership.email}
                  </div>
                  <div style={{ opacity: 0.9, fontSize: 13 }}>
                    {selectedUserForMembership.email}
                  </div>
                </div>
              </div>
            </Card>

            <Form layout="vertical" form={membershipForm}>
              <Form.Item
                label={<span style={{ fontSize: 16, fontWeight: 500 }}>选择会员计划</span>}
              >
                {membershipPlans.length > 0 ? (
                  <Radio.Group
                    value={selectedPlanId}
                    onChange={e => setSelectedPlanId(e.target.value)}
                    style={{ width: '100%' }}
                  >
                    <Row gutter={[12, 12]}>
                      {membershipPlans.map(plan => (
                        <Col span={24} key={plan.id}>
                          <Card
                            hoverable
                            style={{
                              cursor: 'pointer',
                              border:
                                selectedPlanId === plan.id
                                  ? '2px solid #1890ff'
                                  : '1px solid #d9d9d9',
                              transition: 'all 0.3s ease'
                            }}
                            onClick={() => setSelectedPlanId(plan.id)}
                          >
                            <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
                              <Radio value={plan.id} style={{ flexShrink: 0 }} />
                              <div style={{ flex: 1 }}>
                                <div
                                  style={{
                                    display: 'flex',
                                    justifyContent: 'space-between',
                                    alignItems: 'center',
                                    marginBottom: 8
                                  }}
                                >
                                  <div style={{ fontSize: 16, fontWeight: 600, color: '#1f1f1f' }}>
                                    {plan.name}
                                  </div>
                                  <div style={{ fontSize: 18, fontWeight: 600, color: '#f56a00' }}>
                                    ¥{plan.price}
                                  </div>
                                </div>
                                <div style={{ marginBottom: 8 }}>
                                  <Tag color="blue">{plan.durationDays}天</Tag>
                                  <Tag color="green">包含{plan.pointsIncluded}积分</Tag>
                                </div>
                                {plan.description && (
                                  <div style={{ color: '#666', fontSize: 13, lineHeight: 1.4 }}>
                                    {plan.description}
                                  </div>
                                )}
                              </div>
                            </div>
                          </Card>
                        </Col>
                      ))}
                    </Row>
                  </Radio.Group>
                ) : (
                  <div
                    style={{
                      textAlign: 'center',
                      padding: '40px 20px',
                      color: '#999',
                      backgroundColor: '#fafafa',
                      borderRadius: 8,
                      border: '1px dashed #d9d9d9'
                    }}
                  >
                    <ExclamationCircleOutlined style={{ fontSize: 24, marginBottom: 16 }} />
                    <div style={{ fontSize: 16, marginBottom: 8 }}>暂无可用的会员计划</div>
                    <div style={{ fontSize: 14 }}>请先到会员管理页面创建会员计划</div>
                  </div>
                )}
              </Form.Item>

              <Divider />

              <Form.Item
                label={<span style={{ fontSize: 16, fontWeight: 500 }}>自定义订阅天数</span>}
                help="默认按计划天数，也可以自定义订阅时长"
              >
                <InputNumber
                  min={1}
                  max={365}
                  value={membershipDuration}
                  onChange={value => setMembershipDuration(value || 30)}
                  style={{ width: 200 }}
                  placeholder="请输入订阅天数"
                  addonAfter="天"
                />
              </Form.Item>

              <Form.Item style={{ marginBottom: 0, marginTop: 24 }}>
                <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 12 }}>
                  <Button
                    size="large"
                    onClick={() => {
                      setMembershipModalVisible(false)
                      setSelectedUserForMembership(null)
                      setSelectedPlanId('')
                      setMembershipDuration(30)
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    type="primary"
                    size="large"
                    loading={membershipLoading}
                    onClick={handleConfirmMembership}
                    icon={<CrownOutlined />}
                    disabled={!selectedPlanId}
                  >
                    确认设为会员
                  </Button>
                </div>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 创建用户弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <PlusOutlined style={{ color: '#1890ff' }} />
            <span>创建用户</span>
          </div>
        }
        open={createUserVisible}
        onCancel={() => {
          setCreateUserVisible(false)
          createUserForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form
          form={createUserForm}
          layout="vertical"
          onFinish={handleCreateUser}
          style={{ marginTop: 16 }}
        >
          <Form.Item
            label="邮箱地址"
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="请输入用户邮箱地址" size="large" />
          </Form.Item>

          <Form.Item label="昵称" name="nickname" help="可选，如不填写将使用邮箱前缀作为昵称">
            <Input placeholder="请输入用户昵称（可选）" size="large" />
          </Form.Item>

          <Form.Item label="密码" name="password" help="可选，如不填写将自动生成临时密码">
            <Input.Password placeholder="请输入密码（可选，留空自动生成）" size="large" />
          </Form.Item>

          <Divider />

          <Form.Item style={{ marginBottom: 0 }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', gap: 12 }}>
              <Button
                size="large"
                onClick={() => {
                  setCreateUserVisible(false)
                  createUserForm.resetFields()
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={createUserLoading}
                icon={<PlusOutlined />}
              >
                创建用户
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UserList

import { createMiddleware } from 'hono/factory'
import { getBestMatchLanguage, isLanguageImplemented, getFallbackLanguage } from '@/i18n/config'
import { getAllMessages, getMessage, type SupportedLanguage } from '@/i18n/messages'
import type { Env } from '@/types/env'

// 扩展 Hono 的 Context 类型
declare module 'hono' {
  interface ContextVariableMap {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string>) => string
  }
}

// 翻译函数类型
type TranslateFunction = (key: string, params?: Record<string, string>) => string

// 创建翻译函数
function createTranslateFunction(language: SupportedLanguage): TranslateFunction {
  return (key: string, params?: Record<string, string>) => {
    return getMessage(key, language, params)
  }
}

// 语言中间件
export const languageMiddleware = createMiddleware<{ Bindings: Env }>(async (c, next) => {
  // 获取 Accept-Language 头部
  const acceptLanguage = c.req.header('Accept-Language') || ''

  // 获取最佳匹配语言
  const detectedLanguage = getBestMatchLanguage(acceptLanguage)

  // 确保语言已实现，否则使用回退语言
  const language = isLanguageImplemented(detectedLanguage)
    ? detectedLanguage
    : getFallbackLanguage(detectedLanguage)

  // 设置语言变量
  c.set('language', language as SupportedLanguage)

  // 创建翻译函数
  const t = createTranslateFunction(language as SupportedLanguage)
  c.set('t', t)

  // 在响应头中设置语言信息
  c.header('Content-Language', language)

  // 记录语言检测结果（仅在开发环境）
  if (c.env.NODE_ENV === 'development') {
    console.log(`🌍 [Language] Accept-Language: ${acceptLanguage}`)
    console.log(`🌍 [Language] Detected: ${detectedLanguage}`)
    console.log(`🌍 [Language] Using: ${language}`)
  }

  await next()
})

// 导出辅助函数
export { createTranslateFunction, type TranslateFunction }

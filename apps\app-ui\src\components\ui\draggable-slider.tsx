import React, { useState, useRef, useCallback } from 'react'
import { motion } from 'framer-motion'

interface DraggableSliderProps {
  value: number
  min?: number
  max?: number
  onChange: (value: number) => void
  color: string
  className?: string
  showTicks?: boolean // 是否显示刻度线
  disabled?: boolean // 是否禁用
}

export const DraggableSlider: React.FC<DraggableSliderProps> = ({
  value,
  min = 1,
  max = 5,
  onChange,
  color,
  className = '',
  showTicks = true,
  disabled = false
}) => {
  const [isDragging, setIsDragging] = useState(false)
  const sliderRef = useRef<HTMLDivElement>(null)

  const getValueFromPosition = useCallback(
    (clientX: number) => {
      if (!sliderRef.current) return value

      const rect = sliderRef.current.getBoundingClientRect()
      const percentage = Math.max(0, Math.min(1, (clientX - rect.left) / rect.width))
      return Math.round(min + percentage * (max - min))
    },
    [min, max, value]
  )

  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (disabled) return

      // 阻止事件冒泡和默认行为，防止触发页面切换
      e.preventDefault()
      e.stopPropagation()

      setIsDragging(true)
      const newValue = getValueFromPosition(e.clientX)
      onChange(newValue)

      const handleMouseMove = (e: MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()
        const newValue = getValueFromPosition(e.clientX)
        onChange(newValue)
      }

      const handleMouseUp = (e: MouseEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setIsDragging(false)
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
      }

      document.addEventListener('mousemove', handleMouseMove, { passive: false })
      document.addEventListener('mouseup', handleMouseUp, { passive: false })
    },
    [getValueFromPosition, onChange, disabled]
  )

  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (disabled) return

      // 阻止事件冒泡和默认行为，防止触发页面切换
      e.preventDefault()
      e.stopPropagation()

      setIsDragging(true)
      const touch = e.touches[0]
      const newValue = getValueFromPosition(touch.clientX)
      onChange(newValue)

      const handleTouchMove = (e: TouchEvent) => {
        e.preventDefault()
        e.stopPropagation()
        const touch = e.touches[0]
        const newValue = getValueFromPosition(touch.clientX)
        onChange(newValue)
      }

      const handleTouchEnd = (e: TouchEvent) => {
        e.preventDefault()
        e.stopPropagation()
        setIsDragging(false)
        document.removeEventListener('touchmove', handleTouchMove)
        document.removeEventListener('touchend', handleTouchEnd)
      }

      document.addEventListener('touchmove', handleTouchMove, { passive: false })
      document.addEventListener('touchend', handleTouchEnd, { passive: false })
    },
    [getValueFromPosition, onChange, disabled]
  )

  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (disabled || isDragging) return

      // 阻止事件冒泡，防止触发页面切换
      e.preventDefault()
      e.stopPropagation()

      const newValue = getValueFromPosition(e.clientX)
      onChange(newValue)
    },
    [disabled, isDragging, getValueFromPosition, onChange]
  )

  const progressPercentage = ((value - min) / (max - min)) * 100
  const handlePosition = `calc(${progressPercentage}% - 14px)`

  return (
    <div className={`relative ${className}`}>
      {/* 滑块轨道 */}
      <div
        ref={sliderRef}
        className={`h-2 bg-[#53576a] rounded-md select-none ${
          disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
        }`}
        style={{
          touchAction: 'none', // 防止触摸时的默认滑动行为
          userSelect: 'none', // 防止文本选择
          WebkitUserSelect: 'none' // Safari 兼容
        }}
        onClick={handleClick}
      >
        {/* 进度条 */}
        <div
          className={`absolute top-0 h-2 rounded-md transition-all duration-200 ${
            disabled ? 'opacity-50' : ''
          }`}
          style={{
            backgroundColor: value === 0 ? '#53576a' : color,
            width: `${progressPercentage}%`
          }}
        />

        {/* 可拖动的滑块手柄 */}
        <motion.div
          className={`absolute top-[-3px] w-7 h-3.5 backdrop-blur-sm rounded-lg border flex items-center justify-center select-none ${
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-grab active:cursor-grabbing'
          } ${value === 0 ? 'bg-gray-500/70 border-gray-400/50' : 'bg-white/70 border-white/50'}`}
          style={{
            left: handlePosition,
            touchAction: 'none', // 防止触摸时的默认滑动行为
            userSelect: 'none', // 防止文本选择
            WebkitUserSelect: 'none' // Safari 兼容
          }}
          onMouseDown={handleMouseDown}
          onTouchStart={handleTouchStart}
          whileDrag={{ scale: disabled ? 1 : 1.1 }}
          animate={{ scale: isDragging && !disabled ? 1.1 : 1 }}
          transition={{ duration: 0.1 }}
        >
          <div className="flex gap-1 pointer-events-none">
            <div
              className={`w-px h-1 rounded-full ${
                value === 0 ? 'bg-gray-300' : disabled ? 'bg-gray-400' : 'bg-white'
              }`}
            ></div>
            <div
              className={`w-px h-1.5 rounded-full ${
                value === 0 ? 'bg-gray-300' : disabled ? 'bg-gray-400' : 'bg-white'
              }`}
            ></div>
            <div
              className={`w-px h-1 rounded-full ${
                value === 0 ? 'bg-gray-300' : disabled ? 'bg-gray-400' : 'bg-white'
              }`}
            ></div>
          </div>
        </motion.div>
      </div>

      {/* 刻度线 */}
      {showTicks && (
        <div className="flex justify-between mt-2">
          {Array.from({ length: max - min + 1 }, (_, i) => {
            const level = min + i
            return (
              <div
                key={level}
                className={`w-px h-1 rounded-full transition-colors ${
                  disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
                }`}
                onClick={() => !disabled && onChange(level)}
                style={{
                  backgroundColor:
                    level <= value ? (value === 0 && level === 0 ? '#53576a' : color) : '#9c9fab'
                }}
              />
            )
          })}
        </div>
      )}
    </div>
  )
}

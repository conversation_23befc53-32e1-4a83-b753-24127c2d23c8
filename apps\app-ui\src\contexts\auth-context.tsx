import React, { createContext, useContext, useEffect, useState, useCallback } from 'react'
import { type Session, type User, authApi, type AuthStatus } from '../api/auth'
import { useVoiceModelsStore } from '@/stores/voice-models-store'
import { useUserProfileStore } from '@/stores/user-profile-store'

// 认证上下文接口
interface AuthContextType {
  session: Session | null
  status: AuthStatus
  user: User | null
  login: (email: string, password: string) => Promise<boolean>
  loginWithCode: (email: string, code: string) => Promise<boolean>
  register: (email: string, password: string, name?: string) => Promise<boolean>
  sendCode: (email: string) => Promise<boolean>
  sendLoginCode: (email: string) => Promise<boolean>
  verifyCode: (
    email: string,
    code: string,
    name?: string,
    password?: string,
    inviteCode?: string
  ) => Promise<{ success: boolean; needsProfile?: boolean }>
  logout: () => Promise<void>
  error?: string
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证上下文提供者接口
interface AuthProviderProps {
  children: React.ReactNode
}

// Token存储键名
const ACCESS_TOKEN_KEY = 'access_token'
const REFRESH_TOKEN_KEY = 'refresh_token'
const TOKEN_EXPIRES_AT_KEY = 'token_expires_at'

// 令牌刷新提前时间（5分钟）
const REFRESH_MARGIN = 5 * 60 * 1000 // 5分钟，以毫秒为单位

// 认证上下文提供者
export function AuthProvider({ children }: AuthProviderProps) {
  const [session, setSession] = useState<Session | null>(null)
  const [status, setStatus] = useState<AuthStatus>('loading')
  const [error, setError] = useState<string | undefined>(undefined)

  // 获取声音模型store
  const { preloadVoiceData } = useVoiceModelsStore()

  // 获取用户资料store
  const { fetchUserProfile } = useUserProfileStore()

  // 保存token到localStorage
  const saveTokens = (accessToken: string, refreshToken: string, expiresAt: number) => {
    localStorage.setItem(ACCESS_TOKEN_KEY, accessToken)
    localStorage.setItem(REFRESH_TOKEN_KEY, refreshToken)
    localStorage.setItem(TOKEN_EXPIRES_AT_KEY, expiresAt.toString())
  }

  // 从localStorage获取token
  const getAccessToken = (): string | null => {
    return localStorage.getItem(ACCESS_TOKEN_KEY)
  }

  // 获取令牌过期时间
  const getTokenExpiresAt = (): number | null => {
    const expiresAt = localStorage.getItem(TOKEN_EXPIRES_AT_KEY)
    return expiresAt ? parseInt(expiresAt) : null
  }

  // 检查令牌是否即将过期
  const isTokenExpiringSoon = (): boolean => {
    const expiresAt = getTokenExpiresAt()
    if (!expiresAt) return true

    const now = Date.now()
    const expiresAtMs = expiresAt * 1000
    return expiresAtMs - now <= REFRESH_MARGIN
  }

  // 清除token
  const clearTokens = () => {
    localStorage.removeItem(ACCESS_TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
    localStorage.removeItem(TOKEN_EXPIRES_AT_KEY)
  }

  // 刷新令牌
  const refreshTokens = useCallback(async (): Promise<boolean> => {
    try {
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY)
      if (!refreshToken) {
        console.log('没有刷新令牌，无法刷新')
        return false
      }

      console.log('正在刷新访问令牌...')

      // 使用 authApi 刷新令牌
      const response = await authApi.refreshToken(refreshToken)

      if (response.success && response.session) {
        console.log('令牌刷新成功')

        // 保存新的令牌
        saveTokens(
          response.session.access_token,
          response.session.refresh_token,
          response.session.expires_at
        )

        // 更新会话
        const newSession: Session = {
          user: response.session.user,
          expires: new Date(response.session.expires_at * 1000).toISOString()
        }
        setSession(newSession)
        setStatus('authenticated')

        return true
      } else {
        console.log('令牌刷新失败:', response.message)
        return false
      }
    } catch (error) {
      console.error('刷新令牌失败:', error)
      return false
    }
  }, [])

  // 自动刷新令牌的定时器
  useEffect(() => {
    if (status !== 'authenticated' || !session) return

    const checkAndRefreshToken = async () => {
      if (isTokenExpiringSoon()) {
        console.log('令牌即将过期，尝试刷新...')
        const refreshed = await refreshTokens()

        if (!refreshed) {
          console.log('令牌刷新失败，用户需要重新登录')
          clearTokens()
          setSession(null)
          setStatus('unauthenticated')
        }
      }
    }

    // 立即检查一次
    checkAndRefreshToken()

    // 每分钟检查一次令牌状态
    const interval = setInterval(checkAndRefreshToken, 60 * 1000)

    return () => clearInterval(interval)
  }, [status, session, refreshTokens])

  // 预加载数据（用户认证成功后）
  useEffect(() => {
    if (status === 'authenticated') {
      // 异步预加载声音数据，不阻塞用户界面
      preloadVoiceData().catch(error => {
        console.warn('预加载声音数据失败:', error)
      })

      // 异步预加载用户资料，不阻塞用户界面
      fetchUserProfile().catch(error => {
        console.warn('预加载用户资料失败:', error)
      })
    }
  }, [status, preloadVoiceData, fetchUserProfile])

  // 获取当前会话
  useEffect(() => {
    const getSession = async () => {
      try {
        console.log('开始获取会话状态...')
        setStatus('loading')

        // 检查本地是否有token
        const accessToken = getAccessToken()
        if (!accessToken) {
          console.log('本地没有访问令牌')
          setSession(null)
          setStatus('unauthenticated')
          return
        }

        // 检查令牌是否过期
        if (isTokenExpiringSoon()) {
          console.log('令牌即将过期，尝试刷新...')
          const refreshed = await refreshTokens()

          if (!refreshed) {
            console.log('令牌刷新失败，清除本地token')
            clearTokens()
            setSession(null)
            setStatus('unauthenticated')
            return
          }
        }

        // 尝试获取会话
        const userSession = await authApi.getSession()
        console.log('获取到会话数据:', userSession)

        // 更新状态
        if (userSession) {
          console.log('收到有效会话，email:', userSession.user?.email)
          setSession(userSession)
          setStatus('authenticated')
          console.log('状态已更新为: authenticated')
        } else {
          console.log('未收到有效会话，清除本地token')
          clearTokens()
          setSession(null)
          setStatus('unauthenticated')
          console.log('状态已更新为: unauthenticated')
        }
      } catch (err) {
        console.error('获取会话时出错:', err)
        clearTokens()
        setSession(null)
        setStatus('unauthenticated')
        setError('获取会话失败')
      }
    }

    // 加载会话
    getSession()
  }, [refreshTokens])

  // 登录方法
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log('开始登录流程...')
      setError(undefined)
      const response = await authApi.login({ email, password })

      if (response.success && response.session) {
        console.log('登录成功，用户信息:', response.session.user)

        // 保存token
        saveTokens(
          response.session.access_token,
          response.session.refresh_token,
          response.session.expires_at
        )

        // 构造会话对象
        const session: Session = {
          user: response.session.user,
          expires: new Date(response.session.expires_at * 1000).toISOString()
        }
        setSession(session)
        setStatus('authenticated')
        return true
      } else {
        console.log('登录失败:', response.message)
        setError(response.message || '登录失败')
        setStatus('unauthenticated')
        return false
      }
    } catch (err) {
      console.error('登录请求异常:', err)
      setError('登录请求失败')
      setStatus('unauthenticated')
      return false
    }
  }

  // 验证码登录方法
  const loginWithCode = async (email: string, code: string): Promise<boolean> => {
    try {
      console.log('开始验证码登录...')
      setError(undefined)
      const response = await authApi.loginWithCode({ email, code })

      if (response.success && response.session) {
        console.log('验证码登录成功，用户信息:', response.session.user)

        // 保存token
        saveTokens(
          response.session.access_token,
          response.session.refresh_token,
          response.session.expires_at
        )

        // 构造会话对象
        const session: Session = {
          user: response.session.user,
          expires: new Date(response.session.expires_at * 1000).toISOString()
        }
        setSession(session)
        setStatus('authenticated')
        return true
      } else {
        console.log('验证码登录失败:', response.message)
        setError(response.message || '登录失败')
        setStatus('unauthenticated')
        return false
      }
    } catch (err) {
      console.error('验证码登录请求异常:', err)
      setError('登录请求失败')
      setStatus('unauthenticated')
      return false
    }
  }

  // 注册方法
  const register = async (email: string, password: string, name?: string): Promise<boolean> => {
    try {
      console.log('开始注册流程...')
      setError(undefined)
      const response = await authApi.register({ email, password, name })

      if (response.success) {
        console.log('注册成功，尝试登录...')

        // 注册成功后延迟一下再登录，给后端足够时间处理
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 尝试登录，最多重试 3 次
        let loginSuccess = false
        let attempts = 0
        while (!loginSuccess && attempts < 3) {
          attempts++
          console.log(`登录尝试 ${attempts}/3...`)

          try {
            loginSuccess = await login(email, password)
            if (loginSuccess) {
              console.log('登录成功!')
              break
            } else {
              console.log('登录失败，等待后重试...')
              await new Promise(resolve => setTimeout(resolve, 1000))
            }
          } catch (loginErr) {
            console.error(`登录尝试 ${attempts} 失败:`, loginErr)
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        }

        // 即使登录失败也返回注册成功，因为用户账号确实已创建
        return true
      } else {
        console.log('注册失败:', response.message)
        setError(response.message || '注册失败')
        return false
      }
    } catch (err) {
      console.error('注册请求异常:', err)
      setError('注册请求失败')
      return false
    }
  }

  // 发送验证码方法
  const sendCode = async (email: string): Promise<boolean> => {
    try {
      console.log('开始发送验证码...')
      setError(undefined)
      const response = await authApi.sendCode({ email })

      if (response.success) {
        console.log('验证码发送成功')
        return true
      } else {
        console.log('验证码发送失败:', response.message)
        setError(response.message || '发送验证码失败')
        return false
      }
    } catch (err) {
      console.error('发送验证码请求异常:', err)
      setError('发送验证码请求失败')
      return false
    }
  }

  // 发送登录码方法
  const sendLoginCode = async (email: string): Promise<boolean> => {
    try {
      console.log('开始发送登录码...')
      setError(undefined)
      const response = await authApi.sendLoginCode({ email })

      if (response.success) {
        console.log('登录码发送成功')
        return true
      } else {
        console.log('登录码发送失败:', response.message)
        setError(response.message || '发送登录码失败')
        return false
      }
    } catch (err) {
      console.error('发送登录码请求异常:', err)
      setError('发送登录码请求失败')
      return false
    }
  }

  // 验证验证码方法
  const verifyCode = async (
    email: string,
    code: string,
    name?: string,
    password?: string,
    inviteCode?: string
  ): Promise<{ success: boolean; needsProfile?: boolean }> => {
    try {
      console.log('开始验证验证码...')
      setError(undefined)
      const response = await authApi.verifyCode({ email, code, name, password, inviteCode })

      if (response.success && response.session) {
        console.log('验证码验证成功，用户信息:', response.session.user)

        // 保存token
        saveTokens(
          response.session.access_token,
          response.session.refresh_token,
          response.session.expires_at
        )

        // 构造会话对象
        const session: Session = {
          user: response.session.user,
          expires: new Date(response.session.expires_at * 1000).toISOString()
        }
        setSession(session)
        setStatus('authenticated')

        // 新注册的用户需要完善个人资料
        return { success: true, needsProfile: true }
      } else {
        console.log('验证码验证失败:', response.message)
        setError(response.message || '验证失败')
        setStatus('unauthenticated')
        return { success: false }
      }
    } catch (err) {
      console.error('验证验证码请求异常:', err)
      setError('验证请求失败')
      setStatus('unauthenticated')
      return { success: false }
    }
  }

  // 注销方法
  const logout = async (): Promise<void> => {
    try {
      await authApi.logout()
      clearTokens()
      setSession(null)
      setStatus('unauthenticated')

      // 使用统一的缓存清理工具
      const { clearAllUserCaches } = await import('@/utils/cache-cleaner')
      await clearAllUserCaches()

      console.log('🧹 [Auth] 用户登出，已清除所有缓存')
    } catch (err) {
      setError('注销失败')

      // 如果统一清理失败，尝试快速清理关键缓存
      try {
        const { clearCriticalCaches } = await import('@/utils/cache-cleaner')
        clearCriticalCaches()
      } catch (fallbackErr) {
        console.error('❌ [Auth] 备用清理也失败:', fallbackErr)
      }
    }
  }

  // 组装上下文值
  const value = {
    session,
    status,
    user: session?.user || null,
    login,
    loginWithCode,
    register,
    sendCode,
    sendLoginCode,
    verifyCode,
    logout,
    error
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

// 使用认证上下文的钩子
export function useAuth() {
  const context = useContext(AuthContext)

  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }

  return context
}

// 导出获取token的工具函数
export const getAccessToken = (): string | null => {
  return localStorage.getItem(ACCESS_TOKEN_KEY)
}

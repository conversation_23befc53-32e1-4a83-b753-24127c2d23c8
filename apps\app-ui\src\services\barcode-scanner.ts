import { CapacitorBarcodeScanner } from '@capacitor/barcode-scanner'
import { Capacitor } from '@capacitor/core'

export interface ScanResult {
  content: string
  cancelled: boolean
}

export class BarcodeScannerService {
  /**
   * 检查设备是否支持二维码扫描
   */
  static async isSupported(): Promise<boolean> {
    if (!Capacitor.isNativePlatform()) {
      return false
    }

    // 对于新版的 @capacitor/barcode-scanner，我们假设在原生平台都支持
    return true
  }

  /**
   * 开始扫描二维码
   */
  static async startScan(): Promise<ScanResult> {
    try {
      // 使用新 API 进行扫描，配置为 QR 码扫描
      const result = await CapacitorBarcodeScanner.scanBarcode({
        hint: 0, // QR_CODE
        scanInstructions: '将二维码置于扫描框内',
        scanButton: false,
        scanText: '扫描中...',
        cameraDirection: 1 // BACK camera
      })

      return {
        content: result.ScanResult || '',
        cancelled: !result.ScanResult
      }
    } catch (error) {
      console.error('二维码扫描失败:', error)
      throw error
    }
  }

  /**
   * Web 平台的模拟扫描（用于开发测试）
   */
  static async mockScan(): Promise<ScanResult> {
    return new Promise(resolve => {
      setTimeout(() => {
        console.log('mockScan')
        // 模拟扫描成功，返回一个示例设备码
        const mockDeviceCodes = ['1583', '6842', '2137', '9876', '5432']
        const randomCode = mockDeviceCodes[Math.floor(Math.random() * mockDeviceCodes.length)]

        resolve({
          content: randomCode,
          cancelled: false
        })
      }, 2000)
    })
  }

  /**
   * 统一的扫描入口（根据平台选择真实扫描或模拟扫描）
   */
  static async scan(): Promise<ScanResult> {
    if (Capacitor.isNativePlatform()) {
      return await this.startScan()
    } else {
      // Web 平台使用模拟扫描
      return await this.mockScan()
    }
  }
}

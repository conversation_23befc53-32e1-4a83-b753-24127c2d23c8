// 核心类型定义

// 导出所有类型
export * from './chat'
export * from './multimodal'
export * from './memory'

// 基础环境类型
export interface LangChainEnv {
  XAI_API_KEY: string
  // 其他环境变量
}

// 用户类型（复用现有定义）
export type UserType = 'regular' | 'premium' | 'enterprise'

// 角色类型（复用现有定义）
export interface CharacterType {
  id: string
  name: string
  gender: 'male' | 'female' | 'other'
  age: string
  relationship: string
  ethnicity: string
  eyeColor: string
  hairStyle: string
  hairColor: string
  bodyType: string
  breastSize?: string
  buttSize?: string
  personality: string
  clothing: string
  voice: string
  imageUrl: string
}

// 配置类型
export interface LangChainConfig {
  // 模型配置
  defaultModel: string

  // 记忆配置（预留）
  memory?: {
    bufferSize: number
    vectorDimension: number
    similarityThreshold: number
  }

  // 多模态配置
  multimodal: {
    enableImage: boolean
    enableAudio: boolean
    enableDevice: boolean
  }

  // 流式配置
  streaming: {
    chunkSize: number
    delayMs: number
  }
}

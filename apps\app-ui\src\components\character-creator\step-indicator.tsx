import React from 'react'
import { Button, Progress, Chip } from '@heroui/react'
import { Icon } from '@iconify/react'

// 定义步骤类型
type Step = {
  id: number
  title: string
  description: string
}

// 组件属性
interface StepIndicatorProps {
  steps: Step[]
  currentStep: number
  onStepClick: (step: number) => void
  highestStepVisited?: number // 添加已访问的最高步骤
  completedSteps?: boolean[] // 添加已完成的步骤数组
}

export default function StepIndicator({
  steps,
  currentStep,
  onStepClick,
  highestStepVisited = 1, // 默认为1
  completedSteps = [] // 默认为空数组
}: StepIndicatorProps) {
  // 只显示当前步骤及其相邻的步骤，一次显示最多5个
  const visibleSteps = React.useMemo(() => {
    // 总步骤数
    const totalSteps = steps.length

    // 如果步骤总数少于等于5个，直接显示全部
    if (totalSteps <= 5) {
      return steps
    }

    // 根据当前步骤计算要显示的索引范围
    // 优先显示当前步骤及其后面的步骤
    if (currentStep <= 3) {
      // 如果当前步骤靠前，显示前5个
      return steps.slice(0, 5)
    } else if (currentStep >= totalSteps - 2) {
      // 如果当前步骤靠后，显示后5个
      return steps.slice(totalSteps - 5, totalSteps)
    } else {
      // 否则以当前步骤为中心，显示前后各2个
      return steps.slice(currentStep - 3, currentStep + 2)
    }
  }, [steps, currentStep])

  // 判断是否需要显示导航按钮
  const showPrevButton = currentStep > 3
  const showNextButton = currentStep < steps.length - 2

  // 计算完成进度
  const completedCount = completedSteps.filter(Boolean).length
  const progressValue = (completedCount / steps.length) * 100

  return (
    <div>
      <div className="flex items-center justify-between mb-2">
        <Chip size="sm" variant="flat" color="primary">
          第 {currentStep}/{steps.length} 步
        </Chip>

        {/* 显示当前步骤名称 */}
        <div className="text-sm font-medium text-primary">{steps[currentStep - 1].title}</div>
      </div>

      {/* 进度条 */}
      <Progress
        value={progressValue}
        color="primary"
        size="sm"
        className="mb-4"
        classNames={{
          track: 'bg-default-200',
          indicator: 'bg-gradient-to-r from-primary to-secondary'
        }}
      />

      <div className="relative flex items-center">
        {/* 导航按钮 - 前一组 */}
        {showPrevButton && (
          <Button
            isIconOnly
            size="sm"
            variant="flat"
            color="default"
            className="absolute left-0 z-20 -translate-x-1"
            onPress={() => onStepClick(Math.max(1, currentStep - 5))}
            aria-label="查看前面的步骤"
          >
            <Icon icon="solar:arrow-left-linear" width={16} />
          </Button>
        )}

        {/* 步骤指示器容器 */}
        <div className="flex-1 flex justify-center items-center relative">
          {/* 连接线 */}
          <div className="absolute top-1/2 left-4 right-4 h-0.5 border-t border-dashed border-default-300 -translate-y-1/2 z-0" />

          {/* 可见步骤按钮 */}
          <div className="flex justify-between items-center w-full relative z-10 px-4">
            {visibleSteps.map(step => {
              // 计算状态: 当前、已完成、未完成
              const isActive = step.id === currentStep
              const isCompleted = completedSteps[step.id - 1] // 使用completedSteps数组
              const isClickable = step.id <= highestStepVisited // 使用highestStepVisited判断是否可点击

              return (
                <Button
                  key={step.id}
                  isIconOnly
                  size="sm"
                  variant={isActive ? 'solid' : isCompleted ? 'flat' : 'bordered'}
                  color={isActive ? 'primary' : isCompleted ? 'success' : 'default'}
                  className={`
                    transition-all duration-300 relative
                    ${isActive ? 'scale-110 shadow-lg' : ''}
                    ${!isClickable ? 'opacity-40 cursor-not-allowed' : ''}
                  `}
                  onPress={() => isClickable && onStepClick(step.id)}
                  isDisabled={!isClickable}
                >
                  {isCompleted ? (
                    <Icon icon="solar:check-circle-bold" width={16} />
                  ) : (
                    <span className="text-xs font-medium">{step.id}</span>
                  )}

                  {/* 步骤名称 - 只显示当前步骤的名称 */}
                  {isActive && (
                    <div className="absolute top-full mt-2 whitespace-nowrap text-xs font-medium text-primary hidden sm:block">
                      {step.title}
                    </div>
                  )}
                </Button>
              )
            })}
          </div>
        </div>

        {/* 导航按钮 - 下一组 */}
        {showNextButton && (
          <Button
            isIconOnly
            size="sm"
            variant="flat"
            color="default"
            className="absolute right-0 z-20 translate-x-1"
            onPress={() => onStepClick(Math.min(steps.length, currentStep + 5))}
            aria-label="查看后面的步骤"
          >
            <Icon icon="solar:arrow-right-linear" width={16} />
          </Button>
        )}
      </div>
    </div>
  )
}

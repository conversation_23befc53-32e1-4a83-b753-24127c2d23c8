import { useState, useCallback, useRef } from 'react'
import { tts3Service } from '@/api/services/tts3'
import { useRoleStore } from '@/stores/role-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'

export type RealStreamStatus =
  | 'idle'
  | 'connecting'
  | 'buffering'
  | 'playing'
  | 'completed'
  | 'failed'

export interface RealStreamState {
  status: RealStreamStatus
  error: string | null
  bufferProgress: number // 缓冲进度 0-100
  playbackProgress: number // 播放进度 0-100
  duration: number // 总时长（秒）
  currentTime: number // 当前播放时间（秒）
}

export const useRealStreamAudio = () => {
  const [state, setState] = useState<RealStreamState>({
    status: 'idle',
    error: null,
    bufferProgress: 0,
    playbackProgress: 0,
    duration: 0,
    currentTime: 0
  })

  const abortControllerRef = useRef<AbortController | null>(null)
  const audioElementRef = useRef<HTMLAudioElement | null>(null)

  // 获取当前角色信息
  const { currentRole } = useRoleStore()

  // 获取用户角色缓存
  const { getUserCharacterById } = useUserCharactersStore()

  // 重置状态
  const reset = useCallback(() => {
    // 取消正在进行的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
      abortControllerRef.current = null
    }

    // 清理音频元素
    if (audioElementRef.current) {
      audioElementRef.current.pause()

      if (audioElementRef.current.src.startsWith('blob:')) {
        URL.revokeObjectURL(audioElementRef.current.src)
      }
      audioElementRef.current.src = ''
      audioElementRef.current = null
    }

    setState({
      status: 'idle',
      error: null,
      bufferProgress: 0,
      playbackProgress: 0,
      duration: 0,
      currentTime: 0
    })
  }, [])

  // 获取当前角色的声音模型ID
  const getCurrentVoice = useCallback(async (): Promise<string | undefined> => {
    if (!currentRole) {
      console.warn('当前没有选择角色，使用默认声音')
      return undefined
    }

    try {
      const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

      if (uuidPattern.test(currentRole.id)) {
        // 自定义角色，优先从缓存获取
        const cachedCharacter = getUserCharacterById(currentRole.id)

        if (cachedCharacter) {
          console.log('🎵 [StreamAudio] 从缓存获取角色声音信息:', cachedCharacter.name)
          return cachedCharacter.voiceModelId || cachedCharacter.voice
        }

        console.warn('⚠️ [StreamAudio] 缓存中未找到角色信息，使用默认声音:', currentRole.id)
        return 'soft'
      } else {
        // 系统角色，使用默认声音映射
        const systemVoiceMap: Record<string, string> = {
          ruyun: 'soft',
          jinlin: 'sweet',
          chenqian: 'elegant',
          ruyan: 'sweet',
          heliping: 'soft'
        }
        const voice = systemVoiceMap[currentRole.id] || 'soft'
        console.log('🎵 [StreamAudio] 系统角色声音映射:', currentRole.id, '->', voice)
        return voice
      }
    } catch (error) {
      console.error('获取角色声音信息失败:', error)
      return 'soft'
    }
  }, [currentRole, getUserCharacterById])

  // 简化的流式播放音频
  const generateAndPlayStream = useCallback(
    async (text: string, messageId?: string, chatId?: string, voiceOverride?: string) => {
      try {
        // 防止重复请求
        if (state.status === 'connecting' || state.status === 'buffering') {
          console.log('⚠️ 请求正在进行中，忽略重复请求')
          return null
        }

        // 重置状态
        reset()

        setState({
          status: 'connecting',
          error: null,
          bufferProgress: 0,
          playbackProgress: 0,
          duration: 0,
          currentTime: 0
        })

        // 获取声音参数
        let voiceModelId = voiceOverride
        if (!voiceModelId) {
          voiceModelId = await getCurrentVoice()
        }

        console.log('🎵 开始流式音频生成:', { text: text.substring(0, 50), voiceModelId })

        // 创建 AbortController
        const abortController = new AbortController()
        abortControllerRef.current = abortController

        // 获取流式音频数据
        const response = await tts3Service.generateStreamAudio(
          {
            text,
            messageId,
            chatId,
            voiceModelId
          },
          abortController.signal
        )

        if (!response.ok) {
          const contentType = response.headers.get('content-type')
          if (contentType?.includes('application/json')) {
            const errorData = await response.json()
            throw new Error(errorData.message || `HTTP ${response.status}`)
          } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`)
          }
        }

        // 简化处理：直接使用 Blob URL
        console.log('📥 开始接收音频流...')
        setState(prev => ({ ...prev, status: 'buffering' }))

        // 收集所有音频数据
        const chunks: Uint8Array[] = []
        const reader = response.body!.getReader()
        const contentLength = parseInt(response.headers.get('content-length') || '0')
        let receivedLength = 0

        try {
          while (true) {
            const { done, value } = await reader.read()

            if (done) break

            if (value) {
              chunks.push(value)
              receivedLength += value.length

              // 更新进度
              if (contentLength > 0) {
                const progress = (receivedLength / contentLength) * 100
                setState(prev => ({ ...prev, bufferProgress: Math.min(progress, 95) }))
              }
            }
          }
        } finally {
          reader.releaseLock()
        }

        // 合并音频数据并创建 Blob URL
        const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0)
        const audioData = new Uint8Array(totalLength)
        let offset = 0

        for (const chunk of chunks) {
          audioData.set(chunk, offset)
          offset += chunk.length
        }

        const audioBlob = new Blob([audioData], { type: 'audio/mpeg' })
        const audioUrl = URL.createObjectURL(audioBlob)

        // 创建音频元素并播放
        const audio = new Audio(audioUrl)
        audioElementRef.current = audio

        // 设置事件监听器
        audio.addEventListener('loadedmetadata', () => {
          setState(prev => ({
            ...prev,
            duration: audio.duration || 0,
            bufferProgress: 100
          }))
        })

        audio.addEventListener('timeupdate', () => {
          const currentTime = audio.currentTime
          const duration = audio.duration || 0

          setState(prev => ({
            ...prev,
            currentTime,
            playbackProgress: duration > 0 ? (currentTime / duration) * 100 : 0
          }))
        })

        audio.addEventListener('ended', () => {
          setState(prev => ({ ...prev, status: 'completed' }))
        })

        audio.addEventListener('error', error => {
          console.error('音频播放错误:', error)
          setState(prev => ({
            ...prev,
            status: 'failed',
            error: '音频播放失败'
          }))
        })

        // 开始播放
        setState(prev => ({ ...prev, status: 'playing' }))
        await audio.play()

        console.log('✅ 音频开始播放')
        return audio
      } catch (error) {
        console.error('MediaSource 流式播放失败:', error)

        // 如果是用户取消，不更新为失败状态
        if (error instanceof Error && error.name === 'AbortError') {
          return null
        }

        setState({
          status: 'failed',
          error: error instanceof Error ? error.message : '播放失败',
          bufferProgress: 0,
          playbackProgress: 0,
          duration: 0,
          currentTime: 0
        })
        throw error
      } finally {
        abortControllerRef.current = null
      }
    },
    [getCurrentVoice, reset]
  )

  // 暂停播放
  const pause = useCallback(() => {
    if (audioElementRef.current) {
      audioElementRef.current.pause()
    }
  }, [])

  // 继续播放
  const play = useCallback(() => {
    if (audioElementRef.current) {
      audioElementRef.current.play().catch(console.error)
    }
  }, [])

  // 取消生成
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    reset()
  }, [reset])

  return {
    state,
    generateAndPlayStream,
    pause,
    play,
    cancel,
    reset,
    currentVoice: currentRole?.name
  }
}

/**
 * 支付服务工厂
 * 统一创建支付服务实例
 */

import { RealPaymentService } from './real-payment-service';
import type { Env } from '@/types/env';

/**
 * 创建支付服务实例
 * 统一使用真实支付服务
 */
export function createPaymentService(env: Env) {
  console.log('🏦 [PAYMENT-FACTORY] 创建真实支付服务');
  return new RealPaymentService(env);
}

/**
 * 获取支付服务调试信息
 */
export function getPaymentServiceInfo() {
  return {
    service: 'RealPaymentService',
    supportedMethods: ['alipay'],
    features: ['真实支付', '订单查询', '回调处理'],
    note: '生产环境支付服务',
  };
}

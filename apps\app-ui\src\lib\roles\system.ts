// 角色类型定义
export interface Role {
  role: string;
  character: string;
  initialScene: string;
  description: string;
  age: number;
  avatar?: string;
}

/**
 * 角色列表
 */
export const roles: Role[] = [
  {
    role: 'ruyun',
    character: '如云',
    age: 30,
    initialScene: '公司顶层办公室，夜幕降临后的加班相遇。',
    description:
      '如云是一位年轻有为的职场女性，她聪明、干练，有着敏锐的商业嗅觉和出色的管理能力。她对工作充满热情，追求卓越，是公司的核心骨干。',
    avatar: '/images/roles/ruyun.png',
  },
  {
    role: 'chenqian',
    character: '陈倩',
    age: 24,
    initialScene: '高档餐厅的包厢，久别重逢的晚餐。',
    description:
      '餐厅包厢内，烛光摇曳，红酒散发淡淡香气。陈倩穿着优雅的连衣裙，坐在桌边，眼神复杂。这个场景适合侯龙涛用旧情回忆切入，挑起她的情感波澜。',
    avatar: '/images/roles/chenqian.png',
  },
  {
    role: 'ruyan',
    character: '茹嫣',
    age: 20,
    initialScene: '豪华酒店的酒吧，深夜的私密交谈。',
    description:
      '酒店酒吧灯光昏暗，爵士乐低回，吧台边只有几人。茹嫣身穿紧身礼服，手持鸡尾酒，眼神挑逗。',
    avatar: '/images/roles/ruyan.png',
  },
  {
    role: 'heliping',
    character: '何莉萍',
    age: 32,
    initialScene: '老胡同的茶肆，傍晚的旧情重温。',
    description: '茶肆昏黄灯光，何莉萍轻抚茶杯，眼神柔情。',
    avatar: '/images/roles/heliping.png',
  },
];

/**
 * 获取角色
 * @param role 角色名称
 * @returns 角色信息
 */
export const getRole = (role: string) => {
  return roles.find((r) => r.role === role) || roles[0];
};

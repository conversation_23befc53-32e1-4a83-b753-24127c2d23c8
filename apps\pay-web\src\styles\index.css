@import 'tailwindcss';
@import 'tw-animate-css';

/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap')
layer(utilities); */

@plugin './hero.ts';

@source '../../../../node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}';
@custom-variant dark (&:is(.dark *));
@config '../../tailwind.config.js';

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}

@layer utilities {
  /**
 * 支付页面全局样式
 */

  * {
    box-sizing: border-box;
  }

  body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu',
      'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    background-color: #f9fafb;
  }

  #root {
    min-height: 100vh;
  }

  /* 确保HeroUI组件样式正确加载 */
}

/* 移动端适配 */
@media (max-width: 640px) {
  body {
    font-size: 14px;
  }
}

/* 确保按钮和链接在移动端易于点击 */
button,
a {
  min-height: 44px;
}

/* 防止页面在移动端被拖拽 */
html,
body {
  overscroll-behavior: none;
}

/* 隐藏滚动条但保持滚动功能 */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

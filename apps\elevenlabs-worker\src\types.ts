// ElevenLabs V3 逆向 API 类型定义

export interface ElevenLabsAccount {
  id: string
  email: string
  password: string
  sessionToken?: string
  refreshToken?: string
  tokenExpiry?: Date
  isActive: boolean
  lastUsed?: Date
  failureCount: number
}

export interface SessionInfo {
  token: string
  refreshToken?: string
  expiry: Date
  userId: string
}

export interface V3GenerateRequest {
  inputs: Array<{
    text: string
    voice_id: string
  }>
  model_id: string
  settings: {
    stability: number
    use_speaker_boost: boolean
  }
}

export interface V3GenerateResponse {
  audio_url?: string
  audio_data?: ArrayBuffer
  task_id?: string
  status: 'completed' | 'processing' | 'failed'
  error?: string
}

// 修复：添加了 clientType 字段
export interface LoginRequest {
  returnSecureToken: boolean
  email: string
  password: string
  clientType: string
}

export interface LoginResponse {
  kind: string
  localId: string
  email: string
  displayName: string
  idToken: string
  registered: boolean
  profilePicture?: string
  refreshToken: string
  expiresIn: string
}

export interface AccountHealth {
  accountId: string
  isHealthy: boolean
  lastCheck: Date
  errorCount: number
  lastError?: string
}

export interface V3ServiceConfig {
  maxRetries: number
  retryDelayMs: number
  tokenRefreshThresholdMs: number
  healthCheckIntervalMs: number
  fallbackToOfficialApi: boolean
}

export type AccountStatus = 'active' | 'inactive' | 'failed' | 'rate_limited'

// 修复：为了兼容 URLSearchParams，添加索引签名
export interface RefreshTokenRequest {
  grant_type: string
  refresh_token: string
  [key: string]: string // 添加索引签名
}

export interface RefreshTokenResponse {
  access_token: string
  expires_in: string
  token_type: string
  refresh_token: string
  id_token: string
  user_id: string
  project_id: string
}

// Worker 环境类型
export interface Env {
  ELEVENLABS_CACHE: KVNamespace
  NODE_ENV?: string
}

// 声明 Cloudflare Worker 全局类型
declare global {
  interface KVNamespace {
    get(key: string): Promise<string | null>
    put(key: string, value: string, options?: any): Promise<void>
    delete(key: string): Promise<void>
  }
}

// API 响应类型
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

// TTS 请求参数
export interface TTSRequest {
  text: string
  voice_id?: string
  model_id?: string
  stability?: number
  use_speaker_boost?: boolean
}

// 健康检查响应
export interface HealthCheckResponse {
  healthy: boolean
  total_accounts: number
  active_accounts: number
  failed_accounts: number
  details: AccountHealth[]
}

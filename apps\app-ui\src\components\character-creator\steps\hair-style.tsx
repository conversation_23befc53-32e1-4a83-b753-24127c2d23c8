import React from 'react'
import { Chip, Card, CardBody, Image, Button } from '@heroui/react'
import type { CharacterData } from '..'
import { characterMapping } from '../mapping'

// 发型选项 - 根据性别变化
const femaleHairStyles = Object.entries(characterMapping.hairStyle.female).map(
  ([value, label], index) => ({
    value,
    label,
    pos: index + 1
  })
)

const maleHairStyles = Object.entries(characterMapping.hairStyle.male).map(
  ([value, label], index) => ({
    value,
    label,
    pos: index + 1
  })
)

// 发色选项
const hairColorMap = {
  black: { color: '#000000' },
  brown: { color: '#8B4513' },
  blonde: { color: '#FFD700' },
  red: { color: '#B22222' },
  white: { color: '#E0E0E0' },
  blue: { color: '#1E90FF' },
  purple: { color: '#9370DB' },
  pink: { color: '#FF69B4' }
}

const hairColorOptions = Object.entries(characterMapping.hairColor).map(([value, label]) => ({
  value,
  label,
  color: hairColorMap[value as keyof typeof hairColorMap]?.color || '#000000'
}))

interface HairStyleProps {
  data: CharacterData
  onUpdate: (data: Partial<CharacterData>) => void
}

// 对应每个section的组件
const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-6">
    <h3 className="font-medium text-center text-lg mb-4 text-primary">{title}</h3>
    {children}
  </div>
)

export default function HairStyle({ data, onUpdate }: HairStyleProps) {
  // 直接使用gender字段，如果没有则默认为female
  const gender = React.useMemo(() => {
    const currentGender = (data.gender as 'male' | 'female') || 'female'
    console.log(`发型组件检测到性别: ${currentGender}, gender字段: ${data.gender}`)
    return currentGender
  }, [data.gender])

  // 分页状态
  const [page, setPage] = React.useState(0)
  const hairStyles = gender === 'female' ? femaleHairStyles : maleHairStyles
  const itemsPerPage = 6
  const totalPages = Math.ceil(hairStyles.length / itemsPerPage)

  const currentHairStyles = hairStyles.slice(page * itemsPerPage, (page + 1) * itemsPerPage)

  const nextPage = () => {
    setPage(current => (current + 1) % totalPages)
  }

  const prevPage = () => {
    setPage(current => (current - 1 + totalPages) % totalPages)
  }

  return (
    <div className="space-y-6">
      {/* 发型选择 */}
      <Section title="选择发型">
        <div className="mb-4 flex justify-between items-center">
          <p className="text-default-500 text-sm">
            适合{gender === 'female' ? '女性' : '男性'}的发型
          </p>
          {totalPages > 1 && (
            <Chip size="sm" variant="flat" color="primary">
              {page + 1}/{totalPages}
            </Chip>
          )}
        </div>

        <div
          className="grid grid-cols-2 gap-3 sm:grid-cols-3 mb-4"
          role="radiogroup"
          aria-label="选择发型"
        >
          {currentHairStyles.map(option => {
            const fileNumber = option.pos < 10 ? `0${option.pos}` : option.pos
            // 修复路径一致性问题，使用 woman(单数) 或 man(单数)
            const imageFile = `${gender === 'female' ? 'women' : 'man'}-hair_${fileNumber}`
            const isSelected = data.hairStyle === option.value

            return (
              <Card
                key={option.value}
                isPressable
                isHoverable
                onPress={() => onUpdate({ hairStyle: option.value })}
                className={`
                  cursor-pointer transition-all duration-200
                  ${isSelected ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : ''}
                `}
                role="radio"
                aria-checked={isSelected}
                aria-label={`选择${option.label}发型`}
              >
                <CardBody className="p-0 relative overflow-hidden">
                  <div className="aspect-square bg-gradient-to-b from-default-100/30 to-transparent">
                    <Image
                      src={`/images/custom/${imageFile}.png`}
                      alt={option.label}
                      className="w-full h-full object-cover transition-transform hover:scale-105"
                      classNames={{
                        wrapper: 'w-full h-full'
                      }}
                    />
                  </div>
                  <Chip
                    color={isSelected ? 'primary' : 'default'}
                    variant={isSelected ? 'solid' : 'flat'}
                    className="absolute bottom-2 right-2 z-10"
                    size="sm"
                    classNames={{
                      content: isSelected ? 'text-white font-medium' : 'text-foreground'
                    }}
                  >
                    {option.label}
                  </Chip>
                </CardBody>
              </Card>
            )
          })}
        </div>

        {/* 分页按钮 */}
        {totalPages > 1 && (
          <div className="flex justify-between">
            <Button size="sm" variant="bordered" onPress={prevPage}>
              上一页
            </Button>
            <Button size="sm" variant="bordered" onPress={nextPage}>
              下一页
            </Button>
          </div>
        )}
      </Section>

      {/* 发色选择 */}
      <Section title="选择发色">
        <div className="grid grid-cols-4 gap-3" role="radiogroup" aria-label="选择发色">
          {hairColorOptions.map(option => {
            const isSelected = data.hairColor === option.value

            return (
              <Card
                key={option.value}
                isPressable
                isHoverable
                onPress={() => onUpdate({ hairColor: option.value })}
                className={`
                  cursor-pointer transition-all duration-200
                  ${
                    isSelected
                      ? 'ring-2 ring-primary ring-offset-2 ring-offset-background bg-primary/5'
                      : ''
                  }
                `}
                role="radio"
                aria-checked={isSelected}
                aria-label={`选择${option.label}发色`}
              >
                <CardBody className="p-3 flex flex-col items-center justify-center">
                  <div
                    className="w-10 h-10 rounded-full mb-2 shadow-md border-2 border-white"
                    style={{ backgroundColor: option.color }}
                  />
                  <span className="text-xs text-center font-medium text-foreground">
                    {option.label}
                  </span>
                </CardBody>
              </Card>
            )
          })}
        </div>
      </Section>
    </div>
  )
}

'use client'

import { ChatHistoryItem } from './chat-history-item'
import type { ChatHist<PERSON> } from '@/lib/chat-history-utils'

interface ChatHistoryGroupProps {
  title: string
  chats: ChatHistory[]
  formatTime: (dateStr: string) => string
  onSelect: (chatId: string, roleId: string) => void
  onDelete: (chatId: string) => void
  roleCache: Record<string, { name: string; avatar: string }>
}

export function ChatHistoryGroup({
  title,
  chats,
  formatTime,
  onSelect,
  onDelete,
  roleCache
}: ChatHistoryGroupProps) {
  if (chats.length === 0) return null

  return (
    <div className="space-y-3">
      <h2 className="text-sm font-medium text-default-500 px-1 uppercase tracking-wide">{title}</h2>
      <div className="space-y-2">
        {chats.map(chat => (
          <ChatHistoryItem
            key={chat.id}
            chat={chat}
            formatTime={formatTime}
            onSelect={onSelect}
            onDelete={() => onDelete(chat.id)}
            roleCache={roleCache}
          />
        ))}
      </div>
    </div>
  )
}

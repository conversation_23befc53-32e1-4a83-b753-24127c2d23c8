<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
  <!-- 生产环境配置 -->
  <domain-config cleartextTrafficPermitted="false">
    <!-- 允许的域名 -->
    <domain includeSubdomains="true">pleasurehub.app</domain>
    <domain includeSubdomains="true">r2.cloudflarestorage.com</domain>
    <domain includeSubdomains="true">r2.dev</domain>
  </domain-config>

  <!-- 开发环境配置 -->
  <domain-config cleartextTrafficPermitted="true">
    <!-- 本地开发服务器 -->
    <domain includeSubdomains="false">localhost</domain>
    <domain includeSubdomains="false">127.0.0.1</domain>
    <domain includeSubdomains="false">***********</domain>
    <domain includeSubdomains="false">*************</domain>
    <domain includeSubdomains="false">*************</domain>
    <domain includeSubdomains="false">********</domain>
  </domain-config>

  <!-- 基础配置 -->
  <base-config cleartextTrafficPermitted="false">
    <trust-anchors>
      <!-- 信任系统证书 -->
      <certificates src="system"/>
      <!-- 在调试版本中也信任用户添加的证书 -->
      <certificates src="user"/>
    </trust-anchors>
  </base-config>

  <!-- 调试配置（仅在调试版本中生效） -->
  <debug-overrides>
    <trust-anchors>
      <certificates src="system"/>
      <certificates src="user"/>
    </trust-anchors>
  </debug-overrides>
</network-security-config> 
import { create } from 'zustand'
import { charactersService } from '@/api/services/characters'
import type { CharacterData } from '@/api/services/characters'

// Store状态接口
interface SystemCharactersState {
  // 数据状态
  systemCharacters: CharacterData[]
  isLoading: boolean
  isRefreshing: boolean
  error: string | null
  lastFetchTime: number | null

  // 请求状态管理
  fetchPromise: Promise<CharacterData[]> | null
}

// Store方法接口
interface SystemCharactersActions {
  // 获取系统角色（带缓存）
  fetchSystemCharacters: (forceRefresh?: boolean) => Promise<CharacterData[]>

  // 强制刷新系统角色数据
  refreshSystemCharacters: () => Promise<CharacterData[]>

  // 根据ID获取单个系统角色
  getSystemCharacterById: (characterId: string) => CharacterData | undefined

  // 清除缓存
  clearSystemCharacters: () => void

  // 检查缓存是否有效
  isCacheValid: () => boolean
}

// 缓存配置
const CACHE_DURATION = 60 * 60 * 1000 // 60分钟缓存
const MAX_CACHE_SIZE = 100 // 最大缓存数量

// 移除本地API服务定义，直接使用charactersService

export const useSystemCharactersStore = create<SystemCharactersState & SystemCharactersActions>(
  (set, get) => ({
    // 初始状态
    systemCharacters: [],
    isLoading: false,
    isRefreshing: false,
    error: null,
    lastFetchTime: null,
    fetchPromise: null,

    // 获取系统角色（带缓存）
    fetchSystemCharacters: async (forceRefresh = false) => {
      const state = get()

      // 如果有正在进行的请求，返回该Promise
      if (state.fetchPromise && !forceRefresh) {
        console.log('⏳ 系统角色请求进行中，复用现有Promise')
        return state.fetchPromise
      }

      // 检查缓存有效性
      if (!forceRefresh && state.isCacheValid() && state.systemCharacters.length > 0) {
        console.log('✅ 使用系统角色缓存数据')
        return state.systemCharacters
      }

      // 创建新的请求Promise
      const fetchPromise = (async () => {
        try {
          console.log('🚀 开始获取系统角色数据')

          set({
            isLoading: !state.systemCharacters.length, // 如果有缓存数据，不显示loading
            isRefreshing: !!state.systemCharacters.length, // 如果有缓存数据，显示refreshing
            error: null
          })

          const response = await charactersService.getSystemCharacters()
          const characters = response?.characters || []

          // 限制缓存大小
          const limitedCharacters = characters.slice(0, MAX_CACHE_SIZE)

          set({
            systemCharacters: limitedCharacters,
            isLoading: false,
            isRefreshing: false,
            error: null,
            lastFetchTime: Date.now(),
            fetchPromise: null
          })

          console.log(`✅ 成功获取 ${limitedCharacters.length} 个系统角色`)
          return limitedCharacters
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '获取系统角色失败'
          console.error('❌ 获取系统角色失败:', errorMessage)

          set({
            isLoading: false,
            isRefreshing: false,
            error: errorMessage,
            fetchPromise: null
          })

          // 如果有缓存数据，在错误时返回缓存数据
          if (state.systemCharacters.length > 0) {
            console.log('⚠️ 请求失败，返回缓存数据')
            return state.systemCharacters
          }

          throw error
        }
      })()

      // 保存Promise到状态中
      set({ fetchPromise })

      return fetchPromise
    },

    // 强制刷新系统角色数据
    refreshSystemCharacters: async () => {
      return get().fetchSystemCharacters(true)
    },

    // 根据ID获取单个系统角色
    getSystemCharacterById: (characterId: string) => {
      const { systemCharacters } = get()
      return systemCharacters.find(char => char.id === characterId)
    },

    // 检查缓存是否有效
    isCacheValid: () => {
      const { lastFetchTime } = get()
      if (!lastFetchTime) return false
      return Date.now() - lastFetchTime < CACHE_DURATION
    },

    // 清除缓存
    clearSystemCharacters: () => {
      console.log('🧹 清除系统角色缓存')
      set({
        systemCharacters: [],
        isLoading: false,
        isRefreshing: false,
        error: null,
        lastFetchTime: null,
        fetchPromise: null
      })
    }
  })
)

// 便捷的数据访问Hook
export const useSystemCharactersData = () => {
  const { systemCharacters, isLoading, isRefreshing, error } = useSystemCharactersStore()
  return { systemCharacters, isLoading, isRefreshing, error }
}

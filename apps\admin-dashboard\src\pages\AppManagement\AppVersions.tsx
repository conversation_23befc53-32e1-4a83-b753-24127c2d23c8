import React, { useState, useEffect } from 'react'
import {
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Progress,
  Card,
  Row,
  Col,
  Tag,
  Space,
  Dropdown,
  Menu,
  message,
  Typography
} from 'antd'
import {
  PlusOutlined,
  UploadOutlined,
  DeleteOutlined,
  DownloadOutlined,
  MoreOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UploadProps, UploadFile } from 'antd/es/upload'
import {
  appManagementService,
  type AppVersion as ServiceAppVersion
} from '@/services/app-management'

const { Title, Text } = Typography
const { TextArea } = Input

// 使用服务中的类型定义
type AppVersion = ServiceAppVersion

interface UploadForm {
  versionName: string
  versionCode: number
  versionType: 'apk' | 'hotfix'
  releaseNotes?: string
  minCompatibleVersion?: string
}

const AppVersions: React.FC = () => {
  const [versions, setVersions] = useState<AppVersion[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedType, setSelectedType] = useState<'all' | 'apk' | 'hotfix'>('all')

  // 上传相关状态
  const [uploadModalVisible, setUploadModalVisible] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [form] = Form.useForm<UploadForm>()
  const [fileList, setFileList] = useState<UploadFile[]>([])

  // 获取版本列表
  const fetchVersions = async () => {
    try {
      setLoading(true)
      const params: { versionType?: 'apk' | 'hotfix' } = {}
      if (selectedType !== 'all') {
        params.versionType = selectedType
      }

      const result = await appManagementService.getVersions(params)
      if (result.success && result.data) {
        setVersions(result.data.data)
      } else {
        throw new Error(result.error || '获取版本列表失败')
      }
    } catch (error) {
      console.error('获取版本列表失败:', error)
      message.error('获取版本列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理文件上传
  const handleUpload = async () => {
    try {
      if (fileList.length === 0) {
        message.error('请选择文件')
        return
      }

      const values = await form.validateFields()
      setUploading(true)
      setUploadProgress(0)

      const formData = new FormData()
      if (fileList[0]?.originFileObj) {
        formData.append('file', fileList[0].originFileObj)
      }
      formData.append('versionName', values.versionName)
      formData.append('versionCode', values.versionCode.toString())
      formData.append('versionType', values.versionType)
      if (values.releaseNotes) {
        formData.append('releaseNotes', values.releaseNotes)
      }
      if (values.minCompatibleVersion) {
        formData.append('minCompatibleVersion', values.minCompatibleVersion)
      }

      setUploadProgress(50)

      const result = await appManagementService.uploadVersion(formData)

      setUploadProgress(90)

      if (result.success) {
        setUploadProgress(100)
        message.success('文件上传成功')

        // 重置表单
        form.resetFields()
        setFileList([])
        setUploadModalVisible(false)

        // 刷新列表
        await fetchVersions()
      } else {
        throw new Error(result.error || '上传失败')
      }
    } catch (error) {
      console.error('上传失败:', error)
      message.error('上传失败')
    } finally {
      setUploading(false)
      setUploadProgress(0)
    }
  }

  // 删除版本
  const handleDelete = async (versionId: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个版本吗？此操作不可撤销。',
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const result = await appManagementService.deleteVersion(versionId)
          if (result.success) {
            message.success('删除成功')
            await fetchVersions()
          } else {
            throw new Error(result.error || '删除失败')
          }
        } catch (error) {
          console.error('删除失败:', error)
          message.error('删除失败')
        }
      }
    })
  }

  // 格式化文件大小
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return '-'
    const mb = bytes / 1024 / 1024
    return `${mb.toFixed(1)} MB`
  }

  // 表格列定义
  const columns: ColumnsType<AppVersion> = [
    {
      title: '版本信息',
      key: 'version',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.versionName}</div>
          <Text type="secondary">Code: {record.versionCode}</Text>
        </div>
      )
    },
    {
      title: '类型',
      dataIndex: 'versionType',
      key: 'versionType',
      render: (type: string) => (
        <Tag color={type === 'apk' ? 'blue' : 'green'}>{type.toUpperCase()}</Tag>
      )
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      render: (size: number) => formatFileSize(size)
    },
    {
      title: '最小兼容版本',
      dataIndex: 'minCompatibleVersion',
      key: 'minCompatibleVersion',
      render: (version: string) => version || '-'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => new Date(date).toLocaleString('zh-CN')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => {
        const menu = (
          <Menu>
            <Menu.Item
              key="download"
              icon={<DownloadOutlined />}
              onClick={() => window.open(record.fileUrl, '_blank')}
            >
              下载文件
            </Menu.Item>
            <Menu.Item
              key="delete"
              icon={<DeleteOutlined />}
              danger
              onClick={() => handleDelete(record.id)}
            >
              删除版本
            </Menu.Item>
          </Menu>
        )

        return (
          <Dropdown overlay={menu} trigger={['click']}>
            <Button icon={<MoreOutlined />} />
          </Dropdown>
        )
      }
    }
  ]

  // 文件上传配置
  const uploadProps: UploadProps = {
    beforeUpload: file => {
      const versionType = form.getFieldValue('versionType') || 'apk'
      const isValidType =
        versionType === 'apk' ? file.name.endsWith('.apk') : file.name.endsWith('.zip')

      if (!isValidType) {
        message.error(`请选择正确的文件类型 (${versionType === 'apk' ? '.apk' : '.zip'})`)
        return false
      }

      const maxSize = versionType === 'apk' ? 200 : 50 // MB
      const isValidSize = file.size / 1024 / 1024 < maxSize
      if (!isValidSize) {
        message.error(`文件大小不能超过 ${maxSize}MB`)
        return false
      }

      setFileList([file])
      return false // 阻止自动上传
    },
    fileList,
    onRemove: () => {
      setFileList([])
    }
  }

  useEffect(() => {
    fetchVersions()
  }, [selectedType])

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面标题 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Title level={2}>应用版本管理</Title>
          <Text type="secondary">管理APK和热更新包的版本</Text>
        </Col>
        <Col>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setUploadModalVisible(true)}
          >
            上传新版本
          </Button>
        </Col>
      </Row>

      {/* 筛选区域 */}
      <Card style={{ marginBottom: '24px' }}>
        <Space>
          <Text>筛选条件:</Text>
          <Select value={selectedType} onChange={setSelectedType} style={{ width: 120 }}>
            <Select.Option value="all">全部</Select.Option>
            <Select.Option value="apk">APK</Select.Option>
            <Select.Option value="hotfix">热更新</Select.Option>
          </Select>
          <Button icon={<ReloadOutlined />} onClick={fetchVersions}>
            刷新
          </Button>
        </Space>
      </Card>

      {/* 版本列表 */}
      <Card title="版本列表">
        <Table
          columns={columns}
          dataSource={versions}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 上传对话框 */}
      <Modal
        title="上传新版本"
        open={uploadModalVisible}
        onCancel={() => {
          if (!uploading) {
            setUploadModalVisible(false)
            form.resetFields()
            setFileList([])
          }
        }}
        footer={
          uploading
            ? null
            : [
                <Button key="cancel" onClick={() => setUploadModalVisible(false)}>
                  取消
                </Button>,
                <Button key="submit" type="primary" onClick={handleUpload}>
                  上传
                </Button>
              ]
        }
        width={600}
        destroyOnClose
        maskClosable={!uploading}
      >
        {uploading ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <div style={{ marginBottom: '16px' }}>正在上传文件...</div>
            <Progress percent={uploadProgress} />
          </div>
        ) : (
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="versionName"
                  label="版本名称"
                  rules={[{ required: true, message: '请输入版本名称' }]}
                >
                  <Input placeholder="例如: 1.0.0" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="versionCode"
                  label="版本代码"
                  rules={[{ required: true, message: '请输入版本代码' }]}
                >
                  <Input type="number" placeholder="例如: 100" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="versionType"
                  label="版本类型"
                  rules={[{ required: true, message: '请选择版本类型' }]}
                  initialValue="apk"
                >
                  <Select>
                    <Select.Option value="apk">APK安装包</Select.Option>
                    <Select.Option value="hotfix">热更新包</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item name="minCompatibleVersion" label="最小兼容版本">
                  <Input placeholder="例如: 0.9.0" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label="选择文件" required>
              <Upload {...uploadProps}>
                <Button icon={<UploadOutlined />}>选择文件</Button>
              </Upload>
              {fileList.length > 0 && (
                <div style={{ marginTop: '8px', color: '#666' }}>
                  文件: {fileList[0].name} ({formatFileSize(fileList[0].size)})
                </div>
              )}
            </Form.Item>

            <Form.Item name="releaseNotes" label="更新说明">
              <TextArea rows={3} placeholder="描述这个版本的更新内容..." />
            </Form.Item>
          </Form>
        )}
      </Modal>
    </div>
  )
}

export default AppVersions

import { BottomNavigation } from '@/components/bottom-navigation'
import KeepAliveRouteOutlet from 'keepalive-for-react-router'

/**
 * 带有底部导航的页面布局
 * 用于首页、聊天历史和导出页面等需要底部导航的页面
 */
export function TabLayout() {
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-1 pb-16">
        <KeepAliveRouteOutlet
          // 指定需要缓存的路径
          include={['/discover', '/chat-history', '/profile']}
        />
      </main>
      <BottomNavigation />
    </div>
  )
}

import React, { useState } from 'react'
import { Button, Input } from '@heroui/react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { type Device } from '@/api/services/devices'
import { useDeviceStore } from '@/stores/device-store'
import { BarcodeScannerService } from '@/services/barcode-scanner'

interface DeviceConnectionFormProps {
  onDeviceConnect: (device: Device) => void
  className?: string
}

export const DeviceConnectionForm: React.FC<DeviceConnectionFormProps> = ({
  onDeviceConnect,
  className = ''
}) => {
  const { t } = useTranslation('device')
  const [deviceCode, setDeviceCode] = useState('')
  const [isConnecting, setIsConnecting] = useState(false)
  const [isScanning, setIsScanning] = useState(false)
  const [error, setError] = useState('')

  // 使用全局设备store的缓存功能
  const { getSupportedDevices, findDeviceByCode } = useDeviceStore()

  // 处理设备连接
  const handleConnect = async () => {
    if (!deviceCode.trim()) {
      setError(t('connection_form.input_placeholder'))
      return
    }

    setIsConnecting(true)
    setError('')

    try {
      // 从缓存中查找设备，而不是调用 API
      const device = await findDeviceByCode(deviceCode.trim())

      if (device) {
        onDeviceConnect(device)
      } else {
        setError(t('connection_form.invalid_code'))
      }
    } catch (error) {
      console.error('设备连接失败:', error)
      setError(t('connection_form.connect_failed'))
    } finally {
      setIsConnecting(false)
    }
  }

  // 处理二维码扫描
  const handleScanQR = async () => {
    setIsScanning(true)
    setError('')

    try {
      // 检查设备是否支持二维码扫描
      const isSupported = await BarcodeScannerService.isSupported()
      if (!isSupported) {
        setError(t('connection_form.qr_not_supported'))
        return
      }

      // 开始扫描
      const scanResult = await BarcodeScannerService.scan()

      if (scanResult.cancelled) {
        // 用户取消了扫描
        return
      }

      if (scanResult.content) {
        // 提取设备码（去除可能的前缀或格式化）
        const scannedDeviceCode = scanResult.content.trim()
        setDeviceCode(scannedDeviceCode)

        // 自动连接扫描到的设备
        const device = await findDeviceByCode(scannedDeviceCode)
        if (device) {
          onDeviceConnect(device)
        } else {
          setError(t('connection_form.invalid_qr_code'))
        }
      } else {
        setError(t('connection_form.qr_unrecognized'))
      }
    } catch (error) {
      console.error('二维码扫描失败:', error)

      // 根据错误类型提供更具体的错误信息
      if (error instanceof Error) {
        if (error.message.includes('权限')) {
          setError(t('connection_form.camera_permission_denied'))
        } else if (error.message.includes('不支持')) {
          setError(t('connection_form.scan_not_supported'))
        } else {
          setError(t('connection_form.scan_failed'))
        }
      } else {
        setError(t('connection_form.scan_failed'))
      }
    } finally {
      setIsScanning(false)
    }
  }

  return (
    <div className={`w-full max-w-sm space-y-6 ${className}`}>
      {/* 输入设备码 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <label
          className="block text-sm mb-3"
          style={{ color: '#7c85b6', fontFamily: "'PingFang SC', sans-serif" }}
        >
          {t('connection_form.input_label')}
        </label>
        <div className="relative">
          <Input
            value={deviceCode}
            onChange={e => {
              setDeviceCode(e.target.value)
              setError('') // 清除错误信息
            }}
            placeholder={t('connection_form.input_example')}
            className="w-full"
            classNames={{
              input: 'text-base pl-5 bg-transparent border-0',
              inputWrapper: 'h-12 rounded-full border-0 shadow-none'
            }}
            style={{
              backgroundColor: '#1d2135',
              color: '#7c85b6',
              fontFamily: "'PingFang SC', sans-serif"
            }}
            isInvalid={!!error}
          />
          {error && (
            <p
              className="text-red-400 text-xs mt-2"
              style={{ fontFamily: "'PingFang SC', sans-serif" }}
            >
              {error}
            </p>
          )}
        </div>
      </motion.div>

      {/* 连接设备按钮 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        <Button
          className="w-full h-12 rounded-full text-white text-base font-normal"
          style={{ backgroundColor: '#ff2d97', fontFamily: "'PingFang SC', sans-serif" }}
          onPress={handleConnect}
          isLoading={isConnecting}
          isDisabled={!deviceCode.trim() || isConnecting || isScanning}
        >
          {isConnecting ? t('connection_form.connecting') : t('connection_form.connect_button')}
        </Button>
      </motion.div>

      {/* 或者分隔线 */}
      <motion.div
        className="flex items-center justify-center py-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        <div className="flex-1 h-px" style={{ backgroundColor: '#343c5b' }}></div>
        <span
          className="px-4 text-xs"
          style={{ color: '#343c5b', fontFamily: "'PingFang SC', sans-serif" }}
        >
          {t('connection_form.or')}
        </span>
        <div className="flex-1 h-px" style={{ backgroundColor: '#343c5b' }}></div>
      </motion.div>

      {/* 扫描二维码按钮 */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <Button
          className="w-full h-12 rounded-full text-white text-base font-normal flex items-center justify-center gap-2"
          style={{ backgroundColor: '#892fff', fontFamily: "'PingFang SC', sans-serif" }}
          onPress={handleScanQR}
          isLoading={isScanning}
          isDisabled={isConnecting || isScanning}
        >
          <Icon icon="solar:qr-code-bold" width={20} className="text-white" />
          {isScanning ? t('connection_form.scanning') : t('connection_form.scan_button')}
        </Button>
      </motion.div>

      {/* 扫描说明文字 */}
      <div className="text-center pt-2">
        <p
          className="text-xs"
          style={{ color: '#7c85b6', fontFamily: "'PingFang SC', sans-serif" }}
        >
          {t('connection_form.scan_instruction')}
        </p>
      </div>
    </div>
  )
}

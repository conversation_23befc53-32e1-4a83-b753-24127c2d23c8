import type { MessageBatch } from '@cloudflare/workers-types';
import type { QueueProcessResult } from '../types';

/**
 * 队列批处理工具函数
 */
export class QueueBatchProcessor<T> {
  constructor(
    private processorName: string,
    private processor: (task: T) => Promise<void>
  ) {}

  /**
   * 处理队列批次
   */
  async processBatch(batch: MessageBatch<T>): Promise<QueueProcessResult> {
    console.log(`🔄 ${this.processorName}队列：处理 ${batch.messages.length} 个任务`);

    const result: QueueProcessResult = {
      processed: 0,
      failed: 0,
      errors: [],
    };

    for (const message of batch.messages) {
      const startTime = Date.now();

      try {
        await this.processor(message.body);
        result.processed++;

        console.log(`✅ ${this.processorName}任务处理完成，耗时: ${Date.now() - startTime}ms`);

        // 确认消息已处理
        message.ack();
      } catch (error) {
        result.failed++;
        const errorMessage = error instanceof Error ? error.message : String(error);
        result.errors.push(`任务失败: ${errorMessage}`);

        console.error(`❌ ${this.processorName}任务处理失败:`, error);

        // 重试消息
        message.retry();
      }
    }

    console.log(`📊 ${this.processorName}队列批次处理完成:`, {
      processed: result.processed,
      failed: result.failed,
      totalErrors: result.errors.length,
    });

    return result;
  }
}

/**
 * 队列错误处理工具
 */
export class QueueErrorHandler {
  /**
   * 安全执行队列任务，带重试机制
   */
  static async safeExecute<T>(
    taskName: string,
    task: () => Promise<T>,
    maxRetries = 3,
    retryDelayMs = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await task();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        console.warn(`⚠️ ${taskName} 第 ${attempt} 次尝试失败:`, lastError.message);

        if (attempt < maxRetries) {
          console.log(`🔄 ${retryDelayMs}ms 后重试...`);
          await new Promise((resolve) => setTimeout(resolve, retryDelayMs));
          retryDelayMs *= 2; // 指数退避
        }
      }
    }

    throw new Error(`${taskName} 在 ${maxRetries} 次尝试后仍然失败: ${lastError!.message}`);
  }

  /**
   * 处理队列任务异常
   */
  static handleTaskError(taskId: string, error: unknown): string {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`❌ 队列任务 ${taskId} 处理失败:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });
    return errorMessage;
  }
}

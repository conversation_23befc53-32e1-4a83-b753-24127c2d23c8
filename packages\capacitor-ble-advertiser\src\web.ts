import { WebPlugin } from '@capacitor/core';

import type { BleAdvertiserPlugin } from './definitions';

export class BleAdvertiserWeb extends WebPlugin implements BleAdvertiserPlugin {
  async initialize(): Promise<{ success: boolean }> {
    console.warn('BleAdvertiser.initialize: Web平台不支持蓝牙广播功能');
    return { success: false };
  }

  async isBluetoothEnabled(): Promise<{ enabled: boolean }> {
    // 检查Web Bluetooth API是否可用
    const isWebBluetoothAvailable = 'bluetooth' in navigator;
    console.warn(
      `BleAdvertiser.isBluetoothEnabled: Web平台蓝牙状态: ${isWebBluetoothAvailable ? '可用' : '不可用'}`,
    );
    return { enabled: isWebBluetoothAvailable };
  }

  async startAdvertising(options: {
    mode?: number;
    manufacturerId?: number;
    data: string | number[];
    instanceId?: number;
  }): Promise<{ success: boolean; instanceId: number }> {
    console.warn(
      'BleAdvertiser.startAdvertising: Web平台不支持蓝牙广播功能',
      options,
    );
    const instanceId = options.instanceId || Math.floor(Math.random() * 10000);
    return { success: false, instanceId };
  }

  async stopAdvertising(options: { instanceId?: number }): Promise<{
    success: boolean;
  }> {
    console.warn(
      'BleAdvertiser.stopAdvertising: Web平台不支持蓝牙广播功能',
      options,
    );
    return { success: false };
  }

  async stopAllAdvertising(): Promise<{ success: boolean }> {
    console.warn('BleAdvertiser.stopAllAdvertising: Web平台不支持蓝牙广播功能');
    return { success: false };
  }
}

import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import type { UploadResponse } from '@/types/api'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import { uploadToR2, getR2ConfigFromEnv, IMAGE_UPLOAD_OPTIONS } from '@/lib/utils/r2-upload'

const app = new Hono<{ Bindings: Env }>()

// 文件上传验证 schema - 使用通用错误消息，在路由中处理国际化
const uploadFileSchema = z.object({
  file: z.string().min(1),
  fileName: z.string().min(1).max(255),
  fileType: z.enum(['image/jpeg', 'image/jpg', 'image/png', 'image/webp']),
  uploadType: z.enum(['avatar', 'character-image'])
})

/**
 * 将 base64 字符串转换为 Uint8Array
 */
function base64ToUint8Array(base64Data: string): Uint8Array {
  // 检查是否是 data URL 格式
  let cleanBase64 = base64Data
  if (base64Data.startsWith('data:')) {
    // 移除 data:image/xxx;base64, 前缀
    cleanBase64 = base64Data.split(',')[1]
  }

  // 将 base64 转换为二进制字符串
  const binaryString = atob(cleanBase64)
  const bytes = new Uint8Array(binaryString.length)

  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }

  return bytes
}

/**
 * POST /api/upload/avatar
 * 上传头像
 */
app.post(
  '/avatar',
  authMiddleware,
  languageMiddleware,
  zValidator('json', uploadFileSchema),
  async c => {
    try {
      const { file, fileName, fileType, uploadType } = c.req.valid('json')
      const env = c.env
      const t = c.get('t')

      // 验证上传类型
      if (uploadType !== 'avatar') {
        return c.json({ error: t('upload.wrong_upload_type_avatar') }, 400)
      }

      // 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(env)
      if (!r2Config) {
        return c.json({ error: t('upload.r2_config_not_found') }, 500)
      }

      // 将 base64 转换为 Uint8Array
      const fileBuffer = base64ToUint8Array(file)

      // 使用 r2-upload 工具上传文件
      const uploadResult = await uploadToR2(fileBuffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName,
        folder: 'avatars',
        maxSize: 5 * 1024 * 1024, // 头像限制为5MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
        makePublic: true
      })

      if (!uploadResult.success) {
        return c.json(
          {
            success: false,
            error: uploadResult.error || t('upload.upload_failed')
          },
          500
        )
      }

      // 构造响应
      const response: UploadResponse = {
        fileId: `avatar_${Date.now()}_${Math.random().toString(36).substring(7)}`,
        fileName: fileName,
        fileSize: fileBuffer.length,
        mimeType: fileType,
        url: uploadResult.url!
      }

      return c.json({
        success: true,
        data: response
      })
    } catch (error) {
      const t = c.get('t')
      console.error('上传头像失败:', error)
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : t('upload.avatar_upload_failed')
        },
        500
      )
    }
  }
)

/**
 * POST /api/upload/character-image
 * 上传角色图片
 */
app.post(
  '/character-image',
  authMiddleware,
  languageMiddleware,
  zValidator('json', uploadFileSchema),
  async c => {
    try {
      const { file, fileName, fileType, uploadType } = c.req.valid('json')
      const env = c.env
      const t = c.get('t')

      // 验证上传类型
      if (uploadType !== 'character-image') {
        return c.json({ error: t('upload.wrong_upload_type_character') }, 400)
      }

      // 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(env)
      if (!r2Config) {
        return c.json({ error: t('upload.r2_config_not_found') }, 500)
      }

      // 将 base64 转换为 Uint8Array
      const fileBuffer = base64ToUint8Array(file)

      // 使用 r2-upload 工具上传文件
      const uploadResult = await uploadToR2(fileBuffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName,
        folder: 'characters',
        maxSize: 10 * 1024 * 1024, // 角色图片限制为10MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
        makePublic: true
      })

      if (!uploadResult.success) {
        return c.json(
          {
            success: false,
            error: uploadResult.error || t('upload.upload_failed')
          },
          500
        )
      }

      // 构造响应
      const response: UploadResponse = {
        fileId: `character_${Date.now()}_${Math.random().toString(36).substring(7)}`,
        fileName: fileName,
        fileSize: fileBuffer.length,
        mimeType: fileType,
        url: uploadResult.url!
      }

      return c.json({
        success: true,
        data: response
      })
    } catch (error) {
      const t = c.get('t')
      console.error('上传角色图片失败:', error)
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : t('upload.character_image_upload_failed')
        },
        500
      )
    }
  }
)

/**
 * POST /api/upload
 * 通用文件上传接口
 */
app.post('/', authMiddleware, languageMiddleware, zValidator('json', uploadFileSchema), async c => {
  try {
    const { file, fileName, fileType, uploadType } = c.req.valid('json')
    const env = c.env
    const t = c.get('t')

    // 根据上传类型确定文件夹和配置
    const folderMap = {
      avatar: 'avatars',
      'character-image': 'characters'
    }

    const maxSizeMap = {
      avatar: 5 * 1024 * 1024, // 5MB
      'character-image': 10 * 1024 * 1024 // 10MB
    }

    const folder = folderMap[uploadType]
    const maxSize = maxSizeMap[uploadType]

    if (!folder) {
      return c.json({ error: t('upload.unsupported_upload_type') }, 400)
    }

    // 获取 R2 配置
    const r2Config = getR2ConfigFromEnv(env)
    if (!r2Config) {
      return c.json({ error: t('upload.r2_config_not_found') }, 500)
    }

    // 将 base64 转换为 Uint8Array
    const fileBuffer = base64ToUint8Array(file)

    // 使用 r2-upload 工具上传文件
    const uploadResult = await uploadToR2(fileBuffer, r2Config, {
      ...IMAGE_UPLOAD_OPTIONS,
      fileName,
      folder,
      maxSize,
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
      makePublic: true
    })

    if (!uploadResult.success) {
      return c.json(
        {
          success: false,
          error: uploadResult.error || t('upload.upload_failed')
        },
        500
      )
    }

    // 构造响应
    const response: UploadResponse = {
      fileId: `${uploadType}_${Date.now()}_${Math.random().toString(36).substring(7)}`,
      fileName: fileName,
      fileSize: fileBuffer.length,
      mimeType: fileType,
      url: uploadResult.url!
    }

    return c.json({
      success: true,
      data: response
    })
  } catch (error) {
    const t = c.get('t')
    console.error('文件上传失败:', error)
    return c.json(
      {
        success: false,
        error: error instanceof Error ? error.message : t('upload.file_upload_failed')
      },
      500
    )
  }
})

export default app

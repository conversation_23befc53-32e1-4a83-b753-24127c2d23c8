export const audioTags = {
  角色类型: {
    dom: '主导',
    sub: '顺从',
  },
  动作类型: {
    moan: '呻吟',
    breathing: '喘息',
    orgasm: '高潮',
    blowjob: '口交',
    biting: '咬',
    kisses: '亲吻',
    laughter: '笑声',
    licking: '舔',
  },
  强度等级: {
    low: '低强度',
    medium: '中强度',
    high: '高强度',
    intense: '强烈',
    extreme: '极端',
  },
  身体部位: {
    mouth: '嘴部',
    nose: '鼻部',
    back: '背部',
  },
  性别标签: {
    female: '女性',
  },
  风格标签: {
    cute: '可爱',
    soft: '柔和',
    breathy: '气息',
    sexy: '性感',
    seductive: '诱惑',
    playful: '顽皮',
    raspy: '沙哑',
  },
};

/**
 * 将音效分类JSON数据转换为LLM友好的字符串格式
 * @param {Object} categoriesData - 音效分类数据对象
 * @returns {string} - 格式化后的字符串，便于LLM理解
 */
function formatAudioCategoriesForLLM(categoriesData: Record<string, Record<string, string>>) {
  let result = '音效分类系统说明：\n\n';

  // 遍历每个分类类型
  Object.entries(categoriesData).forEach(([categoryType, items], index) => {
    result += `${index + 1}. ${categoryType}：\n`;

    // 遍历该分类下的所有标签
    const itemEntries = Object.entries(items);
    itemEntries.forEach(([key, displayName], itemIndex) => {
      const isLast = itemIndex === itemEntries.length - 1;
      result += `   - ${key}（${displayName}）${isLast ? '' : '\n'}`;
    });

    result += '\n\n';
  });

  // 添加使用说明
  result += '- 先选取动作类型，再根据预警等其他因素，选择合适的其他标签\n';
  result += '- 标签采用英文名称，括号内为中文显示名称\n';
  result += '- 可以组合不同类型的标签来描述复杂的音效特征\n';
  result += '- 第二个标签必须是动作类型\n';
  result += '- 例如：["dom", "moan", "high", "female"] 表示女性主导高强度呻吟声';

  return result;
}

/**
 * 生成音效标签的提示词部分
 */
export function generateAudioPrompt() {
  return `
### 音效标签系统 (可选)
${formatAudioCategoriesForLLM(audioTags)}
- 使用规则/说明:
- 这些音效都属于敏感的音效，比如：["sub","blowjob","low"], 这个组合的音效就是属于中等强度的口交音效，比如["dom","moan","high"]，这种就是比较强烈的呻吟音效.
- (重点)所以需要根据当前情境，用户语义，情感状态选择合适的音效，并不是每次都需要音效，按需，通常都是进入到有敏感场面时，才需要音效.如果还没到敏感场面，则不需要音效.（重点）
- (重点)亦或者用户主要要求：比如："给我口一下"，这个时候就需要按照语义给出口交的音效，如果角色强势，可先拒绝后条件交换，增加互动层次再给出对应的音效.（重点）
- 可组合多个标签，用逗号分隔: dom,moan,mouth,medium.
- 格式: <audioTags>标签1,标签2</audioTags>
`;
}

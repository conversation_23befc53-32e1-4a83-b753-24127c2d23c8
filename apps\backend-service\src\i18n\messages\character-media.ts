// 角色媒体相关消息
export const characterMediaMessages = {
  zh: {
    'character-media.user_not_found': '未找到用户信息',
    'character-media.user_not_exist': '用户不存在',
    'character-media.query_failed': '查询失败',
    'character-media.query_media_failed': '查询角色媒体记录失败',
    'character-media.query_images_failed': '查询角色图片失败',
    'character-media.query_videos_failed': '查询角色视频失败',
    'character-media.character_id_required': '角色ID不能为空'
  },
  'zh-TW': {
    'character-media.user_not_found': '未找到使用者資訊',
    'character-media.user_not_exist': '使用者不存在',
    'character-media.query_failed': '查詢失敗',
    'character-media.query_media_failed': '查詢角色媒體記錄失敗',
    'character-media.query_images_failed': '查詢角色圖片失敗',
    'character-media.query_videos_failed': '查詢角色影片失敗',
    'character-media.character_id_required': '角色ID不能為空'
  },
  ja: {
    'character-media.user_not_found': 'ユーザー情報が見つかりません',
    'character-media.user_not_exist': 'ユーザーが存在しません',
    'character-media.query_failed': 'クエリに失敗しました',
    'character-media.query_media_failed': 'キャラクターメディア記録の取得に失敗しました',
    'character-media.query_images_failed': 'キャラクター画像の取得に失敗しました',
    'character-media.query_videos_failed': 'キャラクター動画の取得に失敗しました',
    'character-media.character_id_required': 'キャラクターIDを入力してください'
  },
  es: {
    'character-media.user_not_found': 'No se encontró la información del usuario',
    'character-media.user_not_exist': 'El usuario no existe',
    'character-media.query_failed': 'Consulta fallida',
    'character-media.query_media_failed':
      'Error al consultar los registros de medios del personaje',
    'character-media.query_images_failed': 'Error al consultar las imágenes del personaje',
    'character-media.query_videos_failed': 'Error al consultar los videos del personaje',
    'character-media.character_id_required': 'El ID del personaje no puede estar vacío'
  },
  en: {
    'character-media.user_not_found': 'User information not found',
    'character-media.user_not_exist': 'User does not exist',
    'character-media.query_failed': 'Query failed',
    'character-media.query_media_failed': 'Failed to query character media records',
    'character-media.query_images_failed': 'Failed to query character images',
    'character-media.query_videos_failed': 'Failed to query character videos',
    'character-media.character_id_required': 'Character ID cannot be empty'
  }
}

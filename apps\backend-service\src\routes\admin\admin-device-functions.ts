import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import { getUserBySupabaseId } from '@/lib/db/queries/user'

const adminDeviceFunctions = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 设备功能列表管理 ====================

// 查询参数验证
const functionListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  isActive: z
    .string()
    .transform(val => val === 'true')
    .optional()
})

// 获取设备功能列表
adminDeviceFunctions.get('/', authMiddleware, zValidator('query', functionListSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { page, pageSize, keyword, isActive } = c.req.valid('query')
    const env = c.env

    console.log('📋 [ADMIN-DEVICE-FUNCTIONS] 获取设备功能列表:', {
      page,
      pageSize,
      keyword,
      isActive
    })

    // 从数据库查询设备功能列表
    const supabase = getSupabase(env)

    let query = supabase.from('DeviceFunction').select('*')

    // 构建筛选条件
    if (keyword) {
      query = query.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%,key.ilike.%${keyword}%`)
    }

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive)
    }

    // 添加排序
    query = query.order('created_at', { ascending: false })

    // 计算总数
    let countQuery = supabase.from('DeviceFunction').select('*', { count: 'exact', head: true })

    if (keyword) {
      countQuery = countQuery.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%,key.ilike.%${keyword}%`)
    }
    if (isActive !== undefined) {
      countQuery = countQuery.eq('is_active', isActive)
    }

    const countResult = await countQuery
    const total = countResult.count || 0

    // 分页查询
    const offset = (page - 1) * pageSize
    const result = await query.range(offset, offset + pageSize - 1)
    const { data: functions, error } = handleSupabaseResult(result)

    if (error) throw error

    console.log(`📋 [ADMIN-DEVICE-FUNCTIONS] 查询到 ${functions.length} 个设备功能，共 ${total} 个`)

    // 获取每个功能的指令数量
    const functionIds = (functions || []).map((func: any) => func.id)
    let commandCounts: { [key: string]: number } = {}
    
    if (functionIds.length > 0) {
      const commandCountResult = await supabase
        .from('DeviceFunctionCommand')
        .select('function_id')
        .in('function_id', functionIds)
      
      if (commandCountResult.data) {
        commandCounts = commandCountResult.data.reduce((acc: { [key: string]: number }, item: any) => {
          acc[item.function_id] = (acc[item.function_id] || 0) + 1
          return acc
        }, {})
      }
    }

    // 转换数据格式 (snake_case -> camelCase)
    const formattedFunctions = (functions || []).map((func: any) => ({
      id: func.id,
      name: func.name,
      key: func.key,
      description: func.description,
      maxIntensity: func.max_intensity,
      isActive: func.is_active,
      createdAt: func.created_at,
      updatedAt: func.updated_at,
      commandCount: commandCounts[func.id] || 0
    }))

    return c.json({
      success: true,
      data: {
        data: formattedFunctions,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-FUNCTIONS] 获取设备功能列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取设备功能列表失败'
      },
      500
    )
  }
})

// ==================== 设备功能详情管理 ====================

// 获取设备功能详情（包括关联的指令集）
adminDeviceFunctions.get('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-DEVICE-FUNCTIONS] 获取设备功能详情:', id)

    // 查询设备功能详情
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceFunction')
      .select('*')
      .eq('id', id)
      .single()

    const { data: func, error } = handleSupabaseResult(result)

    if (error || !func) {
      return c.json(
        {
          success: false,
          message: '设备功能不存在'
        },
        404
      )
    }

    // 查询关联的指令集
    const commandsResult = await supabase
      .from('DeviceFunctionCommand')
      .select(`
        *,
        DeviceCommandSet (
          id,
          name,
          command,
          broadcast,
          description
        )
      `)
      .eq('function_id', id)
      .order('intensity', { ascending: true })

    const { data: commands, error: commandsError } = handleSupabaseResult(commandsResult)

    if (commandsError) {
      console.error('查询功能指令关联失败:', commandsError)
    }

    // 格式化数据 (snake_case -> camelCase)
    const formattedFunction = {
      id: func.id,
      name: func.name,
      key: func.key,
      description: func.description,
      maxIntensity: func.max_intensity,
      isActive: func.is_active,
      createdAt: func.created_at,
      updatedAt: func.updated_at,
      commands: (commands || []).map((cmd: any) => ({
        id: cmd.id,
        intensity: cmd.intensity,
        description: cmd.description,
        commandSet: cmd.DeviceCommandSet ? {
          id: cmd.DeviceCommandSet.id,
          name: cmd.DeviceCommandSet.name,
          command: cmd.DeviceCommandSet.command,
          broadcast: cmd.DeviceCommandSet.broadcast,
          description: cmd.DeviceCommandSet.description
        } : null
      }))
    }

    return c.json({
      success: true,
      data: formattedFunction
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-FUNCTIONS] 获取设备功能详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取设备功能详情失败'
      },
      500
    )
  }
})

// ==================== 设备功能创建和编辑 ====================

// 创建设备功能验证模式
const createFunctionSchema = z.object({
  name: z.string().min(1, '功能名称不能为空'),
  key: z.string().min(1, '功能键不能为空'),
  description: z.string().optional(),
  maxIntensity: z.number().min(1, '最大强度必须大于0').default(3),
  isActive: z.boolean().default(true)
})

// 创建设备功能
adminDeviceFunctions.post('/', authMiddleware, zValidator('json', createFunctionSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const functionData = c.req.valid('json')
    const env = c.env

    console.log('➕ [ADMIN-DEVICE-FUNCTIONS] 创建设备功能:', functionData.name)

    // 检查功能键是否已存在
    const supabase = getSupabase(env)
    const checkResult = await supabase
      .from('DeviceFunction')
      .select('id')
      .eq('key', functionData.key)
      .single()

    if (checkResult.data) {
      return c.json({ success: false, message: '功能键已存在' }, 400)
    }

    // 创建设备功能
    const result = await supabase
      .from('DeviceFunction')
      .insert([{
        name: functionData.name,
        key: functionData.key,
        description: functionData.description,
        max_intensity: functionData.maxIntensity,
        is_active: functionData.isActive
      }])
      .select()

    const { data: newFunctions, error } = handleSupabaseResult(result)

    if (error || !newFunctions || newFunctions.length === 0) {
      return c.json({ success: false, message: '创建设备功能失败' }, 500)
    }

    const newFunction = newFunctions[0]

    console.log('✅ [ADMIN-DEVICE-FUNCTIONS] 设备功能创建成功:', newFunction.id)

    return c.json({
      success: true,
      data: {
        id: newFunction.id,
        name: newFunction.name,
        key: newFunction.key,
        description: newFunction.description,
        maxIntensity: newFunction.max_intensity,
        isActive: newFunction.is_active,
        createdAt: newFunction.created_at,
        updatedAt: newFunction.updated_at
      },
      message: '设备功能创建成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-FUNCTIONS] 创建设备功能失败:', error)
    return c.json(
      {
        success: false,
        message: '创建设备功能失败'
      },
      500
    )
  }
})

// 更新设备功能
adminDeviceFunctions.put(
  '/:id',
  authMiddleware,
  zValidator('json', createFunctionSchema.partial()),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const updateData = c.req.valid('json')
      const env = c.env

      console.log('📝 [ADMIN-DEVICE-FUNCTIONS] 更新设备功能:', id)

      // 如果要更新功能键，检查是否已存在
      const supabase = getSupabase(env)
      if (updateData.key) {
        const checkResult = await supabase
          .from('DeviceFunction')
          .select('id')
          .eq('key', updateData.key)
          .neq('id', id)
          .single()

        if (checkResult.data) {
          return c.json({ success: false, message: '功能键已存在' }, 400)
        }
      }

      // 更新设备功能
      const updatePayload: any = {}

      if (updateData.name !== undefined) updatePayload.name = updateData.name
      if (updateData.key !== undefined) updatePayload.key = updateData.key
      if (updateData.description !== undefined) updatePayload.description = updateData.description
      if (updateData.maxIntensity !== undefined) updatePayload.max_intensity = updateData.maxIntensity
      if (updateData.isActive !== undefined) updatePayload.is_active = updateData.isActive

      updatePayload.updated_at = new Date().toISOString()

      const result = await supabase
        .from('DeviceFunction')
        .update(updatePayload)
        .eq('id', id)
        .select()

      const { data: updatedFunctions, error } = handleSupabaseResult(result)

      if (error || !updatedFunctions || updatedFunctions.length === 0) {
        return c.json({ success: false, message: '设备功能不存在' }, 404)
      }

      const updatedFunction = updatedFunctions[0]

      console.log('✅ [ADMIN-DEVICE-FUNCTIONS] 设备功能更新成功:', id)

      return c.json({
        success: true,
        data: {
          id: updatedFunction.id,
          name: updatedFunction.name,
          key: updatedFunction.key,
          description: updatedFunction.description,
          maxIntensity: updatedFunction.max_intensity,
          isActive: updatedFunction.is_active,
          createdAt: updatedFunction.created_at,
          updatedAt: updatedFunction.updated_at
        },
        message: '设备功能更新成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-DEVICE-FUNCTIONS] 更新设备功能失败:', error)
      return c.json(
        {
          success: false,
          message: '更新设备功能失败'
        },
        500
      )
    }
  }
)

// 删除设备功能
adminDeviceFunctions.delete('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🗑️ [ADMIN-DEVICE-FUNCTIONS] 删除设备功能:', id)

    // 删除设备功能
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceFunction')
      .delete()
      .eq('id', id)
      .select()

    const { data: deletedFunctions, error } = handleSupabaseResult(result)

    if (error || !deletedFunctions || deletedFunctions.length === 0) {
      return c.json({ success: false, message: '设备功能不存在' }, 404)
    }

    console.log('✅ [ADMIN-DEVICE-FUNCTIONS] 设备功能删除成功:', id)

    return c.json({
      success: true,
      message: '设备功能删除成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-FUNCTIONS] 删除设备功能失败:', error)
    return c.json(
      {
        success: false,
        message: '删除设备功能失败'
      },
      500
    )
  }
})

// ==================== 功能指令关联管理 ====================

// 为功能添加指令关联
const addFunctionCommandSchema = z.object({
  commandSetId: z.string().uuid('指令集ID格式不正确'),
  intensity: z.number().min(-1, '强度等级不能小于-1（-1表示停止）'),
  description: z.string().optional()
})

adminDeviceFunctions.post('/:id/commands', authMiddleware, zValidator('json', addFunctionCommandSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const functionId = c.req.param('id')
    const commandData = c.req.valid('json')
    const env = c.env

    console.log('➕ [ADMIN-DEVICE-FUNCTIONS] 为功能添加指令关联:', functionId)

    // 检查功能是否存在
    const supabase = getSupabase(env)
    const funcResult = await supabase
      .from('DeviceFunction')
      .select('id, max_intensity')
      .eq('id', functionId)
      .single()

    if (!funcResult.data) {
      return c.json({ success: false, message: '设备功能不存在' }, 404)
    }

    // 检查强度等级是否超过最大值
    if (commandData.intensity > funcResult.data.max_intensity) {
      return c.json({ success: false, message: '强度等级超过最大值' }, 400)
    }

    // 检查指令集是否存在
    const commandSetResult = await supabase
      .from('DeviceCommandSet')
      .select('id')
      .eq('id', commandData.commandSetId)
      .single()

    if (!commandSetResult.data) {
      return c.json({ success: false, message: '指令集不存在' }, 404)
    }

    // 检查是否已存在相同的功能-强度组合
    const existingResult = await supabase
      .from('DeviceFunctionCommand')
      .select('id')
      .eq('function_id', functionId)
      .eq('intensity', commandData.intensity)
      .single()

    if (existingResult.data) {
      return c.json({ success: false, message: '该强度等级已存在指令关联' }, 400)
    }

    // 创建关联
    const result = await supabase
      .from('DeviceFunctionCommand')
      .insert([{
        function_id: functionId,
        command_set_id: commandData.commandSetId,
        intensity: commandData.intensity,
        description: commandData.description
      }])
      .select()

    const { data: newCommands, error } = handleSupabaseResult(result)

    if (error || !newCommands || newCommands.length === 0) {
      return c.json({ success: false, message: '创建指令关联失败' }, 500)
    }

    console.log('✅ [ADMIN-DEVICE-FUNCTIONS] 指令关联创建成功')

    return c.json({
      success: true,
      message: '指令关联创建成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-FUNCTIONS] 创建指令关联失败:', error)
    return c.json(
      {
        success: false,
        message: '创建指令关联失败'
      },
      500
    )
  }
})

// 删除功能指令关联
adminDeviceFunctions.delete('/:id/commands/:commandId', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const functionId = c.req.param('id')
    const commandId = c.req.param('commandId')
    const env = c.env

    console.log('🗑️ [ADMIN-DEVICE-FUNCTIONS] 删除功能指令关联:', functionId, commandId)

    // 删除关联
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceFunctionCommand')
      .delete()
      .eq('id', commandId)
      .eq('function_id', functionId)
      .select()

    const { data: deletedCommands, error } = handleSupabaseResult(result)

    if (error || !deletedCommands || deletedCommands.length === 0) {
      return c.json({ success: false, message: '指令关联不存在' }, 404)
    }

    console.log('✅ [ADMIN-DEVICE-FUNCTIONS] 指令关联删除成功')

    return c.json({
      success: true,
      message: '指令关联删除成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-FUNCTIONS] 删除指令关联失败:', error)
    return c.json(
      {
        success: false,
        message: '删除指令关联失败'
      },
      500
    )
  }
})

export default adminDeviceFunctions
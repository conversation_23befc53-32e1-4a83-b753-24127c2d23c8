import CoreBluetooth
import Foundation

// 确保正确导入Capacitor
#if canImport(Capacitor)
  import Capacitor
#endif

/// 蓝牙广播插件 - iOS实现
@objcMembers
@objc(BleAdvertiserPlugin)
public class BleAdvertiserPlugin: CAPPlugin {
  // 蓝牙外设管理器
  private var peripheralManager: CBPeripheralManager?

  // 广播实例存储
  private var advertisingInstances = [Int: AdvertisingInstance]()

  // 日志标签
  private let TAG = "[BleAdvertiser]"

  /**
     * 插件加载时调用
     */
  override public func load() {
    NSLog("\(self.TAG) 插件加载")
  }

  /**
     * 初始化蓝牙服务
     */
  @objc func initialize(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 初始化蓝牙服务")

    // 如果已经初始化，则直接返回成功
    if peripheralManager != nil {
      NSLog("\(self.TAG) 蓝牙服务已初始化")
      call.resolve(["success": true])
      return
    }

    // 创建蓝牙外设管理器
    peripheralManager = CBPeripheralManager(delegate: self, queue: nil)

    // 延迟检查蓝牙状态，确保委托方法被调用
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [self] in
      if self.peripheralManager?.state == .poweredOn {
        NSLog("\(self.TAG) 蓝牙服务初始化成功")
        call.resolve(["success": true])
      } else {
        NSLog("\(self.TAG) 蓝牙服务初始化失败，状态: \(String(describing: self.peripheralManager?.state))")
        call.resolve([
          "success": false,
          "message": "蓝牙未启用或不可用",
        ])
      }
    }
  }

  /**
     * 检查蓝牙是否启用
     */
  @objc func isBluetoothEnabled(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 检查蓝牙是否启用")

    // 如果外设管理器未初始化，则初始化它
    if peripheralManager == nil {
      peripheralManager = CBPeripheralManager(delegate: self, queue: nil)

      // 延迟检查蓝牙状态
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [self] in
        let enabled = self.peripheralManager?.state == .poweredOn
        NSLog("\(self.TAG) 蓝牙状态: \(enabled ? "已启用" : "未启用")")
        call.resolve(["enabled": enabled])
      }
    } else {
      let enabled = peripheralManager?.state == .poweredOn
      NSLog("\(self.TAG) 蓝牙状态: \(enabled ? "已启用" : "未启用")")
      call.resolve(["enabled": enabled])
    }
  }

  /**
     * 开始广播
     */
  @objc func startAdvertising(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 开始广播")

    // 获取参数
    let mode = call.getInt("mode") ?? 2
    let manufacturerId = call.getInt("manufacturerId") ?? 255
    let instanceId =
      call.getInt("instanceId")
      ?? Int(Date().timeIntervalSince1970.truncatingRemainder(dividingBy: 10000))

    // 检查蓝牙状态
    guard let manager = peripheralManager, manager.state == .poweredOn else {
      NSLog("\(self.TAG) 蓝牙未启用或不可用")
      call.reject("蓝牙未启用或不可用")
      return
    }

    // 获取数据
    guard let dataBytes = getDataBytes(from: call) else {
      NSLog("\(self.TAG) 无效的数据格式")
      call.reject("无效的数据格式")
      return
    }

    // 停止之前的广播（如果存在）
    if let existingInstance = advertisingInstances[instanceId] {
      NSLog("\(self.TAG) 停止之前的广播实例: \(instanceId)")
      manager.stopAdvertising()
      existingInstance.timer?.invalidate()
      advertisingInstances.removeValue(forKey: instanceId)
    }

    // 创建广播数据
    var advertisementData: [String: Any] = [:]

    // 添加制造商数据
    let manufacturerData = NSData(
      bytes: [UInt8(manufacturerId & 0xff), UInt8((manufacturerId >> 8) & 0xff)] + dataBytes,
      length: dataBytes.count + 2)
    advertisementData[CBAdvertisementDataManufacturerDataKey] = manufacturerData

    // 根据模式设置广播功率
    if mode == 0 {  // 平衡模式
      advertisementData[CBAdvertisementDataLocalNameKey] = ""
    } else if mode == 2 {  // 低功耗模式
      advertisementData[CBAdvertisementDataLocalNameKey] = ""
      advertisementData[CBAdvertisementDataTxPowerLevelKey] = -20
    } else {  // 低延迟模式
      advertisementData[CBAdvertisementDataLocalNameKey] = ""
      advertisementData[CBAdvertisementDataTxPowerLevelKey] = 0
    }

    // 创建广播实例
    let instance = AdvertisingInstance(
      id: instanceId,
      data: advertisementData,
      call: call
    )

    // 保存广播实例
    advertisingInstances[instanceId] = instance

    // 开始广播
    NSLog("\(self.TAG) 开始广播数据, 实例ID: \(instanceId)")
    manager.stopAdvertising()  // 先停止之前的广播
    manager.startAdvertising(advertisementData)

    // 设置定时器检查广播状态
    instance.timer = Timer.scheduledTimer(withTimeInterval: 0.5, repeats: false) { [self] _ in
      if manager.isAdvertising {
        NSLog("\(self.TAG) 广播开始成功，实例ID: \(instanceId)")
        call.resolve([
          "success": true,
          "instanceId": instanceId,
        ])
      } else {
        NSLog("\(self.TAG) 广播开始失败，实例ID: \(instanceId)")
        self.advertisingInstances.removeValue(forKey: instanceId)
        call.resolve([
          "success": false,
          "instanceId": instanceId,
          "message": "广播启动失败",
        ])
      }
    }
  }

  /**
     * 停止广播
     */
  @objc func stopAdvertising(_ call: CAPPluginCall) {
    if let instanceId = call.getInt("instanceId") {
      NSLog("\(self.TAG) 停止广播, 实例ID: \(instanceId)")

      // 检查蓝牙状态
      guard let manager = peripheralManager else {
        NSLog("\(self.TAG) 蓝牙管理器不可用")
        call.resolve(["success": false, "message": "蓝牙管理器不可用"])
        return
      }

      // 检查是否存在该实例
      if advertisingInstances[instanceId] != nil {
        manager.stopAdvertising()
        advertisingInstances.removeValue(forKey: instanceId)
        NSLog("\(self.TAG) 已停止广播, 实例ID: \(instanceId)")
        call.resolve(["success": true])
      } else {
        NSLog("\(self.TAG) 找不到广播实例: \(instanceId)")
        call.resolve(["success": false, "message": "找不到广播实例: \(instanceId)"])
      }
    } else {
      // 如果没有提供实例ID，则停止所有广播
      stopAllAdvertising(call)
    }
  }

  /**
     * 停止所有广播
     */
  @objc func stopAllAdvertising(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 停止所有广播")

    // 检查蓝牙状态
    guard let manager = peripheralManager else {
      NSLog("\(self.TAG) 蓝牙管理器不可用")
      call.resolve(["success": false, "message": "蓝牙管理器不可用"])
      return
    }

    // 停止广播
    manager.stopAdvertising()

    // 清理所有实例
    for (id, instance) in advertisingInstances {
      instance.timer?.invalidate()
      NSLog("\(self.TAG) 已停止广播, 实例ID: \(id)")
    }

    // 清空实例列表
    advertisingInstances.removeAll()

    call.resolve(["success": true])
  }

  /**
     * 从调用参数中获取数据字节数组
     */
  private func getDataBytes(from call: CAPPluginCall) -> [UInt8]? {
    // 尝试获取数组格式
    if let dataArray = call.getArray("data", Int.self) {
      return dataArray.map { UInt8($0) }
    }

    // 尝试获取字符串格式（十六进制）
    if let dataString = call.getString("data") {
      return hexStringToByteArray(dataString)
    }

    return nil
  }

  /**
     * 将十六进制字符串转换为字节数组
     */
  private func hexStringToByteArray(_ hexString: String) -> [UInt8]? {
    // 移除所有空格和其他非十六进制字符
    let cleanString = hexString.replacingOccurrences(
      of: "[^0-9A-Fa-f]", with: "", options: .regularExpression)

    // 确保字符串长度为偶数
    guard cleanString.count % 2 == 0 else {
      return nil
    }

    var bytes = [UInt8]()
    var index = cleanString.startIndex

    while index < cleanString.endIndex {
      let nextIndex = cleanString.index(index, offsetBy: 2)
      let byteString = cleanString[index..<nextIndex]

      if let byte = UInt8(byteString, radix: 16) {
        bytes.append(byte)
      } else {
        return nil
      }

      index = nextIndex
    }

    return bytes
  }
}

/// 广播实例类
class AdvertisingInstance {
  let id: Int
  let data: [String: Any]
  let call: CAPPluginCall
  var timer: Timer?

  init(id: Int, data: [String: Any], call: CAPPluginCall) {
    self.id = id
    self.data = data
    self.call = call
  }
}

/// 蓝牙外设管理器委托
extension BleAdvertiserPlugin: CBPeripheralManagerDelegate {
  public func peripheralManagerDidUpdateState(_ peripheral: CBPeripheralManager) {
    NSLog("\(self.TAG) 蓝牙状态更新: \(peripheral.state)")
  }
}

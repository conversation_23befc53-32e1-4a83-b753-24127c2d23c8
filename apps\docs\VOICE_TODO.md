# 🎤 语音对话模块开发 TODO

## 📅 开发计划概述

- **预估工期**: 2-3 周
- **核心技术**: MediaRecorder API + Cloudflare Worker AI Whisper
- **集成方式**: 在现有 MultimodalInputV2 中添加语音功能

---

## 🏗️ Phase 1: 基础架构搭建 (3-4 天)

### 📦 创建核心 Hook

- [x] **`hooks/useVoiceRecorder.ts`** - 录制状态管理

  - [x] 录制状态 (idle/recording/processing/completed/error)
  - [x] 开始/停止录制逻辑
  - [x] 音频数据管理 (Blob, duration)
  - [x] 录制时长限制 (默认 60 秒)
  - [x] 音量检测和显示

- [x] **`hooks/useAudioPermission.ts`** - 麦克风权限管理

  - [x] 权限状态检测 (unknown/granted/denied/requesting)
  - [x] 权限请求逻辑
  - [x] 权限变化监听
  - [x] 错误处理和重试

- [x] **`hooks/useSpeechToText.ts`** - 语音转文本处理
  - [x] API 调用封装
  - [x] 上传进度跟踪
  - [x] 结果缓存机制
  - [x] 错误重试逻辑

### 🛠️ 创建工具模块

- [x] **`lib/audio/audioUtils.ts`** - 音频处理工具

  - [x] 音频格式检测和转换
  - [x] 音频压缩和优化
  - [x] 音频时长计算
  - [x] 波形数据生成
  - [x] 浏览器兼容性检测

- [x] **`api/endpoints/speechToText.ts`** - Whisper API 调用

  - [x] API 端点配置
  - [x] 文件上传封装
  - [x] 响应数据处理
  - [x] 错误码映射
  - [x] 超时和重试机制

- [x] **`lib/audio/audioRecorder.ts`** - MediaRecorder 封装
  - [x] 录制器初始化
  - [x] 多格式支持 (webm/mp4/wav)
  - [x] 实时音量监测
  - [x] 录制数据流处理
  - [x] 内存管理优化

### 🎯 后端 API 开发

- [ ] **`/api/speech-to-text`** - 语音转文本端点
  - [ ] 文件上传处理
  - [ ] Cloudflare Worker AI 集成
  - [ ] Whisper 模型调用
  - [ ] 错误处理和响应
  - [ ] 请求日志和监控

---

## 🎨 Phase 2: UI 组件开发 (4-5 天)

### 🧩 创建核心组件

- [x] **`components/voice/VoiceRecorder.tsx`** - 主录制组件

  - [x] 录制按钮 (麦克风图标)
  - [x] 状态切换逻辑 (默认/录制中/处理中)
  - [x] 长按和点击交互
  - [x] 取消录制手势 (上滑取消)
  - [x] 录制时长显示

- [x] **`components/voice/AudioWaveform.tsx`** - 音频波形显示

  - [x] 实时音量波形动画
  - [x] 录制完成后的静态波形
  - [x] 响应式设计适配
  - [x] 性能优化 (SVG)
  - [x] 颜色主题适配

- [x] **`components/voice/VoicePermissionGuide.tsx`** - 权限引导

  - [x] 权限请求引导界面
  - [x] 权限被拒绝的解决方案
  - [x] 浏览器差异化说明
  - [x] 重新请求权限按钮
  - [x] 友好的错误提示

- [x] **`components/voice/RecordingControls.tsx`** - 录制控制面板
  - [x] 重录按钮
  - [x] 发送按钮
  - [x] 取消按钮
  - [x] 播放预览按钮
  - [x] 录制质量设置

### 🎭 状态和动画设计

- [x] **录制状态动画**

  - [x] 麦克风图标脉动效果
  - [x] 录制中的红色渐变背景
  - [x] 音波流动动画
  - [x] 按钮状态过渡动画
  - [x] 加载和处理动画

- [x] **交互反馈**
  - [x] 长按触觉反馈 (移动端)
  - [x] 悬停状态提示 (桌面端)
  - [x] 手势引导动画
  - [x] 错误状态抖动效果
  - [x] 成功状态确认动画

---

## 🔗 Phase 3: 系统集成 (2-3 天)

### 📱 集成到现有组件

- [x] **修改 `MultimodalInputV2.tsx`**

  - [x] 添加语音录制按钮
  - [x] 布局调整适配新功能
  - [x] 与现有发送逻辑集成
  - [x] 状态管理整合
  - [x] 键盘避让适配

- [ ] **修改 `chat.tsx`**
  - [ ] 语音消息处理逻辑
  - [ ] 与聊天流程集成
  - [ ] 错误处理集成
  - [ ] 消息类型扩展

### 📐 响应式适配

- [ ] **移动端优化**

  - [ ] 触摸交互优化
  - [ ] 屏幕尺寸适配
  - [ ] 键盘弹出处理
  - [ ] 横竖屏切换
  - [ ] 手势冲突避免

- [ ] **桌面端增强**
  - [ ] 鼠标交互优化
  - [ ] 快捷键支持 (空格录制)
  - [ ] 悬停提示完善
  - [ ] 窗口大小适配
  - [ ] 多显示器支持

---

## 🧪 Phase 4: 测试和优化 (2-3 天)

### ✅ 功能测试

- [ ] **基础功能测试**

  - [ ] 录制功能完整性
  - [ ] 权限处理正确性
  - [ ] API 调用稳定性
  - [ ] 错误处理覆盖
  - [ ] 边界条件测试

- [ ] **兼容性测试**
  - [ ] 主流浏览器测试 (Chrome/Safari/Firefox/Edge)
  - [ ] 移动设备测试 (iOS/Android)
  - [ ] 不同网络环境测试
  - [ ] 音频格式兼容性
  - [ ] 老设备性能测试

### 🚀 性能优化

- [ ] **内存管理**

  - [ ] 音频数据及时释放
  - [ ] 组件卸载清理
  - [ ] 内存泄漏检测
  - [ ] 大文件处理优化
  - [ ] 缓存策略优化

- [ ] **用户体验优化**
  - [ ] 启动速度优化
  - [ ] 响应延迟优化
  - [ ] 动画性能优化
  - [ ] 网络请求优化
  - [ ] 错误恢复优化

---

## 🔧 Phase 5: 高级功能 (可选扩展)

### 🌟 增强功能

- [ ] **实时转换**

  - [ ] 边录边转功能
  - [ ] 实时文本预览
  - [ ] 延迟优化
  - [ ] 准确率提升

- [ ] **多语言支持**

  - [ ] 语言自动检测
  - [ ] 多语言混合识别
  - [ ] 语言偏好设置
  - [ ] 地区适配优化

- [ ] **语音命令**
  - [ ] "发送消息"语音命令
  - [ ] "取消录制"语音命令
  - [ ] 设备控制语音命令
  - [ ] 自定义命令支持

### ⚙️ 配置和设置

- [ ] **用户设置面板**

  - [ ] 录制质量选择
  - [ ] 自动发送开关
  - [ ] 语言偏好设置
  - [ ] 录制时长限制
  - [ ] 音频格式偏好

- [ ] **统计和分析**
  - [ ] 使用频率统计
  - [ ] 转换准确率跟踪
  - [ ] 错误率监控
  - [ ] 性能指标收集

---

## 📋 技术细节清单

### 🔧 技术栈确认

- [x] **前端**: React + TypeScript + HeroUI
- [x] **音频录制**: MediaRecorder API
- [x] **语音转文本**: Cloudflare Worker AI Whisper
- [x] **状态管理**: React Hooks + Context
- [x] **样式**: Tailwind CSS + 现有设计系统

### 📦 依赖包管理

- [ ] 评估是否需要新增音频处理库
- [ ] 确认浏览器 API 兼容性 polyfill 需求
- [ ] 优化 bundle 大小影响
- [ ] 设置懒加载策略

### 🔒 安全和隐私

- [ ] 音频数据传输加密
- [ ] 临时文件清理策略
- [ ] 用户隐私保护说明
- [ ] 敏感信息过滤机制

---

## 🎯 验收标准

### ✅ 核心功能

- [ ] 可以正常录制和播放音频
- [ ] 语音转文本准确率 >90%
- [ ] 权限处理流畅无卡顿
- [ ] 错误处理友好完整
- [ ] 移动端和桌面端体验一致

### 📊 性能指标

- [ ] 录制启动延迟 <200ms
- [ ] 语音转文本响应 <5s
- [ ] 内存使用增长 <50MB
- [ ] 首次加载影响 <500KB
- [ ] 动画帧率 >30fps

### 🔧 兼容性要求

- [ ] Chrome 80+, Safari 14+, Firefox 75+, Edge 80+
- [ ] iOS 14+, Android 8+
- [ ] 网络环境: 2G/3G/4G/5G/WiFi
- [ ] 设备性能: 低中高端全覆盖

### 状态管理架构

stateDiagram-v2
[*] --> Idle
Idle --> PermissionRequesting: 首次使用
PermissionRequesting --> PermissionGranted: 用户授权
PermissionRequesting --> PermissionDenied: 用户拒绝
PermissionDenied --> PermissionRequesting: 重新请求

    PermissionGranted --> Recording: 开始录制
    Recording --> Processing: 结束录制
    Recording --> Cancelled: 取消录制

    Processing --> Completed: 转换成功
    Processing --> Error: 转换失败

    Completed --> Idle: 发送消息
    Error --> Idle: 重试或取消
    Cancelled --> Idle: 返回初始状态

### 数据流向设计

sequenceDiagram
participant U as 用户
participant VR as VoiceRecorder
participant AP as useAudioPermission
participant AR as useVoiceRecorder
participant MR as MediaRecorder
participant AU as audioUtils
participant API as speechToTextApi
participant CF as Cloudflare AI
participant MI as MultimodalInput

    U->>VR: 长按录制按钮
    VR->>AP: 检查麦克风权限
    AP->>VR: 权限状态
    VR->>AR: 开始录制
    AR->>MR: 初始化录制器
    MR->>AR: 音频数据流
    AR->>VR: 更新录制状态
    U->>VR: 松开按钮
    VR->>AR: 停止录制
    AR->>AU: 处理音频数据
    AU->>API: 发送音频文件
    API->>CF: 调用Whisper模型
    CF->>API: 返回文本结果
    API->>AR: 转换完成
    AR->>MI: 填入文本到输入框
    MI->>U: 显示转换结果

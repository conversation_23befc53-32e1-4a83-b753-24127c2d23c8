// 聊天相关类型定义

// 消息类型
export interface LangChainMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  parts: Array<{
    type: 'text'
    text: string
  }>
  attachments?: Array<{
    url: string
    name: string
    contentType: string
  }>
  createdAt: Date
}

// 聊天选项
export interface LangChainChatOptions {
  chatId: string
  message: LangChainMessage
  userId: string
  userType: import('./index').UserType
  username: string
  userGender?: string // 用户性别（可选）
  characterId?: string
  language?: string // 用户语言（可选）
}

// 聊天上下文
export interface ChatContext {
  messages: LangChainMessage[]
  character?: import('./index').CharacterType
  systemPrompt: string
}

// 聊天响应
export interface ChatResponse {
  id: string
  content: string
  parts: Array<{
    type: 'text'
    text: string
  }>
  attachments?: Array<{
    url: string
    name: string
    contentType: string
  }>
  createdAt: Date
}

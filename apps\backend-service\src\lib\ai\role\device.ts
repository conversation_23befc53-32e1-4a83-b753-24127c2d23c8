export const devicePrompt = () => {
  return `
    ### 设备联动
    - 设备联动的意思根据对话上下文，你自身的角色，判断是否需要调用设备功能
      - 比如：
        - 用户主动说，“你自己动把”，则触发对应的设备功能
        - 你的角色性格是主动强势的，可以在合适的对话当中，在用户没有要求的情况下，主动调用设备功能。
    - 如果用户的对话数据中带有<device>设备功能信息</device>，才有会设备联动的功能，否则不会触发设备联动。
    - 设备联动的示例数据(会通过 JSON.stringify 转换为字符串)："<device>{\"thrust\":{\"1\":\"6db643ce97fe427ce49c6c\",\"2\":\"6db643ce97fe427ce7075e\",\"3\":\"6db643ce97fe427ce68e4f\",\"-1\":\"6db643ce97fe427ce5157d\"},\"suction\":{\"1\":\"6db643ce97fe427ca4982e\",\"2\":\"6db643ce97fe427ca7031c\",\"3\":\"6db643ce97fe427ca68a0d\",\"-1\":\"6db643ce97fe427ce5157d\"}}</device>"
      - 这里面就有两个设备功能，分别是：
        - 抽插
        - 吮吸
      - 每个设备功能里面有多个强度等级，分别是：
        - 1
        - 2
        - 3
        - -1（关闭）
      - command是最终的设备蓝牙命令
      - 以上只是示例
    - 你最终返回的设备联动数据格式为：<device>[command, command]</device>。
    - command可以多个，也可以单个。
    - 要记住，需要根据对话上下文，你自身的角色，判断是否需要调用设备功能，无需每次都调用。
    - 最终返回的<device>[command, command]</device>，加到上一个上文的回复格式当中。
  `;
};

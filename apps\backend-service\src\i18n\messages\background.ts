// 背景图相关消息
export const backgroundMessages = {
  zh: {
    auth_failed: '用户认证失败',
    user_not_found: '用户不存在',
    scene_description_required: '场景描述不能为空',
    scene_description_too_long: '场景描述过长，请控制在500字符以内',
    generate_failed: '生成背景图失败，请稍后重试',
    get_background_failed: '获取背景图失败',
    generate_success: '背景图生成成功',
    get_success: '获取背景图成功'
  },
  'zh-TW': {
    auth_failed: '使用者認證失敗',
    user_not_found: '使用者不存在',
    scene_description_required: '場景描述不能為空',
    scene_description_too_long: '場景描述過長，請控制在500字元以內',
    generate_failed: '生成背景圖失敗，請稍後重試',
    get_background_failed: '取得背景圖失敗',
    generate_success: '背景圖生成成功',
    get_success: '取得背景圖成功'
  },
  ja: {
    auth_failed: 'ユーザー認証に失敗しました',
    user_not_found: 'ユーザーが見つかりません',
    scene_description_required: 'シーンの説明を入力してください',
    scene_description_too_long: 'シーンの説明が長すぎます。500文字以内にしてください',
    generate_failed: '背景画像の生成に失敗しました。しばらく後に再試行してください',
    get_background_failed: '背景画像の取得に失敗しました',
    generate_success: '背景画像の生成に成功しました',
    get_success: '背景画像の取得に成功しました'
  },
  es: {
    auth_failed: 'Error de autenticación de usuario',
    user_not_found: 'Usuario no encontrado',
    scene_description_required: 'La descripción de la escena no puede estar vacía',
    scene_description_too_long:
      'La descripción de la escena es demasiado larga, por favor manténgala dentro de 500 caracteres',
    generate_failed: 'Error al generar la imagen de fondo, por favor inténtelo más tarde',
    get_background_failed: 'Error al obtener la imagen de fondo',
    generate_success: 'Imagen de fondo generada correctamente',
    get_success: 'Imagen de fondo obtenida correctamente'
  },
  en: {
    auth_failed: 'User authentication failed',
    user_not_found: 'User not found',
    scene_description_required: 'Scene description cannot be empty',
    scene_description_too_long:
      'Scene description is too long, please keep it within 500 characters',
    generate_failed: 'Failed to generate background image, please try again later',
    get_background_failed: 'Failed to get background image',
    generate_success: 'Background image generated successfully',
    get_success: 'Background image retrieved successfully'
  }
}

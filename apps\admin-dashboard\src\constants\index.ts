// API 基础配置
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api'

// 分页配置
export const DEFAULT_PAGE_SIZE = 20
export const PAGE_SIZE_OPTIONS = ['10', '20', '50', '100']

// 菜单配置
export const MENU_ITEMS = [
  {
    key: 'dashboard',
    label: '数据概览',
    icon: 'DashboardOutlined',
    path: '/dashboard'
  },
  {
    key: 'users',
    label: '用户管理',
    icon: 'UserOutlined',
    path: '/users'
  },
  {
    key: 'orders',
    label: '订单管理',
    icon: 'ShoppingOutlined',
    path: '/orders'
  },
  {
    key: 'membership',
    label: '会员管理',
    icon: 'CrownOutlined',
    children: [
      {
        key: 'membership-plans',
        label: '会员套餐',
        path: '/membership/plans'
      },
      {
        key: 'membership-subscriptions',
        label: '用户订阅',
        path: '/membership/subscriptions'
      }
    ]
  },
  {
    key: 'points',
    label: '积分管理',
    icon: 'GiftOutlined',
    children: [
      {
        key: 'points-packages',
        label: '积分套餐',
        path: '/points/packages'
      },
      {
        key: 'points-users',
        label: '用户积分',
        path: '/points/users'
      }
    ]
  },
  {
    key: 'content',
    label: '内容管理',
    icon: 'FileTextOutlined',
    children: [
      {
        key: 'characters',
        label: '角色管理',
        path: '/content/characters'
      },
      {
        key: 'scripts',
        label: '剧本管理',
        path: '/content/scripts'
      }
    ]
  },
  {
    key: 'marketing',
    label: '营销管理',
    icon: 'BulbOutlined',
    children: [
      {
        key: 'invite-codes',
        label: '邀请码管理',
        path: '/marketing/invite-codes'
      },
      {
        key: 'commissions',
        label: '佣金管理',
        path: '/marketing/commissions'
      },
      {
        key: 'withdrawals',
        label: '提现审核',
        path: '/marketing/withdrawals'
      }
    ]
  },
  {
    key: 'devices',
    label: '设备管理',
    icon: 'TabletOutlined',
    path: '/devices'
  },
  {
    key: 'settings',
    label: '系统设置',
    icon: 'SettingOutlined',
    path: '/settings'
  }
]

// 订单状态选项
export const ORDER_STATUS_OPTIONS = [
  { label: '待支付', value: 'pending', color: 'orange' },
  { label: '已完成', value: 'completed', color: 'green' },
  { label: '已失败', value: 'failed', color: 'red' },
  { label: '已取消', value: 'cancelled', color: 'gray' }
]

// 会员状态选项
export const SUBSCRIPTION_STATUS_OPTIONS = [
  { label: '有效', value: 'active', color: 'green' },
  { label: '已过期', value: 'expired', color: 'red' },
  { label: '已取消', value: 'cancelled', color: 'gray' },
  { label: '待处理', value: 'pending', color: 'orange' }
]

// 提现状态选项
export const WITHDRAW_STATUS_OPTIONS = [
  { label: '待审核', value: 'pending', color: 'orange' },
  { label: '已批准', value: 'approved', color: 'blue' },
  { label: '已拒绝', value: 'rejected', color: 'red' },
  { label: '已完成', value: 'completed', color: 'green' }
]

// 性别选项
export const GENDER_OPTIONS = [
  { label: '男', value: 'male' },
  { label: '女', value: 'female' },
  { label: '未设置', value: '' }
]

// 表格通用配置
export const TABLE_CONFIG = {
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
}
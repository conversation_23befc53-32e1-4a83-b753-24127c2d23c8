import React, { useMemo, useState, useRef, useCallback, useEffect } from 'react'
import { Card, CardBody, Button, Progress, Chip } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'
import type { CharacterData } from '..'
import { getLabel, getColorForField, generateKeywords } from '../mapping'
import { CharacterGenerationLoading } from '../loading-screen'
import { useNavigate } from 'react-router'
import { apiService } from '@/api'
import { addToast } from '@heroui/react'
import { PermissionGuard } from '@/components/permission'
import { useUserCharactersStore } from '@/stores/user-characters-store'

interface PreviewProps {
  data: CharacterData
}

// 对应每个section的组件
const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div>
    <h3 className="font-medium text-xl mb-2 text-primary">{title}</h3>
    {children}
  </div>
)

export default function Preview({ data }: PreviewProps) {
  const { t } = useTranslation(['customRole'])
  const navigate = useNavigate()
  // 添加状态管理生成过程
  const [generating, setGenerating] = useState(false)
  const [generatedImage, setGeneratedImage] = useState<string | null>(null)
  const [showLoadingScreen, setShowLoadingScreen] = useState(false)
  // 添加进度相关状态
  const [progress, setProgress] = useState(0)
  const [taskId, setTaskId] = useState<string | null>(null)
  const [estimatedSteps, setEstimatedSteps] = useState(0)
  const [completedSteps, setCompletedSteps] = useState(0)

  // 轮询相关引用
  const pollIntervalRef = useRef<NodeJS.Timeout | null>(null)
  const isPollingRef = useRef(false)

  // 添加一个状态来跟踪生成流程
  const [isGenerationComplete, setIsGenerationComplete] = useState(false)
  const [generationKeywords, setGenerationKeywords] = useState('')
  const [generationPrompt, setGenerationPrompt] = useState('')

  // 直接使用gender字段，如果没有则默认为female
  const gender = useMemo(() => {
    return (data.gender as 'male' | 'female') || 'female'
  }, [data.gender])

  // 检查是否有缺失的必要信息
  const missingFields = Object.entries({
    name: t('customRole:preview.missing_fields.name'),
    relationship: t('customRole:preview.missing_fields.relationship'),
    ethnicity: t('customRole:preview.missing_fields.ethnicity'),
    age: t('customRole:preview.missing_fields.age'),
    eyeColor: t('customRole:preview.missing_fields.eyeColor'),
    hairStyle: t('customRole:preview.missing_fields.hairStyle'),
    hairColor: t('customRole:preview.missing_fields.hairColor'),
    bodyType: t('customRole:preview.missing_fields.bodyType'),
    personality: t('customRole:preview.missing_fields.personality'),
    clothing: t('customRole:preview.missing_fields.clothing'),
    voiceModelId: t('customRole:preview.missing_fields.voiceModelId')
  }).filter(([key]) => !data[key as keyof CharacterData])

  // 计算完成度 - 在UI中使用
  const completionRate = useMemo(() => {
    const totalFields = 11 // 基础必填字段总数
    const completedFields = totalFields - missingFields.length
    return Math.round((completedFields / totalFields) * 100)
  }, [missingFields.length])

  // 获取角色头像图片
  const avatarImage = useMemo(() => {
    if (!data.ethnicity) return null

    // 基于性别和人种选择合适的头像
    const ethnicityType = data.ethnicity.split('-')[1] || 'caucasian'
    let imgNum = 1

    // 映射人种到对应的图片编号
    switch (ethnicityType) {
      case 'caucasian':
        imgNum = 1
        break
      case 'latina':
        imgNum = 2
        break
      case 'asian':
        imgNum = 3
        break
      case 'arab':
        imgNum = 4
        break
      case 'black':
        imgNum = 5
        break
      default:
        imgNum = 1
    }

    const prefix = gender === 'female' ? 'female' : 'male'
    return `/images/custom/${prefix}_0${imgNum}.png`
  }, [data.ethnicity, gender])

  // 清理轮询
  const clearPolling = useCallback(() => {
    if (pollIntervalRef.current) {
      clearInterval(pollIntervalRef.current)
      pollIntervalRef.current = null
    }
    isPollingRef.current = false
  }, [])

  // 轮询任务状态
  const pollTaskStatus = useCallback(
    async (taskId: string) => {
      if (isPollingRef.current) return // 防止重复轮询

      isPollingRef.current = true
      let isPollingActive = true // 用于控制轮询是否继续

      const poll = async (): Promise<boolean> => {
        try {
          const result = await apiService.image.getImageGenerationStatusV3(taskId)

          if (!result.success) {
            throw new Error(t('customRole:preview.error.api_error'))
          }

          const { data } = result

          // 更新进度状态
          setProgress(data.progress)
          setEstimatedSteps(data.estimatedSteps)
          setCompletedSteps(data.completedSteps)

          // 如果任务完成，设置生成的图片并停止轮询
          if (data.status === 'completed' && data.imageUrl) {
            setGeneratedImage(data.imageUrl)
            isPollingActive = false // 停止轮询
            clearPolling()
            return true // 返回true表示任务完成
          }

          // 如果任务失败，抛出错误并停止轮询
          if (data.status === 'failed') {
            isPollingActive = false // 停止轮询
            clearPolling()
            throw new Error(
              data.errorMessage || t('customRole:preview.error.image_generation_failed')
            )
          }

          return false // 返回false表示任务还在进行中
        } catch (error) {
          console.error(t('customRole:preview.error.polling_task_status_failed'), error)
          isPollingActive = false
          clearPolling()
          throw error
        }
      }

      // 递归轮询函数，确保上一个请求完成后才发起下一个
      const recursivePoll = async () => {
        if (!isPollingActive) return

        try {
          const isCompleted = await poll()
          if (isCompleted || !isPollingActive) {
            return // 任务完成或轮询被停止
          }

          // 等待1.5秒后继续下一次轮询
          setTimeout(() => {
            if (isPollingActive && isPollingRef.current) {
              recursivePoll()
            }
          }, 1500)
        } catch (error) {
          console.error(t('customRole:preview.error.error_during_polling'), error)
          isPollingActive = false
          throw error
        }
      }

      // 开始轮询
      await recursivePoll()
    },
    [clearPolling]
  )

  // 保存数据到数据库的函数
  const saveToDatabase = async (
    characterData: CharacterData,
    keywords: string,
    prompt: string,
    imageUrl: string
  ) => {
    try {
      // 在保存之前检查角色创建权限
      console.log(t('customRole:preview.log.checking_character_creation_permission'))
      const permissionResponse = await apiService.membership.verifyAccess('CHARACTER_CREATE')

      if (!permissionResponse.data.canAccess) {
        throw new Error(
          permissionResponse.data.reason ||
            t('customRole:preview.error.insufficient_character_creation_permission')
        )
      }

      console.log(t('customRole:preview.log.permission_check_passed_saving_character_data'))

      // 调用API保存角色数据
      const response = await apiService.roles.createCharacter({
        characterData,
        keywords,
        prompt,
        imageUrl
      })

      if (!response) {
        throw new Error(t('customRole:preview.error.failed_to_save_character_data'))
      }

      // 成功创建角色后，更新Zustand store缓存
      if (response.character) {
        const userCharactersStore = useUserCharactersStore.getState()
        userCharactersStore.addUserCharacter(response.character)
        console.log(
          t('customRole:preview.log.new_character_added_to_cache'),
          response.character.name
        )
      }

      return response
    } catch (error) {
      console.error(t('customRole:preview.error.failed_to_save_to_database'), error)
      throw new Error(
        error instanceof Error
          ? error.message
          : t('customRole:preview.error.failed_to_save_to_database')
      )
    }
  }

  // 监听 generatedImage 变化，当图片生成完成时执行后续操作
  useEffect(() => {
    if (generatedImage && isGenerationComplete && generationKeywords && generationPrompt) {
      const completeGeneration = async () => {
        try {
          console.log(t('customRole:preview.log.start_saving_character_data_to_database'))

          // 4. 数据存储
          await saveToDatabase(data, generationKeywords, generationPrompt, generatedImage)

          addToast({
            title: t('customRole:preview.toast.character_image_generated'),
            color: 'success'
          })

          // 5. 创建成功后，延迟一小段时间让用户看到成功提示，然后跳转到首页
          setTimeout(() => {
            navigate('/')
          }, 1500)
        } catch (error) {
          console.error(t('customRole:preview.error.failed_to_save_data'), error)
        } finally {
          // 重置生成状态，防止重复执行
          setIsGenerationComplete(false)
          setGenerationKeywords('')
          setGenerationPrompt('')
          setGenerating(false)
          setShowLoadingScreen(false)
          clearPolling() // 确保轮询已停止
          // 重置进度状态
          setProgress(0)
          setTaskId(null)
          setEstimatedSteps(0)
          setCompletedSteps(0)
          setGeneratedImage(null) // 重置生成的图片状态
        }
      }

      completeGeneration()
    }
  }, [
    generatedImage,
    isGenerationComplete,
    generationKeywords,
    generationPrompt,
    data,
    navigate,
    clearPolling
  ])

  // 组件卸载时清理轮询
  useEffect(() => {
    return () => {
      clearPolling()
    }
  }, [clearPolling])

  // 生成角色图像并跳转到主页
  const handleGenerate = async () => {
    // 检查是否有缺失字段
    if (missingFields.length > 0) {
      addToast({
        title: t('customRole:preview.toast.complete_all_required_fields'),
        color: 'danger'
      })
      return
    }

    setGenerating(true)
    setShowLoadingScreen(true) // 显示全屏加载动画

    // 云雾AI同步生成，设置模拟进度
    setProgress(0)
    setEstimatedSteps(1)
    setCompletedSteps(0)

    try {
      // 1. 得到所选的参数，组合成一串关键词
      const keywords = generateKeywords(gender, data)
      console.log(t('customRole:preview.log.generated_keywords'), keywords)

      // 使用新的一体化接口：关键词优化 + 图片生成（使用云雾AI）
      const response = await apiService.image.generateWithOptimization({
        keywords,
        generator: 'yunwu',
        waitForCompletion: true // 云雾AI是同步生成，等待完成
      })

      if (!response?.success || !response?.data) {
        throw new Error(t('customRole:preview.error.character_image_api_response_format_error'))
      }

      const { data: responseData } = response

      // 保存关键词和优化后的prompt，用于后续保存数据库
      setGenerationKeywords(responseData.originalKeywords)
      setGenerationPrompt(responseData.optimizedPrompt)

      console.log(t('customRole:preview.log.optimized_prompt'), responseData.optimizedPrompt)

      // 云雾AI同步生成，直接处理结果
      if (responseData.type === 'completed' && responseData.imageUrl) {
        // 更新进度为完成状态
        setProgress(100)
        setCompletedSteps(1)

        // 直接设置生成的图片
        setGeneratedImage(responseData.imageUrl)
        setIsGenerationComplete(true)
        console.log(
          t('customRole:preview.log.yunwu_ai_image_generation_completed'),
          responseData.imageUrl
        )
      } else if (responseData.type === 'async' && responseData.taskId) {
        // 如果是异步任务，开始轮询（备用方案）
        setTaskId(responseData.taskId)
        await pollTaskStatus(responseData.taskId)
        console.log(
          t('customRole:preview.log.image_generation_task_submitted'),
          responseData.taskId
        )
      } else {
        throw new Error(t('customRole:preview.error.no_image_url_or_task_id'))
      }
    } catch (error) {
      console.error(t('customRole:preview.error.failed_to_generate_character_image'), error)
      // 出错时重置状态
      setIsGenerationComplete(false)
      setGenerationKeywords('')
      setGenerationPrompt('')
      setGenerating(false)
      setShowLoadingScreen(false)
      clearPolling()
      // 重置进度状态
      setProgress(0)
      setTaskId(null)
      setEstimatedSteps(0)
      setCompletedSteps(0)
    }
  }

  return (
    <>
      {/* 显示全屏加载动画 - 传递真实进度数据 */}
      {showLoadingScreen && (
        <CharacterGenerationLoading
          progress={progress}
          estimatedSteps={estimatedSteps}
          completedSteps={completedSteps}
          taskId={taskId}
        />
      )}

      <Section title={t('customRole:preview.confirm_character_info')}>
        {/* 角色预览和完成度 */}
        <div className="flex flex-col sm:flex-row gap-2 mb-4">
          {/* 角色图像预览 */}
          <div className="shrink-0 w-full mx-auto sm:mx-0">
            <div className="aspect-square rounded-xl overflow-hidden border-2 border-muted bg-muted/10 relative">
              {generatedImage ? (
                <img
                  src={generatedImage}
                  alt={data.name || t('customRole:preview.unnamed_character')}
                  className="w-full h-full object-cover"
                />
              ) : avatarImage ? (
                <img
                  src={avatarImage}
                  alt={data.name || t('customRole:preview.unnamed_character')}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Icon icon="solar:user-bold" className="size-20 text-default-300" />
                </div>
              )}
              <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/70 to-transparent py-3 px-4">
                <div className="text-white font-medium text-lg truncate">
                  {data.name || t('customRole:preview.unnamed_character')}
                </div>
                <div className="text-white/70 text-sm truncate">
                  {getLabel('relationship', data.relationship || '')}
                </div>
              </div>
            </div>
          </div>

          {/* 完成度和生成按钮 */}
          <div className="flex-1 flex flex-col">
            {/* 显示完成度 */}
            <div className="mb-3">
              <Progress
                value={completionRate}
                color="primary"
                size="sm"
                classNames={{
                  track: 'bg-default-200',
                  indicator: 'bg-gradient-to-r from-primary to-secondary'
                }}
              />
            </div>
            <div className="text-xs text-default-500 mb-2 text-right">
              {t('customRole:preview.completion_rate')}: {completionRate}%
            </div>

            {/* 生成按钮 - 带权限守卫 */}
            <PermissionGuard
              feature="character_create"
              uiStrategy="modal"
              featureName={t('customRole:preview.create_character')}
            >
              {({ hasPermission, isChecking, checkAndExecute }) => (
                <Button
                  onPress={async () => {
                    const result = await checkAndExecute(handleGenerate)
                    if (!result.success) {
                      console.log(
                        t('customRole:preview.error.permission_denied_character_creation_failed')
                      )
                    }
                  }}
                  className="w-full h-14 text-lg font-medium"
                  isDisabled={
                    missingFields.length > 0 || generating || isChecking || hasPermission === false
                  }
                  isLoading={generating || isChecking}
                  color={
                    missingFields.length === 0 && hasPermission !== false ? 'primary' : 'default'
                  }
                  variant={
                    missingFields.length === 0 && hasPermission !== false ? 'solid' : 'bordered'
                  }
                  size="lg"
                >
                  {generating ? (
                    <>
                      <Icon icon="solar:refresh-bold" className="mr-2 h-5 w-5 animate-spin" />
                      {t('customRole:preview.generating')}
                    </>
                  ) : isChecking ? (
                    <>
                      <Icon icon="solar:refresh-bold" className="mr-2 h-5 w-5 animate-spin" />
                      {t('customRole:preview.checking_permission')}
                    </>
                  ) : hasPermission === false ? (
                    <>
                      <Icon icon="solar:lock-bold" className="mr-2 h-5 w-5" />
                      {t('customRole:preview.permission_required')}
                    </>
                  ) : (
                    <>
                      <Icon icon="solar:magic-stick-3-bold" className="mr-2 h-5 w-5" />
                      {t('customRole:preview.generate_character_image')}
                    </>
                  )}
                </Button>
              )}
            </PermissionGuard>
          </div>
        </div>

        {/* 如果有缺失项，显示警告 */}
        {missingFields.length > 0 && (
          <Card className="bg-warning-50 border-warning-200 mb-5">
            <CardBody className="p-4">
              <div className="flex items-start gap-3">
                <Icon
                  icon="solar:danger-circle-bold"
                  className="size-5 mt-0.5 shrink-0 text-warning-500"
                />
                <div>
                  <h3 className="font-medium text-warning-800">
                    {t('customRole:preview.missing_info_warning')}
                  </h3>
                  <ul className="grid grid-cols-2 gap-x-4 gap-y-1 pl-5 mt-2 text-sm list-disc text-warning-700">
                    {missingFields.map(([key, label]) => (
                      <li key={key}>{label}</li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardBody>
          </Card>
        )}

        {/* 角色信息卡片 */}
        <Card className="mb-6">
          <CardBody className="p-0">
            {/* 标题区域 */}
            <div className="bg-default-100 px-4 py-3 border-b border-default-200">
              <div className="flex justify-between items-center">
                <h3 className="text-xl font-semibold text-foreground">
                  {t('customRole:preview.character_details')}
                </h3>
                <Chip color={gender === 'male' ? 'primary' : 'secondary'} variant="flat" size="sm">
                  {gender === 'male'
                    ? t('customRole:preview.male_character')
                    : t('customRole:preview.female_character')}
                </Chip>
              </div>
            </div>
            {/* 角色信息列表 */}
            <div className="divide-y divide-default-200">
              <InfoItem
                icon={<Icon icon="solar:global-bold" className="size-5" />}
                label={t('customRole:preview.missing_fields.ethnicity')}
                value={getLabel('ethnicity', data.ethnicity || '')}
              />
              <InfoItem
                icon={<Icon icon="solar:calendar-bold" className="size-5" />}
                label={t('customRole:preview.missing_fields.age')}
                value={getLabel('age', data.age || '')}
              />
              <InfoItem
                icon={
                  <Icon
                    icon="solar:eye-bold"
                    className={`size-5 ${getColorForField('eyeColor', data.eyeColor || '')}`}
                  />
                }
                label={t('customRole:preview.missing_fields.eyeColor')}
                value={getLabel('eyeColor', data.eyeColor || '')}
              />
              <InfoItem
                icon={<Icon icon="solar:scissors-bold" className="size-5" />}
                label={t('customRole:preview.missing_fields.hairStyle')}
                value={data.hairStyle || t('customRole:preview.not_set')}
              />
              <InfoItem
                icon={
                  <Icon
                    icon="solar:palette-bold"
                    className={`size-5 ${getColorForField('hairColor', data.hairColor || '')}`}
                  />
                }
                label={t('customRole:preview.missing_fields.hairColor')}
                value={getLabel('hairColor', data.hairColor || '')}
              />
              <InfoItem
                icon={<Icon icon="solar:body-bold" className="size-5" />}
                label={t('customRole:preview.missing_fields.bodyType')}
                value={data.bodyType || t('customRole:preview.not_set')}
              />

              {/* 女性特有属性 */}
              {gender === 'female' && (
                <>
                  <InfoItem
                    icon={<span className="text-lg">🧿</span>}
                    label={t('customRole:preview.missing_fields.breastSize')}
                    value={data.breastSize || t('customRole:preview.not_set')}
                  />
                  <InfoItem
                    icon={<span className="text-lg">🍑</span>}
                    label={t('customRole:preview.missing_fields.buttSize')}
                    value={data.buttSize || t('customRole:preview.not_set')}
                  />
                </>
              )}

              <InfoItem
                icon={
                  <Icon
                    icon="solar:crown-bold"
                    className={`size-5 ${getColorForField('personality', data.personality || '')}`}
                  />
                }
                label={t('customRole:preview.missing_fields.personality')}
                value={getLabel('personality', data.personality || '')}
              />
              <InfoItem
                icon={<Icon icon="solar:t-shirt-bold" className="size-5" />}
                label={t('customRole:preview.missing_fields.clothing')}
                value={data.clothing || t('customRole:preview.not_set')}
              />
              <InfoItem
                icon={<Icon icon="solar:volume-loud-bold" className="size-5" />}
                label={t('customRole:preview.missing_fields.voiceModelId')}
                value={getLabel('voice', data.voice || '')}
              />
            </div>
          </CardBody>
        </Card>
      </Section>
    </>
  )
}

// 信息项组件
const InfoItem = ({
  icon,
  label,
  value
}: {
  icon: React.ReactNode
  label: string
  value: string
}) => {
  const { t } = useTranslation(['customRole'])

  return (
    <div className="flex justify-between py-3 px-4 hover:bg-default-100/50 transition-colors">
      <span className="text-default-600 flex items-center gap-2">
        <span className="text-default-500 size-5 shrink-0">{icon}</span>
        {label}
      </span>
      <span
        className={`font-medium ${
          value === t('customRole:preview.not_set') ? 'text-default-400 italic' : 'text-foreground'
        }`}
      >
        {value}
      </span>
    </div>
  )
}

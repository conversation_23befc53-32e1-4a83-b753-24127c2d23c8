import type { Env } from '@/types/env'
import { getUserPoints, updateUserPoints } from '@/lib/db/queries/membership'
import { getFeaturePointsCost } from './config'
import { createPointsCycleManager } from './points-cycle'

/**
 * 积分消费结果
 */
export interface PointsConsumptionResult {
  success: boolean
  remainingPoints?: number
  pointsConsumed?: number
  error?: string
  transactionId?: string
  cycleInfo?: {
    daysRemaining: number
    cycleEndDate: Date | null
    wasReset: boolean
  }
}

/**
 * 积分操作类型
 */
export type PointsSource = 'subscription' | 'purchase' | 'generation' | 'refund' | 'bonus' | 'admin'
export type PointsTransactionType = 'earn' | 'spend'

/**
 * 默认积分消费配置（作为后备配置）
 * 实际使用时优先从数据库获取
 */
export const POINTS_CONSUMPTION_CONFIG = {
  // 各功能积分消费
  IMAGE_GENERATION: 10, // 图片生成：10积分/张
  VOICE_GENERATION: 5, // 语音生成：5积分/次
  SCRIPT_PURCHASE: 50, // 剧本购买：50积分/个
  GALLERY_GENERATION: 15, // 写真集生成：15积分/次
  VIDEO_GENERATION: 20, // 视频生成：20积分/次（暂无，后续加）

  // 最小余额要求（保留积分）
  MINIMUM_BALANCE: 0,

  // 单次最大消费限制
  MAX_SINGLE_CONSUMPTION: 1000
} as const

/**
 * 积分服务类
 */
export class PointsService {
  constructor(private env: Env) {}

  /**
   * 检查积分余额（集成周期检查）
   */
  async checkBalance(userId: string): Promise<number> {
    // 先检查并处理积分周期
    const cycleManager = createPointsCycleManager(this.env)
    await cycleManager.checkAndHandleExpiredPoints(userId)

    const pointsInfo = await getUserPoints(this.env, userId)
    return pointsInfo.availablePoints
  }

  /**
   * 检查是否有足够积分
   */
  async hasEnoughPoints(userId: string, requiredPoints: number): Promise<boolean> {
    const balance = await this.checkBalance(userId)
    return balance >= requiredPoints
  }

  /**
   * 消费积分（带事务保护）
   */
  async consumePoints(
    userId: string,
    points: number,
    source: PointsSource,
    sourceId?: string,
    description?: string
  ): Promise<PointsConsumptionResult> {
    try {
      // 参数验证
      if (points <= 0) {
        return {
          success: false,
          error: '消费积分数量必须大于0'
        }
      }

      if (points > POINTS_CONSUMPTION_CONFIG.MAX_SINGLE_CONSUMPTION) {
        return {
          success: false,
          error: `单次消费不能超过${POINTS_CONSUMPTION_CONFIG.MAX_SINGLE_CONSUMPTION}积分`
        }
      }

      // 检查并处理积分周期
      const cycleManager = createPointsCycleManager(this.env)
      const cycleCheck = await cycleManager.checkAndHandleExpiredPoints(userId)

      // 获取周期信息
      const cycleInfo = await cycleManager.getUserPointsCycle(userId)

      // 检查余额
      const currentBalance = await this.checkBalance(userId)
      if (currentBalance < points) {
        return {
          success: false,
          error: `积分不足，需要${points}积分，当前可用${currentBalance}积分`,
          remainingPoints: currentBalance,
          cycleInfo: {
            daysRemaining: cycleInfo.daysRemaining,
            cycleEndDate: cycleInfo.cycleEndDate,
            wasReset: cycleCheck.wasExpired
          }
        }
      }

      // 执行扣除
      const updatedPoints = await updateUserPoints(
        this.env,
        userId,
        points,
        'spend',
        source,
        sourceId,
        description || `消费${points}积分`
      )

      try {
        const { updateUserPointsCache } = await import('@/lib/cache/cache-utils')

        // 更新积分缓存（直接更新为正确的剩余积分）
        await updateUserPointsCache(this.env, userId, updatedPoints.availablePoints, points)
      } catch (cacheError) {
        console.error('积分消费后更新缓存失败:', cacheError)
      }

      return {
        success: true,
        remainingPoints: updatedPoints.availablePoints,
        pointsConsumed: points,
        transactionId: crypto.randomUUID(),
        cycleInfo: {
          daysRemaining: cycleInfo.daysRemaining,
          cycleEndDate: cycleInfo.cycleEndDate,
          wasReset: cycleCheck.wasExpired
        }
      }
    } catch (error) {
      console.error('积分消费失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '积分消费失败'
      }
    }
  }

  /**
   * 添加积分
   */
  async addPoints(
    userId: string,
    points: number,
    source: PointsSource,
    sourceId?: string,
    description?: string
  ): Promise<PointsConsumptionResult> {
    try {
      if (points <= 0) {
        return {
          success: false,
          error: '添加积分数量必须大于0'
        }
      }

      const updatedPoints = await updateUserPoints(
        this.env,
        userId,
        points,
        'earn',
        source,
        sourceId,
        description || `获得${points}积分`
      )

      return {
        success: true,
        remainingPoints: updatedPoints.availablePoints,
        transactionId: crypto.randomUUID()
      }
    } catch (error) {
      console.error('积分添加失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '积分添加失败'
      }
    }
  }

  /**
   * 预扣积分（用于确保积分足够但暂不扣除）
   */
  async reservePoints(userId: string, points: number): Promise<boolean> {
    const balance = await this.checkBalance(userId)
    return balance >= points
  }

  /**
   * 退还积分
   */
  async refundPoints(
    userId: string,
    points: number,
    originalSourceId?: string,
    reason?: string
  ): Promise<PointsConsumptionResult> {
    return this.addPoints(userId, points, 'refund', originalSourceId, reason || `退还${points}积分`)
  }

  /**
   * 获取积分消费历史
   */
  async getPointsHistory(userId: string, limit = 50) {
    const { getUserPointsTransactions } = await import('../db/queries/membership')
    return getUserPointsTransactions(this.env, userId, limit)
  }
}

/**
 * 积分消费助手函数
 */
export async function consumePointsForFeature(
  env: Env,
  userId: string,
  feature: keyof typeof POINTS_CONSUMPTION_CONFIG,
  sourceId?: string,
  customAmount?: number
): Promise<PointsConsumptionResult> {
  const pointsService = new PointsService(env)

  // 获取功能对应的积分消费量
  const points = customAmount || POINTS_CONSUMPTION_CONFIG[feature]

  if (typeof points !== 'number') {
    return {
      success: false,
      error: '未知的功能类型'
    }
  }

  return pointsService.consumePoints(
    userId,
    points,
    'generation',
    sourceId,
    `使用${feature}功能消费${points}积分`
  )
}

/**
 * 检查功能使用权限（积分 + 会员状态）
 */
export async function checkFeatureAccess(
  env: Env,
  userId: string,
  feature: string,
  isMember: boolean
): Promise<{ canAccess: boolean; reason?: string; pointsRequired?: number }> {
  // 从数据库获取积分配置
  const pointsRequired = await getFeaturePointsCost(env, feature)

  if (pointsRequired <= 0) {
    return {
      canAccess: false,
      reason: '未知的功能类型或功能已关闭'
    }
  }

  // 某些功能需要会员权限
  const memberOnlyFeatures = [
    'SCRIPT_PURCHASE',
    'GALLERY_GENERATION',
    'VIDEO_GENERATION',
    'VOICE_GENERATION'
  ]
  if (memberOnlyFeatures.includes(feature.toUpperCase()) && !isMember) {
    return {
      canAccess: false,
      reason: '该功能仅限会员使用',
      pointsRequired
    }
  }

  // 检查积分余额
  const pointsService = new PointsService(env)
  const hasEnough = await pointsService.hasEnoughPoints(userId, pointsRequired)

  if (!hasEnough) {
    const currentBalance = await pointsService.checkBalance(userId)
    return {
      canAccess: false,
      reason: `积分不足，需要${pointsRequired}积分，当前可用${currentBalance}积分`,
      pointsRequired
    }
  }

  return {
    canAccess: true,
    pointsRequired
  }
}

/**
 * 简化的积分扣除（用于性能优化，跳过复杂的会员检查和周期处理）
 */
export async function consumePointsDirectly(
  env: Env,
  userId: string,
  options: {
    amount: number
    source: PointsSource
    sourceId?: string
    description: string
  }
): Promise<PointsConsumptionResult> {
  try {
    const { getUserPoints, updateUserPoints } = await import('../db/queries/membership')

    // 参数验证
    if (options.amount <= 0) {
      return {
        success: false,
        error: '消费积分数量必须大于0'
      }
    }

    // 简单的余额检查
    const pointsInfo = await getUserPoints(env, userId)
    if (pointsInfo.availablePoints < options.amount) {
      return {
        success: false,
        error: `积分不足，需要${options.amount}积分，当前可用${pointsInfo.availablePoints}积分`,
        remainingPoints: pointsInfo.availablePoints
      }
    }

    // 直接扣除积分
    const updatedPoints = await updateUserPoints(
      env,
      userId,
      options.amount,
      'spend',
      options.source,
      options.sourceId,
      options.description
    )

    // 🔥 直接积分扣除后也要更新缓存
    try {
      const { updateUserPointsCache } = await import('@/lib/cache/cache-utils')
      await updateUserPointsCache(env, userId, updatedPoints.availablePoints, options.amount)
      console.log(
        `💾 直接积分扣除后已更新缓存: userId=${userId}, 消费=${options.amount}, 剩余=${updatedPoints.availablePoints}`
      )
    } catch (cacheError) {
      console.error('直接积分扣除后更新缓存失败:', cacheError)
    }

    return {
      success: true,
      remainingPoints: updatedPoints.availablePoints,
      pointsConsumed: options.amount,
      transactionId: crypto.randomUUID()
    }
  } catch (error) {
    console.error('积分扣除失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '积分扣除失败'
    }
  }
}

/**
 * 创建积分服务实例
 */
export function createPointsService(env: Env): PointsService {
  return new PointsService(env)
}

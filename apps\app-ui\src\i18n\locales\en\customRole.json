{"page_title": "Custom Role", "page_subtitle": "Create your exclusive character", "steps": {"basic_info": {"title": "Basic Info", "description": "Character name and relationship"}, "gender_age": {"title": "Gender & Age", "description": "Select gender and age"}, "ethnicity": {"title": "Ethnicity", "description": "Ethnicity and eye color"}, "hair_style": {"title": "Hair Style", "description": "Hair style and color"}, "body_type": {"title": "Body Type", "description": "Body type and other features"}, "personality": {"title": "Personality", "description": "Select character personality"}, "clothing_voice": {"title": "Clothing & Voice", "description": "Select clothing and voice"}, "preview": {"title": "Preview", "description": "Confirm your character"}}, "navigation": {"prev_step": "Previous", "next_step": "Next", "complete_selection": "Please complete selection"}, "validation": {"fill_name_relationship": "Please fill in character name and relationship", "select_gender_age": "Please select gender and age", "select_ethnicity_eye": "Please select ethnicity and eye color", "select_hair_style_color": "Please select hair style and color", "select_body_type": "Please select body type", "select_personality": "Please select personality", "select_clothing_voice": "Please select clothing and voice", "complete_current_step": "Please complete current step before continuing"}, "basic_info": {"character_name": "Character Name", "character_name_placeholder": "Enter character name", "select_relationship": "Select Relationship", "custom_relationship": "Custom Relationship", "custom_relationship_placeholder": "Enter custom relationship", "select_relationship_aria": "Select character relationship", "select_specific_relationship_aria": "Select {{label}} relationship"}, "gender_age": {"select_gender": "Select Character Gender", "select_gender_aria": "Select character gender", "select_specific_gender_aria": "Select {{label}}", "female": "Female", "male": "Male", "select_age": "Select Character Age", "select_age_aria": "Select character age", "select_specific_age_aria": "Select {{label}} age group"}, "ethnicity": {"select_ethnicity": "Select Ethnicity", "select_ethnicity_aria": "Select character ethnicity", "select_specific_ethnicity_aria": "Select {{label}} ethnicity", "select_eye_color": "Select Eye Color", "select_eye_color_aria": "Select eye color", "select_specific_eye_color_aria": "Select {{label}} eye color"}, "preview": {"confirm_character_info": "Confirm Character Information", "unnamed_character": "Unnamed Character", "completion_rate": "Completion Rate", "checking_permission": "Checking permission...", "permission_required": "Permission Required", "generating": "Generating...", "generate_character_image": "Generate Character Image", "missing_info_warning": "The following information is incomplete:", "character_details": "Character Details", "male_character": "Male Character", "female_character": "Female Character", "not_set": "Not Set", "missing_fields": {"name": "Character Name", "relationship": "Relationship", "ethnicity": "Ethnicity", "age": "Age", "eyeColor": "Eye Color", "hairStyle": "Hair Style", "hairColor": "Hair Color", "bodyType": "Body Type", "personality": "Personality", "clothing": "Clothing", "voiceModelId": "Voice", "breastSize": "<PERSON><PERSON><PERSON>", "buttSize": "<PERSON><PERSON> Size"}, "toast": {"complete_all_required_fields": "Please complete all required fields before generating the character image", "character_image_generated": "Character image generated successfully!"}, "error": {"api_error": "API returned failure status", "image_generation_failed": "Image generation failed", "insufficient_character_creation_permission": "Insufficient permission for character creation", "failed_to_save_character_data": "Failed to save character data", "failed_to_save_to_database": "Failed to save to database", "character_image_api_response_format_error": "Character image generation API returned incorrect data format", "no_image_url_or_task_id": "No image URL or task ID received", "failed_to_generate_character_image": "Failed to generate character image", "permission_denied_character_creation_failed": "Permission denied, character creation failed", "failed_to_save_data": "Failed to save data", "polling_task_status_failed": "Failed to poll task status", "error_during_polling": "Error occurred during polling"}, "log": {"checking_character_creation_permission": "Checking character creation permission...", "permission_check_passed_saving_character_data": "Permission check passed, saving character data...", "new_character_added_to_cache": "✅ New character added to cache:", "generated_keywords": "Generated keywords:", "optimized_prompt": "Optimized prompt:", "yunwu_ai_image_generation_completed": "Yunwu AI image generation completed:", "image_generation_task_submitted": "Image generation task submitted, task ID:", "start_saving_character_data_to_database": "Start saving character data to database..."}, "create_character": "Create Character"}, "loading": {"stages": {"preparing_canvas": "Preparing canvas...", "sketching": "Sketching...", "adding_details": "Adding details...", "coloring": "Coloring...", "finalizing": "Finalizing..."}, "creating_your_exclusive_character": "Creating Your Exclusive Character", "creation_progress": "Creation Progress", "in_progress": "In Progress", "drawing_unique_character": "We are carefully drawing your unique character image...", "almost_complete_please_wait": "Almost complete, please wait a moment..."}}
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  color: rgba(0, 0, 0, 0.88);
  background-color: #ffffff;
}

#root {
  height: 100%;
}

/* Ant Design 全局样式调整 */
.ant-layout {
  background: #f5f5f5;
}

.ant-layout-header {
  height: 64px;
  line-height: 64px;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
}

.ant-menu {
  border-inline-end: none !important;
}

/* 表格样式调整 */
.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 500;
}

/* 卡片样式调整 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 2px -2px rgba(0, 0, 0, 0.16), 
    0 3px 6px 0 rgba(0, 0, 0, 0.12), 
    0 5px 12px 4px rgba(0, 0, 0, 0.09);
}

/* 按钮样式调整 */
.ant-btn {
  border-radius: 6px;
}

/* 输入框样式调整 */
.ant-input,
.ant-select-selector {
  border-radius: 6px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-layout-sider {
    position: fixed !important;
    z-index: 999;
    height: 100vh;
  }
  
  .ant-layout-content {
    margin-left: 0 !important;
  }
}

// 定义默认和可用的聊天模型

// 默认聊天模型
export const DEFAULT_CHAT_MODEL = 'chat-model';

// 可用的聊天模型列表
export const AVAILABLE_CHAT_MODELS = [
  {
    id: 'chat-model',
    name: 'Chat model',
    description: 'Primary model for all-purpose chat',
  },
  {
    id: 'chat-model-reasoning',
    name: 'Reasoning model',
    description: 'Uses advanced reasoning',
  },
];

// 获取模型名称函数
export function getModelName(modelId: string): string {
  const model = AVAILABLE_CHAT_MODELS.find((m) => m.id === modelId);
  return model?.name || '未知模型';
}

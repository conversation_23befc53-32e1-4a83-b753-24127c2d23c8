/**
 * 资源管理服务
 * 负责管理本地下载的资源文件，提供blob URL或文件路径
 */
import { scriptDB } from '@/lib/database'
import { FileSystemDownloadService } from './filesystem-download'
import { Capacitor } from '@capacitor/core'

export class ResourceManagerService {
  private static blobUrlCache = new Map<string, string>()

  /**
   * 生成文件URL的哈希ID（与下载服务保持一致）
   */
  private static async generateFileId(url: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(url)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  /**
   * 获取资源的本地URL（如果已下载）或原始URL
   */
  static async getResourceUrl(originalUrl: string): Promise<string> {
    try {
      // 检查缓存
      if (this.blobUrlCache.has(originalUrl)) {
        return this.blobUrlCache.get(originalUrl)!
      }

      // 在Capacitor环境中优先使用文件系统
      if (Capacitor.isNativePlatform()) {
        const fileSystemFile = await FileSystemDownloadService.getResourceFile(originalUrl)
        if (fileSystemFile && fileSystemFile.webPath) {
          // 缓存文件路径
          this.blobUrlCache.set(originalUrl, fileSystemFile.webPath)
          console.log(`✅ 使用本地文件: ${originalUrl} -> ${fileSystemFile.webPath}`)
          return fileSystemFile.webPath
        }
      }

      // Web环境或文件系统失败时，使用blob方式
      const fileId = await this.generateFileId(originalUrl)
      const resourceFile = await scriptDB.resourceFiles.get(fileId)

      if (resourceFile && resourceFile.blob) {
        // 创建blob URL
        const blobUrl = URL.createObjectURL(resourceFile.blob)

        // 缓存blob URL
        this.blobUrlCache.set(originalUrl, blobUrl)

        // 更新最后访问时间
        await scriptDB.resourceFiles.update(fileId, {
          lastAccessedAt: new Date()
        })

        console.log(`✅ 使用本地资源: ${originalUrl} -> ${blobUrl}`)
        return blobUrl
      } else {
        console.log(`⚠️ 本地资源不存在，使用原始URL: ${originalUrl}`)
        return originalUrl
      }
    } catch (error) {
      console.error(`❌ 获取资源失败: ${originalUrl}`, error)
      return originalUrl
    }
  }

  /**
   * 批量处理资源URL
   */
  static async processResourceUrls(urls: string[]): Promise<Map<string, string>> {
    const urlMap = new Map<string, string>()

    const promises = urls.map(async url => {
      const processedUrl = await this.getResourceUrl(url)
      urlMap.set(url, processedUrl)
    })

    await Promise.allSettled(promises)
    return urlMap
  }

  /**
   * 递归处理对象中的所有资源URL
   */
  static async processResourcesInObject(obj: any, urlMap: Map<string, string>): Promise<any> {
    if (!obj || typeof obj !== 'object') {
      return obj
    }

    if (Array.isArray(obj)) {
      return Promise.all(obj.map(item => this.processResourcesInObject(item, urlMap)))
    }

    const processed: any = {}

    for (const [key, value] of Object.entries(obj)) {
      if (typeof value === 'string' && this.isResourceUrl(value)) {
        // 如果是资源URL，使用处理后的URL
        processed[key] = urlMap.get(value) || value
      } else if (typeof value === 'object') {
        // 递归处理嵌套对象
        processed[key] = await this.processResourcesInObject(value, urlMap)
      } else {
        processed[key] = value
      }
    }

    return processed
  }

  /**
   * 判断是否是资源URL
   */
  private static isResourceUrl(url: string): boolean {
    if (!url || typeof url !== 'string') return false

    // 检查是否是HTTP/HTTPS URL
    if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('/')) {
      return false
    }

    // 检查文件扩展名
    const ext = url.toLowerCase().split('.').pop()
    const resourceExtensions = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'webp',
      'svg',
      'bmp', // 图片
      'mp4',
      'webm',
      'ogg',
      'avi',
      'mov',
      'wmv',
      'flv',
      'mkv', // 视频
      'mp3',
      'wav',
      'ogg',
      'aac',
      'm4a' // 音频
    ]

    return ext ? resourceExtensions.includes(ext) : false
  }

  /**
   * 提取对象中的所有资源URL
   */
  static extractResourceUrls(obj: any, audioUrl?: string): string[] {
    const urls = new Set<string>()

    // 添加音频URL
    if (audioUrl && this.isResourceUrl(audioUrl)) {
      urls.add(audioUrl)
    }

    // 递归提取所有资源URL
    const extractFromObject = (target: any) => {
      if (!target || typeof target !== 'object') return

      if (Array.isArray(target)) {
        target.forEach(extractFromObject)
      } else {
        Object.values(target).forEach(value => {
          if (typeof value === 'string' && this.isResourceUrl(value)) {
            urls.add(value)
          } else if (typeof value === 'object') {
            extractFromObject(value)
          }
        })
      }
    }

    extractFromObject(obj)
    return Array.from(urls)
  }

  /**
   * 清理blob URL缓存
   */
  static clearBlobUrlCache(): void {
    // 释放所有blob URLs
    for (const blobUrl of this.blobUrlCache.values()) {
      if (blobUrl.startsWith('blob:')) {
        URL.revokeObjectURL(blobUrl)
      }
    }
    this.blobUrlCache.clear()
    console.log('🧹 清理blob URL缓存完成')
  }

  /**
   * 预处理剧本数据，将所有资源URL替换为本地URL
   */
  static async preprocessScriptData(
    scriptContent: any,
    audioUrl?: string
  ): Promise<{
    processedContent: any
    processedAudioUrl: string
  }> {
    console.log('🔄 开始预处理剧本资源...')

    // 提取所有资源URL
    const resourceUrls = this.extractResourceUrls(scriptContent, audioUrl)
    console.log(`📋 找到 ${resourceUrls.length} 个资源需要处理`)

    // 批量处理资源URL
    const urlMap = await this.processResourceUrls(resourceUrls)

    // 处理剧本内容
    const processedContent = await this.processResourcesInObject(scriptContent, urlMap)

    // 处理音频URL
    const processedAudioUrl =
      audioUrl && urlMap.has(audioUrl) ? urlMap.get(audioUrl)! : audioUrl || ''

    console.log('✅ 剧本资源预处理完成')
    return {
      processedContent,
      processedAudioUrl
    }
  }
}

// 创建单例实例
export const resourceManager = ResourceManagerService

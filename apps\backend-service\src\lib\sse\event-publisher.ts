import type { MediaCompletionEvent } from '@/routes/sse'

/**
 * SSE事件发布工具
 * 提供统一的事件发送接口供队列消费者使用
 */
export class SSEEventPublisher {
  private sseManager: any

  constructor() {
    // 动态导入SSE管理器，避免循环依赖
    this.initSSEManager()
  }

  private async initSSEManager() {
    try {
      const { sseManager } = await import('@/routes/sse')
      this.sseManager = sseManager
    } catch (error) {
      console.error('📡 [SSE] 初始化SSE管理器失败:', error)
    }
  }

  /**
   * 发布媒体生成完成事件
   */
  async publishMediaCompletion(event: MediaCompletionEvent, userId?: string): Promise<boolean> {
    if (!this.sseManager) {
      console.warn('📡 [SSE] SSE管理器未初始化，跳过事件发送')
      return false
    }

    try {
      const sseEvent = {
        id: `media_${event.messageId}_${Date.now()}`,
        event: 'media_completed',
        data: event
      }

      if (userId) {
        // 发送给特定用户
        const success = await this.sseManager.sendToUser(userId, sseEvent)
        console.log(`📡 [SSE] 媒体完成事件已发送给用户 ${userId}: ${success}`)
        return success
      } else {
        // 广播到所有连接（一般不推荐，除非是全局事件）
        const sentCount = await this.sseManager.broadcast(sseEvent)
        console.log(`📡 [SSE] 媒体完成事件已广播: ${sentCount} 个连接`)
        return sentCount > 0
      }
    } catch (error) {
      console.error('📡 [SSE] 发送媒体完成事件失败:', error)
      return false
    }
  }

  /**
   * 发布媒体生成进度事件
   */
  async publishMediaProgress(
    messageId: string,
    chatId: string,
    mediaType: 'audio' | 'image' | 'video',
    progress: {
      status: 'starting' | 'processing' | 'completed' | 'failed'
      progress: number
      message: string
      errorMessage?: string
    },
    userId?: string
  ): Promise<boolean> {
    if (!this.sseManager) {
      console.warn('📡 [SSE] SSE管理器未初始化，跳过进度事件发送')
      return false
    }

    try {
      const sseEvent = {
        id: `progress_${messageId}_${Date.now()}`,
        event: 'media_progress',
        data: {
          type: 'media_progress',
          messageId,
          chatId,
          mediaType,
          ...progress,
          timestamp: new Date().toISOString()
        }
      }

      if (userId) {
        const success = await this.sseManager.sendToUser(userId, sseEvent)
        console.log(`📡 [SSE] 媒体进度事件已发送给用户 ${userId}: ${success}`)
        return success
      } else {
        const sentCount = await this.sseManager.broadcast(sseEvent)
        console.log(`📡 [SSE] 媒体进度事件已广播: ${sentCount} 个连接`)
        return sentCount > 0
      }
    } catch (error) {
      console.error('📡 [SSE] 发送媒体进度事件失败:', error)
      return false
    }
  }

  /**
   * 发布通用自定义事件
   */
  async publishCustomEvent(eventType: string, data: any, userId?: string): Promise<boolean> {
    if (!this.sseManager) {
      console.warn('📡 [SSE] SSE管理器未初始化，跳过自定义事件发送')
      return false
    }

    try {
      const sseEvent = {
        id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`,
        event: eventType,
        data: {
          ...data,
          timestamp: new Date().toISOString()
        }
      }

      if (userId) {
        const success = await this.sseManager.sendToUser(userId, sseEvent)
        console.log(`📡 [SSE] 自定义事件 ${eventType} 已发送给用户 ${userId}: ${success}`)
        return success
      } else {
        const sentCount = await this.sseManager.broadcast(sseEvent)
        console.log(`📡 [SSE] 自定义事件 ${eventType} 已广播: ${sentCount} 个连接`)
        return sentCount > 0
      }
    } catch (error) {
      console.error(`📡 [SSE] 发送自定义事件 ${eventType} 失败:`, error)
      return false
    }
  }
}

// 导出全局实例
export const sseEventPublisher = new SSEEventPublisher()

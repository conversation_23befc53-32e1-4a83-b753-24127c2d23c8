import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import {
  getUserBySupabaseId,
  getCharactersByUserId,
  getCharacterById,
  createCharacter,
  updateCharacterById,
  deleteCharacterById,
  getPublicCharacters,
  getSystemCharacters
} from '@/lib/db/queries'
import {
  checkUserCanCreateCharacter,
  getUserCharacterStats
} from '@/lib/db/queries/membership-limits'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { Env } from '@/types/env'

const characters = new Hono<{ Bindings: Env }>()

// ==================== 验证模式 ====================

// 角色创建验证模式 - 使用通用错误消息，在路由中处理国际化
const createCharacterSchema = z.object({
  name: z.string().min(1).max(50),
  description: z.string().max(500).optional(),
  relationship: z.string().max(50).optional(),
  ethnicity: z.string().max(30).optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
  age: z.string().max(20).optional(),
  eyeColor: z.string().max(30).optional(),
  hairStyle: z.string().max(50).optional(),
  hairColor: z.string().max(30).optional(),
  bodyType: z.string().max(50).optional(),
  breastSize: z.string().max(20).optional(),
  buttSize: z.string().max(20).optional(),
  personality: z.string().max(500).optional(),
  clothing: z.string().max(200).optional(),
  voice: z.string().max(100).optional(),
  voiceModelId: z.string().uuid().optional(),
  keywords: z.string().max(200).default(''),
  prompt: z.string().max(2000).default(''),
  imageUrl: z.string().url().optional(),
  category: z.string().max(50).optional(),
  isPublic: z.boolean().default(false)
})

// 角色更新验证模式
const updateCharacterSchema = createCharacterSchema.partial()

// ==================== 获取系统角色列表 ====================

characters.get('/system', languageMiddleware, async c => {
  try {
    const env = c.env

    // 获取查询参数
    const page = Number.parseInt(c.req.query('page') || '1')
    const limit = Number.parseInt(c.req.query('limit') || '50')
    const category = c.req.query('category') || undefined

    // 计算偏移量
    const offset = (page - 1) * limit

    // 获取系统角色列表
    const charactersData = await getSystemCharacters(env, {
      limit,
      offset,
      category
    })

    return c.json(
      {
        success: true,
        data: charactersData,
        pagination: {
          page,
          limit,
          total: charactersData.length,
          hasMore: charactersData.length === limit
        }
      },
      200
    )
  } catch (error) {
    const t = c.get('t')
    console.error('获取系统角色列表失败:', error)
    return c.json(
      {
        success: false,
        message: t('characters.get_system_failed')
      },
      500
    )
  }
})

// ==================== 获取公开角色列表 ====================

characters.get('/public', languageMiddleware, async c => {
  try {
    const env = c.env

    // 获取查询参数
    const page = Number.parseInt(c.req.query('page') || '1')
    const limit = Number.parseInt(c.req.query('limit') || '20')
    const category = c.req.query('category') || undefined

    // 计算偏移量
    const offset = (page - 1) * limit

    // 获取公开角色列表
    const charactersData = await getPublicCharacters(env, {
      limit,
      offset,
      category
    })

    return c.json(
      {
        success: true,
        data: charactersData,
        pagination: {
          page,
          limit,
          total: charactersData.length,
          hasMore: charactersData.length === limit
        }
      },
      200
    )
  } catch (error) {
    const t = c.get('t')
    console.error('获取公开角色列表失败:', error)
    return c.json(
      {
        success: false,
        message: t('characters.get_public_failed')
      },
      500
    )
  }
})

// ==================== 获取角色统计信息 ====================

/**
 * GET /api/characters/stats
 * 获取用户角色统计信息
 */
characters.get('/stats', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_exist')
        },
        404
      )
    }

    // 获取角色统计信息
    const stats = await getUserCharacterStats(env, dbUser.id)

    return c.json({
      success: true,
      data: stats,
      message: t('characters.get_stats_success')
    })
  } catch (error) {
    const t = c.get('t')
    console.error('获取角色统计失败:', error)
    return c.json(
      {
        success: false,
        message: t('characters.get_stats_failed')
      },
      500
    )
  }
})

// ==================== 获取用户角色列表 ====================

characters.get('/', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_exist')
        },
        404
      )
    }

    // 获取用户创建的角色列表
    const charactersData = await getCharactersByUserId(env, dbUser.id)

    return c.json(
      {
        success: true,
        data: charactersData,
        pagination: {
          total: charactersData.length
        }
      },
      200
    )
  } catch (error) {
    const t = c.get('t')
    console.error('获取用户角色列表失败:', error)
    return c.json(
      {
        success: false,
        message: t('characters.get_user_characters_failed')
      },
      500
    )
  }
})

// ==================== 创建新角色 ====================

characters.post(
  '/',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createCharacterSchema),
  async c => {
    try {
      const user = c.get('user')
      const data = c.req.valid('json')
      const env = c.env
      const t = c.get('t')

      if (!user) {
        return c.json(
          {
            success: false,
            message: t('characters.user_not_found')
          },
          401
        )
      }

      // 获取数据库用户信息
      const dbUser = await getUserBySupabaseId(env, user.id)
      if (!dbUser) {
        return c.json(
          {
            success: false,
            message: t('characters.user_not_exist')
          },
          404
        )
      }

      // 检查用户角色创建限制
      const canCreateResult = await checkUserCanCreateCharacter(env, dbUser.id)
      if (!canCreateResult.canCreate) {
        return c.json(
          {
            success: false,
            message: canCreateResult.reason || t('characters.cannot_create_more'),
            data: {
              currentCount: canCreateResult.currentCount,
              maxAllowed: canCreateResult.maxAllowed
            }
          },
          403
        )
      }

      // 创建新角色
      const [newCharacter] = await createCharacter(env, {
        userId: dbUser.id,
        name: data.name,
        description: data.description,
        relationship: data.relationship,
        ethnicity: data.ethnicity,
        gender: data.gender,
        age: data.age,
        eyeColor: data.eyeColor,
        hairStyle: data.hairStyle,
        hairColor: data.hairColor,
        bodyType: data.bodyType,
        breastSize: data.breastSize,
        buttSize: data.buttSize,
        personality: data.personality,
        clothing: data.clothing,
        voice: data.voice,
        voiceModelId: data.voiceModelId,
        keywords: data.keywords,
        prompt: data.prompt,
        imageUrl: data.imageUrl,
        category: data.category,
        isPublic: data.isPublic
      })

      return c.json(
        {
          success: true,
          data: {
            id: newCharacter.id,
            name: newCharacter.name,
            description: newCharacter.description,
            relationship: newCharacter.relationship,
            ethnicity: newCharacter.ethnicity,
            gender: newCharacter.gender,
            age: newCharacter.age,
            eyeColor: newCharacter.eyeColor,
            hairStyle: newCharacter.hairStyle,
            hairColor: newCharacter.hairColor,
            bodyType: newCharacter.bodyType,
            breastSize: newCharacter.breastSize,
            buttSize: newCharacter.buttSize,
            personality: newCharacter.personality,
            clothing: newCharacter.clothing,
            voice: newCharacter.voice,
            voiceModelId: newCharacter.voiceModelId,
            keywords: newCharacter.keywords,
            prompt: newCharacter.prompt,
            imageUrl: newCharacter.imageUrl,
            category: newCharacter.category,
            isPublic: newCharacter.isPublic,
            createdAt: newCharacter.createdAt,
            updatedAt: newCharacter.updatedAt
          }
        },
        201
      )
    } catch (error) {
      const t = c.get('t')
      console.error('创建角色失败:', error)
      return c.json(
        {
          success: false,
          message: t('characters.create_failed')
        },
        500
      )
    }
  }
)

// ==================== 获取角色详情 ====================

characters.get('/:id', languageMiddleware, async c => {
  try {
    const characterId = c.req.param('id')
    const env = c.env
    const t = c.get('t')

    // 获取角色信息
    const character = await getCharacterById(env, characterId)
    if (!character) {
      return c.json(
        {
          success: false,
          message: t('characters.character_not_exist')
        },
        404
      )
    }

    // 如果是私有角色，需要验证权限
    if (!character.isPublic) {
      // 尝试获取用户信息（可选认证）
      const authHeader = c.req.header('Authorization')
      if (!authHeader) {
        return c.json(
          {
            success: false,
            message: t('characters.private_character_login_required')
          },
          401
        )
      }

      // 这里可以添加权限验证逻辑
      // 暂时允许访问，后续可以根据需要添加更严格的权限控制
    }

    return c.json(
      {
        success: true,
        data: {
          id: character.id,
          name: character.name,
          description: character.description,
          relationship: character.relationship,
          ethnicity: character.ethnicity,
          gender: character.gender,
          age: character.age,
          eyeColor: character.eyeColor,
          hairStyle: character.hairStyle,
          hairColor: character.hairColor,
          bodyType: character.bodyType,
          breastSize: character.breastSize,
          buttSize: character.buttSize,
          personality: character.personality,
          clothing: character.clothing,
          voice: character.voice,
          voiceModelId: character.voiceModelId,
          keywords: character.keywords,
          prompt: character.prompt,
          imageUrl: character.imageUrl,
          category: character.category,
          isPublic: character.isPublic,
          createdAt: character.createdAt,
          updatedAt: character.updatedAt
        }
      },
      200
    )
  } catch (error) {
    const t = c.get('t')
    console.error('获取角色详情失败:', error)
    return c.json(
      {
        success: false,
        message: t('characters.get_character_details_failed')
      },
      500
    )
  }
})

// ==================== 更新角色 ====================

characters.put(
  '/:id',
  authMiddleware,
  languageMiddleware,
  zValidator('json', updateCharacterSchema),
  async c => {
    try {
      const user = c.get('user')
      const characterId = c.req.param('id')
      const data = c.req.valid('json')
      const env = c.env
      const t = c.get('t')

      if (!user) {
        return c.json(
          {
            success: false,
            message: t('characters.user_not_found')
          },
          401
        )
      }

      // 获取数据库用户信息
      const dbUser = await getUserBySupabaseId(env, user.id)
      if (!dbUser) {
        return c.json(
          {
            success: false,
            message: t('characters.user_not_exist')
          },
          404
        )
      }

      // 获取角色信息
      const character = await getCharacterById(env, characterId)
      if (!character) {
        return c.json(
          {
            success: false,
            message: t('characters.character_not_exist')
          },
          404
        )
      }

      // 检查权限（只有角色创建者可以更新）
      if (character.userId !== dbUser.id) {
        return c.json(
          {
            success: false,
            message: t('characters.no_permission_modify')
          },
          403
        )
      }

      // 更新角色
      const [updatedCharacter] = await updateCharacterById(env, characterId, data)

      return c.json(
        {
          success: true,
          data: {
            id: updatedCharacter.id,
            name: updatedCharacter.name,
            description: updatedCharacter.description,
            relationship: updatedCharacter.relationship,
            ethnicity: updatedCharacter.ethnicity,
            gender: updatedCharacter.gender,
            age: updatedCharacter.age,
            eyeColor: updatedCharacter.eyeColor,
            hairStyle: updatedCharacter.hairStyle,
            hairColor: updatedCharacter.hairColor,
            bodyType: updatedCharacter.bodyType,
            breastSize: updatedCharacter.breastSize,
            buttSize: updatedCharacter.buttSize,
            personality: updatedCharacter.personality,
            clothing: updatedCharacter.clothing,
            voice: updatedCharacter.voice,
            voiceModelId: updatedCharacter.voiceModelId,
            keywords: updatedCharacter.keywords,
            prompt: updatedCharacter.prompt,
            imageUrl: updatedCharacter.imageUrl,
            category: updatedCharacter.category,
            isPublic: updatedCharacter.isPublic,
            createdAt: updatedCharacter.createdAt,
            updatedAt: updatedCharacter.updatedAt
          }
        },
        200
      )
    } catch (error) {
      const t = c.get('t')
      console.error('更新角色失败:', error)
      return c.json(
        {
          success: false,
          message: t('characters.update_failed')
        },
        500
      )
    }
  }
)

// ==================== 删除角色 ====================

characters.delete('/:id', authMiddleware, languageMiddleware, async c => {
  try {
    const user = c.get('user')
    const characterId = c.req.param('id')
    const env = c.env
    const t = c.get('t')

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_found')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('characters.user_not_exist')
        },
        404
      )
    }

    // 获取角色信息
    const character = await getCharacterById(env, characterId)
    if (!character) {
      return c.json(
        {
          success: false,
          message: t('characters.character_not_exist')
        },
        404
      )
    }

    // 检查权限（只有角色创建者可以删除）
    if (character.userId !== dbUser.id) {
      return c.json(
        {
          success: false,
          message: t('characters.no_permission_delete')
        },
        403
      )
    }

    // TODO: 检查角色是否正在被使用（有关联的聊天）
    // 如果有关联聊天，可以选择禁止删除或者提示用户

    // 删除角色
    await deleteCharacterById(env, characterId)

    return c.json(
      {
        success: true,
        message: t('characters.delete_success')
      },
      200
    )
  } catch (error) {
    const t = c.get('t')
    console.error('删除角色失败:', error)
    return c.json(
      {
        success: false,
        message: t('characters.delete_failed')
      },
      500
    )
  }
})

export default characters

#!/bin/bash

# 快速部署脚本
# 使用方法: ./deploy.sh [dev|production]

set -e

ENV=${1:-dev}

echo "🚀 部署到 ${ENV} 环境..."

# 检查登录
if ! pnpm wrangler whoami > /dev/null 2>&1; then
    echo "❌ 请先登录: pnpm wrangler login"
    exit 1
fi

# 构建和部署
pnpm build
pnpm wrangler deploy --env $ENV

echo "✅ 部署完成！"

# 获取 URL
WORKER_URL=$(pnpm wrangler deployments list --env $ENV | grep -o 'https://[^[:space:]]*' | head -n 1)
if [ -n "$WORKER_URL" ]; then
    echo "访问地址: $WORKER_URL/health"
fi 
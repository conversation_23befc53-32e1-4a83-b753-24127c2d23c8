/**
 * 麦克风权限管理 Hook
 * 专为Capacitor应用优化，使用原生权限API
 */

import { useState, useEffect, useCallback } from 'react'
import { Capacitor } from '@capacitor/core'
import { Device } from '@capacitor/device'

export type PermissionState = 'unknown' | 'granted' | 'denied' | 'requesting' | 'prompt'

export interface AudioPermissionState {
  state: PermissionState
  isSupported: boolean
  error?: string
}

export interface AudioPermissionActions {
  requestPermission: () => Promise<boolean>
  checkPermission: () => Promise<PermissionState>
  reset: () => void
}

export function useAudioPermission(): AudioPermissionState & AudioPermissionActions {
  const [state, setState] = useState<PermissionState>('unknown')
  const [isSupported, setIsSupported] = useState(true)
  const [error, setError] = useState<string>()

  // 检测运行环境并检查API支持
  useEffect(() => {
    const checkSupport = () => {
      // 在Capacitor原生环境中，权限处理更简单
      if (Capacitor.isNativePlatform()) {
        setIsSupported(true)
        return
      }

      // Web环境检查
      const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
      const hasMediaRecorder = !!window.MediaRecorder

      const supported = hasGetUserMedia && hasMediaRecorder
      setIsSupported(supported)

      if (!supported) {
        const missingFeatures = []
        if (!hasGetUserMedia) missingFeatures.push('麦克风访问')
        if (!hasMediaRecorder) missingFeatures.push('音频录制')

        setError(`浏览器不支持: ${missingFeatures.join(', ')}`)
        setState('denied')
      }
    }

    checkSupport()
  }, [])

  // 检查当前权限状态
  const checkPermission = useCallback(async (): Promise<PermissionState> => {
    if (!isSupported) {
      return 'denied'
    }

    try {
      // 在原生环境中，通过尝试获取媒体流来检查权限状态
      if (Capacitor.isNativePlatform()) {
        try {
          // 尝试获取媒体流来检测权限状态
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              echoCancellation: true,
              noiseSuppression: true,
              autoGainControl: true,
              sampleRate: 16000,
              channelCount: 1
            }
          })

          // 如果成功获取，说明权限已授予
          stream.getTracks().forEach(track => track.stop())
          setState('granted')
          return 'granted'
        } catch (error) {
          // 根据错误类型判断权限状态
          if (error instanceof Error) {
            if (error.name === 'NotAllowedError') {
              setState('denied')
              return 'denied'
            }
          }
          // 其他错误或未知情况
          setState('unknown')
          return 'unknown'
        }
      }

      // Web环境的权限检查
      if (navigator.permissions) {
        const result = await navigator.permissions.query({ name: 'microphone' as PermissionName })
        const permissionState = result.state as PermissionState
        setState(permissionState === 'prompt' ? 'unknown' : permissionState)
        return permissionState === 'prompt' ? 'unknown' : permissionState
      }

      // 回退方案：检查设备列表
      const devices = await navigator.mediaDevices.enumerateDevices()
      const hasAudioInput = devices.some(device => device.kind === 'audioinput')

      if (!hasAudioInput) {
        setState('denied')
        setError('未检测到音频输入设备')
        return 'denied'
      }

      setState('unknown')
      return 'unknown'
    } catch (error) {
      console.error('检查权限失败:', error)
      setState('denied')
      setError(error instanceof Error ? error.message : '权限检查失败')
      return 'denied'
    }
  }, [isSupported])

  // 请求麦克风权限
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!isSupported) {
      setError(Capacitor.isNativePlatform() ? '设备不支持音频录制' : '浏览器不支持音频录制功能')
      return false
    }

    setState('requesting')
    setError(undefined)

    try {
      // 统一使用Web API，Capacitor会自动处理原生权限
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        console.log('开始请求麦克风权限...')

        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            // 为移动设备优化音频设置
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 16000, // 适合语音识别的采样率
            channelCount: 1 // 单声道
          }
        })

        console.log('麦克风权限获取成功')

        // 立即关闭流，我们只是要获取权限
        stream.getTracks().forEach(track => track.stop())

        setState('granted')
        return true
      } else {
        setError('此设备不支持音频录制')
        setState('denied')
        return false
      }
    } catch (error) {
      console.error('权限请求失败:', error)

      let errorMessage = '权限请求失败'

      if (error instanceof Error) {
        switch (error.name) {
          case 'NotAllowedError':
            errorMessage = Capacitor.isNativePlatform()
              ? '请在设置中允许应用访问麦克风'
              : '用户拒绝了麦克风权限'
            setState('denied')
            break
          case 'NotFoundError':
            errorMessage = '未找到音频输入设备'
            setState('denied')
            break
          case 'NotSupportedError':
            errorMessage = Capacitor.isNativePlatform()
              ? '设备不支持音频录制'
              : '浏览器不支持音频录制'
            setState('denied')
            setIsSupported(false)
            break
          case 'NotReadableError':
            errorMessage = '音频设备被其他应用占用'
            setState('denied')
            break
          case 'OverconstrainedError':
            errorMessage = '音频设备不满足要求'
            setState('denied')
            break
          case 'SecurityError':
            errorMessage = Capacitor.isNativePlatform()
              ? '权限被系统拒绝'
              : '安全限制，请使用HTTPS访问'
            setState('denied')
            break
          default:
            errorMessage = error.message || '未知错误'
            setState('denied')
        }
      } else {
        setState('denied')
      }

      setError(errorMessage)
      return false
    }
  }, [isSupported])

  // 重置状态
  const reset = useCallback(() => {
    setState('unknown')
    setError(undefined)
  }, [])

  // 组件挂载时检查权限
  useEffect(() => {
    if (isSupported) {
      checkPermission()
    }
  }, [isSupported, checkPermission])

  // 监听权限变化（仅在Web环境中）
  useEffect(() => {
    // 原生环境不需要权限监听器
    if (Capacitor.isNativePlatform() || !navigator.permissions || !isSupported) {
      return
    }

    let permissionStatus: PermissionStatus | null = null

    const setupPermissionListener = async () => {
      try {
        permissionStatus = await navigator.permissions.query({
          name: 'microphone' as PermissionName
        })

        const handlePermissionChange = () => {
          const newState = permissionStatus!.state as PermissionState
          setState(newState === 'prompt' ? 'unknown' : newState)

          if (newState === 'denied') {
            setError('麦克风权限被拒绝')
          } else if (newState === 'granted') {
            setError(undefined)
          }
        }

        permissionStatus.addEventListener('change', handlePermissionChange)

        // 初始状态
        handlePermissionChange()
      } catch (error) {
        console.warn('设置权限监听器失败:', error)
      }
    }

    setupPermissionListener()

    return () => {
      if (permissionStatus) {
        permissionStatus.removeEventListener('change', () => {})
      }
    }
  }, [isSupported])

  return {
    state,
    isSupported,
    error,
    requestPermission,
    checkPermission,
    reset
  }
}

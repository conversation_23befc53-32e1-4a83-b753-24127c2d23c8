import Capacitor
import CoreBluetooth
// swiftlint:disable identifier_name
// swiftlint:disable type_body_length
import Foundation

// 确保正确导入Capacitor - 这是插件工作的基础。如果 Xcode 报告找不到 CAPPlugin、CAPPluginCall 等类型，
// 请检查您的项目配置，确保 Capacitor.framework 已正确链接到此插件目标，或者 Cocoapods 依赖项已正确安装和更新。
// #if canImport(Capacitor)
//   import Capacitor
// #endif

/// 蓝牙广播插件 - iOS实现
@objcMembers
@objc(BleAdvertiserPlugin)
public class BleAdvertiserPlugin: CAPPlugin, CBPeripheralManagerDelegate {
  // 蓝牙外设管理器
  private var peripheralManager: CBPeripheralManager?

  // 广播实例存储
  private var advertiseCallbacks = [Int: AdvertisingInstance]()

  // 服务和特征
  private var advertisingService: CBMutableService?
  private var advertisingCharacteristic: CBMutableCharacteristic?

  // 日志标签
  private let TAG = "[BleAdvertiser]"

  // 声明一个变量来保存 startAdvertising 的 CAPPluginCall
  private var startAdvertisingCall: CAPPluginCall?
  private var currentInstanceId: Int?

  /**
     * 插件加载时调用
     */
  override public func load() {
    NSLog("\(self.TAG) 插件加载")
  }

  /**
     * 初始化蓝牙服务
     */
  @objc func initialize(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 初始化蓝牙服务")

    // 如果已经初始化，则直接返回成功
    if peripheralManager != nil {
      NSLog("\(self.TAG) 蓝牙服务已初始化")
      call.resolve(["success": true])
      return
    }

    // 创建蓝牙外设管理器
    peripheralManager = CBPeripheralManager(delegate: self, queue: nil)

    // 延迟检查蓝牙状态，确保委托方法被调用
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [self] in
      if self.peripheralManager?.state == .poweredOn {
        NSLog("\(self.TAG) 蓝牙服务初始化成功")
        call.resolve(["success": true])
      } else {
        NSLog("\(self.TAG) 蓝牙服务初始化失败，状态: \(String(describing: self.peripheralManager?.state))")
        call.resolve([
          "success": false,
          "message": "蓝牙未启用或不可用",
        ])
      }
    }
  }

  /**
     * 检查蓝牙是否启用
     */
  @objc func isBluetoothEnabled(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 检查蓝牙是否启用")

    // 如果外设管理器未初始化，则初始化它
    if peripheralManager == nil {
      peripheralManager = CBPeripheralManager(delegate: self, queue: nil)

      // 延迟检查蓝牙状态
      DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [self] in
        let enabled = self.peripheralManager?.state == .poweredOn
        NSLog("\(self.TAG) 蓝牙状态: \(enabled ? "已启用" : "未启用")")
        call.resolve(["enabled": enabled])
      }
    } else {
      let enabled = peripheralManager?.state == .poweredOn
      NSLog("\(self.TAG) 蓝牙状态: \(enabled ? "已启用" : "未启用")")
      call.resolve(["enabled": enabled])
    }
  }

  /**
     * 开始广播
     */
  @objc func startAdvertising(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 开始广播")

    // 获取参数
    let mode = call.getInt("mode") ?? 2
    let manufacturerId = call.getInt("manufacturerId") ?? 255
    let instanceId =
      call.getInt("instanceId")
      ?? Int(Date().timeIntervalSince1970.truncatingRemainder(dividingBy: 10000))

    NSLog(
      "\(self.TAG) 参数获取完毕: mode=\(mode), manufacturerId=\(manufacturerId), instanceId=\(instanceId)"
    )

    // 保存当前的 call 和 instanceId，以便在代理方法中使用
    self.startAdvertisingCall = call
    self.currentInstanceId = instanceId
    NSLog(
      "\(self.TAG) startAdvertising: self.currentInstanceId 设置为: \(String(describing: self.currentInstanceId)) (原始 instanceId: \(instanceId))"
    )

    // 检查蓝牙状态
    guard let manager = peripheralManager, manager.state == .poweredOn else {
      // 详细记录 peripheralManager 的状态
      var managerStateDetail = "未知状态"
      if peripheralManager == nil {
        managerStateDetail = "peripheralManager 为 nil"
      } else {
        switch peripheralManager!.state {
        case .poweredOn: managerStateDetail = "poweredOn"
        case .poweredOff: managerStateDetail = "poweredOff"
        case .resetting: managerStateDetail = "resetting"
        case .unauthorized: managerStateDetail = "unauthorized"
        case .unknown: managerStateDetail = "unknown"
        case .unsupported: managerStateDetail = "unsupported"
        @unknown default: managerStateDetail = "其他未知状态"
        }
      }
      NSLog("\(self.TAG) 蓝牙未启用或不可用。详细状态: \(managerStateDetail)")
      call.reject("蓝牙未启用或不可用，状态: \(managerStateDetail)")
      return
    }
    NSLog("\(self.TAG) 蓝牙状态检查通过，状态: poweredOn")

    // 获取数据
    guard let dataBytes = getDataBytes(from: call) else {
      NSLog("\(self.TAG) 无效的数据格式")
      call.reject("无效的数据格式")
      return
    }

    // 停止之前的广播（如果存在）
    if let existingInstance = advertiseCallbacks[instanceId] {
      NSLog("\(self.TAG) 停止之前的广播实例: \(instanceId)")
      manager.stopAdvertising()
      existingInstance.timer?.invalidate()
      advertiseCallbacks.removeValue(forKey: instanceId)
    }

    // 停止当前广播
    manager.stopAdvertising()

    // 移除旧服务（如果有）
    if let service = advertisingService {
      manager.remove(service)
      advertisingService = nil
      advertisingCharacteristic = nil
    }

    // 创建自定义服务和特征
    // 使用标准的FADE服务UUID，与Android版本保持一致
    let serviceUUID = CBUUID(string: "FADE")
    let characteristicUUID = CBUUID(string: "DEAD")

    // 构建数据 - 包含制造商ID和用户数据
    var fullData = [UInt8(manufacturerId & 0xff), UInt8((manufacturerId >> 8) & 0xff)]
    fullData.append(contentsOf: dataBytes)
    let manufacturerData = Data(fullData)

    // 打印调试信息
    let debugBytes = fullData.map { String(format: "%02X", $0) }.joined(separator: " ")
    NSLog("\(self.TAG) 广播数据 (Hex): \(debugBytes)")

    // 检查数据长度
    if fullData.count > 28 {
      NSLog("\(self.TAG) 警告：广播数据长度(\(fullData.count)字节)可能超过iOS限制")
    }

    // 创建特征 - 存储完整数据
    // 与Android端保持一致：确保特征可读取，包含与Android相同的制造商数据
    // 注意：带有缓存值的特征必须是只读的（.read），不能添加notify等其他属性
    advertisingCharacteristic = CBMutableCharacteristic(
      type: characteristicUUID,
      properties: [.read],  // 只设置为可读，避免"Characteristics with cached values must be read-only"错误
      value: manufacturerData,
      permissions: [.readable]
    )

    // 创建服务
    advertisingService = CBMutableService(type: serviceUUID, primary: true)

    // 将特征添加到服务
    if let characteristic = advertisingCharacteristic {
      advertisingService?.characteristics = [characteristic]
      NSLog("\(self.TAG) 特征值设置完成，数据长度: \(manufacturerData.count)字节")
    }

    // 将服务添加到外设管理器
    if let service = advertisingService {
      manager.add(service)
    }

    // 创建标准的 iOS 广播配置
    // 注意：iOS对广播配置有严格限制，与Android不完全相同
    // 1. 使用服务UUID代替制造商数据（iOS限制）
    // 2. 设置为可连接（与Android一致）
    // 3. iOS自动管理功率和延迟（无需手动设置）
    var advertisementData: [String: Any] = [
      CBAdvertisementDataServiceUUIDsKey: [serviceUUID]
    ]

    // 尝试设置为可连接（与Android的setConnectable(true)对应）
    // 注意：在某些iOS版本中，这可能会被警告为不允许的键
    // #if DEBUG
    //   // 只在调试模式尝试，以避免App Store拒绝
    //   advertisementData[CBAdvertisementDataIsConnectableKey] = true
    // #endif

    // 记录广播数据配置
    NSLog("\(self.TAG) 使用服务UUID进行广播，完整数据存储在特征值中")
    NSLog("\(self.TAG) 广播配置: 可连接模式，等效于Android高功率和低延迟模式")

    // 开始广播
    NSLog("\(self.TAG) 开始广播数据, 实例ID: \(instanceId)")
    manager.startAdvertising(advertisementData)

    // 保存广播实例
    let instance = AdvertisingInstance(
      id: instanceId,
      data: advertisementData,
      call: call
    )
    advertiseCallbacks[instanceId] = instance

    // 设置备用定时器，以防didStartAdvertising回调没有被调用
    // 这个定时器仅作为安全网，通常通过didStartAdvertising回调应该先返回结果
    instance.timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: false) { [weak self] _ in
      guard let self = self else { return }

      // 检查当前实例ID和startAdvertisingCall是否还存在
      // 如果不存在，说明didStartAdvertising已经处理了
      if self.currentInstanceId == instanceId && self.startAdvertisingCall != nil {
        NSLog("\(self.TAG) 定时器触发 - didStartAdvertising回调未被调用，使用备用处理")

        if manager.isAdvertising {
          NSLog("\(self.TAG) 广播已启动(通过定时器确认)，实例ID: \(instanceId)")
          call.resolve([
            "success": true,
            "instanceId": instanceId,
          ])
        } else {
          NSLog("\(self.TAG) 广播未启动(通过定时器确认)，实例ID: \(instanceId)")
          self.advertiseCallbacks.removeValue(forKey: instanceId)
          call.resolve([
            "success": false,
            "instanceId": instanceId,
            "message": "广播启动失败(超时)",
          ])
        }

        // 清理
        self.startAdvertisingCall = nil
        self.currentInstanceId = nil
      } else {
        NSLog("\(self.TAG) 定时器触发 - didStartAdvertising已处理，忽略")
      }
    }
  }

  /**
     * 停止广播
     */
  @objc func stopAdvertising(_ call: CAPPluginCall) {
    if let instanceId = call.getInt("instanceId") {
      NSLog("\(self.TAG) 停止广播, 实例ID: \(instanceId)")

      // 检查蓝牙状态
      guard let manager = peripheralManager else {
        NSLog("\(self.TAG) 蓝牙管理器不可用")
        call.resolve(["success": false, "message": "蓝牙管理器不可用"])
        return
      }

      // 检查是否存在该实例
      if advertiseCallbacks[instanceId] != nil {
        manager.stopAdvertising()

        // 移除服务
        if let service = advertisingService {
          manager.remove(service)
          advertisingService = nil
          advertisingCharacteristic = nil
        }

        advertiseCallbacks.removeValue(forKey: instanceId)
        NSLog("\(self.TAG) 已停止广播, 实例ID: \(instanceId)")
        call.resolve(["success": true])
      } else {
        NSLog("\(self.TAG) 找不到广播实例: \(instanceId)")
        call.resolve(["success": false, "message": "找不到广播实例: \(instanceId)"])
      }
    } else {
      // 如果没有提供实例ID，则停止所有广播
      stopAllAdvertising(call)
    }
  }

  /**
     * 停止所有广播
     */
  @objc func stopAllAdvertising(_ call: CAPPluginCall) {
    NSLog("\(self.TAG) 停止所有广播")

    // 检查蓝牙状态
    guard let manager = peripheralManager else {
      NSLog("\(self.TAG) 蓝牙管理器不可用")
      call.resolve(["success": false, "message": "蓝牙管理器不可用"])
      return
    }

    // 停止广播
    manager.stopAdvertising()

    // 移除服务
    if let service = advertisingService {
      manager.remove(service)
      advertisingService = nil
      advertisingCharacteristic = nil
    }

    // 清理所有实例
    for (id, instance) in advertiseCallbacks {
      instance.timer?.invalidate()
      NSLog("\(self.TAG) 已停止广播, 实例ID: \(id)")
    }

    // 清空实例列表
    advertiseCallbacks.removeAll()

    call.resolve(["success": true])
  }

  /**
     * 从调用参数中获取数据字节数组
     */
  private func getDataBytes(from call: CAPPluginCall) -> [UInt8]? {
    // 优先尝试获取数组格式，与Android版本保持一致
    if let dataArray = call.getArray("data", Int.self) {
      NSLog("\(self.TAG) 从整数数组获取数据: \(dataArray)")
      return dataArray.map { UInt8($0) }
    }

    // 尝试获取字符串格式（十六进制）
    if let dataString = call.getString("data") {
      NSLog("\(self.TAG) 从字符串获取数据: \(dataString)")
      let bytes = hexStringToByteArray(dataString)

      // 调试信息
      if let bytes = bytes {
        let debugBytes = bytes.map { String(format: "%02X", $0) }.joined(separator: " ")
        NSLog("\(self.TAG) 解析后的字节数组: \(debugBytes)")
      } else {
        NSLog("\(self.TAG) 字符串解析失败")
      }

      return bytes
    }

    NSLog("\(self.TAG) 未找到有效的数据参数")
    return nil
  }

  /**
     * 将十六进制字符串转换为字节数组
     */
  private func hexStringToByteArray(_ hexString: String) -> [UInt8]? {
    // 移除所有空格和其他非十六进制字符
    let cleanString = hexString.replacingOccurrences(
      of: "[^0-9A-Fa-f]", with: "", options: .regularExpression)

    NSLog("\(self.TAG) 清理后的十六进制字符串: \(cleanString)")

    // 确保字符串长度为偶数
    guard cleanString.count % 2 == 0 else {
      NSLog("\(self.TAG) 十六进制字符串长度不是偶数: \(cleanString.count)")
      return nil
    }

    var bytes = [UInt8]()
    var index = cleanString.startIndex

    while index < cleanString.endIndex {
      let nextIndex = cleanString.index(index, offsetBy: 2)
      let byteString = cleanString[index..<nextIndex]

      if let byte = UInt8(byteString, radix: 16) {
        bytes.append(byte)
      } else {
        NSLog("\(self.TAG) 无法解析十六进制字符: \(byteString)")
        return nil
      }

      index = nextIndex
    }

    return bytes
  }

  /// 蓝牙外设管理器委托
  public func peripheralManagerDidUpdateState(_ peripheral: CBPeripheralManager) {
    NSLog("\(self.TAG) 蓝牙状态更新: \(peripheral.state)")

    // 提供更详细的状态信息
    switch peripheral.state {
    case .poweredOn:
      NSLog("\(self.TAG) 蓝牙已启用")
      // 检查服务和特征是否已设置
      if let service = advertisingService {
        NSLog("\(self.TAG) 当前配置的服务: \(service.uuid.uuidString)")
        if let characteristics = service.characteristics {
          NSLog("\(self.TAG) 服务包含 \(characteristics.count) 个特征")
          for (index, characteristic) in characteristics.enumerated() {
            NSLog("\(self.TAG) 特征 \(index): \(characteristic.uuid.uuidString)")
          }
        } else {
          NSLog("\(self.TAG) 服务没有特征")
        }
      } else {
        NSLog("\(self.TAG) 没有配置服务")
      }
    case .poweredOff:
      NSLog("\(self.TAG) 蓝牙已关闭")
    case .unauthorized:
      NSLog("\(self.TAG) 蓝牙未授权")
    case .unsupported:
      NSLog("\(self.TAG) 设备不支持蓝牙")
    case .resetting:
      NSLog("\(self.TAG) 蓝牙正在重置")
    case .unknown:
      NSLog("\(self.TAG) 蓝牙状态未知")
    @unknown default:
      NSLog("\(self.TAG) 蓝牙状态未识别")
    }

    // 检查广播状态
    if peripheral.isAdvertising {
      NSLog("\(self.TAG) 当前有广播正在运行")
    } else {
      NSLog("\(self.TAG) 当前没有广播正在运行")
    }
  }

  // 处理广播开始的回调
  public func peripheralManagerDidStartAdvertising(_ peripheral: CBPeripheralManager, error: Error?)
  {
    NSLog("\(self.TAG) peripheralManagerDidStartAdvertising 被调用")

    // 确保我们有正确的instanceId和call
    guard let call = self.startAdvertisingCall, let instanceId = self.currentInstanceId else {
      NSLog("\(self.TAG) 无法找到对应的 call 或 instanceId")
      return
    }

    // 确保我们没有在定时器中已经调用过 resolve
    if let instance = advertiseCallbacks[instanceId], instance.timer != nil {
      // 停止定时器，防止它也调用resolve
      instance.timer?.invalidate()
      instance.timer = nil
      NSLog("\(self.TAG) 已停止定时器，防止重复调用resolve")
    }

    if let error = error {
      // 提供更详细的错误信息
      NSLog("\(self.TAG) 广播开始失败，实例ID: \(instanceId), 错误: \(error.localizedDescription)")
      NSLog("\(self.TAG) 错误详情: \(error)")

      // 检查具体错误类型并记录
      let errorMessage = error.localizedDescription
      if let nsError = error as NSError? {
        NSLog("\(self.TAG) 错误域: \(nsError.domain), 错误码: \(nsError.code)")
        if let userInfo = nsError.userInfo as? [String: Any] {
          for (key, value) in userInfo {
            NSLog("\(self.TAG) 错误信息 \(key): \(value)")
          }
        }
      }

      // 返回失败结果
      advertiseCallbacks.removeValue(forKey: instanceId)
      call.resolve([
        "success": false,
        "instanceId": instanceId,
        "message": "广播启动失败: \(error.localizedDescription)",
      ])
    } else {
      NSLog("\(self.TAG) 广播开始成功，实例ID: \(instanceId)")
      NSLog("\(self.TAG) 当前广播服务: \(String(describing: advertisingService?.uuid.uuidString))")
      NSLog("\(self.TAG) 广播设置: \(peripheral.isAdvertising ? "活跃" : "未活跃")")

      call.resolve([
        "success": true,
        "instanceId": instanceId,
      ])
    }

    // 清理保存的 call 和 instanceId
    self.startAdvertisingCall = nil
    self.currentInstanceId = nil
  }

  // 处理外设连接和读取请求
  public func peripheralManager(
    _ peripheral: CBPeripheralManager, didReceiveRead request: CBATTRequest
  ) {
    NSLog("\(self.TAG) 收到读取请求: \(request.characteristic.uuid)")

    // 检查是否匹配我们的特征
    if request.characteristic.uuid.uuidString == "DEAD" {
      if let value = advertisingCharacteristic?.value {
        request.value = value

        // 打印读取的数据（用于调试）
        if let data = request.value {
          let hexString = data.map { String(format: "%02X", $0) }.joined(separator: " ")
          NSLog("\(self.TAG) 响应读取请求，发送数据: \(hexString)")
        }

        peripheral.respond(to: request, withResult: .success)
        NSLog("\(self.TAG) 成功响应读取请求，数据长度: \(value.count)字节")
      } else {
        NSLog("\(self.TAG) 无法响应读取请求：特征值为空")
        peripheral.respond(to: request, withResult: .unlikelyError)
      }
    } else {
      NSLog("\(self.TAG) 特征不匹配: \(request.characteristic.uuid)")
      peripheral.respond(to: request, withResult: .attributeNotFound)
    }
  }

  // 添加一个服务添加完成回调
  public func peripheralManager(
    _ peripheral: CBPeripheralManager, didAdd service: CBService, error: Error?
  ) {
    if let error = error {
      NSLog("\(self.TAG) 添加服务失败: \(error.localizedDescription)")
    } else {
      NSLog("\(self.TAG) 服务添加成功: \(service.uuid)")
    }
  }
}

/// 广播实例类
class AdvertisingInstance {
  let id: Int
  let data: [String: Any]
  let call: CAPPluginCall
  var timer: Timer?

  init(id: Int, data: [String: Any], call: CAPPluginCall) {
    self.id = id
    self.data = data
    self.call = call
  }
}

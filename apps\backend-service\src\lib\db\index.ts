import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import type { Env } from '@/types/env'

// 创建数据库连接
/**
 * @NOTE 尽量不使用这个，直接使用 supabase
 */
export function createDbConnection(env: Env) {
  // 为 Cloudflare Workers 优化的连接配置
  const client = postgres(env.DATABASE_URL, {
    max: 1, // Workers 环境建议单连接
    idle_timeout: 30, // 增加空闲超时
    connect_timeout: 30, // 增加连接超时到30秒
    prepare: false, // 禁用预处理语句以提高兼容性
    transform: {
      undefined: null // 处理undefined值
    },
    connection: {
      application_name: 'pleasurehub-backend-service' // 添加应用名称便于调试
    }
  })

  return drizzle(client)
}

// 数据库连接类型
export type DbConnection = ReturnType<typeof createDbConnection>

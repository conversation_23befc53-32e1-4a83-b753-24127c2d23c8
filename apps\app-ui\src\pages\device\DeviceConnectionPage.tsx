import { useEffect } from 'react'
import { useNavigate } from 'react-router'
import { motion } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { DeviceControl } from '../../components/device/DeviceControl'
import { DeviceConnectionForm } from '../../components/device/DeviceConnectionForm'
import { useDeviceStore } from '../../stores/device-store'

export default function DeviceConnectionPage() {
  const navigate = useNavigate()
  const { t } = useTranslation('device')

  // 使用全局设备状态
  const { connectedDevice, connectDevice, disconnectDevice } = useDeviceStore()

  // 处理设备连接
  const handleDeviceConnect = async (device: any) => {
    try {
      await connectDevice(device, 'home') // 标记为首页连接
    } catch (error) {
      console.error(t('connection.failed'), error)
    }
  }

  // 处理断开连接
  const handleDisconnect = async () => {
    try {
      await disconnectDevice()
    } catch (error) {
      console.error(t('connection.disconnect_failed'), error)
    }
  }

  // 如果已连接，显示设备控制台
  if (connectedDevice) {
    return <DeviceControl device={connectedDevice} onBack={handleDisconnect} />
  }

  return (
    <div
      className="min-h-screen relative overflow-hidden flex flex-col"
      style={{ backgroundColor: '#121521' }}
    >
      {/* 背景装饰元素 - 使用设计稿提供的SVG */}
      <div className="absolute top-0 left-0 pointer-events-none w-screen">
        <img src="/images/device/bg.svg" className="w-full h-full" />
      </div>

      {/* 页面标题 */}
      <div className="pt-16 pb-8 text-center relative z-10">
        <h1
          className="text-white text-xl font-semibold"
          style={{ fontFamily: "'PingFang SC', sans-serif" }}
        >
          {t('connection.title')}
        </h1>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 flex flex-col items-center px-6 relative z-10">
        {/* 设备图标 */}
        <motion.div
          className="mb-12"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <img
            src="/images/device/bluetooth.svg"
            alt={t('connection.bluetooth_device')}
            className="w-18 h-28 object-contain"
          />
        </motion.div>

        {/* 设备连接表单 */}
        <DeviceConnectionForm onDeviceConnect={handleDeviceConnect} />
      </div>
    </div>
  )
}

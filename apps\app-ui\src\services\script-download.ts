import { scriptDB, type ScriptContent, type ResourceFile, type DownloadTask } from '@/lib/database'
import { scriptPurchaseService } from '@/api/services/script-purchase'
import { NetworkUtils } from '@/utils/networkUtils'
import { FileSystemDownloadService } from './filesystem-download'
import { Capacitor } from '@capacitor/core'

// 下载进度回调接口
export interface DownloadProgress {
  taskId: string
  scriptId: string
  scriptTitle: string
  progress: number // 0-100
  status: DownloadTask['status']
  totalFiles: number
  downloadedFiles: number
  totalSize: number
  downloadedSize: number
  currentFile?: string
  error?: string
}

// 下载服务类
export class ScriptDownloadService {
  private downloadTasks = new Map<string, AbortController>()
  private progressCallbacks = new Set<(progress: DownloadProgress) => void>()

  // 添加进度监听器
  onProgress(callback: (progress: DownloadProgress) => void) {
    this.progressCallbacks.add(callback)
    return () => this.progressCallbacks.delete(callback)
  }

  // 发送进度更新
  private emitProgress(progress: DownloadProgress) {
    this.progressCallbacks.forEach(callback => {
      try {
        callback(progress)
      } catch (error) {
        console.error('进度回调执行失败:', error)
      }
    })
  }

  // 生成文件URL的哈希ID
  private async generateFileId(url: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(url)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  // 从URL获取MIME类型
  private getMimeTypeFromUrl(url: string): string {
    const extension = url.toLowerCase().split('.').pop()
    const mimeMap: Record<string, string> = {
      jpg: 'image/jpeg',
      jpeg: 'image/jpeg',
      png: 'image/png',
      gif: 'image/gif',
      webp: 'image/webp',
      svg: 'image/svg+xml',
      mp4: 'video/mp4',
      webm: 'video/webm',
      mp3: 'audio/mpeg',
      wav: 'audio/wav',
      ogg: 'audio/ogg'
    }
    return mimeMap[extension || ''] || 'application/octet-stream'
  }

  // 下载单个文件
  private async downloadFile(
    url: string,
    scriptId: string,
    signal?: AbortSignal,
    onProgress?: (loaded: number, total: number) => void
  ): Promise<ResourceFile> {
    // 在Capacitor环境中使用文件系统下载
    if (Capacitor.isNativePlatform()) {
      try {
        const fileSystemFile = await FileSystemDownloadService.downloadFileToFileSystem(
          url,
          scriptId,
          onProgress
        )
        // 转换为ResourceFile类型
        return {
          id: fileSystemFile.id,
          url: fileSystemFile.url,
          mimeType: fileSystemFile.mimeType,
          size: fileSystemFile.size,
          scriptId: fileSystemFile.scriptId,
          downloadedAt: fileSystemFile.downloadedAt,
          lastAccessedAt: fileSystemFile.lastAccessedAt,
          filePath: fileSystemFile.filePath
        }
      } catch (error) {
        console.error(`❌ [FileSystem] 下载失败，回退到blob方式: ${url}`, error)
        // 如果文件系统下载失败，回退到原来的blob方式
      }
    }
    try {
      console.log(`📥 开始下载文件: ${url}`)

      // 检查是否已存在
      const fileId = await this.generateFileId(url)
      const existingFile = await scriptDB.resourceFiles.get(fileId)

      if (existingFile) {
        console.log(`✅ 文件已存在，跳过下载: ${url}`)
        // 更新最后访问时间
        await scriptDB.resourceFiles.update(fileId, {
          lastAccessedAt: new Date()
        })
        return existingFile
      }

      // 使用NetworkUtils处理跨域问题
      let response: Response

      if (NetworkUtils.isCapacitor()) {
        // 在Capacitor环境中使用原生HTTP
        const { CapacitorHttp } = await import('@capacitor/core')
        const httpResponse = await CapacitorHttp.get({
          url,
          headers: {},
          responseType: 'blob'
        })

        // 转换为标准Response对象
        const blob = new Blob([httpResponse.data])
        response = new Response(blob, {
          status: httpResponse.status,
          headers: httpResponse.headers as HeadersInit
        })
      } else {
        // Web环境中使用fetch
        response = await fetch(url, { signal })
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const contentLength = response.headers.get('content-length')
      const total = contentLength ? parseInt(contentLength, 10) : 0

      // 读取响应体并跟踪进度
      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法读取响应体')
      }

      const chunks: Uint8Array[] = []
      let loaded = 0

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        chunks.push(value)
        loaded += value.length

        // 发送进度更新
        onProgress?.(loaded, total || loaded)

        // 检查是否被取消
        if (signal?.aborted) {
          throw new Error('下载被取消')
        }
      }

      // 创建blob
      const blob = new Blob(chunks)
      const mimeType = response.headers.get('content-type') || this.getMimeTypeFromUrl(url)

      // 保存到数据库
      const resourceFile: ResourceFile = {
        id: fileId,
        url,
        blob,
        mimeType,
        size: blob.size,
        scriptId,
        downloadedAt: new Date(),
        lastAccessedAt: new Date()
      }

      await scriptDB.resourceFiles.add(resourceFile)

      console.log(`✅ 文件下载完成: ${url} (${blob.size} bytes)`)
      return resourceFile
    } catch (error) {
      console.error(`❌ 文件下载失败: ${url}`, error)
      throw error
    }
  }

  // 从剧本内容中提取所有资源URL
  private extractResourceUrls(content: any, audioUrl: string): string[] {
    const urls = new Set<string>()

    // 添加音频URL
    if (audioUrl) {
      urls.add(audioUrl)
    }

    // 递归提取content中的图片URL
    const extractFromObject = (obj: any) => {
      if (!obj || typeof obj !== 'object') return

      if (Array.isArray(obj)) {
        obj.forEach(extractFromObject)
      } else {
        Object.values(obj).forEach(value => {
          if (
            typeof value === 'string' &&
            (value.startsWith('http://') || value.startsWith('https://') || value.startsWith('/'))
          ) {
            // 检查是否是图片或视频URL
            const ext = value.toLowerCase().split('.').pop()
            if (ext && ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'mp4', 'webm'].includes(ext)) {
              urls.add(value)
            }
          } else if (typeof value === 'object') {
            extractFromObject(value)
          }
        })
      }
    }

    extractFromObject(content)

    return Array.from(urls)
  }

  // 检查剧本是否已下载
  async isScriptDownloaded(scriptId: string): Promise<boolean> {
    try {
      const content = await scriptDB.scriptContents.get(scriptId)
      return !!content
    } catch (error) {
      console.error('检查下载状态失败:', error)
      return false
    }
  }

  // 获取已下载的剧本内容
  async getDownloadedScript(scriptId: string): Promise<ScriptContent | null> {
    try {
      const content = await scriptDB.scriptContents.get(scriptId)
      if (content) {
        // 更新最后访问时间
        await scriptDB.scriptContents.update(scriptId, {
          lastAccessedAt: new Date()
        })
      }
      return content || null
    } catch (error) {
      console.error('获取已下载剧本失败:', error)
      return null
    }
  }

  // 获取下载任务状态
  async getDownloadTask(scriptId: string): Promise<DownloadTask | null> {
    try {
      return (await scriptDB.downloadTasks.where('scriptId').equals(scriptId).first()) || null
    } catch (error) {
      console.error('获取下载任务失败:', error)
      return null
    }
  }

  // 下载剧本
  async downloadScript(scriptId: string, scriptTitle?: string): Promise<ScriptContent> {
    const taskId = `download-${scriptId}-${Date.now()}`
    const abortController = new AbortController()
    this.downloadTasks.set(taskId, abortController)

    try {
      console.log(`🚀 开始下载剧本: ${scriptId}`)

      // 检查是否已下载
      const existingContent = await this.getDownloadedScript(scriptId)
      if (existingContent) {
        console.log(`✅ 剧本已存在，直接返回: ${scriptId}`)
        return existingContent
      }

      // 创建下载任务
      const downloadTask: DownloadTask = {
        id: taskId,
        scriptId,
        scriptTitle: scriptTitle || `剧本 ${scriptId}`,
        status: 'pending',
        progress: 0,
        totalFiles: 0,
        downloadedFiles: 0,
        totalSize: 0,
        downloadedSize: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      }

      await scriptDB.downloadTasks.add(downloadTask)

      // 发送初始进度
      this.emitProgress({
        taskId,
        scriptId,
        scriptTitle: downloadTask.scriptTitle,
        progress: 0,
        status: 'pending',
        totalFiles: 0,
        downloadedFiles: 0,
        totalSize: 0,
        downloadedSize: 0
      })

      // 从服务器获取剧本内容
      console.log(`📡 从服务器获取剧本内容: ${scriptId}`)

      await scriptDB.downloadTasks.update(taskId, {
        status: 'downloading',
        updatedAt: new Date()
      })

      const response = await scriptPurchaseService.downloadScriptContent(scriptId)
      if (!response.success) {
        throw new Error(response.message || '获取剧本内容失败')
      }

      const { content, audioUrl, title } = response.data
      const safeAudioUrl = audioUrl || ''

      // 提取所有资源URL
      const resourceUrls = this.extractResourceUrls(content, safeAudioUrl)
      console.log(`📋 找到 ${resourceUrls.length} 个资源文件`)

      // 更新任务信息
      await scriptDB.downloadTasks.update(taskId, {
        totalFiles: resourceUrls.length,
        updatedAt: new Date()
      })

      this.emitProgress({
        taskId,
        scriptId,
        scriptTitle: title || downloadTask.scriptTitle,
        progress: 5,
        status: 'downloading',
        totalFiles: resourceUrls.length,
        downloadedFiles: 0,
        totalSize: 0,
        downloadedSize: 0,
        currentFile: '正在下载资源文件...'
      })

      // 下载所有资源文件
      let downloadedFiles = 0
      let totalDownloadedSize = 0

      for (const url of resourceUrls) {
        if (abortController.signal.aborted) {
          throw new Error('下载被取消')
        }

        try {
          await this.downloadFile(url, scriptId, abortController.signal, (loaded, total) => {
            // 单个文件的进度回调
            const progress = Math.round(
              ((downloadedFiles + loaded / (total || loaded)) / resourceUrls.length) * 85 + 10
            )

            this.emitProgress({
              taskId,
              scriptId,
              scriptTitle: title || downloadTask.scriptTitle,
              progress,
              status: 'downloading',
              totalFiles: resourceUrls.length,
              downloadedFiles,
              totalSize: 0, // 这里可以优化计算总大小
              downloadedSize: totalDownloadedSize + loaded,
              currentFile: url.split('/').pop() || url
            })
          })

          downloadedFiles++

          // 更新任务进度
          const progress = Math.round((downloadedFiles / resourceUrls.length) * 85 + 10)

          await scriptDB.downloadTasks.update(taskId, {
            downloadedFiles,
            progress,
            updatedAt: new Date()
          })

          this.emitProgress({
            taskId,
            scriptId,
            scriptTitle: title || downloadTask.scriptTitle,
            progress,
            status: 'downloading',
            totalFiles: resourceUrls.length,
            downloadedFiles,
            totalSize: 0,
            downloadedSize: totalDownloadedSize,
            currentFile: `已完成 ${downloadedFiles}/${resourceUrls.length}`
          })
        } catch (error) {
          console.warn(`⚠️ 资源文件下载失败，跳过: ${url}`, error)
          // 继续下载其他文件，不中断整个流程
        }
      }

      // 保存剧本内容到数据库
      console.log(`💾 保存剧本内容到数据库`)

      const scriptContent: ScriptContent = {
        id: scriptId,
        title: title || downloadTask.scriptTitle,
        content,
        audioUrl: safeAudioUrl,
        downloadedAt: new Date(),
        lastAccessedAt: new Date(),
        version: 1
      }

      await scriptDB.scriptContents.add(scriptContent)

      // 完成下载任务
      await scriptDB.downloadTasks.update(taskId, {
        status: 'completed',
        progress: 100,
        completedAt: new Date(),
        updatedAt: new Date()
      })

      this.emitProgress({
        taskId,
        scriptId,
        scriptTitle: title || downloadTask.scriptTitle,
        progress: 100,
        status: 'completed',
        totalFiles: resourceUrls.length,
        downloadedFiles,
        totalSize: 0,
        downloadedSize: totalDownloadedSize
      })

      console.log(`✅ 剧本下载完成: ${scriptId}`)
      return scriptContent
    } catch (error) {
      console.error(`❌ 剧本下载失败: ${scriptId}`, error)

      // 更新任务为失败状态
      await scriptDB.downloadTasks.update(taskId, {
        status: 'failed',
        error: error instanceof Error ? error.message : '未知错误',
        updatedAt: new Date()
      })

      this.emitProgress({
        taskId,
        scriptId,
        scriptTitle: scriptTitle || `剧本 ${scriptId}`,
        progress: 0,
        status: 'failed',
        totalFiles: 0,
        downloadedFiles: 0,
        totalSize: 0,
        downloadedSize: 0,
        error: error instanceof Error ? error.message : '未知错误'
      })

      throw error
    } finally {
      this.downloadTasks.delete(taskId)
    }
  }

  // 取消下载
  async cancelDownload(scriptId: string): Promise<void> {
    // 找到对应的下载任务
    for (const [taskId, controller] of this.downloadTasks.entries()) {
      if (taskId.includes(scriptId)) {
        controller.abort()
        this.downloadTasks.delete(taskId)

        // 更新任务状态
        await scriptDB.downloadTasks.where('scriptId').equals(scriptId).modify({
          status: 'failed',
          error: '用户取消下载',
          updatedAt: new Date()
        })

        console.log(`🚫 取消下载: ${scriptId}`)
        break
      }
    }
  }

  // 删除已下载的剧本
  async deleteDownloadedScript(scriptId: string): Promise<void> {
    try {
      // 删除剧本内容
      await scriptDB.scriptContents.delete(scriptId)

      // 删除相关资源文件
      await scriptDB.resourceFiles.where('scriptId').equals(scriptId).delete()

      // 删除下载任务
      await scriptDB.downloadTasks.where('scriptId').equals(scriptId).delete()

      console.log(`🗑️ 删除已下载剧本: ${scriptId}`)
    } catch (error) {
      console.error(`删除剧本失败: ${scriptId}`, error)
      throw error
    }
  }

  // 获取所有下载任务
  async getAllDownloadTasks(): Promise<DownloadTask[]> {
    try {
      return await scriptDB.downloadTasks.orderBy('createdAt').reverse().toArray()
    } catch (error) {
      console.error('获取下载任务列表失败:', error)
      return []
    }
  }

  // 清理失败的下载任务
  async cleanupFailedTasks(): Promise<void> {
    try {
      await scriptDB.downloadTasks.where('status').equals('failed').delete()

      console.log('🧹 清理失败的下载任务完成')
    } catch (error) {
      console.error('清理失败任务出错:', error)
    }
  }
}

// 创建单例实例
export const scriptDownloadService = new ScriptDownloadService()

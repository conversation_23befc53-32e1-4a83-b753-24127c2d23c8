/**
 * 集成的 ElevenLabs 服务 - 极致性能优化版本
 * 直接集成到 backend-service，消除网络跳转，实现极致速度
 */

import type { Env } from '@/types/env'
import type {
  TTSRequest,
  V3GenerateRequest,
  APIResponse,
  HealthCheckResponse,
  ElevenLabsServiceConfig
} from './types'
import { OptimizedAccountManager } from './account-manager'
import { OptimizedSessionManager } from './session-manager'
import { OptimizedElevenLabsApiClient } from './api-client'
import { ConnectionPool, RequestDeduplicator, SmartRetryManager } from './connection-pool'

export default class IntegratedElevenLabsService {
  private config: ElevenLabsServiceConfig
  private accountManager: OptimizedAccountManager
  private sessionManager: OptimizedSessionManager
  private apiClient: OptimizedElevenLabsApiClient

  // 高级优化组件
  private connectionPool: ConnectionPool
  private requestDeduplicator: RequestDeduplicator
  private retryManager: SmartRetryManager

  // 性能监控
  private totalRequests = 0
  private successfulRequests = 0
  private startTime = Date.now()

  constructor(env: Env, config?: Partial<ElevenLabsServiceConfig>) {
    // env 参数传递给子组件，这里不需要存储

    // 默认配置 - 极致性能优化
    this.config = {
      maxRetries: 2, // 减少重试次数
      retryDelayMs: 1000, // 减少重试延迟
      tokenRefreshThresholdMs: 5 * 60 * 1000, // 5分钟
      healthCheckIntervalMs: 30 * 60 * 1000, // 30分钟
      accountCacheTimeMs: 5 * 60 * 1000, // 5分钟账号缓存
      sessionCacheTimeMs: 30 * 60 * 1000, // 30分钟会话缓存
      maxConcurrentRequests: 10,
      connectionTimeoutMs: 15000, // 15秒连接超时
      requestTimeoutMs: 60000, // 60秒请求超时
      enableRequestDeduplication: true,
      enableConnectionPooling: true,
      ...config
    }

    // 初始化组件
    this.accountManager = new OptimizedAccountManager(env, this.config)
    this.sessionManager = new OptimizedSessionManager(env, this.config)
    this.apiClient = new OptimizedElevenLabsApiClient(this.config)

    // 初始化高级优化组件
    this.connectionPool = new ConnectionPool({
      maxConnections: this.config.maxConcurrentRequests,
      maxIdleTime: 300000, // 5分钟
      connectionTimeout: this.config.connectionTimeoutMs,
      retryAttempts: this.config.maxRetries
    })

    this.requestDeduplicator = new RequestDeduplicator(300000) // 5分钟缓存
    this.retryManager = new SmartRetryManager()
  }

  /**
   * 生成TTS音频流 - 极致优化版本
   */
  async generateTTSStream(params: TTSRequest): Promise<Response> {
    const startTime = Date.now()
    this.totalRequests++

    // console.log(`🚀 开始极速TTS生成: `, params)

    let lastError: Error | null = null

    // 智能重试逻辑
    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        console.log(`⚡ 尝试 ${attempt}/${this.config.maxRetries} (${Date.now() - startTime}ms)`)

        // 1. 极速获取账号 (内存缓存)
        const account = await this.accountManager.getNextAvailableAccount()
        if (!account) {
          throw new Error('没有可用的 ElevenLabs 账号')
        }

        console.log(
          `📱 选中账号: ${account.email.substring(0, 3)}*** (${Date.now() - startTime}ms)`
        )

        // 2. 极速获取会话 (内存缓存 + 去重)
        const session = await this.sessionManager.getValidSession(account)
        if (!session) {
          throw new Error(`账号 ${account.email} 无法获取有效会话`)
        }

        console.log(
          `🔑 获取会话: ${session.token.substring(0, 10)}... (${Date.now() - startTime}ms)`
        )

        // 3. 构建请求
        const v3Request: V3GenerateRequest = {
          inputs: [
            {
              text: params.text,
              voice_id: this.getVoiceId(params.voice_id)
            }
          ],
          model_id: params.model_id || 'eleven_v3',
          settings: {
            stability: params.stability || 0.5,
            use_speaker_boost: params.use_speaker_boost || true
          }
        }

        console.log(`🎯 开始API调用 (${Date.now() - startTime}ms)`)

        // 4. 直接调用流式API
        const response = await this.apiClient.generateTTSStream(v3Request, session)

        console.log(`✅ 流式响应获取成功 (${Date.now() - startTime}ms)`)

        // 5. 异步更新账号成功状态
        this.updateAccountSuccessAsync(account.id, Date.now() - startTime)

        this.successfulRequests++

        // 6. 直接返回流式响应
        return new Response(response.body, {
          status: response.status,
          headers: {
            'Content-Type': 'audio/mpeg',
            'Transfer-Encoding': 'chunked',
            'X-Source': 'integrated-elevenlabs-v3',
            'X-Account': account.email.substring(0, 3) + '***',
            'X-Response-Time': (Date.now() - startTime).toString(),
            'X-Attempt': attempt.toString()
          }
        })
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        console.error(`❌ 尝试 ${attempt} 失败 (${Date.now() - startTime}ms):`, lastError.message)

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === this.config.maxRetries) {
          break
        }

        // 智能延迟：第一次重试立即，后续递增
        if (attempt > 1) {
          await this.sleep(this.config.retryDelayMs * (attempt - 1))
        }
      }
    }

    console.error(`💥 所有尝试失败 (${Date.now() - startTime}ms)`)
    throw lastError || new Error('TTS生成失败')
  }

  /**
   * 生成TTS音频 (Base64返回)
   */
  async generateTTS(params: TTSRequest): Promise<APIResponse<{ audioBase64: string }>> {
    const startTime = Date.now()

    try {
      const account = await this.accountManager.getNextAvailableAccount()
      if (!account) {
        return {
          success: false,
          error: '没有可用的 ElevenLabs 账号'
        }
      }

      const session = await this.sessionManager.getValidSession(account)
      if (!session) {
        return {
          success: false,
          error: `账号 ${account.email} 无法获取有效会话`
        }
      }

      const v3Request: V3GenerateRequest = {
        inputs: [
          {
            text: params.text,
            voice_id: this.getVoiceId(params.voice_id)
          }
        ],
        model_id: params.model_id || 'eleven_v3',
        settings: {
          stability: params.stability || 0.5,
          use_speaker_boost: params.use_speaker_boost || true
        }
      }

      const result = await this.apiClient.generateTTS(v3Request, session)

      if (result.status === 'completed' && result.audio_data) {
        const audioBase64 = this.arrayBufferToBase64(result.audio_data)

        this.updateAccountSuccessAsync(account.id, Date.now() - startTime)
        this.successfulRequests++

        return {
          success: true,
          data: { audioBase64 },
          message: '音频生成成功',
          responseTime: Date.now() - startTime,
          accountUsed: account.email.substring(0, 3) + '***'
        }
      } else {
        return {
          success: false,
          error: result.error || 'TTS生成失败'
        }
      }
    } catch (error) {
      console.error('TTS生成失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 获取可用声音列表
   */
  async getAvailableVoices(): Promise<APIResponse<any[]>> {
    try {
      const account = await this.accountManager.getNextAvailableAccount()
      if (!account) {
        return {
          success: false,
          error: '没有可用的 ElevenLabs 账号'
        }
      }

      const session = await this.sessionManager.getValidSession(account)
      if (!session) {
        return {
          success: false,
          error: '无法获取有效会话'
        }
      }

      const voices = await this.apiClient.getVoices(session)

      return {
        success: true,
        data: voices,
        message: '获取声音列表成功'
      }
    } catch (error) {
      console.error('获取声音列表失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<APIResponse<HealthCheckResponse>> {
    try {
      const accountStats = this.accountManager.getPerformanceStats()
      const sessionStats = this.sessionManager.getPerformanceStats()
      const apiStats = this.apiClient.getPerformanceStats()

      const uptime = Date.now() - this.startTime
      const avgResponseTime = uptime / Math.max(this.totalRequests, 1)

      const healthData: HealthCheckResponse = {
        healthy: accountStats.accountsLoaded > 0,
        total_accounts: accountStats.accountsLoaded,
        active_accounts: accountStats.accountsLoaded, // 简化统计
        failed_accounts: 0,
        details: [],
        avgResponseTime,
        cacheHitRate:
          (accountStats.cacheHitRate + sessionStats.cacheHitRate + apiStats.cacheHitRate) / 3,
        totalRequests: this.totalRequests
      }

      return {
        success: true,
        data: healthData,
        message: '健康检查完成'
      }
    } catch (error) {
      console.error('健康检查失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 获取voice_id
   */
  private getVoiceId(voiceModelId?: string): string {
    if (!voiceModelId) {
      return 'JBFqnCBsd6RMkjVDRZzb' // 默认声音
    }
    return voiceModelId
  }

  /**
   * ArrayBuffer转Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    let binary = ''
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  /**
   * 异步更新账号成功状态
   */
  private updateAccountSuccessAsync(accountId: string, responseTime: number): void {
    // 使用setTimeout异步执行，不阻塞响应
    setTimeout(() => {
      // 这里可以更新账号的成功率和响应时间统计
      console.log(`📊 账号 ${accountId} 响应时间: ${responseTime}ms`)
    }, 0)
  }

  /**
   * 等待函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 获取服务统计信息
   */
  getServiceStats() {
    return {
      totalRequests: this.totalRequests,
      successfulRequests: this.successfulRequests,
      successRate: this.totalRequests > 0 ? this.successfulRequests / this.totalRequests : 0,
      uptime: Date.now() - this.startTime,
      accountManager: this.accountManager.getPerformanceStats(),
      sessionManager: this.sessionManager.getPerformanceStats(),
      apiClient: this.apiClient.getPerformanceStats(),
      // 新增：高级优化组件统计
      connectionPool: this.connectionPool.getStats(),
      requestDeduplicator: this.requestDeduplicator.getStats(),
      retryManager: this.retryManager.getStats()
    }
  }
}

import * as React from 'react'
import { useState, useEffect, useRef, useCallback } from 'react'
import { createPortal } from 'react-dom'
import { Keyboard } from '@capacitor/keyboard'
import { Capacitor } from '@capacitor/core'
import { cn } from '@/lib/utils'
import { Input } from '../ui/input'
import { Textarea } from '../ui/textarea'

interface KeyboardAdaptiveInputProps {
  type: 'input' | 'textarea'
  onSubmit?: (value: string) => void
  className?: string
  placeholder?: string
  value?: string
  defaultValue?: string
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  disabled?: boolean
  readOnly?: boolean
  maxLength?: number
  rows?: number // for textarea
  [key: string]: any // 允许传递其他 props
}

interface KeyboardInfo {
  keyboardHeight: number
}

export function KeyboardAdaptiveInput({
  type,
  onSubmit,
  className,
  placeholder,
  value: controlledValue,
  defaultValue,
  onChange,
  disabled,
  readOnly,
  maxLength,
  rows,
  ...props
}: KeyboardAdaptiveInputProps) {
  const [isFloatingVisible, setIsFloatingVisible] = useState(false)
  const [keyboardHeight, setKeyboardHeight] = useState(0)
  const [internalValue, setInternalValue] = useState(defaultValue || '')
  const [isActiveInput, setIsActiveInput] = useState(false) // 标记当前是否是活跃的输入框
  const [webViewShrunk, setWebViewShrunk] = useState(false) // 标记 WebView 是否自动缩放
  const originalInputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null)
  const floatingInputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null)
  const initialHeightRef = useRef<number>(window.innerHeight) // 记录初始高度
  const cursorPositionRef = useRef<number>(0) // 记录光标位置

  // 判断是否为受控组件
  const isControlled = controlledValue !== undefined
  const currentValue = isControlled ? controlledValue : internalValue

  // 处理值变化
  const handleValueChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const newValue = e.target.value

      // 保存当前光标位置
      cursorPositionRef.current = e.target.selectionStart || 0

      if (!isControlled) {
        setInternalValue(newValue)
      }

      onChange?.(e)
    },
    [isControlled, onChange]
  )

  // 处理原始输入框的点击事件
  const handleOriginalClick = useCallback((e: React.MouseEvent) => {
    // 只在原生平台上启用键盘适配
    if (Capacitor.isNativePlatform()) {
      e.preventDefault()
      e.stopPropagation()

      // 保存当前光标位置（如果原始输入框有焦点）
      if (originalInputRef.current) {
        cursorPositionRef.current = originalInputRef.current.selectionStart || 0
      }

      // 标记为活跃输入框
      setIsActiveInput(true)
      // 显示浮动输入框
      setIsFloatingVisible(true)
    }
  }, [])

  // 处理提交
  const handleSubmit = useCallback(() => {
    onSubmit?.(currentValue)

    // 同步值到原始输入框
    if (originalInputRef.current) {
      const event = new Event('input', { bubbles: true })
      Object.defineProperty(event, 'target', {
        writable: false,
        value: originalInputRef.current
      })
      originalInputRef.current.value = currentValue
      originalInputRef.current.dispatchEvent(event)
    }

    // 隐藏键盘
    Keyboard.hide()
  }, [currentValue, onSubmit])

  // 监听键盘事件
  useEffect(() => {
    if (!Capacitor.isNativePlatform()) {
      return
    }

    let keyboardWillShowListener: any
    let keyboardWillHideListener: any

    const setupListeners = async () => {
      keyboardWillShowListener = await Keyboard.addListener(
        'keyboardWillShow',
        (info: KeyboardInfo) => {
          console.log('KeyboardInfo', JSON.stringify(info))

          // 检测 WebView 是否自动缩放
          const afterKeyboardHeight = window.innerHeight
          const keyboardHeight = info.keyboardHeight
          const initialHeight = initialHeightRef.current

          const webViewShrunk = afterKeyboardHeight < initialHeight

          console.log('Height check:', {
            initialHeight,
            afterKeyboardHeight,
            keyboardHeight,
            webViewShrunk
          })

          setWebViewShrunk(webViewShrunk)

          if (webViewShrunk) {
            // WebView 高度已经缩了，不要再移动元素
            setKeyboardHeight(0) // 贴合底部即可
          } else {
            // WebView 高度没缩，手动移动输入框
            setKeyboardHeight(keyboardHeight)
          }

          // 只有活跃的输入框才显示浮动输入框
          if (isActiveInput) {
            setIsFloatingVisible(true)
          }
        }
      )

      keyboardWillHideListener = await Keyboard.addListener('keyboardWillHide', () => {
        setIsFloatingVisible(false)
        setKeyboardHeight(0)
        setIsActiveInput(false) // 重置活跃状态
        setWebViewShrunk(false) // 重置 WebView 状态
        // 重新记录当前高度作为新的初始高度
        initialHeightRef.current = window.innerHeight
      })
    }

    setupListeners()

    return () => {
      keyboardWillShowListener?.remove()
      keyboardWillHideListener?.remove()
    }
  }, [isActiveInput])

  // 浮动输入框组件
  const FloatingInput = () => {
    if (!isFloatingVisible || !isActiveInput) return null

    // 浮动输入框显示后恢复光标位置
    React.useEffect(() => {
      if (isFloatingVisible && floatingInputRef.current) {
        const input = floatingInputRef.current

        // 延迟恢复光标位置，等待 autoFocus 完成
        setTimeout(() => {
          if (input.setSelectionRange && cursorPositionRef.current > 0) {
            const position = Math.min(cursorPositionRef.current, input.value.length)
            input.setSelectionRange(position, position)
          }
        }, 100)
      }
    }, [isFloatingVisible])

    const floatingStyle: React.CSSProperties = {
      position: 'fixed',
      bottom: webViewShrunk ? '0px' : `${keyboardHeight}px`, // 根据 WebView 是否缩放调整定位
      left: 0,
      right: 0,
      zIndex: 9999,
      backgroundColor: 'white',
      padding: '16px',
      boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.15)',
      borderTop: '1px solid #e5e7eb'
    }

    const InputComponent = type === 'input' ? Input : Textarea

    return createPortal(
      <div style={floatingStyle}>
        <InputComponent
          ref={floatingInputRef as any}
          autoFocus
          value={currentValue}
          onChange={handleValueChange}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readOnly}
          maxLength={maxLength}
          rows={type === 'textarea' ? rows || 3 : undefined}
          className={cn(
            'w-full bg-background border border-input rounded-md px-3 py-2 text-sm',
            'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
            type === 'textarea' && 'resize-none min-h-[80px]'
          )}
          onKeyDown={e => {
            // 监听回车键提交
            if (e.key === 'Enter' && !e.shiftKey && type === 'input') {
              e.preventDefault()
              handleSubmit()
            }
            // textarea 中 Ctrl/Cmd + Enter 提交
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey) && type === 'textarea') {
              e.preventDefault()
              handleSubmit()
            }
          }}
          {...props}
        />
      </div>,
      document.body
    )
  }

  // 渲染原始输入框
  const OriginalInputComponent = type === 'input' ? Input : Textarea

  // 在原生平台上的渲染逻辑
  if (Capacitor.isNativePlatform()) {
    return (
      <>
        {/* 原始输入框 - 只在浮动输入框未显示时渲染 */}
        {!isFloatingVisible && (
          <OriginalInputComponent
            ref={originalInputRef as any}
            value={currentValue}
            onChange={handleValueChange}
            onClick={handleOriginalClick}
            placeholder={placeholder}
            disabled={disabled}
            readOnly={true}
            maxLength={maxLength}
            rows={type === 'textarea' ? rows : undefined}
            className={className}
            {...props}
          />
        )}
        {/* 浮动输入框 */}
        <FloatingInput />
      </>
    )
  }

  // Web 平台的正常渲染
  return (
    <OriginalInputComponent
      ref={originalInputRef as any}
      value={currentValue}
      onChange={handleValueChange}
      placeholder={placeholder}
      disabled={disabled}
      readOnly={readOnly}
      maxLength={maxLength}
      rows={type === 'textarea' ? rows : undefined}
      className={className}
      {...props}
    />
  )
}

// 便捷的导出组件
export function KeyboardAdaptiveTextarea(props: Omit<KeyboardAdaptiveInputProps, 'type'>) {
  return <KeyboardAdaptiveInput type="textarea" {...props} />
}

export function KeyboardAdaptiveInputField(props: Omit<KeyboardAdaptiveInputProps, 'type'>) {
  return <KeyboardAdaptiveInput type="input" {...props} />
}

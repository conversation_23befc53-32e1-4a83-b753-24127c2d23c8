// 多语言配置文件
export interface LanguageConfig {
  code: string // 语言代码，如 'zh', 'en', 'ja'
  name: string // 语言名称
  nativeName: string // 本地化名称
  region?: string // 地区代码，如 'CN', 'US', 'JP'
  direction: 'ltr' | 'rtl' // 文字方向
  fallback?: string // 回退语言
}

// 支持的语言配置
export const SUPPORTED_LANGUAGES: Record<string, LanguageConfig> = {
  zh: {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    region: 'CN',
    direction: 'ltr',
    fallback: 'en'
  },
  'zh-TW': {
    code: 'zh-TW',
    name: 'Traditional Chinese',
    nativeName: '繁體中文',
    region: 'TW',
    direction: 'ltr',
    fallback: 'zh'
  },
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    region: 'US',
    direction: 'ltr'
  },
  ja: {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    region: 'JP',
    direction: 'ltr',
    fallback: 'en'
  },
  // ko: {
  //   code: 'ko',
  //   name: 'Korean',
  //   nativeName: '한국어',
  //   region: 'KR',
  //   direction: 'ltr',
  //   fallback: 'en'
  // },
  es: {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    region: 'ES',
    direction: 'ltr',
    fallback: 'en'
  }
  // fr: {
  //   code: 'fr',
  //   name: 'French',
  //   nativeName: 'Français',
  //   region: 'FR',
  //   direction: 'ltr',
  //   fallback: 'en'
  // },
  // de: {
  //   code: 'de',
  //   name: 'German',
  //   nativeName: 'Deutsch',
  //   region: 'DE',
  //   direction: 'ltr',
  //   fallback: 'en'
  // },
  // pt: {
  //   code: 'pt',
  //   name: 'Portuguese',
  //   nativeName: 'Português',
  //   region: 'PT',
  //   direction: 'ltr',
  //   fallback: 'en'
  // },
  // ru: {
  //   code: 'ru',
  //   name: 'Russian',
  //   nativeName: 'Русский',
  //   region: 'RU',
  //   direction: 'ltr',
  //   fallback: 'en'
  // },
  // ar: {
  //   code: 'ar',
  //   name: 'Arabic',
  //   nativeName: 'العربية',
  //   region: 'SA',
  //   direction: 'rtl',
  //   fallback: 'en'
  // }
}

// 国际化默认配置
export const I18N_DEFAULTS = {
  DEFAULT_LANGUAGE: 'zh',
  FALLBACK_LANGUAGE: 'en'
} as const

// 兼容性导出
export const DEFAULT_LANGUAGE = I18N_DEFAULTS.DEFAULT_LANGUAGE
export const FALLBACK_LANGUAGE = I18N_DEFAULTS.FALLBACK_LANGUAGE

// 语言代码类型
export type SupportedLanguage = keyof typeof SUPPORTED_LANGUAGES

// 获取支持的语言列表
export function getSupportedLanguages(): string[] {
  return Object.keys(SUPPORTED_LANGUAGES)
}

// 检查语言是否支持
export function isLanguageSupported(language: string): language is SupportedLanguage {
  return language in SUPPORTED_LANGUAGES
}

// 获取语言配置
export function getLanguageConfig(language: string): LanguageConfig | null {
  return SUPPORTED_LANGUAGES[language] || null
}

// 获取回退语言
export function getFallbackLanguage(language: string): string {
  const config = getLanguageConfig(language)
  return config?.fallback || FALLBACK_LANGUAGE
}

// 获取最佳匹配语言
export function getBestMatchLanguage(acceptLanguage: string): string {
  if (!acceptLanguage) return DEFAULT_LANGUAGE

  // 简化的语言匹配逻辑
  const languages = acceptLanguage.toLowerCase().split(',')

  for (const langEntry of languages) {
    // 提取语言代码（忽略质量值）
    const langCode = langEntry.trim().split(';')[0].split('-')[0]

    if (isLanguageSupported(langCode)) {
      return langCode
    }
  }

  return DEFAULT_LANGUAGE
}

// 格式化语言代码为 Accept-Language 格式
export function formatAcceptLanguage(language: string): string {
  const config = getLanguageConfig(language)
  if (!config) return `${DEFAULT_LANGUAGE}-${SUPPORTED_LANGUAGES[DEFAULT_LANGUAGE].region}`

  const region = config.region
  if (region) {
    return `${language}-${region},${language};q=0.9,${FALLBACK_LANGUAGE};q=0.8`
  }

  return `${language};q=0.9,${FALLBACK_LANGUAGE};q=0.8`
}

// 获取当前已实现的语言列表（目前有简体中文、繁体中文、英文、西班牙语和日文的完整翻译）
export function getImplementedLanguages(): string[] {
  return ['zh', 'zh-TW', 'en', 'es', 'ja']
}

// 检查语言是否已实现
export function isLanguageImplemented(language: string): boolean {
  return getImplementedLanguages().includes(language)
}

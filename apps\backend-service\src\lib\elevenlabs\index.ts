/**
 * ElevenLabs 集成服务模块入口
 * 导出所有优化的组件和服务
 */

// 主要服务
import IntegratedElevenLabsService from './integrated-service'

// 核心组件
export { OptimizedAccountManager } from './account-manager'
export { OptimizedSessionManager } from './session-manager'
export { OptimizedElevenLabsApiClient } from './api-client'

// 高级优化组件
export { ConnectionPool, RequestDeduplicator, SmartRetryManager } from './connection-pool'

// 类型定义
export type {
  ElevenLabsAccount,
  SessionInfo,
  V3GenerateRequest,
  V3GenerateResponse,
  LoginRequest,
  LoginResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  AccountHealth,
  ElevenLabsServiceConfig,
  TTSRequest,
  APIResponse,
  HealthCheckResponse,
  CachedAccountSelection,
  RequestMetrics,
  ConnectionPoolConfig,
  RequestDeduplicationConfig,
  PerformanceEvent,
  BatchAccountUpdate,
  BatchSessionUpdate
} from './types'

/**
 * 创建默认的集成 ElevenLabs 服务实例
 */
export function createIntegratedElevenLabsService(
  env: any,
  config?: Partial<import('./types').ElevenLabsServiceConfig>
) {
  return new IntegratedElevenLabsService(env, config)
}

/**
 * 默认配置
 */
export const DEFAULT_CONFIG: import('./types').ElevenLabsServiceConfig = {
  maxRetries: 2,
  retryDelayMs: 500,
  tokenRefreshThresholdMs: 5 * 60 * 1000, // 5分钟
  healthCheckIntervalMs: 30 * 60 * 1000, // 30分钟
  accountCacheTimeMs: 5 * 60 * 1000, // 5分钟
  sessionCacheTimeMs: 30 * 60 * 1000, // 30分钟
  maxConcurrentRequests: 10,
  connectionTimeoutMs: 10000, // 10秒
  requestTimeoutMs: 45000, // 45秒
  enableRequestDeduplication: true,
  enableConnectionPooling: true
}

/**
 * 高性能配置 - 极致优化
 */
export const HIGH_PERFORMANCE_CONFIG: import('./types').ElevenLabsServiceConfig = {
  maxRetries: 1, // 减少重试，提高速度
  retryDelayMs: 200, // 极短重试延迟
  tokenRefreshThresholdMs: 10 * 60 * 1000, // 10分钟
  healthCheckIntervalMs: 60 * 60 * 1000, // 1小时
  accountCacheTimeMs: 10 * 60 * 1000, // 10分钟
  sessionCacheTimeMs: 60 * 60 * 1000, // 1小时
  maxConcurrentRequests: 20, // 更高并发
  connectionTimeoutMs: 5000, // 5秒
  requestTimeoutMs: 30000, // 30秒
  enableRequestDeduplication: true,
  enableConnectionPooling: true
}

/**
 * 稳定性配置 - 注重可靠性
 */
export const RELIABILITY_CONFIG: import('./types').ElevenLabsServiceConfig = {
  maxRetries: 5, // 更多重试
  retryDelayMs: 2000, // 较长重试延迟
  tokenRefreshThresholdMs: 3 * 60 * 1000, // 3分钟
  healthCheckIntervalMs: 15 * 60 * 1000, // 15分钟
  accountCacheTimeMs: 3 * 60 * 1000, // 3分钟
  sessionCacheTimeMs: 15 * 60 * 1000, // 15分钟
  maxConcurrentRequests: 5, // 较低并发
  connectionTimeoutMs: 20000, // 20秒
  requestTimeoutMs: 90000, // 90秒
  enableRequestDeduplication: true,
  enableConnectionPooling: true
}

import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router'
import { addToast, Button, Skeleton } from '@heroui/react'
import { Icon } from '@iconify/react'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'
import {
  usePhotoTemplates,
  usePhotoHistory,
  usePhotoGeneration,
  type PhotoTemplate
} from '@/stores/photo-generation-store'
import { useCompleteGenerationMonitor } from '@/hooks/usePhotoGenerationStatus'
import { useRoleStore } from '@/stores/role-store'
import { TemplateCard, HistoryCard, PhotoComparisonModal, PhotoGenerationModal } from './components'
import { PullToRefresh } from '@/components/ui/PullToRefresh'

export default function PhotoAlbumPage() {
  const navigate = useNavigate()
  const { roleId } = useParams<{ roleId: string }>()
  const { t } = useTranslation('photo-album')

  const [activeTab, setActiveTab] = useState<'templates' | 'history'>('history')
  const [selectedTemplateLocal, setSelectedTemplateLocal] = useState<PhotoTemplate | null>(null)
  const [showComparison, setShowComparison] = useState(false)
  const [showGeneration, setShowGeneration] = useState(false)

  // 使用状态管理
  const { templates, loading: templatesLoading, fetchTemplates } = usePhotoTemplates()
  const { history, fetchHistory } = usePhotoHistory()
  const {
    setSelectedTemplate,
    generationStatus,
    currentTask,
    startGeneration,
    setGenerationStatus,
    setCurrentTask
  } = usePhotoGeneration()

  // 角色数据
  const { currentRole, setRole } = useRoleStore()

  // 生成监控
  useCompleteGenerationMonitor({
    onComplete: task => {
      console.log('生成完成:', task)
      setGenerationStatus('completed')
      setCurrentTask(task)

      // 自动刷新历史记录
      if (currentRole?.id) {
        console.log('🔄 生成完成，自动刷新历史记录')
        fetchHistory(currentRole.id).catch(error => {
          console.error('自动刷新历史记录失败:', error)
        })
      }
    },
    onFailed: task => {
      console.log('生成失败:', task)
      setGenerationStatus('failed')
      setCurrentTask(task)
    }
  })

  // 设置当前角色
  useEffect(() => {
    if (roleId && currentRole?.id !== roleId) {
      console.log('设置角色:', roleId)
      setRole(roleId)
    }
  }, [roleId, currentRole, setRole])

  // 加载历史数据
  useEffect(() => {
    if (roleId) {
      fetchHistory(roleId)
    }
  }, [roleId, fetchHistory])

  // 加载模板数据 - 统一处理，避免重复请求
  useEffect(() => {
    if (roleId) {
      if (currentRole && currentRole.gender) {
        // 有角色性别信息时，按性别筛选
        const gender = currentRole.gender as 'male' | 'female'
        console.log('🔄 按性别加载模板，角色ID:', roleId, '性别:', gender)
        fetchTemplates({
          characterId: roleId,
          gender: gender
        })
      } else {
        // 没有性别信息时，加载所有模板
        console.log('🔄 加载所有模板，角色ID:', roleId)
        fetchTemplates({ characterId: roleId })
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [roleId, currentRole?.gender])

  const handleTabChange = (tab: 'templates' | 'history') => {
    setActiveTab(tab)
  }

  const handleTemplateSelect = (_templateId: string, template: PhotoTemplate) => {
    setSelectedTemplateLocal(template)
    setSelectedTemplate(template) // 设置到全局 store
    setShowComparison(true) // 显示对比页面
  }

  const handleConfirmGeneration = async () => {
    if (selectedTemplateLocal && currentRole) {
      try {
        setShowComparison(false)
        setShowGeneration(true)

        // 开始生成，传递角色详细信息
        await startGeneration(
          selectedTemplateLocal.id,
          currentRole.id,
          currentRole.name,
          currentRole.imageUrl || '/images/roles/default.jpg',
          {
            gender:
              currentRole.gender === 'male' || currentRole.gender === 'female'
                ? currentRole.gender
                : 'female', // 默认为女性
            ethnicity: currentRole.ethnicity,
            age: currentRole.age,
            eyeColor: currentRole.eyeColor,
            hairStyle: currentRole.hairStyle,
            hairColor: currentRole.hairColor,
            bodyType: currentRole.bodyType,
            breastSize: currentRole.breastSize,
            buttSize: currentRole.buttSize
          }
        )
      } catch (error: any) {
        console.error('生成失败:', error)
        setShowGeneration(false)
        setShowComparison(true)

        // 显示详细错误信息
        const errorMessage =
          error?.response?.data?.error || error?.message || '生成失败，请稍后重试'
        addToast({
          title: t('toast.generationFailed.title'),
          description: errorMessage,
          color: 'danger'
        })
      }
    }
  }

  const handleCancelComparison = () => {
    setShowComparison(false)
    setSelectedTemplateLocal(null)
  }

  // 刷新历史记录
  const handleRefreshHistory = async () => {
    if (!currentRole?.id) return

    try {
      await fetchHistory(currentRole.id)
    } catch (error) {
      console.error('刷新历史记录失败:', error)
      addToast({
        title: t('toast.refreshFailed.title'),
        description: t('toast.refreshFailed.description'),
        color: 'danger'
      })
    }
  }

  // 调试信息
  console.log('📊 PhotoAlbumPage:', {
    roleId,
    templatesCount: templates?.length || 0,
    templatesLoading,
    activeTab
  })

  const handleBack = () => {
    navigate(-1)
  }

  return (
    <div className="min-h-screen bg-[#0a0b0f]">
      {/* 顶部导航 */}
      <div className="sticky top-0 z-50">
        <div
          className="backdrop-blur-lg border-b border-white/10 h-20 pt-4"
          style={{
            backgroundColor: 'rgba(18, 21, 33, 0.6)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)'
          }}
        >
          <div className="flex items-cente justify-center px-4 relative flex-1">
            {/* 返回按钮 */}
            <Button
              isIconOnly
              variant="light"
              className="text-white absolute top-1/2 transform -translate-y-1/2 left-4"
              onPress={handleBack}
            >
              <Icon icon="solar:arrow-left-linear" className="w-6 h-6" />
            </Button>

            {/* Tab切换 - 居中 */}
            <div className="flex items-center justify-center">
              <div className="relative">
                <button
                  onClick={() => handleTabChange('history')}
                  className={cn(
                    'px-6 py-2 text-lg font-semibold transition-colors',
                    activeTab === 'history' ? 'text-white' : 'text-[#999999]'
                  )}
                  style={{ fontFamily: "'PingFang SC', sans-serif" }}
                >
                  {t('title')}
                </button>
                {activeTab === 'history' && (
                  <div
                    className="absolute -bottom-2 left-1/2 transform -translate-x-1/2"
                    style={{
                      width: '80px',
                      height: '28px',
                      background: 'rgba(147, 51, 234, 0.4)',
                      borderRadius: '50px',
                      filter: 'blur(8px)'
                    }}
                  />
                )}
              </div>

              <div className="relative">
                <button
                  onClick={() => handleTabChange('templates')}
                  className={cn(
                    'px-6 py-2 text-lg font-semibold transition-colors',
                    activeTab === 'templates' ? 'text-white' : 'text-[#999999]'
                  )}
                  style={{ fontFamily: "'PingFang SC', sans-serif" }}
                >
                  {t('templates')}
                </button>
                {activeTab === 'templates' && (
                  <div
                    className="absolute -bottom-2 left-1/2 transform -translate-x-1/2"
                    style={{
                      width: '80px',
                      height: '28px',
                      background: 'rgba(147, 51, 234, 0.4)',
                      borderRadius: '50px',
                      filter: 'blur(8px)'
                    }}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 内容区域 */}
      <div className="pb-20 pt-4">
        <AnimatePresence mode="wait">
          {activeTab === 'templates' ? (
            <motion.div
              key="templates"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.2 }}
            >
              {templatesLoading ? (
                <div className="grid grid-cols-2 gap-3 px-3">
                  {[1, 2, 3, 4].map(placeholder => (
                    <Skeleton key={placeholder} className="rounded-3xl">
                      <div className="w-full h-[280px] rounded-3xl bg-gray-800"></div>
                    </Skeleton>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3 px-3">
                  {templates && templates.length > 0 ? (
                    templates.map(template => (
                      <TemplateCard
                        key={template.id}
                        template={template}
                        onSelect={handleTemplateSelect}
                      />
                    ))
                  ) : (
                    <div className="col-span-2 flex flex-col items-center justify-center py-20">
                      <Icon icon="solar:gallery-bold" className="w-16 h-16 text-gray-600 mb-4" />
                      <div className="text-gray-400 text-lg">{t('empty.templates.title')}</div>
                      <div className="text-gray-500 text-sm mt-2">{t('empty.templates.subtitle')}</div>
                    </div>
                  )}
                </div>
              )}
            </motion.div>
          ) : (
            <motion.div
              key="history"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
              transition={{ duration: 0.2 }}
            >
              <PullToRefresh
                onRefresh={handleRefreshHistory}
                pullText={t('pullToRefresh.pull')}
                releaseText={t('pullToRefresh.release')}
                refreshingText={t('pullToRefresh.refreshing')}
              >
                {!Array.isArray(history) || history.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-20">
                    <Icon icon="solar:gallery-bold" className="w-16 h-16 text-gray-600 mb-4" />
                    <div className="text-gray-400 text-lg">{t('empty.history.title')}</div>
                    <div className="text-gray-500 text-sm mt-2">{t('empty.history.subtitle')}</div>
                    {!Array.isArray(history) && (
                      <div className="text-red-400 text-xs mt-2">
                        {t('empty.history.error', { type: typeof history })}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-3 px-3">
                    {history.map(item => (
                      <HistoryCard key={item.id} history={item} />
                    ))}
                  </div>
                )}
              </PullToRefresh>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 图片对比页面 */}
      <PhotoComparisonModal
        isVisible={showComparison}
        template={selectedTemplateLocal}
        currentRole={currentRole}
        onConfirm={handleConfirmGeneration}
        onCancel={handleCancelComparison}
        t={t}
      />

      {/* 生成阶段页面 - 抽卡风格 */}
      <PhotoGenerationModal
        isVisible={showGeneration}
        template={selectedTemplateLocal}
        currentRole={currentRole}
        generationStatus={generationStatus}
        currentTask={currentTask}
        onBackgroundGeneration={() => {
          setShowGeneration(false)
          // 后台继续生成
        }}
        onReturn={() => {
          setShowGeneration(false)
          setSelectedTemplateLocal(null)
          setGenerationStatus('idle')
        }}
        onRetry={() => {
          setGenerationStatus('idle')
          handleConfirmGeneration()
        }}
        t={t}
      />
    </div>
  )
}

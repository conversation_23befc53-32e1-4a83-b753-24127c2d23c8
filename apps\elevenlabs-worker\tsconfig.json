{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "WebWorker"], "module": "ESNext", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "types": ["@cloudflare/workers-types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}
# Edge Functions

这个包包含了 Pleasurehub App 的 Supabase Edge Functions。

## 功能

- `generate-image`: 异步图片生成服务，使用 Insa3D API

## 部署前准备

### 1. 安装 Supabase CLI

```bash
npm install -g supabase
```

### 2. 登录 Supabase

```bash
supabase login
```

### 3. 链接到项目

```bash
cd apps/edge-functions
supabase link --project-ref YOUR_PROJECT_REF
```

### 4. 设置环境变量

在 Supabase Dashboard 中设置以下环境变量：

- `INSA3D_API_ENDPOINT`: Insa3D API 端点
- `INSA3D_API_TOKEN`: Insa3D API 令牌

**注意**: `SUPABASE_URL` 和 `SUPABASE_SERVICE_ROLE_KEY` 是 Supabase 自动提供的内置环境变量，无需手动设置。

## 部署

### 从根目录部署

```bash
# 部署所有 Edge Functions
pnpm deploy:edge-functions

# 开发模式
pnpm dev:edge-functions
```

### 从当前目录部署

```bash
# 部署所有函数
npm run deploy

# 只部署图片生成函数
npm run deploy:generate-image

# 本地开发
npm run dev
```

## 测试

部署后，可以通过以下 URL 测试：

```
POST https://YOUR_PROJECT_REF.supabase.co/functions/v1/generate-image
```

请求体：

```json
{
  "messageId": "uuid",
  "chatId": "uuid",
  "prompt": "图片描述",
  "characterAvatar": "可选的角色头像URL"
}
```

## 日志查看

```bash
supabase functions logs generate-image
```

## 架构

```
Cloudflare Worker → Edge Function → Insa3D API → Supabase Database
                                                      ↓
                                                 前端轮询检查
```

## 优势

1. **长时间执行**: Edge Functions 支持最多 25 分钟执行时间
2. **稳定性**: 专门为长时间任务设计
3. **解耦**: 图片生成与聊天流程分离
4. **可扩展**: 可以轻松添加更多异步任务

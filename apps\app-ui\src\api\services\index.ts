// 统一的 API 服务入口文件

// 导出所有服务（避免类型冲突）
export {
  chatV2Service,
  type Message,
  type TextUIPart,
  type UIMessage,
  type ChatConversation,
  type LangChainModel,
  type LangChainStatus
} from './chat'

export { profileService, type UserProfile } from './profile'

export { charactersService, type CharacterData, type Role } from './characters'

export { imageService, type ImageGenerationTask, type ImageGenerationResponse } from './image'

export { historyService, type ChatHistory } from './history'

export {
  uploadService,
  type UploadResponse,
  type UploadType,
  type UploadProgressCallback
} from './upload'

export {
  audioService,
  type AudioEffect,
  type AudioLibraryResponse,
  type AudioQueryParams,
  type AudioSearchParams,
  type AudioStats,
  type AudioCategory
} from './audio'

export { ttsService, type GenerateAudioRequest, type GenerateAudioResponse } from './tts'

export { tts2Service } from './tts2'

export { tts3Service, type GenerateStreamAudioRequest } from './tts3'

export {
  deviceService,
  type DeviceConnectRequest,
  type DeviceConnectResponse,
  type SupportedDevicesResponse
} from './devices'

export {
  membershipService,
  type MembershipPlan,
  type UserSubscription,
  type UserPoints,
  type PointsTransaction,
  type PointsPackage,
  type MembershipStatus,
  type CreateSubscriptionRequest,
  type ConsumePointsRequest,
  type CheckPermissionRequest
} from './membership'

export {
  generateChatBackground,
  getChatBackground,
  type BackgroundImageResponse
} from './background'

export {
  referralService,
  type InviteCode,
  type InviteStats,
  type CommissionAccount,
  type CommissionRecord,
  type InvitedUser,
  type WithdrawRequest,
  type WithdrawConfig,
  type ReferralApiResponse,
  type PaginatedResponse
} from './referral'

export {
  activationCodeService,
  type ActivationCode,
  type ActivationCodeValidation,
  type ActivationCodeUsageResult,
  type ActivationCodeUsage,
  type ActivationCodeStats
} from './activation-code'

export {
  photoAlbumService,
  type PhotoTemplate,
  type PhotoHistory,
  type UserMembership,
  type TemplatesResponse,
  type GenerationTaskResponse,
  type GenerationRequest
} from './photo-album'

export {
  characterMediaService,
  type MediaGeneration,
  type ImageRecord,
  type VideoRecord,
  type MediaGenerationsResponse,
  type ImagesResponse,
  type VideosResponse
} from './character-media'

// 导出认证服务（从上级目录）
export * from '../auth'

// 创建统一的 API 服务对象
import { chatV2Service } from './chat'
import { profileService } from './profile'
import { charactersService } from './characters'
import { imageService } from './image'
import { historyService } from './history'
import { uploadService } from './upload'
import { audioService } from './audio'
import { ttsService } from './tts'
import { tts2Service } from './tts2'
import { tts3Service } from './tts3'
import { deviceService } from './devices'
import { membershipService } from './membership'
import { generateChatBackground, getChatBackground } from './background'
import { referralService } from './referral'
import { activationCodeService } from './activation-code'
import { photoAlbumService } from './photo-album'
import { characterMediaService } from './character-media'
import { authApi } from '../auth'

// 统一的 API 服务对象（保持向后兼容）
export const apiService = {
  // 认证相关
  auth: authApi,

  // 聊天相关（LangChain 系统）
  chatV2: chatV2Service,

  // 用户资料相关
  profile: profileService,

  // 角色相关
  roles: charactersService,
  characters: charactersService, // 别名

  // 图像生成相关
  image: imageService,

  // 历史记录相关
  history: historyService,

  // 文件上传相关
  upload: uploadService,

  // 音频相关
  audio: audioService,

  // TTS相关
  tts: ttsService,
  tts2: tts2Service,
  tts3: tts3Service,

  // 设备相关
  device: deviceService,

  // 会员相关
  membership: membershipService,

  // 背景图相关
  background: {
    generate: generateChatBackground,
    get: getChatBackground
  },

  // 邀请码相关
  referral: referralService,

  // 激活码相关
  activationCode: activationCodeService,

  // 写真集相关
  photoAlbum: photoAlbumService,

  // 角色媒体相关
  characterMedia: characterMediaService
}

// 默认导出
export default apiService

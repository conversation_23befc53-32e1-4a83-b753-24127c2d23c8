{"connection": {"title": "連接裝置", "failed": "裝置連接失敗", "disconnect_failed": "裝置中斷失敗", "bluetooth_device": "藍牙裝置"}, "connection_form": {"input_placeholder": "請輸入裝置碼", "invalid_code": "裝置碼無效，請檢查後重試", "connect_failed": "連接裝置失敗，請稍後重試", "qr_not_supported": "目前裝置不支援QR碼掃描", "invalid_qr_code": "掃描到的裝置碼無效", "qr_unrecognized": "未能識別QR碼內容", "camera_permission_denied": "攝影機權限被拒絕，請在設定中開啟攝影機權限", "scan_not_supported": "目前裝置不支援QR碼掃描功能", "scan_failed": "掃描失敗，請重試", "input_label": "輸入裝置碼", "input_example": "範例裝置碼:1583，6842，2137", "connecting": "連接中...", "connect_button": "連接裝置", "or": "或者", "scanning": "掃描中...", "scan_button": "掃描裝置QR碼", "scan_instruction": "將裝置QR碼置於攝影機前進行掃描"}, "control": {"title": "裝置控制台", "connected": "已連接", "bluetooth_error": "藍牙異常", "bluetooth_ready": "藍牙就緒", "bluetooth_initializing": "藍牙初始化中...", "bluetooth_not_initialized": "藍牙未初始化", "wait_bluetooth_init": "請等待藍牙初始化完成後再試", "bluetooth_connection_error": "藍牙連接異常", "mode_not_found": "找不到模式 ID", "select_classic_mode": "選擇經典模式", "send_classic_mode_failed": "發送經典模式指令失敗", "device_function_set": "裝置功能 {{functionKey}} 強度設定為 {{statusText}} (數值: {{intensity}})", "debounce_delay": "防抖延遲後發送藍牙指令", "intensity_zero": "強度為0，發送停止指令", "stop_command_sent": "已發送停止指令", "stop_command_not_found": "找不到功能 {{functionKey}} 的停止指令", "intensity": "強度", "off": "關閉", "level": "{{level}}檔", "classic_mode": "經典模式", "disconnect_failed": "中斷連接失敗", "disconnect_button": "中斷連接"}}
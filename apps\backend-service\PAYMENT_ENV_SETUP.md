# 支付环境变量配置指南

本文档说明如何配置支付系统所需的环境变量。

## 支付宝配置

### 1. 获取支付宝密钥

1. 登录 [支付宝开放平台](https://openhome.alipay.com/platform/developerIndex.htm)
2. 创建应用或使用现有应用
3. 获取以下信息：
   - APP_ID：应用ID
   - 应用私钥 (PRIVATE_KEY)
   - 支付宝公钥 (PUBLIC_KEY)

### 2. 开发环境配置

使用 wrangler 设置开发环境密钥：

```bash
# 切换到 backend-service 目录
cd apps/backend-service

# 设置支付宝配置 (沙箱环境)
pnpm wrangler secret put ALIPAY_APP_ID --env dev
pnpm wrangler secret put ALIPAY_PRIVATE_KEY --env dev
pnpm wrangler secret put ALIPAY_PUBLIC_KEY --env dev

# 设置支付回调域名
pnpm wrangler secret put PAY_CALLBACK_DOMAIN --env dev
# 值示例: http://localhost:8787 (开发环境)
```

### 3. 生产环境配置

```bash
# 设置支付宝配置 (生产环境)
pnpm wrangler secret put ALIPAY_APP_ID --env production
pnpm wrangler secret put ALIPAY_PRIVATE_KEY --env production  
pnpm wrangler secret put ALIPAY_PUBLIC_KEY --env production

# 设置支付回调域名
pnpm wrangler secret put PAY_CALLBACK_DOMAIN --env production
# 值示例: https://api.example.com (生产环境)
```

## 微信支付配置（待实现）

### 开发环境
```bash
pnpm wrangler secret put WECHAT_MCHID --env dev
pnpm wrangler secret put WECHAT_API_KEY --env dev
pnpm wrangler secret put WECHAT_APPID --env dev
```

### 生产环境
```bash
pnpm wrangler secret put WECHAT_MCHID --env production
pnpm wrangler secret put WECHAT_API_KEY --env production
pnpm wrangler secret put WECHAT_APPID --env production
```

## 验证配置

配置完成后，可以通过以下方式验证：

1. 启动开发服务器：
```bash
pnpm dev
```

2. 访问支付配置端点：
```bash
curl http://localhost:8787/api/payment/config/check
```

## 支付宝沙箱测试

开发环境使用支付宝沙箱：
- 网关：https://openapi-sandbox.dl.alipaydev.com/gateway.do
- 可以使用沙箱账号进行测试支付
- 沙箱买家账号：需要在支付宝开放平台获取

## 注意事项

1. **密钥安全**：私钥信息绝不能提交到代码仓库
2. **环境分离**：开发和生产环境使用不同的配置
3. **回调域名**：确保回调域名可以被支付宝访问到
4. **HTTPS要求**：生产环境必须使用HTTPS域名

## 故障排除

### 常见错误

1. **签名验证失败**
   - 检查私钥格式是否正确
   - 确认使用的是RSA2签名算法

2. **回调接收失败**
   - 检查回调URL是否可访问
   - 确认回调处理逻辑是否正确

3. **网关连接失败**
   - 检查网关地址是否正确
   - 确认网络连接正常
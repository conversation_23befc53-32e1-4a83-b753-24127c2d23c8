import { createContext, useContext, useEffect, useState } from 'react';

// 定义主题类型
type Theme = 'dark' | 'light' | 'system';

// 主题上下文类型
type ThemeContextType = {
  theme: Theme;
  setTheme: (theme: Theme) => void;
  resolvedTheme: 'dark' | 'light';
};

// 创建主题上下文
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

// 主题提供者属性
type ThemeProviderProps = {
  children: React.ReactNode;
  defaultTheme?: Theme;
  storageKey?: string;
  disableTransitionOnChange?: boolean;
  enableSystem?: boolean;
  attribute?: string;
};

// 主题提供者组件
export function ThemeProvider({
  children,
  defaultTheme = 'dark',
  storageKey = 'ui-theme',
  ...props
}: ThemeProviderProps) {
  const [theme, setTheme] = useState<Theme>(() => {
    // 从本地存储获取主题，如果没有则使用默认主题
    if (typeof window !== 'undefined') {
      try {
        const storedTheme = localStorage.getItem(storageKey);
        return (storedTheme as Theme) || defaultTheme;
      } catch (e) {
        console.error(e);
      }
    }
    return defaultTheme;
  });

  // 根据系统设置和用户选择解析实际主题
  const [resolvedTheme, setResolvedTheme] = useState<'dark' | 'light'>('dark');

  useEffect(() => {
    const root = window.document.documentElement;

    // 移除之前的类
    root.classList.remove('light', 'dark');

    // 处理系统主题
    if (theme === 'system') {
      const systemTheme = window.matchMedia('(prefers-color-scheme: dark)')
        .matches
        ? 'dark'
        : 'light';
      root.classList.add(systemTheme);
      setResolvedTheme(systemTheme);
      return;
    }

    // 应用用户选择的主题
    root.classList.add(theme);
    setResolvedTheme(theme === 'dark' ? 'dark' : 'light');
  }, [theme]);

  // 保存主题到本地存储
  useEffect(() => {
    try {
      localStorage.setItem(storageKey, theme);
    } catch (e) {
      console.error(e);
    }
  }, [theme, storageKey]);

  // 监听系统主题变化
  useEffect(() => {
    if (theme !== 'system') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = () => {
      const root = window.document.documentElement;
      const systemTheme = mediaQuery.matches ? 'dark' : 'light';

      root.classList.remove('light', 'dark');
      root.classList.add(systemTheme);
      setResolvedTheme(systemTheme);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [theme]);

  // 禁用主题切换时的过渡
  useEffect(() => {
    if (!props.disableTransitionOnChange) return;

    const { classList } = document.documentElement;
    const transitionClass = 'disable-transitions';

    const onChange = () => {
      classList.add(transitionClass);
      setTimeout(() => classList.remove(transitionClass), 0);
    };

    window.addEventListener('themeChange', onChange);
    return () => window.removeEventListener('themeChange', onChange);
  }, [props.disableTransitionOnChange]);

  const value = {
    theme,
    setTheme,
    resolvedTheme,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

// 使用主题的钩子
export function useTheme(): ThemeContextType {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

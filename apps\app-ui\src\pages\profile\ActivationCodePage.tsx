import { useState } from 'react'
import { useNavigate } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { useMembershipStatus, useUserPoints } from '@/hooks/use-membership'

// HeroUI 组件
import {
  Card,
  CardBody,
  CardHeader,
  Button,
  Input,
  Divider,
  Chip,
  addToast,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure
} from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

// API 服务
import { apiService } from '@/api'

interface ActivationResult {
  success: boolean
  message: string
  data?: {
    resultType: string
    conflictResolution: string
    activationCode: {
      type: string
      description: string
      membershipPlan?: {
        name: string
        durationDays: number
        pointsIncluded: number
      }
      pointsPackage?: {
        name: string
        points: number
        bonusPoints: number
      }
    }
  }
}

export default function ActivationCodePage() {
  const navigate = useNavigate()
  const { user } = useAuth()
  const { refetch: refetchMembership } = useMembershipStatus()
  const { refetch: refetchPoints } = useUserPoints()

  const [activationCode, setActivationCode] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [validationResult, setValidationResult] = useState<any>(null)
  const [isValidating, setIsValidating] = useState(false)

  const { isOpen, onOpen, onClose } = useDisclosure()
  const [confirmData, setConfirmData] = useState<any>(null)

  // 验证激活码
  const validateCode = async (code: string) => {
    if (!code.trim()) {
      setValidationResult(null)
      return
    }

    setIsValidating(true)
    try {
      const response = await apiService.activationCode.validateCode(code)

      if (response.success && response.data.valid) {
        setValidationResult(response.data.activationCode)
      } else {
        setValidationResult({ error: response.data.reason || '激活码无效' })
      }
    } catch (error: any) {
      console.error('验证激活码失败:', error)
      setValidationResult({ error: error.message || '验证失败' })
    } finally {
      setIsValidating(false)
    }
  }

  // 处理激活码输入
  const handleCodeChange = (value: string) => {
    setActivationCode(value)

    // 防抖验证
    const timer = setTimeout(() => {
      validateCode(value)
    }, 500)

    return () => clearTimeout(timer)
  }

  // 使用激活码
  const handleActivate = async () => {
    if (!activationCode.trim()) {
      addToast({
        title: '请输入激活码',
        color: 'warning'
      })
      return
    }

    // 如果有验证结果且需要确认，显示确认对话框
    if (validationResult && !validationResult.error) {
      setConfirmData(validationResult)
      onOpen()
      return
    }

    await performActivation()
  }

  // 执行激活
  const performActivation = async () => {
    setIsLoading(true)
    try {
      const response = await apiService.activationCode.useCode(activationCode)

      if (response.success) {
        addToast({
          title: '激活成功！',
          description: response.message,
          color: 'success'
        })

        // 刷新用户数据
        await Promise.all([refetchMembership(), refetchPoints()])

        // 清空输入
        setActivationCode('')
        setValidationResult(null)
        onClose()
      } else {
        addToast({
          title: '激活失败',
          description: response.message,
          color: 'danger'
        })
      }
    } catch (error: any) {
      console.error('激活失败:', error)
      addToast({
        title: '激活失败',
        description: error.message || '请稍后重试',
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 渲染验证结果
  const renderValidationResult = () => {
    if (!validationResult) return null

    if (validationResult.error) {
      return (
        <Card className="mt-4 border-danger-200 bg-danger-50">
          <CardBody className="p-4">
            <div className="flex items-center gap-2">
              <Icon icon="lucide:x-circle" className="w-5 h-5 text-danger" />
              <span className="text-danger text-sm">{validationResult.error}</span>
            </div>
          </CardBody>
        </Card>
      )
    }

    return (
      <Card className="mt-4 border-success-200 bg-success-50">
        <CardBody className="p-4">
          <div className="flex items-start gap-3">
            <Icon icon="lucide:check-circle" className="w-5 h-5 text-success mt-0.5" />
            <div className="flex-1">
              <p className="text-success font-medium text-sm mb-2">激活码有效</p>

              {validationResult.membershipPlan && (
                <div className="space-y-1">
                  <p className="text-success-700 text-sm">
                    <strong>会员套餐:</strong> {validationResult.membershipPlan.name}
                  </p>
                  <p className="text-success-600 text-xs">
                    有效期: {validationResult.membershipPlan.durationDays} 天
                  </p>
                  <p className="text-success-600 text-xs">
                    包含积分: {validationResult.membershipPlan.pointsIncluded}
                  </p>
                </div>
              )}

              {validationResult.pointsPackage && (
                <div className="space-y-1">
                  <p className="text-success-700 text-sm">
                    <strong>积分包:</strong> {validationResult.pointsPackage.name}
                  </p>
                  <p className="text-success-600 text-xs">
                    积分: {validationResult.pointsPackage.points}
                    {validationResult.pointsPackage.bonusPoints > 0 &&
                      ` + ${validationResult.pointsPackage.bonusPoints} 奖励积分`}
                  </p>
                </div>
              )}

              {validationResult.description && (
                <p className="text-success-600 text-xs mt-2">{validationResult.description}</p>
              )}
            </div>
          </div>
        </CardBody>
      </Card>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 safe-area-top">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Button isIconOnly variant="light" onPress={() => navigate(-1)} className="text-foreground">
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-foreground">激活码</h1>
        <Button
          isIconOnly
          variant="light"
          onPress={() => navigate('/profile/activation-history')}
          className="text-foreground"
        >
          <Icon icon="lucide:history" className="w-5 h-5" />
        </Button>
      </div>

      {/* 主要内容 */}
      <div className="px-4 pb-20">
        <Card className="bg-content1/50 backdrop-blur-sm">
          <CardHeader className="pb-2">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon icon="lucide:ticket" className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-foreground">激活码兑换</h2>
                <p className="text-sm text-default-500">输入激活码获取会员或积分奖励</p>
              </div>
            </div>
          </CardHeader>

          <CardBody className="pt-2">
            <div className="space-y-4">
              {/* 激活码输入 */}
              <div>
                <Input
                  label="激活码"
                  placeholder="请输入激活码"
                  value={activationCode}
                  onValueChange={handleCodeChange}
                  variant="bordered"
                  size="lg"
                  startContent={<Icon icon="lucide:ticket" className="w-4 h-4 text-default-400" />}
                  endContent={
                    isValidating && (
                      <Icon
                        icon="lucide:loader-2"
                        className="w-4 h-4 text-default-400 animate-spin"
                      />
                    )
                  }
                  classNames={{
                    input: 'text-center tracking-wider uppercase',
                    inputWrapper: 'border-2'
                  }}
                />
              </div>

              {/* 验证结果 */}
              {renderValidationResult()}

              {/* 激活按钮 */}
              <Button
                color="primary"
                size="lg"
                className="w-full"
                onPress={handleActivate}
                isLoading={isLoading}
                isDisabled={!activationCode.trim() || !!validationResult?.error}
              >
                {isLoading ? '激活中...' : '立即激活'}
              </Button>
            </div>
          </CardBody>
        </Card>

        {/* 使用说明 */}
        <Card className="mt-6 bg-content1/30 backdrop-blur-sm">
          <CardHeader>
            <h3 className="text-base font-medium text-foreground">使用说明</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <div className="space-y-3 text-sm text-default-600">
              <div className="flex items-start gap-2">
                <Icon icon="lucide:info" className="w-4 h-4 text-primary mt-0.5" />
                <span>激活码可以兑换会员套餐或积分包</span>
              </div>
              <div className="flex items-start gap-2">
                <Icon icon="lucide:shield-check" className="w-4 h-4 text-success mt-0.5" />
                <span>每个激活码只能使用一次</span>
              </div>
              <div className="flex items-start gap-2">
                <Icon icon="lucide:clock" className="w-4 h-4 text-warning mt-0.5" />
                <span>激活码可能有有效期限制，请及时使用</span>
              </div>
              <div className="flex items-start gap-2">
                <Icon icon="lucide:crown" className="w-4 h-4 text-secondary mt-0.5" />
                <span>激活会员时将直接替换当前会员状态</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 确认对话框 */}
      <Modal isOpen={isOpen} onClose={onClose} placement="center">
        <ModalContent>
          <ModalHeader>
            <div className="flex items-center gap-2">
              <Icon icon="lucide:alert-triangle" className="w-5 h-5 text-warning" />
              确认激活
            </div>
          </ModalHeader>
          <ModalBody>
            {confirmData && (
              <div className="space-y-3">
                <p className="text-foreground">您即将激活以下内容：</p>

                {confirmData.membershipPlan && (
                  <Card className="bg-warning-50 border-warning-200">
                    <CardBody className="p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon icon="lucide:crown" className="w-4 h-4 text-warning" />
                        <span className="font-medium text-warning-800">
                          {confirmData.membershipPlan.name}
                        </span>
                      </div>
                      <p className="text-xs text-warning-700">
                        有效期: {confirmData.membershipPlan.durationDays} 天
                      </p>
                    </CardBody>
                  </Card>
                )}

                {confirmData.pointsPackage && (
                  <Card className="bg-primary-50 border-primary-200">
                    <CardBody className="p-3">
                      <div className="flex items-center gap-2 mb-2">
                        <Icon icon="lucide:coins" className="w-4 h-4 text-primary" />
                        <span className="font-medium text-primary-800">
                          {confirmData.pointsPackage.name}
                        </span>
                      </div>
                      <p className="text-xs text-primary-700">
                        积分: {confirmData.pointsPackage.points}
                        {confirmData.pointsPackage.bonusPoints > 0 &&
                          ` + ${confirmData.pointsPackage.bonusPoints} 奖励`}
                      </p>
                    </CardBody>
                  </Card>
                )}

                <p className="text-sm text-default-600">激活后将立即生效，确定要继续吗？</p>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              取消
            </Button>
            <Button color="primary" onPress={performActivation} isLoading={isLoading}>
              确认激活
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </div>
  )
}

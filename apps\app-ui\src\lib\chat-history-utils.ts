import { isToday, isYesterday, subMonths, subWeeks, format } from 'date-fns'

// 聊天记录接口
export interface ChatHistory {
  id: string
  roleId: string
  title: string
  lastMessage: string
  createdAt: string
  updatedAt: string
}

// 分组的聊天记录
export type GroupedChats = {
  today: ChatHistory[]
  yesterday: ChatHistory[]
  lastWeek: ChatHistory[]
  lastMonth: ChatHistory[]
  older: ChatHistory[]
}

/**
 * 将聊天记录按日期分组
 * @param chats 聊天记录数组
 * @returns 分组后的聊天记录
 */
export function groupChatsByDate(chats: ChatHistory[]): GroupedChats {
  const now = new Date()
  const oneWeekAgo = subWeeks(now, 1)
  const oneMonthAgo = subMonths(now, 1)

  return chats.reduce(
    (groups, chat) => {
      const chatDate = new Date(chat.updatedAt || chat.createdAt)

      if (isToday(chatDate)) {
        groups.today.push(chat)
      } else if (isYesterday(chatDate)) {
        groups.yesterday.push(chat)
      } else if (chatDate > oneWeekAgo) {
        groups.lastWeek.push(chat)
      } else if (chatDate > oneMonthAgo) {
        groups.lastMonth.push(chat)
      } else {
        groups.older.push(chat)
      }

      return groups
    },
    {
      today: [],
      yesterday: [],
      lastWeek: [],
      lastMonth: [],
      older: []
    } as GroupedChats
  )
}

/**
 * 格式化聊天时间
 * @param dateString 日期字符串
 * @returns 格式化后的时间
 */
export function formatChatTime(dateString: string): string {
  const date = new Date(dateString)
  if (isToday(date)) {
    return format(date, 'HH:mm')
  } else if (isYesterday(date)) {
    return '昨天'
  } else {
    return format(date, 'MM-dd')
  }
}

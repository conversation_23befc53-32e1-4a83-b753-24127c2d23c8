{"name": "capacitor-ble-advertiser", "version": "0.1.0", "description": "Capacitor插件，用于实现BLE广播功能", "main": "dist/plugin.js", "module": "dist/esm/index.js", "types": "dist/esm/index.d.ts", "type": "module", "scripts": {"build": "pnpm run clean && tsc && rollup -c rollup.config.js", "clean": "rimraf ./dist", "watch": "tsc --watch", "prepublishOnly": "pnpm run build"}, "author": "RC App Team", "dependencies": {"@capacitor/core": "^7.2.0"}, "devDependencies": {"@capacitor/android": "^7.2.0", "@capacitor/ios": "^7.2.0", "@rollup/plugin-commonjs": "^25.0.7", "@rollup/plugin-node-resolve": "^15.2.3", "rimraf": "^6.0.1", "rollup": "^4.40.2", "typescript": "^5.8.3"}, "files": ["dist/", "ios/", "android/", "CapacitorBleAdvertiser.podspec"], "keywords": ["capacitor", "plugin", "native", "bluetooth", "ble", "advertise"], "capacitor": {"ios": {"src": "ios"}, "android": {"src": "android"}}}
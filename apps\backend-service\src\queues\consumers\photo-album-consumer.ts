import type { Env } from '@/types/env'
import type { MessageBatch } from '@cloudflare/workers-types'
import { uploadToR2, getR2ConfigFromEnv, IMAGE_UPLOAD_OPTIONS } from '@/lib/utils/r2-upload'

const config = {
  endpoint: 'nizuhoxrees7jv',
  token: 'sEYZ9719krdppRaYSoTxnw'
}

// 写真集生成任务类型
export interface PhotoAlbumGenerationTask {
  messageId: string
  userId: string
  templateId: string
  characterId: string
  characterName: string
  characterImageUrl: string
  prompt: string
  templateName: string
  generationId: string
  mediaGenerationId: string // 关联的数据库记录ID
  metadata?: {
    width?: number
    height?: number
    characterDescription?: string
    originalTemplatePrompt?: string
    characterInfo?: any
    [key: string]: any
  }
}

// Insa3D API 请求类型
interface Insa3DTaskRequest {
  inputs: {
    [key: string]: {
      title: string
      value: any
    }
  }
}

// Insa3D API 响应类型
interface Insa3DTaskResponse {
  task_id: string
  estimated_steps: number
}

interface Insa3DStatusResponse {
  task_id: string
  status: 'PENDING' | 'IN_PROGRESS' | 'EXECUTING' | 'COMPLETED' | 'FAILED'
  estimated_steps: number
  completed_steps: number
  image_urls?: string[]
  error_message?: string
}

/**
 * 写真集生成队列消费者
 */
export class PhotoAlbumGenerationConsumer {
  constructor(private env: Env) {}

  /**
   * 处理队列消息
   */
  async handleMessage(batch: MessageBatch<PhotoAlbumGenerationTask>): Promise<void> {
    console.log(`📸 [写真集生成] 收到 ${batch.messages.length} 个任务`)

    for (const message of batch.messages) {
      try {
        await this.processPhotoAlbumGeneration(message.body)
        message.ack()
      } catch (error) {
        console.error('📸 [写真集生成] 处理任务失败:', error)
        message.retry()
      }
    }
  }

  /**
   * 处理单个写真集生成任务
   */
  private async processPhotoAlbumGeneration(task: PhotoAlbumGenerationTask): Promise<void> {
    console.log(`📸 [写真集生成] 开始处理任务:`, {
      messageId: task.messageId,
      templateId: task.templateId,
      characterName: task.characterName
    })

    try {
      // 1. 更新状态为开始生成
      await this.updateGenerationProgress(task, {
        status: 'processing',
        progress: 10,
        message: '正在连接服务...'
      })

      // 2. 调用 Insa3D API 启动任务
      const insa3dTaskId = await this.startInsa3DGeneration(task)

      // 3. 更新状态为处理中
      await this.updateGenerationProgress(task, {
        status: 'processing',
        progress: 30,
        message: '正在处理...'
      })

      // 4. 等待任务完成
      const result = await this.waitForTaskCompletion(insa3dTaskId, task)

      // 5. 处理完成结果
      if (result.status === 'COMPLETED' && result.image_urls?.[0]) {
        await this.updateGenerationProgress(task, {
          status: 'completed',
          progress: 100,
          message: '生成完成',
          imageUrl: result.image_urls[0],
          completedSteps: result.completed_steps,
          estimatedSteps: result.estimated_steps
        })
        console.log(`📸 [写真集生成] 任务完成:`, task.messageId)
      } else {
        throw new Error(result.error_message || '生成失败')
      }
    } catch (error) {
      console.error(`📸 [写真集生成] 任务失败:`, task.messageId, error)
      await this.updateGenerationProgress(task, {
        status: 'failed',
        progress: 0,
        message: '生成失败',
        errorMessage: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  /**
   * 启动 Insa3D 写真集生成
   */
  private async startInsa3DGeneration(task: PhotoAlbumGenerationTask): Promise<string> {
    const request: Insa3DTaskRequest = {
      inputs: {
        acf316b99d40ca0a: {
          title: 'prompt',
          value: task.prompt
        },
        e66b86632f418b48: {
          title: 'seed',
          value: 4124214324244234
        },
        da9850330bee2a7b: {
          title: 'width',
          value: task.metadata?.width || 1024
        },
        '144baa72b0139eb9': {
          title: 'height',
          value: task.metadata?.height || 1440
        },
        a5f1cde189946aba: {
          title: 'load image',
          value: task.characterImageUrl
        }
      }
    }

    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${config.endpoint}/run_task`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${config.token}`
        },
        body: JSON.stringify(request)
      }
    )

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Insa3D API 请求失败: ${response.status} ${errorText}`)
    }

    const result: Insa3DTaskResponse = await response.json()
    console.log('📸 [写真集生成] Insa3D 任务已启动:', result.task_id)

    return result.task_id
  }

  /**
   * 等待任务完成
   */
  private async waitForTaskCompletion(
    taskId: string,
    task: PhotoAlbumGenerationTask,
    maxWaitTime = 300000, // 5分钟
    pollInterval = 5000 // 5秒
  ): Promise<Insa3DStatusResponse> {
    const startTime = Date.now()

    while (Date.now() - startTime < maxWaitTime) {
      const status = await this.checkTaskStatus(taskId)

      // 更新进度
      if (status.estimated_steps > 0 && status.completed_steps >= 0) {
        const progress = Math.min(
          Math.round((status.completed_steps / status.estimated_steps) * 70) + 30, // 30-100%
          status.status === 'COMPLETED' ? 100 : 99
        )

        await this.updateGenerationProgress(task, {
          status: 'processing',
          progress,
          message: `正在生成... (${status.completed_steps}/${status.estimated_steps})`,
          estimatedSteps: status.estimated_steps,
          completedSteps: status.completed_steps
        })
      }

      if (status.status === 'COMPLETED' || status.status === 'FAILED') {
        return status
      }

      // 等待下次轮询
      await new Promise(resolve => setTimeout(resolve, pollInterval))
    }

    throw new Error('任务等待超时')
  }

  /**
   * 检查任务状态
   */
  private async checkTaskStatus(taskId: string): Promise<Insa3DStatusResponse> {
    const response = await fetch(
      `https://api.instasd.com/api_endpoints/${config.endpoint}/task_status/${taskId}`,
      {
        method: 'GET',
        headers: {
          Authorization: `Bearer ${config.token}`
        }
      }
    )

    if (!response.ok) {
      throw new Error(`查询任务状态失败: ${response.status}`)
    }

    return await response.json()
  }

  /**
   * 下载图片并上传到 R2
   */
  private async downloadAndUploadToR2(
    imageUrl: string,
    task: PhotoAlbumGenerationTask
  ): Promise<string | null> {
    try {
      console.log(`📸 [R2上传] 开始下载图片:`, imageUrl)

      // 1. 下载图片
      const response = await fetch(imageUrl)
      if (!response.ok) {
        throw new Error(`下载图片失败: ${response.status} ${response.statusText}`)
      }

      const imageBuffer = await response.arrayBuffer()
      console.log(`📸 [R2上传] 图片下载完成，大小: ${imageBuffer.byteLength} 字节`)

      // 2. 获取 R2 配置
      const r2Config = getR2ConfigFromEnv(this.env)
      if (!r2Config) {
        console.error(`❌ [R2上传] R2 配置缺失，检查环境变量`)
        throw new Error('R2 配置缺失')
      }

      console.log(`📸 [R2上传] R2 配置获取成功:`, {
        bucketName: r2Config.bucketName,
        hasAccountId: !!r2Config.accountId,
        hasAccessKey: !!r2Config.accessKeyId,
        hasSecretKey: !!r2Config.secretAccessKey,
        publicBaseUrl: r2Config.publicBaseUrl
      })

      // 3. 生成文件名
      const timestamp = new Date().toISOString().split('T')[0]
      const uniqueId = task.generationId.split('-').pop() || 'unknown'
      const fileName = `photo-album-${uniqueId}.png`

      // 4. 上传到 R2
      const uploadResult = await uploadToR2(imageBuffer, r2Config, {
        ...IMAGE_UPLOAD_OPTIONS,
        fileName,
        folder: `photo-album/${timestamp}`
      })

      if (!uploadResult.success) {
        throw new Error(`R2上传失败: ${uploadResult.error}`)
      }

      console.log(`✅ [R2上传] 上传成功:`, {
        originalUrl: imageUrl,
        r2Url: uploadResult.url,
        key: uploadResult.key,
        size: uploadResult.size
      })

      return uploadResult.url!
    } catch (error) {
      console.error(`❌ [R2上传] 失败:`, error)
      return null
    }
  }

  /**
   * 更新生成进度
   */
  private async updateGenerationProgress(
    task: PhotoAlbumGenerationTask,
    update: {
      status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
      progress: number
      message: string
      imageUrl?: string
      estimatedSteps?: number
      completedSteps?: number
      errorMessage?: string
    }
  ): Promise<void> {
    try {
      console.log(`📸 [写真集生成] 更新进度:`, {
        messageId: task.messageId,
        mediaGenerationId: task.mediaGenerationId,
        ...update
      })

      // 导入数据库更新函数
      const { updateMediaGeneration, getMediaGenerationById } = await import(
        '@/lib/db/queries/media-generation'
      )

      // 先获取当前记录，保留原有的 metadata
      const currentRecord = await getMediaGenerationById(this.env, task.mediaGenerationId)
      if (!currentRecord) {
        throw new Error(`找不到媒体生成记录: ${task.mediaGenerationId}`)
      }

      // 准备更新数据，保留原有 metadata 并只更新进度相关字段
      const updateData: any = {
        status: update.status,
        metadata: {
          ...(currentRecord.metadata as any), // 保留原有的 metadata（包含 generationId）
          progress: update.progress,
          message: update.message,
          estimatedSteps: update.estimatedSteps,
          completedSteps: update.completedSteps,
          errorMessage: update.errorMessage
        }
      }

      // 如果任务完成，处理图片上传到 R2
      console.log(`📸 [写真集生成] 检查 R2 上传条件:`, {
        status: update.status,
        hasImageUrl: !!update.imageUrl,
        imageUrl: update.imageUrl?.substring(0, 100) + '...',
        shouldUpload: update.status === 'completed' && !!update.imageUrl
      })

      if (update.status === 'completed' && update.imageUrl) {
        console.log(`📸 [写真集生成] 任务完成，开始上传到 R2...`)

        // 尝试上传到 R2
        const r2Url = await this.downloadAndUploadToR2(update.imageUrl, task)

        if (r2Url) {
          // 使用 R2 URL
          updateData.outputUrls = [r2Url]
          console.log(`✅ [写真集生成] R2 上传成功，使用 R2 URL: ${r2Url}`)
        } else {
          // 如果 R2 上传失败，使用原始 URL
          updateData.outputUrls = [update.imageUrl]
          console.log(`⚠️ [写真集生成] R2 上传失败，使用原始 URL: ${update.imageUrl}`)
        }

        updateData.completedAt = new Date()
      }

      // 如果任务失败，设置错误信息
      if (update.status === 'failed' && update.errorMessage) {
        updateData.errorMessage = update.errorMessage
      }

      // 更新数据库记录
      await updateMediaGeneration(this.env, task.mediaGenerationId, updateData)

      console.log(`📸 [写真集生成] 数据库状态已更新:`, {
        mediaGenerationId: task.mediaGenerationId,
        status: update.status
      })
    } catch (error) {
      console.error(`📸 [写真集生成] 更新进度失败:`, error)
      // 不抛出错误，避免影响主流程
    }
  }
}

/**
 * 队列消费者入口函数
 */
export default {
  async queue(batch: MessageBatch<PhotoAlbumGenerationTask>, env: Env): Promise<void> {
    const consumer = new PhotoAlbumGenerationConsumer(env)
    await consumer.handleMessage(batch)
  }
}

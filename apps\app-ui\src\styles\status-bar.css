/* 状态栏适配样式 */

/* 状态栏相关的CSS变量（由StatusBarAdapter组件设置） */
:root {
  --status-bar-height: 0px; /* 实际状态栏高度 */
  --safe-area-top-actual: 0px; /* 实际使用的顶部安全区域 */
  --status-bar-configured: 0; /* 状态栏是否已配置完成 */
}

/* 状态栏适配器组件样式 */
.status-bar-adapter {
  /* 确保高度正确 */
  min-height: 100vh;
  min-height: calc(100vh - env(safe-area-inset-top, 0px));

  /* 平滑过渡效果 */
  transition: padding-top 0.3s ease;
}

/* 工具类：需要考虑状态栏高度的元素 */
.with-status-bar-padding {
  padding-top: var(--safe-area-top-actual);
}

.with-status-bar-margin {
  margin-top: var(--safe-area-top-actual);
}

/* 工具类：固定定位元素的顶部偏移 */
.fixed-top-with-status-bar {
  top: var(--safe-area-top-actual);
}

/* 工具类：绝对定位元素的顶部偏移 */
.absolute-top-with-status-bar {
  top: var(--safe-area-top-actual);
}

/* 工具类：sticky定位元素的顶部偏移 */
.sticky-top-with-status-bar {
  top: var(--safe-area-top-actual);
}

/* 高度计算工具类 */
.height-without-status-bar {
  height: calc(100vh - var(--safe-area-top-actual));
}

.min-height-without-status-bar {
  min-height: calc(100vh - var(--safe-area-top-actual));
}

.max-height-without-status-bar {
  max-height: calc(100vh - var(--safe-area-top-actual));
}

/* 针对不同平台的特殊处理 */
body.platform-ios .status-bar-adapter {
  /* iOS特定的状态栏处理 */
}

body.platform-android .status-bar-adapter {
  /* Android特定的状态栏处理 */
}

/* 调试模式样式（仅开发环境） */
.status-bar-debug-info {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  line-height: 1.2;
  padding: 8px;
  border-bottom-left-radius: 8px;
  font-family: monospace;
  pointer-events: none;
  user-select: none;
}

/* 确保调试信息在所有内容之上 */
.status-bar-debug-info {
  transform: translateZ(0);
  will-change: transform;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .status-bar-debug-info {
    font-size: 9px;
    padding: 6px;
  }
}

/* 横屏模式调整 */
@media (orientation: landscape) {
  .status-bar-adapter {
    /* 横屏时可能需要不同的处理 */
  }
}

/* 高DPI设备调整 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .status-bar-debug-info {
    font-size: 8px;
  }
}

// 主要接口和类型
export type {
  ChatDatabaseInterface,
  ChatDatabaseConfig,
  ChatSession,
  ChatMessage,
  MessageAttachment
} from './types'

// 导出LangChain消息类型（用于兼容性）
export type { Message as LangChainMessage } from '@/api/services'

export { ChatDatabaseError, DEFAULT_DATABASE_CONFIG } from './types'

// 主数据库类
export { ChatDatabase, chatDatabase, initializeChatDatabase } from './chat-database'

// 导入数据库实例
import { chatDatabase as _chatDatabase } from './chat-database'

// 全局数据库实例获取函数
export const getGlobalChatDatabase = () => {
  return _chatDatabase
}

// 模块化组件
export { DatabaseConnection } from './database-connection'
export { DatabaseSchema } from './database-schema'
export { SessionRepository } from './session-repository'
export { MessageRepository } from './message-repository'
export { AttachmentRepository } from './attachment-repository'
export { BackgroundRepository } from './background-repository'
export { DatabaseUtils } from './database-utils'

// 聊天同步管理器
export { defaultChatSyncManager } from '../chat-sync/chat-sync-manager'

// 兼容性导出（保持向后兼容）
export { initializeChatDatabase as initializeChatSystem } from './chat-database'
export { initializeChatDatabase as initializeEnhancedChatSystem } from './chat-database'

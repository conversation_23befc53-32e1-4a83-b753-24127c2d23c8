import { useEffect, useState } from 'react'
import { bluetoothService } from '../pages/interactive/utils/BluetoothService'
import { Capacitor } from '@capacitor/core'

/**
 * 蓝牙测试按钮组件
 * 用于测试蓝牙广播功能
 */
export function BleTestButton() {
  const [isLoading, setIsLoading] = useState(false)
  const [isNative, setIsNative] = useState(false)
  const [isInitialized, setIsInitialized] = useState(false)
  const [bluetoothEnabled, setBluetoothEnabled] = useState(false)
  const [status, setStatus] = useState<string>('准备就绪')
  const [error, setError] = useState<string | null>(null)
  const [platformInfo, setPlatformInfo] = useState<string>('')
  const [selectedCommand, setSelectedCommand] = useState('6db643ce97fe427ce49c6c')
  const [isMultiMode, setIsMultiMode] = useState(false)
  const [selectedCommands, setSelectedCommands] = useState<string[]>([])
  const [interval, setInterval] = useState(100)

  // 测试命令列表
  const testCommands = [
    { id: 'cmd1', name: '命令1 - 强度1', value: '6db643ce97fe427ce49c6c' },
    { id: 'cmd2', name: '命令2 - 强度2', value: '6db643ce97fe427ce7075e' },
    { id: 'cmd3', name: '命令3 - 强度3', value: '6db643ce97fe427ce68e4f' },
    { id: 'cmd4', name: '停止命令', value: '6db643ce97fe427ce5157d' }
  ]

  // 复合命令预设
  const multiCommandPresets = [
    {
      id: 'preset1',
      name: '同时控制多功能',
      commands: ['6db643ce97fe427ce49c6c', '6db643ce97fe427ca4982e']
    },
    {
      id: 'preset2',
      name: '震动+强度递增',
      commands: ['6db643ce97fe427ce49c6c', '6db643ce97fe427ce7075e', '6db643ce97fe427cecd4e0']
    }
  ]

  // 组件挂载时检查环境
  useEffect(() => {
    // 检查是否在原生环境
    const checkEnvironment = async () => {
      const isNativeApp = Capacitor.isNativePlatform()
      setIsNative(isNativeApp)

      // 获取平台信息
      const platform = Capacitor.getPlatform()
      setPlatformInfo(`平台: ${platform}`)
      setStatus(isNativeApp ? '正在初始化蓝牙...' : '浏览器环境，模拟蓝牙功能')

      if (isNativeApp) {
        try {
          // 初始化蓝牙服务
          setStatus('尝试初始化蓝牙服务...')
          const initialized = await bluetoothService.initialize()
          setIsInitialized(initialized)
          setStatus(initialized ? '蓝牙服务已初始化' : '蓝牙服务初始化失败')

          if (initialized) {
            // 检查蓝牙状态
            setStatus('检查蓝牙状态...')
            const enabled = await bluetoothService.checkBluetooth()
            setBluetoothEnabled(enabled)
            setStatus(enabled ? '蓝牙已启用' : '蓝牙未启用')
          } else {
            setError('蓝牙服务初始化失败，请检查插件配置')
          }
        } catch (error) {
          console.error('初始化蓝牙服务出错:', error)
          setStatus('蓝牙服务初始化错误')
          if (error instanceof Error) {
            setError(`初始化错误: ${error.message}`)
          } else {
            setError(`初始化错误: ${String(error)}`)
          }
        }
      }
    }

    checkEnvironment()
  }, [])

  /**
   * 发送单个蓝牙广播命令
   */
  const sendCommand = async () => {
    if (isLoading) return

    setIsLoading(true)
    setStatus('正在发送命令...')
    setError(null)

    try {
      // 如果蓝牙未初始化，尝试初始化
      if (!isInitialized && isNative) {
        setStatus('初始化蓝牙服务...')
        const initialized = await bluetoothService.initialize()
        setIsInitialized(initialized)

        if (!initialized) {
          setStatus('蓝牙服务初始化失败')
          return
        }

        // 检查蓝牙状态
        const enabled = await bluetoothService.checkBluetooth()
        setBluetoothEnabled(enabled)

        if (!enabled) {
          setStatus('蓝牙未启用')
          return
        }
      }

      if (isMultiMode) {
        // 发送多个命令
        if (selectedCommands.length === 0) {
          setStatus('未选择命令')
          return
        }

        setStatus(`正在按顺序发送 ${selectedCommands.length} 个命令...`)

        for (let i = 0; i < selectedCommands.length; i++) {
          const cmd = selectedCommands[i]
          console.log(`发送多命令中的第 ${i + 1}/${selectedCommands.length} 个: ${cmd}`)
          setStatus(`发送命令 ${i + 1}/${selectedCommands.length}: ${cmd}`)

          const result = await bluetoothService.broadcastCommand(cmd)

          if (!result) {
            setStatus(`命令 ${i + 1} 发送失败`)
            break
          }

          // 非最后一个命令时等待指定间隔
          if (i < selectedCommands.length - 1) {
            await new Promise(resolve => setTimeout(resolve, interval))
          }
        }

        setStatus('多命令发送完成')
      } else {
        // 发送单个广播命令
        console.log(`准备发送广播命令: ${selectedCommand}`)
        setStatus(`正在广播命令: ${selectedCommand}`)

        const result = await bluetoothService.broadcastCommand(selectedCommand)
        console.log('发送广播命令结果:', result)

        if (result) {
          setStatus('命令发送成功')
        } else {
          setStatus('命令发送失败')
          setError('命令发送失败，请检查蓝牙状态和插件配置')
        }
      }
    } catch (error) {
      console.error('发送命令出错:', error)
      // 显示详细错误信息
      if (error instanceof Error) {
        console.error('详细错误:', {
          name: error.name,
          message: error.message,
          stack: error.stack
        })
        setStatus(`发送命令错误: ${error.message}`)
        setError(`错误: ${error.message}\n堆栈: ${error.stack}`)
      } else {
        console.error('未知错误类型:', error)
        setStatus(`发送命令错误: ${String(error)}`)
        setError(`未知错误: ${String(error)}`)
      }
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 切换多命令预设
   */
  const selectPreset = (presetId: string) => {
    const preset = multiCommandPresets.find(p => p.id === presetId)
    if (preset) {
      setSelectedCommands(preset.commands)
    }
  }

  /**
   * 添加/移除命令到多命令列表
   */
  const toggleCommand = (command: string) => {
    setSelectedCommands(prev => {
      if (prev.includes(command)) {
        return prev.filter(c => c !== command)
      } else {
        return [...prev, command]
      }
    })
  }

  return (
    <div className="p-4 bg-gray-800 rounded-lg shadow-lg max-w-md mx-auto">
      <h2 className="text-xl font-bold text-white mb-4">蓝牙测试</h2>

      {/* 环境状态 */}
      <div className="mb-4 p-2 bg-gray-700 rounded">
        <div className="flex items-center mb-2">
          <div
            className={`w-3 h-3 rounded-full mr-2 ${isNative ? 'bg-green-500' : 'bg-yellow-500'}`}
          />
          <span className="text-gray-300 text-sm font-medium">
            {isNative ? '原生环境' : '浏览器环境'} - {platformInfo}
          </span>
        </div>

        <div className="flex items-center mb-2">
          <div
            className={`w-3 h-3 rounded-full mr-2 ${isInitialized ? 'bg-green-500' : 'bg-red-500'}`}
          />
          <span className="text-gray-300 text-sm font-medium">
            蓝牙服务: {isInitialized ? '已初始化' : '未初始化'}
          </span>
        </div>

        {isNative && (
          <div className="flex items-center mb-2">
            <div
              className={`w-3 h-3 rounded-full mr-2 ${
                bluetoothEnabled ? 'bg-green-500' : 'bg-red-500'
              }`}
            />
            <span className="text-gray-300 text-sm font-medium">
              蓝牙: {bluetoothEnabled ? '已启用' : '未启用'}
            </span>
          </div>
        )}

        <p className="text-gray-300 text-sm">{status}</p>

        {error && (
          <div className="mt-2 p-2 bg-red-900 rounded text-red-100 text-xs overflow-auto max-h-24">
            {error}
          </div>
        )}
      </div>

      {/* 测试模式切换 */}
      <div className="mb-4">
        <div className="flex">
          <button
            type="button"
            onClick={() => setIsMultiMode(false)}
            className={`flex-1 py-2 px-3 text-sm font-medium rounded-l ${
              !isMultiMode
                ? 'bg-pink-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            单命令模式
          </button>
          <button
            type="button"
            onClick={() => setIsMultiMode(true)}
            className={`flex-1 py-2 px-3 text-sm font-medium rounded-r ${
              isMultiMode ? 'bg-pink-600 text-white' : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            多命令模式
          </button>
        </div>
      </div>

      {isMultiMode ? (
        /* 多命令模式 */
        <>
          {/* 预设选择 */}
          <div className="mb-4">
            <span className="block text-gray-300 text-sm font-medium mb-2">选择预设组合</span>
            <div className="flex flex-wrap space-x-2">
              {multiCommandPresets.map(preset => (
                <button
                  type="button"
                  key={preset.id}
                  onClick={() => selectPreset(preset.id)}
                  className="px-3 py-1 text-xs bg-gray-700 hover:bg-gray-600 text-white rounded-full"
                >
                  {preset.name}
                </button>
              ))}
            </div>
          </div>

          {/* 命令间隔设置 */}
          <div className="mb-4">
            <label
              htmlFor="interval-range"
              className="block text-gray-300 text-sm font-medium mb-2"
            >
              命令间隔时间 (毫秒): {interval}ms
            </label>
            <input
              id="interval-range"
              type="range"
              min="50"
              max="2000"
              step="50"
              value={interval}
              onChange={e => setInterval(Number(e.target.value))}
              className="w-full"
            />
          </div>

          {/* 命令多选 */}
          <div className="mb-4">
            <span className="block text-gray-300 text-sm font-medium mb-2">
              选择要发送的命令 ({selectedCommands.length})
            </span>
            <div className="bg-gray-700 p-2 rounded max-h-40 overflow-y-auto">
              {testCommands.map(cmd => (
                <div key={cmd.id} className="flex items-center mb-2">
                  <input
                    type="checkbox"
                    id={`multi-${cmd.id}`}
                    className="mr-2"
                    checked={selectedCommands.includes(cmd.value)}
                    onChange={() => toggleCommand(cmd.value)}
                  />
                  <label
                    htmlFor={`multi-${cmd.id}`}
                    className="text-gray-300 text-sm cursor-pointer"
                  >
                    {cmd.name} - <span className="text-gray-400 text-xs">{cmd.value}</span>
                  </label>
                </div>
              ))}
            </div>
          </div>

          {/* 选中的命令序列预览 */}
          {selectedCommands.length > 0 && (
            <div className="mb-4 p-2 bg-gray-700 rounded">
              <h4 className="text-gray-300 text-sm font-medium mb-1">发送顺序:</h4>
              <ul className="list-decimal pl-5 text-gray-300 text-xs">
                {selectedCommands.map((cmd, index) => (
                  <li key={`cmd-${cmd}-${index}`}>
                    {cmd}{' '}
                    {index < selectedCommands.length - 1 ? (
                      <span className="text-gray-500">▶ {interval}ms</span>
                    ) : (
                      ''
                    )}
                  </li>
                ))}
              </ul>
            </div>
          )}
        </>
      ) : (
        /* 单命令模式 */
        <>
          {/* 命令选择 */}
          <div className="mb-4">
            <label
              htmlFor="command-select"
              className="block text-gray-300 text-sm font-medium mb-2"
            >
              选择命令
            </label>
            <select
              id="command-select"
              value={selectedCommand}
              onChange={e => setSelectedCommand(e.target.value)}
              className="w-full px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {testCommands.map(cmd => (
                <option key={cmd.id} value={cmd.value}>
                  {cmd.name}
                </option>
              ))}
            </select>
          </div>

          {/* 手动输入 */}
          <div className="mb-4">
            <label
              htmlFor="custom-command"
              className="block text-gray-300 text-sm font-medium mb-2"
            >
              自定义命令 (十六进制)
            </label>
            <div className="flex">
              <input
                id="custom-command"
                type="text"
                value={selectedCommand}
                onChange={e => setSelectedCommand(e.target.value)}
                className="flex-1 px-3 py-2 text-gray-700 border rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入十六进制命令"
              />
            </div>
            <p className="text-xs text-gray-400 mt-1">示例: 6db643ce97fe427ce49c6c</p>
          </div>
        </>
      )}

      {/* 发送按钮 */}
      <button
        type="button"
        onClick={sendCommand}
        disabled={isLoading}
        className={`w-full py-2 px-4 rounded-lg text-white font-medium ${
          isLoading ? 'bg-gray-500 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
        }`}
      >
        {isLoading ? '发送中...' : isMultiMode ? '发送多命令序列' : '发送命令'}
      </button>
    </div>
  )
}

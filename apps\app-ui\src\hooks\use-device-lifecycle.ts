import { useEffect } from 'react'
import { useDeviceStore } from '../stores/device-store'

/**
 * 设备生命周期管理Hook
 * 用于处理页面进入/退出时的设备状态管理
 */
export const useDeviceLifecycle = (functionType: 'chat' | 'script', enabled: boolean = true) => {
  const { connectedDevice, enterFunction, exitFunction } = useDeviceStore()

  useEffect(() => {
    if (!enabled || !connectedDevice) return

    console.log(`进入${functionType}功能，管理设备生命周期`)

    // 进入功能时，切换到对应的控制模式
    enterFunction(functionType)

    // 组件卸载时，退出功能模式
    return () => {
      console.log(`退出${functionType}功能，管理设备生命周期`)
      exitFunction(functionType)
    }
  }, [connectedDevice, functionType, enabled, enterFunction, exitFunction])

  return {
    hasConnectedDevice: !!connectedDevice
  }
}

import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react'
import { Button, Progress, Tooltip } from '@heroui/react'
import { Icon } from '@iconify/react'
import cx from 'classnames'
import { useTranslation } from 'react-i18next'
import { useVoiceRecorder } from '@/hooks/useVoiceRecorder'
import { useAudioPermission } from '@/hooks/useAudioPermission'
import { useSpeechToText } from '@/hooks/useSpeechToText'
import { AudioWaveform } from './AudioWaveform'
import { VoicePermissionGuide } from './VoicePermissionGuide'
import { RecordingControls } from './RecordingControls'

interface VoiceRecorderProps {
  /** 录制完成后的文本回调 */
  onTextResult: (text: string) => void
  /** 是否禁用录制 */
  disabled?: boolean
  /** 最大录制时长(秒) */
  maxDuration?: number
  /** 组件类名 */
  className?: string
}

/**
 * 语音录制器组件
 * 支持长按录制、点击录制、上滑取消等交互
 */
export function VoiceRecorder({
  onTextResult,
  disabled = false,
  maxDuration = 60,
  className
}: VoiceRecorderProps) {
  const { t } = useTranslation('voice')
  // 权限管理
  const { state: permissionStatus, requestPermission } = useAudioPermission()

  // 录制管理
  const voiceRecorderOptions = useMemo(() => ({ maxDuration }), [maxDuration])
  const {
    status: recordingStatus,
    duration,
    volume,
    audioBlob,
    startRecording,
    stopRecording,
    reset: resetRecording
  } = useVoiceRecorder(voiceRecorderOptions)

  // 语音转文本
  const {
    convert: convertToText,
    status: convertStatus,
    progress,
    error: convertError
  } = useSpeechToText()

  const isConverting = convertStatus === 'uploading' || convertStatus === 'processing'

  // 状态管理
  const [isLongPress, setIsLongPress] = useState(false)
  const [cancelGesture, setCancelGesture] = useState(false)
  const [showControls, setShowControls] = useState(false)

  // 交互管理
  const longPressTimer = useRef<ReturnType<typeof setTimeout> | null>(null)
  const touchStartY = useRef<number>(0)
  const buttonRef = useRef<HTMLButtonElement>(null)

  // 格式化录制时长显示
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 处理权限请求
  const handlePermissionRequest = useCallback(async () => {
    const granted = await requestPermission()
    if (!granted) {
      // 权限被拒绝，显示引导
    }
  }, [requestPermission])

  // 开始录制
  const handleStartRecording = useCallback(async () => {
    if (disabled || recordingStatus !== 'idle') return

    if (permissionStatus !== 'granted') {
      await handlePermissionRequest()
      return
    }

    try {
      await startRecording()
      setShowControls(false) // 录制时隐藏控制按钮
    } catch (error) {
      console.error('开始录制失败:', error)
    }
  }, [disabled, recordingStatus, permissionStatus, startRecording, handlePermissionRequest])

  // 停止录制
  const handleStopRecording = useCallback(() => {
    if (recordingStatus === 'recording') {
      stopRecording()
      setShowControls(true) // 录制完成后显示控制按钮
    }
  }, [recordingStatus, stopRecording])

  // 取消录制
  const handleCancelRecording = useCallback(() => {
    resetRecording()
    setShowControls(false)
    setCancelGesture(false)
  }, [resetRecording])

  // 发送录制内容
  const handleSendRecording = useCallback(async () => {
    if (!audioBlob) return

    try {
      const text = await convertToText(audioBlob)
      if (text) {
        onTextResult(text)
        resetRecording()
        setShowControls(false)
      }
    } catch (error) {
      console.error('语音转文本失败:', error)
    }
  }, [audioBlob, convertToText, onTextResult, resetRecording])

  // 重新录制
  const handleRetryRecording = useCallback(() => {
    resetRecording()
    setShowControls(false)
    // 自动开始新的录制
    setTimeout(() => {
      handleStartRecording()
    }, 100)
  }, [resetRecording, handleStartRecording])

  // 长按开始
  const handleTouchStart = useCallback(
    (e: React.TouchEvent) => {
      if (disabled) return

      const touch = e.touches[0]
      touchStartY.current = touch.clientY

      longPressTimer.current = setTimeout(() => {
        setIsLongPress(true)
        handleStartRecording()
        // 触觉反馈 (移动端)
        if ('vibrate' in navigator) {
          navigator.vibrate(50)
        }
      }, 200) // 200ms判定为长按
    },
    [disabled, handleStartRecording]
  )

  // 长按结束
  const handleTouchEnd = useCallback(
    (e: React.TouchEvent) => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
      }

      if (isLongPress) {
        if (cancelGesture) {
          handleCancelRecording()
        } else {
          handleStopRecording()
        }
        setIsLongPress(false)
      }

      setCancelGesture(false)
    },
    [isLongPress, cancelGesture, handleCancelRecording, handleStopRecording]
  )

  // 长按移动 - 检测上滑取消手势
  const handleTouchMove = useCallback(
    (e: React.TouchEvent) => {
      if (!isLongPress) return

      const touch = e.touches[0]
      const deltaY = touchStartY.current - touch.clientY

      // 上滑超过50px判定为取消手势
      if (deltaY > 50) {
        setCancelGesture(true)
      } else {
        setCancelGesture(false)
      }
    },
    [isLongPress]
  )

  // 鼠标事件 (桌面端)
  const handleMouseDown = useCallback(() => {
    if (disabled) return

    longPressTimer.current = setTimeout(() => {
      setIsLongPress(true)
      handleStartRecording()
    }, 200)
  }, [disabled, handleStartRecording])

  const handleMouseUp = useCallback(() => {
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
    }

    if (isLongPress) {
      handleStopRecording()
      setIsLongPress(false)
    }
  }, [isLongPress, handleStopRecording])

  // 点击事件 (短按)
  const handleClick = useCallback(() => {
    if (isLongPress || disabled) return

    if (recordingStatus === 'idle') {
      handleStartRecording()
    } else if (recordingStatus === 'recording') {
      handleStopRecording()
    }
  }, [isLongPress, disabled, recordingStatus, handleStartRecording, handleStopRecording])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
      }
    }
  }, [])

  // 权限被拒绝时显示引导
  if (permissionStatus === 'denied') {
    return (
      <div className={cx('flex items-center justify-center', className)}>
        <VoicePermissionGuide onRetry={handlePermissionRequest} />
      </div>
    )
  }

  // 主录制按钮
  const renderRecordingButton = () => {
    const isRecording = recordingStatus === 'recording'
    const isProcessing = recordingStatus === 'processing' || isConverting

    return (
      <div className="relative flex flex-col items-center">
        {/* 主录制按钮 */}
        <Tooltip
          content={
            isRecording
              ? t('recorder.tooltip.recording')
              : isProcessing
              ? t('recorder.tooltip.processing')
              : t('recorder.tooltip.idle')
          }
          showArrow
        >
          <Button
            ref={buttonRef}
            isIconOnly
            size="lg"
            radius="full"
            variant={isRecording ? 'solid' : 'flat'}
            color={isRecording ? 'danger' : 'primary'}
            isDisabled={disabled || isProcessing}
            className={cx(
              'w-16 h-16 transition-all duration-200',
              isRecording && 'animate-pulse scale-110',
              cancelGesture && 'bg-warning scale-95',
              'hover:scale-105 active:scale-95'
            )}
            onClick={handleClick}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onTouchMove={handleTouchMove}
            onMouseDown={handleMouseDown}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {isProcessing ? (
              <Icon icon="svg-spinners:3-dots-bounce" className="text-2xl" />
            ) : (
              <Icon
                icon={isRecording ? 'solar:stop-bold' : 'solar:microphone-bold'}
                className={cx('text-2xl transition-transform', isRecording && 'text-white')}
              />
            )}
          </Button>
        </Tooltip>

        {/* 录制状态提示 */}
        {isRecording && (
          <div className="mt-2 flex flex-col items-center space-y-1">
            <div className="text-sm font-medium text-danger">{formatDuration(duration)}</div>
            <div className="text-xs text-default-500">
              {cancelGesture ? t('recorder.status.swipe_cancel') : t('recorder.status.recording')}
            </div>
          </div>
        )}

        {/* 转换进度 */}
        {isConverting && (
          <div className="mt-2 w-24">
            <Progress value={progress} size="sm" color="primary" showValueLabel />
            <div className="text-xs text-center text-default-500 mt-1">{t('recorder.status.converting')}</div>
          </div>
        )}

        {/* 音频波形 */}
        {isRecording && (
          <div className="mt-3">
            <AudioWaveform
              volume={volume}
              isActive={isRecording}
              color={cancelGesture ? 'warning' : 'danger'}
            />
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={cx('flex flex-col items-center space-y-4', className)}>
      {/* 录制按钮区域 */}
      {renderRecordingButton()}

      {/* 录制控制面板 */}
      {showControls && audioBlob && (
        <RecordingControls
          audioBlob={audioBlob}
          duration={duration}
          onSend={handleSendRecording}
          onRetry={handleRetryRecording}
          onCancel={handleCancelRecording}
          isConverting={isConverting}
          convertError={convertError}
        />
      )}

      {/* 使用提示 */}
      {recordingStatus === 'idle' && !showControls && (
        <div className="text-center space-y-1">
          <div className="text-sm text-default-600">{t('recorder.tips.click_record')}</div>
          <div className="text-xs text-default-400">{t('recorder.tips.max_duration', { duration: formatDuration(maxDuration) })}</div>
        </div>
      )}
    </div>
  )
}

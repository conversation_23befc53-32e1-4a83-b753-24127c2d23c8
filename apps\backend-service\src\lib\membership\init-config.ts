import type { Env } from '@/types/env';
import { createPointsConfigManager } from './config';

/**
 * 初始化积分配置数据
 * 在首次部署或重置时使用
 */

export async function initializePointsConfig(env: Env): Promise<void> {
  const configManager = createPointsConfigManager(env);
  const defaultConfig = configManager.getDefaultConfig();

  console.log('开始初始化积分配置...');

  // 初始化积分配置
  for (const [feature, cost] of Object.entries(defaultConfig.points)) {
    try {
      await configManager.updatePointsCost(feature, cost);
      console.log(`✅ ${feature}: ${cost}积分`);
    } catch (error) {
      console.error(`❌ ${feature} 初始化失败:`, error);
    }
  }

  // 初始化会员等级配置
  for (const [level, config] of Object.entries(defaultConfig.membership)) {
    try {
      await configManager.updateMembershipConfig(level, config);
      console.log(`✅ ${level}会员等级配置`);
    } catch (error) {
      console.error(`❌ ${level}会员等级配置初始化失败:`, error);
    }
  }

  console.log('积分配置初始化完成！');
}

/**
 * 检查并补充缺失的配置
 */
export async function ensureConfigExists(env: Env): Promise<void> {
  const configManager = createPointsConfigManager(env);
  const defaultConfig = configManager.getDefaultConfig();

  // 检查积分配置
  for (const [feature] of Object.entries(defaultConfig.points)) {
    try {
      const cost = await configManager.getPointsCost(feature);
      if (cost === 0) {
        // 配置不存在或为0，使用默认值
        const defaultCost = defaultConfig.points[feature as keyof typeof defaultConfig.points];
        await configManager.updatePointsCost(feature, defaultCost);
        console.log(`🔧 补充缺失配置 ${feature}: ${defaultCost}积分`);
      }
    } catch (error) {
      console.error(`检查配置失败 ${feature}:`, error);
    }
  }
}

/**
 * 获取配置状态报告
 */
export async function getConfigReport(env: Env): Promise<{
  points: Record<string, { current: number; default: number; isDefault: boolean }>;
  summary: { total: number; usingDefault: number; customized: number };
}> {
  const configManager = createPointsConfigManager(env);
  const defaultConfig = configManager.getDefaultConfig();
  const currentConfig = await configManager.getAllPointsCosts();

  const points: Record<string, { current: number; default: number; isDefault: boolean }> = {};
  let usingDefault = 0;
  let customized = 0;

  for (const [feature, defaultCost] of Object.entries(defaultConfig.points)) {
    const currentCost = currentConfig[feature] || 0;
    const isDefault = currentCost === defaultCost;

    points[feature] = {
      current: currentCost,
      default: defaultCost,
      isDefault,
    };

    if (isDefault) {
      usingDefault++;
    } else {
      customized++;
    }
  }

  return {
    points,
    summary: {
      total: Object.keys(defaultConfig.points).length,
      usingDefault,
      customized,
    },
  };
}

import { useState, useEffect, useRef } from 'react'
import { generateUUID } from '@/lib/utils'
import { apiService } from '@/api'
import type { Message } from '@/api/services'
import { messageConverter } from '@/lib/chat-database/message-converter'
import type { ChatInitializationOptions, ChatInitializationResult } from '@/lib/chat-database/types'

/**
 * 聊天初始化Hook - 实现本地优先的消息加载策略
 */
export function useChatInitialization(
  options: ChatInitializationOptions
): ChatInitializationResult {
  const { chatId, roleId, chatDatabase } = options

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [messages, setMessages] = useState<Message[]>([])
  const [actualChatId, setActualChatId] = useState<string>(chatId || '')

  // 防止重复加载
  const isLoadingRef = useRef(false)

  useEffect(() => {
    loadChatMessages()
  }, [chatId, roleId])

  const loadChatMessages = async () => {
    if (isLoadingRef.current) return

    try {
      isLoadingRef.current = true
      setIsLoading(true)
      setError(null)

      // 场景1: 新聊天（没有chatId）
      if (!chatId) {
        const newChatId = generateUUID()
        console.log('🆕 创建新聊天:', newChatId)

        setActualChatId(newChatId)
        setMessages([])
        setIsLoading(false)
        return
      }

      console.log('🔍 初始化聊天:', { chatId, roleId })

      // 场景2: 现有聊天 - 本地优先策略
      const result = await loadWithLocalFirstStrategy(chatId, roleId)

      setMessages(result.messages)
      setActualChatId(chatId)
    } catch (error) {
      console.error('初始化聊天失败:', error)
      setError('加载聊天失败，请稍后重试')
    } finally {
      isLoadingRef.current = false
      setIsLoading(false)
    }
  }

  /**
   * 本地优先加载策略 - 真正非阻塞版本
   */
  const loadWithLocalFirstStrategy = async (chatId: string, roleId: string) => {
    let messages: Message[] = []
    let hasLocalData = false

    // 步骤1: 尝试从本地数据库加载（优先级最高，立即返回）
    if (chatDatabase) {
      try {
        // 🚀 先清理可能存在的重复空消息
        try {
          const cleanedCount = await chatDatabase.cleanupDuplicateEmptyAssistantMessages(chatId)
          if (cleanedCount > 0) {
            console.log(`🧹 [ChatInit] 清理了 ${cleanedCount} 条重复空消息`)
          }
        } catch (error) {
          console.warn('⚠️ [ChatInit] 清理重复消息失败，继续加载:', error)
        }

        const localMessages = await chatDatabase.getMessagesByChat(chatId, 1000) // 🔧 修复：增加限制到1000条，避免消息丢失
        console.log('📦 本地数据库找到消息:', localMessages.length)

        if (localMessages.length > 0) {
          // 获取消息对应的附件并转换
          const messagesWithAttachments = await Promise.all(
            localMessages.map(async msg => {
              const attachments = await chatDatabase.getAttachmentsByMessage(msg.id)
              return { message: msg, attachments }
            })
          )
          messages = messageConverter.batchToLangChain(messagesWithAttachments)
          hasLocalData = true

          // 检查并恢复流式状态
          await handleStreamingRecovery(localMessages, chatId)

          console.log('⚡ 本地数据立即可用，开始后台同步')

          // 🚀 关键优化：如果有本地数据，立即返回，API调用放到后台
          performBackgroundSync(chatId, roleId, messages).catch(error => {
            console.warn('后台同步失败:', error)
          })

          return { messages }
        }
      } catch (error) {
        console.warn('从本地数据库加载失败:', error)
      }
    }

    // 步骤2: 如果没有本地数据，才同步调用API
    console.log('📡 无本地数据，从服务器获取')
    try {
      const serverResponse = await apiService.chatV2.getMessages(chatId)
      const serverMessages = serverResponse.messages || []

      if (serverMessages.length > 0) {
        const convertedServerMessages = convertApiMessagesToLangChain(serverMessages)
        messages = convertedServerMessages

        console.log('🌐 从服务器完整加载并存储到本地')
        if (chatDatabase && roleId) {
          await storeMessagesToLocal(chatId, roleId, messages)
        }
      } else {
        // 服务器和本地都没有数据 - 空聊天
        console.log('📝 空聊天 - 延迟创建会话记录，等待用户发送消息')
        // 🚀 修复：不要立即创建空会话，等用户发送第一条消息时再创建
        // 这样可以避免在聊天历史中显示空的会话记录
        messages = []
      }
    } catch (error) {
      console.error('服务器加载失败:', error)
      throw error
    }

    return { messages }
  }

  /**
   * 后台同步函数 - 不阻塞主流程
   */
  const performBackgroundSync = async (
    chatId: string,
    roleId: string,
    localMessages: Message[]
  ) => {
    try {
      console.log('🔄 开始后台增量同步')

      const serverResponse = await apiService.chatV2.getMessages(chatId)
      const serverMessages = serverResponse.messages || []

      if (serverMessages.length > 0) {
        const convertedServerMessages = convertApiMessagesToLangChain(serverMessages)

        // 增量同步 - 合并本地和服务器数据
        const mergedMessages = await mergeLocalAndServerMessages(
          localMessages,
          convertedServerMessages,
          chatId
        )

        // 检查是否有更新，如果有则通知UI
        if (
          mergedMessages.length !== localMessages.length ||
          JSON.stringify(mergedMessages) !== JSON.stringify(localMessages)
        ) {
          console.log('📲 后台同步发现更新，刷新消息')

          // 更新状态（这会触发UI重新渲染）
          setMessages(mergedMessages)
        } else {
          console.log('✅ 后台同步完成，无更新')
        }
      }
    } catch (error) {
      console.warn('⚠️ 后台同步失败，不影响用户体验:', error)
    }
  }

  /**
   * 处理流式状态恢复
   */
  const handleStreamingRecovery = async (localMessages: any[], chatId: string) => {
    if (!chatDatabase) return

    const streamingMessages = localMessages.filter(msg => msg.isStreaming)

    if (streamingMessages.length > 0) {
      console.log('🔄 发现流式中断消息:', streamingMessages.length)

      // 清理流式状态 - 标记为完成或失败
      for (const msg of streamingMessages) {
        try {
          await chatDatabase.updateMessage(msg.id, {
            isStreaming: false,
            content: msg.content || '[回复中断]'
          })
        } catch (error) {
          console.error('清理流式状态失败:', error)
        }
      }
    }
  }

  /**
   * 增量合并本地和服务器消息
   */
  const mergeLocalAndServerMessages = async (
    localMessages: Message[],
    serverMessages: Message[],
    chatId: string
  ): Promise<Message[]> => {
    // 创建消息映射表
    const localMsgMap = new Map(localMessages.map(msg => [msg.id, msg]))
    const serverMsgMap = new Map(serverMessages.map(msg => [msg.id, msg]))

    // 合并策略：服务器数据优先，补充本地独有的消息
    const mergedMessages = [...serverMessages]

    // 添加本地独有的消息（比如用户刚发送但还没同步的）
    for (const localMsg of localMessages) {
      if (!serverMsgMap.has(localMsg.id)) {
        console.log('📝 保留本地独有消息:', localMsg.id)
        mergedMessages.push(localMsg)
      }
    }

    // 按时间排序
    mergedMessages.sort(
      (a, b) => new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime()
    )

    // 更新本地数据库（如果有需要）
    if (chatDatabase) {
      try {
        await updateLocalMessagesFromServer(chatId, mergedMessages)
      } catch (error) {
        console.warn('更新本地数据库失败:', error)
      }
    }

    return mergedMessages
  }

  /**
   * 存储消息到本地数据库
   */
  const storeMessagesToLocal = async (chatId: string, roleId: string, messages: Message[]) => {
    if (!chatDatabase) return

    try {
      // 创建或更新会话
      const existingSession = await chatDatabase.getSession(chatId)
      if (!existingSession) {
        await chatDatabase.createSession({
          id: chatId,
          roleId,
          title: extractChatTitle(messages),
          messageCount: messages.length,
          lastMessageAt: new Date().toISOString()
        })
      } else {
        await chatDatabase.updateSession(chatId, {
          title: extractChatTitle(messages),
          messageCount: messages.length,
          lastMessageAt: new Date().toISOString()
        })
      }

      // 存储消息
      const dbMessages = messageConverter.batchFromLangChain(messages, chatId)

      for (const messageData of dbMessages) {
        const existingMessage = await chatDatabase.getMessage(messageData.message.id)
        if (!existingMessage) {
          await chatDatabase.createMessage(messageData.message)
        }

        // 处理附件
        for (const attachment of messageData.attachments) {
          const attachmentWithMessageId = { ...attachment, messageId: messageData.message.id }
          const existingAttachment = await chatDatabase.getAttachment(attachment.id)
          if (!existingAttachment) {
            await chatDatabase.createAttachment(attachmentWithMessageId)
          }
        }
      }

      console.log('💾 成功存储到本地数据库:', messages.length, '条消息')
    } catch (error) {
      console.error('存储到本地数据库失败:', error)
    }
  }

  /**
   * 更新本地消息从服务器数据
   */
  const updateLocalMessagesFromServer = async (chatId: string, mergedMessages: Message[]) => {
    if (!chatDatabase) return

    const dbMessages = messageConverter.batchFromLangChain(mergedMessages, chatId)

    for (const messageData of dbMessages) {
      const existingMessage = await chatDatabase.getMessage(messageData.message.id)
      if (!existingMessage) {
        await chatDatabase.createMessage(messageData.message)
      } else {
        await chatDatabase.updateMessage(messageData.message.id, messageData.message)
      }

      // 处理附件
      for (const attachment of messageData.attachments) {
        const attachmentWithMessageId = { ...attachment, messageId: messageData.message.id }
        const existingAttachment = await chatDatabase.getAttachment(attachment.id)
        if (!existingAttachment) {
          await chatDatabase.createAttachment(attachmentWithMessageId)
        } else {
          await chatDatabase.updateAttachment(attachment.id, attachmentWithMessageId)
        }
      }
    }
  }

  /**
   * 转换API消息格式到LangChain格式
   */
  const convertApiMessagesToLangChain = (messages: Message[]): Message[] => {
    return messages.map(message => {
      const content = message.parts
        .filter(part => part.type === 'text' && part.text)
        .map(part => part.text)
        .join('')

      return {
        id: message.id,
        role: message.role,
        content: content || '',
        parts: message.parts
          .filter(part => part.type === 'text')
          .map(part => {
            if (part.type === 'text' && part.text) {
              return { type: 'text' as const, text: part.text }
            }
            return { type: 'text' as const, text: JSON.stringify(part) }
          }),
        attachments: message.attachments,
        createdAt: new Date(message.createdAt)
      }
    })
  }

  /**
   * 提取聊天标题
   */
  const extractChatTitle = (messages: Message[]): string => {
    const firstUserMessage = messages.find(msg => msg.role === 'user')
    if (firstUserMessage?.content) {
      return firstUserMessage.content.slice(0, 50)
    }
    return '新对话'
  }

  return {
    isLoading,
    error,
    messages,
    chatId: actualChatId,
    refresh: loadChatMessages
  }
}

package com.pleasurehub.app.security;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.os.Debug;
import android.util.Log;
import java.io.File;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import com.pleasurehub.app.BuildConfig;

/**
 * 应用安全管理器
 * 提供反调试、签名验证、完整性检查等安全功能
 */
public class SecurityManager {
    
    private static final String TAG = "SecurityManager";
    private static final boolean DEBUG = BuildConfig.ENABLE_SECURITY_LOG; // 跟随构建配置
    
    // 预期的应用签名 SHA256 指纹（需要替换为实际签名）
    private static final String EXPECTED_SIGNATURE = "YOUR_APP_SIGNATURE_SHA256_HERE";
    
    private final Context context;
    
    public SecurityManager(Context context) {
        this.context = context;
    }
    
    /**
     * 执行完整的安全检查
     * @return true 如果所有检查都通过
     */
    public boolean performSecurityCheck() {
        try {
            // 在调试模式下，降低安全检查的严格程度
            if (BuildConfig.DEBUG) {
                if (DEBUG) Log.d(TAG, "调试模式：使用宽松的安全检查");
                
                // 仅在调试模式下执行基本检查
                if (isEmulatorDetected()) {
                    if (DEBUG) Log.w(TAG, "检测到模拟器环境");
                    // 在调试模式下，模拟器是允许的
                }
                
                if (isRootDetected()) {
                    if (DEBUG) Log.w(TAG, "检测到Root环境");
                    // 在调试模式下，Root设备是允许的
                }
                
                return true; // 调试模式下总是返回 true
            }
            
            // 生产环境的完整安全检查
            // 1. 反调试检查
            if (isDebuggingDetected()) {
                if (DEBUG) Log.w(TAG, "调试检测失败");
                return false;
            }
            
            // 2. 模拟器检查
            if (isEmulatorDetected()) {
                if (DEBUG) Log.w(TAG, "模拟器检测失败");
                return false;
            }
            
            // 3. Root 检查
            if (isRootDetected()) {
                if (DEBUG) Log.w(TAG, "Root 检测失败");
                return false;
            }
            
            // 4. 签名验证
            if (!verifyAppSignature()) {
                if (DEBUG) Log.w(TAG, "签名验证失败");
                return false;
            }
            
            // 5. 安装来源检查
            if (!verifyInstallSource()) {
                if (DEBUG) Log.w(TAG, "安装来源验证失败");
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            if (DEBUG) Log.e(TAG, "安全检查异常", e);
            return false;
        }
    }
    
    /**
     * 反调试检查
     */
    private boolean isDebuggingDetected() {
        // 检查调试器连接
        if (Debug.isDebuggerConnected()) {
            return true;
        }
        
        // 检查应用是否可调试
        ApplicationInfo appInfo = context.getApplicationInfo();
        if ((appInfo.flags & ApplicationInfo.FLAG_DEBUGGABLE) != 0) {
            return true;
        }
        
        // 检查调试相关的系统属性
        try {
            String debugProp = System.getProperty("ro.debuggable");
            if ("1".equals(debugProp)) {
                return true;
            }
        } catch (Exception e) {
            // 忽略异常
        }
        
        return false;
    }
    
    /**
     * 模拟器检查
     */
    private boolean isEmulatorDetected() {
        // 检查常见的模拟器特征
        String[] emulatorSigns = {
            "generic", "unknown", "emulator", "sdk_gphone", "google_sdk",
            "Genymotion", "Andy", "nox", "ttVM_Hdragon", "TiantianVM"
        };
        
        String brand = android.os.Build.BRAND.toLowerCase();
        String device = android.os.Build.DEVICE.toLowerCase();
        String model = android.os.Build.MODEL.toLowerCase();
        String product = android.os.Build.PRODUCT.toLowerCase();
        
        for (String sign : emulatorSigns) {
            if (brand.contains(sign) || device.contains(sign) || 
                model.contains(sign) || product.contains(sign)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Root 检查
     */
    private boolean isRootDetected() {
        // 检查常见的 Root 文件
        String[] rootPaths = {
            "/system/app/Superuser.apk",
            "/sbin/su",
            "/system/bin/su",
            "/system/xbin/su",
            "/data/local/xbin/su",
            "/data/local/bin/su",
            "/system/sd/xbin/su",
            "/system/bin/failsafe/su",
            "/data/local/su",
            "/su/bin/su"
        };
        
        for (String path : rootPaths) {
            if (new File(path).exists()) {
                return true;
            }
        }
        
        // 检查 Root 管理应用
        String[] rootApps = {
            "com.noshufou.android.su",
            "com.noshufou.android.su.elite",
            "eu.chainfire.supersu",
            "com.koushikdutta.superuser",
            "com.thirdparty.superuser",
            "com.yellowes.su",
            "com.topjohnwu.magisk"
        };
        
        PackageManager pm = context.getPackageManager();
        for (String packageName : rootApps) {
            try {
                pm.getPackageInfo(packageName, 0);
                return true;
            } catch (PackageManager.NameNotFoundException e) {
                // 包不存在，继续检查
            }
        }
        
        return false;
    }
    
    /**
     * 应用签名验证
     */
    private boolean verifyAppSignature() {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            if (packageInfo.signatures == null || packageInfo.signatures.length == 0) {
                return false;
            }
            
            // 计算签名的 SHA256 指纹
            Signature signature = packageInfo.signatures[0];
            MessageDigest md = MessageDigest.getInstance("SHA256");
            md.update(signature.toByteArray());
            byte[] digest = md.digest();
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            String currentSignature = hexString.toString().toUpperCase();
            
            // 在开发阶段，记录签名用于配置
            if (DEBUG) {
                Log.d(TAG, "当前应用签名 SHA256: " + currentSignature);
            }
            
            // 如果是默认的占位符值，说明还没有配置真实签名，在开发阶段返回 true
            if ("YOUR_APP_SIGNATURE_SHA256_HERE".equals(EXPECTED_SIGNATURE)) {
                if (DEBUG) Log.d(TAG, "使用默认签名配置，跳过验证");
                return true;
            }
            
            // 生产环境中验证签名
            boolean isValid = currentSignature.equals(EXPECTED_SIGNATURE);
            if (!isValid && DEBUG) {
                Log.w(TAG, "签名不匹配。期望: " + EXPECTED_SIGNATURE + ", 实际: " + currentSignature);
            }
            
            return isValid;
            
        } catch (Exception e) {
            if (DEBUG) Log.e(TAG, "签名验证异常", e);
            return false;
        }
    }
    
    /**
     * 安装来源验证
     */
    private boolean verifyInstallSource() {
        try {
            String installer = context.getPackageManager()
                .getInstallerPackageName(context.getPackageName());
            
            if (DEBUG) {
                Log.d(TAG, "安装来源: " + (installer != null ? installer : "直接安装"));
            }
            
            // 允许的安装来源
            List<String> allowedInstallers = new ArrayList<>();
            allowedInstallers.add("com.android.vending"); // Google Play Store
            allowedInstallers.add("com.huawei.appmarket"); // 华为应用市场
            allowedInstallers.add("com.xiaomi.mipicks"); // 小米应用商店
            allowedInstallers.add("com.oppo.market"); // OPPO 软件商店
            allowedInstallers.add("com.vivo.appstore"); // vivo 应用商店
            allowedInstallers.add("com.sec.android.app.samsungapps"); // 三星应用商店
            allowedInstallers.add("com.android.packageinstaller"); // 系统安装器
            allowedInstallers.add("com.google.android.packageinstaller"); // Google 包安装器
            allowedInstallers.add(null); // 直接安装（开发调试时）
            
            boolean isAllowed = allowedInstallers.contains(installer);
            
            if (!isAllowed && DEBUG) {
                Log.w(TAG, "不受信任的安装来源: " + installer);
            }
            
            return isAllowed;
            
        } catch (Exception e) {
            if (DEBUG) Log.e(TAG, "安装来源验证异常", e);
            return false;
        }
    }
    
    /**
     * 获取应用签名 SHA256（用于开发时获取签名）
     */
    public String getAppSignatureSHA256() {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            if (packageInfo.signatures != null && packageInfo.signatures.length > 0) {
                Signature signature = packageInfo.signatures[0];
                MessageDigest md = MessageDigest.getInstance("SHA256");
                md.update(signature.toByteArray());
                byte[] digest = md.digest();
                
                StringBuilder hexString = new StringBuilder();
                for (byte b : digest) {
                    String hex = Integer.toHexString(0xff & b);
                    if (hex.length() == 1) {
                        hexString.append('0');
                    }
                    hexString.append(hex);
                }
                
                return hexString.toString().toUpperCase();
            }
        } catch (Exception e) {
            if (DEBUG) Log.e(TAG, "获取签名异常", e);
        }
        
        return null;
    }
} 
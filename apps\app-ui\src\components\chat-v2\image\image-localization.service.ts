import { ImageStorage } from '@/lib/media/image-storage'
import { getGlobalChatDatabase } from '@/lib/chat-database'

/**
 * 图片本地化服务
 * 职责：处理图片的本地存储、数据库操作、URL管理
 */
export class ImageLocalizationService {
  private static instance: ImageLocalizationService

  static getInstance(): ImageLocalizationService {
    if (!ImageLocalizationService.instance) {
      ImageLocalizationService.instance = new ImageLocalizationService()
    }
    return ImageLocalizationService.instance
  }

  private constructor() {}

  /**
   * 检查本地图片文件
   */
  async checkLocalImage(messageId: string): Promise<string | null> {
    try {
      // 优先检查数据库中的附件记录
      const chatDatabase = getGlobalChatDatabase()
      if (chatDatabase) {
        try {
          const attachments = await chatDatabase.getAttachmentsByMessage(messageId)
          const imageAttachment = attachments.find(
            attachment =>
              attachment.type === 'image' &&
              attachment.status === 'completed' &&
              attachment.localPath
          )

          if (imageAttachment && imageAttachment.localPath) {
            // 检查本地文件是否仍然存在
            const imageStorage = ImageStorage.getInstance()
            const fileExists = await imageStorage.fileExists(imageAttachment.localPath)

            if (fileExists) {
              // 获取可用的URL
              const localImageUrl = await imageStorage.getImageUrl(imageAttachment.localPath)
              if (localImageUrl) {
                console.log('🖼️ [LOCAL-DB] 从数据库找到本地图片:', localImageUrl)
                return localImageUrl
              }
            } else {
              console.warn('⚠️ [LOCAL-DB] 数据库记录的图片文件不存在，将清理记录')
              // 清理失效的数据库记录
              await chatDatabase.deleteAttachment(imageAttachment.id)
            }
          }
        } catch (dbError) {
          console.warn('⚠️ [LOCAL-DB] 数据库查询失败:', dbError)
        }
      }

      // 回退到文件系统查找
      const imageStorage = ImageStorage.getInstance()
      const localPath = await imageStorage.findImageByMessageId(messageId)
      if (localPath) {
        const localImageUrl = await imageStorage.getImageUrl(localPath)
        if (localImageUrl) {
          console.log('🖼️ [LOCAL-FS] 从文件系统找到本地图片:', localImageUrl)
          return localImageUrl
        }
      }
    } catch (error) {
      console.warn('⚠️ [LOCAL] 检查本地图片失败:', error)
    }
    return null
  }

  /**
   * 处理已有图片的本地化（后台异步进行）
   */
  async localizeExistingImage(remoteImageUrl: string, messageId: string): Promise<string | null> {
    try {
      console.log('💾 [LOCALIZE] 开始本地化已有图片:', remoteImageUrl)

      const imageStorage = ImageStorage.getInstance()

      // 检查是否已经有本地文件
      const existingLocalUrl = await this.checkLocalImage(messageId)
      if (existingLocalUrl) {
        console.log('💾 [LOCALIZE] 图片已存在本地，跳过下载')
        return existingLocalUrl
      }

      // 后台下载并保存
      const savedPath = await imageStorage.saveImageFromUrl(
        remoteImageUrl,
        messageId,
        `existing_${messageId}`
      )

      if (savedPath) {
        console.log('💾 [LOCALIZE] 已有图片本地化成功:', savedPath)

        // 保存到数据库附件表
        await this.saveAttachmentToDatabase(
          messageId,
          savedPath,
          remoteImageUrl,
          'localized_existing'
        )

        // 获取本地URL
        const localImageUrl = await imageStorage.getImageUrl(savedPath)
        if (localImageUrl) {
          console.log('💾 [LOCALIZE] 切换到本地图片显示:', localImageUrl)
          return localImageUrl
        }
      }
    } catch (error) {
      console.warn('⚠️ [LOCALIZE] 图片本地化失败:', error)
    }
    return null
  }

  /**
   * 保存生成的图片到本地和数据库
   */
  async localizeGeneratedImage(
    remoteImageUrl: string,
    messageId: string,
    prompt?: string
  ): Promise<string | null> {
    try {
      const imageStorage = ImageStorage.getInstance()

      // 下载并保存图片
      const savedPath = await imageStorage.saveImageFromUrl(
        remoteImageUrl,
        messageId,
        `image_${messageId}`
      )

      if (savedPath) {
        console.log('💾 [GENERATED] 图片已保存到本地:', savedPath)

        // 保存到数据库附件表
        await this.saveAttachmentToDatabase(
          messageId,
          savedPath,
          remoteImageUrl,
          'generated',
          prompt
        )

        // 获取本地URL
        const localImageUrl = await imageStorage.getImageUrl(savedPath)
        return localImageUrl || remoteImageUrl
      }
    } catch (error) {
      console.warn('⚠️ [GENERATED] 本地存储失败:', error)
    }
    return remoteImageUrl
  }

  /**
   * 保存附件信息到数据库
   */
  private async saveAttachmentToDatabase(
    messageId: string,
    savedPath: string,
    originalUrl: string,
    source: string,
    prompt?: string
  ): Promise<void> {
    const chatDatabase = getGlobalChatDatabase()
    if (!chatDatabase) return

    try {
      // 检查是否已有attachment记录
      const existingAttachments = await chatDatabase.getAttachmentsByMessage(messageId)
      const existingImageAttachment = existingAttachments.find(att => att.type === 'image')

      if (existingImageAttachment) {
        // 更新现有记录的本地路径
        await chatDatabase.updateAttachment(existingImageAttachment.id, {
          localPath: savedPath,
          status: 'completed',
          originalUrl: originalUrl
        })
        console.log('💾 [DB] 更新数据库attachment记录:', existingImageAttachment.id)
      } else {
        // 创建新的attachment记录
        const attachmentId = `image_${messageId}_${Date.now()}`

        await chatDatabase.createAttachment({
          id: attachmentId,
          messageId,
          type: 'image',
          name: savedPath.split('/').pop() || 'image.png',
          contentType: 'image/png',
          originalUrl: originalUrl,
          localPath: savedPath,
          status: 'completed',
          metadata: JSON.stringify({
            source,
            prompt: prompt?.substring(0, 100),
            width: 1024,
            height: 1440,
            timestamp: Date.now()
          })
        })
        console.log('💾 [DB] 创建新数据库attachment记录:', attachmentId)
      }
    } catch (dbError) {
      console.warn('⚠️ [DB] 数据库保存失败，但本地文件已保存:', dbError)
    }
  }
}

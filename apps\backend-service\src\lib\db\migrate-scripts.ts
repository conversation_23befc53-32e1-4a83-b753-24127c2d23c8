import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { script } from './schema';
import { TimeUtils, ScriptValidator, type ScriptContent, type ScriptStage } from '@/types/script';
import 'dotenv/config';

// 导入现有的JSON数据
import homestayData from '@/lib/scripts/homestay.json';
import dialogueWithMultiIntensityData from '@/lib/scripts/dialogue_with_multi_intensity.json';
import airportEncounterData from '@/lib/scripts/airport_encounter.json';

// 数据库连接
const connectionString = process.env.DATABASE_URL!;
const client = postgres(connectionString);
const db = drizzle(client);

// 剧本映射配置
interface ScriptMapping {
  id: string;
  title: string;
  description: string;
  coverImage: string;
  category: string;
  tags: string[];
  audioUrl: string;
  stages: any[];
  isPremium?: boolean;
}

const scriptMappings: ScriptMapping[] = [
  {
    id: 'homestay',
    title: '民宿激情',
    description: '一场在民宿里的温馨浪漫邂逅，从轻柔到激情的完美体验',
    coverImage:
      'https://khp5oj9p6kyyvhff.public.blob.vercel-storage.com/play/homestay/1-QHJ2jqnaNk6umojRUGyXWGFfqRESiu.jpeg',
    category: '浪漫',
    tags: ['浪漫', '室内', '民宿'],
    audioUrl:
      'https://asset.pleasurehub.app/play/homestay/homestay-CsX18dwmgCVVtxOH0wxfarWtmtnGRT.MP3',
    stages: homestayData,
    isPremium: false,
  },
  {
    id: 'professor',
    title: '实验室的抚慰',
    description:
      '无休止的教学工作让我疲惫不堪，幸好此时我的助理走近了实验室，用她的温柔抚慰了我的所有...',
    coverImage:
      'https://khp5oj9p6kyyvhff.public.blob.vercel-storage.com/play/professor_13-ic1kc2ENmAyzGhb8o5Knn6AoWvPttd.png',
    category: '职场',
    tags: ['课堂', '师生', '知识'],
    audioUrl:
      'https://asset.pleasurehub.app/play/dialogue_with_multi_intensity/professor-2dtxzTbZMdPo8EveiHueCJM43uZ9Tt.MP3',
    stages: dialogueWithMultiIntensityData,
    isPremium: false,
  },
  {
    id: 'airport',
    title: '机场商务',
    description: '商务旅行中的偶遇，短暂而激情的火花',
    coverImage:
      'https://images.pexels.com/photos/2132795/pexels-photo-2132795.jpeg?auto=compress&cs=tinysrgb&w=1200',
    category: '商务',
    tags: ['商务', '旅行', '偶遇'],
    audioUrl:
      'https://asset.pleasurehub.app/play/airport_encounter/lesbian-GwRPZ44IZ9WUlf708V0MvKurV2r8Lx.MP3',
    stages: airportEncounterData,
    isPremium: false,
  },
];

/**
 * 处理剧本数据，转换为标准格式
 */
function processScriptData(mapping: ScriptMapping): {
  basicInfo: any;
  content: ScriptContent;
} {
  const stages = mapping.stages as ScriptStage[];

  // 计算总时长
  const totalDuration = TimeUtils.calculateTotalDuration(stages);

  // 构建剧本内容
  const content: ScriptContent = {
    audioUrl: mapping.audioUrl,
    totalDuration,
    stageCount: stages.length,
    stages: stages.map((stage) => ({
      ...stage,
      duration: stage.duration || calculateStageDuration(stage),
    })),
    metadata: {
      language: 'zh-CN',
      version: '1.0',
      importedFrom: 'json',
    },
  };

  // 验证内容
  const validation = ScriptValidator.validateScriptContent(content);
  if (!validation.valid) {
    throw new Error(`剧本 ${mapping.title} 验证失败: ${validation.errors.join(', ')}`);
  }

  // 构建基本信息
  const basicInfo = {
    title: mapping.title,
    description: mapping.description,
    coverImage: mapping.coverImage,
    duration: TimeUtils.secondsToTimeString(totalDuration),
    tags: mapping.tags,
    category: mapping.category,
    content: content,
    audioUrl: mapping.audioUrl,
    totalDuration: totalDuration,
    stageCount: stages.length,
    isPublic: true,
    isPremium: mapping.isPremium || false,
    isActive: true,
  };

  return { basicInfo, content };
}

/**
 * 计算阶段时长（基于对话时间点）
 */
function calculateStageDuration(stage: ScriptStage): number {
  if (stage.dialogues.length === 0) return 0;

  const times = stage.dialogues.map((d) => TimeUtils.timeStringToSeconds(d.time));
  const minTime = Math.min(...times);
  const maxTime = Math.max(...times);

  return maxTime - minTime + 30; // 加30秒作为缓冲
}

/**
 * 迁移剧本数据到数据库
 */
export async function migrateScriptData() {
  console.log('🚀 开始迁移剧本数据...');

  try {
    for (const mapping of scriptMappings) {
      console.log(`📖 处理剧本: ${mapping.title}`);

      const { basicInfo } = processScriptData(mapping);

      // 插入到数据库
      const [insertedScript] = await db.insert(script).values(basicInfo).returning();

      console.log(`✅ 剧本 "${mapping.title}" 迁移成功，ID: ${insertedScript.id}`);
    }

    console.log('🎉 所有剧本数据迁移完成！');
    console.log(`📊 共迁移了 ${scriptMappings.length} 个剧本`);
  } catch (error) {
    console.error('❌ 剧本数据迁移失败:', error);
    throw error;
  } finally {
    await client.end();
  }
}

/**
 * 验证现有剧本数据
 */
export async function validateExistingScripts() {
  console.log('🔍 验证现有剧本数据...');

  try {
    for (const mapping of scriptMappings) {
      console.log(`📖 验证剧本: ${mapping.title}`);

      const { content } = processScriptData(mapping);
      const validation = ScriptValidator.validateScriptContent(content);

      if (validation.valid) {
        console.log(`✅ 剧本 "${mapping.title}" 验证通过`);
        console.log(`   - 总时长: ${TimeUtils.secondsToTimeString(content.totalDuration)}`);
        console.log(`   - 阶段数: ${content.stageCount}`);
        console.log(
          `   - 对话数: ${content.stages.reduce((sum, stage) => sum + stage.dialogues.length, 0)}`
        );
      } else {
        console.log(`❌ 剧本 "${mapping.title}" 验证失败:`);
        validation.errors.forEach((error) => console.log(`   - ${error}`));
      }
    }
  } catch (error) {
    console.error('❌ 验证失败:', error);
    throw error;
  }
}

// 如果直接运行此文件，则执行迁移
if (import.meta.url === `file://${process.argv[1]}`) {
  const command = process.argv[2];

  if (command === 'validate') {
    validateExistingScripts().catch((error) => {
      console.error('验证失败:', error);
      process.exit(1);
    });
  } else {
    migrateScriptData().catch((error) => {
      console.error('迁移失败:', error);
      process.exit(1);
    });
  }
}

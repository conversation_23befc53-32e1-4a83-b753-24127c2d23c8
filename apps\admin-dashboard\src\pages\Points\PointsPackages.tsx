import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  message,
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  GiftOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { PointsPackage } from '@/types/api'
import type { PointsPackageParams } from '@/services/points'
import { pointsService } from '@/services/points'
import { TABLE_CONFIG } from '@/constants'
import dayjs from 'dayjs'

const { Title } = Typography
const { TextArea } = Input

const PointsPackages: React.FC = () => {
  const [packages, setPackages] = useState<PointsPackage[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingPackage, setEditingPackage] = useState<PointsPackage | null>(null)
  const [form] = Form.useForm()
  
  // 统计数据
  const [stats, setStats] = useState({
    totalPackages: 0,
    activePackages: 0,
    totalPoints: 0,
    monthlyRevenue: 0
  })

  useEffect(() => {
    loadPackages()
    loadStats()
  }, [])

  const loadPackages = async () => {
    try {
      setLoading(true)
      
      const response = await pointsService.getPackages()
      
      if (response.success && response.data) {
        setPackages(response.data)
      } else {
        message.error(response.message || '获取积分包列表失败')
      }
    } catch (error) {
      console.error('获取积分包列表失败:', error)
      message.error('获取积分包列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      // 首先尝试正常查询
      let response = await pointsService.getPointsStats()
      
      if (response.success && response.data) {
        setStats({
          totalPackages: response.data.totalPackages,
          activePackages: response.data.activePackages,
          totalPoints: response.data.totalPoints,
          monthlyRevenue: response.data.monthlyRevenue || 0
        })
      } else {
        console.error('获取统计数据失败:', response.message)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 如果出错，尝试快速模式
      try {
        const quickResponse = await pointsService.getPointsStats(true)
        if (quickResponse.success && quickResponse.data) {
          setStats({
            totalPackages: quickResponse.data.totalPackages,
            activePackages: quickResponse.data.activePackages,
            totalPoints: quickResponse.data.totalPoints,
            monthlyRevenue: quickResponse.data.monthlyRevenue || 0
          })
        }
      } catch (quickError) {
        console.error('快速模式也失败:', quickError)
      }
    }
  }

  const handleCreate = () => {
    setEditingPackage(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (pkg: PointsPackage) => {
    setEditingPackage(pkg)
    form.setFieldsValue(pkg)
    setModalVisible(true)
  }

  const handleSubmit = async (values: PointsPackageParams) => {
    try {
      if (editingPackage) {
        const response = await pointsService.updatePackage(editingPackage.id, values)
        if (response.success) {
          message.success('积分包更新成功')
        } else {
          message.error(response.message || '更新失败')
          return
        }
      } else {
        const response = await pointsService.createPackage(values)
        if (response.success) {
          message.success('积分包创建成功')
        } else {
          message.error(response.message || '创建失败')
          return
        }
      }

      setModalVisible(false)
      loadPackages()
      loadStats()
    } catch (error) {
      console.error(editingPackage ? '更新失败:' : '创建失败:', error)
      message.error(editingPackage ? '更新失败' : '创建失败')
    }
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await pointsService.deletePackage(id)
      if (response.success) {
        message.success('积分包删除成功')
        loadPackages()
        loadStats()
      } else {
        message.error(response.message || '删除失败')
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const handleViewDetail = (pkg: PointsPackage) => {
    Modal.info({
      title: '积分包详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div><strong>积分包名称:</strong> {pkg.name}</div>
            <div><strong>积分数量:</strong> {pkg.points.toLocaleString()}积分</div>
            <div><strong>价格:</strong> ¥{pkg.price}</div>
            <div><strong>性价比:</strong> {(pkg.points / pkg.price).toFixed(0)}积分/元</div>
            <div><strong>状态:</strong> {
              pkg.isActive ? 
                <Tag color="green">启用</Tag> : 
                <Tag color="red">禁用</Tag>
            }</div>
            <div><strong>描述:</strong> {pkg.description || '无'}</div>
            <div><strong>创建时间:</strong> {dayjs(pkg.createdAt).format('YYYY-MM-DD HH:mm:ss')}</div>
          </Space>
        </div>
      )
    })
  }

  const columns: ColumnsType<PointsPackage> = [
    {
      title: '积分包名称',
      dataIndex: 'name',
      render: (name) => (
        <Space>
          <GiftOutlined style={{ color: '#52c41a' }} />
          <span style={{ fontWeight: 500 }}>{name}</span>
        </Space>
      ),
    },
    {
      title: '积分数量',
      dataIndex: 'points',
      render: (points) => (
        <span style={{ color: '#1890ff', fontWeight: 500 }}>
          {points.toLocaleString()}
        </span>
      ),
    },
    {
      title: '价格',
      dataIndex: 'price',
      render: (price) => (
        <span style={{ color: '#f50', fontWeight: 500 }}>
          ¥{price.toFixed(2)}
        </span>
      ),
    },
    {
      title: '性价比',
      key: 'ratio',
      render: (_, record) => (
        <span>{(record.points / record.price).toFixed(0)} 积分/元</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="link" 
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个积分包吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button 
                type="link" 
                danger 
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        积分套餐管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总套餐数"
              value={stats.totalPackages}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="启用套餐"
              value={stats.activePackages}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总积分数"
              value={stats.totalPoints}
              valueStyle={{ color: '#722ed1' }}
              formatter={(value) => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="月收入"
              value={stats.monthlyRevenue}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16 }}>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={handleCreate}
          >
            新建积分包
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={packages}
          rowKey="id"
          loading={loading}
          pagination={false}
          {...TABLE_CONFIG}
        />
      </Card>

      {/* 创建/编辑模态框 */}
      <Modal
        title={editingPackage ? '编辑积分包' : '新建积分包'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="积分包名称"
            rules={[{ required: true, message: '请输入积分包名称' }]}
          >
            <Input placeholder="例如：基础积分包" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="points"
                label="积分数量"
                rules={[{ required: true, message: '请输入积分数量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  placeholder="1000"
                  formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, '')) as 1}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="bonusPoints"
                label="奖励积分"
                initialValue={0}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  placeholder="100"
                  formatter={(value) => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={(value) => Number(value!.replace(/\$\s?|(,*)/g, '')) as 0}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="price"
                label="价格（元）"
                rules={[{ required: true, message: '请输入价格' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  placeholder="9.90"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="积分包描述"
          >
            <TextArea 
              rows={3}
              placeholder="详细描述积分包的特色和用途..."
            />
          </Form.Item>

          <Form.Item
            name="isActive"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingPackage ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default PointsPackages
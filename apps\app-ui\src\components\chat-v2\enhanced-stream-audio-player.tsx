import { useState, useEffect } from 'react'
import { Button } from '@heroui/react'
import { useMediaSourceAudio } from '@/hooks/use-media-source-audio'
import { useRealStreamAudio } from '@/hooks/use-real-stream-audio'
import { usePermissionGuard } from '@/hooks/use-permission-guard'
import { useGlobalAudioStore } from '@/stores/global-audio-store'
import { AudioStorage } from '@/lib/media/audio-storage'
import { getGlobalChatDatabase } from '@/lib/chat-database'

interface EnhancedStreamAudioPlayerProps {
  text: string
  messageId?: string
  chatId?: string
  existingAttachments?: Array<{
    url: string
    name: string
    contentType: string
  }>
  className?: string
}

export const EnhancedStreamAudioPlayer = ({
  text,
  messageId,
  chatId,
  existingAttachments,
  className
}: EnhancedStreamAudioPlayerProps) => {
  // MediaSource 播放器（优先使用）
  const mediaSourcePlayer = useMediaSourceAudio()

  // 降级播放器（Blob URL）
  const fallbackPlayer = useRealStreamAudio()

  // 当前使用的播放器
  const [useMediaSource, setUseMediaSource] = useState(true)
  const [hasExistingAudio, setHasExistingAudio] = useState(false)
  const [existingAudioUrl, setExistingAudioUrl] = useState<string | null>(null)
  const [isGenerating, setIsGenerating] = useState(false) // 正在生成状态

  // 全局音频管理
  const globalAudio = useGlobalAudioStore()

  // 音频存储管理器
  const audioStorage = AudioStorage.getInstance()

  // 聊天数据库实例
  const chatDatabase = getGlobalChatDatabase()

  // 本地音频文件路径状态
  const [localAudioPath, setLocalAudioPath] = useState<string | null>(null)

  // 生成唯一的播放器ID
  const playerId = `${messageId || 'unknown'}_${text.substring(0, 20)}`

  // 检查当前是否在播放
  const isCurrentlyPlaying = globalAudio.isCurrentPlaying(playerId)

  // 选择当前播放器
  const currentPlayer = useMediaSource ? mediaSourcePlayer : fallbackPlayer

  // 检查是否已有本地音频文件
  const checkLocalAudio = async (): Promise<string | null> => {
    if (!messageId) return null

    try {
      // 1. 先检查数据库中的附件记录
      if (chatDatabase) {
        const attachments = await chatDatabase.getAttachmentsByMessage(messageId)
        const audioAttachment = attachments.find(
          attachment => attachment.type === 'audio' && attachment.localPath
        )

        if (audioAttachment && audioAttachment.localPath) {
          // 检查本地文件是否仍然存在
          const fileExists = await audioStorage.fileExists(audioAttachment.localPath)
          if (fileExists) {
            return audioAttachment.localPath
          } else {
            console.warn('⚠️ 数据库记录的音频文件不存在，将清理记录')
            // 清理失效的数据库记录
            await chatDatabase.deleteAttachment(audioAttachment.id)
          }
        }
      }

      // 2. 检查本地文件系统中是否有对应的音频文件
      const localPath = await audioStorage.findAudioByMessageId(messageId)
      if (localPath) {
        return localPath
      }

      return null
    } catch (error) {
      console.warn('⚠️ 检查本地音频失败:', error)
      return null
    }
  }

  // 保存音频到本地并更新数据库
  const saveAudioToLocal = async (
    audioData: Uint8Array | string,
    isUrl: boolean = false
  ): Promise<string | null> => {
    if (!messageId) return null

    try {
      const textHash = text
        .substring(0, 50)
        .split('')
        .reduce((hash, char) => {
          return ((hash << 5) - hash + char.charCodeAt(0)) & 0xffffffff
        }, 0)

      let localPath: string

      if (isUrl && typeof audioData === 'string') {
        // 从URL下载并保存
        localPath = await audioStorage.saveAudioFromUrl(
          audioData,
          messageId,
          Math.abs(textHash).toString()
        )
      } else if (audioData instanceof Uint8Array) {
        // 直接保存Uint8Array数据
        localPath = await audioStorage.saveAudio(
          audioData,
          messageId,
          Math.abs(textHash).toString()
        )
      } else {
        console.warn('⚠️ 不支持的音频数据类型')
        return null
      }

      // 🆕 更新数据库附件记录（检查重复）
      if (chatDatabase) {
        try {
          // 先检查是否已有同类型的音频附件
          const existingAttachments = await chatDatabase.getAttachmentsByMessage(messageId)
          const existingAudioAttachment = existingAttachments.find(att => att.type === 'audio')

          if (existingAudioAttachment) {
            // 更新现有记录
            await chatDatabase.updateAttachment(existingAudioAttachment.id, {
              localPath,
              status: 'completed',
              originalUrl:
                typeof audioData === 'string' ? audioData : existingAudioAttachment.originalUrl,
              metadata: JSON.stringify({
                textHash: Math.abs(textHash).toString(),
                updatedAt: Date.now()
              })
            })
            console.log('💾 [AUDIO] 更新现有音频附件记录:', existingAudioAttachment.id)
          } else {
            // 创建新记录，使用更安全的ID生成策略
            const attachmentId = `audio_${messageId}_${Math.abs(textHash).toString(
              36
            )}_${Date.now()}`
            await chatDatabase.createAttachment({
              id: attachmentId,
              messageId,
              type: 'audio',
              name: localPath.split('/').pop() || 'audio.mp3',
              contentType: 'audio/mpeg',
              originalUrl: typeof audioData === 'string' ? audioData : '',
              localPath,
              status: 'completed',
              metadata: JSON.stringify({ textHash: Math.abs(textHash).toString() })
            })
            console.log('💾 [AUDIO] 创建新音频附件记录:', attachmentId)
          }
        } catch (dbError) {
          console.warn('⚠️ 保存到数据库失败，但本地文件已保存:', dbError)
        }
      }

      return localPath
    } catch (error) {
      console.error('❌ 保存音频到本地失败:', error)
      return null
    }
  }

  // 检查 MediaSource 支持并设置播放器
  useEffect(() => {
    const supported = mediaSourcePlayer.isMediaSourceSupported()
    if (!supported) {
      console.log('⚠️ 浏览器不支持 MediaSource，降级到 Blob URL 播放')
      setUseMediaSource(false)
    }
  }, [mediaSourcePlayer])

  // 检查是否已有音频附件（包括本地缓存）
  useEffect(() => {
    const checkAudioAttachments = async () => {
      // 🔧 如果正在播放，不要更新状态，避免干扰播放
      if (isCurrentlyPlaying) {
        return
      }

      // 首先检查本地音频（最高优先级）
      const localPath = await checkLocalAudio()
      if (localPath) {
        setLocalAudioPath(localPath)
        setExistingAudioUrl(await audioStorage.getAudioUrl(localPath))
        setHasExistingAudio(true)
        return
      }

      // 如果没有本地文件，检查服务器附件
      let serverAudioUrl: string | null = null
      if (existingAttachments && existingAttachments.length > 0) {
        const audioAttachment = existingAttachments.find(attachment =>
          attachment.contentType.startsWith('audio/')
        )
        if (audioAttachment) {
          serverAudioUrl = audioAttachment.url
        }
      }

      // 使用服务器附件或清空状态
      if (serverAudioUrl) {
        setExistingAudioUrl(serverAudioUrl)
        setHasExistingAudio(true)
        setLocalAudioPath(null)
      } else {
        setHasExistingAudio(false)
        setExistingAudioUrl(null)
        setLocalAudioPath(null)
      }
    }

    checkAudioAttachments()
  }, [existingAttachments, text, messageId])

  // 播放现有音频
  const playExistingAudio = async () => {
    if (!existingAudioUrl) {
      console.warn('⚠️ 没有现有音频URL')
      return
    }

    try {
      const audio = new Audio()

      // 设置事件监听器
      const handleError = (e: Event) => {
        console.error('🎵 现有音频播放失败:', e)

        // 清理失效的本地文件记录
        if (localAudioPath) {
          setLocalAudioPath(null)
        }

        // 重置状态
        setHasExistingAudio(false)
        setExistingAudioUrl(null)

        // 清理全局状态
        globalAudio.clearCurrent()
      }

      const handleCanPlay = () => {
        // 音频准备就绪
      }

      // 🔧 移除 handlePlay 中的状态更新，改为在播放开始后异步处理下载
      const handleCanPlayThrough = async () => {
        // 检查是否为远程URL且没有本地文件，在音频准备就绪后开始后台下载
        const isRemoteUrl = existingAudioUrl.startsWith('http')
        if (isRemoteUrl && !localAudioPath && messageId) {
          // 🔧 使用 setTimeout 异步处理，避免在播放事件中更新状态
          setTimeout(async () => {
            try {
              // 检查是否已经在处理下载，防止重复
              if (audio.dataset.downloading === 'true') {
                return
              }

              // 标记正在下载
              audio.dataset.downloading = 'true'
              
              console.log('🎵 开始后台下载音频到本地')
              // 后台下载并保存音频
              const savedPath = await saveAudioToLocal(existingAudioUrl, true)
              if (savedPath) {
                console.log('🎵 音频下载完成，下次播放将使用本地文件')
                // 🔧 不在这里更新状态，避免触发重新渲染
                // setLocalAudioPath(savedPath)
              }
            } catch (downloadError) {
              console.warn('⚠️ 后台下载音频失败:', downloadError)
            } finally {
              // 清除下载标记
              audio.dataset.downloading = 'false'
            }
          }, 100)
        }
      }

      audio.addEventListener('error', handleError)
      audio.addEventListener('canplay', handleCanPlay)
      audio.addEventListener('canplaythrough', handleCanPlayThrough)

      // 设置音频源
      audio.src = existingAudioUrl

      // 注册到全局音频管理器
      globalAudio.setCurrentPlaying(playerId, audio)

      // 尝试播放
      await audio.play()
    } catch (error) {
      console.error('🎵 播放现有音频异常:', error)

      // 清理失效的本地文件记录
      if (localAudioPath) {
        setLocalAudioPath(null)
      }

      // 重置状态
      setHasExistingAudio(false)
      setExistingAudioUrl(null)

      // 清理全局状态
      globalAudio.clearCurrent()
    }
  }

  // 权限守卫
  const permissionGuard = usePermissionGuard({
    feature: 'voice_generation',
    onDenied: result => {
      console.log('🚫 [PERMISSION] 语音生成权限不足:', result.reason)
    },
    onGranted: () => {
      console.log('✅ [PERMISSION] 语音生成权限验证通过')
    }
  })

  // 生成新音频
  const handleGenerateNewAudio = async () => {
    try {
      setIsGenerating(true)

      // 权限检查
      const result = await permissionGuard.executeWithPermission(async () => {
        // 如果 MediaSource 失败，自动降级到 Blob URL
        try {
          if (useMediaSource) {
            const result = await currentPlayer.generateAndPlayStream(text, messageId, chatId)

            // 处理 MediaSource 播放器的返回结果
            if (result && typeof result === 'object' && 'audioElement' in result) {
              // MediaSource 播放器返回 { audioElement, audioChunks }
              const { audioElement, audioChunks } = result
              if (audioElement && audioElement.src) {
                console.log('🎵 MediaSource 播放成功，保存到本地存储')

                // 如果有原始音频块数据，直接保存
                if (audioChunks && audioChunks.length > 0) {
                  // 合并所有音频块
                  const totalLength = audioChunks.reduce((acc, chunk) => acc + chunk.length, 0)
                  const audioData = new Uint8Array(totalLength)
                  let offset = 0

                  for (const chunk of audioChunks) {
                    audioData.set(chunk, offset)
                    offset += chunk.length
                  }

                  const localPath = await saveAudioToLocal(audioData)
                  if (localPath) {
                    setLocalAudioPath(localPath)
                    const localUrl = await audioStorage.getAudioUrl(localPath)
                    setExistingAudioUrl(localUrl)
                    setHasExistingAudio(true)
                  }
                } else {
                  // 从音频元素的src保存
                  const localPath = await saveAudioToLocal(audioElement.src, true)
                  if (localPath) {
                    setLocalAudioPath(localPath)
                    const localUrl = await audioStorage.getAudioUrl(localPath)
                    setExistingAudioUrl(localUrl)
                    setHasExistingAudio(true)
                  }
                }

                // 注册到全局音频管理器
                globalAudio.setCurrentPlaying(playerId, audioElement)
              }
            } else if (result && 'src' in result) {
              // 普通音频元素 - 保存到本地存储
              console.log('🎵 MediaSource 播放成功，保存到本地存储')

              const localPath = await saveAudioToLocal(result.src, true)
              if (localPath) {
                setLocalAudioPath(localPath)
                const localUrl = await audioStorage.getAudioUrl(localPath)
                setExistingAudioUrl(localUrl || result.src)
                setHasExistingAudio(true)
              } else {
                setExistingAudioUrl(result.src)
                setHasExistingAudio(true)
              }
            }
          } else {
            const result = await currentPlayer.generateAndPlayStream(text, messageId, chatId)

            // 处理 Blob URL 播放器的返回结果 - 保存到本地存储
            if (result && 'src' in result && result.src) {
              const localPath = await saveAudioToLocal(result.src, true)
              if (localPath) {
                setLocalAudioPath(localPath)
                const localUrl = await audioStorage.getAudioUrl(localPath)
                setExistingAudioUrl(localUrl || result.src)
                setHasExistingAudio(true)
              } else {
                setExistingAudioUrl(result.src)
                setHasExistingAudio(true)
              }
            }
          }
        } catch (error) {
          if (useMediaSource) {
            console.warn('⚠️ MediaSource 播放失败，降级到 Blob URL:', error)
            setUseMediaSource(false)
            const result = await fallbackPlayer.generateAndPlayStream(text, messageId, chatId)

            // 降级播放成功后，保存到本地存储
            if (result && 'src' in result && result.src) {
              const localPath = await saveAudioToLocal(result.src, true)
              if (localPath) {
                setLocalAudioPath(localPath)
                const localUrl = await audioStorage.getAudioUrl(localPath)
                setExistingAudioUrl(localUrl || result.src)
                setHasExistingAudio(true)
              } else {
                setExistingAudioUrl(result.src)
                setHasExistingAudio(true)
              }
            }
          } else {
            throw error
          }
        }
      })

      if (!result.success) {
        return
      }
    } catch (error) {
      console.error('生成新音频失败:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  // 处理按钮点击
  const handleClick = async () => {
    // 如果正在生成，忽略点击
    if (isGenerating) {
      return
    }

    // 如果有现有音频，优先处理现有音频逻辑
    if (hasExistingAudio && existingAudioUrl) {
      // 如果正在播放现有音频，暂停
      if (isCurrentlyPlaying) {
        console.log('🎵 暂停当前播放的音频')
        globalAudio.pauseCurrent()
        return
      }

      // 检查是否有暂停的音频可以恢复
      if (globalAudio.currentAudio && globalAudio.currentPlayingId === playerId && !globalAudio.isPlaying) {
        console.log('🎵 恢复暂停的音频')
        globalAudio.resumeCurrent()
        return
      }

      // 如果不在播放中，开始播放现有音频
      console.log('🎵 开始播放现有音频')
      await playExistingAudio()
      return
    }

    // 如果正在播放流式音频，暂停
    if (currentPlayer.state.status === 'playing') {
      currentPlayer.pause()
      return
    }

    // 如果正在连接，取消
    if (currentPlayer.state.status === 'connecting') {
      currentPlayer.cancel()
      return
    }

    // 如果在缓冲状态但有音频元素，尝试恢复播放
    if (currentPlayer.state.status === 'buffering') {
      // 检查是否是暂停状态（有音频元素且有缓冲数据）
      const canPlay =
        'canPlay' in currentPlayer.state
          ? currentPlayer.state.canPlay
          : currentPlayer.state.bufferProgress > 50
      if (currentPlayer.state.bufferProgress > 0 && canPlay) {
        currentPlayer.play()
        return
      } else {
        // 真正的初始缓冲状态，取消
        currentPlayer.cancel()
        return
      }
    }

    // 如果是暂停状态，恢复播放
    if (currentPlayer.state.status === 'paused') {
      currentPlayer.play()
      return
    }

    // 生成新音频
    await handleGenerateNewAudio()
  }

  // 重试
  const handleRetry = async () => {
    currentPlayer.reset()
    globalAudio.clearCurrent()
    await handleGenerateNewAudio()
  }

  // 获取按钮状态
  const getButtonState = () => {
    // 如果正在生成音频
    if (isGenerating) {
      return {
        icon: 'solar:play-circle-linear',
        loading: true,
        disabled: false,
        color: 'primary' as const,
        tooltip: '正在生成音频...'
      }
    }

    // 如果正在播放现有音频
    if (isCurrentlyPlaying) {
      return {
        icon: 'solar:pause-circle-linear',
        loading: false,
        disabled: false,
        color: 'success' as const,
        tooltip: '暂停播放（现有音频）'
      }
    }

    // 如果有现有音频且处于可播放状态
    if (
      hasExistingAudio &&
      (currentPlayer.state.status === 'idle' ||
        currentPlayer.state.status === 'completed' ||
        currentPlayer.state.status === 'failed')
    ) {
      return {
        icon: 'solar:play-circle-linear',
        tooltip: '播放语音（使用已生成）',
        loading: false,
        disabled: false,
        color: 'success' as const
      }
    }

    // 流式播放状态
    switch (currentPlayer.state.status) {
      case 'connecting':
        return {
          icon: 'solar:play-circle-linear',
          loading: true,
          disabled: false,
          color: 'primary' as const,
          tooltip: '正在连接...'
        }

      case 'buffering': {
        const canPlay =
          'canPlay' in currentPlayer.state
            ? currentPlayer.state.canPlay
            : currentPlayer.state.bufferProgress > 50
        return {
          icon: canPlay ? 'solar:pause-circle-linear' : 'solar:play-circle-linear',
          loading: !canPlay,
          disabled: false,
          color: 'primary' as const,
          tooltip: `缓冲中 ${Math.round(currentPlayer.state.bufferProgress)}%`
        }
      }

      case 'playing':
        return {
          icon: 'solar:pause-circle-linear',
          loading: false,
          disabled: false,
          color: 'warning' as const,
          tooltip: `播放中 ${Math.round(currentPlayer.state.playbackProgress)}%`
        }

      case 'paused':
        return {
          icon: 'solar:play-circle-linear',
          loading: false,
          disabled: false,
          color: 'warning' as const,
          tooltip: '继续播放'
        }

      case 'completed':
        return {
          icon: 'solar:play-circle-linear',
          loading: false,
          disabled: false,
          color: 'success' as const,
          tooltip: '重新播放'
        }

      case 'failed':
        return {
          icon: 'solar:refresh-linear',
          loading: false,
          disabled: false,
          color: 'danger' as const,
          tooltip: '重试'
        }

      default:
        return {
          icon: 'solar:play-circle-linear',
          loading: false,
          disabled: false,
          color: 'primary' as const,
          tooltip: useMediaSource ? '流式播放（MediaSource）' : '流式播放（降级模式）'
        }
    }
  }

  const buttonState = getButtonState()

  return (
    <div className="flex items-center shrink-0">
      <Button
        isIconOnly
        size="md"
        variant="flat"
        onPress={currentPlayer.state.status === 'failed' ? handleRetry : handleClick}
        isLoading={buttonState.loading}
        isDisabled={buttonState.disabled}
        className={`${className} bg-transparent`}
        title={buttonState.tooltip}
      >
        {!buttonState.loading && <img src="/images/voice.svg" className="w-10 h-10 text-white" />}
      </Button>

      {/* 调试信息（开发环境） */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="ml-2 text-xs text-gray-500">
          <div>模式: {useMediaSource ? 'MediaSource' : 'Blob URL'}</div>
          <div>状态: {currentPlayer.state.status}</div>
          <div>缓存: {hasExistingAudio ? '有' : '无'}</div>
          <div>生成中: {isGenerating ? '是' : '否'}</div>
          {currentPlayer.state.bufferProgress > 0 && (
            <div>缓冲: {Math.round(currentPlayer.state.bufferProgress)}%</div>
          )}
          {existingAudioUrl && <div>URL: {existingAudioUrl.substring(0, 20)}...</div>}
        </div>
      )} */}
    </div>
  )
}

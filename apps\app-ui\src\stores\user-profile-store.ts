import { create } from 'zustand'
import { profileService } from '@/api/services/profile'
import type { UserProfile } from '@/api/services/profile'

// Store状态接口
interface UserProfileState {
  // 数据状态
  userProfile: UserProfile | null

  // 加载状态
  isLoading: boolean
  isUpdating: boolean
  error: string | null
  lastFetchTime: number | null

  // 请求状态管理
  fetchPromise: Promise<UserProfile | null> | null
}

// Store方法接口
interface UserProfileActions {
  // 获取用户资料
  fetchUserProfile: (forceRefresh?: boolean) => Promise<UserProfile | null>

  // 更新用户资料
  updateUserProfile: (profileData: Partial<UserProfile>) => Promise<UserProfile | null>

  // 更新头像
  updateAvatar: (avatarUrl: string) => void

  // 获取用户资料（同步）
  getUserProfile: () => UserProfile | null

  // 检查是否有完整的用户资料
  hasCompleteProfile: () => boolean

  // 强制刷新用户资料
  refreshUserProfile: () => Promise<UserProfile | null>

  // 清除缓存
  clearUserProfile: () => void

  // 检查缓存是否有效
  isCacheValid: () => boolean
}

// 缓存配置
const CACHE_EXPIRY_TIME = 10 * 60 * 1000 // 10分钟缓存
const PROFILE_REQUIRED_FIELDS = ['nickname', 'gender'] // 必需字段

// 创建store
export const useUserProfileStore = create<UserProfileState & UserProfileActions>((set, get) => ({
  // 初始状态
  userProfile: null,
  isLoading: false,
  isUpdating: false,
  error: null,
  lastFetchTime: null,
  fetchPromise: null,

  // 检查缓存是否有效
  isCacheValid: () => {
    const { lastFetchTime } = get()
    if (!lastFetchTime) return false
    return Date.now() - lastFetchTime < CACHE_EXPIRY_TIME
  },

  // 获取用户资料
  fetchUserProfile: async (forceRefresh = false) => {
    const state = get()

    // 如果不强制刷新且缓存有效，直接返回缓存数据
    if (!forceRefresh && state.isCacheValid() && state.userProfile) {
      console.log('🎯 使用缓存的用户资料数据')
      return state.userProfile
    }

    // 如果正在请求中，返回同一个Promise避免重复请求
    if (state.fetchPromise) {
      console.log('🔄 等待进行中的用户资料请求')
      return state.fetchPromise
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log('🚀 开始获取用户资料数据')

        set({
          isLoading: !state.userProfile, // 如果有缓存数据，不显示loading
          error: null
        })

        const response = await profileService.get()
        const profile = response?.userProfile || null

        set({
          userProfile: profile,
          isLoading: false,
          error: null,
          lastFetchTime: Date.now(),
          fetchPromise: null
        })

        console.log('✅ 成功获取用户资料数据')
        return profile
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取用户资料失败'
        console.error('❌ 获取用户资料失败:', errorMessage)

        set({
          isLoading: false,
          error: errorMessage,
          fetchPromise: null
        })

        // 如果有缓存数据，在错误时返回缓存数据
        if (state.userProfile) {
          console.log('⚠️ 请求失败，返回缓存数据')
          return state.userProfile
        }

        throw error
      }
    })()

    // 保存Promise到状态中
    set({ fetchPromise })

    return fetchPromise
  },

  // 更新用户资料
  updateUserProfile: async (profileData: Partial<UserProfile>) => {
    const state = get()

    try {
      console.log('🔄 开始更新用户资料')

      set({
        isUpdating: true,
        error: null
      })

      const response = await profileService.update(profileData)
      const updatedProfile = response?.userProfile

      if (updatedProfile) {
        // 更新缓存中的用户资料
        set({
          userProfile: updatedProfile,
          isUpdating: false,
          error: null,
          lastFetchTime: Date.now() // 更新缓存时间
        })

        console.log('✅ 成功更新用户资料')
        return updatedProfile
      } else {
        throw new Error('更新用户资料失败')
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '更新用户资料失败'
      console.error('❌ 更新用户资料失败:', errorMessage)

      set({
        isUpdating: false,
        error: errorMessage
      })

      throw error
    }
  },

  // 更新头像（本地更新，不调用API）
  updateAvatar: (avatarUrl: string) => {
    const state = get()
    if (state.userProfile) {
      set({
        userProfile: {
          ...state.userProfile,
          avatarUrl
        },
        lastFetchTime: Date.now()
      })
      console.log('🖼️ 更新用户头像')
    }
  },

  // 获取用户资料（同步）
  getUserProfile: () => {
    return get().userProfile
  },

  // 检查是否有完整的用户资料
  hasCompleteProfile: () => {
    const { userProfile } = get()
    if (!userProfile) return false

    // 检查必需字段是否都存在且不为空
    return PROFILE_REQUIRED_FIELDS.every(field => {
      const value = userProfile[field as keyof UserProfile]
      return value !== null && value !== undefined && value !== ''
    })
  },

  // 强制刷新用户资料
  refreshUserProfile: async () => {
    return get().fetchUserProfile(true)
  },

  // 清除缓存
  clearUserProfile: () => {
    console.log('🧹 清除用户资料缓存')
    set({
      userProfile: null,
      isLoading: false,
      isUpdating: false,
      error: null,
      lastFetchTime: null,
      fetchPromise: null
    })
  }
}))

// 便捷的Hook，用于只获取数据而不触发请求
export const useUserProfileData = () => {
  const store = useUserProfileStore()
  return {
    userProfile: store.userProfile,
    isLoading: store.isLoading,
    isUpdating: store.isUpdating,
    error: store.error,
    isCacheValid: store.isCacheValid(),
    hasCompleteProfile: store.hasCompleteProfile()
  }
}

// 便捷的Hook，用于获取特定字段
export const useUserProfileField = <K extends keyof UserProfile>(field: K) => {
  const userProfile = useUserProfileStore(state => state.userProfile)
  return userProfile?.[field]
}

// 便捷的Hook，用于检查资料完整性
export const useProfileCompleteness = () => {
  const hasCompleteProfile = useUserProfileStore(state => state.hasCompleteProfile)
  return hasCompleteProfile()
}

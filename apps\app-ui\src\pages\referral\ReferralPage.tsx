import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { apiService } from '@/api'
import { useTranslation } from 'react-i18next'

// HeroUI 组件
import { Card, CardBody, Button, Divider, Chip, addToast, useDisclosure } from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

// 类型定义
import type { InviteCode, InviteStats, CommissionAccount } from '@/api'

export default function ReferralPage() {
  const navigate = useNavigate()
  const { user, status } = useAuth()
  const { t } = useTranslation(['referral'])

  // 状态管理
  const [isLoading, setIsLoading] = useState(true)
  const [inviteCode, setInviteCode] = useState<InviteCode | null>(null)
  const [stats, setStats] = useState<InviteStats>({
    totalInvites: 0,
    successfulInvites: 0,
    pendingInvites: 0,
    totalCommissionEarned: '0.00'
  })
  const [commissionAccount, setCommissionAccount] = useState<CommissionAccount | null>(null)

  useEffect(() => {
    // 检查认证状态
    if (status === 'unauthenticated') {
      addToast({
        title: t('referral:common.please_login'),
        color: 'warning'
      })
      navigate('/login')
      return
    }

    if (status === 'authenticated') {
      loadReferralData()
    }
  }, [status, navigate])

  const loadReferralData = async () => {
    try {
      setIsLoading(true)
      const response = await apiService.referral.getMyInviteCode()

      if (response.success) {
        setInviteCode(response.data.inviteCode)
        setStats(response.data.stats)
        setCommissionAccount(response.data.commissionAccount)
      } else {
        addToast({
          title: t('referral:referral.generate_failed'),
          description: response.error,
          color: 'danger'
        })
      }
    } catch (error) {
      console.error('获取邀请码信息失败:', error)
      addToast({
        title: t('referral:referral.generate_failed'),
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleGenerateCode = async () => {
    try {
      setIsLoading(true)
      const response = await apiService.referral.generateInviteCode()

      if (response.success) {
        setInviteCode(response.data)
        addToast({
          title: t('referral:referral.code_generated'),
          color: 'success'
        })
      } else {
        addToast({
          title: t('referral:referral.generate_failed'),
          description: response.error,
          color: 'danger'
        })
      }
    } catch (error) {
      console.error('生成邀请码失败:', error)
      addToast({
        title: t('referral:referral.generate_failed'),
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleCopyCode = async () => {
    if (!inviteCode) return

    try {
      await navigator.clipboard.writeText(inviteCode.code)
      addToast({
        title: t('referral:referral.code_copied'),
        color: 'success'
      })
    } catch (error) {
      console.error('复制失败:', error)
      addToast({
        title: t('referral:referral.copy_failed'),
        color: 'danger'
      })
    }
  }

  const handleShareCode = () => {
    if (!inviteCode) return

    const shareText = t('referral:referral.share_text', { code: inviteCode.code })

    if (navigator.share) {
      navigator
        .share({
          title: t('referral:referral.title'),
          text: shareText
        })
        .catch(console.error)
    } else {
      // 降级到复制
      handleCopyCode()
    }
  }

  const handleViewInvites = () => {
    navigate('/referral/invites')
  }

  const handleViewCommission = () => {
    navigate('/referral/commission')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Icon icon="lucide:loader-2" className="w-8 h-8 animate-spin text-primary" />
          <p className="text-default-500">{t('referral:common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 safe-area-top pb-20">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Button
          isIconOnly
          variant="light"
          className="text-default-500 hover:text-foreground"
          onPress={() => navigate('/profile')}
        >
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-foreground">{t('referral:referral.title')}</h1>
        <div className="w-10" /> {/* 占位符保持居中 */}
      </div>

      {/* 邀请码卡片 */}
      <div className="px-6 mb-6">
        <Card className="bg-gradient-to-r from-primary-50 to-primary-100 border-primary-200">
          <CardBody className="p-6">
            <div className="text-center">
              <Icon icon="lucide:gift" className="w-12 h-12 text-primary mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-primary-700 mb-2">
                {t('referral:referral.my_invite_code')}
              </h2>

              {inviteCode ? (
                <>
                  <div className="bg-white/50 rounded-lg p-4 mb-4">
                    <p className="text-2xl font-bold text-primary-800 tracking-wider">
                      {inviteCode.code}
                    </p>
                  </div>
                  <div className="flex gap-3 justify-center">
                    <Button
                      color="primary"
                      variant="solid"
                      onPress={handleCopyCode}
                      startContent={<Icon icon="lucide:copy" className="w-4 h-4" />}
                    >
                      {t('referral:referral.copy_code')}
                    </Button>
                    <Button
                      color="primary"
                      variant="bordered"
                      onPress={handleShareCode}
                      startContent={<Icon icon="lucide:share-2" className="w-4 h-4" />}
                    >
                      {t('referral:referral.share')}
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <p className="text-primary-600 mb-4">{t('referral:referral.no_invite_code')}</p>
                  <Button
                    color="primary"
                    variant="solid"
                    onPress={handleGenerateCode}
                    isLoading={isLoading}
                    startContent={<Icon icon="lucide:plus" className="w-4 h-4" />}
                  >
                    {t('referral:referral.generate_code')}
                  </Button>
                </>
              )}
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 统计数据 */}
      <div className="px-6 mb-6">
        <div className="grid grid-cols-2 gap-3">
          <Card className="bg-content1/50 backdrop-blur-sm">
            <CardBody className="p-4 text-center">
              <Icon icon="lucide:users" className="w-6 h-6 text-success mx-auto mb-2" />
              <p className="text-2xl font-bold text-foreground">{stats.successfulInvites}</p>
              <p className="text-xs text-default-500">
                {t('referral:referral.successful_invites')}
              </p>
            </CardBody>
          </Card>

          <Card className="bg-content1/50 backdrop-blur-sm">
            <CardBody className="p-4 text-center">
              <Icon icon="lucide:coins" className="w-6 h-6 text-warning mx-auto mb-2" />
              <p className="text-2xl font-bold text-foreground">¥{stats.totalCommissionEarned}</p>
              <p className="text-xs text-default-500">{t('referral:referral.total_commission')}</p>
            </CardBody>
          </Card>
        </div>
      </div>

      {/* 功能菜单 */}
      <div className="px-6">
        <Card className="bg-content1/50 backdrop-blur-sm">
          <CardBody className="p-0">
            {/* 我的邀请 */}
            <div
              className="flex items-center justify-between p-4 cursor-pointer hover:bg-content2/50 transition-colors"
              onClick={handleViewInvites}
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-content2 rounded-lg flex items-center justify-center">
                  <Icon icon="lucide:user-plus" className="w-5 h-5 text-foreground" />
                </div>
                <div>
                  <span className="text-foreground font-medium">
                    {t('referral:referral.my_invites')}
                  </span>
                  <p className="text-xs text-default-500">{t('referral:referral.view_invites')}</p>
                </div>
              </div>
              <Icon icon="lucide:chevron-right" className="w-5 h-5 text-default-400" />
            </div>

            <Divider />

            {/* 佣金管理 */}
            <div
              className="flex items-center justify-between p-4 cursor-pointer hover:bg-content2/50 transition-colors"
              onClick={handleViewCommission}
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-content2 rounded-lg flex items-center justify-center">
                  <Icon icon="lucide:wallet" className="w-5 h-5 text-foreground" />
                </div>
                <div>
                  <span className="text-foreground font-medium">
                    {t('referral:referral.commission_management')}
                  </span>
                  <p className="text-xs text-default-500">
                    {t('referral:referral.view_commission')}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {commissionAccount && parseFloat(commissionAccount.availableBalance) > 0 && (
                  <Chip size="sm" color="success" variant="flat">
                    ¥{commissionAccount.availableBalance}
                  </Chip>
                )}
                <Icon icon="lucide:chevron-right" className="w-5 h-5 text-default-400" />
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 邀请规则说明 */}
      <div className="px-6 mt-6">
        <Card className="bg-content1/50 backdrop-blur-sm">
          <CardBody className="p-4">
            <h3 className="text-foreground font-medium mb-3 flex items-center gap-2">
              <Icon icon="lucide:info" className="w-4 h-4" />
              {t('referral:referral.invitation_rules')}
            </h3>
            <div className="space-y-2 text-sm text-default-600">
              <p>• {t('referral:referral.rule_1')}</p>
              <p>• {t('referral:referral.rule_2')}</p>
              <p>• {t('referral:referral.rule_3')}</p>
              <p>• {t('referral:referral.rule_4')}</p>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 底部空间 */}
      <div className="h-16" />
    </div>
  )
}

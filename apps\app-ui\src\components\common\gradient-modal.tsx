import React from 'react'
import {
  <PERSON><PERSON>,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Button,
  ModalProps
} from '@heroui/react'
import { Icon } from '@iconify/react'

interface GradientModalProps extends Omit<ModalProps, 'children'> {
  /** 模态框标题 */
  title?: string
  /** 模态框内容 */
  children: React.ReactNode
  /** 取消按钮文本 */
  cancelText?: string
  /** 确认按钮文本 */
  confirmText?: string
  /** 取消按钮点击事件 */
  onCancel?: () => void
  /** 确认按钮点击事件 */
  onConfirm?: () => void
  /** 是否显示底部按钮 */
  showFooter?: boolean
  /** 自定义底部内容 */
  footerContent?: React.ReactNode
  /** 确认按钮是否加载中 */
  confirmLoading?: boolean
  /** 确认按钮是否禁用 */
  confirmDisabled?: boolean
}

export default function GradientModal({
  title,
  children,
  cancelText = '取消',
  confirmText = '确认',
  onCancel,
  onConfirm,
  showFooter = true,
  footerContent,
  confirmLoading = false,
  confirmDisabled = false,
  ...modalProps
}: GradientModalProps) {
  return (
    <Modal
      placement="center"
      backdrop="blur"
      classNames={{
        base: 'bg-transparent shadow-none',
        wrapper: 'bg-transparent',
        backdrop: 'bg-black/60 backdrop-blur-sm border-none'
      }}
      {...modalProps}
    >
      <ModalContent className="bg-transparent shadow-none w-[85%] mx-4 rounded-3xl">
        {onClose => (
          <div className="relative">
            {/* 主要内容容器 */}
            <div className="relative z-10 backdrop-blur-xl rounded-3xl overflow-hidden bg-background">
              {/* 左上角渐变装饰 */}
              <div className="absolute top-0 left-0 size-full pointer-events-none z-1">
                <img
                  src="/images/decorate/decorate-9.svg"
                  alt=""
                  className="w-full h-full opacity-80"
                />
              </div>
              {/* 模态框头部 */}
              {title && (
                <ModalHeader className="relative z-10 flex items-center justify-center p-6 pb-2">
                  {title && <h3 className="text-white text-base font-semibold">{title}</h3>}
                </ModalHeader>
              )}

              {/* 模态框内容 */}
              <ModalBody className="relative z-10 px-6 pb-4">
                <div className="text-white/90">{children}</div>
              </ModalBody>

              {/* 模态框底部 */}
              {showFooter && (
                <ModalFooter className="relative z-10 p-6 pt-4">
                  {footerContent || (
                    <div className="flex gap-3 w-full">
                      {onCancel && (
                        <Button
                          variant="bordered"
                          onPress={() => {
                            onCancel()
                            onClose()
                          }}
                          className="flex-1 border-white/20 text-white hover:bg-white/10 transition-colors h-12 px-11 py-3.5 rounded-2xl"
                        >
                          {cancelText}
                        </Button>
                      )}
                      {onConfirm && (
                        <Button
                          onPress={() => {
                            onConfirm()
                            if (!confirmLoading) {
                              onClose()
                            }
                          }}
                          isLoading={confirmLoading}
                          isDisabled={confirmDisabled}
                          className="flex-1 text-white font-medium transition-all bg-button-primary  h-12 px-11 py-3.5 rounded-2xl"
                        >
                          {confirmText}
                        </Button>
                      )}
                    </div>
                  )}
                </ModalFooter>
              )}
            </div>
          </div>
        )}
      </ModalContent>
    </Modal>
  )
}

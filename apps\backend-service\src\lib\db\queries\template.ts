import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type { Template, MediaGeneration } from '../schema'
import type { Env } from '@/types/env'

// ==================== 模板操作 ====================

/**
 * 获取模板列表
 */
export async function getTemplates(
  env: Env,
  options: {
    category?: string
    isActive?: boolean
    isPremium?: boolean
    limit?: number
    offset?: number
  } = {}
): Promise<Template[]> {
  try {
    const supabase = getSupabase(env)
    const { category, isActive = true, isPremium, limit = 20, offset = 0 } = options

    let query = supabase.from(TABLE_NAMES.template).select('*')

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive)
    }
    if (category) {
      query = query.eq('category', category)
    }
    if (isPremium !== undefined) {
      query = query.eq('is_premium', isPremium)
    }

    const result = await query
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('获取模板列表失败', error)
    throw error
  }
}

/**
 * 根据分类获取模板
 */
export async function getTemplatesByCategory(
  env: Env,
  category: string,
  isPremium?: boolean
): Promise<Template[]> {
  try {
    const supabase = getSupabase(env)

    let query = supabase
      .from(TABLE_NAMES.template)
      .select('*')
      .eq('category', category)
      .eq('is_active', true)

    if (isPremium !== undefined) {
      query = query.eq('is_premium', isPremium)
    }

    const result = await query.order('created_at', { ascending: false })

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('根据分类获取模板失败', error)
    throw error
  }
}

/**
 * 获取所有模板分类
 */
export async function getTemplateCategories(env: Env): Promise<string[]> {
  try {
    const supabase = getSupabase(env)

    const result = await supabase
      .from(TABLE_NAMES.template)
      .select('category')
      .eq('is_active', true)
      .not('category', 'is', null)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    // 提取唯一的分类
    const categories = [
      ...new Set((data || []).map((row: any) => row.category).filter(Boolean))
    ] as string[]
    return categories
  } catch (error) {
    console.error('获取模板分类失败', error)
    throw error
  }
}

/**
 * 搜索模板
 */
export async function searchTemplates(
  env: Env,
  options: {
    query?: string
    category?: string
    tags?: string[]
    isPremium?: boolean
    sortBy?: 'created' | 'name' | 'cost' | 'popular'
    sortOrder?: 'asc' | 'desc'
    limit?: number
    offset?: number
  }
): Promise<{ templates: Template[]; total: number }> {
  try {
    const supabase = getSupabase(env)
    const {
      query: searchQuery,
      category,
      tags,
      isPremium,
      sortBy = 'created',
      sortOrder = 'desc',
      limit = 20,
      offset = 0
    } = options

    let queryBuilder = supabase
      .from(TABLE_NAMES.template)
      .select('*', { count: 'exact' })
      .eq('is_active', true)

    // 文本搜索
    if (searchQuery) {
      queryBuilder = queryBuilder.or(
        `name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`
      )
    }

    // 分类过滤
    if (category) {
      queryBuilder = queryBuilder.eq('category', category)
    }

    // 标签过滤
    if (tags && tags.length > 0) {
      for (const tag of tags) {
        queryBuilder = queryBuilder.contains('tags', [tag])
      }
    }

    // 会员模板过滤
    if (isPremium !== undefined) {
      queryBuilder = queryBuilder.eq('is_premium', isPremium)
    }

    // 构建排序
    let sortColumn: string
    switch (sortBy) {
      case 'name':
        sortColumn = 'name'
        break
      case 'cost':
        sortColumn = 'points_cost'
        break
      case 'popular':
        sortColumn = 'usage_count'
        break
      case 'created':
      default:
        sortColumn = 'created_at'
        break
    }

    queryBuilder = queryBuilder.order(sortColumn, { ascending: sortOrder === 'asc' })

    // 应用分页
    queryBuilder = queryBuilder.range(offset, offset + limit - 1)

    const result = await queryBuilder
    const { data, error, count } = result
    if (error) throw error

    const { data: convertedData } = handleSupabaseResult({ data, error: null })

    return {
      templates: convertedData || [],
      total: count || 0
    }
  } catch (error) {
    console.error('搜索模板失败', error)
    throw error
  }
}

/**
 * 根据ID获取模板
 */
export async function getTemplateById(env: Env, id: string): Promise<Template | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase.from(TABLE_NAMES.template).select('*').eq('id', id).single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) return null
    return data
  } catch (error) {
    console.error('获取模板详情失败', error)
    throw error
  }
}

/**
 * 创建模板
 */
export async function createTemplate(
  env: Env,
  data: {
    name: string
    description?: string
    category?: string
    previewImage?: string
    prompt: string
    negativePrompt?: string
    pointsCost?: number
    isPremium?: boolean
    isPublic?: boolean
    isActive?: boolean
    tags?: string[]
    settings?: any
    createdBy?: string
  }
): Promise<Template[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.template)
      .insert({
        name: data.name,
        description: data.description,
        category: data.category,
        preview_image: data.previewImage,
        prompt: data.prompt,
        negative_prompt: data.negativePrompt,
        points_cost: data.pointsCost ?? 1,
        is_premium: data.isPremium ?? false,
        is_public: data.isPublic ?? true,
        is_active: data.isActive ?? true,
        tags: data.tags || [],
        settings: data.settings || {},
        created_by: data.createdBy
      })
      .select()

    const { data: templates, error } = handleSupabaseResult(result)
    if (error) throw error
    return templates || []
  } catch (error) {
    console.error('创建模板失败', error)
    throw error
  }
}

/**
 * 更新模板
 */
export async function updateTemplate(
  env: Env,
  id: string,
  data: {
    name?: string
    description?: string
    category?: string
    previewImage?: string
    prompt?: string
    negativePrompt?: string
    pointsCost?: number
    isPremium?: boolean
    isPublic?: boolean
    isActive?: boolean
    tags?: string[]
    settings?: any
  }
): Promise<Template[]> {
  try {
    const supabase = getSupabase(env)

    // 转换字段名为 snake_case
    const updateData: any = {}
    if (data.name !== undefined) updateData.name = data.name
    if (data.description !== undefined) updateData.description = data.description
    if (data.category !== undefined) updateData.category = data.category
    if (data.previewImage !== undefined) updateData.preview_image = data.previewImage
    if (data.prompt !== undefined) updateData.prompt = data.prompt
    if (data.negativePrompt !== undefined) updateData.negative_prompt = data.negativePrompt
    if (data.pointsCost !== undefined) updateData.points_cost = data.pointsCost
    if (data.isPremium !== undefined) updateData.is_premium = data.isPremium
    if (data.isPublic !== undefined) updateData.is_public = data.isPublic
    if (data.isActive !== undefined) updateData.is_active = data.isActive
    if (data.tags !== undefined) updateData.tags = data.tags
    if (data.settings !== undefined) updateData.settings = data.settings

    updateData.updated_at = new Date().toISOString()

    const result = await supabase
      .from(TABLE_NAMES.template)
      .update(updateData)
      .eq('id', id)
      .select()

    const { data: templates, error } = handleSupabaseResult(result)
    if (error) throw error
    return templates || []
  } catch (error) {
    console.error('更新模板失败', error)
    throw error
  }
}

/**
 * 删除模板（软删除）
 */
export async function deleteTemplate(env: Env, id: string): Promise<Template[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.template)
      .update({
        is_active: false,
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('删除模板失败', error)
    throw error
  }
}

/**
 * 更新模板使用次数（异步安全版本）
 */
export async function updateTemplateUsageCount(env: Env, id: string): Promise<void> {
  try {
    const supabase = getSupabase(env)
    console.log('🔄 [模板使用次数] 开始更新:', { templateId: id })

    // 使用查询-更新模式，添加重试机制处理并发
    let retries = 3
    while (retries > 0) {
      try {
        const currentResult = await supabase
          .from(TABLE_NAMES.template)
          .select('usage_count')
          .eq('id', id)
          .single()

        const { data: current } = handleSupabaseSingleResult(currentResult)
        if (!current) {
          console.warn('⚠️ [模板使用次数] 模板不存在:', id)
          return // 静默返回，不抛出错误
        }

        console.log('📊 [模板使用次数] 当前数据:', {
          templateId: id,
          currentUsageCount: current.usageCount,
          rawData: current
        })

        const newCount = (current.usageCount || 0) + 1
        console.log('📈 [模板使用次数] 准备更新为:', { templateId: id, newCount })

        const updateResult = await supabase
          .from(TABLE_NAMES.template)
          .update({
            usage_count: newCount,
            updated_at: new Date().toISOString()
          })
          .eq('id', id)

        if (!updateResult.error) {
          console.log('✅ [模板使用次数] 更新成功:', {
            templateId: id,
            oldCount: current.usageCount || 0,
            newCount
          })
          break // 更新成功，退出重试循环
        } else {
          console.error('❌ [模板使用次数] 更新失败:', {
            templateId: id,
            error: updateResult.error,
            retry: 4 - retries
          })
        }

        retries--
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 100 * (4 - retries))) // 递增延迟
        }
      } catch (retryError: any) {
        console.error('❌ [模板使用次数] 重试过程中出错:', {
          templateId: id,
          retryError: retryError.message,
          remainingRetries: retries - 1
        })
        retries--
        if (retries === 0) throw retryError
      }
    }
  } catch (error: any) {
    console.error('⚠️ [模板使用次数] 更新最终失败:', { templateId: id, error: error.message })
    // 不抛出错误，避免影响主要功能
    // 可以考虑发送到错误监控系统或稍后重试
  }
}

/**
 * 获取模板统计信息
 */
export async function getTemplateStats(env: Env): Promise<{
  overview: {
    total: number
    active: number
    premium: number
    avgCost: number
  }
  categories: Array<{ category: string; count: number }>
}> {
  try {
    const supabase = getSupabase(env)

    // 基础统计
    const { count: total } = await supabase
      .from(TABLE_NAMES.template)
      .select('*', { count: 'exact', head: true })

    const { count: active } = await supabase
      .from(TABLE_NAMES.template)
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    const { count: premium } = await supabase
      .from(TABLE_NAMES.template)
      .select('*', { count: 'exact', head: true })
      .eq('is_premium', true)

    // 平均费用计算（需要手动计算）
    const avgResult = await supabase.from(TABLE_NAMES.template).select('points_cost')

    const { data: avgData } = handleSupabaseResult(avgResult)
    const avgCost =
      avgData && avgData.length > 0
        ? avgData.reduce((sum: number, item: any) => sum + (item.points_cost || 0), 0) /
          avgData.length
        : 0

    // 分类统计（需要手动分组）
    const categoryResult = await supabase
      .from(TABLE_NAMES.template)
      .select('category')
      .eq('is_active', true)

    const { data: categoryData } = handleSupabaseResult(categoryResult)
    const categoryStats = (categoryData || []).reduce((acc: any, item: any) => {
      const category = item.category || 'uncategorized'
      acc[category] = (acc[category] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const categories = Object.entries(categoryStats).map(([category, count]) => ({
      category,
      count: count as number
    }))

    return {
      overview: {
        total: total || 0,
        active: active || 0,
        premium: premium || 0,
        avgCost: Math.round(avgCost * 100) / 100
      },
      categories
    }
  } catch (error) {
    console.error('获取模板统计失败', error)
    throw error
  }
}

// ==================== 生成历史操作 ====================

/**
 * 创建生成历史
 */
export async function createGenerationHistory(
  env: Env,
  data: {
    userId: string
    templateId?: string
    originalImageUrl?: string
    generatedImageUrl?: string
    prompt?: string
    negativePrompt?: string
    pointsUsed: number
    status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
    errorMessage?: string
    generationTime?: number
    metadata?: any
  }
): Promise<MediaGeneration[]> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .insert({
        user_id: data.userId,
        template_id: data.templateId,
        original_image_url: data.originalImageUrl,
        generated_image_url: data.generatedImageUrl,
        prompt: data.prompt,
        negative_prompt: data.negativePrompt,
        points_used: data.pointsUsed,
        status: data.status ?? 'pending',
        error_message: data.errorMessage,
        generation_time: data.generationTime?.toString(),
        metadata: data.metadata || {}
      })
      .select()

    const { data: history, error } = handleSupabaseResult(result)
    if (error) throw error
    return history || []
  } catch (error) {
    console.error('创建生成历史失败', error)
    throw error
  }
}

/**
 * 更新生成历史
 */
export async function updateGenerationHistory(
  env: Env,
  id: string,
  data: {
    status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled'
    generatedImageUrl?: string
    errorMessage?: string
    generationTime?: number
    completedAt?: Date
  }
): Promise<MediaGeneration[]> {
  try {
    const supabase = getSupabase(env)

    // 转换字段名为 snake_case
    const updateData: any = {}
    if (data.status !== undefined) updateData.status = data.status
    if (data.generatedImageUrl !== undefined)
      updateData.generated_image_url = data.generatedImageUrl
    if (data.errorMessage !== undefined) updateData.error_message = data.errorMessage
    if (data.generationTime !== undefined)
      updateData.generation_time = data.generationTime.toString()
    if (data.completedAt !== undefined) updateData.completed_at = data.completedAt.toISOString()

    const result = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .update(updateData)
      .eq('id', id)
      .select()

    const { data: history, error } = handleSupabaseResult(result)
    if (error) throw error
    return history || []
  } catch (error) {
    console.error('更新生成历史失败', error)
    throw error
  }
}

/**
 * 获取用户生成历史
 */
export async function getUserGenerationHistory(
  env: Env,
  userId: string,
  options: {
    limit?: number
    offset?: number
  } = {}
): Promise<MediaGeneration[]> {
  try {
    const supabase = getSupabase(env)
    const { limit = 20, offset = 0 } = options

    const result = await supabase
      .from(TABLE_NAMES.mediaGeneration)
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('获取用户生成历史失败', error)
    throw error
  }
}

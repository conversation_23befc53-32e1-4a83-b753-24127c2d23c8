import { Hono } from 'hono';
import type { Env } from '@/types/env';
import { createSupabaseClient } from '@/lib/supabase';

const health = new Hono<{ Bindings: Env }>();

// 基础健康检查
health.get('/', (c) => {
  return c.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'pleasurehub-backend-service',
  });
});

// 详细健康检查（包括数据库连接）
health.get('/detailed', async (c) => {
  const checks = {
    service: 'ok',
    timestamp: new Date().toISOString(),
    supabase: 'unknown',
    database: 'unknown',
  };

  // 检查 Supabase 连接
  try {
    const supabase = createSupabaseClient(c.env);
    const { data, error } = await supabase.from('characters').select('count').limit(1);

    if (error) {
      checks.supabase = 'error';
      checks.database = error.message;
    } else {
      checks.supabase = 'ok';
      checks.database = 'ok';
    }
  } catch (error) {
    checks.supabase = 'error';
    checks.database = error instanceof Error ? error.message : 'Unknown error';
  }

  const isHealthy = checks.supabase === 'ok' && checks.database === 'ok';

  return c.json(checks, isHealthy ? 200 : 503);
});

export default health;

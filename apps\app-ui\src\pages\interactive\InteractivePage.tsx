import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useTranslation } from 'react-i18next'
import { ScriptSelector } from './components/ScriptSelector'
import { DeviceConnectDrawer } from './components/DeviceConnectDrawer'
import { useDeviceStore } from '../../stores/device-store'
import { scriptService } from '@/api/services/scripts'

export default function InteractivePage() {
  const navigate = useNavigate()
  const { t } = useTranslation('interactive')

  // 状态管理
  const [selectedScriptId, setSelectedScriptId] = useState<string>('')
  const [isDeviceDrawerOpen, setIsDeviceDrawerOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 使用全局设备状态和缓存的设备列表
  const {
    connectedDevice,
    connectDevice,
    supportedDevices,
    isLoadingDevices,
    getSupportedDevices,
    enterFunction
  } = useDeviceStore()

  // 初始化设备列表
  useEffect(() => {
    const initServices = async () => {
      try {
        // 使用缓存的设备列表获取方法
        await getSupportedDevices()
      } catch (error) {
        console.error(`❌ ${t('errors.initDevicesFailed')}`, error)
      }
    }

    initServices()
  }, [getSupportedDevices])

  // 处理剧本选择
  const handleScriptSelect = (scriptId: string) => {
    setSelectedScriptId(scriptId)

    // 如果已经有连接的设备，直接开始剧本
    if (connectedDevice) {
      console.log('🔍 InteractivePage - 检测到已连接设备，直接开始剧本:', connectedDevice.name)
      // 设置设备为剧本模式（不改变连接状态，只改变控制模式）
      startScriptWithConnectedDevice(scriptId)
    } else {
      // 没有连接设备，显示设备连接抽屉
      console.log('🔍 InteractivePage - 未检测到连接设备，显示设备连接抽屉')
      setIsDeviceDrawerOpen(true)
    }
  }

  // 处理设备连接
  const handleDeviceConnect = async (device: any) => {
    try {
      await connectDevice(device, 'script') // 标记为剧本功能连接
      setIsDeviceDrawerOpen(false)
      // 连接设备后进入播放器页面
      startScript()
    } catch (error) {
      console.error(`${t('errors.deviceConnectFailed')}`, error)
    }
  }

  // 跳过设备连接
  const handleSkipDevice = () => {
    setIsDeviceDrawerOpen(false)
    // 跳过设备连接直接进入播放器页面
    startScript()
  }

  // 使用已连接设备开始剧本
  const startScriptWithConnectedDevice = async (scriptId: string) => {
    try {
      // 通知设备store进入剧本功能模式
      await enterFunction('script')

      console.log('🔍 InteractivePage - 使用已连接设备开始剧本:', {
        scriptId,
        device: connectedDevice?.name
      })

      // 在实际跳转路由之前，异步更新剧本使用次数
      scriptService.useScript(scriptId).catch(error => {
        console.error(`${t('errors.updateScriptUsageFailed')}`, error)
      })

      // 导航到播放器页面
      navigate('/interactive/player', {
        state: {
          scriptId: scriptId
        }
      })
    } catch (error) {
      console.error('使用已连接设备开始剧本失败:', error)
    }
  }

  // 开始剧本播放（新连接设备后）
  const startScript = () => {
    if (!selectedScriptId) return

    console.log('🔍 InteractivePage - 开始剧本，设备信息:', connectedDevice)

    // 在实际跳转路由之前，异步更新剧本使用次数
    scriptService.useScript(selectedScriptId).catch(error => {
      console.error(`${t('errors.updateScriptUsageFailed')}`, error)
    })

    // 导航到播放器页面，传递 scriptId，设备信息由全局状态管理
    navigate('/interactive/player', {
      state: {
        scriptId: selectedScriptId
      }
    })
  }

  // 关闭设备连接抽屉
  const handleCloseDrawer = () => {
    setIsDeviceDrawerOpen(false)
    setSelectedScriptId('')
  }

  return (
    <div className="flex-1 flex flex-col">
      <div className="size-full min-h-screen">
        {isLoading ? (
          <div className="container py-6 pb-20 flex items-center justify-center h-[calc(100vh-4rem)]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-pink-500 mx-auto mb-4" />
              <p className="text-muted-foreground">{t('loading')}</p>
            </div>
          </div>
        ) : error ? (
          <div className="container py-6 pb-20 flex items-center justify-center h-[calc(100vh-4rem)]">
            <div className="text-center max-w-md p-4 bg-red-900/20 rounded-lg border border-red-800/50">
              <svg
                className="w-10 h-10 text-red-500 mx-auto mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
              <h3 className="text-red-200 text-lg font-bold mb-2">{t('error.title')}</h3>
              <p className="text-red-300 mb-4">{error}</p>
              <button
                type="button"
                onClick={() => setError(null)}
                className="px-4 py-2 bg-red-700 hover:bg-red-600 text-white rounded"
              >
                {t('error.retry')}
              </button>
            </div>
          </div>
        ) : (
          <div className="py-4">
            <ScriptSelector onScriptSelect={handleScriptSelect} />
          </div>
        )}

        {/* 设备连接抽屉 */}
        <DeviceConnectDrawer
          isOpen={isDeviceDrawerOpen}
          onClose={handleCloseDrawer}
          onDeviceConnect={handleDeviceConnect}
          onSkip={handleSkipDevice}
          supportedDevices={supportedDevices}
          isLoadingDevices={isLoadingDevices}
        />
      </div>
    </div>
  )
}

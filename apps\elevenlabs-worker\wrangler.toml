name = "elevenlabs-worker"
main = "src/index.ts"
compatibility_date = "2024-12-20"

# 环境变量配置
[vars]
NODE_ENV = "production"

# KV 存储配置（用于账号和会话管理）
# 请用 wrangler kv:namespace create "ELEVENLABS_CACHE" 命令生成的实际 ID 替换下面的占位符
[[kv_namespaces]]
binding = "ELEVENLABS_CACHE"
preview_id = "107e4dd57e9544fc9c2570b07c64c60c"
id = "673ecdbbb09146219032ae45e637a852"

# 环境配置
[env.production]
vars = { NODE_ENV = "production" }

[env.staging]
vars = { NODE_ENV = "staging" }

# 开发环境
[env.development]
vars = { NODE_ENV = "development" } 

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true

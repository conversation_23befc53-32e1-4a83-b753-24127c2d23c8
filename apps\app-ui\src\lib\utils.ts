import { clsx, type ClassValue } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 检测当前应用是否在 Capacitor 环境中运行
 * @returns 如果在 Capacitor 环境中返回 true，否则返回 false
 */
export function isCapacitorEnvironment(): boolean {
  try {
    // 检查是否在web环境中
    if (typeof window === 'undefined') return false

    // 判断是否在原生平台上运行
    const capacitorPlatform = (window as any)?.Capacitor?.getPlatform?.()

    // 如果平台是 'web'，则说明是在浏览器中运行的，而不是通过 Capacitor 的原生容器
    // 如果平台是 'ios' 或 'android'，则说明是在 Capacitor 的原生容器中运行
    return capacitorPlatform && capacitorPlatform !== 'web'
  } catch (e) {
    console.error('检测 Capacitor 环境出错:', e)
    return false
  }
}

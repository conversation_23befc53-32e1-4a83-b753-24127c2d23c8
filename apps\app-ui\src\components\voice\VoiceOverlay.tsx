import React, { useState, useRef, useCallback, useEffect } from 'react'
import { Button, Progress } from '@heroui/react'
import { Icon } from '@iconify/react'
import cx from 'classnames'
import { useVoiceRecorder } from '@/hooks/useVoiceRecorder'
import { useAudioPermission } from '@/hooks/useAudioPermission'
import { useSpeechToText } from '@/hooks/useSpeechToText'
import { AudioWaveform } from './AudioWaveform'
import { VoicePermissionGuide } from './VoicePermissionGuide'
import { useTranslation } from 'react-i18next'

interface VoiceOverlayProps {
  /** 是否显示语音录制界面 */
  isOpen: boolean
  /** 关闭语音录制界面 */
  onClose: () => void
  /** 语音转文本完成回调 */
  onTextResult: (text: string) => void
  /** 最大录制时长(秒) */
  maxDuration?: number
  /** 是否禁用 */
  disabled?: boolean
}

/**
 * 全屏覆盖式语音界面
 * 提供沉浸式的语音录制体验
 */
export function VoiceOverlay({
  isOpen,
  onClose,
  onTextResult,
  maxDuration = 60,
  disabled = false
}: VoiceOverlayProps) {
  // 国际化
  const { t } = useTranslation('voice')
  // 权限管理
  const { state: permissionStatus, requestPermission } = useAudioPermission()

  // 录制管理
  const voiceRecorderOptions = React.useMemo(() => ({ maxDuration }), [maxDuration])
  const {
    status: recordingStatus,
    duration,
    volume,
    audioBlob,
    startRecording,
    stopRecording,
    reset: resetRecording
  } = useVoiceRecorder(voiceRecorderOptions)

  // 语音转文本
  const {
    convert: convertToText,
    status: convertStatus,
    progress,
    error: convertError
  } = useSpeechToText()

  const isConverting = convertStatus === 'uploading' || convertStatus === 'processing'

  // 状态管理
  const [isLongPress, setIsLongPress] = useState(false)
  const [cancelGesture, setCancelGesture] = useState(false)
  const [showRetryOptions, setShowRetryOptions] = useState(false)

  // 交互管理
  const longPressTimer = useRef<ReturnType<typeof setTimeout> | null>(null)
  const touchStartY = useRef<number>(0)
  const overlayRef = useRef<HTMLDivElement>(null)

  // 格式化录制时长显示
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // 处理权限请求
  const handlePermissionRequest = useCallback(async () => {
    const granted = await requestPermission()
    return granted
  }, [requestPermission])

  // 开始录制
  const handleStartRecording = useCallback(async () => {
    if (disabled || recordingStatus !== 'idle') return

    if (permissionStatus !== 'granted') {
      const granted = await handlePermissionRequest()
      if (!granted) return
    }

    try {
      await startRecording()
    } catch (error) {
      console.error(`${t('recorder.error.start_failed')}:`, error)
    }
  }, [disabled, recordingStatus, permissionStatus, startRecording, handlePermissionRequest])

  // 停止录制
  const handleStopRecording = useCallback(() => {
    if (recordingStatus === 'recording') {
      stopRecording()
      setShowRetryOptions(true)
    }
  }, [recordingStatus, stopRecording])

  // 取消录制
  const handleCancelRecording = useCallback(() => {
    resetRecording()
    setShowRetryOptions(false)
    setCancelGesture(false)
    onClose()
  }, [resetRecording, onClose])

  // 发送录制内容
  const handleSendRecording = useCallback(async () => {
    if (!audioBlob) return

    try {
      const text = await convertToText(audioBlob)
      if (text) {
        onTextResult(text)
        resetRecording()
        setShowRetryOptions(false)
        onClose()
      }
    } catch (error) {
      console.error(`${t('recorder.error.conversion_failed')}:`, error)
    }
  }, [audioBlob, convertToText, onTextResult, resetRecording, onClose])

  // 重新录制
  const handleRetryRecording = useCallback(() => {
    resetRecording()
    setShowRetryOptions(false)
    setCancelGesture(false)
    // 自动开始新的录制
    setTimeout(() => {
      handleStartRecording()
    }, 100)
  }, [resetRecording, handleStartRecording])

  // 长按开始 - 支持防误触
  const handleTouchStart = useCallback(
    (e: React.TouchEvent | React.MouseEvent) => {
      if (disabled || recordingStatus !== 'idle') return

      const startY = 'touches' in e ? e.touches[0].clientY : (e as React.MouseEvent).clientY
      touchStartY.current = startY

      longPressTimer.current = setTimeout(() => {
        setIsLongPress(true)
        handleStartRecording()
        // 触觉反馈 (移动端)
        if ('vibrate' in navigator) {
          navigator.vibrate(50)
        }
      }, 150) // 150ms判定为长按，比普通点击稍快
    },
    [disabled, recordingStatus, handleStartRecording]
  )

  // 长按结束 - 智能处理松开太快的情况
  const handleTouchEnd = useCallback(
    (e: React.TouchEvent | React.MouseEvent) => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
        longPressTimer.current = null
      }

      if (isLongPress) {
        if (cancelGesture) {
          handleCancelRecording()
        } else if (duration < 1) {
          // 录制时间太短，提示重新录制
          resetRecording()
          setIsLongPress(false)
          setCancelGesture(false)
        } else {
          handleStopRecording()
        }
        setIsLongPress(false)
      }

      setCancelGesture(false)
    },
    [
      isLongPress,
      cancelGesture,
      duration,
      handleCancelRecording,
      handleStopRecording,
      resetRecording
    ]
  )

  // 长按移动 - 检测上滑取消手势
  const handleTouchMove = useCallback(
    (e: React.TouchEvent | React.MouseEvent) => {
      if (!isLongPress) return

      const currentY = 'touches' in e ? e.touches[0].clientY : (e as React.MouseEvent).clientY
      const deltaY = touchStartY.current - currentY

      // 上滑超过60px判定为取消手势
      if (deltaY > 60) {
        setCancelGesture(true)
      } else {
        setCancelGesture(false)
      }
    },
    [isLongPress]
  )

  // 点击录制 (短按)
  const handleClick = useCallback(() => {
    if (isLongPress || disabled) return

    if (recordingStatus === 'idle') {
      handleStartRecording()
    } else if (recordingStatus === 'recording') {
      handleStopRecording()
    }
  }, [isLongPress, disabled, recordingStatus, handleStartRecording, handleStopRecording])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
      }
    }
  }, [])

  // 键盘快捷键支持 (空格键录制)
  useEffect(() => {
    if (!isOpen) return

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.code === 'Space' && !e.repeat) {
        e.preventDefault()
        handleStartRecording()
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.code === 'Space') {
        e.preventDefault()
        handleStopRecording()
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('keyup', handleKeyUp)
    }
  }, [isOpen, handleStartRecording, handleStopRecording])

  if (!isOpen) return null

  // 权限被拒绝时显示引导
  if (permissionStatus === 'denied') {
    return (
      <div
        className={cx(
          'fixed inset-0 bg-background/95 backdrop-blur-md z-50',
          'flex items-center justify-center p-6'
        )}
      >
        <VoicePermissionGuide onRetry={handlePermissionRequest} />
      </div>
    )
  }

  return (
    <div
      ref={overlayRef}
      className={cx(
        'fixed inset-0 bg-background/95 backdrop-blur-md z-50',
        'flex flex-col items-center justify-center',
        'transition-all duration-300'
      )}
    >
      {/* 关闭按钮 */}
      <Button
        isIconOnly
        variant="light"
        size="sm"
        className="absolute top-6 right-6"
        onPress={handleCancelRecording}
      >
        <Icon icon="material-symbols:close" className="text-xl" />
      </Button>

      {/* 语音录制区域 */}
      <div className="flex flex-col items-center space-y-8 max-w-sm w-full px-6">
        {/* 状态提示 */}
        <div className="text-center space-y-2">
          <h2 className="text-2xl font-semibold">
            {recordingStatus === 'idle' && !showRetryOptions && t('overlay.title.voice_input')}
            {recordingStatus === 'recording' && t('overlay.title.recording')}
            {recordingStatus === 'processing' && t('overlay.title.processing')}
            {isConverting && t('overlay.title.recognizing')}
            {showRetryOptions && t('overlay.title.recording_complete')}
          </h2>

          <p className="text-default-500">
            {recordingStatus === 'idle' && !showRetryOptions && t('overlay.desc.click_or_hold')}
            {recordingStatus === 'recording' &&
              (cancelGesture ? t('recorder.status.swipe_cancel') : t('overlay.desc.recording_duration', { duration: formatDuration(duration) }))}
            {recordingStatus === 'processing' && t('overlay.desc.processing_audio')}
            {isConverting && t('overlay.desc.recognizing_progress', { progress: Math.round(progress) })}
            {showRetryOptions && t('overlay.desc.recording_success')}
          </p>
        </div>

        {/* 主录制按钮 */}
        <div className="relative">
          <Button
            isIconOnly
            size="lg"
            radius="full"
            variant={recordingStatus === 'recording' ? 'solid' : 'flat'}
            color={recordingStatus === 'recording' ? 'danger' : 'primary'}
            isDisabled={disabled || isConverting}
            className={cx(
              'w-24 h-24 text-3xl transition-all duration-200',
              recordingStatus === 'recording' && 'animate-pulse scale-110',
              cancelGesture && 'bg-warning scale-95',
              'hover:scale-105 active:scale-95'
            )}
            onClick={handleClick}
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
            onTouchMove={handleTouchMove}
            onMouseDown={handleTouchStart}
            onMouseUp={handleTouchEnd}
            onMouseMove={handleTouchMove}
            onMouseLeave={handleTouchEnd}
          >
            {isConverting ? (
              <Icon icon="svg-spinners:3-dots-bounce" />
            ) : (
              <Icon
                icon={recordingStatus === 'recording' ? 'solar:stop-bold' : 'solar:microphone-bold'}
                className={recordingStatus === 'recording' ? 'text-white' : ''}
              />
            )}
          </Button>

          {/* 音频波形 */}
          {recordingStatus === 'recording' && (
            <div className="absolute -bottom-16 left-1/2 transform -translate-x-1/2">
              <AudioWaveform
                volume={volume}
                isActive={true}
                color={cancelGesture ? 'warning' : 'danger'}
                bars={7}
              />
            </div>
          )}
        </div>

        {/* 转换进度 */}
        {isConverting && (
          <div className="w-full space-y-2">
            <Progress
              value={progress}
              size="sm"
              color="primary"
              showValueLabel
              className="w-full"
            />
          </div>
        )}

        {/* 操作按钮组 - 录制完成后立即显示 */}
        {showRetryOptions && audioBlob && !isConverting && (
          <div className="flex flex-col space-y-4 w-full max-w-xs">
            {/* 主要操作按钮 */}
            <Button
              size="lg"
              variant="solid"
              color="primary"
              onPress={handleSendRecording}
              startContent={<Icon icon="solar:arrow-up-linear" width={20} />}
              className="w-full h-12"
            >
              {t('overlay.button.send')}
            </Button>

            {/* 次要操作按钮 */}
            <div className="flex space-x-3">
              <Button
                size="md"
                variant="flat"
                color="warning"
                onPress={handleRetryRecording}
                startContent={<Icon icon="solar:refresh-bold" width={16} />}
                className="flex-1"
              >
                {t('overlay.button.rerecord')}
              </Button>

              <Button
                size="md"
                variant="flat"
                color="default"
                onPress={handleCancelRecording}
                startContent={<Icon icon="solar:close-circle-linear" width={16} />}
                className="flex-1"
              >
                {t('overlay.button.cancel')}
              </Button>
            </div>
          </div>
        )}

        {/* 错误提示 */}
        {convertError && (
          <div className="bg-danger/10 rounded-lg p-3 w-full">
            <div className="flex items-center space-x-2">
              <Icon icon="solar:danger-bold" className="text-danger text-lg" />
              <div>
                <p className="text-sm text-danger font-medium">{t('overlay.error.conversion_failed')}</p>
                <p className="text-xs text-danger/80">{convertError}</p>
              </div>
            </div>
          </div>
        )}

        {/* 使用提示 */}
        {recordingStatus === 'idle' && !showRetryOptions && (
          <div className="text-center space-y-2">
            <p className="text-sm text-default-600">{t('overlay.tips.click_or_hold')}</p>
            <p className="text-xs text-default-400">
              {t('overlay.tips.usage_info', { duration: formatDuration(maxDuration) })}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

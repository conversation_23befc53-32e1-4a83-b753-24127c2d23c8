export const paymentMessages = {
  zh: {
    // 用户认证相关
    user_not_authenticated: '用户未认证',
    membership_plan_not_exist: '会员计划不存在',
    
    // 订阅状态检查
    downgrade_not_supported: '不支持降级操作，请选择更高等级的会员计划',
    already_same_tier_member: '您已经是该等级会员，无需重复订阅',
    
    // 支付订单创建
    create_payment_order_failed: '创建支付订单失败',
    
    // 支付回调处理
    wechat_callback_not_implemented: '微信支付回调暂未实现',
    wechat_callback_processing_failed: '微信支付回调处理失败',
    
    // 订单查询
    order_not_exist: '订单不存在',
    get_order_info_failed: '获取订单信息失败',
    
    // 支付状态查询
    query_payment_status_failed: '查询支付状态失败',
    
    // 调试信息
    get_debug_info_failed: '获取调试信息失败',
    
    // 会员激活
    payment_order_not_exist: '支付订单不存在',
    order_not_paid_cannot_activate: '订单未支付，无法激活会员',
    order_missing_plan_info: '订单缺少会员计划信息',
    membership_plan_not_exist_activation: '会员计划不存在',
    
    // 积分包处理
    points_package_not_exist: '积分包不存在',
    
    // 硬编码错误消息
    cannot_get_alipay_config: '无法获取Alipay配置信息'
  },
  'zh-TW': {
    // 使用者認證相關
    user_not_authenticated: '使用者未認證',
    membership_plan_not_exist: '會員方案不存在',
    
    // 訂閱狀態檢查
    downgrade_not_supported: '不支援降級操作，請選擇更高等級的會員方案',
    already_same_tier_member: '您已經是該等級會員，無需重複訂閱',
    
    // 付款訂單建立
    create_payment_order_failed: '建立付款訂單失敗',
    
    // 付款回呼處理
    wechat_callback_not_implemented: '微信付款回呼暫未實作',
    wechat_callback_processing_failed: '微信付款回呼處理失敗',
    
    // 訂單查詢
    order_not_exist: '訂單不存在',
    get_order_info_failed: '取得訂單資訊失敗',
    
    // 付款狀態查詢
    query_payment_status_failed: '查詢付款狀態失敗',
    
    // 調試資訊
    get_debug_info_failed: '取得調試資訊失敗',
    
    // 會員啟用
    payment_order_not_exist: '付款訂單不存在',
    order_not_paid_cannot_activate: '訂單未付款，無法啟用會員',
    order_missing_plan_info: '訂單缺少會員方案資訊',
    membership_plan_not_exist_activation: '會員方案不存在',
    
    // 點數包處理
    points_package_not_exist: '點數包不存在',
    
    // 硬編碼錯誤訊息
    cannot_get_alipay_config: '無法取得Alipay設定資訊'
  },
  ja: {
    // ユーザー認証関連
    user_not_authenticated: 'ユーザーが認証されていません',
    membership_plan_not_exist: 'メンバーシッププランが存在しません',
    
    // サブスクリプション状態チェック
    downgrade_not_supported: 'ダウングレードはサポートされていません。より上位のメンバーシッププランを選択してください',
    already_same_tier_member: 'すでにそのレベルのメンバーです',
    
    // 支払い注文作成
    create_payment_order_failed: '支払い注文の作成に失敗しました',
    
    // 支払いコールバック処理
    wechat_callback_not_implemented: 'WeChatペイメントのコールバックはまだ実装されていません',
    wechat_callback_processing_failed: 'WeChatペイメントのコールバック処理に失敗しました',
    
    // 注文照会
    order_not_exist: '注文が存在しません',
    get_order_info_failed: '注文情報の取得に失敗しました',
    
    // 支払い状態照会
    query_payment_status_failed: '支払い状態の照会に失敗しました',
    
    // デバッグ情報
    get_debug_info_failed: 'デバッグ情報の取得に失敗しました',
    
    // メンバーシップ有効化
    payment_order_not_exist: '支払い注文が存在しません',
    order_not_paid_cannot_activate: '注文が支払われていないため、メンバーシップを有効化できません',
    order_missing_plan_info: '注文にメンバーシッププラン情報がありません',
    membership_plan_not_exist_activation: 'メンバーシッププランが存在しません',
    
    // ポイントパッケージ処理
    points_package_not_exist: 'ポイントパッケージが存在しません',
    
    // ハードコードされたエラーメッセージ
    cannot_get_alipay_config: 'Alipayの設定情報を取得できません'
  },
  es: {
    // Relacionado con autenticación de usuario
    user_not_authenticated: 'Usuario no autenticado',
    membership_plan_not_exist: 'El plan de membresía no existe',
    
    // Verificación de estado de suscripción
    downgrade_not_supported: 'No se admite la degradación, por favor seleccione un plan de membresía de nivel superior',
    already_same_tier_member: 'Ya tiene este nivel de membresía',
    
    // Creación de orden de pago
    create_payment_order_failed: 'Error al crear la orden de pago',
    
    // Procesamiento de callback de pago
    wechat_callback_not_implemented: 'El callback de pago de WeChat aún no está implementado',
    wechat_callback_processing_failed: 'Error en el procesamiento del callback de pago de WeChat',
    
    // Consulta de orden
    order_not_exist: 'La orden no existe',
    get_order_info_failed: 'Error al obtener la información de la orden',
    
    // Consulta de estado de pago
    query_payment_status_failed: 'Error al consultar el estado del pago',
    
    // Información de depuración
    get_debug_info_failed: 'Error al obtener la información de depuración',
    
    // Activación de membresía
    payment_order_not_exist: 'La orden de pago no existe',
    order_not_paid_cannot_activate: 'La orden no está pagada, no se puede activar la membresía',
    order_missing_plan_info: 'La orden no tiene información del plan de membresía',
    membership_plan_not_exist_activation: 'El plan de membresía no existe',
    
    // Procesamiento de paquete de puntos
    points_package_not_exist: 'El paquete de puntos no existe',
    
    // Mensajes de error codificados
    cannot_get_alipay_config: 'No se puede obtener la información de configuración de Alipay'
  },
  en: {
    // User authentication
    user_not_authenticated: 'User not authenticated',
    membership_plan_not_exist: 'Membership plan does not exist',
    
    // Subscription status check
    downgrade_not_supported: 'Downgrading isn\'t allowed. Please choose a higher membership tier',
    already_same_tier_member: 'You already have this membership level',
    
    // Payment order creation
    create_payment_order_failed: 'Failed to create payment order',
    
    // Payment callback processing
    wechat_callback_not_implemented: 'WeChat payment callback not implemented yet',
    wechat_callback_processing_failed: 'WeChat payment callback processing failed',
    
    // Order query
    order_not_exist: 'Order does not exist',
    get_order_info_failed: 'Failed to get order information',
    
    // Payment status query
    query_payment_status_failed: 'Failed to query payment status',
    
    // Debug information
    get_debug_info_failed: 'Failed to get debug information',
    
    // Membership activation
    payment_order_not_exist: 'Payment order does not exist',
    order_not_paid_cannot_activate: 'Order not paid, cannot activate membership',
    order_missing_plan_info: 'Order missing membership plan information',
    membership_plan_not_exist_activation: 'Membership plan does not exist',
    
    // Points package processing
    points_package_not_exist: 'Points package does not exist',
    
    // Hardcoded error messages
    cannot_get_alipay_config: 'Unable to get Alipay configuration information'
  }
}
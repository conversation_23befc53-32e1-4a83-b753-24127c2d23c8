import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import type { Env } from '@/types/env'
import { ApiError } from '@/types/api'
import type { ScriptContent } from '@/types/script'
import { authMiddleware } from '@/middleware/auth'
import { languageMiddleware } from '@/middleware/language'
import type { SupportedLanguage } from '@/i18n/config'
import type { Context } from 'hono'
import { getUserBySupabaseId } from '@/lib/db/queries/user'
import {
  getPublicScripts,
  getScriptById,
  createScript,
  updateScriptById,
  deleteScriptById,
  incrementScriptUsage,
  getScriptCategories,
  createScriptUsage,
  getUserScriptUsageHistory,
  updateScriptRating
} from '@/lib/db/queries/script'
import {
  checkScriptPurchase,
  createScriptPurchase,
  getUserPurchasedScriptIds,
  markScriptDownloaded,
  purchaseScriptWithPoints
} from '@/lib/db/queries/script-purchase'

// 获取国际化函数的辅助函数
function getI18n(
  c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>
): (key: string, params?: Record<string, string | number>) => string {
  return c.get('t')
}

// 创建路由实例
const app = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// 辅助函数：从Supabase用户上下文解析本地用户ID
async function resolveLocalUserId(c: any): Promise<string> {
  const t = getI18n(c)
  const supabaseUser = c.get('user')
  if (!supabaseUser) {
    throw new Error(t('user_not_authenticated'))
  }

  // 获取数据库用户信息
  const dbUser = await getUserBySupabaseId(c.env, supabaseUser.id)
  if (!dbUser) {
    throw new Error(t('user_data_not_found'))
  }

  return dbUser.id
}

// ==================== 验证模式 ====================

// 获取剧本列表的查询参数验证
const getScriptsQuerySchema = z.object({
  limit: z.coerce.number().optional().default(20),
  offset: z.coerce.number().optional().default(0),
  category: z.string().optional(),
  search: z.string().optional(),
  sortBy: z.enum(['latest', 'popular', 'rating']).optional().default('latest')
})

// 创建剧本的请求体验证 - 静态版本
const createScriptSchema = z.object({
  title: z.string().min(1).max(100),
  description: z.string().min(1),
  coverImage: z.string().url(),
  duration: z.string().min(1),
  tags: z.array(z.string()).optional().default([]),
  category: z.string().optional(),
  content: z.any().optional(),
  isPublic: z.boolean().optional().default(true),
  isPremium: z.boolean().optional().default(false)
})

// 更新剧本的请求体验证
const updateScriptSchema = z.object({
  title: z.string().min(1).max(100).optional(),
  description: z.string().min(1).optional(),
  coverImage: z.string().url().optional(),
  duration: z.string().min(1).optional(),
  tags: z.array(z.string()).optional(),
  category: z.string().optional(),
  content: z.any().optional(),
  isPublic: z.boolean().optional(),
  isPremium: z.boolean().optional(),
  isActive: z.boolean().optional()
})

// 创建剧本使用记录的请求体验证 - 静态版本
const createScriptUsageSchema = z.object({
  scriptId: z.string().uuid(),
  chatId: z.string().uuid().optional(),
  duration: z.number().min(0).optional(),
  rating: z.number().min(1).max(5).optional(),
  feedback: z.string().optional()
})

// ==================== 路由处理器 ====================

/**
 * 获取公开剧本列表
 * GET /api/scripts
 */
app.get('/', languageMiddleware, zValidator('query', getScriptsQuerySchema), async c => {
  try {
    const query = c.req.valid('query')

    const scripts = await getPublicScripts(c.env, {
      limit: query.limit,
      offset: query.offset,
      category: query.category,
      search: query.search,
      sortBy: query.sortBy
    })

    // 移除敏感内容，列表页面不返回content和audioUrl
    const filteredScripts = scripts.map(script => ({
      id: script.id,
      title: script.title,
      description: script.description,
      coverImage: script.coverImage,
      duration: script.duration,
      tags: script.tags,
      category: script.category,
      pointsCost: script.pointsCost,
      isPublic: script.isPublic,
      isPremium: script.isPremium,
      usageCount: script.usageCount,
      rating: script.rating,
      stageCount: script.stageCount,
      totalDuration: script.totalDuration,
      createdAt: script.createdAt,
      updatedAt: script.updatedAt
      // 不返回 content 和 audioUrl
    }))

    return c.json({
      success: true,
      data: filteredScripts,
      pagination: {
        limit: query.limit,
        offset: query.offset,
        total: scripts.length // 实际项目中应该返回总数
      }
    })
  } catch (error) {
    console.error('获取剧本列表失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_list_failed'))
  }
})

/**
 * 获取剧本分类列表
 * GET /api/scripts/categories
 */
app.get('/categories', languageMiddleware, async c => {
  try {
    const categories = await getScriptCategories(c.env)

    return c.json({
      success: true,
      data: categories
    })
  } catch (error) {
    console.error('获取剧本分类失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_categories_failed'))
  }
})

/**
 * 获取用户已购买的剧本ID列表
 * GET /api/scripts/purchased
 */
app.get('/purchased', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)

    const purchasedScriptIds = await getUserPurchasedScriptIds(c.env, userId)

    return c.json({
      success: true,
      data: {
        scriptIds: purchasedScriptIds
      }
    })
  } catch (error) {
    console.error('获取已购买剧本列表失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_purchased_list_failed'))
  }
})

/**
 * 创建剧本使用记录
 * POST /api/scripts/usage
 */
app.post(
  '/usage',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createScriptUsageSchema),
  async c => {
    try {
      const data = c.req.valid('json')
      const userId = await resolveLocalUserId(c)
      const t = getI18n(c)

      // 验证剧本ID
      if (!data.scriptId || data.scriptId.trim().length === 0) {
        throw new ApiError(400, t('script_id_required'))
      }

      const [usage] = await createScriptUsage(c.env, {
        ...data,
        userId
      })

      // 如果有评分，更新剧本的平均评分
      if (data.rating) {
        await updateScriptRating(c.env, data.scriptId)
      }

      return c.json(
        {
          success: true,
          data: usage,
          message: t('script_usage_created')
        },
        201
      )
    } catch (error) {
      console.error('创建剧本使用记录失败:', error)
      const t = getI18n(c)
      if (error instanceof ApiError) {
        throw error
      }
      throw new ApiError(500, t('script_usage_create_failed'))
    }
  }
)

/**
 * 获取用户的剧本使用历史
 * GET /api/scripts/usage/history
 */
app.get('/usage/history', authMiddleware, languageMiddleware, async c => {
  try {
    const userId = await resolveLocalUserId(c)

    const limit = Number.parseInt(c.req.query('limit') || '20', 10)
    const offset = Number.parseInt(c.req.query('offset') || '0', 10)

    const history = await getUserScriptUsageHistory(c.env, userId, {
      limit,
      offset
    })

    return c.json({
      success: true,
      data: history,
      pagination: {
        limit,
        offset,
        total: history.length // 实际项目中应该返回总数
      }
    })
  } catch (error) {
    console.error('获取剧本使用历史失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_usage_history_failed'))
  }
})

/**
 * 根据ID获取剧本详情
 * GET /api/scripts/:id
 */
app.get('/:id', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const t = getI18n(c)

    if (!id) {
      throw new ApiError(400, t('script_id_required'))
    }

    const script = await getScriptById(c.env, id)

    if (!script) {
      throw new ApiError(404, t('script_not_found'))
    }

    return c.json({
      success: true,
      data: script
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('获取剧本详情失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_detail_failed'))
  }
})

/**
 * 获取剧本详细内容（包含阶段、对话等）
 * GET /api/scripts/:id/content
 */
app.get('/:id/content', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const t = getI18n(c)

    if (!id) {
      throw new ApiError(400, t('script_id_required'))
    }

    const script = await getScriptById(c.env, id)

    if (!script) {
      throw new ApiError(404, t('script_not_found'))
    }

    // 返回剧本的详细内容
    return c.json({
      success: true,
      data: {
        id: script.id,
        title: script.title,
        content: script.content,
        audioUrl: script.audioUrl,
        totalDuration: script.totalDuration,
        stageCount: script.stageCount
      }
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('获取剧本内容失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_content_failed'))
  }
})

/**
 * 获取剧本阶段列表
 * GET /api/scripts/:id/stages
 */
app.get('/:id/stages', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const t = getI18n(c)

    if (!id) {
      throw new ApiError(400, t('script_id_required'))
    }

    const script = await getScriptById(c.env, id)

    if (!script) {
      throw new ApiError(404, t('script_not_found'))
    }

    const content = script.content as ScriptContent
    if (!content || !content.stages) {
      throw new ApiError(400, t('script_content_incomplete'))
    }

    // 返回阶段列表（不包含详细对话，减少数据传输）
    const stages = content.stages.map(stage => ({
      stage: stage.stage,
      stageTitle: stage.stageTitle,
      duration: stage.duration,
      pics: stage.pics,
      dialogueCount: stage.dialogues?.length || 0
    }))

    return c.json({
      success: true,
      data: {
        scriptId: script.id,
        title: script.title,
        totalStages: script.stageCount,
        stages
      }
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('获取剧本阶段失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_stages_failed'))
  }
})

/**
 * 获取特定阶段详情
 * GET /api/scripts/:id/stages/:stageId
 */
app.get('/:id/stages/:stageId', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const stageId = Number.parseInt(c.req.param('stageId'))
    const t = getI18n(c)

    if (!id) {
      throw new ApiError(400, t('script_id_required'))
    }

    if (isNaN(stageId)) {
      throw new ApiError(400, t('stage_id_invalid'))
    }

    const script = await getScriptById(c.env, id)

    if (!script) {
      throw new ApiError(404, t('script_not_found'))
    }

    const content = script.content as ScriptContent
    if (!content || !content.stages) {
      throw new ApiError(400, t('script_content_incomplete'))
    }

    const stage = content.stages.find(s => s.stage === stageId)

    if (!stage) {
      throw new ApiError(404, t('stage_not_found'))
    }

    return c.json({
      success: true,
      data: {
        scriptId: script.id,
        scriptTitle: script.title,
        stage
      }
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('获取阶段详情失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_stage_detail_failed'))
  }
})

/**
 * 创建新剧本
 * POST /api/scripts
 */
app.post(
  '/',
  authMiddleware,
  languageMiddleware,
  zValidator('json', createScriptSchema),
  async c => {
    try {
      const data = c.req.valid('json')
      const userId = await resolveLocalUserId(c)
      const t = getI18n(c)

      const [newScript] = await createScript(c.env, {
        ...data,
        createdBy: userId
      })

      return c.json(
        {
          success: true,
          data: newScript,
          message: t('script_created')
        },
        201
      )
    } catch (error) {
      console.error('创建剧本失败:', error)
      const t = getI18n(c)
      throw new ApiError(500, t('script_create_failed'))
    }
  }
)

/**
 * 更新剧本
 * PUT /api/scripts/:id
 */
app.put('/:id', languageMiddleware, zValidator('json', updateScriptSchema), async c => {
  try {
    const id = c.req.param('id')
    const data = c.req.valid('json')
    const t = getI18n(c)

    if (!id) {
      throw new ApiError(400, t('script_id_required'))
    }

    // 检查剧本是否存在
    const existingScript = await getScriptById(c.env, id)
    if (!existingScript) {
      throw new ApiError(404, t('script_not_found'))
    }

    // TODO: 检查用户权限
    // const userId = c.get('userId')
    // if (existingScript.createdBy !== userId) {
    //   throw new ApiError(403, '无权限修改此剧本')
    // }

    const [updatedScript] = await updateScriptById(c.env, id, data)

    return c.json({
      success: true,
      data: updatedScript,
      message: t('script_updated')
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('更新剧本失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_update_failed'))
  }
})

/**
 * 删除剧本（软删除）
 * DELETE /api/scripts/:id
 */
app.delete('/:id', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const t = getI18n(c)

    if (!id) {
      throw new ApiError(400, t('script_id_required'))
    }

    // 检查剧本是否存在
    const existingScript = await getScriptById(c.env, id)
    if (!existingScript) {
      throw new ApiError(404, t('script_not_found'))
    }

    // TODO: 检查用户权限
    // const userId = c.get('userId')
    // if (existingScript.createdBy !== userId) {
    //   throw new ApiError(403, '无权限删除此剧本')
    // }

    await deleteScriptById(c.env, id)

    return c.json({
      success: true,
      message: t('script_deleted')
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('删除剧本失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_delete_failed'))
  }
})

/**
 * 使用剧本（增加使用次数）
 * POST /api/scripts/:id/use
 */
app.post('/:id/use', languageMiddleware, async c => {
  try {
    const id = c.req.param('id')
    const t = getI18n(c)

    if (!id) {
      throw new ApiError(400, t('script_id_required'))
    }

    // 检查剧本是否存在
    const script = await getScriptById(c.env, id)
    if (!script) {
      throw new ApiError(404, t('script_not_found'))
    }

    // 增加使用次数
    await incrementScriptUsage(c.env, id)

    return c.json({
      success: true,
      message: t('script_usage_updated')
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('更新剧本使用次数失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_usage_update_failed'))
  }
})

/**
 * 购买剧本
 * POST /api/scripts/:id/purchase
 */
app.post('/:id/purchase', authMiddleware, languageMiddleware, async c => {
  try {
    const scriptId = c.req.param('id')
    const t = getI18n(c)

    if (!scriptId) {
      throw new ApiError(400, t('script_id_required'))
    }

    const userId = await resolveLocalUserId(c)

    // 并行检查剧本和购买状态
    const [script, existingPurchase] = await Promise.all([
      getScriptById(c.env, scriptId),
      checkScriptPurchase(c.env, userId, scriptId)
    ])

    if (!script) {
      throw new ApiError(404, t('script_not_found'))
    }

    if (existingPurchase) {
      throw new ApiError(400, t('script_already_purchased'))
    }

    // 免费剧本直接创建购买记录（无需积分交易记录）
    if (script.pointsCost <= 0) {
      const [purchase] = await createScriptPurchase(c.env, {
        userId,
        scriptId,
        pointsCost: 0
        // 免费剧本不设置transactionId，避免外键约束问题
      })

      return c.json({
        success: true,
        data: {
          purchase,
          script: {
            id: script.id,
            title: script.title,
            description: script.description,
            coverImage: script.coverImage
          }
        },
        message: t('script_free_success')
      })
    }

    // 使用优化的购买函数（包含事务处理）
    const purchaseResult = await purchaseScriptWithPoints(c.env, {
      userId,
      scriptId,
      pointsCost: script.pointsCost,
      scriptTitle: script.title
    })

    if (!purchaseResult.success) {
      throw new ApiError(400, purchaseResult.error || t('script_purchase_failed'))
    }

    return c.json({
      success: true,
      data: {
        purchase: purchaseResult.purchase,
        script: {
          id: script.id,
          title: script.title,
          description: script.description,
          coverImage: script.coverImage
        },
        remainingPoints: purchaseResult.remainingPoints
      },
      message: t('script_purchase_success')
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('购买剧本失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_purchase_failed'))
  }
})

/**
 * 检查剧本购买状态
 * GET /api/scripts/:id/purchase-status
 */
app.get('/:id/purchase-status', authMiddleware, languageMiddleware, async c => {
  try {
    const scriptId = c.req.param('id')
    const t = getI18n(c)

    if (!scriptId) {
      throw new ApiError(400, t('script_id_required'))
    }

    const userId = await resolveLocalUserId(c)

    const purchase = await checkScriptPurchase(c.env, userId, scriptId)

    return c.json({
      success: true,
      data: {
        isPurchased: !!purchase,
        purchase: purchase || null
      }
    })
  } catch (error) {
    console.error('检查购买状态失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_purchase_status_failed'))
  }
})

/**
 * 下载剧本内容
 * POST /api/scripts/:id/download
 */
app.post('/:id/download', authMiddleware, languageMiddleware, async c => {
  try {
    const scriptId = c.req.param('id')
    const t = getI18n(c)

    if (!scriptId) {
      throw new ApiError(400, t('script_id_required'))
    }

    const userId = await resolveLocalUserId(c)

    // 检查是否已购买
    const purchase = await checkScriptPurchase(c.env, userId, scriptId)
    if (!purchase) {
      throw new ApiError(403, t('script_not_purchased'))
    }

    // 获取剧本详细内容
    const script = await getScriptById(c.env, scriptId)
    if (!script) {
      throw new ApiError(404, t('script_not_found'))
    }

    // 标记为已下载
    await markScriptDownloaded(c.env, userId, scriptId)

    return c.json({
      success: true,
      data: {
        id: script.id,
        title: script.title,
        content: script.content,
        audioUrl: script.audioUrl,
        totalDuration: script.totalDuration,
        stageCount: script.stageCount
      },
      message: t('script_download_success')
    })
  } catch (error) {
    if (error instanceof ApiError) {
      throw error
    }
    console.error('下载剧本内容失败:', error)
    const t = getI18n(c)
    throw new ApiError(500, t('script_download_failed'))
  }
})

export default app

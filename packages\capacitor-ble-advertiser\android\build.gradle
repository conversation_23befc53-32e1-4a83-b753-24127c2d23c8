plugins {
    id 'com.android.library'
}

android {
    namespace "com.rcapp.bleadvertiser" // 请确认这个命名空间是否正确
    compileSdk 34

    defaultConfig {
        minSdk 22
        targetSdk 34
        consumerProguardFiles "consumer-rules.pro"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    lintOptions {
        abortOnError false
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation project(':capacitor-android') // 依赖于 Capacitor Android
    // 如果你的插件有其他特定的 .jar 或 .aar 依赖，请在这里添加
    // 例如: implementation files('libs/your-library.jar')
} 
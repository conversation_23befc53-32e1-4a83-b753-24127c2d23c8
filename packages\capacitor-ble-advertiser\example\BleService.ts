import { BleAdvertiser } from 'capacitor-ble-advertiser';

/**
 * 蓝牙广播服务类
 * 用于发送蓝牙广播命令到设备
 */
export class BleService {
  private static instance: BleService;
  private initialized = false;
  private activeInstanceIds: number[] = [];

  /**
   * 获取单例实例
   */
  public static getInstance(): BleService {
    if (!BleService.instance) {
      BleService.instance = new BleService();
    }
    return BleService.instance;
  }

  /**
   * 私有构造函数，防止外部实例化
   */
  private constructor() {}

  /**
   * 初始化蓝牙服务
   * @returns 是否初始化成功
   */
  public async initialize(): Promise<boolean> {
    try {
      if (this.initialized) {
        console.log('蓝牙服务已初始化');
        return true;
      }

      console.log('正在初始化蓝牙服务...');
      const { success } = await BleAdvertiser.initialize();

      if (success) {
        console.log('蓝牙服务初始化成功');
        this.initialized = true;
        return true;
      } else {
        console.error('蓝牙服务初始化失败');
        return false;
      }
    } catch (error) {
      console.error('蓝牙服务初始化错误:', error);
      return false;
    }
  }

  /**
   * 检查蓝牙是否启用
   * @returns 蓝牙是否已启用
   */
  public async isBluetoothEnabled(): Promise<boolean> {
    try {
      const { enabled } = await BleAdvertiser.isBluetoothEnabled();
      console.log(`蓝牙状态: ${enabled ? '已启用' : '未启用'}`);
      return enabled;
    } catch (error) {
      console.error('检查蓝牙状态错误:', error);
      return false;
    }
  }

  /**
   * 发送命令到设备
   * @param command 十六进制命令字符串
   * @param mode 广播模式 (0=平衡, 1=低延迟, 2=低功耗)
   * @returns 是否发送成功
   */
  public async sendCommand(
    command: string,
    mode: number = 1,
  ): Promise<boolean> {
    try {
      // 确保蓝牙已初始化
      if (!this.initialized) {
        const initSuccess = await this.initialize();
        if (!initSuccess) {
          console.error('无法发送命令，蓝牙服务未初始化');
          return false;
        }
      }

      // 检查蓝牙是否启用
      const isEnabled = await this.isBluetoothEnabled();
      if (!isEnabled) {
        console.error('无法发送命令，蓝牙未启用');
        return false;
      }

      // 发送广播
      console.log(`正在广播命令: ${command}`);
      const { success, instanceId } = await BleAdvertiser.startAdvertising({
        mode,
        manufacturerId: 255,
        data: command,
        instanceId: Date.now() % 10000,
      });

      if (success && instanceId) {
        console.log(`命令广播成功，实例ID: ${instanceId}`);
        this.activeInstanceIds.push(instanceId);

        // 3秒后自动停止广播
        setTimeout(() => {
          this.stopCommand(instanceId);
        }, 3000);

        return true;
      } else {
        console.error('命令广播失败');
        return false;
      }
    } catch (error) {
      console.error('发送命令错误:', error);
      return false;
    }
  }

  /**
   * 停止特定命令广播
   * @param instanceId 广播实例ID
   * @returns 是否停止成功
   */
  public async stopCommand(instanceId: number): Promise<boolean> {
    try {
      console.log(`停止广播，实例ID: ${instanceId}`);
      const { success } = await BleAdvertiser.stopAdvertising({ instanceId });

      if (success) {
        console.log(`广播已停止，实例ID: ${instanceId}`);
        this.activeInstanceIds = this.activeInstanceIds.filter(
          (id) => id !== instanceId,
        );
        return true;
      } else {
        console.error(`停止广播失败，实例ID: ${instanceId}`);
        return false;
      }
    } catch (error) {
      console.error('停止广播错误:', error);
      return false;
    }
  }

  /**
   * 停止所有广播
   * @returns 是否停止成功
   */
  public async stopAllCommands(): Promise<boolean> {
    try {
      console.log('停止所有广播');
      const { success } = await BleAdvertiser.stopAllAdvertising();

      if (success) {
        console.log('所有广播已停止');
        this.activeInstanceIds = [];
        return true;
      } else {
        console.error('停止所有广播失败');
        return false;
      }
    } catch (error) {
      console.error('停止所有广播错误:', error);
      return false;
    }
  }
}

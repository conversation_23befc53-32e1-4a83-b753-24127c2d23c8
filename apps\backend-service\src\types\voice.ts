// 声音模块相关类型定义

export interface VoiceModelType {
  id: string;
  modelId: string;
  name: string;
  displayName: string;
  description?: string;
  gender: 'male' | 'female' | 'neutral';
  language: string;
  supportedLanguages?: string[];
  category?: string;
  tags?: string[];
  isActive: boolean;
  isPremium: boolean;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface VoiceSampleType {
  id: string;
  voiceModelId: string;
  language: string;
  sampleText: string;
  audioUrl?: string;
  duration?: number;
  fileSize?: number;
  isDefault: boolean;
  createdAt: Date;
}

export interface VoiceModelWithSamples extends VoiceModelType {
  samples: VoiceSampleType[];
}

export interface VoiceModelWithDefaultSample extends VoiceModelType {
  defaultSample: VoiceSampleType | null;
}

// API 请求/响应类型
export interface CreateVoiceModelRequest {
  modelId: string;
  name: string;
  displayName: string;
  description?: string;
  gender: 'male' | 'female' | 'neutral';
  language?: string;
  supportedLanguages?: string[];
  category?: string;
  tags?: string[];
  isPremium?: boolean;
  sortOrder?: number;
}

export interface UpdateVoiceModelRequest extends Partial<CreateVoiceModelRequest> {}

export interface CreateVoiceSampleRequest {
  language: string;
  sampleText: string;
  audioUrl?: string;
  duration?: number;
  fileSize?: number;
  isDefault?: boolean;
}

export interface UpdateVoiceSampleRequest extends Partial<CreateVoiceSampleRequest> {}

export interface VoiceModelListResponse {
  success: boolean;
  data: VoiceModelType[] | VoiceModelWithDefaultSample[];
}

export interface VoiceModelDetailResponse {
  success: boolean;
  data: VoiceModelType | VoiceModelWithSamples;
}

export interface VoiceSampleListResponse {
  success: boolean;
  data: VoiceSampleType[];
}

// 声音选项（用于前端显示）
export interface VoiceOption {
  value: string; // modelId
  label: string; // displayName
  description?: string;
  gender: 'male' | 'female' | 'neutral';
  isPremium: boolean;
  tags?: string[];
  defaultSample?: VoiceSampleType;
}

// 旧版本兼容性映射
export const LEGACY_VOICE_MAPPING = {
  soft: 'alloy', // 柔和的
  deep: 'echo', // 低沉的
  sweet: 'fable', // 甜美的
  husky: 'onyx', // 沙哑的
  energetic: 'nova', // 有活力的
  seductive: 'shimmer', // 诱惑的
  childish: 'fable', // 稚嫩的
  mature: 'echo', // 成熟的
} as const;

export type LegacyVoiceKey = keyof typeof LEGACY_VOICE_MAPPING;

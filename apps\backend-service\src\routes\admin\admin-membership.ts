import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import {
  getMembershipPlans,
  getMembershipPlanById,
  getUserSubscriptionHistory,
  getUserActiveSubscription,
  getPointsPackages,
  getPointsPackageById
} from '@/lib/db/queries/membership'
import { createSupabaseServiceClient } from '@/lib/supabase'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types'

const adminMembership = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 会员套餐管理 ====================

// 创建会员套餐验证schema
const createPlanSchema = z.object({
  name: z.string().min(1, '套餐名称不能为空'),
  description: z.string().optional(),
  price: z.number().min(0, '价格不能小于0'),
  durationDays: z.number().min(1, '有效期至少1天'),
  pointsIncluded: z.number().min(0, '积分不能小于0'),
  isActive: z.boolean().default(true),
  sortOrder: z.number().optional(),
  features: z.object({
    maxCharacters: z.number().optional(),
    canCreatePublicCharacters: z.boolean().optional(),
    canUseCustomVoices: z.boolean().optional(),
    canAccessPremiumTemplates: z.boolean().optional()
  }).optional()
})

// 创建会员套餐
adminMembership.post('/plans', authMiddleware, zValidator('json', createPlanSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const planData = c.req.valid('json')
    const env = c.env

    console.log('🔍 [ADMIN-MEMBERSHIP] 创建会员套餐:', planData)

    // 使用Supabase创建套餐
    const supabase = getSupabase(env)

    const result = await supabase
      .from(TABLE_NAMES.membershipPlan)
      .insert({
        name: planData.name,
        description: planData.description,
        price: planData.price.toString(),
        duration_days: planData.durationDays,
        points_included: planData.pointsIncluded,
        is_active: planData.isActive,
        sort_order: planData.sortOrder || 0,
        features: planData.features || {}
      })
      .select()
      .single()

    const { data: newPlan, error } = result
    if (error) throw error

    console.log('✅ [ADMIN-MEMBERSHIP] 套餐创建成功:', newPlan.id)

    return c.json({
      success: true,
      data: {
        id: newPlan.id,
        name: newPlan.name,
        description: newPlan.description,
        price: newPlan.price,
        durationDays: newPlan.duration_days,
        pointsIncluded: newPlan.points_included,
        isActive: newPlan.is_active,
        sortOrder: newPlan.sort_order,
        features: newPlan.features || {},
        createdAt: newPlan.created_at,
        updatedAt: newPlan.updated_at
      },
      message: '套餐创建成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-MEMBERSHIP] 创建套餐失败:', error)
    return c.json(
      {
        success: false,
        message: '创建套餐失败'
      },
      500
    )
  }
})

// 更新会员套餐
adminMembership.put(
  '/plans/:id',
  authMiddleware,
  zValidator('json', createPlanSchema.partial()),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const planId = c.req.param('id')
      const updateData = c.req.valid('json')
      const env = c.env

      console.log('🔍 [ADMIN-MEMBERSHIP] 更新会员套餐:', planId, updateData)

      const supabase = getSupabase(env)

      // 构建更新数据
      const updateFields: any = {}
      if (updateData.name !== undefined) updateFields.name = updateData.name
      if (updateData.description !== undefined) updateFields.description = updateData.description
      if (updateData.price !== undefined) updateFields.price = updateData.price.toString()
      if (updateData.durationDays !== undefined)
        updateFields.duration_days = updateData.durationDays
      if (updateData.pointsIncluded !== undefined)
        updateFields.points_included = updateData.pointsIncluded
      if (updateData.isActive !== undefined) updateFields.is_active = updateData.isActive
      if (updateData.sortOrder !== undefined) updateFields.sort_order = updateData.sortOrder
      if (updateData.features !== undefined) updateFields.features = updateData.features

      const result = await supabase
        .from(TABLE_NAMES.membershipPlan)
        .update(updateFields)
        .eq('id', planId)
        .select()
        .single()

      const { data: updatedPlan, error } = result
      if (error || !updatedPlan) {
        return c.json({ success: false, message: '套餐不存在' }, 404)
      }

      console.log('✅ [ADMIN-MEMBERSHIP] 套餐更新成功:', updatedPlan.id)

      return c.json({
        success: true,
        data: {
          id: updatedPlan.id,
          name: updatedPlan.name,
          description: updatedPlan.description,
          price: updatedPlan.price,
          durationDays: updatedPlan.duration_days,
          pointsIncluded: updatedPlan.points_included,
          isActive: updatedPlan.is_active,
          sortOrder: updatedPlan.sort_order,
          features: updatedPlan.features || {},
          createdAt: updatedPlan.created_at,
          updatedAt: updatedPlan.updated_at
        },
        message: '套餐更新成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-MEMBERSHIP] 更新套餐失败:', error)
      return c.json(
        {
          success: false,
          message: '更新套餐失败'
        },
        500
      )
    }
  }
)

// 删除会员套餐
adminMembership.delete('/plans/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const planId = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-MEMBERSHIP] 删除会员套餐:', planId)

    const supabase = getSupabase(env)

    // 检查是否有用户正在使用此套餐
    const activeSubscriptions = await supabase
      .from(TABLE_NAMES.userSubscription)
      .select('id')
      .eq('plan_id', planId)
      .limit(1)

    if (activeSubscriptions.data && activeSubscriptions.data.length > 0) {
      return c.json(
        {
          success: false,
          message: '该套餐仍有用户订阅，无法删除'
        },
        400
      )
    }

    const result = await supabase
      .from(TABLE_NAMES.membershipPlan)
      .delete()
      .eq('id', planId)
      .select()
      .single()

    const { data: deletedPlan, error } = result
    if (error || !deletedPlan) {
      return c.json({ success: false, message: '套餐不存在' }, 404)
    }

    console.log('✅ [ADMIN-MEMBERSHIP] 套餐删除成功:', deletedPlan.id)

    return c.json({
      success: true,
      message: '套餐删除成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-MEMBERSHIP] 删除套餐失败:', error)
    return c.json(
      {
        success: false,
        message: '删除套餐失败'
      },
      500
    )
  }
})

// 获取所有会员套餐（管理员）
adminMembership.get('/plans', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env
    const plans = await getMembershipPlans(env)

    // 添加统计信息
    const planStats = await Promise.all(
      plans.map(async plan => {
        // 这里可以添加每个套餐的订阅统计
        return {
          ...plan,
          subscriberCount: 0, // TODO: 实现订阅者统计
          revenue: 0 // TODO: 实现收入统计
        }
      })
    )

    return c.json({
      success: true,
      data: planStats
    })
  } catch (error) {
    console.error('获取会员套餐失败:', error)
    return c.json(
      {
        success: false,
        message: '获取会员套餐失败'
      },
      500
    )
  }
})

// 获取会员套餐详情
adminMembership.get('/plans/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const planId = c.req.param('id')
    const env = c.env

    const plan = await getMembershipPlanById(env, planId)
    if (!plan) {
      return c.json(
        {
          success: false,
          message: '套餐不存在'
        },
        404
      )
    }

    return c.json({
      success: true,
      data: plan
    })
  } catch (error) {
    console.error('获取套餐详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取套餐详情失败'
      },
      500
    )
  }
})

// ==================== 会员统计数据 ====================

adminMembership.get('/stats', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📊 [ADMIN-MEMBERSHIP] 获取会员统计')

    // 获取会员套餐统计
    const plans = await getMembershipPlans(env)
    const activePlans = plans.filter(plan => plan.isActive)

    // 从数据库查询实际统计数据
    const supabase = getSupabase(env)

    // 今日时间范围
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // 最近30天时间
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)

    // 并行查询统计数据
    const [
      totalSubscriptionsResult,
      activeSubscriptionsResult,
      todayOrdersResult,
      monthlyOrdersResult
    ] = await Promise.all([
      // 总订阅数
      supabase.from(TABLE_NAMES.userSubscription).select('*', { count: 'exact', head: true }),

      // 活跃订阅数
      supabase
        .from(TABLE_NAMES.userSubscription)
        .select('*', { count: 'exact', head: true })
        .eq('status', 'active'),

      // 今日收入（从支付订单计算）
      supabase
        .from(TABLE_NAMES.paymentOrder)
        .select('amount')
        .eq('status', 'paid')
        .gte('created_at', today.toISOString())
        .not('plan_id', 'is', null),

      // 月度收入（最近30天）
      supabase
        .from(TABLE_NAMES.paymentOrder)
        .select('amount')
        .eq('status', 'paid')
        .gte('created_at', thirtyDaysAgo.toISOString())
        .not('plan_id', 'is', null)
    ])

    const todayRevenue =
      todayOrdersResult.data?.reduce(
        (sum, item) => sum + Number.parseFloat(item.amount || '0'),
        0
      ) || 0
    const monthlyRevenue =
      monthlyOrdersResult.data?.reduce(
        (sum, item) => sum + Number.parseFloat(item.amount || '0'),
        0
      ) || 0

    const stats = {
      totalPlans: plans.length,
      activePlans: activePlans.length,
      totalSubscriptions: totalSubscriptionsResult.count || 0,
      activeSubscriptions: activeSubscriptionsResult.count || 0,
      monthlyRevenue,
      todayRevenue
    }

    console.log('📊 [ADMIN-MEMBERSHIP] 统计结果:', stats)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('❌ [ADMIN-MEMBERSHIP] 获取会员统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取统计数据失败'
      },
      500
    )
  }
})

// ==================== 用户订阅管理 ====================

// 获取所有订阅列表（分页）
const subscriptionListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  status: z.enum(['all', 'active', 'expired', 'cancelled']).default('all'),
  planId: z.string().optional(),
  keyword: z.string().optional()
})

adminMembership.get(
  '/subscriptions',
  authMiddleware,
  zValidator('query', subscriptionListSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const { page, pageSize, status, planId, keyword } = c.req.valid('query')
      const env = c.env

      console.log('🔍 [ADMIN-MEMBERSHIP] 获取订阅列表:', {
        page,
        pageSize,
        status,
        planId,
        keyword
      })

      const supabase = getSupabase(env)

      // 基础查询 - 获取订阅及关联的用户和套餐信息
      let query = supabase
        .from(TABLE_NAMES.userSubscription)
        .select(
          `
          id,
          user_id,
          plan_id,
          start_date,
          end_date,
          status,
          auto_renew,
          payment_id,
          created_at,
          ${TABLE_NAMES.membershipPlan}!inner(name, price, duration_days, points_included),
          ${TABLE_NAMES.user}!inner(email)
        `
        )
        .order('created_at', { ascending: false })

      // 应用筛选条件
      if (status && status !== 'all') {
        query = query.eq('status', status)
      }

      if (planId) {
        query = query.eq('plan_id', planId)
      }

      // 计算总数
      const countQuery = supabase
        .from(TABLE_NAMES.userSubscription)
        .select('*', { count: 'exact', head: true })
      const countResult = await countQuery
      const total = countResult.count || 0

      // 分页查询
      const offset = (page - 1) * pageSize
      const result = await query.range(offset, offset + pageSize - 1)
      const { data: subscriptions, error } = handleSupabaseResult(result)
      if (error) throw error

      console.log(
        `🔍 [ADMIN-MEMBERSHIP] 查询到 ${subscriptions?.length || 0} 条订阅，共 ${total} 条`
      )

      // 格式化数据
      const formattedSubscriptions = (subscriptions || []).map((sub: any) => ({
        id: sub.id,
        userId: sub.user_id,
        userEmail: sub.user?.email || '',
        planId: sub.plan_id,
        planName: sub.membershipPlan?.name || '',
        planPrice: sub.membershipPlan?.price || '',
        planDuration: sub.membershipPlan?.duration_days || 0,
        planPoints: sub.membershipPlan?.points_included || 0,
        status: sub.status,
        startDate: sub.start_date,
        endDate: sub.end_date,
        autoRenew: sub.auto_renew,
        paymentId: sub.payment_id,
        createdAt: sub.created_at
      }))

      return c.json({
        success: true,
        data: {
          data: formattedSubscriptions,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        }
      })
    } catch (error) {
      console.error('❌ [ADMIN-MEMBERSHIP] 获取订阅列表失败:', error)
      return c.json(
        {
          success: false,
          message: '获取订阅列表失败'
        },
        500
      )
    }
  }
)

// 获取订阅详情
adminMembership.get('/subscriptions/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const subscriptionId = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-MEMBERSHIP] 获取订阅详情:', subscriptionId)

    const supabase = getSupabase(env)

    // 查询订阅详情
    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .select(
        `
        id,
        user_id,
        plan_id,
        start_date,
        end_date,
        status,
        auto_renew,
        payment_id,
        created_at,
        ${TABLE_NAMES.membershipPlan}!inner(name, price, duration_days, points_included, description)
      `
      )
      .eq('id', subscriptionId)
      .single()

    const { data: subscription, error } = result
    if (error || !subscription) {
      return c.json({ success: false, message: '订阅不存在' }, 404)
    }

    // 获取用户信息
    const supabaseService = createSupabaseServiceClient(env)
    const { data: authUser } = await supabaseService.auth.admin.getUserById(subscription.user_id)

    const subscriptionDetail = {
      id: subscription.id,
      userId: subscription.user_id,
      userEmail: authUser?.user?.email || '未知用户',
      planId: subscription.plan_id,
      // planName: subscription.membershipPlan?.name || '',
      // planPrice: subscription.membershipPlan?.price || '',
      // planDuration: subscription.membershipPlan?.duration_days || 0,
      // planPoints: subscription.membershipPlan?.points_included || 0,
      // planDescription: subscription.membershipPlan?.description || '',
      status: subscription.status,
      startDate: subscription.start_date,
      endDate: subscription.end_date,
      autoRenew: subscription.auto_renew,
      paymentId: subscription.payment_id,
      createdAt: subscription.created_at
    }

    return c.json({
      success: true,
      data: subscriptionDetail
    })
  } catch (error) {
    console.error('❌ [ADMIN-MEMBERSHIP] 获取订阅详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取订阅详情失败'
      },
      500
    )
  }
})

// 取消订阅
adminMembership.post('/subscriptions/:id/cancel', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const subscriptionId = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-MEMBERSHIP] 取消订阅:', subscriptionId)

    const supabase = getSupabase(env)

    const result = await supabase
      .from(TABLE_NAMES.userSubscription)
      .update({ status: 'cancelled' })
      .eq('id', subscriptionId)
      .select()
      .single()

    const { data: updatedSubscription, error } = result
    if (error || !updatedSubscription) {
      return c.json({ success: false, message: '订阅不存在' }, 404)
    }

    console.log('✅ [ADMIN-MEMBERSHIP] 订阅取消成功:', updatedSubscription.id)

    return c.json({
      success: true,
      data: {
        id: updatedSubscription.id,
        userId: updatedSubscription.user_id,
        planId: updatedSubscription.plan_id,
        status: updatedSubscription.status,
        startDate: updatedSubscription.start_date,
        endDate: updatedSubscription.end_date,
        autoRenew: updatedSubscription.auto_renew,
        paymentId: updatedSubscription.payment_id,
        createdAt: updatedSubscription.created_at,
        updatedAt: updatedSubscription.updated_at
      },
      message: '订阅已取消'
    })
  } catch (error) {
    console.error('❌ [ADMIN-MEMBERSHIP] 取消订阅失败:', error)
    return c.json(
      {
        success: false,
        message: '取消订阅失败'
      },
      500
    )
  }
})

// 延长订阅
const extendSchema = z.object({
  days: z.number().min(1, '延长天数必须大于0').max(365, '延长天数不能超过365天')
})

adminMembership.post(
  '/subscriptions/:id/extend',
  authMiddleware,
  zValidator('json', extendSchema),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const subscriptionId = c.req.param('id')
      const { days } = c.req.valid('json')
      const env = c.env

      console.log('🔍 [ADMIN-MEMBERSHIP] 延长订阅:', subscriptionId, '天数:', days)

      const supabase = getSupabase(env)

      // 获取当前订阅信息
      const currentResult = await supabase
        .from(TABLE_NAMES.userSubscription)
        .select('*')
        .eq('id', subscriptionId)
        .single()

      const { data: currentSubscription, error: currentError } = currentResult
      if (currentError || !currentSubscription) {
        return c.json({ success: false, message: '订阅不存在' }, 404)
      }

      // 计算新的结束时间
      const currentEndDate = new Date(currentSubscription.end_date)
      const newEndDate = new Date(currentEndDate.getTime() + days * 24 * 60 * 60 * 1000)

      const result = await supabase
        .from(TABLE_NAMES.userSubscription)
        .update({ end_date: newEndDate.toISOString() })
        .eq('id', subscriptionId)
        .select()
        .single()

      const { data: updatedSubscription, error } = result
      if (error || !updatedSubscription) {
        return c.json({ success: false, message: '更新订阅失败' }, 500)
      }

      console.log('✅ [ADMIN-MEMBERSHIP] 订阅延长成功:', updatedSubscription.id)

      return c.json({
        success: true,
        data: {
          id: updatedSubscription.id,
          userId: updatedSubscription.user_id,
          planId: updatedSubscription.plan_id,
          status: updatedSubscription.status,
          startDate: updatedSubscription.start_date,
          endDate: updatedSubscription.end_date,
          autoRenew: updatedSubscription.auto_renew,
          paymentId: updatedSubscription.payment_id,
          createdAt: updatedSubscription.created_at,
          updatedAt: updatedSubscription.updated_at
        },
        message: `订阅已延长${days}天`
      })
    } catch (error) {
      console.error('❌ [ADMIN-MEMBERSHIP] 延长订阅失败:', error)
      return c.json(
        {
          success: false,
          message: '延长订阅失败'
        },
        500
      )
    }
  }
)

// ==================== 积分套餐管理 ====================

// 获取所有积分套餐
adminMembership.get('/points-packages', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env
    const packages = await getPointsPackages(env)

    return c.json({
      success: true,
      data: packages
    })
  } catch (error) {
    console.error('获取积分套餐失败:', error)
    return c.json(
      {
        success: false,
        message: '获取积分套餐失败'
      },
      500
    )
  }
})

// 获取积分套餐详情
adminMembership.get('/points-packages/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const packageId = c.req.param('id')
    const env = c.env

    const pointsPackage = await getPointsPackageById(env, packageId)
    if (!pointsPackage) {
      return c.json(
        {
          success: false,
          message: '积分套餐不存在'
        },
        404
      )
    }

    return c.json({
      success: true,
      data: pointsPackage
    })
  } catch (error) {
    console.error('获取积分套餐详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取积分套餐详情失败'
      },
      500
    )
  }
})

export default adminMembership

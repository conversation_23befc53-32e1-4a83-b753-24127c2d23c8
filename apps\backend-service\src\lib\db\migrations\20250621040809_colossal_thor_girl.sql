ALTER TABLE "UserPoints" ADD COLUMN "cycle_start_date" timestamp;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN "cycle_end_date" timestamp;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN "membership_level" varchar(20);--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN "monthly_allocation" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN "cycle_consumed" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN "cycle_gifted" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN "cycle_received" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "UserPoints" ADD COLUMN "last_cycle_check" timestamp;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_user_points_cycle_end" ON "UserPoints" USING btree ("cycle_end_date");
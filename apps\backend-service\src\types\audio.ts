// 音频效果查询参数
export interface AudioEffectQuery {
  page?: number
  pageSize?: number
  tags?: string[]
  search?: string
  sortBy?: 'id' | 'duration' | 'avgPitch' | 'avgLoudness' | 'createdAt' | 'usageCount'
  sortOrder?: 'asc' | 'desc'
  isActive?: boolean
  categoryId?: string
}

// 音频效果创建参数
export interface CreateAudioEffectRequest {
  name?: string
  description?: string
  categoryId?: string
  tags: string[]
  url: string
  duration: number
  avgPitch?: number
  avgLoudness?: number
  energyVariation?: number
  isPublic?: boolean
}

// 音频效果更新参数
export interface UpdateAudioEffectRequest {
  name?: string
  description?: string
  categoryId?: string
  tags?: string[]
  url?: string
  duration?: number
  avgPitch?: number
  avgLoudness?: number
  energyVariation?: number
  isPublic?: boolean
  isActive?: boolean
}

// 音频分类查询参数
export interface AudioCategoryQuery {
  parentId?: string | null
  isActive?: boolean
}

// 音频分类创建参数
export interface CreateAudioCategoryRequest {
  name: string
  displayName?: string
  description?: string
  parentId?: string
  sortOrder?: number
}

// 音频分类更新参数
export interface UpdateAudioCategoryRequest {
  name?: string
  displayName?: string
  description?: string
  parentId?: string
  sortOrder?: number
  isActive?: boolean
}

// 分页响应
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 音频统计信息
export interface AudioStats {
  totalAudios: number
  avgDuration: number
  topTags: Array<{
    tag: string
    count: number
  }>
  categoriesCount: number
  totalUsage: number
}

// 批量创建音频效果请求
export interface BatchCreateAudioEffectsRequest {
  audioEffects: CreateAudioEffectRequest[]
}

// 原始JSON数据格式（用于数据迁移）
export interface OriginalAudioData {
  id: number
  tags: string[]
  url: string
  duration: number
  avg_pitch: number
  avg_loudness: number
  energy_variation: number
}

// 搜索响应
export interface AudioSearchResponse {
  data: any[] // 使用数据库类型
  total: number
}

// 随机音频响应
export interface RandomAudioResponse {
  data: any[] // 使用数据库类型
  count: number
}

// 语音转文本响应
export interface SpeechToTextResponse {
  success: boolean
  transcription: string
  metadata: {
    audioFileName: string
    audioFileType: string
    audioFileSize: number
    transcriptionLength: number
    processingTime: string
    language: string
    provider: 'elevenlabs' | 'fish-audio' | 'whisper' // 服务提供商
    duration?: number // 音频时长（秒）
    segmentsCount?: number // 分段数量
    processed: boolean // 是否经过后处理
    hasTranslation: boolean // 是否进行了翻译
    hasPunctuation: boolean // 是否添加了标点符号
  }
  segments?: Array<{
    text: string
    start: number
    end: number
  }> // 可选的分段信息
}

// 语音转文本请求参数
export interface SpeechToTextRequest {
  audio: File
  language?: string // 可选语言参数，如 'zh', 'en', 'ja' 等
}

// 语音转文本错误响应
export interface SpeechToTextError {
  error: string
  allowedTypes?: string[]
  maxSize?: string
  receivedSize?: string
  details?: string
}

// Queue 音频处理任务类型
export interface AudioProcessingTask {
  taskId: string
  audioUrl: string // 临时 R2 URL
  messageId?: string
  chatId?: string
  text: string
  timestamp: number
  isTemporary: boolean // 标记是否为临时文件
}

export interface AudioProcessingResult {
  taskId: string
  success: boolean
  audioUrl?: string
  error?: string
  processingTime: number
}

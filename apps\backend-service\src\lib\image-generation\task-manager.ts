import type { Env } from '@/types/env';
import type { ImageGenerationTask } from '@/types/image-generation';

/**
 * 图片生成任务管理器
 * 使用 Cloudflare KV 存储任务状态
 */
export class TaskManager {
  private static readonly TASK_PREFIX = 'image_task:';
  private static readonly TASK_TTL = 24 * 60 * 60; // 24小时

  constructor(private env: Env) {}

  /**
   * 保存任务状态
   */
  async saveTask(task: ImageGenerationTask): Promise<void> {
    const key = this.getTaskKey(task.taskId);
    await this.env.CACHE.put(key, JSON.stringify(task), {
      expirationTtl: TaskManager.TASK_TTL,
    });
  }

  /**
   * 获取任务状态
   */
  async getTask(taskId: string): Promise<ImageGenerationTask | null> {
    const key = this.getTaskKey(taskId);
    const taskData = await this.env.CACHE.get(key);

    if (!taskData) {
      return null;
    }

    try {
      return JSON.parse(taskData) as ImageGenerationTask;
    } catch (error) {
      console.error('解析任务数据失败:', error);
      return null;
    }
  }

  /**
   * 删除任务
   */
  async deleteTask(taskId: string): Promise<void> {
    const key = this.getTaskKey(taskId);
    await this.env.CACHE.delete(key);
  }

  /**
   * 更新任务状态
   */
  async updateTask(taskId: string, updates: Partial<ImageGenerationTask>): Promise<void> {
    const existingTask = await this.getTask(taskId);
    if (!existingTask) {
      throw new Error('任务不存在');
    }

    const updatedTask = { ...existingTask, ...updates };
    await this.saveTask(updatedTask);
  }

  /**
   * 生成任务键
   */
  private getTaskKey(taskId: string): string {
    return `${TaskManager.TASK_PREFIX}${taskId}`;
  }

  /**
   * 生成唯一任务ID
   */
  static generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substring(7)}`;
  }
}

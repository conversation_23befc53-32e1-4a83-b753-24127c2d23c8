import { useState, useEffect } from 'react'
import { Icon } from '@iconify/react'
import { Skeleton, Button } from '@heroui/react'
import { motion, AnimatePresence } from 'framer-motion'
import type { DisplayRole } from '@/lib/types'
import { apiService } from '@/api'
import type { VideoRecord } from '@/api/services/character-media'
import { VideoDrawer } from '@/components/common/video-drawer'

interface VideosTabProps {
  roleId?: string
  role: DisplayRole | null
}

export function VideosTab({ roleId }: VideosTabProps) {
  const [videos, setVideos] = useState<VideoRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(false)
  const [selectedVideo, setSelectedVideo] = useState<VideoRecord | null>(null)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)

  // 获取视频数据
  const fetchVideos = async () => {
    if (!roleId) {
      console.log('🔍 [VideosTab] roleId 为空，跳过获取视频')
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      console.log('🔍 [VideosTab] 开始获取角色视频:', roleId)

      const data = await apiService.characterMedia.getCharacterVideos(roleId, {
        limit: 20,
        offset: 0
      })

      console.log('🔍 [VideosTab] API 响应:', data)

      if (data) {
        const videos = data.videos || []
        console.log('🔍 [VideosTab] 设置视频数据:', videos)
        setVideos(videos)
        setHasMore(data.hasMore || false)
      } else {
        console.error('🔍 [VideosTab] API 调用失败:', data)
        setError('获取视频失败')
        setVideos([]) // 确保设置为空数组
      }
    } catch (err) {
      console.error('🔍 [VideosTab] 获取角色视频失败:', err)
      setError('获取视频失败')
      setVideos([]) // 确保设置为空数组
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchVideos()
  }, [roleId])

  // 处理视频点击
  const handleVideoClick = (video: VideoRecord) => {
    setSelectedVideo(video)
    setIsDrawerOpen(true)
  }

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setIsDrawerOpen(false)
    setSelectedVideo(null)
  }

  if (isLoading) {
    return (
      <div className="py-4">
        <div className="grid grid-cols-2 gap-3">
          {Array.from({ length: 4 }).map((_, index) => (
            <Skeleton key={index} className="aspect-video rounded-lg">
              <div className="w-full h-full bg-default-200" />
            </Skeleton>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="py-8 flex flex-col items-center justify-center">
        <Icon icon="solar:video-broken" className="w-12 h-12 text-danger mb-4" />
        <p className="text-danger text-center mb-4">{error}</p>
        <Button
          variant="light"
          color="primary"
          onPress={fetchVideos}
          startContent={<Icon icon="solar:refresh-linear" className="w-4 h-4" />}
        >
          重试
        </Button>
      </div>
    )
  }

  if (!videos || videos.length === 0) {
    return (
      <div className="py-8 flex flex-col items-center justify-center">
        <Icon icon="solar:video-library-linear" className="w-12 h-12 text-default-400 mb-4" />
        <p className="text-default-500 text-center">还没有生成过视频</p>
        <p className="text-default-400 text-center text-sm mt-2">在聊天中生成视频后会显示在这里</p>
      </div>
    )
  }

  return (
    <>
      <div className="py-4">
        <AnimatePresence>
          <motion.div
            className="grid grid-cols-2 gap-3"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {(videos || []).map((video, index) => (
              <motion.div
                key={video.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05, duration: 0.3 }}
                className="relative aspect-video bg-default-100 rounded-lg overflow-hidden cursor-pointer group"
                onClick={() => handleVideoClick(video)}
              >
                {/* 视频缩略图 */}
                <div className="absolute inset-0 bg-black flex items-center justify-center">
                  <Icon icon="solar:play-circle-linear" className="w-12 h-12 text-white/80" />
                </div>

                {/* 播放按钮覆盖层 */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                  <Icon
                    icon="solar:play-bold"
                    className="w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  />
                </div>

                {/* 生成类型标签 */}
                {video.generationType && (
                  <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {video.generationType === 'multimodal_chat'
                        ? '聊天'
                        : video.generationType === 'template_based'
                        ? '模板'
                        : '独立'}
                    </div>
                  </div>
                )}

                {/* 视频时长标签（如果有的话） */}
                {video.metadata?.duration && (
                  <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                    {video.metadata.duration}s
                  </div>
                )}
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* 加载更多按钮 */}
        {hasMore && (
          <div className="flex justify-center mt-6">
            <Button
              variant="light"
              color="primary"
              startContent={<Icon icon="solar:refresh-linear" className="w-4 h-4" />}
            >
              加载更多
            </Button>
          </div>
        )}
      </div>

      {/* 视频查看器 */}
      {selectedVideo && (
        <VideoDrawer
          isOpen={isDrawerOpen}
          onClose={handleDrawerClose}
          videoUrl={selectedVideo.url}
          title="生成的视频"
        />
      )}
    </>
  )
}

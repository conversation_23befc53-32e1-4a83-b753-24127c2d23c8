import ReactDOM from 'react-dom/client'
import { RouterProvider } from 'react-router'
import router from './router'
import './styles/globals.css'
import './styles/mobile.css'
import { AuthProvider } from './contexts/auth-context'
import { ThemeProvider } from '@/components/theme-provider'
// import { BleTestButton } from './components/BleTestButton'
import { HeroUIProvider } from '@heroui/react'
import { ToastProvider } from '@heroui/toast'
import { Capacitor } from '@capacitor/core'
import { App as CapacitorApp } from '@capacitor/app'

// 导入i18n配置
import './i18n'

// 导入并初始化隐私屏幕服务
import { privacyScreenService } from './lib/privacy-screen'

// 导入并初始化数据库
import { initDatabase } from './lib/database'

// 导入并初始化聊天系统
import { initializeChatSystem } from './lib/chat-database'

// 导入存储系统初始化器
import { storageInitializer } from './lib/storage-initializer'

// 导入 Android 兼容性检测工具
import { setupAndroidCompatibility } from './utils/android-compatibility'

// 在应用启动前执行兼容性检测
setupAndroidCompatibility()

// 应用启动时强制启用隐私屏幕（导入时自动初始化）
console.log('隐私屏幕服务已初始化')

// 深度链接处理函数
const handleDeepLink = (url: string) => {
  console.log('🔗 [DEEPLINK] 收到深度链接:', url)

  try {
    const urlObj = new URL(url)
    const path = urlObj.pathname
    const searchParams = urlObj.searchParams

    // 处理支付结果返回
    if (path.includes('payment/result')) {
      const orderId = searchParams.get('orderId')
      const status = searchParams.get('status')

      console.log('💳 [DEEPLINK] 支付结果返回:', { orderId, status })

      // 构建应用内路由
      let targetRoute = '/chat-history' // 默认返回聊天历史页面

      if (orderId) {
        // 如果有订单ID，可以跳转到具体的支付结果页面或相关页面
        if (status === 'success') {
          targetRoute = `/membership?orderId=${orderId}&status=success`
        } else {
          targetRoute = `/membership?orderId=${orderId}&status=failed`
        }
      }

      // 延迟导航，确保应用已完全加载
      setTimeout(() => {
        window.location.hash = targetRoute
        console.log('🔗 [DEEPLINK] 导航到:', targetRoute)
      }, 500)
    }
  } catch (error) {
    console.error('❌ [DEEPLINK] 处理深度链接失败:', error)
  }
}

// 初始化深度链接监听器
const initializeDeepLinkHandlers = () => {
  if (Capacitor.isNativePlatform()) {
    console.log('🔗 [DEEPLINK] 初始化深度链接监听器')

    // 监听应用通过深度链接打开的事件
    CapacitorApp.addListener('appUrlOpen', event => {
      console.log('🔗 [DEEPLINK] appUrlOpen 事件:', event)
      handleDeepLink(event.url)
    })

    // 监听应用恢复到前台的事件（可能是从外部链接返回）
    CapacitorApp.addListener('appStateChange', state => {
      if (state.isActive) {
        console.log('📱 [APP] 应用恢复到前台')
      }
    })
  }
}

// 获取根元素
const rootElement = document.getElementById('root')

// 创建一个测试容器，用于测试蓝牙功能
const createTestContainer = () => {
  // 检查是否已存在测试容器
  let testContainer = document.getElementById('ble-test-container')

  if (!testContainer) {
    // 创建测试容器
    testContainer = document.createElement('div')
    testContainer.id = 'ble-test-container'
    testContainer.style.position = 'fixed'
    testContainer.style.bottom = '20px'
    testContainer.style.right = '20px'
    testContainer.style.zIndex = '9999'
    document.body.appendChild(testContainer)
  }

  return testContainer
}

if (rootElement) {
  // storageInitializer.initializeAll().catch(console.error)

  // 初始化数据库
  initDatabase().catch(console.error)

  // 初始化增强聊天系统
  initializeChatSystem()
    .then(async () => {
      // 尝试升级到增强系统
      try {
        const { initializeEnhancedChatSystem } = await import('@/lib/chat-database')
        await initializeEnhancedChatSystem()
        console.log('✅ 增强聊天系统初始化完成')

        // 初始化所有存储系统
        await storageInitializer.initializeAll()

        // 初始化媒体下载管理器
        const { getMediaDownloadManager } = await import('@/lib/media/media-download-manager')
        const mediaManager = getMediaDownloadManager({
          enabled: true,
          maxConcurrentDownloads: 2,
          retryAttempts: 3,
          downloadDirectory: 'ai_chat_media'
        })

        console.log('✅ 媒体下载管理器初始化完成')
      } catch (error) {
        console.warn('⚠️ 增强功能初始化失败，使用基础系统:', error)
      }
    })
    .catch(console.error)

  // 初始化深度链接处理
  initializeDeepLinkHandlers()

  ReactDOM.createRoot(rootElement).render(
    // <React.StrictMode>
    <HeroUIProvider>
      <ToastProvider
        toastProps={{
          timeout: 1500,
          hideIcon: true,
          classNames: {
            closeButton: 'opacity-100 absolute right-4 top-1/2 -translate-y-1/2',
            base: 'z-[9999]'
          }
        }}
      />
      <ThemeProvider
        defaultTheme="dark"
        enableSystem
        disableTransitionOnChange
        storageKey="app-theme"
      >
        <AuthProvider>
          <RouterProvider router={router} />
        </AuthProvider>
      </ThemeProvider>
    </HeroUIProvider>
    // </React.StrictMode>
  )

  // 渲染测试按钮到测试容器
  // const testContainer = createTestContainer();
  // ReactDOM.createRoot(testContainer).render(
  //   <React.StrictMode>
  //     <ThemeProvider
  //       defaultTheme="dark"
  //       enableSystem
  //       disableTransitionOnChange
  //       storageKey="app-theme"
  //     >
  //       <BleTestButton />
  //     </ThemeProvider>
  //   </React.StrictMode>,
  // );
} else {
  console.error('Root element not found')
}

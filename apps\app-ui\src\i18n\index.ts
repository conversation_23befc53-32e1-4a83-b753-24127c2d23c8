import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import LanguageDetector from 'i18next-browser-languagedetector'
import { I18N_DEFAULTS } from '@/config/i18n'

// 导入翻译文件
import enTranslation from './locales/en/index'
import zhTranslation from './locales/zh/index'
import zhTWTranslation from './locales/zh-TW/index'
import esTranslation from './locales/es/index'
import jaTranslation from './locales/ja/index'

// 初始化i18next
i18n
  // 检测用户语言
  .use(LanguageDetector)
  // 将i18n实例传递给react-i18next
  .use(initReactI18next)
  // 初始化i18next
  .init({
    // 默认语言
    fallbackLng: I18N_DEFAULTS.DEFAULT_LANGUAGE,
    // 不要在生产环境中使用
    debug: import.meta.env.DEV,
    // 命名空间
    ns: [
      'common',
      'login',
      'discover',
      'roleDetail',
      'register',
      'customRole',
      'chat',
      'chat-v2',
      'chat-history',
      'device',
      'profile',
      'my',
      'photo-album',
      'interactive',
      'voice',
      'toast',
      'referral',
      'membership',
      'points'
    ],
    defaultNS: 'common',
    // 配置资源
    resources: {
      en: enTranslation,
      zh: zhTranslation,
      'zh-TW': zhTWTranslation,
      es: esTranslation,
      ja: jaTranslation
    },
    // 插值配置
    interpolation: {
      // 不需要在React中转义
      escapeValue: false
    }
  })

interface Language {
  code: string
  name: string
  nativeName: string
}

export const languages: Language[] = [
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文'
  },
  {
    code: 'zh-TW',
    name: 'Traditional Chinese',
    nativeName: '繁體中文'
  },
  {
    code: 'en',
    name: 'English',
    nativeName: 'English'
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語'
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español'
  }
]

export default i18n

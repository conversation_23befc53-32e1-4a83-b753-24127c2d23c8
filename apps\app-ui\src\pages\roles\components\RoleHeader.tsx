import { Button } from '@heroui/react'
import { Icon } from '@iconify/react'
import { motion } from 'framer-motion'
import type { DisplayRole } from '@/lib/types'

interface RoleHeaderProps {
  role: DisplayRole
  roleId?: string
  safeAreaTop: number
  onGoBack: () => void
}

export function RoleHeader({ role, roleId, safeAreaTop, onGoBack }: RoleHeaderProps) {
  return (
    <>
      {/* 独立的返回按钮 */}
      <motion.div
        className="absolute top-0 left-0 z-50 p-4"
        style={{ paddingTop: `${safeAreaTop + 16}px` }}
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <Button
          isIconOnly
          variant="flat"
          className="bg-black/50 backdrop-blur-md text-white border-white/20"
          onPress={onGoBack}
          aria-label="返回"
        >
          <Icon icon="solar:arrow-left-linear" width={20} />
        </Button>
      </motion.div>

      {/* 顶部图片区域 - 3:1 比例，固定高度 */}
      <div className="relative h-[40vh] max-h-[300px] min-h-[150px] w-full overflow-hidden">
        <motion.div
          className="w-full h-full overflow-hidden"
          layoutId={`role-avatar-${role.id || roleId}`}
          initial={{
            scale: 0.2,
            x: '30vw',
            y: '-10vh',
            borderRadius: '50%'
          }}
          animate={{
            scale: 1,
            x: 0,
            y: 0,
            borderRadius: '0%'
          }}
          transition={{
            duration: 0.5,
            ease: [0.4, 0, 0.2, 1],
            scale: {
              duration: 0.5,
              ease: [0.4, 0, 0.2, 1],
              times: [0, 0.7, 1],
              values: [0.2, 0.9, 1]
            },
            x: {
              duration: 0.5,
              ease: [0.4, 0, 0.2, 1]
            },
            y: {
              duration: 0.5,
              ease: [0.4, 0, 0.2, 1]
            },
            borderRadius: {
              duration: 0.3,
              delay: 0.1,
              ease: [0.4, 0, 0.2, 1]
            }
          }}
          style={{
            transformOrigin: 'center center'
          }}
        >
          <img
            src={role.avatar}
            alt={role.character}
            className="w-full h-full object-cover"
            style={{
              objectPosition: '50% 10%',
              filter: 'brightness(0.9)'
            }}
            onLoad={e => {
              // 动态调整图片位置以确保头部可见（针对3:1显示优化）
              const img = e.target as HTMLImageElement
              const aspectRatio = img.naturalWidth / img.naturalHeight

              // 针对3:1显示区域优化位置
              if (aspectRatio >= 0.7 && aspectRatio <= 0.8) {
                img.style.objectPosition = '50% 5%' // 3:4图片在3:1区域显示最顶部
              } else if (aspectRatio < 0.7) {
                img.style.objectPosition = '50% 0%' // 更窄的图片显示绝对顶部
              } else {
                img.style.objectPosition = '50% 15%' // 更宽的图片稍微下移
              }
            }}
          />

          {/* 渐变遮罩 - 优化以确保文字清晰可见 */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent" />

          {/* 底部额外遮罩确保文字区域足够暗 */}
          <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/95 to-transparent" />

          {/* 角色名称 */}
          <motion.div
            className="absolute bottom-6 left-6 right-6 z-5"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ delay: 0.35, duration: 0.4 }}
          >
            <h1 className="text-white text-3xl font-bold mb-2 drop-shadow-lg">{role.character}</h1>
            {role.age && role.age !== '?' && (
              <div className="bg-pink-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium inline-block shadow-lg">
                {role.age}岁
              </div>
            )}
          </motion.div>
        </motion.div>
      </div>
    </>
  )
}

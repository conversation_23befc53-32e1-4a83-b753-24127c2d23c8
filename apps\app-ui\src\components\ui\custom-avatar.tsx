import React from 'react'
import { cn } from '@/lib/utils'

interface CustomAvatarProps {
  src?: string
  alt?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl'
  className?: string
  fallback?: React.ReactNode
  onClick?: () => void
}

const sizeClasses = {
  sm: 'w-8 h-8',
  md: 'w-12 h-12',
  lg: 'w-16 h-16',
  xl: 'w-20 h-20',
  '2xl': 'w-24 h-24'
}

/**
 * 自定义头像组件
 * 专门处理 3:4 比例图片中头部位置在顶部三分之一的显示问题
 * 通过调整 object-position 来确保头部区域正确显示
 */
export const CustomAvatar: React.FC<CustomAvatarProps> = ({
  src,
  alt = '头像',
  size = 'md',
  className,
  fallback,
  onClick
}) => {
  const [imageError, setImageError] = React.useState(false)
  const [imageLoaded, setImageLoaded] = React.useState(false)

  const handleImageError = () => {
    setImageError(true)
  }

  const handleImageLoad = () => {
    setImageLoaded(true)
    setImageError(false)
  }

  // 当src改变时重置状态
  React.useEffect(() => {
    if (src && src.trim() !== '') {
      setImageError(false)
      setImageLoaded(false)

      // 检查图片是否已经在缓存中加载完成
      const img = new Image()
      img.onload = () => {
        setImageLoaded(true)
        setImageError(false)
      }
      img.onerror = () => {
        setImageError(true)
        setImageLoaded(false)
      }
      img.src = src
    } else {
      setImageError(false)
      setImageLoaded(false)
    }
  }, [src])

  const baseClasses = cn(
    'relative overflow-hidden rounded-full bg-gray-100 flex items-center justify-center',
    sizeClasses[size],
    onClick && 'cursor-pointer hover:opacity-80 transition-opacity',
    className
  )

  // 如果没有图片或图片加载失败，显示fallback
  if (!src || src.trim() === '' || imageError) {
    return (
      <div className={baseClasses} onClick={onClick}>
        {fallback || (
          <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium">
            {alt.charAt(0).toUpperCase()}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={baseClasses} onClick={onClick}>
      {/* 图片加载时显示 fallback，加载完成后显示图片 */}
      {!imageLoaded && (
        <div className="absolute inset-0 flex items-center justify-center">
          {fallback || (
            <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-medium">
              {alt.charAt(0).toUpperCase()}
            </div>
          )}
        </div>
      )}

      <img
        src={src}
        alt={alt}
        className={cn(
          'w-full h-full object-cover transition-opacity duration-200',
          // 关键：调整图片位置，让头部区域显示在圆形头像中
          // 对于3:4比例的图片，头部通常在顶部1/3位置
          // 使用 object-position 来调整显示区域
          'object-[center_20%]', // 显示图片顶部20%的位置作为中心
          imageLoaded ? 'opacity-100' : 'opacity-0'
        )}
        onError={handleImageError}
        onLoad={handleImageLoad}
        loading="lazy"
        // 添加 key 属性确保 src 变化时重新渲染
        key={src}
      />
    </div>
  )
}

// 导出一些预设的头像变体
export const SmallAvatar: React.FC<Omit<CustomAvatarProps, 'size'>> = props => (
  <CustomAvatar {...props} size="sm" />
)

export const MediumAvatar: React.FC<Omit<CustomAvatarProps, 'size'>> = props => (
  <CustomAvatar {...props} size="md" />
)

export const LargeAvatar: React.FC<Omit<CustomAvatarProps, 'size'>> = props => (
  <CustomAvatar {...props} size="lg" />
)

export const ExtraLargeAvatar: React.FC<Omit<CustomAvatarProps, 'size'>> = props => (
  <CustomAvatar {...props} size="xl" />
)

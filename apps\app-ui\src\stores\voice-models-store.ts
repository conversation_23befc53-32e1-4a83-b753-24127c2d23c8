import { create } from 'zustand'
import { voicesService } from '@/api/services/voices'
import type { VoiceModelWithSample, VoiceOption } from '@/api/services/voices'

// Store状态接口
interface VoiceModelsState {
  // 数据状态
  allVoiceModels: VoiceModelWithSample[]
  maleVoiceModels: VoiceModelWithSample[]
  femaleVoiceModels: VoiceModelWithSample[]
  neutralVoiceModels: VoiceModelWithSample[]

  // 加载状态
  isLoading: boolean
  isRefreshing: boolean
  error: string | null
  lastFetchTime: number | null

  // 请求状态管理
  fetchPromise: Promise<VoiceModelWithSample[]> | null
}

// Store方法接口
interface VoiceModelsActions {
  // 获取所有声音模型
  fetchAllVoiceModels: (forceRefresh?: boolean) => Promise<VoiceModelWithSample[]>

  // 根据性别获取声音模型
  fetchVoiceModelsByGender: (
    gender: 'male' | 'female' | 'neutral',
    forceRefresh?: boolean
  ) => Promise<VoiceModelWithSample[]>

  // 获取声音模型详情
  getVoiceModelById: (id: string) => VoiceModelWithSample | undefined

  // 获取声音模型（通过modelId）
  getVoiceModelByModelId: (modelId: string) => VoiceModelWithSample | undefined

  // 转换为前端选项格式
  getVoiceOptions: (gender?: 'male' | 'female' | 'neutral') => VoiceOption[]

  // 预加载声音数据
  preloadVoiceData: () => Promise<void>

  // 强制刷新所有数据
  refreshAllVoiceModels: () => Promise<VoiceModelWithSample[]>

  // 清除缓存
  clearVoiceModels: () => void

  // 检查缓存是否有效
  isCacheValid: () => boolean
}

// 缓存配置
const CACHE_EXPIRY_TIME = 30 * 60 * 1000 // 30分钟缓存
const MAX_CACHE_SIZE = 200 // 最大缓存声音模型数量

// 创建store
export const useVoiceModelsStore = create<VoiceModelsState & VoiceModelsActions>((set, get) => ({
  // 初始状态
  allVoiceModels: [],
  maleVoiceModels: [],
  femaleVoiceModels: [],
  neutralVoiceModels: [],
  isLoading: false,
  isRefreshing: false,
  error: null,
  lastFetchTime: null,
  fetchPromise: null,

  // 检查缓存是否有效
  isCacheValid: () => {
    const { lastFetchTime } = get()
    if (!lastFetchTime) return false
    return Date.now() - lastFetchTime < CACHE_EXPIRY_TIME
  },

  // 获取所有声音模型
  fetchAllVoiceModels: async (forceRefresh = false) => {
    const state = get()

    // 如果不强制刷新且缓存有效，直接返回缓存数据
    if (!forceRefresh && state.isCacheValid() && state.allVoiceModels.length > 0) {
      console.log('🎯 使用缓存的声音模型数据')
      return state.allVoiceModels
    }

    // 如果正在请求中，返回同一个Promise避免重复请求
    if (state.fetchPromise) {
      console.log('🔄 等待进行中的声音模型请求')
      return state.fetchPromise
    }

    // 创建新的请求Promise
    const fetchPromise = (async () => {
      try {
        console.log('🚀 开始获取声音模型数据')

        set({
          isLoading: !state.allVoiceModels.length, // 如果有缓存数据，不显示loading
          isRefreshing: !!state.allVoiceModels.length, // 如果有缓存数据，显示refreshing
          error: null
        })

        const models = await voicesService.getVoiceModels(true)

        // 限制缓存大小
        const limitedModels = models.slice(0, MAX_CACHE_SIZE)

        // 按性别分类
        const maleModels = limitedModels.filter(model => model.gender === 'male')
        const femaleModels = limitedModels.filter(model => model.gender === 'female')
        const neutralModels = limitedModels.filter(model => model.gender === 'neutral')

        set({
          allVoiceModels: limitedModels,
          maleVoiceModels: maleModels,
          femaleVoiceModels: femaleModels,
          neutralVoiceModels: neutralModels,
          isLoading: false,
          isRefreshing: false,
          error: null,
          lastFetchTime: Date.now(),
          fetchPromise: null
        })

        console.log(`✅ 成功获取 ${limitedModels.length} 个声音模型`)
        return limitedModels
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '获取声音模型失败'
        console.error('❌ 获取声音模型失败:', errorMessage)

        set({
          isLoading: false,
          isRefreshing: false,
          error: errorMessage,
          fetchPromise: null
        })

        // 如果有缓存数据，在错误时返回缓存数据
        if (state.allVoiceModels.length > 0) {
          console.log('⚠️ 请求失败，返回缓存数据')
          return state.allVoiceModels
        }

        throw error
      }
    })()

    // 保存Promise到状态中
    set({ fetchPromise })

    return fetchPromise
  },

  // 根据性别获取声音模型
  fetchVoiceModelsByGender: async (gender: 'male' | 'female' | 'neutral', forceRefresh = false) => {
    const state = get()

    // 确保已加载所有模型
    await state.fetchAllVoiceModels(forceRefresh)

    // 从缓存中返回对应性别的模型
    switch (gender) {
      case 'male':
        return state.maleVoiceModels
      case 'female':
        return state.femaleVoiceModels
      case 'neutral':
        return state.neutralVoiceModels
      default:
        return []
    }
  },

  // 获取声音模型详情（通过ID）
  getVoiceModelById: (id: string) => {
    const { allVoiceModels } = get()
    return allVoiceModels.find(model => model.id === id)
  },

  // 获取声音模型（通过modelId）
  getVoiceModelByModelId: (modelId: string) => {
    const { allVoiceModels } = get()
    return allVoiceModels.find(model => model.modelId === modelId)
  },

  // 转换为前端选项格式
  getVoiceOptions: (gender?: 'male' | 'female' | 'neutral') => {
    const state = get()
    let models: VoiceModelWithSample[]

    if (gender) {
      switch (gender) {
        case 'male':
          models = state.maleVoiceModels
          break
        case 'female':
          models = state.femaleVoiceModels
          break
        case 'neutral':
          models = state.neutralVoiceModels
          break
        default:
          models = state.allVoiceModels
      }
    } else {
      models = state.allVoiceModels
    }

    return voicesService.convertToVoiceOptions(models)
  },

  // 预加载声音数据
  preloadVoiceData: async () => {
    try {
      console.log('🔄 开始预加载声音数据')
      const state = get()

      // 预加载所有声音模型
      await state.fetchAllVoiceModels()

      console.log('✅ 声音数据预加载完成')
    } catch (error) {
      console.error('❌ 预加载声音数据失败:', error)
    }
  },

  // 强制刷新所有数据
  refreshAllVoiceModels: async () => {
    return get().fetchAllVoiceModels(true)
  },

  // 清除缓存
  clearVoiceModels: () => {
    console.log('🧹 清除声音模型缓存')
    set({
      allVoiceModels: [],
      maleVoiceModels: [],
      femaleVoiceModels: [],
      neutralVoiceModels: [],
      isLoading: false,
      isRefreshing: false,
      error: null,
      lastFetchTime: null,
      fetchPromise: null
    })
  }
}))

// 便捷的Hook，用于只获取数据而不触发请求
export const useVoiceModelsData = () => {
  const store = useVoiceModelsStore()
  return {
    allVoiceModels: store.allVoiceModels,
    maleVoiceModels: store.maleVoiceModels,
    femaleVoiceModels: store.femaleVoiceModels,
    neutralVoiceModels: store.neutralVoiceModels,
    isLoading: store.isLoading,
    isRefreshing: store.isRefreshing,
    error: store.error,
    isCacheValid: store.isCacheValid()
  }
}

// 便捷的Hook，用于获取特定性别的声音模型
export const useVoiceModelsByGender = (gender: 'male' | 'female' | 'neutral') => {
  const store = useVoiceModelsStore()

  switch (gender) {
    case 'male':
      return store.maleVoiceModels
    case 'female':
      return store.femaleVoiceModels
    case 'neutral':
      return store.neutralVoiceModels
    default:
      return []
  }
}

// 便捷的Hook，用于获取声音选项
export const useVoiceOptions = (gender?: 'male' | 'female' | 'neutral') => {
  const getVoiceOptions = useVoiceModelsStore(state => state.getVoiceOptions)
  return getVoiceOptions(gender)
}

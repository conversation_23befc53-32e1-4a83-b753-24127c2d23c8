import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import {
  createSupabaseServiceClient,
  signUpWithEmail,
  signInWithEmail,
  signOut
} from '@/lib/supabase'
import {
  createUser,
  getUserBySupabaseId,
  createUserProfile,
  getUser,
  initializeNewUserPointsAsync
} from '@/lib/db/queries'
import {
  generateVerificationCode,
  sendRegistrationCodeEmail,
  sendLoginCodeEmail,
  storeVerificationCode,
  verifyStoredCode
} from '@/lib/email'
import { authMiddleware, optionalAuthMiddleware } from '@/middleware/auth'
import {
  validateInviteCode,
  createReferralRelation,
  incrementInviteCodeUsage
} from '@/lib/db/queries/referral'
import type { Env } from '@/types/env'
import type { Context } from 'hono'
import type { SupportedLanguage } from '@/i18n/config'

// 获取国际化函数的辅助函数
function getI18n(
  c: Context<{
    Bindings: Env
    Variables: {
      language: SupportedLanguage
      t: (key: string, params?: Record<string, string | number>) => string
    }
  }>
): (key: string, params?: Record<string, string | number>) => string {
  return c.get('t')
}

const auth = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// ==================== 验证模式 ====================

// 创建验证模式的工厂函数，支持国际化
const createAuthSchemas = (
  t: (key: string, params?: Record<string, string | number>) => string
) => {
  return {
    registerSchema: z.object({
      email: z.string().email(t('validation.email_invalid')),
      password: z.string().min(6, t('validation.password_min_length', { length: 6 })),
      name: z.string().optional(),
      inviteCode: z.string().optional() // 邀请码（可选）
    }),

    loginSchema: z.object({
      email: z.string().email(t('validation.email_invalid')),
      password: z.string().min(6, t('validation.password_min_length', { length: 6 }))
    }),

    // 发送验证码模式
    sendCodeSchema: z.object({
      email: z.string().email(t('validation.email_invalid'))
    }),

    // 验证验证码模式
    verifyCodeSchema: z.object({
      email: z.string().email(t('validation.email_invalid')),
      code: z.string().min(6, t('validation.code_min_length', { length: 6 })),
      name: z.string().optional(),
      password: z.string().optional(),
      inviteCode: z.string().optional() // 邀请码（可选）
    }),

    // 登录验证码模式
    loginCodeSchema: z.object({
      email: z.string().email(t('validation.email_invalid')),
      code: z.string().min(6, t('validation.code_min_length', { length: 6 }))
    }),

    // 刷新令牌验证模式
    refreshTokenSchema: z.object({
      refresh_token: z.string().min(1, t('validation.refresh_token_required'))
    })
  }
}

// 保留原有的验证模式（向后兼容）
const registerSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少需要6个字符'),
  name: z.string().optional(),
  inviteCode: z.string().optional() // 邀请码（可选）
})

const loginSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  password: z.string().min(6, '密码至少需要6个字符')
})

// 发送验证码模式
const sendCodeSchema = z.object({
  email: z.string().email('邮箱格式不正确')
})

// 验证验证码模式
const verifyCodeSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  code: z.string().min(6, '验证码至少需要6位'),
  name: z.string().optional(),
  password: z.string().optional(),
  inviteCode: z.string().optional() // 邀请码（可选）
})

// 登录验证码模式
const loginCodeSchema = z.object({
  email: z.string().email('邮箱格式不正确'),
  code: z.string().min(6, '验证码至少需要6位')
})

// 刷新令牌验证模式
const refreshTokenSchema = z.object({
  refresh_token: z.string().min(1, '刷新令牌不能为空')
})

// ==================== 发送验证码 ====================

auth.post('/send-code', async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { email } = data
    const env = c.env

    // 检查数据库中是否已存在用户记录
    const existingUsers = await getUser(env, email)
    if (existingUsers && existingUsers.length > 0) {
      return c.json(
        {
          success: false,
          message: t('auth.email_already_registered')
        },
        409
      )
    }

    // 生成6位数字验证码
    const verificationCode = generateVerificationCode()

    // 发送注册验证码邮件
    const emailResult = await sendRegistrationCodeEmail(env, email, verificationCode)

    if (!emailResult.success) {
      console.error('发送注册验证码邮件失败:', emailResult.error)
      return c.json(
        {
          success: false,
          message: t('auth.send_code_failed')
        },
        400
      )
    }

    // 将验证码存储到 KV（10分钟有效期）
    await storeVerificationCode(env, email, verificationCode, 10)

    return c.json(
      {
        success: true,
        message: t('auth.code_sent_to_email')
      },
      200
    )
  } catch (error) {
    console.error('发送验证码过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: t('auth.send_code_error')
      },
      500
    )
  }
})

// ==================== 验证验证码并注册 ====================

auth.post('/verify-code', async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { email, code, name, password, inviteCode } = data
    const env = c.env

    // 验证存储的验证码
    const codeVerification = await verifyStoredCode(env, email, code)

    if (!codeVerification.valid) {
      return c.json(
        {
          success: false,
          message: codeVerification.error || t('auth.code_verification_failed')
        },
        400
      )
    }

    // 验证码正确，尝试创建用户
    const supabase = createSupabaseServiceClient(env)

    // 如果用户提供了密码，使用用户密码；否则生成临时密码
    const userPassword = password || 'temp_' + Math.random().toString(36).substring(2, 15)

    let authData: any
    let isNewUser = true

    // 尝试创建新用户
    const { data: createData, error: createError } = await supabase.auth.admin.createUser({
      email,
      password: userPassword,
      email_confirm: true, // 自动确认邮箱
      user_metadata: {
        name: name || email.split('@')[0],
        hasPassword: !!password // 标记用户是否设置了密码
      }
    })

    if (createError) {
      if (
        createError.message.includes('already been registered') ||
        createError.message.includes('already exists')
      ) {
        // 用户已存在，尝试获取现有用户
        isNewUser = false

        const { data: users, error: listError } = await supabase.auth.admin.listUsers()
        if (listError) {
          console.error('获取用户列表失败:', listError)
          return c.json(
            {
              success: false,
              message: t('auth.email_already_registered_login_directly')
            },
            409
          )
        }

        const existingUser = users?.users?.find(u => u.email === email)
        if (!existingUser) {
          return c.json(
            {
              success: false,
              message: t('auth.user_status_abnormal')
            },
            500
          )
        }

        authData = { user: existingUser }
      } else {
        console.error('Supabase 创建用户失败:', createError)
        return c.json(
          {
            success: false,
            message: t('auth.registration_failed_try_again')
          },
          400
        )
      }
    } else {
      authData = createData
    }

    if (!authData.user) {
      return c.json(
        {
          success: false,
          message: t('auth.registration_failed')
        },
        500
      )
    }

    try {
      // 检查数据库中是否已有用户记录
      let dbUser = await getUserBySupabaseId(env, authData.user.id)

      if (!dbUser && isNewUser) {
        // 在数据库中创建用户记录
        const [newDbUser] = await createUser(env, email, authData.user.id)
        dbUser = newDbUser

        // 创建用户配置文件
        if (dbUser) {
          await createUserProfile(env, {
            userId: dbUser.id,
            nickname: name || email.split('@')[0],
            gender: 'other'
          })

          // 🆕 异步初始化新用户积分（不阻塞注册流程）
          initializeNewUserPointsAsync(env, dbUser.id)

          // 处理邀请码（如果提供了）
          if (inviteCode && inviteCode.trim()) {
            try {
              const validationResult = await validateInviteCode(env, inviteCode.trim())

              if (validationResult.valid && validationResult.inviteCode) {
                // 创建推荐关系
                await createReferralRelation(
                  env,
                  validationResult.inviteCode.userId,
                  dbUser.id,
                  validationResult.inviteCode.id
                )

                // 更新邀请码使用次数
                await incrementInviteCodeUsage(env, validationResult.inviteCode.id)

                console.log(
                  `用户 ${dbUser.id} 通过邀请码 ${inviteCode} 注册成功，邀请人: ${validationResult.inviteCode.userId}`
                )
              } else {
                console.warn(`邀请码处理失败: ${validationResult.error}`)
                // 邀请码处理失败不影响注册流程，只记录日志
              }
            } catch (error) {
              console.error('处理邀请码时出错:', error)
              // 邀请码处理失败不影响注册流程
            }
          }
        }
      }

      // 为用户创建会话
      let signInData: any = null

      if (password) {
        // 用户设置了密码，使用密码登录
        const { data: passwordSignIn, error: passwordError } =
          await supabase.auth.signInWithPassword({
            email,
            password: userPassword
          })

        if (!passwordError && passwordSignIn.session) {
          signInData = passwordSignIn
        }
      }

      // 如果密码登录失败或用户没有设置密码，尝试其他方式
      if (!signInData?.session) {
        console.warn('密码登录失败或用户未设置密码，返回注册成功状态')
        return c.json(
          {
            success: true,
            message: isNewUser ? t('auth.registration_success') : t('auth.verification_success'),
            requireLogin: !password, // 如果没有密码，提示需要登录
            hasPassword: !!password
          },
          201
        )
      }

      return c.json(
        {
          success: true,
          message: isNewUser ? t('auth.registration_success') : t('auth.verification_success'),
          session: {
            access_token: signInData.session.access_token,
            refresh_token: signInData.session.refresh_token,
            expires_at: signInData.session.expires_at,
            user: {
              id: authData.user.id,
              email: authData.user.email,
              emailConfirmed: true, // 通过验证码验证的用户邮箱已确认
              dbUserId: dbUser?.id
            }
          }
        },
        201
      )
    } catch (dbError) {
      console.error('数据库操作失败:', dbError)
      return c.json(
        {
          success: false,
          message: t('auth.registration_success_but_init_failed')
        },
        500
      )
    }
  } catch (error) {
    console.error('验证验证码过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: t('auth.verification_error')
      },
      500
    )
  }
})

// ==================== 用户注册 (保留原有功能) ====================

auth.post('/register', async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { email, password, name, inviteCode } = data
    const env = c.env

    // 使用 Supabase Auth 注册用户
    const { data: authData, error } = await signUpWithEmail(env, email, password)

    if (error) {
      console.error('Supabase 注册失败:', error)

      // 处理常见错误
      if (error.message.includes('already registered')) {
        return c.json(
          {
            success: false,
            message: t('auth.email_already_registered')
          },
          409
        )
      }

      return c.json(
        {
          success: false,
          message: error.message || t('auth.registration_failed')
        },
        400
      )
    }

    if (!authData.user) {
      return c.json(
        {
          success: false,
          message: t('auth.registration_failed_user_not_created')
        },
        500
      )
    }

    try {
      // 在数据库中创建用户记录
      const [dbUser] = await createUser(env, email, authData.user.id)

      // 创建用户配置文件
      if (dbUser) {
        await createUserProfile(env, {
          userId: dbUser.id,
          nickname: name || email.split('@')[0],
          gender: 'other'
        })

        // 🆕 异步初始化新用户积分（不阻塞注册流程）
        initializeNewUserPointsAsync(env, dbUser.id)

        // 处理邀请码（如果提供了）
        if (inviteCode && inviteCode.trim()) {
          try {
            const validationResult = await validateInviteCode(env, inviteCode.trim())

            if (validationResult.valid && validationResult.inviteCode) {
              // 创建推荐关系
              await createReferralRelation(
                env,
                validationResult.inviteCode.userId,
                dbUser.id,
                validationResult.inviteCode.id
              )

              // 更新邀请码使用次数
              await incrementInviteCodeUsage(env, validationResult.inviteCode.id)

              console.log(
                `用户 ${dbUser.id} 通过邀请码 ${inviteCode} 注册成功，邀请人: ${validationResult.inviteCode.userId}`
              )
            } else {
              console.warn(`邀请码处理失败: ${validationResult.error}`)
              // 邀请码处理失败不影响注册流程，只记录日志
            }
          } catch (error) {
            console.error('处理邀请码时出错:', error)
            // 邀请码处理失败不影响注册流程
          }
        }
      }

      return c.json(
        {
          success: true,
          message: t('auth.registration_success_check_email'),
          user: {
            id: authData.user.id,
            email: authData.user.email,
            emailConfirmed: authData.user.email_confirmed_at !== null
          }
        },
        201
      )
    } catch (dbError) {
      console.error('数据库操作失败:', dbError)
      return c.json(
        {
          success: false,
          message: '注册成功但初始化用户信息失败'
        },
        500
      )
    }
  } catch (error) {
    console.error('注册过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: t('auth.registration_error')
      },
      500
    )
  }
})

// ==================== 用户登录 ====================

auth.post('/login', async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { email, password } = data
    const env = c.env
    // 使用已获取的翻译函数 t

    // 使用 Supabase Auth 登录
    const { data: authData, error } = await signInWithEmail(env, email, password)

    if (error) {
      console.error('Supabase 登录失败:', error)
      return c.json(
        {
          success: false,
          message: t('user.login_failed')
        },
        401
      )
    }

    if (!authData.user || !authData.session) {
      return c.json(
        {
          success: false,
          message: t('user.login_failed')
        },
        401
      )
    }

    // 获取或创建数据库用户记录
    let dbUser = await getUserBySupabaseId(env, authData.user.id)

    if (!dbUser) {
      // 如果数据库中没有用户记录，创建一个
      const [newDbUser] = await createUser(env, email, authData.user.id)
      dbUser = newDbUser

      // 创建默认用户配置文件
      if (dbUser) {
        await createUserProfile(env, {
          userId: dbUser.id,
          nickname: email.split('@')[0],
          gender: 'other'
        })
      }
    }

    return c.json(
      {
        success: true,
        message: t('user.login_success'),
        session: {
          access_token: authData.session.access_token,
          refresh_token: authData.session.refresh_token,
          expires_at: authData.session.expires_at,
          user: {
            id: authData.user.id,
            email: authData.user.email,
            emailConfirmed: authData.user.email_confirmed_at !== null,
            dbUserId: dbUser?.id
          }
        }
      },
      200
    )
  } catch (error) {
    console.error('登录过程中出现错误:', error)
    // 使用已获取的翻译函数 t
    return c.json(
      {
        success: false,
        message: t('auth.login_error')
      },
      500
    )
  }
})

// ==================== 登录验证码验证 ====================

// 发送登录验证码
auth.post('/send-login-code', async c => {
  try {
    const t = c.get('t')
    const data = await c.req.json()
    const { email } = data
    const env = c.env

    // 检查数据库中是否存在用户记录
    const existingUsers = await getUser(env, email)
    if (!existingUsers || existingUsers.length === 0) {
      return c.json(
        {
          success: false,
          message: t('auth.user_not_exist_register_first')
        },
        404
      )
    }

    // 生成6位数字验证码
    const verificationCode = generateVerificationCode()

    // 发送登录验证码邮件
    const emailResult = await sendLoginCodeEmail(env, email, verificationCode)

    if (!emailResult.success) {
      console.error('发送登录验证码邮件失败:', emailResult.error)
      return c.json(
        {
          success: false,
          message: '发送验证码失败，请稍后重试'
        },
        400
      )
    }

    // 将验证码存储到 KV（10分钟有效期）
    await storeVerificationCode(env, email, verificationCode, 10)

    return c.json(
      {
        success: true,
        message: '验证码已发送到您的邮箱，请查收'
      },
      200
    )
  } catch (error) {
    console.error('发送登录验证码过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: '发送验证码过程中出现错误'
      },
      500
    )
  }
})

auth.post('/login-code', async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { email, code } = data
    const env = c.env

    // 验证存储的验证码
    const codeVerification = await verifyStoredCode(env, email, code)

    if (!codeVerification.valid) {
      return c.json(
        {
          success: false,
          message: codeVerification.error || '验证码验证失败'
        },
        400
      )
    }

    // 验证码正确，检查用户是否存在
    const existingUsers = await getUser(env, email)
    if (!existingUsers || existingUsers.length === 0) {
      return c.json(
        {
          success: false,
          message: t('auth.user_not_exist_register_first')
        },
        404
      )
    }

    const dbUser = existingUsers[0]

    // 获取 Supabase 用户信息
    const supabase = createSupabaseServiceClient(env)
    const { data: users, error: listError } = await supabase.auth.admin.listUsers()

    if (listError) {
      console.error('获取用户列表失败:', listError)
      return c.json(
        {
          success: false,
          message: t('auth.login_code_failed')
        },
        500
      )
    }

    const authUser = users?.users?.find(u => u.id === dbUser.supabaseUserId)
    if (!authUser) {
      return c.json(
        {
          success: false,
          message: '用户状态异常，请联系客服'
        },
        500
      )
    }

    try {
      // 生成一个临时的强密码
      const tempPassword = 'TempPass_' + Math.random().toString(36).substring(2, 15) + Date.now()

      // 更新用户密码（临时）
      const { error: updateError } = await supabase.auth.admin.updateUserById(authUser.id, {
        password: tempPassword
      })

      if (updateError) {
        console.error('更新临时密码失败:', updateError)
        throw updateError
      }

      // 使用临时密码登录
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email,
        password: tempPassword
      })

      if (loginError || !loginData.session) {
        console.error('临时密码登录失败:', loginError)
        throw loginError
      }

      return c.json(
        {
          success: true,
          message: t('auth.login_success'),
          session: {
            access_token: loginData.session.access_token,
            refresh_token: loginData.session.refresh_token,
            expires_at: loginData.session.expires_at,
            user: {
              id: authUser.id,
              email: authUser.email,
              emailConfirmed: authUser.email_confirmed_at !== null,
              dbUserId: dbUser.id
            }
          }
        },
        200
      )
    } catch (fallbackError) {
      console.error('验证码登录失败:', fallbackError)
      return c.json(
        {
          success: false,
          message: t('auth.login_code_failed')
        },
        500
      )
    }
  } catch (error) {
    console.error('验证码登录过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: t('auth.login_code_error')
      },
      500
    )
  }
})

// ==================== 用户登出 ====================

auth.post('/logout', optionalAuthMiddleware, async c => {
  const t = c.get('t')
  try {
    const env = c.env

    // 从请求头获取 token
    const authHeader = c.req.header('Authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)

      // 使用 Supabase Auth 登出
      await signOut(env, token)
    }

    return c.json(
      {
        success: true,
        message: t('auth.logout_success')
      },
      200
    )
  } catch (error) {
    console.error('登出过程中出现错误:', error)
    // 即使发生错误也返回成功，因为前端可能已清除本地状态
    return c.json(
      {
        success: true,
        message: t('auth.logout_success')
      },
      200
    )
  }
})

// ==================== 获取用户信息 ====================

auth.get('/profile', authMiddleware, async c => {
  const t = c.get('t')
  try {
    const user = c.get('user')
    const env = c.env

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('auth.user_not_found')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)

    return c.json(
      {
        success: true,
        user: {
          id: user.id,
          email: user.email,
          emailConfirmed: user.email_confirmed_at !== null,
          dbUserId: dbUser?.id,
          createdAt: user.created_at,
          lastSignIn: user.last_sign_in_at
        }
      },
      200
    )
  } catch (error) {
    console.error('获取用户信息失败:', error)
    return c.json(
      {
        success: false,
        message: t('auth.get_user_info_failed')
      },
      500
    )
  }
})

// ==================== 获取会话信息 ====================

auth.get('/session', optionalAuthMiddleware, async c => {
  try {
    const t = c.get('t')
    const user = c.get('user')

    if (!user) {
      return c.json(null, 200)
    }

    const env = c.env
    const dbUser = await getUserBySupabaseId(env, user.id)

    // 计算实际的 token 过期时间（Supabase JWT 默认 1 小时）
    // 建议在 token 过期前 10 分钟提醒刷新
    const tokenExpiresAt = new Date(Date.now() + 50 * 60 * 1000) // 50 分钟后提醒刷新
    const sessionExpiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 天后完全过期

    return c.json(
      {
        user: {
          id: user.id,
          email: user.email,
          emailConfirmed: user.email_confirmed_at !== null,
          dbUserId: dbUser?.id
        },
        expires: sessionExpiresAt.toISOString(), // 会话过期时间
        tokenRefreshAt: tokenExpiresAt.toISOString(), // 建议刷新时间
        message: t('auth.token_refresh_suggestion', { minutes: 50 })
      },
      200
    )
  } catch (error) {
    console.error('获取会话信息失败:', error)
    return c.json(null, 200)
  }
})

// ==================== 刷新令牌 ====================

auth.post('/refresh', async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { refresh_token } = data
    const env = c.env

    // 使用 Supabase 刷新令牌
    const supabase = createSupabaseServiceClient(env)

    const { data: refreshData, error } = await supabase.auth.refreshSession({
      refresh_token
    })

    if (error || !refreshData.session || !refreshData.user) {
      console.error('刷新令牌失败:', error)
      return c.json(
        {
          success: false,
          message: t('auth.refresh_token_failed_or_expired')
        },
        401
      )
    }

    // 获取或创建数据库用户记录
    let dbUser = await getUserBySupabaseId(env, refreshData.user.id)

    if (!dbUser) {
      // 如果数据库中没有用户记录，创建一个
      const [newDbUser] = await createUser(env, refreshData.user.email || '', refreshData.user.id)
      dbUser = newDbUser

      // 创建默认用户配置文件
      if (dbUser) {
        await createUserProfile(env, {
          userId: dbUser.id,
          nickname: refreshData.user.email?.split('@')[0] || 'User',
          gender: 'other'
        })
      }
    }

    // 计算下次建议刷新时间（token 过期前 10 分钟）
    const nextRefreshAt = new Date(Date.now() + 50 * 60 * 1000) // 50 分钟后

    return c.json(
      {
        success: true,
        message: t('auth.token_refresh_success'),
        session: {
          access_token: refreshData.session.access_token,
          refresh_token: refreshData.session.refresh_token,
          expires_at: refreshData.session.expires_at,
          user: {
            id: refreshData.user.id,
            email: refreshData.user.email,
            emailConfirmed: refreshData.user.email_confirmed_at !== null,
            dbUserId: dbUser?.id
          }
        },
        // 添加刷新建议信息
        nextRefreshAt: nextRefreshAt.toISOString(),
        refreshInterval: 3000000, // 50 分钟（毫秒）
        message_detail: t('auth.token_refresh_suggestion', { minutes: 50 })
      },
      200
    )
  } catch (error) {
    console.error('刷新令牌过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: t('auth.refresh_token_error')
      },
      500
    )
  }
})

// ==================== 管理员登录 ====================

auth.post('/admin/login', async c => {
  const t = c.get('t')
  try {
    const data = await c.req.json()
    const { email, password } = data
    const env = c.env

    // 使用 Supabase Auth 登录
    const { data: authData, error } = await signInWithEmail(env, email, password)

    if (error) {
      console.error('管理员登录失败:', error)
      return c.json(
        {
          success: false,
          message: t('auth.admin_login_invalid_credentials')
        },
        401
      )
    }

    if (!authData.user || !authData.session) {
      return c.json(
        {
          success: false,
          message: t('auth.admin_login_failed')
        },
        401
      )
    }

    // 检查管理员权限
    // Supabase 可能使用 user_metadata 或 raw_user_meta_data
    const userMetadata =
      authData.user.user_metadata || (authData.user as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    const isAdminByEmail = adminEmails.includes(authData.user.email || '')

    if (!isAdmin && !isAdminByEmail) {
      return c.json(
        {
          success: false,
          message: t('auth.admin_no_permission')
        },
        403
      )
    }

    // 获取或创建数据库用户记录
    let dbUser = await getUserBySupabaseId(env, authData.user.id)

    if (!dbUser) {
      // 创建数据库用户记录
      const newUsers = await createUser(env, authData.user.email!, authData.user.id)
      dbUser = newUsers[0]
    }

    return c.json(
      {
        success: true,
        message: t('auth.admin_login_success'),
        data: {
          user: {
            id: authData.user.id,
            email: authData.user.email,
            isAdmin: true
          },
          token: authData.session.access_token
        }
      },
      200
    )
  } catch (error) {
    console.error('管理员登录过程中出现错误:', error)
    return c.json(
      {
        success: false,
        message: t('auth.admin_login_error')
      },
      500
    )
  }
})

// 获取管理员信息
auth.get('/admin/profile', authMiddleware, async c => {
  const t = c.get('t')
  try {
    const user = c.get('user')
    if (!user) {
      return c.json(
        {
          success: false,
          message: t('auth.not_logged_in')
        },
        401
      )
    }

    // 检查管理员权限
    // Supabase 可能使用 user_metadata 或 raw_user_meta_data
    const userMetadata = user.user_metadata || user.raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    const isAdminByEmail = adminEmails.includes(user.email || '')

    if (!isAdmin && !isAdminByEmail) {
      return c.json(
        {
          success: false,
          message: t('auth.admin_no_permission')
        },
        403
      )
    }

    return c.json(
      {
        success: true,
        data: {
          id: user.id,
          email: user.email,
          isAdmin: true,
          createdAt: user.created_at
        }
      },
      200
    )
  } catch (error) {
    console.error('获取管理员信息失败:', error)
    return c.json(
      {
        success: false,
        message: t('auth.admin_profile_error')
      },
      500
    )
  }
})

// 管理员登出
auth.post('/admin/logout', authMiddleware, async c => {
  const t = c.get('t')
  try {
    const env = c.env
    const authHeader = c.req.header('Authorization')

    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      await signOut(env, token)
    }

    return c.json(
      {
        success: true,
        message: t('auth.admin_logout_success')
      },
      200
    )
  } catch (error) {
    console.error('管理员登出失败:', error)
    return c.json(
      {
        success: false,
        message: t('auth.admin_logout_error')
      },
      500
    )
  }
})

export default auth

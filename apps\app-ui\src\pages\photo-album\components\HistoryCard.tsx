import { motion, AnimatePresence } from 'framer-motion'
import { Icon } from '@iconify/react'
import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import type { PhotoHistory } from '@/stores/photo-generation-store'

interface HistoryCardProps {
  history: PhotoHistory
}

export function HistoryCard({ history }: HistoryCardProps) {
  const { t } = useTranslation('photo-album')
  const [showFullImage, setShowFullImage] = useState(false)

  const handleImageClick = () => {
    if (history.status === 'completed' && history.generatedImageUrl) {
      setShowFullImage(true)
    }
  }

  return (
    <>
      <motion.div
        className="w-full cursor-pointer"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileTap={{ scale: 0.98 }}
        onClick={handleImageClick}
      >
        <div className="relative w-full h-[240px] mx-auto rounded-2xl overflow-hidden">
          <motion.img
            src={history.generatedImageUrl}
            alt={history.templateName}
            className="absolute inset-0 w-full h-full object-cover"
            layoutId={`history-image-${history.id}`}
          />

          {/* 状态遮罩 */}
          {history.status !== 'completed' && (
            <div className="absolute inset-0 bg-black/60 flex items-center justify-center">
              {history.status === 'processing' ? (
                <div className="text-center">
                  <Icon
                    icon="solar:refresh-bold"
                    className="w-8 h-8 text-white animate-spin mb-2 mx-auto"
                  />
                  <div className="text-white text-sm">{t('generation.generating.title')}</div>
                </div>
              ) : (
                <div className="text-center">
                  <Icon
                    icon="solar:close-circle-bold"
                    className="w-8 h-8 text-red-400 mb-2 mx-auto"
                  />
                  <div className="text-white text-sm">{t('generation.failed.title')}</div>
                </div>
              )}
            </div>
          )}

          {/* 底部信息 */}
          <div className="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-black/80 to-transparent">
            <div className="absolute bottom-0 left-0 right-0 p-3">
              <div className="text-white text-sm font-medium">{history.templateName}</div>
              <div className="text-gray-300 text-xs">
                {new Date(history.createdAt).toLocaleDateString()}
              </div>
            </div>
          </div>
        </div>
      </motion.div>

      {/* 全屏图片查看器 */}
      <AnimatePresence>
        {showFullImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 z-50 bg-black flex items-center justify-center"
            onClick={() => setShowFullImage(false)}
          >
            {/* 关闭按钮 */}
            <motion.button
              className="absolute top-6 right-6 z-10 w-12 h-12 bg-black/50 backdrop-blur-sm rounded-full flex items-center justify-center text-white"
              onClick={e => {
                e.stopPropagation()
                setShowFullImage(false)
              }}
              whileTap={{ scale: 0.9 }}
            >
              <Icon icon="solar:close-circle-bold" className="w-6 h-6" />
            </motion.button>

            {/* 图片信息 */}
            <motion.div
              className="absolute bottom-6 left-6 right-6 z-10 bg-black/50 backdrop-blur-sm rounded-2xl p-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="text-white text-lg font-semibold mb-1">{history.templateName}</div>
              <div className="text-gray-300 text-sm">
                {new Date(history.createdAt).toLocaleDateString('zh-CN', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </div>
              {history.pointsUsed && (
                <div className="flex items-center mt-2">
                  <Icon icon="solar:star-bold" className="w-4 h-4 text-yellow-400 mr-1" />
                  <span className="text-yellow-400 text-sm">{t('history.pointsUsed', { points: history.pointsUsed })}</span>
                </div>
              )}
            </motion.div>

            {/* 全屏图片 */}
            <motion.img
              layoutId={`history-image-${history.id}`}
              src={history.generatedImageUrl}
              alt={history.templateName}
              className="max-w-full max-h-full object-contain"
              onClick={e => e.stopPropagation()}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

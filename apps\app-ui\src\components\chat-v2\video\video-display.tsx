import { useState, useRef, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { VideoDrawer } from '@/components/common/video-drawer'

interface VideoDisplayProps {
  videoUrl: string
  title?: string
  className?: string
}

export const VideoDisplay = ({ videoUrl, title, className = '' }: VideoDisplayProps) => {
  const { t } = useTranslation('chat-v2')
  const [isVideoModalOpen, setIsVideoModalOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [posterUrl, setPosterUrl] = useState<string>('')
  const [useDefaultPoster, setUseDefaultPoster] = useState(false)
  const videoRef = useRef<HTMLVideoElement>(null)
  const thumbnailGeneratedRef = useRef(false) // 使用ref而不是state，避免重渲染

  // 当视频URL变化时，重置所有状态
  useEffect(() => {
    setIsLoading(true)
    setHasError(false)
    setPosterUrl('')
    setUseDefaultPoster(false)
    thumbnailGeneratedRef.current = false // 重置生成标记
  }, [videoUrl])

  // 默认视频封面 - 使用SVG生成
  const defaultPoster = `data:image/svg+xml;base64,${btoa(`
    <svg width="720" height="1280" viewBox="0 0 720 1280" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect width="720" height="1280" fill="#1f2937"/>
      <circle cx="360" cy="640" r="60" fill="#6b7280" opacity="0.5"/>
      <path d="M330 610 L330 670 L390 640 Z" fill="#e5e7eb"/>
    </svg>
  `)}`

  // 生成视频缩略图作为封面
  const generateThumbnail = async (video: HTMLVideoElement): Promise<string> => {
    return new Promise(resolve => {
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')

      if (!ctx) {
        resolve('')
        return
      }

      // 尝试多个时间点，找到第一个非黑帧
      const timePoints = [0.1, 0.5, 1.0, 2.0] // 多个时间点
      let currentIndex = 0

      const tryNextTimePoint = () => {
        if (currentIndex >= timePoints.length) {
          console.warn(`⚠️ [VideoDisplay] ${t('video.thumbnail_generation_failed_all_timepoints')}`)
          resolve('')
          return
        }

        const targetTime = Math.min(timePoints[currentIndex], video.duration || 1.0)
        currentIndex++

        // 临时事件处理器
        const handleSeeked = () => {
          video.removeEventListener('seeked', handleSeeked)

          // 等待一小段时间确保帧渲染完成
          setTimeout(() => {
            try {
              // 设置画布尺寸
              canvas.width = video.videoWidth || 720
              canvas.height = video.videoHeight || 1280

              // 绘制视频帧
              ctx.drawImage(video, 0, 0, canvas.width, canvas.height)

              // 转换为data URL
              const dataURL = canvas.toDataURL('image/jpeg', 0.8)

              // 检查是否是有效图片（不是全黑）
              if (isValidThumbnail(canvas)) {
                console.log(
                  `🖼️ [VideoDisplay] ${t('video.thumbnail_generation_success', {
                    time: targetTime
                  })}`
                )
                resolve(dataURL)
              } else {
                console.warn(
                  `⚠️ [VideoDisplay] ${t('video.thumbnail_generation_black_frame', {
                    time: targetTime
                  })}`
                )
                tryNextTimePoint() // 尝试下一个时间点
              }
            } catch (error) {
              console.error(`❌ [VideoDisplay] ${t('video.thumbnail_generation_error')}:`, error)
              tryNextTimePoint() // 尝试下一个时间点
            }
          }, 100) // 等待100ms确保渲染完成
        }

        // 监听seeked事件
        video.addEventListener('seeked', handleSeeked)

        // seek到目标时间
        video.currentTime = targetTime

        // 如果已经在正确位置，直接触发
        if (Math.abs(video.currentTime - targetTime) < 0.01) {
          handleSeeked()
        }
      }

      // 开始尝试第一个时间点
      tryNextTimePoint()
    })
  }

  // 检查缩略图是否有效（不是全黑）
  const isValidThumbnail = (canvas: HTMLCanvasElement): boolean => {
    const ctx = canvas.getContext('2d')
    if (!ctx) return false

    // 获取中心区域的像素数据
    const centerX = Math.floor(canvas.width / 2)
    const centerY = Math.floor(canvas.height / 2)
    const imageData = ctx.getImageData(centerX - 10, centerY - 10, 20, 20)
    const data = imageData.data

    // 检查是否所有像素都是黑色或接近黑色
    let nonBlackPixels = 0
    for (let i = 0; i < data.length; i += 4) {
      const r = data[i]
      const g = data[i + 1]
      const b = data[i + 2]

      // 如果RGB值都大于30，认为不是黑色
      if (r > 30 || g > 30 || b > 30) {
        nonBlackPixels++
      }
    }

    // 如果超过10%的像素不是黑色，认为是有效缩略图
    const totalPixels = imageData.data.length / 4
    return nonBlackPixels / totalPixels > 0.1
  }

  // 处理视频加载
  const handleVideoLoad = async () => {
    const video = videoRef.current
    if (!video || thumbnailGeneratedRef.current) {
      // 如果已经生成过缩略图，直接返回
      if (thumbnailGeneratedRef.current) {
        setIsLoading(false)
      }
      return
    }

    // 设置生成标记，防止重复执行
    thumbnailGeneratedRef.current = true

    try {
      // 生成缩略图
      const thumbnail = await generateThumbnail(video)
      if (thumbnail) {
        setPosterUrl(thumbnail)
      } else {
        setUseDefaultPoster(true) // 缩略图生成失败，使用默认封面
      }
    } catch (error) {
      setUseDefaultPoster(true) // 缩略图生成失败，使用默认封面
    }

    setIsLoading(false)
  }

  return (
    <>
      <div
        className={`w-full aspect-[5/7] rounded-lg overflow-hidden cursor-pointer relative group bg-gray-100 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 ${className}`}
        onClick={() => setIsVideoModalOpen(true)}
      >
        {/* 背景缩略图 */}
        {(posterUrl || useDefaultPoster) && (
          <img
            src={posterUrl || defaultPoster}
            alt={t('video.thumbnail')}
            className="absolute inset-0 w-full h-full object-cover"
            onError={() => {
              console.warn(`⚠️ [VideoDisplay] ${t('video.thumbnail_loading_failed')}`)
              if (posterUrl) {
                // 如果自定义封面失败，使用默认封面
                setPosterUrl('')
                setUseDefaultPoster(true)
              }
            }}
          />
        )}

        <video
          ref={videoRef}
          src={videoUrl}
          muted
          autoPlay={false}
          loop={false}
          preload="metadata"
          playsInline
          webkit-playsinline="true"
          x5-playsinline="true"
          controls={false}
          disablePictureInPicture
          className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          style={{
            objectFit: 'cover',
            backgroundColor: '#f3f4f6',
            opacity: posterUrl || useDefaultPoster ? 0 : 1 // 有封面时隐藏video元素
          }}
          onLoadStart={() => {
            console.log(`🎬 [VideoDisplay] ${t('video.loading_start')}:`, videoUrl.substring(0, 50))
          }}
          onLoadedData={() => {
            console.log(`✅ [VideoDisplay] ${t('video.loading_complete')}`)
          }}
          onCanPlayThrough={() => {
            console.log(`✅ [VideoDisplay] ${t('video.can_play_smoothly')}`)
            handleVideoLoad()
          }}
          onError={error => {
            console.error(`❌ ${t('video.playback_error')}:`, error)
            setHasError(true)
            setIsLoading(false)
          }}
        />

        {/* 加载状态 - 只在没有任何封面时显示 */}
        {isLoading && !hasError && !posterUrl && !useDefaultPoster && (
          <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
            <div className="flex flex-col items-center space-y-3">
              <div className="w-8 h-8 border-2 border-gray-400 border-t-blue-500 rounded-full animate-spin"></div>
              <span className="text-sm text-gray-600 dark:text-gray-300">{t('video.loading')}</span>
            </div>
          </div>
        )}

        {/* 缩略图加载中状态 */}
        {isLoading && !hasError && (posterUrl || useDefaultPoster) && (
          <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
            <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}

        {/* 错误状态 */}
        {hasError && (
          <div className="absolute inset-0 bg-gray-100 dark:bg-gray-800 flex flex-col items-center justify-center text-gray-500 border-2 border-dashed border-gray-300 dark:border-gray-600">
            <svg className="w-12 h-12 mb-2 text-gray-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" />
            </svg>
            <span className="text-sm">{t('video.loading_failed')}</span>
            <button
              onClick={e => {
                e.stopPropagation()
                setHasError(false)
                setIsLoading(true)
                setPosterUrl('')
                setUseDefaultPoster(false) // 重置默认封面状态
                thumbnailGeneratedRef.current = false // 重置生成标记
                // 重新触发视频加载
                const video = videoRef.current
                if (video) {
                  video.load()
                }
              }}
              className="mt-2 px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              {t('video.retry')}
            </button>
          </div>
        )}

        {/* 播放按钮覆盖层 - 只在视频加载成功后显示 */}
        {!isLoading && !hasError && (
          <>
            {/* 渐变遮罩 */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

            {/* 中心播放按钮 */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-14 h-14 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center group-hover:bg-white group-hover:scale-110 transition-all duration-300 shadow-lg">
                <svg
                  className="w-6 h-6 text-gray-800 ml-0.5"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                  aria-label={t('video.play')}
                >
                  <path d="M8 5v14l11-7z" />
                </svg>
              </div>
            </div>

            {/* 右下角视频图标标识 */}
            <div className="absolute bottom-2 right-2 bg-black/50 backdrop-blur-sm rounded-full p-1.5 group-hover:bg-black/70 transition-colors duration-300">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17,10.5V7A1,1 0 0,0 16,6H4A1,1 0 0,0 3,7V17A1,1 0 0,0 4,18H16A1,1 0 0,0 17,17V13.5L21,17.5V6.5L17,10.5Z" />
              </svg>
            </div>
          </>
        )}
      </div>

      {/* 视频播放模态框 */}
      <VideoDrawer
        isOpen={isVideoModalOpen}
        onClose={() => setIsVideoModalOpen(false)}
        videoUrl={videoUrl}
        title={title || t('video.generated_video')}
      />
    </>
  )
}

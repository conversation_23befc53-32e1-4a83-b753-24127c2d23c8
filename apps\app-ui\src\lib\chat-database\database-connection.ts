import { CapacitorSQLite, SQLiteConnection } from '@capacitor-community/sqlite'
import { Capacitor } from '@capacitor/core'
import type { ChatDatabaseConfig } from './types'
import { ChatDatabaseError, DEFAULT_DATABASE_CONFIG } from './types'

/**
 * 数据库连接管理器
 * 负责SQLite连接的创建、配置和管理
 */
export class DatabaseConnection {
  private sqlite: SQLiteConnection
  private db: any = null
  private isConnected = false
  private config: ChatDatabaseConfig

  constructor(config: ChatDatabaseConfig = DEFAULT_DATABASE_CONFIG) {
    this.config = { ...DEFAULT_DATABASE_CONFIG, ...config }
    this.sqlite = new SQLiteConnection(CapacitorSQLite)
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<boolean> {
    try {
      console.log('🔌 [DB Connection] 开始初始化数据库连接')

      // 检查是否已连接
      if (this.isConnected) {
        console.log('✅ [DB Connection] 数据库已连接，跳过')
        return true
      }

      // 检查平台兼容性
      if (!Capacitor.isNativePlatform()) {
        console.warn('⚠️ [DB Connection] 当前平台不支持SQLite，使用内存模式')
        return false
      }

      // 关闭现有连接（如果存在）
      await this.closeExistingConnection()

      // 创建或打开数据库
      await this.createOrOpenDatabase()

      // 打开数据库连接
      await this.db.open()

      // 配置数据库设置
      await this.configureDatabase()

      this.isConnected = true
      console.log('✅ [DB Connection] 数据库连接初始化完成')

      return true
    } catch (error) {
      console.error('❌ [DB Connection] 数据库连接初始化失败:', error)
      this.isConnected = false
      this.db = null
      throw new ChatDatabaseError('数据库连接初始化失败', 'CONNECTION_INIT_FAILED', error as Error)
    }
  }

  /**
   * 关闭现有连接
   */
  private async closeExistingConnection(): Promise<void> {
    try {
      if (this.db) {
        await this.db.close()
        this.db = null
      }
    } catch (error) {
      console.log('🔄 [DB Connection] 清理旧连接')
    }
  }

  /**
   * 创建或打开数据库
   */
  private async createOrOpenDatabase(): Promise<void> {
    const isDbExists = await this.sqlite.isDatabase(this.config.dbName)

    if (!isDbExists.result) {
      console.log('🔨 [DB Connection] 创建新数据库')
      this.db = await this.sqlite.createConnection(
        this.config.dbName,
        false,
        'no-encryption',
        this.config.version,
        false
      )
    } else {
      console.log('📂 [DB Connection] 打开已存在的数据库')
      try {
        this.db = await this.sqlite.createConnection(
          this.config.dbName,
          false,
          'no-encryption',
          this.config.version,
          false
        )
      } catch (error) {
        // 如果连接已存在，尝试获取现有连接
        console.log('🔄 [DB Connection] 获取现有连接')
        try {
          this.db = await this.sqlite.retrieveConnection(this.config.dbName, false)
        } catch (retrieveError) {
          // 如果获取失败，尝试关闭并重新创建
          console.log('🔄 [DB Connection] 重新创建连接')
          try {
            await this.sqlite.closeConnection(this.config.dbName, false)
          } catch (closeError) {
            // 忽略关闭错误
          }
          this.db = await this.sqlite.createConnection(
            this.config.dbName,
            false,
            'no-encryption',
            this.config.version,
            false
          )
        }
      }
    }
  }

  /**
   * 配置数据库设置
   */
  private async configureDatabase(): Promise<void> {
    try {
      console.log('⚙️ [DB Connection] 开始配置数据库设置')

      // 确保没有活动事务
      try {
        await this.db.execute('COMMIT;')
      } catch {
        // 忽略没有事务的错误
      }

      // 设置同步模式为NORMAL以提高性能
      try {
        await this.db.execute('PRAGMA synchronous = NORMAL;')
        console.log('✅ [DB Connection] 同步模式设置成功')
      } catch (syncError) {
        console.warn('⚠️ [DB Connection] 同步模式设置失败:', syncError)
      }

      // 设置临时存储为内存
      try {
        await this.db.execute('PRAGMA temp_store = MEMORY;')
        console.log('✅ [DB Connection] 临时存储设置成功')
      } catch (tempError) {
        console.warn('⚠️ [DB Connection] 临时存储设置失败:', tempError)
      }

      // 启用外键约束
      if (this.config.enableForeignKeys) {
        try {
          await this.db.execute('PRAGMA foreign_keys = ON;')
          console.log('✅ [DB Connection] 外键约束启用成功')
        } catch (fkError) {
          console.warn('⚠️ [DB Connection] 外键约束启用失败:', fkError)
        }
      }

      // 设置WAL模式（最后设置，最容易失败）
      if (this.config.enableWAL) {
        try {
          const result = await this.db.query('PRAGMA journal_mode;')
          const currentMode = result.values?.[0]?.journal_mode

          if (currentMode !== this.config.journalMode) {
            console.log(
              `🔄 [DB Connection] 切换journal模式: ${currentMode} → ${this.config.journalMode}`
            )
            await this.db.execute(`PRAGMA journal_mode = ${this.config.journalMode};`)
            console.log('✅ [DB Connection] WAL模式设置成功')
          } else {
            console.log(`✅ [DB Connection] Journal模式已是 ${currentMode}`)
          }
        } catch (walError) {
          console.warn('⚠️ [DB Connection] WAL模式设置失败，继续使用默认模式:', walError)
          // WAL模式失败不影响数据库使用
        }
      }

      console.log('✅ [DB Connection] 数据库配置完成')
    } catch (error) {
      console.warn('⚠️ [DB Connection] 数据库配置失败:', error)
      // 配置失败不影响基本功能
    }
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    if (this.db && this.isConnected) {
      await this.db.close()
      this.isConnected = false
      this.db = null
      console.log('🔒 [DB Connection] 数据库连接已关闭')
    }
  }

  /**
   * 获取数据库实例
   */
  getDatabase(): any {
    if (!this.isConnected || !this.db) {
      throw new ChatDatabaseError('数据库未连接', 'NOT_CONNECTED')
    }
    return this.db
  }

  /**
   * 检查连接状态
   */
  isInitialized(): boolean {
    return this.isConnected && this.db !== null
  }

  /**
   * 获取配置
   */
  getConfig(): ChatDatabaseConfig {
    return this.config
  }
}

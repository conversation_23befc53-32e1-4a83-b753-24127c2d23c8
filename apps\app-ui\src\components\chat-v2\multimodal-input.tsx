import type { Lang<PERSON>hainMessage, Attachment } from './type'

type ChatStatus = 'idle' | 'streaming' | 'submitted' | 'error'
import { useState, useRef, useCallback, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import {
  <PERSON><PERSON>,
  Textarea,
  <PERSON><PERSON><PERSON>,
  Drawer,
  <PERSON>er<PERSON><PERSON><PERSON>,
  Drawer<PERSON>eader,
  DrawerBody,
  useDisclosure,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem,
  addToast
} from '@heroui/react'
import { Icon } from '@iconify/react'
import cx from 'classnames'
import { useLocalStorage, useWindowSize } from 'usehooks-ts'
import { DeviceConnectCore } from '@/components/device'
import { useDeviceStore } from '../../stores/device-store'
import { commandQueueManager } from '../../pages/interactive/utils/bluetoothUtils'
import { VoiceInlineRecorder } from '../voice'
import type { VoiceInlineRecorderRef } from '../voice/VoiceInlineRecorder'
import { useSpeechToText } from '@/hooks/useSpeechToText'

// 定义扩展的ChatRequestOptions类型
interface ExtendedChatRequestOptions extends Record<string, any> {
  experimental_attachments?: Attachment[]
  deviceInfo?: string
  isVoiceMessage?: boolean
  audioBlob?: Blob
  audioDuration?: number
  voiceText?: string
}

export interface MultimodalInputV2Props {
  chatId: string
  input: string
  setInput: (value: string) => void
  handleSubmit: (
    event?: React.FormEvent | { preventDefault?: () => void },
    chatRequestOptions?: any
  ) => void
  status: ChatStatus
  stop: () => void
  attachments: Array<Attachment>
  setAttachments: (attachments: Array<Attachment>) => void
  messages: Array<LangChainMessage>
  setMessages: (messages: Array<LangChainMessage>) => void
  append: (message: LangChainMessage) => void
  role: string
}

export function MultimodalInputV2({
  chatId,
  input,
  setInput,
  handleSubmit,
  status,
  stop,
  attachments,
  setAttachments,
  messages,
  setMessages,
  append,
  role
}: MultimodalInputV2Props) {
  const { t } = useTranslation('chat-v2')
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const voiceRecorderRef = useRef<VoiceInlineRecorderRef>(null)
  const { width } = useWindowSize()
  const [justSent, setJustSent] = useState(false)
  // 使用全局设备状态
  const { connectedDevice, connectDevice, disconnectDevice, enterFunction, exitFunction } =
    useDeviceStore()
  const { isOpen, onOpen, onOpenChange } = useDisclosure()

  // 语音输入管理
  const [isVoiceMode, setIsVoiceMode] = useState(false) // 是否处于语音模式
  const [voiceRecorderState, setVoiceRecorderState] = useState<
    'idle' | 'click-recording' | 'click-recorded' | 'longpress-recording' | 'processing'
  >('idle')
  const [isVoiceProcessing, setIsVoiceProcessing] = useState(false) // 语音识别中

  // 语音转文本
  const { convert: convertToText } = useSpeechToText()

  // 切换语音/键盘模式
  const toggleInputMode = () => {
    if (isVoiceMode) {
      // 从语音模式切换到键盘模式
      setIsVoiceMode(false)
      setVoiceRecorderState('idle')
      // 清理可能的录制状态
      if (voiceRecorderRef.current) {
        voiceRecorderRef.current.cancelRecording()
      }
    } else {
      // 从键盘模式切换到语音模式
      setIsVoiceMode(true)
    }
  }

  const resetHeight = () => {
    // 重置时清除任何手动设置的样式
    if (textareaRef.current) {
      textareaRef.current.style.height = ''
    }
  }

  const [localStorageInput, setLocalStorageInput] = useLocalStorage('input', '')

  useEffect(() => {
    if (textareaRef.current) {
      const domValue = textareaRef.current.value
      // Prefer DOM value over localStorage to handle hydration
      const finalValue = domValue || localStorageInput || ''
      setInput(finalValue)
      // HeroUI的Textarea会自动调整高度
    }
    // Only run once after hydration
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    setLocalStorageInput(input)
  }, [input, setLocalStorageInput])

  const handleInput = (value: string) => {
    setInput(value)
    // HeroUI的Textarea会自动调整高度
  }

  const isLoading = status === 'streaming' || status === 'submitted'

  const submitForm = useCallback(() => {
    window.history.replaceState({}, '', `/chat/${chatId}?role=${role}`)

    // 创建自定义选项对象
    const customOptions: ExtendedChatRequestOptions = {
      experimental_attachments: attachments
    }

    // 检查是否有设备信息
    const hasDeviceInfo = connectedDevice?.func && connectedDevice.func.length > 0

    // 如果有连接的设备，添加设备信息
    if (hasDeviceInfo) {
      // 构建设备功能映射
      const deviceInfoObj: Record<string, Record<string, string>> = {}

      connectedDevice.func.forEach(func => {
        deviceInfoObj[func.key] = {}
        func.commands.forEach(cmd => {
          deviceInfoObj[func.key][cmd.intensity.toString()] = cmd.command
        })
      })

      // 将设备信息转为JSON字符串，并添加到设备标签中
      const deviceInfoTag = `<device>${JSON.stringify(deviceInfoObj)}</device>`

      // 添加到自定义选项中
      customOptions.deviceInfo = deviceInfoTag
    }

    handleSubmit(undefined, customOptions)

    // 统一处理清理逻辑
    setAttachments([])
    setLocalStorageInput('')
    resetHeight()
    setJustSent(true)

    // 发送普通消息后保持当前输入模式（不自动切换）

    // 重置发送状态
    setTimeout(() => {
      setJustSent(false)
    }, 1000)

    if (width && width > 768) {
      textareaRef.current?.focus()
    }
  }, [
    attachments,
    handleSubmit,
    setAttachments,
    setLocalStorageInput,
    width,
    chatId,
    role,
    connectedDevice
  ])

  // 处理设备连接
  const handleDeviceConnect = async (device: any) => {
    try {
      await connectDevice(device, 'chat') // 标记为对话功能连接
      onOpenChange()
    } catch (error) {
      console.error('设备连接失败:', error)
    }
  }

  // 页面生命周期管理
  useEffect(() => {
    // 进入对话功能时，如果有连接的设备，切换到对话模式
    if (connectedDevice) {
      enterFunction('chat')
    }

    // 页面退出时的清理函数
    const handlePageExit = () => {
      if (connectedDevice) {
        console.log(`🛑 ${t('multimodal.page_exit_disconnect_device')}`)

        try {
          // 发送所有功能的停止命令
          const stopCommands: string[] = []
          connectedDevice.func.forEach(func => {
            const stopCommand = func.commands.find(cmd => cmd.intensity === -1)
            if (stopCommand) {
              stopCommands.push(stopCommand.command)
            }
          })

          if (stopCommands.length > 0) {
            console.log(`${t('multimodal.sending_stop_commands')}:`, stopCommands)
            commandQueueManager.addCommands(stopCommands)
          }

          // 强制断开设备连接
          exitFunction('chat')
          disconnectDevice()
        } catch (error) {
          console.error(`${t('multimodal.disconnect_device_error')}:`, error)
        }
      }
    }

    // 监听页面卸载事件
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      handlePageExit()
    }

    // 监听 visibilitychange 事件（页面切换、最小化等）
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden' && connectedDevice) {
        console.log(`🛑 ${t('multimodal.page_hidden_stop_device')}`)
        try {
          const stopCommands: string[] = []
          connectedDevice.func.forEach(func => {
            const stopCommand = func.commands.find(cmd => cmd.intensity === -1)
            if (stopCommand) {
              stopCommands.push(stopCommand.command)
            }
          })

          if (stopCommands.length > 0) {
            commandQueueManager.addCommands(stopCommands)
          }
        } catch (error) {
          console.error('页面隐藏时发送停止命令失败:', error)
        }
      }
    }

    // 添加事件监听器
    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 组件卸载时的清理
    return () => {
      // 移除事件监听器
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('visibilitychange', handleVisibilityChange)

      // 执行页面退出清理逻辑
      handlePageExit()
    }
  }, [connectedDevice, enterFunction, exitFunction, disconnectDevice])

  // 处理语音录制状态变化
  const handleVoiceStateChange = useCallback(
    (
      state: 'idle' | 'click-recording' | 'click-recorded' | 'longpress-recording' | 'processing'
    ) => {
      setVoiceRecorderState(state)
    },
    []
  )

  // 处理语音提交
  const handleVoiceSubmit = useCallback(
    async (audioBlob: Blob) => {
      setIsVoiceProcessing(true) // 开始语音识别loading

      try {
        // 语音转文本
        const recognizedText = await convertToText(audioBlob)

        if (recognizedText && recognizedText.trim()) {
          // 构建设备信息选项（如果有连接的设备）
          const customOptions: ExtendedChatRequestOptions = {
            experimental_attachments: attachments,
            voiceText: recognizedText // 直接传递识别的文本
          }

          // 检查是否有设备信息
          const hasDeviceInfo = connectedDevice?.func && connectedDevice.func.length > 0

          // 如果有连接的设备，添加设备信息
          if (hasDeviceInfo) {
            // 构建设备功能映射
            const deviceInfoObj: Record<string, Record<string, string>> = {}

            connectedDevice.func.forEach(func => {
              deviceInfoObj[func.key] = {}
              func.commands.forEach(cmd => {
                deviceInfoObj[func.key][cmd.intensity.toString()] = cmd.command
              })
            })

            // 将设备信息转为JSON字符串，并添加到设备标签中
            const deviceInfoTag = `<device>${JSON.stringify(deviceInfoObj)}</device>`

            // 添加到自定义选项中
            customOptions.deviceInfo = deviceInfoTag
          }

          // 直接发送语音消息，无需等待input状态更新
          window.history.replaceState({}, '', `/chat/${chatId}?role=${role}`)
          handleSubmit(undefined, customOptions)

          // 统一处理清理逻辑
          setAttachments([])
          setInput('') // 清空输入框
          setLocalStorageInput('')
          resetHeight()
          setJustSent(true)
          setIsVoiceProcessing(false) // 结束loading

          // 重置发送状态
          setTimeout(() => {
            setJustSent(false)
          }, 1000)

          if (width && width > 768) {
            textareaRef.current?.focus()
          }
        } else {
          setIsVoiceProcessing(false) // 结束loading
          // 可以显示错误提示
        }
      } catch (error) {
        console.error('语音识别错误:', error)
        setIsVoiceProcessing(false) // 结束loading
        // 可以显示错误提示
      }
    },
    [
      convertToText,
      setInput,
      attachments,
      connectedDevice,
      handleSubmit,
      chatId,
      role,
      setAttachments,
      setLocalStorageInput,
      width
    ]
  )

  return (
    <div className="flex w-full flex-col gap-4">
      {/* 主输入表单 - 优化后的现代化布局 */}
      <form className="flex w-full items-center gap-3 p-1">
        {/* 左侧操作按钮区 */}
        <div className="flex items-center gap-2">
          {/* 加号按钮 - 集成设备连接 */}
          {connectedDevice ? (
            <Dropdown>
              <DropdownTrigger>
                <Button isIconOnly size="sm" variant="light" className="min-w-9 w-9 h-9">
                  <Icon className="text-success-600" icon="solar:devices-bold" width={18} />
                </Button>
              </DropdownTrigger>
              <DropdownMenu aria-label="设备操作">
                <DropdownItem key="device-info" isReadOnly textValue="设备信息">
                  <div className="flex items-center space-x-2">
                    {connectedDevice.pic ? (
                      <img
                        src={connectedDevice.pic}
                        alt={connectedDevice.name}
                        className="w-6 h-6 rounded object-cover bg-white"
                        onError={e => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                    ) : (
                      <Icon icon="solar:devices-bold" className="w-4 h-4 text-success-600" />
                    )}
                    <div>
                      <p className="text-sm font-medium">{connectedDevice.name}</p>
                      <p className="text-xs text-success-600">{t('multimodal_input.connected')}</p>
                    </div>
                  </div>
                </DropdownItem>
                <DropdownItem
                  key="disconnect"
                  className="text-danger"
                  color="danger"
                  startContent={<Icon icon="solar:logout-2-linear" width={16} />}
                  onPress={async () => {
                    // 设备断开逻辑
                    if (connectedDevice?.func) {
                      try {
                        const stopCommands: string[] = []
                        connectedDevice.func.forEach(func => {
                          const stopCommand = func.commands.find(cmd => cmd.intensity === -1)
                          if (stopCommand) {
                            stopCommands.push(stopCommand.command)
                          }
                        })
                        if (stopCommands.length > 0) {
                          commandQueueManager.addCommands(stopCommands)
                        }
                      } catch (error) {
                        console.error(t('multimodal_input.stop_device_failed') + ':', error)
                        addToast({
                          title: t('multimodal_input.stop_device_failed'),
                          color: 'danger'
                        })
                      }
                    }
                    disconnectDevice()
                    addToast({
                      title: t('multimodal_input.device_disconnected'),
                      color: 'success'
                    })
                  }}
                >
                  {t('multimodal_input.disconnect_device')}
                </DropdownItem>
              </DropdownMenu>
            </Dropdown>
          ) : (
            <Button
              isIconOnly
              size="sm"
              variant="light"
              className="min-w-9 w-9 h-9 border border-divider rounded-xl"
              onPress={onOpen}
            >
              <Icon className="text-default-500" icon="solar:devices-bold" width={18} />
            </Button>
          )}

          {/* 语音/键盘切换按钮 */}
          <Button
            isIconOnly
            size="sm"
            variant="light"
            className="min-w-9 w-9 h-9 border border-divider rounded-xl"
            onPress={toggleInputMode}
            isDisabled={isLoading}
          >
            <Icon
              className="text-default-500"
              icon={isVoiceMode ? 'solar:keyboard-linear' : 'solar:microphone-bold'}
              width={18}
            />
          </Button>
        </div>

        {/* 中间输入区域 - 根据输入模式切换 */}
        {isVoiceMode ? (
          isVoiceProcessing ? (
            <div className="flex-1 min-w-0 flex items-center justify-center bg-primary-50 border border-primary-200 rounded-xl px-4 py-3">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 border-2 border-primary-600 border-t-transparent rounded-full animate-spin"></div>
                <span className="text-primary-600 text-sm font-medium">
                  {t('multimodal_input.recognizing_voice')}
                </span>
              </div>
            </div>
          ) : (
            <VoiceInlineRecorder
              ref={voiceRecorderRef}
              onSubmit={handleVoiceSubmit}
              onCancel={() => {
                // 取消录制但保持语音模式
                setVoiceRecorderState('idle')
              }}
              onStateChange={handleVoiceStateChange}
              disabled={isLoading || isVoiceProcessing}
              maxDuration={60}
            />
          )
        ) : (
          <div className="flex-1 min-w-0">
            <Textarea
              ref={textareaRef}
              placeholder={
                justSent ? t('multimodal_input.message_sent') : t('multimodal_input.input_message')
              }
              value={input}
              onValueChange={handleInput}
              classNames={{
                inputWrapper: 'rounded-xl',
                input: cx(
                  'text-medium placeholder:text-default-400',
                  justSent && 'text-success-600'
                )
              }}
              minRows={1}
              maxRows={4}
              radius="none"
              variant="flat"
              onKeyDown={event => {
                if (event.key === 'Enter' && !event.shiftKey && !event.nativeEvent.isComposing) {
                  event.preventDefault()
                  if (isLoading) {
                    // toast.error('请等待模型完成回复!')
                  } else {
                    submitForm()
                  }
                }
              }}
            />
          </div>
        )}

        {/* 右侧发送按钮 */}
        <div className="flex items-center">
          <Tooltip
            showArrow
            content={
              isLoading ? t('multimodal_input.stop_generating') : t('multimodal_input.send_message')
            }
          >
            <Button
              isIconOnly
              color={
                isLoading
                  ? 'danger'
                  : voiceRecorderState === 'click-recorded' ||
                    voiceRecorderState === 'click-recording'
                  ? 'primary' // 语音录制状态使用主题色
                  : !input
                  ? 'default'
                  : 'primary' // 普通发送使用主题色
              }
              isDisabled={!isLoading && !input && voiceRecorderState === 'idle'}
              radius="lg"
              size="sm"
              variant="solid"
              className="min-w-9 w-9 h-9"
              onPress={() => {
                if (isLoading) {
                  stop()
                } else {
                  if (voiceRecorderState === 'click-recorded') {
                    voiceRecorderRef.current?.cancelRecording()
                  } else if (voiceRecorderState === 'click-recording') {
                    voiceRecorderRef.current?.cancelRecording()
                  } else {
                    submitForm()
                  }
                }
              }}
            >
              {isLoading ? (
                <Icon className="text-white" icon="solar:stop-bold" width={16} />
              ) : voiceRecorderState === 'click-recorded' ||
                voiceRecorderState === 'click-recording' ? (
                <Icon className="text-white" icon="material-symbols:close" width={18} />
              ) : (
                <Icon
                  className={cx(
                    '[&>path]:stroke-[2px]',
                    !input && voiceRecorderState === 'idle'
                      ? 'text-default-600'
                      : 'text-primary-foreground'
                  )}
                  icon="solar:arrow-up-linear"
                  width={18}
                />
              )}
            </Button>
          </Tooltip>
        </div>
      </form>

      {/* 设备连接抽屉 */}
      <Drawer
        isOpen={isOpen}
        onOpenChange={onOpenChange}
        placement="bottom"
        size="lg"
        classNames={{
          wrapper: 'z-101'
        }}
      >
        <DrawerContent>
          {onClose => (
            <>
              <DrawerHeader>
                <h2 className="text-xl font-semibold">{t('multimodal_input.connect_device')}</h2>
              </DrawerHeader>
              <DrawerBody className="p-6">
                <DeviceConnectCore
                  onDeviceConnect={handleDeviceConnect}
                  showSkipButton={false}
                  connectButtonText={t('multimodal_input.connect_device')}
                />
              </DrawerBody>
            </>
          )}
        </DrawerContent>
      </Drawer>
    </div>
  )
}

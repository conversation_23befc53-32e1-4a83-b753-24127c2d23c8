import React, { useState, useEffect, useCallback, useRef } from 'react'
import { Button, Input, Spinner } from '@heroui/react'
import { type SupportedDevicesResponse, type Device } from '@/api/services/devices'
import { useDeviceStore } from '@/stores/device-store'
import { Keyboard } from '@capacitor/keyboard'
import { Capacitor } from '@capacitor/core'

interface DeviceConnectCoreProps {
  onDeviceConnect: (device: Device) => void
  onSkip?: () => void
  showSkipButton?: boolean
  connectButtonText?: string
  skipButtonText?: string
  isConnecting?: boolean
  onConnectingChange?: (connecting: boolean) => void
  supportedDevices?: SupportedDevicesResponse['devices']
  isLoadingDevices?: boolean
  className?: string
}

/**
 * 设备连接核心组件
 * 可复用的设备连接逻辑，支持不同的业务场景
 */
export const DeviceConnectCore: React.FC<DeviceConnectCoreProps> = ({
  onDeviceConnect,
  onSkip,
  showSkipButton = true,
  connectButtonText = '连接设备',
  skipButtonText = '跳过连接',
  isConnecting: externalIsConnecting,
  onConnectingChange,
  supportedDevices: externalSupportedDevices,
  isLoadingDevices: externalIsLoadingDevices,
  className = ''
}) => {
  const [internalIsConnecting, setInternalIsConnecting] = useState(false)
  const [deviceCode, setDeviceCode] = useState('')
  const [error, setError] = useState('')
  const [isScanning, setIsScanning] = useState(false)
  const [internalSupportedDevices, setInternalSupportedDevices] = useState<
    SupportedDevicesResponse['devices']
  >([])
  const [internalIsLoadingDevices, setInternalIsLoadingDevices] = useState(false)
  const deviceCodeInputRef = useRef<HTMLInputElement>(null)

  // 使用全局设备store的缓存功能
  const { getSupportedDevices: getCachedSupportedDevices, findDeviceByCode } = useDeviceStore()

  // 键盘适配相关状态
  const [keyboardHeight, setKeyboardHeight] = useState(0)
  const [webViewShrunk, setWebViewShrunk] = useState(false)
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false)
  const initialHeightRef = useRef<number>(window.innerHeight)

  // 键盘避让 - 使用 Capacitor Keyboard API
  useEffect(() => {
    if (!Capacitor.isNativePlatform()) {
      // Web 平台回退到原有的监听方式
      let initialHeight = window.innerHeight
      let isInputFocused = false

      const handleFocus = () => {
        isInputFocused = true
        initialHeight = window.innerHeight
        setIsKeyboardVisible(true)
      }

      const handleBlur = () => {
        isInputFocused = false
        setKeyboardHeight(0) // 失焦时重置键盘高度
        setIsKeyboardVisible(false)
      }

      const handleResize = () => {
        if (!isInputFocused) return

        const currentHeight = window.innerHeight
        const heightDiff = initialHeight - currentHeight

        // 如果高度减少超过100px，说明键盘弹出了
        if (heightDiff > 100) {
          setKeyboardHeight(heightDiff)
          setIsKeyboardVisible(true)
        } else {
          setKeyboardHeight(0)
          setIsKeyboardVisible(false)
        }
      }

      // 监听输入框焦点
      const input = deviceCodeInputRef.current
      if (input) {
        input.addEventListener('focus', handleFocus)
        input.addEventListener('blur', handleBlur)
      }

      // 监听窗口大小变化
      window.addEventListener('resize', handleResize)

      return () => {
        if (input) {
          input.removeEventListener('focus', handleFocus)
          input.removeEventListener('blur', handleBlur)
        }
        window.removeEventListener('resize', handleResize)
      }
    }

    // 原生平台使用 Keyboard API
    let keyboardWillShowListener: any
    let keyboardWillHideListener: any

    const setupKeyboardListeners = async () => {
      keyboardWillShowListener = await Keyboard.addListener(
        'keyboardWillShow',
        (info: { keyboardHeight: number }) => {
          console.log('设备连接页面 - 键盘将显示，高度:', info.keyboardHeight)

          // 检测 WebView 是否自动缩放
          const afterKeyboardHeight = window.innerHeight
          const keyboardHeight = info.keyboardHeight
          const initialHeight = initialHeightRef.current

          const webViewShrunk = afterKeyboardHeight < initialHeight

          console.log('设备连接页面 - 键盘高度检查:', {
            initialHeight,
            afterKeyboardHeight,
            keyboardHeight,
            webViewShrunk
          })

          setWebViewShrunk(webViewShrunk)
          setIsKeyboardVisible(true)

          if (webViewShrunk) {
            // WebView 高度已经缩了，适当预留空间
            setKeyboardHeight(80) // 预留适当空间
          } else {
            // WebView 高度没缩，使用实际键盘高度
            setKeyboardHeight(keyboardHeight)
          }
        }
      )

      keyboardWillHideListener = await Keyboard.addListener('keyboardWillHide', () => {
        console.log('设备连接页面 - 键盘将隐藏')
        setIsKeyboardVisible(false)
        setKeyboardHeight(0)
        setWebViewShrunk(false)
        // 重新记录当前高度作为新的初始高度
        initialHeightRef.current = window.innerHeight
      })
    }

    setupKeyboardListeners()

    return () => {
      keyboardWillShowListener?.remove()
      keyboardWillHideListener?.remove()
    }
  }, [])

  // 使用外部传入的连接状态或内部状态
  const isConnecting =
    externalIsConnecting !== undefined ? externalIsConnecting : internalIsConnecting

  const setIsConnecting = (connecting: boolean) => {
    if (onConnectingChange) {
      onConnectingChange(connecting)
    } else {
      setInternalIsConnecting(connecting)
    }
  }

  // 使用外部传入的设备列表或内部状态
  const supportedDevices = externalSupportedDevices || internalSupportedDevices
  const isLoadingDevices =
    externalIsLoadingDevices !== undefined ? externalIsLoadingDevices : internalIsLoadingDevices

  // 只有在没有外部设备列表时才获取内部设备列表
  useEffect(() => {
    if (externalSupportedDevices) return // 如果有外部设备列表，不需要内部请求

    const loadSupportedDevices = async () => {
      try {
        setInternalIsLoadingDevices(true)
        // 使用缓存的设备列表获取方法
        const devices = await getCachedSupportedDevices()
        setInternalSupportedDevices(devices)
      } catch (error) {
        console.error('获取支持设备列表失败:', error)
      } finally {
        setInternalIsLoadingDevices(false)
      }
    }

    loadSupportedDevices()
  }, [externalSupportedDevices, getCachedSupportedDevices])

  const handleDeviceConnect = (device: Device) => {
    setIsConnecting(true)
    // 模拟连接延迟
    setTimeout(() => {
      setIsConnecting(false)
      onDeviceConnect(device)
    }, 1000)
  }

  // 连接设备
  const connectDevice = async () => {
    if (!deviceCode) {
      setError('请输入设备码')
      return
    }

    try {
      setError('')
      // 从缓存中查找设备，而不是调用 API
      const device = await findDeviceByCode(deviceCode.trim())
      
      if (device) {
        handleDeviceConnect(device)
      } else {
        setError('设备码无效，请检查后重试')
      }
    } catch (error) {
      console.error('连接设备失败:', error)
      setError('连接设备失败，请稍后重试')
    }
  }

  // 模拟扫描二维码
  const handleScan = () => {
    setIsScanning(true)

    // 模拟扫描过程
    setTimeout(async () => {
      try {
        // 如果有支持的设备，随机选择一个设备码进行模拟扫描
        if (supportedDevices.length > 0) {
          const randomDevice = supportedDevices[Math.floor(Math.random() * supportedDevices.length)]
          const deviceCode = randomDevice.deviceCode

          setDeviceCode(deviceCode)

          // 从缓存中获取设备信息
          const device = await findDeviceByCode(deviceCode)
          setIsScanning(false)
          
          if (device) {
            handleDeviceConnect(device)
          } else {
            setError('扫描到的设备码无效')
          }
        } else {
          setIsScanning(false)
          setError('暂无可用设备')
        }
      } catch (error) {
        console.error('扫描设备失败:', error)
        setIsScanning(false)
        setError('扫描失败，请重试')
      }
    }, 2000)
  }

  const handleSkip = () => {
    if (onSkip) {
      onSkip()
    }
  }

  if (isConnecting) {
    return (
      <div className={`flex flex-col items-center justify-center py-8 ${className}`}>
        <Spinner size="lg" color="primary" />
        <p className="mt-4 text-gray-400">正在连接设备...</p>
      </div>
    )
  }

  return (
    <div
      className={`space-y-6 ${className}`}
      style={{
        paddingBottom: isKeyboardVisible
          ? `${keyboardHeight + 20}px` // 键盘弹出时额外增加20px缓冲
          : '0px',
        transition: 'padding-bottom 0.3s ease'
      }}
    >
      {/* 设备码输入 */}
      <div className="space-y-3">
        <Input
          ref={deviceCodeInputRef}
          label="设备码"
          placeholder="请输入6位设备码"
          value={deviceCode}
          onChange={e => setDeviceCode(e.target.value)}
          variant="bordered"
          classNames={{
            input: 'text-white',
            inputWrapper: `bg-gray-800 border-gray-600 hover:border-gray-500 transition-all duration-200 ${
              isKeyboardVisible ? 'border-primary-500 shadow-lg' : ''
            }`,
            label: 'text-gray-300'
          }}
          isInvalid={!!error}
          errorMessage={error}
        />

        <div className="space-y-2">
          <Button
            color="primary"
            size="lg"
            className="w-full"
            onPress={connectDevice}
            isDisabled={!deviceCode}
          >
            {connectButtonText}
          </Button>

          {showSkipButton && onSkip && (
            <Button variant="light" size="sm" className="w-full text-gray-400" onPress={handleSkip}>
              {skipButtonText}
            </Button>
          )}
        </div>

        {supportedDevices.length > 0 && (
          <p className="text-gray-500 text-xs text-center">
            示例设备码:{' '}
            {supportedDevices
              .slice(0, 3)
              .map(d => d.deviceCode)
              .join(', ')}
            {supportedDevices.length > 3 && '...'}
          </p>
        )}
        {isLoadingDevices && (
          <p className="text-gray-500 text-xs text-center">正在加载设备列表...</p>
        )}
      </div>

      {/* 分割线 */}
      <div className="flex items-center justify-center">
        <div className="flex-1 h-px bg-gray-700" />
        <span className="px-4 text-gray-500 text-sm">或者</span>
        <div className="flex-1 h-px bg-gray-700" />
      </div>

      {/* 扫描二维码 */}
      <div className="text-center">
        <Button
          variant="bordered"
          size="lg"
          className="w-full border-gray-600 text-white hover:bg-gray-800"
          onPress={handleScan}
          isLoading={isScanning}
          spinner={<Spinner size="sm" color="white" />}
        >
          {isScanning ? '扫描中...' : '扫描设备二维码'}
        </Button>
        <p className="text-gray-500 text-xs mt-2">将设备二维码置于摄像头前进行扫描</p>
      </div>
    </div>
  )
}

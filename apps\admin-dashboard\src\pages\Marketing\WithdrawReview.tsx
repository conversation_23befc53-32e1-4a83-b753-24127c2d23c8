import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  Tag,
  Select,
  DatePicker,
  message,
  Modal,
  Form,
  Typography,
  Avatar,
  Tooltip,
  Row,
  Col,
  Statistic,
  Badge,
  Descriptions
} from 'antd'
import {
  SearchOutlined,
  ExportOutlined,
  UserOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { WithdrawRequest } from '@/types/api'
import type { WithdrawListParams, WithdrawReviewParams } from '@/services/marketing'
import { marketingService } from '@/services/marketing'
import { WITHDRAW_STATUS_OPTIONS, TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

const { RangePicker } = DatePicker
const { Title } = Typography
const { TextArea } = Input

const WithdrawReview: React.FC = () => {
  const [withdrawals, setWithdrawals] = useState<WithdrawRequest[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [reviewModalVisible, setReviewModalVisible] = useState(false)
  const [completeModalVisible, setCompleteModalVisible] = useState(false)
  const [currentWithdraw, setCurrentWithdraw] = useState<WithdrawRequest | null>(null)
  const [reviewForm] = Form.useForm()
  const [completeForm] = Form.useForm()

  // 搜索条件
  const [searchParams, setSearchParams] = useState<WithdrawListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState({
    pendingCount: 0,
    pendingAmount: 0,
    todayCount: 0,
    todayAmount: 0,
    monthlyAmount: 0
  })

  useEffect(() => {
    loadWithdrawals()
    loadStats()
  }, [currentPage, pageSize, searchParams])

  const loadWithdrawals = async () => {
    try {
      setLoading(true)

      const params = {
        page: currentPage,
        pageSize,
        ...searchParams
      }

      const response = await marketingService.getWithdrawRequests(params)

      if (response.success && response.data) {
        setWithdrawals(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取提现申请列表失败')
      }
    } catch (error) {
      console.error('获取提现申请列表失败:', error)
      message.error('获取提现申请列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await marketingService.getWithdrawStats()

      if (response.success && response.data) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
  }

  const handleReview = (withdraw: WithdrawRequest) => {
    setCurrentWithdraw(withdraw)
    reviewForm.resetFields()
    setReviewModalVisible(true)
  }

  const handleComplete = (withdraw: WithdrawRequest) => {
    setCurrentWithdraw(withdraw)
    completeForm.resetFields()
    setCompleteModalVisible(true)
  }

  const handleReviewSubmit = async (values: WithdrawReviewParams) => {
    if (!currentWithdraw) return

    try {
      const response = await marketingService.reviewWithdrawRequest(currentWithdraw.id, values)

      if (response.success) {
        message.success(`提现申请已${values.status === 'approved' ? '批准' : '拒绝'}`)
        setReviewModalVisible(false)
        loadWithdrawals()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('审核提现申请失败:', error)
      message.error('操作失败')
    }
  }

  const handleCompleteSubmit = async (values: { transactionNo: string }) => {
    if (!currentWithdraw) return

    try {
      const response = await marketingService.completeWithdraw(currentWithdraw.id, values)

      if (response.success) {
        message.success('提现已完成')
        setCompleteModalVisible(false)
        loadWithdrawals()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('完成提现失败:', error)
      message.error('操作失败')
    }
  }

  const handleViewDetail = (withdraw: WithdrawRequest) => {
    Modal.info({
      title: '提现申请详情',
      width: 700,
      content: (
        <div style={{ marginTop: 16 }}>
          <Descriptions column={2} bordered size="small">
            <Descriptions.Item label="申请ID">{withdraw.id}</Descriptions.Item>
            <Descriptions.Item label="用户ID">{withdraw.userId}</Descriptions.Item>
            <Descriptions.Item label="提现金额">¥{withdraw.amount.toFixed(2)}</Descriptions.Item>
            <Descriptions.Item label="申请状态">
              <Tag color={getStatusColor(withdraw.status)}>{getStatusText(withdraw.status)}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="银行名称">{withdraw.bankInfo?.bankName}</Descriptions.Item>
            <Descriptions.Item label="账户名">{withdraw.bankInfo?.accountName}</Descriptions.Item>
            <Descriptions.Item label="银行卡号" span={2}>
              {withdraw.bankInfo?.accountNumber}
            </Descriptions.Item>
            <Descriptions.Item label="申请时间" span={2}>
              {dayjs(withdraw.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
            {withdraw.reviewedAt && (
              <Descriptions.Item label="审核时间" span={2}>
                {dayjs(withdraw.reviewedAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
            {withdraw.completedAt && (
              <Descriptions.Item label="完成时间" span={2}>
                {dayjs(withdraw.completedAt).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            )}
          </Descriptions>
        </div>
      )
    })
  }

  const getStatusColor = (status: string) => {
    const option = WITHDRAW_STATUS_OPTIONS.find(s => s.value === status)
    return option?.color || 'default'
  }

  const getStatusText = (status: string) => {
    const option = WITHDRAW_STATUS_OPTIONS.find(s => s.value === status)
    return option?.label || status
  }

  const columns: ColumnsType<WithdrawRequest> = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <div style={{ fontWeight: 500 }}>
              用户{record.userId ? record.userId.slice(-3) : '未知'}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>ID: {record.userId}</div>
          </div>
        </Space>
      )
    },
    {
      title: '提现金额',
      dataIndex: 'amount',
      render: amount => (
        <span style={{ color: '#f50', fontWeight: 500, fontSize: '16px' }}>
          ¥{amount.toFixed(2)}
        </span>
      ),
      sorter: (a, b) => a.amount - b.amount
    },
    {
      title: '银行信息',
      key: 'bankInfo',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{record.bankInfo?.bankName}</div>
          <div style={{ color: '#999', fontSize: '12px' }}>{record.bankInfo?.accountName}</div>
          <div style={{ color: '#999', fontSize: '12px' }}>{record.bankInfo?.accountNumber}</div>
        </div>
      )
    },
    {
      title: '申请状态',
      dataIndex: 'status',
      render: status => {
        const option = WITHDRAW_STATUS_OPTIONS.find(s => s.value === status)
        return (
          <Badge
            status={
              option?.color === 'orange'
                ? 'processing'
                : option?.color === 'green'
                ? 'success'
                : option?.color === 'red'
                ? 'error'
                : 'default'
            }
            text={<Tag color={option?.color}>{option?.label}</Tag>}
          />
        )
      },
      filters: WITHDRAW_STATUS_OPTIONS.map(option => ({
        text: option.label,
        value: option.value
      })),
      onFilter: (value, record) => record.status === value
    },
    {
      title: '申请时间',
      dataIndex: 'createdAt',
      render: date => (
        <div>
          <div>{dayjs(date).format('MM-DD HH:mm')}</div>
          <div style={{ color: '#999', fontSize: '12px' }}>{dayjs(date).fromNow()}</div>
        </div>
      ),
      sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix()
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)} />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="审核">
              <Button
                type="link"
                icon={<ExclamationCircleOutlined />}
                onClick={() => handleReview(record)}
              />
            </Tooltip>
          )}
          {record.status === 'approved' && (
            <Tooltip title="完成提现">
              <Button
                type="link"
                icon={<CheckOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleComplete(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        提现审核
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={5}>
          <Card>
            <Statistic
              title="待审核"
              value={stats.pendingCount}
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="待审核金额"
              value={stats.pendingAmount}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="今日申请"
              value={stats.todayCount}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="今日金额"
              value={stats.todayAmount}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="本月提现"
              value={stats.monthlyAmount}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
      </Row>

      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索用户ID"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="申请状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.status}
            onChange={value => setSearchParams({ ...searchParams, status: value })}
          >
            {WITHDRAW_STATUS_OPTIONS.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>

          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={dates => {
              if (dates) {
                setSearchParams({
                  ...searchParams,
                  startDate: dates[0]?.toISOString(),
                  endDate: dates[1]?.toISOString()
                })
              } else {
                setSearchParams({
                  ...searchParams,
                  startDate: undefined,
                  endDate: undefined
                })
              }
            }}
          />

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button icon={<ExportOutlined />}>导出</Button>
        </Space>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={withdrawals}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 审核模态框 */}
      <Modal
        title="审核提现申请"
        open={reviewModalVisible}
        onCancel={() => setReviewModalVisible(false)}
        footer={null}
        width={500}
      >
        {currentWithdraw && (
          <div>
            <Descriptions column={1} bordered size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="用户">{currentWithdraw.userId}</Descriptions.Item>
              <Descriptions.Item label="提现金额">
                ¥{currentWithdraw.amount.toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="银行信息">
                {currentWithdraw.bankInfo?.bankName} - {currentWithdraw.bankInfo?.accountName}
              </Descriptions.Item>
            </Descriptions>

            <Form form={reviewForm} layout="vertical" onFinish={handleReviewSubmit}>
              <Form.Item
                name="status"
                label="审核结果"
                rules={[{ required: true, message: '请选择审核结果' }]}
              >
                <Select placeholder="选择审核结果">
                  <Select.Option value="approved">
                    <Space>
                      <CheckOutlined style={{ color: '#52c41a' }} />
                      批准提现
                    </Space>
                  </Select.Option>
                  <Select.Option value="rejected">
                    <Space>
                      <CloseOutlined style={{ color: '#f50' }} />
                      拒绝提现
                    </Space>
                  </Select.Option>
                </Select>
              </Form.Item>

              <Form.Item name="remark" label="审核备注">
                <TextArea rows={3} placeholder="请输入审核备注信息..." />
              </Form.Item>

              <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                <Space>
                  <Button onClick={() => setReviewModalVisible(false)}>取消</Button>
                  <Button type="primary" htmlType="submit">
                    确定审核
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 完成提现模态框 */}
      <Modal
        title="完成提现"
        open={completeModalVisible}
        onCancel={() => setCompleteModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form form={completeForm} layout="vertical" onFinish={handleCompleteSubmit}>
          <Form.Item
            name="transactionNo"
            label="转账交易号"
            rules={[{ required: true, message: '请输入转账交易号' }]}
          >
            <Input placeholder="银行转账交易流水号" />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setCompleteModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确认完成
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default WithdrawReview

import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import { getUserBySupabaseId } from '@/lib/db/queries/user'

const adminDeviceCommandSets = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 指令集列表管理 ====================

// 查询参数验证
const commandSetListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  isActive: z
    .string()
    .transform(val => val === 'true')
    .optional()
})

// 获取指令集列表
adminDeviceCommandSets.get('/', authMiddleware, zValidator('query', commandSetListSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { page, pageSize, keyword, isActive } = c.req.valid('query')
    const env = c.env

    console.log('📋 [ADMIN-COMMAND-SETS] 获取指令集列表:', {
      page,
      pageSize,
      keyword,
      isActive
    })

    // 从数据库查询指令集列表
    const supabase = getSupabase(env)

    let query = supabase.from('DeviceCommandSet').select('*')

    // 构建筛选条件
    if (keyword) {
      query = query.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`)
    }

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive)
    }

    // 添加排序
    query = query.order('created_at', { ascending: false })

    // 计算总数
    let countQuery = supabase.from('DeviceCommandSet').select('*', { count: 'exact', head: true })

    if (keyword) {
      countQuery = countQuery.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`)
    }
    if (isActive !== undefined) {
      countQuery = countQuery.eq('is_active', isActive)
    }

    const countResult = await countQuery
    const total = countResult.count || 0

    // 分页查询
    const offset = (page - 1) * pageSize
    const result = await query.range(offset, offset + pageSize - 1)
    const { data: commandSets, error } = handleSupabaseResult(result)

    if (error) throw error

    console.log(`📋 [ADMIN-COMMAND-SETS] 查询到 ${commandSets.length} 个指令集，共 ${total} 个`)

    // 转换数据格式 (snake_case -> camelCase)
    const formattedCommandSets = (commandSets || []).map((commandSet: any) => ({
      id: commandSet.id,
      name: commandSet.name,
      description: commandSet.description,
      command: commandSet.command,
      broadcast: commandSet.broadcast,
      isActive: commandSet.is_active,
      createdAt: commandSet.created_at,
      updatedAt: commandSet.updated_at
    }))

    return c.json({
      success: true,
      data: {
        data: formattedCommandSets,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('❌ [ADMIN-COMMAND-SETS] 获取指令集列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取指令集列表失败'
      },
      500
    )
  }
})

// ==================== 指令集详情管理 ====================

// 获取指令集详情
adminDeviceCommandSets.get('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-COMMAND-SETS] 获取指令集详情:', id)

    // 查询指令集详情
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceCommandSet')
      .select('*')
      .eq('id', id)
      .single()

    const { data: commandSet, error } = handleSupabaseResult(result)

    if (error || !commandSet) {
      return c.json(
        {
          success: false,
          message: '指令集不存在'
        },
        404
      )
    }

    // 格式化数据 (snake_case -> camelCase)
    const formattedCommandSet = {
      id: commandSet.id,
      name: commandSet.name,
      description: commandSet.description,
      command: commandSet.command,
      broadcast: commandSet.broadcast,
      isActive: commandSet.is_active,
      createdAt: commandSet.created_at,
      updatedAt: commandSet.updated_at
    }

    return c.json({
      success: true,
      data: formattedCommandSet
    })
  } catch (error) {
    console.error('❌ [ADMIN-COMMAND-SETS] 获取指令集详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取指令集详情失败'
      },
      500
    )
  }
})

// ==================== 指令集创建和编辑 ====================

// 创建指令集验证模式
const createCommandSetSchema = z.object({
  name: z.string().min(1, '指令集名称不能为空'),
  description: z.string().optional(),
  command: z.string().min(1, '控制指令不能为空'),
  broadcast: z.string().optional(),
  isActive: z.boolean().default(true)
})

// 创建指令集
adminDeviceCommandSets.post('/', authMiddleware, zValidator('json', createCommandSetSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const commandSetData = c.req.valid('json')
    const env = c.env

    console.log('➕ [ADMIN-COMMAND-SETS] 创建指令集:', commandSetData.name)

    // 创建指令集
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceCommandSet')
      .insert([{
        name: commandSetData.name,
        description: commandSetData.description,
        command: commandSetData.command,
        broadcast: commandSetData.broadcast,
        is_active: commandSetData.isActive
      }])
      .select()

    const { data: newCommandSets, error } = handleSupabaseResult(result)

    if (error || !newCommandSets || newCommandSets.length === 0) {
      return c.json({ success: false, message: '创建指令集失败' }, 500)
    }

    const newCommandSet = newCommandSets[0]

    console.log('✅ [ADMIN-COMMAND-SETS] 指令集创建成功:', newCommandSet.id)

    return c.json({
      success: true,
      data: {
        id: newCommandSet.id,
        name: newCommandSet.name,
        description: newCommandSet.description,
        command: newCommandSet.command,
        broadcast: newCommandSet.broadcast,
        isActive: newCommandSet.is_active,
        createdAt: newCommandSet.created_at,
        updatedAt: newCommandSet.updated_at
      },
      message: '指令集创建成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-COMMAND-SETS] 创建指令集失败:', error)
    return c.json(
      {
        success: false,
        message: '创建指令集失败'
      },
      500
    )
  }
})

// 更新指令集
adminDeviceCommandSets.put(
  '/:id',
  authMiddleware,
  zValidator('json', createCommandSetSchema.partial()),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const updateData = c.req.valid('json')
      const env = c.env

      console.log('📝 [ADMIN-COMMAND-SETS] 更新指令集:', id)

      // 更新指令集
      const supabase = getSupabase(env)
      const updatePayload: any = {}

      if (updateData.name !== undefined) updatePayload.name = updateData.name
      if (updateData.description !== undefined) updatePayload.description = updateData.description
      if (updateData.command !== undefined) updatePayload.command = updateData.command
      if (updateData.broadcast !== undefined) updatePayload.broadcast = updateData.broadcast
      if (updateData.isActive !== undefined) updatePayload.is_active = updateData.isActive

      updatePayload.updated_at = new Date().toISOString()

      const result = await supabase
        .from('DeviceCommandSet')
        .update(updatePayload)
        .eq('id', id)
        .select()

      const { data: updatedCommandSets, error } = handleSupabaseResult(result)

      if (error || !updatedCommandSets || updatedCommandSets.length === 0) {
        return c.json({ success: false, message: '指令集不存在' }, 404)
      }

      const updatedCommandSet = updatedCommandSets[0]

      console.log('✅ [ADMIN-COMMAND-SETS] 指令集更新成功:', id)

      return c.json({
        success: true,
        data: {
          id: updatedCommandSet.id,
          name: updatedCommandSet.name,
          description: updatedCommandSet.description,
          command: updatedCommandSet.command,
          broadcast: updatedCommandSet.broadcast,
          isActive: updatedCommandSet.is_active,
          createdAt: updatedCommandSet.created_at,
          updatedAt: updatedCommandSet.updated_at
        },
        message: '指令集更新成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-COMMAND-SETS] 更新指令集失败:', error)
      return c.json(
        {
          success: false,
          message: '更新指令集失败'
        },
        500
      )
    }
  }
)

// 删除指令集
adminDeviceCommandSets.delete('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🗑️ [ADMIN-COMMAND-SETS] 删除指令集:', id)

    // 删除指令集
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceCommandSet')
      .delete()
      .eq('id', id)
      .select()

    const { data: deletedCommandSets, error } = handleSupabaseResult(result)

    if (error || !deletedCommandSets || deletedCommandSets.length === 0) {
      return c.json({ success: false, message: '指令集不存在' }, 404)
    }

    console.log('✅ [ADMIN-COMMAND-SETS] 指令集删除成功:', id)

    return c.json({
      success: true,
      message: '指令集删除成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-COMMAND-SETS] 删除指令集失败:', error)
    return c.json(
      {
        success: false,
        message: '删除指令集失败'
      },
      500
    )
  }
})

// ==================== 指令集状态管理 ====================

// 切换指令集启用状态
adminDeviceCommandSets.post(
  '/:id/toggle-status',
  authMiddleware,
  zValidator(
    'json',
    z.object({
      isActive: z.boolean()
    })
  ),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const { isActive } = c.req.valid('json')
      const env = c.env

      console.log('🔄 [ADMIN-COMMAND-SETS] 切换指令集状态:', id, isActive)

      // 更新状态
      const supabase = getSupabase(env)
      const result = await supabase
        .from('DeviceCommandSet')
        .update({
          is_active: isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()

      const { data: updatedCommandSets, error } = handleSupabaseResult(result)

      if (error || !updatedCommandSets || updatedCommandSets.length === 0) {
        return c.json({ success: false, message: '指令集不存在' }, 404)
      }

      console.log('✅ [ADMIN-COMMAND-SETS] 指令集状态更新成功:', id)

      return c.json({
        success: true,
        message: isActive ? '指令集已启用' : '指令集已禁用'
      })
    } catch (error) {
      console.error('❌ [ADMIN-COMMAND-SETS] 切换指令集状态失败:', error)
      return c.json(
        {
          success: false,
          message: '状态更新失败'
        },
        500
      )
    }
  }
)

export default adminDeviceCommandSets
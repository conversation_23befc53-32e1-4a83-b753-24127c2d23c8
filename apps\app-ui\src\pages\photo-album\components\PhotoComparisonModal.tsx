import { motion, AnimatePresence } from 'framer-motion'
import { But<PERSON> } from '@heroui/react'
import { Icon } from '@iconify/react'
import { CustomAvatar } from '@/components/ui/custom-avatar'
import { TFunction } from 'i18next'
import type { PhotoTemplate } from '@/stores/photo-generation-store'

interface PhotoComparisonModalProps {
  isVisible: boolean
  template: PhotoTemplate | null
  currentRole: any
  onConfirm: () => void
  onCancel: () => void
  t: TFunction<'photo-album', undefined>
}

export function PhotoComparisonModal({
  isVisible,
  template,
  currentRole,
  onConfirm,
  onCancel,
  t
}: PhotoComparisonModalProps) {
  if (!template || !currentRole) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex flex-col"
          style={{
            background: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)'
          }}
        >
          {/* 图片生成预览区域 - 重叠展示 */}
          <div className="flex-1 flex items-center justify-center px-6 py-8">
            <div className="relative w-full max-w-sm">
              {/* 主要模板图片 - 占据主要视觉空间 */}
              <motion.div layoutId={`template-container-${template.id}`} className="relative">
                {/* 发光效果 */}
                <div className="absolute -inset-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 rounded-3xl blur-xl" />

                <motion.img
                  layoutId={`template-image-${template.id}`}
                  src={template.previewImage}
                  alt={template.name}
                  className="w-full aspect-[3/4] object-cover rounded-3xl shadow-2xl relative z-10 border border-white/10"
                />

                {/* 角色头像 - 左上角重叠显示 */}
                <motion.div
                  layoutId={`character-${currentRole.id}`}
                  className="absolute -top-2 -left-2 z-20"
                >
                  <div className="relative">
                    <CustomAvatar
                      src={currentRole.imageUrl}
                      alt={currentRole.name}
                      size="xl"
                      className="border-4 border-white shadow-lg"
                    />
                    {/* 连接箭头 */}
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-white rounded-full flex items-center justify-center shadow-md">
                      <Icon icon="solar:arrow-right-bold" className="w-3 h-3 text-purple-600" />
                    </div>
                  </div>
                </motion.div>

                {/* 角色名称标签 */}
                <div className="absolute top-20 left-2 z-20">
                  <div className="bg-black/60 backdrop-blur-sm px-2 py-1 rounded-lg">
                    <div className="text-white/90 text-xs font-medium">{currentRole.name}</div>
                  </div>
                </div>
              </motion.div>

              {/* 模板信息 */}
              <div className="mt-4 text-center">
                <div className="text-white text-lg font-semibold mb-2">{template.name}</div>

                {/* 积分消耗提示 */}
                <div className="flex items-center justify-center px-4 py-2 bg-white/10 rounded-full backdrop-blur-sm">
                  <Icon icon="solar:star-bold" className="w-4 h-4 text-yellow-400 mr-2" />
                  <span className="text-white/90 text-sm font-medium">
                    {t('comparison.title', { points: template.pointsCost || 1 })}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* 底部按钮区域 */}
          <div className="p-8 flex flex-col items-center space-y-3">
            {/* 主要生成按钮 */}
            <Button
              className="w-64 h-14 bg-gradient-to-r from-purple-600 to-pink-600 text-white text-lg font-bold rounded-2xl shadow-lg"
              onPress={onConfirm}
            >
              <Icon icon="solar:magic-stick-3-bold" className="w-5 h-5 mr-2" />
              {t('comparison.generate')}
            </Button>

            {/* 取消按钮 */}
            <Button variant="light" className="text-white/60 text-sm h-10" onPress={onCancel}>
              {t('comparison.cancel')}
            </Button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

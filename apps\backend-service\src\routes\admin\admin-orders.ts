import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import { createSupabaseServiceClient } from '@/lib/supabase'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types'

const adminOrders = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata = supabaseUser.user_metadata || supabaseUser.raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// 查询参数验证
const orderListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  status: z.string().optional(),
  type: z.string().optional(),
  paymentMethod: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional()
})

// ==================== 获取订单列表 ====================

adminOrders.get('/', authMiddleware, zValidator('query', orderListSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { page, pageSize, keyword, status, type, paymentMethod, startDate, endDate } =
      c.req.valid('query')
    const env = c.env

    console.log('📋 [ADMIN-ORDERS] 获取订单列表:', {
      page,
      pageSize,
      keyword,
      status,
      type,
      paymentMethod,
      startDate,
      endDate
    })

    // 从数据库查询订单列表
    const supabase = getSupabase(env)

    let query = supabase
      .from(TABLE_NAMES.paymentOrder)
      .select(
        `
        id,
        order_no,
        user_id,
        plan_id,
        points_package_id,
        amount,
        currency,
        payment_method,
        status,
        description,
        external_order_id,
        paid_at,
        created_at,
        ${TABLE_NAMES.user}!inner(email),
        ${TABLE_NAMES.membershipPlan}(name),
        ${TABLE_NAMES.pointsPackage}(name)
      `
      )
      .order('created_at', { ascending: false })

    // 应用筛选条件
    if (keyword) {
      query = query.or(
        `order_no.ilike.%${keyword}%,external_order_id.ilike.%${keyword}%,${TABLE_NAMES.user}.email.ilike.%${keyword}%`
      )
    }

    if (status) {
      query = query.eq('status', status)
    }

    if (type) {
      if (type === 'membership') {
        query = query.not('plan_id', 'is', null)
      } else if (type === 'points') {
        query = query.not('points_package_id', 'is', null)
      }
    }

    if (paymentMethod) {
      query = query.eq('payment_method', paymentMethod)
    }

    if (startDate) {
      query = query.gte('created_at', new Date(startDate).toISOString())
    }

    if (endDate) {
      query = query.lte('created_at', new Date(endDate).toISOString())
    }

    // 计算总数
    const countQuery = supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*', { count: 'exact', head: true })
    const countResult = await countQuery
    const total = countResult.count || 0

    // 分页查询
    const offset = (page - 1) * pageSize
    const result = await query.range(offset, offset + pageSize - 1)
    const { data: orders, error } = handleSupabaseResult(result)
    if (error) throw error

    console.log(`📋 [ADMIN-ORDERS] 查询到 ${orders?.length || 0} 条订单，共 ${total} 条`)

    // 转换数据格式
    const formattedOrders = (orders || []).map((order: any) => ({
      id: order.order_no, // 使用 orderNo 作为主键
      userId: order.user_id,
      userEmail: order.user?.email || '',
      type: order.plan_id ? 'membership' : 'points',
      itemName: order.membershipPlan?.name || order.pointsPackage?.name || '未知商品',
      amount: Number.parseFloat(order.amount),
      currency: order.currency,
      status: mapOrderStatus(order.status),
      paymentMethod: order.payment_method,
      description: order.description,
      tradeNo: order.external_order_id,
      paidAt: order.paid_at,
      createdAt: order.created_at,
      updatedAt: order.created_at // paymentOrder表没有updatedAt字段，使用createdAt
    }))

    return c.json({
      success: true,
      data: {
        data: formattedOrders,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('❌ [ADMIN-ORDERS] 获取订单列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取订单列表失败'
      },
      500
    )
  }
})

// ==================== 获取订单详情 ====================

adminOrders.get('/:orderId', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const orderId = c.req.param('orderId')
    const env = c.env

    console.log('🔍 [ADMIN-ORDERS] 获取订单详情:', orderId)

    // 查询订单详情
    const { getPaymentOrderWithDetails } = await import('@/lib/db/queries/payment')
    const orderDetail = await getPaymentOrderWithDetails(env, orderId)

    if (!orderDetail) {
      return c.json(
        {
          success: false,
          message: '订单不存在'
        },
        404
      )
    }

    const { order, plan, pointsPackage } = orderDetail

    // 查询用户信息
    const supabase = createSupabaseServiceClient(env)
    const { data: authUser } = await supabase.auth.admin.getUserById(order.userId)

    const result = {
      id: order.orderNo,
      userId: order.userId,
      userEmail: authUser?.user?.email || '未知用户',
      type: order.planId ? 'membership' : 'points',
      amount: Number.parseFloat(order.amount),
      currency: order.currency,
      status: mapOrderStatus(order.status),
      paymentMethod: order.paymentMethod,
      description: order.description,
      tradeNo: order.externalOrderId,
      paidAt: order.paidAt,
      expiresAt: order.expiresAt,
      createdAt: order.createdAt,
      updatedAt: order.createdAt, // paymentOrder表没有updatedAt字段，使用createdAt
      metadata: order.metadata,
      // 商品信息
      itemInfo: plan
        ? {
            type: 'membership',
            id: plan.id,
            name: plan.name,
            description: plan.description,
            price: Number.parseFloat(plan.price),
            durationDays: plan.durationDays,
            pointsIncluded: plan.pointsIncluded
          }
        : pointsPackage
        ? {
            type: 'points',
            id: pointsPackage.id,
            name: pointsPackage.name,
            description: pointsPackage.description,
            price: Number.parseFloat(pointsPackage.price),
            points: pointsPackage.points,
            bonusPoints: pointsPackage.bonusPoints || 0
          }
        : null
    }

    return c.json({
      success: true,
      data: result
    })
  } catch (error) {
    console.error('❌ [ADMIN-ORDERS] 获取订单详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取订单详情失败'
      },
      500
    )
  }
})

// ==================== 获取订单统计 ====================

adminOrders.get('/stats/summary', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const env = c.env

    console.log('📊 [ADMIN-ORDERS] 获取订单统计')

    const supabase = getSupabase(env)

    // 今日时间范围
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    // 并行查询各种统计数据
    const [
      totalOrdersResult,
      completedOrdersResult,
      todayOrdersResult,
      todayRevenueResult,
      totalRevenueResult
    ] = await Promise.all([
      // 总订单数
      supabase.from(TABLE_NAMES.paymentOrder).select('*', { count: 'exact', head: true }),

      // 已完成订单数
      supabase
        .from(TABLE_NAMES.paymentOrder)
        .select('*', { count: 'exact', head: true })
        .eq('status', 'paid'),

      // 今日订单数
      supabase
        .from(TABLE_NAMES.paymentOrder)
        .select('*', { count: 'exact', head: true })
        .gte('created_at', today.toISOString()),

      // 今日收入
      supabase
        .from(TABLE_NAMES.paymentOrder)
        .select('amount')
        .eq('status', 'paid')
        .gte('created_at', today.toISOString()),

      // 总收入
      supabase.from(TABLE_NAMES.paymentOrder).select('amount').eq('status', 'paid')
    ])

    const todayRevenue =
      todayRevenueResult.data?.reduce(
        (sum, item) => sum + Number.parseFloat(item.amount || '0'),
        0
      ) || 0
    const totalRevenue =
      totalRevenueResult.data?.reduce(
        (sum, item) => sum + Number.parseFloat(item.amount || '0'),
        0
      ) || 0

    const stats = {
      totalOrders: totalOrdersResult.count || 0,
      completedOrders: completedOrdersResult.count || 0,
      todayOrders: todayOrdersResult.count || 0,
      todayRevenue,
      totalRevenue
    }

    console.log('📊 [ADMIN-ORDERS] 统计结果:', stats)

    return c.json({
      success: true,
      data: stats
    })
  } catch (error) {
    console.error('❌ [ADMIN-ORDERS] 获取订单统计失败:', error)
    return c.json(
      {
        success: false,
        message: '获取订单统计失败'
      },
      500
    )
  }
})

// 辅助函数：映射订单状态
function mapOrderStatus(status: string): string {
  const statusMap: Record<string, string> = {
    pending: 'pending',
    paid: 'completed',
    failed: 'failed',
    cancelled: 'cancelled',
    expired: 'cancelled'
  }
  return statusMap[status] || status
}

export default adminOrders

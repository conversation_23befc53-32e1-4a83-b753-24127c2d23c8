import React from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider, App as AntdApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { authService } from '@/services/auth'

// 页面组件（懒加载）
const LoginPage = React.lazy(() => import('@/pages/Auth/LoginPage'))
const MainLayout = React.lazy(() => import('@/components/Layout/MainLayout'))
const Dashboard = React.lazy(() => import('@/pages/Dashboard/Dashboard'))
const UserList = React.lazy(() => import('@/pages/Users/<USER>'))
const OrderList = React.lazy(() => import('@/pages/Orders/OrderList'))
const MembershipPlans = React.lazy(() => import('@/pages/Membership/MembershipPlans'))
const MembershipSubscriptions = React.lazy(
  () => import('@/pages/Membership/MembershipSubscriptions')
)
const PointsPackages = React.lazy(() => import('@/pages/Points/PointsPackages'))
const UserPoints = React.lazy(() => import('@/pages/Points/UserPoints'))
const PointsConfig = React.lazy(() => import('@/pages/Points/PointsConfig'))
const InviteCodes = React.lazy(() => import('@/pages/Marketing/InviteCodes'))
const CommissionManagement = React.lazy(() => import('@/pages/Marketing/CommissionManagement'))
const WithdrawReview = React.lazy(() => import('@/pages/Marketing/WithdrawReview'))
const ActivationCodes = React.lazy(() => import('@/pages/Marketing/ActivationCodes'))
const DeviceManagement = React.lazy(() => import('@/pages/Devices/DeviceManagement'))
const CommandSetsManagement = React.lazy(() => import('@/pages/Devices/CommandSetsManagement'))
const FunctionsManagement = React.lazy(() => import('@/pages/Devices/FunctionsManagement'))
const ModesManagement = React.lazy(() => import('@/pages/Devices/SimpleModesManagement'))
const ScriptManagement = React.lazy(() => import('@/pages/Content/ScriptManagement'))
const ScriptEditor = React.lazy(() => import('@/pages/Content/ScriptEditor'))
const TemplateManagement = React.lazy(() => import('@/pages/Content/TemplateManagement'))
const CharacterManagement = React.lazy(() => import('@/pages/Characters/CharacterManagement'))
const AppVersions = React.lazy(() => import('@/pages/AppManagement/AppVersions'))

// 路由守卫组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = authService.isAuthenticated()

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// 公开路由组件
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = authService.isAuthenticated()

  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />
  }

  return <>{children}</>
}

const App: React.FC = () => {
  return (
    <ConfigProvider locale={zhCN}>
      <AntdApp>
        <Router>
          <React.Suspense fallback={<div>加载中...</div>}>
            <Routes>
              {/* 公开路由 */}
              <Route
                path="/login"
                element={
                  <PublicRoute>
                    <LoginPage />
                  </PublicRoute>
                }
              />

              {/* 受保护的路由 */}
              <Route
                path="/*"
                element={
                  <ProtectedRoute>
                    <MainLayout>
                      <Routes>
                        <Route path="/dashboard" element={<Dashboard />} />
                        <Route path="/users" element={<UserList />} />
                        <Route path="/orders" element={<OrderList />} />
                        <Route path="/membership/plans" element={<MembershipPlans />} />
                        <Route
                          path="/membership/subscriptions"
                          element={<MembershipSubscriptions />}
                        />
                        <Route path="/points/packages" element={<PointsPackages />} />
                        <Route path="/points/users" element={<UserPoints />} />
                        <Route path="/points/config" element={<PointsConfig />} />

                        {/* 营销管理 */}
                        <Route path="/marketing/invites" element={<InviteCodes />} />
                        <Route path="/marketing/commission" element={<CommissionManagement />} />
                        <Route path="/marketing/withdraw" element={<WithdrawReview />} />
                        <Route path="/marketing/activation" element={<ActivationCodes />} />

                        {/* 设备管理 */}
                        <Route path="/devices/command-sets" element={<CommandSetsManagement />} />
                        <Route path="/devices/functions" element={<FunctionsManagement />} />
                        <Route path="/devices/modes" element={<ModesManagement />} />
                        <Route path="/devices/management" element={<DeviceManagement />} />

                        {/* 内容管理 */}
                        <Route path="/content/scripts" element={<ScriptManagement />} />
                        <Route path="/content/scripts/editor/:id?" element={<ScriptEditor />} />
                        <Route path="/content/templates" element={<TemplateManagement />} />
                        <Route path="/content/characters" element={<CharacterManagement />} />

                        {/* 应用管理 */}
                        <Route path="/app/versions" element={<AppVersions />} />

                        <Route path="/" element={<Navigate to="/dashboard" replace />} />
                      </Routes>
                    </MainLayout>
                  </ProtectedRoute>
                }
              />
            </Routes>
          </React.Suspense>
        </Router>
      </AntdApp>
    </ConfigProvider>
  )
}

export default App

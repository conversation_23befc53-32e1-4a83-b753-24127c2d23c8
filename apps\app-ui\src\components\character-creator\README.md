角色定制
步骤：

1. 输入角色名称

2. Choose Relationship 选择关系
   比如 stonger，school Mate，Men<PERSON>，girlfriend，sex friend，step sister，step Mom 等等（自己发挥）

3. Choose Ethnicity 选择民族/人种（根据当前个人信息的性别来展示男或者女，如果用户男，就展示女相关的。）
   Caucasian，Latina, Asian，Arab，Black/Afro
   图片：雪碧图，大小: 2373 × 475
   public/images/custom/women.png
   public/images/custom/man.png

4. 选择年龄
   Teen（18+） 20s，30s，40-55

5. 选择眼睛颜色
   brown，blue，green
   图片：雪碧图，大小: 1846 × 412
   public/images/custom/eye.png

6. Choose Hair Style，选择发型

public/images/custom/women-hair.png
图片：雪碧图，大小: 1024 × 1536（女）

1. 直发（Straight Hair）
2. 自然卷发（Curly Hair）
3. 编发（Braids）
4. 高丸子头（High Bun）
5. 马尾辫（Ponytail）
6. 齐刘海短发（Short Hair with Bangs）
7. 超短发（Pixie Cut）
8. 长波浪卷（Long Waves）
9. 双马尾（Twin Tails）

public/images/custom/man-hair.png
图片：雪碧图，大小: 1024 × 1024（男）

1. 短寸（Buzz Cut）
2. 庞巴度（Pompadour）
3. 三七侧分（Side Part）
4. 凌乱蓬松短发（Messy Quiff）
5. 长直发（Long Straight Hair）
6. 自然卷发（Curly Hair）
7. 丸子头（Man Bun）
8. 渐变短寸（Textured Crop with Fade）
9. 脏辫（Dreadlocks）

10. Choose Hair Color 选择发色

11. Choose Body Type 选择身体

男性身体：
图片：雪碧图，大小: 1536 × 1024
public/images/custom/man-body.png

1. 瘦型（Slim）
2. 运动型（Athletic）
3. 健美型（Muscular）
4. 肥胖型（Heavy-set）
5. 普通型（Average）

女性身体：
图片：雪碧图，大小：1780 × 475
public/images/custom/women-body.png

1. 瘦型（Slim / Ectomorph）
2. 直筒型（Rectangle / Banana）
3. 健美型（Athletic / Muscular）
4. 沙漏型（Hourglass）
5. 丰腴型（Full-figured / Curvy）

6. Choose Breast Size 选择罩杯（女性专有）

图片：雪碧图，大小：2373 × 475
public/images/custom/women-breast.png

1. Flat – 扁平的
2. Petite – 小巧的
3. Moderate – 中等的
4. Full – 丰满的
5. Busty – 胸部丰盈的

6. Choose Butt Size 选择臀部尺寸（女性专有）

图片：雪碧图，大小：2373 × 475
public/images/custom/women-butt.png

1. Toned / Compact – 紧实小巧型（紧致、线条分明，整体偏小）
2. Slim / Petite – 纤细型（臀部圆润但体积较小，比例匀称）
3. Perky / Medium – 中等挺翘型（体积适中，臀线清晰，有自然上翘感）
4. Round / Full – 丰满圆润型（体积较大，曲线明显，有视觉冲击感）
5. Athletic / Firm – 健康运动型（肌肉线条明显，体积中等但上提、紧致）

6. 选择角色个性
   Caregiver
   Nurturing, protective, and always there to offer comfort.

Jester
Playful, humorous, and always there to make you laugh.

Submissive
Obedient, yielding, and happy to follow.

等等，列出 12 个

12. Choose Voice 选择声音。

13. Choose Clothing 选择服装（要区分男女）
    Bikini Pencil Dress Long Dress Woman Basketball Jersey Cheerleader

Skirt Tennis Soccer Swimsuit lab Coat Santa Claus Sailor
等等，列出几十个出来

14. 最后确定展示所选的所有项，点击确定，就开始生成。

# 生成步骤：

1. 得到所选的参数，组合成一串关键词
2. 加工图片的 Prompt，通过以下 Prompt，使用 openrouter.ai 的 `deepseek/deepseek-chat-v3-0324:free` 模型，输出最终的图片的 Prompt
3. 利用输出的图片 Prompt，调用 https://yunwu.ai/v1/images/generations
   文档地址：- 文生图模型 [Flux（OpenAI dall-e-3 格式）](https://yunwu.apifox.cn/api-232421932.md)
4. 最后生成角色形象
5. 数据存储

- 角色形象存数据库
  - 一些关键值：角色 id，关联的用户 id，角色关键词（字段单独存，不要存 json，到时候不好检索吧），图片的 Prompt，角色图片（需要判断返回的是 base64 还是 url，如果是 url 直接使用，base64 需要先上传到 vercel/blob，之后再存 url）

```优化 Prompt
你是一个专业的图像提示词生成器。你的任务是根据用户提供的角色关键词，生成一个详细、丰富且适合图像生成的英文的提示词。以下是生成提示词时需要遵循的原则：

1. **保留核心特征**：确保用户提供的所有关键词都被包含在最终的提示词中，不遗漏任何细节。
2. **细节增强**：
   - 对于外貌特征（如头发、眼睛、身材），添加具体的形容词和短语，增强视觉表现力。
   - 对于服装和配饰，描述材质、颜色、样式，增加画面质感。
   - 加入背景或环境描述，提供场景和氛围。
3. **艺术风格**：默认加入“detailed textures, vibrant colors, cinematic composition”来保证图像的高质量。
4. **语言要求**：使用生动、具体的语言，避免模糊或笼统的表达，不要超过  550 字符。
5. **结构**：最终提示词应包含以下部分：
   - 角色描述（外貌、服装等）
   - 背景或环境
   - 光线和氛围
   - 艺术风格和质量描述

用户会提供角色关键词（例如“白种人，长头发，黑色头发，蓝色眼睛，穿着紧身衣”），你的任务是将这些关键词整合到提示词中，并根据上述原则进行扩展和优化。直接输出最终的图片提示词，不需要额外的解释。

最终输出格式：
- 直接是 Prompt 内容，不需要分段，不需要带：Prompt：等，直接主内容即可。
- 不需要加粗，不需要特殊格式，直接就是纯文本。
```

重构方向分析

核心问题：状态管理过于复杂

当前有太多状态需要同步：

- UI 状态（React 组件）
- 内存缓存（哈希、ID、计数）
- 本地数据库（SQLite）
- 服务器状态（API）

重构方向 1：单一数据源模式

设计思路：
SQLite 作为唯一真实数据源 → UI 直接从数据库读取 → 后台异步同步服务器

核心改变：

- 移除所有内存缓存层
- UI 组件直接订阅数据库变化
- 简化同步逻辑为"仅写入数据库"

重构方向 2：事件驱动架构

设计思路：
消息事件 → 事件总线 → 多个处理器（UI 更新、数据库存储、服务器同步）

核心改变：

- 用事件驱动替代复杂的状态检测
- 每个处理器独立，互不影响
- 错误隔离，单点失败不影响整体

重构方向 3：简化的两层架构

设计思路：
内存状态层（React state） + 持久化层（定期批量同步）

核心改变：

- 移除实时同步，改为定时批量同步
- 简化为内存+定期持久化的模式
- 减少复杂的变化检测逻辑

我推荐的重构方向：方向 1 + 部分方向 2

理由：

1. SQLite 作为单一数据源最符合移动端特性
2. 事件驱动能很好解决当前的耦合问题
3. 实现相对简单，风险可控

具体架构：
UI 层：只负责展示，订阅数据库变化
数据层：SQLite + 简单的增删改查
同步层：独立的后台服务，定期与服务器同步
事件层：协调各层通信

优势：

- 状态管理简单化
- 错误隔离更好
- 更容易测试和维护
- 性能更可预测

风险：

- 需要重写大部分同步逻辑
- UI 需要适配新的数据订阅模式
- 迁移现有数据的复杂性

import React from 'react'
import cx from 'classnames'

interface AudioWaveformProps {
  /** 音量大小 (0-100) */
  volume: number
  /** 是否激活状态 */
  isActive: boolean
  /** 波形颜色主题 */
  color?: 'primary' | 'danger' | 'warning' | 'success'
  /** 波形条数 */
  bars?: number
  /** 组件类名 */
  className?: string
  /** 测试模式 - 显示模拟波形动画 */
  testMode?: boolean
}

/**
 * 音频波形显示组件
 * 实时显示音量大小的波形动画
 */
export function AudioWaveform({
  volume,
  isActive,
  color = 'primary',
  bars = 5,
  className,
  testMode
}: AudioWaveformProps) {
  // 生成波形条的高度
  const generateBarHeights = () => {
    const heights: number[] = []

    if (!isActive && !testMode) {
      // 静态状态，所有条都是最小高度
      return new Array(bars).fill(2)
    }

    // 测试模式或真实音量模式
    let effectiveVolume = volume
    if (testMode && isActive) {
      // 测试模式：生成模拟音量数据
      effectiveVolume = 0.3 + Math.sin(Date.now() * 0.01) * 0.4 + Math.random() * 0.3
    }

    // 音量放大和处理，提高敏感度
    const amplifiedVolume = Math.min(100, effectiveVolume * 200) // 降低放大倍数至200倍
    const baseHeight = Math.max(3, amplifiedVolume * 0.12) // 最小高度3px，基础高度稍微降低

    for (let i = 0; i < bars; i++) {
      // 中间的条更高，两边的条稍低，营造立体效果
      const centerIndex = Math.floor(bars / 2)
      const distanceFromCenter = Math.abs(i - centerIndex)
      const factor = 1 - distanceFromCenter * 0.1 // 减少衰减，让边缘条也有足够高度

      // 添加随机波动，但保持一定的规律性
      const randomFactor = 0.8 + Math.random() * 0.4 // 0.8-1.2的随机因子
      let height = baseHeight * factor * randomFactor

      // 确保最小高度和合理的最大高度
      height = Math.max(3, Math.min(16, height)) // 调整高度范围到3-16px
      heights.push(height)
    }

    return heights
  }

  const barHeights = generateBarHeights()

  // 颜色映射
  const colorClasses = {
    primary: 'bg-primary',
    danger: 'bg-danger',
    warning: 'bg-warning',
    success: 'bg-success'
  }

  return (
    <div className={cx('flex items-end justify-center space-x-1', className)}>
      {barHeights.map((height, index) => (
        <div
          key={index}
          className={cx(
            'w-1 rounded-full transition-all duration-150',
            colorClasses[color],
            isActive && 'animate-pulse'
          )}
          style={{
            height: `${height}px`,
            animationDelay: `${index * 50}ms` // 错开动画时间
          }}
        />
      ))}
    </div>
  )
}

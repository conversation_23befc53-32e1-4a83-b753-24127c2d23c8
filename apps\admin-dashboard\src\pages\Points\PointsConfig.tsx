import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Space,
  message,
  Form,
  InputNumber,
  Typography,
  Divider,
  Row,
  Col,
  Tooltip,
  Popconfirm,
  Alert,
  Descriptions
} from 'antd'
import {
  SettingOutlined,
  SaveOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { pointsService } from '@/services/points'

const { Title, Text } = Typography

// 功能名称映射
const FEATURE_NAMES: Record<string, { name: string; description: string; icon: string }> = {
  IMAGE_GENERATION: {
    name: '图片生成',
    description: '使用AI生成图片时消耗的积分',
    icon: '🖼️'
  },
  VOICE_GENERATION: {
    name: '语音生成',
    description: '使用TTS生成语音时消耗的积分',
    icon: '🎤'
  },
  SCRIPT_PURCHASE: {
    name: '脚本购买',
    description: '购买互动脚本时消耗的积分',
    icon: '📜'
  },
  GALLERY_GENERATION: {
    name: '写真集生成',
    description: '生成写真集时消耗的积分',
    icon: '📸'
  },
  VIDEO_GENERATION: {
    name: '视频生成',
    description: '使用AI生成视频时消耗的积分',
    icon: '🎬'
  }
}

const PointsConfigPage: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [resetting, setResetting] = useState(false)
  const [pointsConfig, setPointsConfig] = useState<Record<string, number>>({})
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    loadPointsConfig()
  }, [])

  const loadPointsConfig = async () => {
    try {
      setLoading(true)
      
      const response = await pointsService.getPointsConfig()
      
      if (response.success && response.data) {
        setPointsConfig(response.data)
        form.setFieldsValue(response.data)
        setHasChanges(false)
      } else {
        message.error(response.message || '获取积分配置失败')
      }
    } catch (error) {
      console.error('获取积分配置失败:', error)
      message.error('获取积分配置失败')
    } finally {
      setLoading(false)
    }
  }

  const handleFormChange = () => {
    setHasChanges(true)
  }

  const handleSave = async () => {
    try {
      const values = await form.validateFields()
      setSaving(true)
      
      // 构建配置更新数组
      const configs = Object.entries(values)
        .filter(([feature, cost]) => 
          cost !== undefined && 
          cost !== pointsConfig[feature] &&
          FEATURE_NAMES[feature]
        )
        .map(([feature, cost]) => ({ feature, cost: Number(cost) }))

      if (configs.length === 0) {
        message.info('没有配置发生变化')
        return
      }

      const response = await pointsService.batchUpdatePointsConfig(configs)
      
      if (response.success) {
        message.success(`成功更新 ${configs.length} 个配置`)
        setHasChanges(false)
        loadPointsConfig() // 重新加载最新配置
      } else {
        message.error(response.message || '保存配置失败')
      }
    } catch (error) {
      console.error('保存积分配置失败:', error)
      message.error('保存配置失败')
    } finally {
      setSaving(false)
    }
  }

  const handleReset = async () => {
    try {
      setResetting(true)
      
      const response = await pointsService.resetPointsConfig()
      
      if (response.success) {
        message.success('成功重置为默认配置')
        setHasChanges(false)
        loadPointsConfig() // 重新加载默认配置
      } else {
        message.error(response.message || '重置配置失败')
      }
    } catch (error) {
      console.error('重置积分配置失败:', error)
      message.error('重置配置失败')
    } finally {
      setResetting(false)
    }
  }

  const handleDiscard = () => {
    form.setFieldsValue(pointsConfig)
    setHasChanges(false)
    message.info('已取消修改')
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2} style={{ margin: 0 }}>
          <SettingOutlined style={{ marginRight: 8 }} />
          积分消耗配置
        </Title>
        
        <Space>
          <Button 
            icon={<ReloadOutlined />}
            onClick={loadPointsConfig}
            loading={loading}
          >
            刷新
          </Button>
          
          {hasChanges && (
            <Button onClick={handleDiscard}>
              取消修改
            </Button>
          )}
          
          <Button 
            type="primary"
            icon={<SaveOutlined />}
            onClick={handleSave}
            loading={saving}
            disabled={!hasChanges}
          >
            保存配置
          </Button>
          
          <Popconfirm
            title="确定要重置为默认配置吗？"
            description="此操作将覆盖所有当前配置，无法撤销"
            onConfirm={handleReset}
            okText="确定"
            cancelText="取消"
            icon={<WarningOutlined style={{ color: 'red' }} />}
          >
            <Button 
              danger
              loading={resetting}
            >
              重置为默认
            </Button>
          </Popconfirm>
        </Space>
      </div>

      {hasChanges && (
        <Alert
          message="配置已修改"
          description="您有未保存的配置修改，请点击「保存配置」来应用更改。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
          action={
            <Space>
              <Button size="small" onClick={handleDiscard}>
                取消修改
              </Button>
              <Button size="small" type="primary" onClick={handleSave}>
                立即保存
              </Button>
            </Space>
          }
        />
      )}

      <Alert
        message="积分配置说明"
        description="这里配置的是各种功能消耗积分的数量。修改后会立即影响用户使用功能时的积分扣除。请谨慎操作。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Card loading={loading}>
        <Form
          form={form}
          layout="vertical"
          onValuesChange={handleFormChange}
        >
          <Row gutter={[16, 16]}>
            {Object.entries(FEATURE_NAMES).map(([feature, info]) => (
              <Col xs={24} sm={12} lg={8} key={feature}>
                <Card 
                  size="small"
                  style={{ height: '100%' }}
                  bodyStyle={{ padding: 16 }}
                >
                  <div style={{ marginBottom: 12 }}>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                      <span style={{ fontSize: '20px', marginRight: 8 }}>
                        {info.icon}
                      </span>
                      <Text strong>{info.name}</Text>
                      <Tooltip title={info.description}>
                        <InfoCircleOutlined 
                          style={{ marginLeft: 4, color: '#999' }}
                        />
                      </Tooltip>
                    </div>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {info.description}
                    </Text>
                  </div>
                  
                  <Form.Item
                    name={feature}
                    label="消耗积分"
                    rules={[
                      { required: true, message: '请输入积分数量' },
                      { type: 'number', min: 0, message: '积分数量不能小于0' }
                    ]}
                    style={{ marginBottom: 0 }}
                  >
                    <InputNumber
                      style={{ width: '100%' }}
                      min={0}
                      max={10000}
                      precision={0}
                      placeholder="0"
                      addonAfter="积分"
                    />
                  </Form.Item>
                </Card>
              </Col>
            ))}
          </Row>
        </Form>

        <Divider />

        <Descriptions
          title="当前配置概览"
          size="small"
          column={{ xs: 1, sm: 2, md: 3 }}
        >
          {Object.entries(FEATURE_NAMES).map(([feature, info]) => (
            <Descriptions.Item 
              key={feature}
              label={
                <span>
                  {info.icon} {info.name}
                </span>
              }
            >
              <Text strong>
                {pointsConfig[feature] || 0} 积分
              </Text>
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Card>
    </div>
  )
}

export default PointsConfigPage
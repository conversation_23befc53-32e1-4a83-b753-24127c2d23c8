import React from 'react'
import { Input, RadioGroup, Radio, Card, CardBody } from '@heroui/react'
import { Icon } from '@iconify/react'
import type { CharacterData } from '..'
import { characterMapping } from '../mapping'
import { useTranslation } from 'react-i18next'

// 预设关系选项的图标映射
const relationshipIcons = {
  friend: 'solar:users-group-rounded-bold',
  lover: 'solar:heart-bold',
  mentor: 'solar:glasses-bold',
  family: 'solar:home-bold',
  colleague: 'solar:case-minimalistic-bold',
  classmate: 'solar:graduation-square-bold'
}

// 预设关系选项
const getRelationshipOptions = (t: any) => [
  ...Object.entries(characterMapping.relationship).map(([value, label]) => ({
    value,
    label,
    icon:
      relationshipIcons[value as keyof typeof relationshipIcons] || 'solar:users-group-rounded-bold'
  })),
  { value: 'custom', label: t('customRole:basic_info.custom_relationship'), icon: 'solar:pen-bold' }
]

interface BasicInfoProps {
  data: CharacterData
  onUpdate: (data: Partial<CharacterData>) => void
}

export default function BasicInfo({ data, onUpdate }: BasicInfoProps) {
  const { t } = useTranslation(['customRole'])
  const relationshipOptions = getRelationshipOptions(t)
  
  const [customRelationship, setCustomRelationship] = React.useState(
    !relationshipOptions.find(option => option.value === data.relationship) &&
      data.relationship !== ''
  )

  // 处理关系选择变更
  const handleRelationshipChange = (value: string) => {
    if (value === 'custom') {
      setCustomRelationship(true)
      onUpdate({ relationship: '' })
    } else {
      setCustomRelationship(false)
      onUpdate({ relationship: value })
    }
  }

  return (
    <div className="space-y-6">
      {/* 角色名称 */}
      <div className="space-y-2">
        <Input
          label={t('customRole:basic_info.character_name')}
          placeholder={t('customRole:basic_info.character_name_placeholder')}
          value={data.name}
          onValueChange={value => onUpdate({ name: value })}
          variant="bordered"
          color="primary"
          size="lg"
          startContent={<Icon icon="solar:user-bold" className="text-default-400" width={20} />}
          classNames={{
            input: 'text-medium',
            inputWrapper: 'bg-default-50 hover:bg-default-100'
          }}
        />
      </div>

      {/* 关系选择 */}
      <div className="space-y-3">
        <h3 className="text-medium font-medium text-foreground">{t('customRole:basic_info.select_relationship')}</h3>
        <RadioGroup
          value={customRelationship ? 'custom' : data.relationship}
          onValueChange={handleRelationshipChange}
          aria-label={t('customRole:basic_info.select_relationship_aria')}
          classNames={{
            wrapper: 'grid grid-cols-2 gap-3 sm:grid-cols-3'
          }}
        >
          {relationshipOptions.map(option => (
            <Card
              key={option.value}
              isPressable
              className={`
                cursor-pointer transition-all duration-200 border-2
                ${
                  (customRelationship && option.value === 'custom') ||
                  (!customRelationship && data.relationship === option.value)
                    ? 'border-primary ring-2 ring-primary/20 bg-primary/5'
                    : 'border-transparent hover:border-primary/30 hover:bg-default-100/50'
                }
              `}
              onPress={() => handleRelationshipChange(option.value)}
            >
              <CardBody className="p-4">
                <Radio
                  value={option.value}
                  className="hidden"
                  aria-label={t('customRole:basic_info.select_specific_relationship_aria', { label: option.label })}
                />
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary">
                    <Icon icon={option.icon} width={18} />
                  </div>
                  <span className="text-medium font-medium text-foreground">{option.label}</span>
                </div>
              </CardBody>
            </Card>
          ))}
        </RadioGroup>

        {/* 自定义关系输入 */}
        {customRelationship && (
          <Input
            label={t('customRole:basic_info.custom_relationship')}
            placeholder={t('customRole:basic_info.custom_relationship_placeholder')}
            value={data.relationship}
            onValueChange={value => onUpdate({ relationship: value })}
            variant="bordered"
            color="primary"
            startContent={<Icon icon="solar:pen-bold" className="text-default-400" width={20} />}
            classNames={{
              input: 'text-medium',
              inputWrapper: 'bg-default-50 hover:bg-default-100'
            }}
          />
        )}
      </div>
    </div>
  )
}

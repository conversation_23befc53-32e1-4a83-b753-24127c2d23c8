{"connection": {"title": "Connect Device", "failed": "Device Connection Failed", "disconnect_failed": "Device Disconnection Failed", "bluetooth_device": "Bluetooth Device"}, "connection_form": {"input_placeholder": "Please enter device code", "invalid_code": "Invalid device code, please check and try again", "connect_failed": "Failed to connect device, please try again later", "qr_not_supported": "Current device does not support QR code scanning", "invalid_qr_code": "Scanned device code is invalid", "qr_unrecognized": "Failed to recognize QR code content", "camera_permission_denied": "Camera permission denied, please enable camera access in settings", "scan_not_supported": "Current device does not support QR code scanning", "scan_failed": "<PERSON><PERSON> failed, please try again", "input_label": "Enter Device Code", "input_example": "Example codes: 1583, 6842, 2137", "connecting": "Connecting...", "connect_button": "Connect Device", "or": "OR", "scanning": "Scanning...", "scan_button": "Scan Device QR Code", "scan_instruction": "Place the device QR code in front of the camera"}, "control": {"title": "Device Control Panel", "connected": "Connected", "bluetooth_error": "Bluetooth Error", "bluetooth_ready": "Bluetooth Ready", "bluetooth_initializing": "Bluetooth Initializing...", "bluetooth_not_initialized": "Bluetooth Not Initialized", "wait_bluetooth_init": "Please wait for Bluetooth initialization to complete", "bluetooth_connection_error": "Bluetooth Connection Error", "mode_not_found": "Mode ID not found", "select_classic_mode": "Select Classic Mode", "send_classic_mode_failed": "Failed to send classic mode command", "device_function_set": "Device function {{function<PERSON>ey}} intensity set to {{statusText}} (value: {{intensity}})", "debounce_delay": "Sending Bluetooth command after debounce delay", "intensity_zero": "Intensity is zero, sending stop command", "stop_command_sent": "Stop command sent", "stop_command_not_found": "Stop command not found for function {{functionKey}}", "intensity": "Intensity", "off": "Off", "level": "Level {{level}}", "classic_mode": "Classic Mode", "disconnect_failed": "Disconnection Failed", "disconnect_button": "Disconnect"}}
// ElevenLabs V3 API 客户端 - 逆向 API 调用
import type { V3GenerateRequest, V3GenerateResponse, SessionInfo } from './types'

export class V3ApiClient {
  private readonly baseUrl = 'https://api.elevenlabs.io'
  private readonly userAgent =
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

  /**
   * 调用 V3 TTS API (逆向)
   */
  async generateTTS(request: V3GenerateRequest, session: SessionInfo): Promise<V3GenerateResponse> {
    try {
      // 构建请求头
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${session.token}`,
        'User-Agent': this.userAgent
      }

      console.log('调用 ElevenLabs V3 API:', {
        inputs: request.inputs.map(input => ({
          text: input.text.substring(0, 50) + '...',
          voice_id: input.voice_id
        })),
        model_id: request.model_id
      })

      // 发送请求到 V3 TTS API
      const response = await fetch(`${this.baseUrl}/v1/text-to-dialogue/stream`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
        signal: AbortSignal.timeout(60000) // 60秒超时
      })

      // 检查响应状态
      if (!response.ok) {
        const errorText = await response.text()
        console.error('V3 API 错误:', response.status, errorText)

        // 解析错误信息
        let errorMessage = `HTTP ${response.status}`
        try {
          const errorJson = JSON.parse(errorText)
          errorMessage = errorJson.message || errorJson.error || errorMessage
        } catch {
          errorMessage = errorText || errorMessage
        }

        return {
          status: 'failed',
          error: errorMessage
        }
      }

      // 检查响应类型
      const contentType = response.headers.get('content-type') || ''

      // ElevenLabs V3 API 返回 stream 格式
      if (contentType.includes('text/') || response.body) {
        // 读取 stream 数据
        const reader = response.body?.getReader()
        const chunks: Uint8Array[] = []

        if (!reader) {
          return {
            status: 'failed',
            error: '无法读取响应数据流'
          }
        }

        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break

            if (value) {
              chunks.push(value)
            }
          }

          // 合并所有 chunks
          const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0)
          const audioBuffer = new Uint8Array(totalLength)
          let offset = 0

          for (const chunk of chunks) {
            audioBuffer.set(chunk, offset)
            offset += chunk.length
          }

          if (audioBuffer.byteLength === 0) {
            return {
              status: 'failed',
              error: 'V3 API 返回空音频数据'
            }
          }

          console.log('V3 API 成功返回音频:', audioBuffer.byteLength, 'bytes')

          return {
            status: 'completed',
            audio_data: audioBuffer.buffer
          }
        } finally {
          reader.releaseLock()
        }
      } else {
        return {
          status: 'failed',
          error: `不支持的响应类型: ${contentType}`
        }
      }
    } catch (error) {
      console.error('V3 API 调用失败:', error)

      if (error instanceof Error) {
        return {
          status: 'failed',
          error: error.message
        }
      } else {
        return {
          status: 'failed',
          error: '未知错误'
        }
      }
    }
  }

  /**
   * 轮询异步任务状态 (如果 V3 API 使用异步模式)
   */
  async pollTaskStatus(
    taskId: string,
    session: SessionInfo,
    maxAttempts: number = 30
  ): Promise<V3GenerateResponse> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await fetch(`${this.baseUrl}/v1/text-to-speech/task/${taskId}`, {
          headers: {
            Authorization: `Bearer ${session.token}`,
            'User-Agent': this.userAgent
          }
        })

        if (!response.ok) {
          console.error(`轮询任务状态失败 (attempt ${attempt}):`, response.status)

          if (attempt === maxAttempts) {
            return {
              status: 'failed',
              error: `轮询任务超时: ${response.status}`
            }
          }

          // 等待后重试
          await this.sleep(2000)
          continue
        }

        const result = await response.json()

        if (result.status === 'completed') {
          if (result.audio_url) {
            // 下载音频数据
            const audioResponse = await fetch(result.audio_url)
            const audioBuffer = await audioResponse.arrayBuffer()

            return {
              status: 'completed',
              audio_data: audioBuffer,
              audio_url: result.audio_url
            }
          } else {
            return {
              status: 'failed',
              error: '任务完成但无音频 URL'
            }
          }
        } else if (result.status === 'failed') {
          return {
            status: 'failed',
            error: result.error || '任务失败'
          }
        } else if (result.status === 'processing') {
          // 继续等待
          console.log(`任务 ${taskId} 处理中... (attempt ${attempt}/${maxAttempts})`)
          await this.sleep(2000)
          continue
        } else {
          return {
            status: 'failed',
            error: `未知任务状态: ${result.status}`
          }
        }
      } catch (error) {
        console.error(`轮询任务状态异常 (attempt ${attempt}):`, error)

        if (attempt === maxAttempts) {
          return {
            status: 'failed',
            error: `轮询任务失败: ${error instanceof Error ? error.message : 'Unknown error'}`
          }
        }

        // 等待后重试
        await this.sleep(2000)
      }
    }

    return {
      status: 'failed',
      error: '轮询任务超时'
    }
  }

  /**
   * 获取可用声音列表
   */
  async getVoices(session: SessionInfo): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/voices`, {
        headers: {
          Authorization: `Bearer ${session.token}`,
          'User-Agent': this.userAgent
        }
      })

      if (!response.ok) {
        console.error('获取声音列表失败:', response.status)
        return []
      }

      const result = await response.json()
      return result.voices || []
    } catch (error) {
      console.error('获取声音列表异常:', error)
      return []
    }
  }

  /**
   * 获取用户配额信息
   */
  async getUserQuota(session: SessionInfo): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/v1/user/subscription`, {
        headers: {
          Authorization: `Bearer ${session.token}`,
          'User-Agent': this.userAgent
        }
      })

      if (!response.ok) {
        console.error('获取用户配额失败:', response.status)
        return null
      }

      return await response.json()
    } catch (error) {
      console.error('获取用户配额异常:', error)
      return null
    }
  }

  /**
   * 等待函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

# RC Demo App - AI 图像生成应用

## 项目概述

基于 React + Next.js 的 AI 图像生成应用，支持角色聊天和模板化图像生成功能。

## 现有功能

### 角色聊天系统

- **前端**: `app-ui` 目录 - React 应用
- **后端**: `backend` 目录 - Next.js 应用

## 新增功能规划

### 1. 会员制度系统

#### 会员套餐设计

- **基础套餐**: $19.9/月，包含 100 点数
- **进阶套餐**: $39.9/月，包含 400 点数
- **专业套餐**: $99.9/月，包含 1000 点数

#### 点数购买（单独购买）

- **点数套餐**: 价格待定，后续调整

#### 点数规则

- 点数无有效期限制
- 会员到期后点数保留，但无法使用会员专属功能
- 会员专属功能：高级模板、优先生成队列、更高生成质量

#### 会员权益

- ✅ 访问会员专属模板
- ✅ 优先生成队列
- ✅ 更高的生成质量选项
- ✅ 历史记录云端同步

### 2. 发现之旅功能

#### 模板系统

- **普通模板**: 免费用户可使用，消耗点数记录在模板表
- **会员模板**: 仅会员可使用，消耗点数记录在模板表

#### 生成流程

1. **模板选择** - 用户浏览并选择模板
2. **图片上传** - 上传原始图片素材
3. **点数扣除** - 根据模板设定的点数消耗规则扣除
4. **生成处理** - 后台 AI 处理生成图像
5. **历史记录** - 自动保存到用户历史记录
6. **结果展示** - 生成完成后展示最终结果

## 业务闭环检查

### 用户获取点数的方式

1. 购买会员套餐（获得对应点数）
2. 单独购买点数套餐

### 用户消耗点数的方式

1. 使用普通模板生成图像
2. 使用会员模板生成图像（需要会员身份）

### 会员身份管理

1. 购买会员套餐获得会员身份
2. 会员到期后失去会员专属功能，但点数保留
3. 可续费延长会员身份

## 数据表设计

### 核心业务表

- **会员计划表** - 存储套餐信息和价格
- **会员订阅表** - 用户当前会员状态
- **会员订阅历史记录表** - 用户所有订阅记录
- **用户点数表** - 用户点数余额和使用记录
- **模板表** - 模板信息和点数消耗规则
- **生成历史记录表** - 用户生成记录

## 第三方集成

- **支付系统**: nowpayments
- **文件存储**: cloudflare r2

---

会员计划表
会员订阅表
用户点数表

---

2. 发现之旅

- 展示模板，有会员模板，有普通模板
- 用户点击，选择对应模板，生图
- 生图需要消耗点数

生成流程

- 选择模版
- 用户上传图片
- 消耗点数
- 添加到历史记录
- 等待生成，生成结束，得到结果

---

模板表
生成历史记录表

---

import type { Message } from '@/api/services'
// 聊天数据库类型定义

// 聊天会话表
export interface ChatSession {
  id: string // 主键，对应chatId
  roleId: string // 当前使用的角色ID
  title: string // 会话标题（取第一条用户消息的前20字符）
  messageCount: number // 消息数量
  backgroundImagePath?: string // 背景图本地文件路径（Capacitor Filesystem）
  backgroundImageUrl?: string // 背景图原始服务器URL（用于重新下载）
  backgroundSceneDescription?: string // 背景图场景描述
  backgroundUpdatedAt?: string // 背景图最后更新时间
  createdAt: string // ISO日期字符串
  updatedAt: string // ISO日期字符串
  lastMessageAt: string // 最后一条消息时间
}

// 聊天消息表
export interface ChatMessage {
  id: string // 主键，对应message.id
  chatId: string // 外键，关联ChatSession.id
  role: 'user' | 'assistant' | 'system'
  content: string // 文本内容
  isStreaming: boolean // 是否正在流式更新
  streamingVersion: number // 流式更新版本号，防止冲突
  createdAt: string // ISO日期字符串
  updatedAt: string // ISO日期字符串
}

// 消息附件表
export interface MessageAttachment {
  id: string // 主键
  messageId: string // 外键，关联ChatMessage.id
  type: 'audio' | 'image' | 'video'
  name: string // 附件名称
  contentType: string // MIME类型
  originalUrl: string // 原始服务器URL
  localPath?: string // 本地文件路径（Capacitor Filesystem）
  status: 'generating' | 'downloading' | 'completed' | 'failed'
  fileSize?: number // 文件大小（字节）
  metadata: string // JSON格式的元数据
  createdAt: string // ISO日期字符串
  updatedAt: string // ISO日期字符串
}

// 数据库操作接口
export interface ChatDatabaseInterface {
  // 数据库管理
  initialize(): Promise<boolean>
  close(): Promise<void>

  // 会话操作
  createSession(session: Omit<ChatSession, 'createdAt' | 'updatedAt'>): Promise<ChatSession>
  getSession(id: string): Promise<ChatSession | null>
  getAllSessions(): Promise<ChatSession[]>
  getSessionsByRole(roleId: string): Promise<ChatSession[]>
  getLatestSessionByRole(roleId: string): Promise<ChatSession | null>
  updateSession(id: string, updates: Partial<ChatSession>): Promise<boolean>
  deleteSession(id: string): Promise<boolean>

  // 消息操作
  createMessage(message: Omit<ChatMessage, 'createdAt' | 'updatedAt'>): Promise<ChatMessage>
  getMessage(id: string): Promise<ChatMessage | null>
  getMessagesByChat(chatId: string, limit?: number, offset?: number): Promise<ChatMessage[]>
  updateMessage(id: string, updates: Partial<ChatMessage>): Promise<boolean>
  deleteMessage(id: string): Promise<boolean>

  // 附件操作
  createAttachment(
    attachment: Omit<MessageAttachment, 'createdAt' | 'updatedAt'>
  ): Promise<MessageAttachment>
  getAttachment(id: string): Promise<MessageAttachment | null>
  getAttachmentsByMessage(messageId: string): Promise<MessageAttachment[]>
  updateAttachment(id: string, updates: Partial<MessageAttachment>): Promise<boolean>
  deleteAttachment(id: string): Promise<boolean>

  // 背景图操作
  updateSessionBackground(
    sessionId: string,
    backgroundImageUrl: string,
    backgroundImagePath: string,
    sceneDescription: string
  ): Promise<boolean>
  getSessionBackground(sessionId: string): Promise<{
    backgroundImagePath: string
    backgroundImageUrl: string
    backgroundSceneDescription: string
    backgroundUpdatedAt: string
  } | null>
  clearSessionBackground(sessionId: string): Promise<boolean>

  // 复合操作
  saveCompleteMessage(
    chatId: string,
    message: Omit<ChatMessage, 'chatId' | 'createdAt' | 'updatedAt'>,
    attachments?: Omit<MessageAttachment, 'messageId' | 'createdAt' | 'updatedAt'>[]
  ): Promise<{ message: ChatMessage; attachments: MessageAttachment[] }>

  // 🚀 新增：清理重复的空助手消息
  cleanupDuplicateEmptyAssistantMessages(chatId: string): Promise<number>

  // 清理操作
  cleanupOldData(
    daysToKeep: number
  ): Promise<{ deletedSessions: number; deletedMessages: number; deletedAttachments: number }>
  getStorageStats(): Promise<{
    sessionCount: number
    messageCount: number
    attachmentCount: number
    totalSize: number
  }>
}

// 同步管理器相关类型
export interface SyncResult {
  success: boolean
  error?: string
  updatedMessages?: number
  updatedAttachments?: number
  skipped?: boolean
  reason?: string
}

export interface SyncOptions {
  enableDebounce?: boolean
  debounceMs?: number
  batchSize?: number
}

// 转换工具类型
export interface MessageConverter {
  fromLangChain(
    message: Message,
    chatId: string
  ): {
    message: Omit<ChatMessage, 'createdAt' | 'updatedAt'>
    attachments: Omit<MessageAttachment, 'messageId' | 'createdAt' | 'updatedAt'>[]
  }
  toLangChain(message: ChatMessage, attachments: MessageAttachment[]): Message
}

// 错误类型
export class ChatDatabaseError extends Error {
  constructor(message: string, public code: string, public cause?: Error) {
    super(message)
    this.name = 'ChatDatabaseError'
  }
}

// 数据库配置
export interface ChatDatabaseConfig {
  dbName: string
  version: number
  enableWAL?: boolean
  enableForeignKeys?: boolean
  journalMode?: 'DELETE' | 'TRUNCATE' | 'PERSIST' | 'MEMORY' | 'WAL' | 'OFF'
}

export const DEFAULT_DATABASE_CONFIG: ChatDatabaseConfig = {
  dbName: 'ChatDatabase',
  version: 1,
  enableWAL: true,
  enableForeignKeys: true,
  journalMode: 'WAL'
}

// 聊天初始化相关类型
export interface ChatInitializationOptions {
  chatId?: string
  roleId: string
  chatDatabase?: ChatDatabaseInterface
}

export interface ChatInitializationResult {
  isLoading: boolean
  error: string | null
  messages: any[] // LangChainMessage类型
  chatId: string
  refresh: () => Promise<void>
}

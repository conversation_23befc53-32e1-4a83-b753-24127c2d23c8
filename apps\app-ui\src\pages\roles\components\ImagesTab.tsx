import { useState, useEffect } from 'react'
import { Icon } from '@iconify/react'
import { Skeleton, Button } from '@heroui/react'
import { motion, AnimatePresence } from 'framer-motion'
import type { DisplayRole } from '@/lib/types'
import { apiService } from '@/api'
import type { ImageRecord } from '@/api/services/character-media'
import { ImageDrawer } from '@/components/common/image-drawer'

interface ImagesTabProps {
  roleId?: string
  role: DisplayRole | null
}

export function ImagesTab({ roleId }: ImagesTabProps) {
  const [images, setImages] = useState<ImageRecord[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(false)
  const [selectedImage, setSelectedImage] = useState<ImageRecord | null>(null)
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)

  // 获取图片数据
  const fetchImages = async () => {
    if (!roleId) {
      console.log('🔍 [ImagesTab] roleId 为空，跳过获取图片')
      setIsLoading(false)
      return
    }

    try {
      setIsLoading(true)
      setError(null)

      console.log('🔍 [ImagesTab] 开始获取角色图片:', roleId)

      const data = await apiService.characterMedia.getCharacterImages(roleId, {
        limit: 20,
        offset: 0
      })

      console.log('🔍 [ImagesTab] API 响应:', data)

      if (data) {
        const images = data.images || []
        console.log('🔍 [ImagesTab] 设置图片数据:', images)
        setImages(images)
        setHasMore(data.hasMore || false)
      } else {
        console.error('🔍 [ImagesTab] API 调用失败:', data)
        setError('获取图片失败')
        setImages([]) // 确保设置为空数组
      }
    } catch (err) {
      console.error('🔍 [ImagesTab] 获取角色图片失败:', err)
      setError('获取图片失败')
      setImages([]) // 确保设置为空数组
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchImages()
  }, [roleId])

  // 处理图片点击
  const handleImageClick = (image: ImageRecord) => {
    setSelectedImage(image)
    setIsDrawerOpen(true)
  }

  // 处理抽屉关闭
  const handleDrawerClose = () => {
    setIsDrawerOpen(false)
    setSelectedImage(null)
  }

  if (isLoading) {
    return (
      <div className="py-4">
        <div className="grid grid-cols-3 gap-2">
          {Array.from({ length: 6 }).map((_, index) => (
            <Skeleton key={index} className="aspect-square rounded-lg">
              <div className="w-full h-full bg-default-200" />
            </Skeleton>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="py-8 flex flex-col items-center justify-center">
        <Icon icon="solar:gallery-broken" className="w-12 h-12 text-danger mb-4" />
        <p className="text-danger text-center mb-4">{error}</p>
        <Button
          variant="light"
          color="primary"
          onPress={fetchImages}
          startContent={<Icon icon="solar:refresh-linear" className="w-4 h-4" />}
        >
          重试
        </Button>
      </div>
    )
  }

  if (!images || images.length === 0) {
    return (
      <div className="py-8 flex flex-col items-center justify-center">
        <Icon icon="solar:gallery-linear" className="w-12 h-12 text-default-400 mb-4" />
        <p className="text-default-500 text-center">还没有生成过图片</p>
        <p className="text-default-400 text-center text-sm mt-2">在聊天中生成图片后会显示在这里</p>
      </div>
    )
  }

  return (
    <>
      <div className="py-4">
        <AnimatePresence>
          <motion.div
            className="grid grid-cols-3 gap-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            {(images || []).map((image, index) => (
              <motion.div
                key={image.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05, duration: 0.3 }}
                className="aspect-square bg-default-100 rounded-lg overflow-hidden cursor-pointer group relative"
                onClick={() => handleImageClick(image)}
              >
                <img
                  src={image.url}
                  alt={image.prompt || '生成的图片'}
                  className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  loading="lazy"
                />
                {/* 悬停遮罩 */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                  <Icon
                    icon="solar:eye-linear"
                    className="w-6 h-6 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
                {/* 生成类型标签 */}
                {image.generationType && (
                  <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="bg-black/70 text-white text-xs px-2 py-1 rounded">
                      {image.generationType === 'multimodal_chat'
                        ? '聊天'
                        : image.generationType === 'template_based'
                        ? '模板'
                        : '独立'}
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* 加载更多按钮 */}
        {hasMore && (
          <div className="flex justify-center mt-6">
            <Button
              variant="light"
              color="primary"
              startContent={<Icon icon="solar:refresh-linear" className="w-4 h-4" />}
            >
              加载更多
            </Button>
          </div>
        )}
      </div>

      {/* 图片查看器 */}
      {selectedImage && (
        <ImageDrawer
          isOpen={isDrawerOpen}
          onClose={handleDrawerClose}
          imageUrl={selectedImage.url}
          title="生成的图片"
        />
      )}
    </>
  )
}

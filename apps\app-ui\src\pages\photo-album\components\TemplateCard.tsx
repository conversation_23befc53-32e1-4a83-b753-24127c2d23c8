import { motion } from 'framer-motion'
import { Icon } from '@iconify/react'
import type { PhotoTemplate } from '@/stores/photo-generation-store'
import { useTranslation } from 'react-i18next'

interface TemplateCardProps {
  template: PhotoTemplate
  onSelect: (templateId: string, template: PhotoTemplate) => void
}

export function TemplateCard({ template, onSelect }: TemplateCardProps) {
  const { t } = useTranslation('photo-album')
  const handleClick = () => {
    if (template.hasAccess) {
      onSelect(template.id, template)
    }
  }

  return (
    <motion.div
      className="w-full cursor-pointer"
      onClick={handleClick}
      whileTap={{ scale: 0.98 }}
      layoutId={`template-container-${template.id}`}
    >
      <div className="relative w-full h-[280px] mx-auto rounded-3xl overflow-hidden group">
        {/* 背景图片 */}
        <motion.img
          src={template.previewImage || '/images/templates/default.jpg'}
          alt={template.name}
          className="absolute inset-0 w-full h-full object-cover"
          layoutId={`template-image-${template.id}`}
        />

        {/* 艺术化渐变遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-t from-black-900/60 via-black-500/20 to-transparent" />

        {/* 底部信息区域 */}
        <div className="absolute inset-x-0 bottom-0 h-28 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />

        {/* 会员标识 */}
        {template.isPremium && (
          <div className="absolute top-3 right-3">
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs font-bold px-2 py-1 rounded-full">
              {t('template.vip')}
            </div>
          </div>
        )}

        {/* 积分消耗 */}
        <div className="absolute top-3 left-3">
          <div className="bg-black/60 backdrop-blur-sm text-white text-xs font-medium px-2 py-1 rounded-full flex items-center gap-1">
            <Icon icon="solar:star-bold" className="w-3 h-3 text-yellow-400" />
            {t('template.points', { points: template.pointsCost })}
          </div>
        </div>

        {/* 模板信息 */}
        <motion.div
          className="absolute bottom-0 left-0 right-0 p-4 pb-3"
          layoutId={`template-info-${template.id}`}
        >
          <motion.div
            className="text-white text-lg font-bold tracking-tight mb-1"
            style={{ fontFamily: "'PingFang SC', sans-serif" }}
            layoutId={`template-name-${template.id}`}
          >
            {template.name}
          </motion.div>
          {template.description && (
            <motion.div
              className="text-gray-200 text-sm leading-relaxed line-clamp-2"
              style={{ fontFamily: "'PingFang SC', sans-serif" }}
              layoutId={`template-description-${template.id}`}
            >
              {template.description}
            </motion.div>
          )}

          {/* 无权限遮罩 */}
          {!template.hasAccess && (
            <div className="absolute inset-0 bg-black/50 backdrop-blur-sm rounded-3xl flex items-center justify-center">
              <div className="text-center">
                <Icon icon="solar:lock-bold" className="w-8 h-8 text-white mb-2 mx-auto" />
                <div className="text-white text-sm font-medium">
                  {template.requiresMembership ? t('template.requiresMembership') : t('template.insufficientPoints')}
              </div>
              </div>
            </div>
          )}
        </motion.div>
      </div>
    </motion.div>
  )
}

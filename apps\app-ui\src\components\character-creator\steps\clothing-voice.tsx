import React from 'react'
import { Card, CardBody, But<PERSON>, Chip, Spinner } from '@heroui/react'
import type { CharacterData } from '..'
import { voicesService, type VoiceOption } from '@/api/services/voices'
import { useVoiceModelsStore, useVoiceModelsData } from '@/stores/voice-models-store'

// 对应每个section的组件
const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-6">
    <h3 className="font-medium text-center text-lg mb-4 text-primary">{title}</h3>
    {children}
  </div>
)

// 女性服装选项
const femaleClothingOptions = [
  { value: 'bikini', label: '比基尼', emoji: '👙' },
  { value: 'pencil-dress', label: '铅笔裙', emoji: '👗' },
  { value: 'long-dress', label: '长裙', emoji: '👗' },
  { value: 'basketball-jersey', label: '篮球服', emoji: '🏀' },
  { value: 'cheerleader', label: '啦啦队', emoji: '📣' },
  { value: 'skirt', label: '短裙', emoji: '👗' },
  { value: 'tennis', label: '网球装', emoji: '🎾' },
  { value: 'soccer', label: '足球装', emoji: '⚽' },
  { value: 'swimsuit', label: '泳装', emoji: '🩱' },
  { value: 'lab-coat', label: '实验室白大褂', emoji: '🔬' },
  { value: 'santa-claus', label: '圣诞装', emoji: '🎅' },
  { value: 'sailor', label: '水手服', emoji: '⚓' },
  { value: 'pyjamas', label: '睡衣', emoji: '🛌' },
  { value: 'sport', label: '运动装', emoji: '🏃‍♀️' },
  { value: 'fancy-dress', label: '礼服', emoji: '✨' },
  { value: 'carnival', label: '嘉年华装', emoji: '🎭' },
  { value: 'wedding-dress', label: '婚纱', emoji: '👰' },
  { value: 'flight-attendant', label: '空姐制服', emoji: '✈️' },
  { value: 'clown', label: '小丑装', emoji: '🤡' },
  { value: 'jeans', label: '牛仔裤', emoji: '👖' },
  { value: 'latex', label: '乳胶装', emoji: '✨' },
  { value: 'princess-dress', label: '公主裙', emoji: '👸' },
  { value: 'corset', label: '束腰', emoji: '⌛' },
  { value: 'leggings', label: '紧身裤', emoji: '🦵' },
  { value: 'yoga-outfit', label: '瑜伽服', emoji: '🧘‍♀️' },
  { value: 'lingerie', label: '内衣', emoji: '👙' },
  { value: 'summer-dress', label: '夏装', emoji: '☀️' },
  { value: 'school-outfit', label: '校服', emoji: '🏫' },
  { value: 'witch', label: '女巫装', emoji: '🧙‍♀️' },
  { value: 'leather-outfit', label: '皮衣', emoji: '🖤' },
  { value: 'maid', label: '女仆装', emoji: '🧹' },
  { value: 'medieval-armor', label: '中世纪盔甲', emoji: '⚔️' },
  { value: 'cowboy', label: '牛仔装', emoji: '🤠' },
  { value: 'viking', label: '维京装', emoji: '🪓' },
  { value: 'casual', label: '休闲装', emoji: '👚' },
  { value: 'nurse', label: '护士装', emoji: '⚕️' },
  { value: 'police', label: '警察制服', emoji: '👮‍♀️' },
  { value: 'steampunk', label: '蒸汽朋克', emoji: '⚙️' },
  { value: 'superhero', label: '超级英雄', emoji: '🦸‍♀️' },
  { value: 'teacher', label: '教师装', emoji: '📚' },
  { value: 'firefighter', label: '消防员', emoji: '🧯' },
  { value: 'military', label: '军装', emoji: '🪖' },
  { value: 'construction', label: '建筑工人', emoji: '👷‍♀️' },
  { value: 'long-coat', label: '长外套', emoji: '🧥' },
  { value: 'ninja', label: '忍者装', emoji: '🥷' },
  { value: 'angel', label: '天使装', emoji: '👼' },
  { value: 'barista', label: '咖啡师', emoji: '☕' },
  { value: 'belly-dancer', label: '肚皮舞者', emoji: '💃' },
  { value: 'goth', label: '哥特风', emoji: '🖤' },
  { value: 'pirate', label: '海盗装', emoji: '🏴‍☠️' },
  { value: 'prisoner', label: '囚犯装', emoji: '🔒' },
  { value: 'onesie', label: '连体睡衣', emoji: '🐼' },
  { value: 'tutu', label: '芭蕾舞裙', emoji: '🩰' },
  { value: 'poncho', label: '雨披', emoji: '🌧️' },
  { value: 'hiphop', label: '嘻哈风', emoji: '🎤' },
  { value: 'skatepark', label: '滑板装', emoji: '🛹' },
  { value: 'adventurer', label: '冒险者', emoji: '🧭' },
  { value: 'lumberjack', label: '伐木工', emoji: '🪓' },
  { value: 'golf', label: '高尔夫装', emoji: '⛳' },
  { value: 'hijab', label: '头巾', emoji: '🧕' },
  { value: 'oversized-shirt', label: '宽松衬衫', emoji: '👕' },
  { value: 'tribal', label: '部落装', emoji: '🏵️' },
  { value: 'secretary', label: '秘书装', emoji: '💼' },
  { value: 'pop-star', label: '流行歌手', emoji: '🎵' },
  { value: 'hoodie', label: '连帽衫', emoji: '👕' },
  { value: 'race-driver', label: '赛车手', emoji: '🏎️' }
]

// 男性服装选项
const maleClothingOptions = [
  { value: 'formal', label: '正装', emoji: '🕴️' },
  { value: 'casual', label: '休闲装', emoji: '👕' },
  { value: 'sportswear', label: '运动装', emoji: '🏃‍♂️' },
  { value: 'uniform', label: '制服', emoji: '👮‍♂️' },
  { value: 'swimwear', label: '泳装', emoji: '🩳' },
  { value: 'sleepwear', label: '睡衣', emoji: '🛌' },
  { value: 'business', label: '商务装', emoji: '💼' },
  { value: 'traditional', label: '传统服饰', emoji: '👘' },
  { value: 'cosplay', label: '角色扮演服', emoji: '🦸‍♂️' },
  { value: 'military', label: '军装', emoji: '🎖️' },
  { value: 'work', label: '工作服', emoji: '🔧' },
  { value: 'none', label: '裸体', emoji: '🧖‍♂️' }
]

// 默认声音选项（作为fallback）
const defaultVoiceOptions: VoiceOption[] = [
  {
    id: 'soft',
    value: 'soft',
    label: '柔和的',
    description: '轻柔细腻的嗓音',
    emoji: '🕊️',
    gender: 'female',
    isPremium: false
  },
  {
    id: 'deep',
    value: 'deep',
    label: '低沉的',
    description: '深沉有力的声线',
    emoji: '🔊',
    gender: 'male',
    isPremium: false
  },
  {
    id: 'sweet',
    value: 'sweet',
    label: '甜美的',
    description: '甜甜的如糖果般的声音',
    emoji: '🍬',
    gender: 'female',
    isPremium: false
  },
  {
    id: 'husky',
    value: 'husky',
    label: '沙哑的',
    description: '略带沙哑的迷人声线',
    emoji: '🥃',
    gender: 'male',
    isPremium: false
  },
  {
    id: 'energetic',
    value: 'energetic',
    label: '有活力的',
    description: '充满朝气和激情的声音',
    emoji: '⚡',
    gender: 'female',
    isPremium: false
  },
  {
    id: 'seductive',
    value: 'seductive',
    label: '诱惑的',
    description: '性感魅惑的声线',
    emoji: '💋',
    gender: 'female',
    isPremium: true
  },
  {
    id: 'childish',
    value: 'childish',
    label: '稚嫩的',
    description: '带有天真感的童声',
    emoji: '🧸',
    gender: 'female',
    isPremium: false
  },
  {
    id: 'mature',
    value: 'mature',
    label: '成熟的',
    description: '稳重老练的声调',
    emoji: '🍷',
    gender: 'male',
    isPremium: false
  }
]

interface ClothingVoiceProps {
  data: CharacterData
  onUpdate: (data: Partial<CharacterData>) => void
}

export default function ClothingVoice({ data, onUpdate }: ClothingVoiceProps) {
  // 声音数据状态
  const [voiceOptions, setVoiceOptions] = React.useState<VoiceOption[]>(defaultVoiceOptions)
  const [isLoadingVoices, setIsLoadingVoices] = React.useState(false)
  const [voiceError, setVoiceError] = React.useState<string | null>(null)

  // 音频播放状态
  const [playingVoice, setPlayingVoice] = React.useState<string | null>(null)
  const audioRef = React.useRef<HTMLAudioElement | null>(null)

  // 直接使用gender字段，如果没有则默认为female
  const gender = React.useMemo(() => {
    return (data.gender as 'male' | 'female') || 'female'
  }, [data.gender])

  const clothingOptions = gender === 'female' ? femaleClothingOptions : maleClothingOptions

  // 使用Zustand store获取声音数据
  const { isLoading: storeIsLoading, error: storeError } = useVoiceModelsData()
  const { fetchVoiceModelsByGender, getVoiceOptions } = useVoiceModelsStore()

  // 加载声音数据
  React.useEffect(() => {
    const loadVoiceData = async () => {
      setIsLoadingVoices(true)
      setVoiceError(null)

      try {
        // 使用Zustand store根据性别获取声音模型
        await fetchVoiceModelsByGender(gender)

        // 获取转换后的声音选项
        const options = getVoiceOptions(gender)

        if (options.length > 0) {
          setVoiceOptions(options)
        } else {
          setVoiceOptions(defaultVoiceOptions)
        }
      } catch (error) {
        console.error('加载声音数据失败:', error)
        setVoiceError('加载声音数据失败，使用默认选项')
        setVoiceOptions(defaultVoiceOptions)
      } finally {
        setIsLoadingVoices(false)
      }
    }

    loadVoiceData()
  }, [gender, fetchVoiceModelsByGender, getVoiceOptions])

  // 播放示例音频并选择声音
  const handleVoiceCardClick = (option: VoiceOption) => {
    // 选择声音 - 同时更新voice描述和voiceModelId
    onUpdate({
      voice: option.label, // 使用显示名称作为描述
      voiceModelId: option.id // 使用数据库ID作为模型ID
    })

    // 如果正在播放同一个声音，则停止播放
    if (playingVoice === option.value) {
      stopAudio()
      return
    }

    // 停止当前播放的音频
    stopAudio()

    // 如果有示例音频，播放它
    if (option.defaultSample?.audioUrl) {
      const audio = new Audio(option.defaultSample.audioUrl)
      audioRef.current = audio
      setPlayingVoice(option.value)

      audio.addEventListener('ended', () => {
        setPlayingVoice(null)
      })

      audio.addEventListener('error', error => {
        console.error('音频播放失败:', error)
        setPlayingVoice(null)
      })

      audio.play().catch(error => {
        console.error('音频播放失败:', error)
        setPlayingVoice(null)
      })
    }
  }

  // 停止音频播放
  const stopAudio = () => {
    if (audioRef.current) {
      audioRef.current.pause()
      audioRef.current = null
    }
    setPlayingVoice(null)
  }

  // 组件卸载时停止音频
  React.useEffect(() => {
    return () => {
      stopAudio()
    }
  }, [])

  // 分页相关
  const [clothingPage, setClothingPage] = React.useState(0)
  const itemsPerPage = 16
  const totalClothingPages = Math.ceil(clothingOptions.length / itemsPerPage)

  const currentClothingOptions = clothingOptions.slice(
    clothingPage * itemsPerPage,
    (clothingPage + 1) * itemsPerPage
  )

  const nextClothingPage = () => {
    setClothingPage(current => (current + 1) % totalClothingPages)
  }

  const prevClothingPage = () => {
    setClothingPage(current => (current - 1 + totalClothingPages) % totalClothingPages)
  }

  return (
    <div className="space-y-4">
      {/* 服装选择 */}
      <Section title="选择服装">
        <div className="flex justify-between items-center mb-3">
          {totalClothingPages > 1 && (
            <div className="text-xs font-medium">
              {clothingPage + 1}/{totalClothingPages}
            </div>
          )}
        </div>

        <div className="grid grid-cols-4 gap-2" role="radiogroup" aria-label="选择服装">
          {currentClothingOptions.map(option => (
            <Card
              key={option.value}
              isPressable
              isHoverable
              onPress={() => onUpdate({ clothing: option.value })}
              className={`
                cursor-pointer transition-all duration-200
                ${
                  data.clothing === option.value
                    ? 'ring-2 ring-primary ring-offset-2 ring-offset-background bg-primary/5'
                    : ''
                }
              `}
              role="radio"
              aria-checked={data.clothing === option.value}
              aria-label={`选择${option.label}服装`}
            >
              <CardBody className="p-2 text-center">
                <div className="text-lg mb-1">{option.emoji}</div>
                <div className="text-xs font-medium text-foreground">{option.label}</div>
              </CardBody>
            </Card>
          ))}
        </div>

        {/* 分页按钮 */}
        {totalClothingPages > 1 && (
          <div className="flex justify-between mt-2">
            <Button size="sm" variant="bordered" onPress={prevClothingPage}>
              上一页
            </Button>
            <Button size="sm" variant="bordered" onPress={nextClothingPage}>
              下一页
            </Button>
          </div>
        )}
      </Section>

      {/* 声音选择 */}
      <Section title="选择声音">
        {voiceError && <div className="text-xs text-warning mb-2 text-center">{voiceError}</div>}

        {isLoadingVoices ? (
          <div className="flex justify-center items-center py-8">
            <Spinner size="sm" />
            <span className="ml-2 text-sm text-default-500">加载声音数据中...</span>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-2" role="radiogroup" aria-label="选择声音">
            {voiceOptions
              .filter(option => option.gender === gender || option.gender === 'neutral')
              .map(option => (
                <Card
                  key={option.value}
                  isPressable
                  isHoverable
                  onPress={() => handleVoiceCardClick(option)}
                  className={`
                    cursor-pointer transition-all duration-200
                    ${
                      data.voiceModelId === option.id
                        ? 'ring-2 ring-primary ring-offset-2 ring-offset-background bg-primary/5'
                        : ''
                    }
                    ${option.isPremium ? 'border-warning/50' : ''}
                  `}
                  role="radio"
                  aria-checked={data.voiceModelId === option.id}
                  aria-label={`选择${option.label}声音`}
                >
                  <CardBody className="p-3">
                    <div className="flex items-center gap-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-1">
                          <div className="text-sm font-medium">{option.label}</div>
                          {/* {option.isPremium && (
                            <Chip
                              size="sm"
                              color="warning"
                              variant="flat"
                              className="text-[8px] h-4 min-h-4"
                            >
                              高级
                            </Chip>
                          )} */}
                        </div>
                        <div className="text-xs text-default-500 line-clamp-1 mt-1">
                          {option.description}
                        </div>
                        {option.tags && option.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {option.tags.slice(0, 2).map(tag => (
                              <Chip
                                key={tag}
                                size="sm"
                                color="secondary"
                                variant="flat"
                                className="text-[10px] h-4 min-h-4 mt-1"
                              >
                                {tag}
                              </Chip>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardBody>
                </Card>
              ))}
          </div>
        )}
      </Section>
    </div>
  )
}

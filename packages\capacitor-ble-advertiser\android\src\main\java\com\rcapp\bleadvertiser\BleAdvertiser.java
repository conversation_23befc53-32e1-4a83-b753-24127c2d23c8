package com.rcapp.bleadvertiser;

import android.Manifest;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothManager;
import android.bluetooth.le.AdvertiseCallback;
import android.bluetooth.le.AdvertiseData;
import android.bluetooth.le.AdvertiseSettings;
import android.content.Context;
import android.os.Build;
import android.os.ParcelUuid;
import android.util.Log;

import com.getcapacitor.JSArray;
import com.getcapacitor.JSObject;
import com.getcapacitor.PermissionState;
import com.getcapacitor.Plugin;
import com.getcapacitor.PluginCall;
import com.getcapacitor.PluginMethod;
import com.getcapacitor.annotation.CapacitorPlugin;
import com.getcapacitor.annotation.Permission;
import com.getcapacitor.annotation.PermissionCallback;

import org.json.JSONArray;
import org.json.JSONException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CapacitorPlugin(
    name = "BleAdvertiser",
    permissions = {
        @Permission(strings = { Manifest.permission.BLUETOOTH_CONNECT }, alias = BleAdvertiser.BLUETOOTH_CONNECT_ALIAS),
        @Permission(strings = { Manifest.permission.BLUETOOTH_ADVERTISE }, alias = BleAdvertiser.BLUETOOTH_ADVERTISE_ALIAS),
        // BLUETOOTH_SCAN is often needed for discovery, but not directly for advertising.
        // Android S (API 31) also splits BLUETOOTH permission into BLUETOOTH_SCAN, BLUETOOTH_ADVERTISE, BLUETOOTH_CONNECT
        // Old BLUETOOTH and BLUETOOTH_ADMIN are for older APIs or if targetSdk < 31
    }
)
public class BleAdvertiser extends Plugin {
    // 日志标签
    private static final String TAG = "BleAdvertiser";
    
    // FADE 服务 UUID，与iOS版本对齐
    private static final ParcelUuid SERVICE_UUID_FADE = ParcelUuid.fromString("0000FADE-0000-1000-8000-00805F9B34FB");
    
    // 蓝牙相关对象
    private BluetoothAdapter bluetoothAdapter;
    private android.bluetooth.le.BluetoothLeAdvertiser bluetoothLeAdvertiser;
    
    // 广播回调存储
    private final HashMap<Integer, AdvertiseCallback> advertiseCallbacks = new HashMap<>();

    // 权限别名常量
    static final String BLUETOOTH_CONNECT_ALIAS = "bluetoothConnect";
    static final String BLUETOOTH_ADVERTISE_ALIAS = "bluetoothAdvertise";

    private static final String INITIALIZE_PERMISSION_CALLBACK = "initializePermissionCallback";
    private static final String IS_BLUETOOTH_ENABLED_PERMISSION_CALLBACK = "isBluetoothEnabledPermissionCallback";
    private static final String START_ADVERTISING_PERMISSION_CALLBACK = "startAdvertisingPermissionCallback";

    /**
     * 插件初始化
     */
    @Override
    public void load() {
        super.load();
        Log.d(TAG, "插件已加载");
    }

    /**
     * 初始化蓝牙服务
     */
    @PluginMethod
    public void initialize(PluginCall call) {
        Log.d(TAG, "请求初始化蓝牙服务");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PermissionState connectState = getPermissionState(BLUETOOTH_CONNECT_ALIAS);
            if (connectState != PermissionState.GRANTED) {
                requestPermissionForAlias(BLUETOOTH_CONNECT_ALIAS, call, INITIALIZE_PERMISSION_CALLBACK);
            } else {
                _initialize(call);
            }
        } else {
            _initialize(call);
        }
    }

    @PermissionCallback
    private void initializePermissionCallback(PluginCall call) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PermissionState connectState = getPermissionState(BLUETOOTH_CONNECT_ALIAS);
            if (connectState == PermissionState.GRANTED) {
                _initialize(call);
            } else {
                Log.e(TAG, "BLUETOOTH_CONNECT 权限被拒绝");
                call.reject("Bluetooth connect permission is required to initialize.");
            }
        }
    }

    private void _initialize(PluginCall call) {
        try {
            BluetoothManager bluetoothManager = (BluetoothManager) getContext().getSystemService(Context.BLUETOOTH_SERVICE);
            if (bluetoothManager == null) {
                Log.e(TAG, "BluetoothManager 不可用");
                call.resolve(createErrorResult("BluetoothManager 不可用"));
                return;
            }
            bluetoothAdapter = bluetoothManager.getAdapter();

            if (bluetoothAdapter == null) {
                Log.e(TAG, "蓝牙适配器不可用");
                call.resolve(createErrorResult("蓝牙适配器不可用"));
                return;
            }
            Log.d(TAG, "蓝牙服务核心组件初始化成功");
            JSObject result = new JSObject();
            result.put("success", true);
            call.resolve(result);
        } catch (SecurityException se) {
            Log.e(TAG, "初始化时发生安全异常", se);
            call.resolve(createErrorResult("初始化失败，权限不足: " + se.getMessage()));
        }
        catch (Exception e) {
            Log.e(TAG, "初始化失败", e);
            call.resolve(createErrorResult("初始化失败: " + e.getMessage()));
        }
    }

    /**
     * 检查蓝牙是否启用
     */
    @PluginMethod
    public void isBluetoothEnabled(PluginCall call) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PermissionState connectState = getPermissionState(BLUETOOTH_CONNECT_ALIAS);
            if (connectState != PermissionState.GRANTED) {
                requestPermissionForAlias(BLUETOOTH_CONNECT_ALIAS, call, IS_BLUETOOTH_ENABLED_PERMISSION_CALLBACK);
            } else {
                _isBluetoothEnabled(call);
            }
        } else {
            _isBluetoothEnabled(call);
        }
    }

    @PermissionCallback
    private void isBluetoothEnabledPermissionCallback(PluginCall call) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PermissionState connectState = getPermissionState(BLUETOOTH_CONNECT_ALIAS);
            if (connectState == PermissionState.GRANTED) {
                _isBluetoothEnabled(call);
            } else {
                Log.w(TAG, "BLUETOOTH_CONNECT 权限被拒绝，返回 enabled: false");
                JSObject result = new JSObject();
                result.put("enabled", false);
                result.put("message", "Bluetooth connect permission not granted.");
                call.resolve(result);
            }
        }
    }

    private void _isBluetoothEnabled(PluginCall call) {
        try {
            if (bluetoothAdapter == null) {
                BluetoothManager bluetoothManager = (BluetoothManager) getContext().getSystemService(Context.BLUETOOTH_SERVICE);
                if (bluetoothManager != null) {
                    bluetoothAdapter = bluetoothManager.getAdapter();
                }
            }
            
            boolean enabled = bluetoothAdapter != null && bluetoothAdapter.isEnabled();
            
            JSObject result = new JSObject();
            result.put("enabled", enabled);
            call.resolve(result);
        } catch (SecurityException se) {
            Log.e(TAG, "检查蓝牙状态时发生安全异常", se);
            JSObject result = new JSObject();
            result.put("enabled", false);
            result.put("message", "Permission denied during check: " + se.getMessage());
            call.resolve(result);
        }
        catch (Exception e) {
            Log.e(TAG, "检查蓝牙状态失败", e);
            JSObject result = new JSObject();
            result.put("enabled", false);
            call.resolve(result);
        }
    }

    /**
     * 开始广播
     */
    @PluginMethod
    public void startAdvertising(PluginCall call) {
        Log.d(TAG, "请求开始广播");
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PermissionState connectState = getPermissionState(BLUETOOTH_CONNECT_ALIAS);
            PermissionState advertiseState = getPermissionState(BLUETOOTH_ADVERTISE_ALIAS);
            Log.d(TAG, "权限状态: BLUETOOTH_CONNECT=" + connectState + ", BLUETOOTH_ADVERTISE=" + advertiseState);

            if (connectState != PermissionState.GRANTED || advertiseState != PermissionState.GRANTED) {
                List<String> aliasesToRequest = new ArrayList<>();
                if (connectState != PermissionState.GRANTED) aliasesToRequest.add(BLUETOOTH_CONNECT_ALIAS);
                if (advertiseState != PermissionState.GRANTED) aliasesToRequest.add(BLUETOOTH_ADVERTISE_ALIAS);
                requestPermissionForAliases(aliasesToRequest.toArray(new String[0]), call, START_ADVERTISING_PERMISSION_CALLBACK);
            } else {
                _startAdvertising(call);
            }
        } else {
            _startAdvertising(call);
        }
    }

    @PermissionCallback
    private void startAdvertisingPermissionCallback(PluginCall call) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            PermissionState connectState = getPermissionState(BLUETOOTH_CONNECT_ALIAS);
            PermissionState advertiseState = getPermissionState(BLUETOOTH_ADVERTISE_ALIAS);

            if (connectState == PermissionState.GRANTED && advertiseState == PermissionState.GRANTED) {
                _startAdvertising(call);
            } else {
                Log.e(TAG, "蓝牙广播权限被拒绝");
                call.reject("Bluetooth advertise or connect permission was denied.");
            }
        }
    }

    private void _startAdvertising(PluginCall call) {
        JSObject options = call.getData();
        if (options == null) {
            Log.e(TAG, "调用参数为空");
            call.reject("调用参数 (options) 为空");
            return;
        }

        int mode = options.getInteger("mode", 2);
        int manufacturerId = options.getInteger("manufacturerId", 255);
        int instanceId = options.getInteger("instanceId", (int)(System.currentTimeMillis() % 10000));
        
        // 获取数据数组
        byte[] dataBytes = null;
        try {
            JSONArray dataArray = options.getJSONArray("data");
            if (dataArray != null && dataArray.length() > 0) {
                dataBytes = new byte[dataArray.length()];
                for (int i = 0; i < dataArray.length(); i++) {
                    int value = dataArray.getInt(i);
                    dataBytes[i] = (byte) value;
                }
                Log.d(TAG, "成功获取数据，长度: " + dataBytes.length);
            }
        } catch (Exception e) {
            Log.e(TAG, "获取或解析数据数组时出错", e);
            call.reject("数据解析失败: " + e.getMessage());
            return;
        }

        Log.d(TAG, "广播参数: mode=" + mode + ", manufacturerId=" + manufacturerId + ", instanceId=" + instanceId);
        
        // 检查数据是否有效
        if (dataBytes == null || dataBytes.length == 0) {
            Log.e(TAG, "数据为空或无效");
            call.reject("广播数据为空或无效");
            return;
        }
        
        // 打印无符号值用于调试
        StringBuilder unsignedValues = new StringBuilder("[");
        for (int i = 0; i < dataBytes.length; i++) {
            unsignedValues.append(dataBytes[i] & 0xFF);
            if (i < dataBytes.length - 1) unsignedValues.append(", ");
        }
        unsignedValues.append("]");
        Log.d(TAG, "数据 (无符号): " + unsignedValues.toString());

        if (bluetoothAdapter == null || !bluetoothAdapter.isEnabled()) {
            Log.e(TAG, "蓝牙未启用或适配器为空");
            call.reject("蓝牙未启用或适配器为空");
            return;
        }

        try {
            if (bluetoothLeAdvertiser == null) {
                 bluetoothLeAdvertiser = bluetoothAdapter.getBluetoothLeAdvertiser();
            }
            if (bluetoothLeAdvertiser == null) {
                Log.e(TAG, "蓝牙广播器不可用");
                call.reject("蓝牙广播器不可用");
                return;
            }
        } catch (SecurityException se) {
             Log.e(TAG, "获取广播器时权限不足", se);
             call.reject("获取蓝牙广播器失败，权限不足: " + se.getMessage());
             return;
        }

        if (!advertiseCallbacks.isEmpty()) {
            Log.d(TAG, "当前存在 " + advertiseCallbacks.size() + " 个旧广播，正在停止...");
            for (AdvertiseCallback existingCallback : new ArrayList<>(advertiseCallbacks.values())) {
                try {
                    if (bluetoothLeAdvertiser != null) {
                         bluetoothLeAdvertiser.stopAdvertising(existingCallback);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "停止旧广播时出错", e);
                }
            }
            advertiseCallbacks.clear();
        }

        AdvertiseSettings.Builder settingsBuilder = new AdvertiseSettings.Builder();
        settingsBuilder.setConnectable(true);
        settingsBuilder.setTimeout(0);

        switch (mode) {
            case 0:
                settingsBuilder.setAdvertiseMode(AdvertiseSettings.ADVERTISE_MODE_BALANCED);
                settingsBuilder.setTxPowerLevel(AdvertiseSettings.ADVERTISE_TX_POWER_MEDIUM);
                break;
            case 1:
                settingsBuilder.setAdvertiseMode(AdvertiseSettings.ADVERTISE_MODE_LOW_LATENCY);
                settingsBuilder.setTxPowerLevel(AdvertiseSettings.ADVERTISE_TX_POWER_HIGH);
                break;
            case 2:
            default:
                settingsBuilder.setAdvertiseMode(AdvertiseSettings.ADVERTISE_MODE_LOW_LATENCY);
                settingsBuilder.setTxPowerLevel(AdvertiseSettings.ADVERTISE_TX_POWER_HIGH);
                break;
        }
        AdvertiseSettings settings = settingsBuilder.build();

        AdvertiseData advertiseData = new AdvertiseData.Builder()
                .setIncludeDeviceName(false)
                .setIncludeTxPowerLevel(false)
                .addManufacturerData(manufacturerId, dataBytes)
                .build();
        
        if (advertiseData.getManufacturerSpecificData() != null && 
            advertiseData.getManufacturerSpecificData().get(manufacturerId) != null) {
            byte[] manuData = advertiseData.getManufacturerSpecificData().get(manufacturerId);
            if (manuData.length < 50) {
                 Log.d(TAG, "广播数据 (Hex): " + bytesToHex(manuData));
            }
        }

        final PluginCall savedCall = call;
        final int finalInstanceId = instanceId;

        AdvertiseCallback callback = new AdvertiseCallback() {
            @Override
            public void onStartSuccess(AdvertiseSettings settingsInEffect) {
                Log.i(TAG, "广播开始成功，实例ID: " + finalInstanceId);
                JSObject result = new JSObject();
                result.put("success", true);
                result.put("instanceId", finalInstanceId);
                savedCall.resolve(result);
            }

            @Override
            public void onStartFailure(int errorCode) {
                String errorMsg = translateAdvertiseErrorCode(errorCode);
                Log.e(TAG, "广播开始失败，错误码: " + errorCode + " (" + errorMsg + ")");
                advertiseCallbacks.remove(finalInstanceId);
                JSObject result = new JSObject();
                result.put("success", false);
                result.put("instanceId", finalInstanceId);
                result.put("errorCode", errorCode);
                result.put("message", "广播启动失败: " + errorMsg);
                savedCall.resolve(result);
            }
        };

        advertiseCallbacks.put(instanceId, callback);

        try {
            bluetoothLeAdvertiser.startAdvertising(settings, advertiseData, callback);
            Log.i(TAG, "已请求开始广播，实例ID: " + instanceId);
        } catch (SecurityException se) {
            Log.e(TAG, "广播失败，权限不足", se);
            advertiseCallbacks.remove(instanceId);
            call.reject("广播失败，权限不足: " + se.getMessage());
        } catch (IllegalStateException ise) {
            Log.e(TAG, "广播失败，蓝牙可能已关闭", ise);
            advertiseCallbacks.remove(instanceId);
            call.reject("广播失败，蓝牙可能已关闭: " + ise.getMessage());
        }
         catch (Exception e) {
            Log.e(TAG, "广播失败，发生未知异常", e);
            advertiseCallbacks.remove(instanceId);
            call.reject("广播失败: " + e.getMessage());
        }
    }

    /**
     * 停止广播
     */
    @PluginMethod
    public void stopAdvertising(PluginCall call) {
        Integer instanceId = call.getInt("instanceId");
        Log.d(TAG, "请求停止广播, 实例ID: " + (instanceId == null ? "(全部)" : instanceId));

        if (instanceId == null) {
            _stopAllAdvertising(call, "无实例ID");
            return;
        }

        if (bluetoothAdapter == null || bluetoothLeAdvertiser == null) {
            Log.e(TAG, "蓝牙未初始化或广播器不可用");
            call.resolve(createErrorResult("蓝牙未初始化或广播器不可用"));
            return;
        }

        try {
            if (advertiseCallbacks.containsKey(instanceId)) {
                AdvertiseCallback callbackToStop = advertiseCallbacks.get(instanceId);
                
                if (callbackToStop != null) {
                    Log.d(TAG, "找到广播回调，实例ID: " + instanceId);
                    
                    try {
                        bluetoothLeAdvertiser.stopAdvertising(callbackToStop);
                        Log.i(TAG, "已发送停止广播请求, 实例ID: " + instanceId);
                    } catch (Exception e) {
                        Log.e(TAG, "停止广播时出错", e);
                        // 即使停止失败，也清理回调，防止内存泄漏
                    }
                    
                    // 无论停止成功与否，都从映射中移除
                    advertiseCallbacks.remove(instanceId);
                    Log.d(TAG, "已移除广播回调, 当前回调数: " + advertiseCallbacks.size());
                    
                    JSObject result = new JSObject();
                    result.put("success", true);
                    call.resolve(result);
                } else {
                    Log.w(TAG, "回调对象为空, 实例ID: " + instanceId);
                    advertiseCallbacks.remove(instanceId); // 移除无效的回调
                    call.resolve(createErrorResult("回调对象为空，已清理: " + instanceId));
                }
            } else {
                Log.w(TAG, "找不到广播实例: " + instanceId);
                call.resolve(createErrorResult("找不到广播实例: " + instanceId));
            }
        } catch (SecurityException se) {
            Log.e(TAG, "停止广播时发生安全异常", se);
            advertiseCallbacks.remove(instanceId); // 尝试清理
            call.resolve(createErrorResult("停止广播失败，权限不足: " + se.getMessage()));
        } catch (IllegalStateException ise) {
            Log.e(TAG, "停止广播时发生非法状态异常", ise);
            advertiseCallbacks.remove(instanceId); // 尝试清理
            call.resolve(createErrorResult("停止广播失败，蓝牙可能已关闭: " + ise.getMessage()));
        }
        catch (Exception e) {
            Log.e(TAG, "停止广播失败", e);
            advertiseCallbacks.remove(instanceId); // 尝试清理
            call.resolve(createErrorResult("停止广播失败: " + e.getMessage()));
        }
    }

    /**
     * 停止所有广播
     */
    @PluginMethod
    public void stopAllAdvertising(PluginCall call) {
        Log.d(TAG, "请求停止所有广播");
        _stopAllAdvertising(call, "直接调用");
    }

    private void _stopAllAdvertising(PluginCall call, String contextMessage) {
        Log.d(TAG, "开始停止所有广播. 上下文: " + contextMessage);
        if (bluetoothAdapter == null || bluetoothLeAdvertiser == null) {
             Log.e(TAG, "蓝牙未初始化或广播器不可用");
            call.resolve(createErrorResult("蓝牙未初始化或广播器不可用"));
            return;
        }

        if (advertiseCallbacks.isEmpty()) {
            Log.i(TAG, "没有正在进行的广播可停止");
            JSObject result = new JSObject();
            result.put("success", true);
            result.put("message", "No active advertisements to stop.");
            call.resolve(result);
            return;
        }

        Log.d(TAG, "当前有 " + advertiseCallbacks.size() + " 个广播回调，正在停止...");
        try {
            HashMap<Integer, AdvertiseCallback> callbacksCopy = new HashMap<>(advertiseCallbacks);
            boolean allStoppedSuccessfully = true;
            
            for (Map.Entry<Integer, AdvertiseCallback> entry : callbacksCopy.entrySet()) {
                Integer id = entry.getKey();
                AdvertiseCallback cb = entry.getValue();
                
                if (cb == null) {
                    Log.w(TAG, "回调对象为空, 实例ID: " + id);
                    advertiseCallbacks.remove(id);
                    continue;
                }
                
                try {
                    bluetoothLeAdvertiser.stopAdvertising(cb);
                    Log.i(TAG, "已发送停止广播请求, 实例ID: " + id);
                } catch (Exception e) {
                    Log.e(TAG, "停止广播失败, 实例ID: " + id, e);
                    allStoppedSuccessfully = false;
                }
                
                // 无论是否成功，都从映射中移除
                advertiseCallbacks.remove(id);
            }

            // 确保所有回调都被清除
            if (!advertiseCallbacks.isEmpty()) {
                Log.w(TAG, "清理后仍有残留回调，强制清空");
                advertiseCallbacks.clear();
            }
            
            Log.i(TAG, "所有广播回调已清除，结果: " + (allStoppedSuccessfully ? "全部成功" : "部分失败"));

            JSObject result = new JSObject();
            result.put("success", allStoppedSuccessfully);
            call.resolve(result);
        } catch (Exception e) {
            Log.e(TAG, "停止所有广播时出错", e);
            // 确保清理所有回调
            advertiseCallbacks.clear();
            call.resolve(createErrorResult("停止所有广播失败: " + e.getMessage()));
        }
    }

    /**
     * 创建错误结果对象
     */
    private JSObject createErrorResult(String message) {
        JSObject result = new JSObject();
        result.put("success", false);
        result.put("message", message);
        return result;
    }

    /**
     * 将 AdvertiseCallback 的错误码转换为可读字符串
     */
    private String translateAdvertiseErrorCode(int errorCode) {
        switch (errorCode) {
            case AdvertiseCallback.ADVERTISE_FAILED_ALREADY_STARTED:
                return "ADVERTISE_FAILED_ALREADY_STARTED";
            case AdvertiseCallback.ADVERTISE_FAILED_DATA_TOO_LARGE:
                return "ADVERTISE_FAILED_DATA_TOO_LARGE";
            case AdvertiseCallback.ADVERTISE_FAILED_FEATURE_UNSUPPORTED:
                return "ADVERTISE_FAILED_FEATURE_UNSUPPORTED";
            case AdvertiseCallback.ADVERTISE_FAILED_INTERNAL_ERROR:
                return "ADVERTISE_FAILED_INTERNAL_ERROR";
            case AdvertiseCallback.ADVERTISE_FAILED_TOO_MANY_ADVERTISERS:
                return "ADVERTISE_FAILED_TOO_MANY_ADVERTISERS";
            default:
                return "UNKNOWN_ERROR_CODE_" + errorCode;
        }
    }

    /**
     * 辅助方法：将字节数组转换为十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        if (bytes == null) return "null";
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X ", b));
        }
        return sb.toString().trim();
    }
} 
import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { Env } from '@/types/env';

// ==================== 系统配置管理 ====================

/**
 * 获取系统配置值
 */
export async function getSystemConfig(env: Env, key: string): Promise<string | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.systemConfig)
    .select('value')
    .eq('key', key)
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data?.value || null;
}

/**
 * 获取多个系统配置值
 */
export async function getSystemConfigs(env: Env, keys: string[]): Promise<Record<string, string>> {
  const supabase = getSupabase(env);

  const result = await supabase.from(TABLE_NAMES.systemConfig).select('key, value').in('key', keys);

  const { data } = handleSupabaseResult(result);

  const configs: Record<string, string> = {};
  if (data) {
    data.forEach((item: any) => {
      configs[item.key] = item.value;
    });
  }

  return configs;
}

/**
 * 获取佣金比例配置
 */
export async function getCommissionRates(env: Env): Promise<{
  membershipRate: number;
  pointsRate: number;
}> {
  const configs = await getSystemConfigs(env, [
    'COMMISSION_RATE_MEMBERSHIP',
    'COMMISSION_RATE_POINTS',
  ]);

  return {
    membershipRate: Number.parseFloat(configs.COMMISSION_RATE_MEMBERSHIP || '0.05'),
    pointsRate: Number.parseFloat(configs.COMMISSION_RATE_POINTS || '0.10'),
  };
}

/**
 * 获取提现配置
 */
export async function getWithdrawConfig(env: Env): Promise<{
  minAmount: number;
  feeRate: number;
}> {
  const configs = await getSystemConfigs(env, ['MIN_WITHDRAW_AMOUNT', 'WITHDRAW_FEE_RATE']);

  return {
    minAmount: Number.parseFloat(configs.MIN_WITHDRAW_AMOUNT || '100'),
    feeRate: Number.parseFloat(configs.WITHDRAW_FEE_RATE || '0.03'),
  };
}

/**
 * 计算佣金金额
 */
export function calculateCommissionAmount(
  sourceAmount: number,
  sourceType: 'membership' | 'points_package',
  rates: { membershipRate: number; pointsRate: number }
): number {
  const rate = sourceType === 'membership' ? rates.membershipRate : rates.pointsRate;
  return Math.round(sourceAmount * rate * 100) / 100;
}

/**
 * 计算提现手续费
 */
export function calculateWithdrawFee(
  amount: number,
  feeRate: number
): {
  feeAmount: number;
  actualAmount: number;
} {
  const feeAmount = Math.round(amount * feeRate * 100) / 100;
  const actualAmount = Math.round((amount - feeAmount) * 100) / 100;

  return {
    feeAmount,
    actualAmount,
  };
}

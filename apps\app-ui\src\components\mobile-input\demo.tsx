import { useState } from 'react'
import {
  KeyboardAdaptiveInput,
  KeyboardAdaptiveInputField,
  KeyboardAdaptiveTextarea
} from './keyboard-adaptive-input'
import { Card } from '../ui/card'
import { Label } from '../ui/label'

/**
 * KeyboardAdaptiveInput 组件演示页面
 * 用于测试和展示键盘适配输入组件的功能
 */
export function KeyboardAdaptiveInputDemo() {
  const [inputValue, setInputValue] = useState('')
  const [textareaValue, setTextareaValue] = useState('')
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })

  const handleInputSubmit = (value: string) => {
    console.log('Input 提交:', value)
    alert(`输入内容: ${value}`)
  }

  const handleTextareaSubmit = (value: string) => {
    console.log('Textarea 提交:', value)
    alert(`文本内容: ${value}`)
    setTextareaValue('') // 提交后清空
  }

  const handleFormSubmit = (field: string) => (value: string) => {
    console.log(`表单字段 ${field} 提交:`, value)
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div className="p-4 space-y-6 max-w-md mx-auto">
      <h1 className="text-2xl font-bold text-center">键盘适配输入组件演示</h1>

      {/* 基础 Input 示例 */}
      <Card className="p-4">
        <h2 className="text-lg font-semibold mb-3">基础 Input</h2>
        <div className="space-y-2">
          <Label htmlFor="basic-input">用户名</Label>
          <KeyboardAdaptiveInputField
            id="basic-input"
            placeholder="请输入用户名"
            value={inputValue}
            onChange={(e: any) => setInputValue(e.target.value)}
            onSubmit={handleInputSubmit}
          />
          <p className="text-sm text-gray-600">当前值: {inputValue}</p>
        </div>
      </Card>

      {/* 基础 Textarea 示例 */}
      <Card className="p-4">
        <h2 className="text-lg font-semibold mb-3">基础 Textarea</h2>
        <div className="space-y-2">
          <Label htmlFor="basic-textarea">评论内容</Label>
          <KeyboardAdaptiveTextarea
            id="basic-textarea"
            placeholder="写下你的评论..."
            value={textareaValue}
            onChange={(e: any) => setTextareaValue(e.target.value)}
            onSubmit={handleTextareaSubmit}
            rows={3}
          />
          <p className="text-sm text-gray-600">当前值: {textareaValue}</p>
        </div>
      </Card>

      {/* 表单示例 */}
      <Card className="p-4">
        <h2 className="text-lg font-semibold mb-3">表单示例</h2>
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="form-name">姓名</Label>
            <KeyboardAdaptiveInput
              type="input"
              id="form-name"
              placeholder="请输入姓名"
              value={formData.name}
              onChange={e => setFormData(prev => ({ ...prev, name: e.target.value }))}
              onSubmit={handleFormSubmit('name')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="form-email">邮箱</Label>
            <KeyboardAdaptiveInput
              type="input"
              id="form-email"
              placeholder="请输入邮箱"
              value={formData.email}
              onChange={e => setFormData(prev => ({ ...prev, email: e.target.value }))}
              onSubmit={handleFormSubmit('email')}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="form-message">留言</Label>
            <KeyboardAdaptiveInput
              type="textarea"
              id="form-message"
              placeholder="请输入留言内容..."
              value={formData.message}
              onChange={e => setFormData(prev => ({ ...prev, message: e.target.value }))}
              onSubmit={handleFormSubmit('message')}
              rows={4}
            />
          </div>
        </div>

        <div className="mt-4 p-3 bg-gray-50 rounded">
          <h3 className="font-medium mb-2">表单数据:</h3>
          <pre className="text-sm">{JSON.stringify(formData, null, 2)}</pre>
        </div>
      </Card>

      {/* 使用说明 */}
      <Card className="p-4">
        <h2 className="text-lg font-semibold mb-3">使用说明</h2>
        <div className="text-sm text-gray-600 space-y-2">
          <p>• 在移动设备上点击输入框，会在键盘上方显示浮动输入框</p>
          <p>• 在 Web 浏览器中，组件表现与普通输入框相同</p>
          <p>• 支持 "完成" 和 "发送" 两种按钮类型</p>
          <p>• 点击按钮后会触发 onSubmit 回调并同步内容</p>
          <p>• 支持受控和非受控两种使用模式</p>
        </div>
      </Card>
    </div>
  )
}

import type { Env } from '@/types/env'
import { createPointsService, type PointsConsumptionResult } from './points'
import { checkUserMembership } from '@/lib/db/queries/membership'
import { getCachedMembership } from '@/lib/cache/cache-utils'
import { getFeaturePointsCost } from './config'

/**
 * 服务积分扣除专用接口
 * 为各个具体服务提供便捷的积分扣除方法
 */

/**
 * 服务积分管理器类
 */
export class ServicePointsManagerClass {
  constructor(private env: Env) {}

  /**
   * TTS语音生成积分扣除
   */
  async consumeTTSPoints(
    userId: string,
    options: {
      textLength?: number
      voiceModel?: string
      taskId?: string
      customDescription?: string
    } = {}
  ): Promise<PointsConsumptionResult> {
    return consumePointsForTTS(this.env, userId, options)
  }

  /**
   * 图片生成积分扣除
   */
  async consumeImageGenerationPoints(
    userId: string,
    options: {
      imageCount?: number
      generationId?: string
      customDescription?: string
    } = {}
  ): Promise<PointsConsumptionResult> {
    return consumePointsForImageGeneration(this.env, userId, options)
  }

  /**
   * 检查聊天权限
   */
  async checkChatPermission(userId: string) {
    const membershipInfo = await checkUserMembership(this.env, userId)
    return {
      isMember: membershipInfo.isMember,
      hasPermission: true // 聊天功能基本都可用
    }
  }

  /**
   * 通用积分消费
   */
  async consumePoints(
    userId: string,
    options: {
      amount: number
      source: 'generation' | 'purchase' | 'subscription' | 'refund' | 'bonus' | 'admin'
      sourceId?: string
      description: string
    }
  ): Promise<PointsConsumptionResult> {
    const pointsService = createPointsService(this.env)
    return pointsService.consumePoints(
      userId,
      options.amount,
      options.source,
      options.sourceId,
      options.description
    )
  }

  /**
   * 退还积分
   */
  async refundPoints(
    userId: string,
    options: {
      amount: number
      source: string
      sourceId?: string
      description?: string
    }
  ): Promise<PointsConsumptionResult> {
    const pointsService = createPointsService(this.env)
    return pointsService.refundPoints(userId, options.amount, options.sourceId, options.description)
  }
}

/**
 * TTS语音生成积分扣除
 */
export async function consumePointsForTTS(
  env: Env,
  userId: string,
  options: {
    textLength?: number // 文本长度，可用于动态计算积分
    voiceModel?: string // 声音模型
    taskId?: string // TTS任务ID
    customDescription?: string
  } = {}
): Promise<PointsConsumptionResult> {
  const pointsService = createPointsService(env)

  // 检查会员状态（TTS需要会员权限）- 使用缓存版本
  const membershipInfo = await getCachedMembership(env, userId)
  if (!membershipInfo.isMember) {
    return {
      success: false,
      error: 'TTS语音生成功能仅限会员使用'
    }
  }

  // 从数据库获取基础积分消费
  let pointsCost = await getFeaturePointsCost(env, 'VOICE_GENERATION')
  if (options.textLength) {
    // 文本长度超过100字符，每100字符额外消费1积分
    const extraChars = Math.max(0, options.textLength - 100)
    const extraPoints = Math.ceil(extraChars / 100)
    pointsCost += extraPoints
  }

  const description =
    options.customDescription ||
    `TTS语音生成${options.textLength ? `(${options.textLength}字符)` : ''}消费${pointsCost}积分`

  return pointsService.consumePoints(userId, pointsCost, 'generation', options.taskId, description)
}

/**
 * 图片生成积分扣除
 */
export async function consumePointsForImageGeneration(
  env: Env,
  userId: string,
  options: {
    imageCount?: number // 生成图片数量
    isHighRes?: boolean // 是否高清生成
    templateId?: string // 模板ID
    generationId?: string // 生成任务ID
    customDescription?: string
  } = {}
): Promise<PointsConsumptionResult> {
  const pointsService = createPointsService(env)

  // 从数据库获取每张图片的积分消费
  const pointsPerImage = await getFeaturePointsCost(env, 'IMAGE_GENERATION')
  const totalPoints = pointsPerImage * (options.imageCount || 1)

  const description =
    options.customDescription ||
    `图片生成${options.imageCount ? `(${options.imageCount}张)` : ''}消费${totalPoints}积分`

  return pointsService.consumePoints(
    userId,
    totalPoints,
    'generation',
    options.generationId,
    description
  )
}

/**
 * 剧本购买积分扣除
 */
export async function consumePointsForScriptPurchase(
  env: Env,
  userId: string,
  options: {
    scriptId: string // 剧本ID (必需)
    scriptTitle?: string // 剧本标题
    customPointsCost?: number // 自定义积分消费
    customDescription?: string
  }
): Promise<PointsConsumptionResult> {
  const pointsService = createPointsService(env)

  // 检查会员状态（剧本购买需要会员权限）- 使用缓存版本
  const membershipInfo = await getCachedMembership(env, userId)
  if (!membershipInfo.isMember) {
    return {
      success: false,
      error: '剧本购买功能仅限会员使用'
    }
  }

  const pointsCost =
    options.customPointsCost || (await getFeaturePointsCost(env, 'SCRIPT_PURCHASE'))
  const description =
    options.customDescription ||
    `购买剧本${options.scriptTitle ? `《${options.scriptTitle}》` : ''}消费${pointsCost}积分`

  return pointsService.consumePoints(userId, pointsCost, 'purchase', options.scriptId, description)
}

/**
 * 视频生成积分扣除
 */
export async function consumePointsForVideoGeneration(
  env: Env,
  userId: string,
  options: {
    duration?: number // 视频时长（秒）
    quality?: 'standard' | 'hd' | '4k' // 视频质量
    generationId?: string // 生成任务ID
    customDescription?: string
  } = {}
): Promise<PointsConsumptionResult> {
  const pointsService = createPointsService(env)

  // 检查会员状态（视频生成需要会员权限）
  const membershipInfo = await checkUserMembership(env, userId)
  if (!membershipInfo.isMember) {
    return {
      success: false,
      error: '视频生成功能仅限会员使用'
    }
  }

  // 从数据库获取基础积分消费
  const pointsCost = await getFeaturePointsCost(env, 'VIDEO_GENERATION')

  // if (options.duration) {
  //   // 每10秒额外消费1积分
  //   const extraPoints = Math.ceil(options.duration / 10)
  //   pointsCost += extraPoints
  // }

  // if (options.quality === 'hd') {
  //   pointsCost += 10
  // } else if (options.quality === '4k') {
  //   pointsCost += 20
  // }

  const description =
    options.customDescription ||
    `视频生成${options.duration ? `(${options.duration}秒)` : ''}${
      options.quality ? `(${options.quality})` : ''
    }消费${pointsCost}积分`

  return pointsService.consumePoints(
    userId,
    pointsCost,
    'generation',
    options.generationId,
    description
  )
}

/**
 * 写真集生成积分扣除
 */
export async function consumePointsForGalleryGeneration(
  env: Env,
  userId: string,
  options: {
    characterId?: string // 角色ID
    characterName?: string // 角色名称
    imageCount?: number // 生成图片数量
    generationId?: string // 生成任务ID
    customDescription?: string
  } = {}
): Promise<PointsConsumptionResult> {
  const pointsService = createPointsService(env)

  // 检查会员状态（写真集生成需要会员权限）- 使用缓存版本
  const membershipInfo = await getCachedMembership(env, userId)
  if (!membershipInfo.isMember) {
    return {
      success: false,
      error: '写真集生成功能仅限会员使用'
    }
  }

  // 从数据库获取基础积分消费
  let pointsCost = await getFeaturePointsCost(env, 'GALLERY_GENERATION')
  if (options.imageCount && options.imageCount > 1) {
    pointsCost += (options.imageCount - 1) * 10 // 每额外一张图片增加10积分
  }

  const description =
    options.customDescription ||
    `为角色${options.characterName ? `《${options.characterName}》` : ''}生成写真集${
      options.imageCount ? `(${options.imageCount}张)` : ''
    }消费${pointsCost}积分`

  return pointsService.consumePoints(
    userId,
    pointsCost,
    'generation',
    options.generationId,
    description
  )
}

/**
 * 聊天对话积分检查（免费用户限制）
 */
export async function checkChatPermission(
  env: Env,
  userId: string,
  options: {
    messageCount?: number // 今日消息数量
    customLimit?: number // 自定义限制
  } = {}
): Promise<{ canChat: boolean; reason?: string; isMember: boolean }> {
  const membershipInfo = await getCachedMembership(env, userId)

  // 会员用户无限制
  if (membershipInfo.isMember) {
    return { canChat: true, isMember: true }
  }

  // 免费用户检查每日限制
  const dailyLimit = options.customLimit || 100 // 免费用户每日100条消息
  const todayCount = options.messageCount || 0

  if (todayCount >= dailyLimit) {
    return {
      canChat: false,
      reason: `免费用户每日对话限制${dailyLimit}条，今日已使用${todayCount}条`,
      isMember: false
    }
  }

  return { canChat: true, isMember: false }
}

/**
 * 角色创建权限检查
 */
export async function checkCharacterCreatePermission(
  env: Env,
  userId: string,
  options: {
    currentCharacterCount?: number // 当前角色数量
  } = {}
): Promise<{ canCreate: boolean; reason?: string; isMember: boolean; limit: number }> {
  const membershipInfo = await getCachedMembership(env, userId)

  // 根据会员等级确定限制
  let limit = 1 // 免费用户默认1个
  if (membershipInfo.isMember && membershipInfo.subscription) {
    // 这里需要根据实际的会员套餐来确定限制
    // 暂时使用简单逻辑
    limit = 5 // 会员默认5个，实际应该从套餐配置中获取
  }

  const currentCount = options.currentCharacterCount || 0

  if (currentCount >= limit) {
    return {
      canCreate: false,
      reason: `${
        membershipInfo.isMember ? '当前会员等级' : '免费用户'
      }最多创建${limit}个角色，当前已创建${currentCount}个`,
      isMember: membershipInfo.isMember,
      limit
    }
  }

  return {
    canCreate: true,
    isMember: membershipInfo.isMember,
    limit
  }
}

/**
 * 统一的服务积分扣除接口
 */
export interface ServicePointsConsumer {
  tts: typeof consumePointsForTTS
  imageGeneration: typeof consumePointsForImageGeneration
  scriptPurchase: typeof consumePointsForScriptPurchase
  videoGeneration: typeof consumePointsForVideoGeneration
  galleryGeneration: typeof consumePointsForGalleryGeneration
}

/**
 * 服务积分消费函数集合
 */
export const PointsConsumptionFunctions = {
  tts: consumePointsForTTS,
  imageGeneration: consumePointsForImageGeneration,
  scriptPurchase: consumePointsForScriptPurchase,
  videoGeneration: consumePointsForVideoGeneration,
  galleryGeneration: consumePointsForGalleryGeneration,

  // 权限检查
  checkChatPermission,
  checkCharacterCreatePermission
} as const

/**
 * 创建服务积分管理器实例
 */
export function createServicePointsManager(env: Env) {
  return {
    // 积分扣除方法
    async consumeTTSPoints(userId: string, options?: Parameters<typeof consumePointsForTTS>[2]) {
      return consumePointsForTTS(env, userId, options)
    },

    async consumeImageGenerationPoints(
      userId: string,
      options?: Parameters<typeof consumePointsForImageGeneration>[2]
    ) {
      return consumePointsForImageGeneration(env, userId, options)
    },

    async consumeScriptPurchasePoints(
      userId: string,
      options: Parameters<typeof consumePointsForScriptPurchase>[2]
    ) {
      return consumePointsForScriptPurchase(env, userId, options)
    },

    async consumeVideoGenerationPoints(
      userId: string,
      options?: Parameters<typeof consumePointsForVideoGeneration>[2]
    ) {
      return consumePointsForVideoGeneration(env, userId, options)
    },

    async consumeGalleryGenerationPoints(
      userId: string,
      options?: Parameters<typeof consumePointsForGalleryGeneration>[2]
    ) {
      return consumePointsForGalleryGeneration(env, userId, options)
    },

    // 权限检查方法
    async checkChatPermission(userId: string, options?: Parameters<typeof checkChatPermission>[2]) {
      return checkChatPermission(env, userId, options)
    },

    async checkCharacterCreatePermission(
      userId: string,
      options?: Parameters<typeof checkCharacterCreatePermission>[2]
    ) {
      return checkCharacterCreatePermission(env, userId, options)
    },

    // 积分退还方法
    async refundPoints(
      userId: string,
      options: {
        amount: number
        source: string
        sourceId?: string
        description?: string
      }
    ) {
      const pointsService = createPointsService(env)
      return pointsService.refundPoints(
        userId,
        options.amount,
        options.sourceId,
        options.description
      )
    }
  }
}

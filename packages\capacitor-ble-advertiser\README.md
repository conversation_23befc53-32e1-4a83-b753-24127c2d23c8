# Capacitor BLE Advertiser

Capacitor插件，用于实现BLE广播功能，支持Android和iOS平台。

## 功能特点

- 支持发送蓝牙低功耗(BLE)广播数据
- 支持多种广播模式（低延迟、平衡、低功耗）
- 支持十六进制字符串或数字数组格式的数据
- 支持多个广播实例管理
- 完整的Android和iOS平台支持

## 安装

```bash
npm install capacitor-ble-advertiser
npx cap sync
```

## 权限配置

### Android

在你的应用的`AndroidManifest.xml`中添加以下权限：

```xml
<!-- 基本蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

<!-- Android 12及以上需要的蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

<!-- 位置权限（Android 11以下需要） -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- 声明设备需要蓝牙LE功能 -->
<uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
```

### iOS

在你的应用的`Info.plist`中添加以下权限描述：

```xml
<!-- 蓝牙使用权限描述 -->
<key>NSBluetoothAlwaysUsageDescription</key>
<string>应用需要使用蓝牙功能来发送设备控制命令</string>
<key>NSBluetoothPeripheralUsageDescription</key>
<string>应用需要使用蓝牙功能来发送设备控制命令</string>

<!-- 蓝牙广播权限 -->
<key>UIBackgroundModes</key>
<array>
    <string>bluetooth-peripheral</string>
</array>
```

## 使用方法

```typescript
import { BleAdvertiser } from 'capacitor-ble-advertiser';

// 初始化蓝牙服务
async function initializeBluetooth() {
  const { success } = await BleAdvertiser.initialize();
  if (success) {
    console.log('蓝牙服务初始化成功');
  } else {
    console.error('蓝牙服务初始化失败');
  }
}

// 检查蓝牙是否启用
async function checkBluetoothEnabled() {
  const { enabled } = await BleAdvertiser.isBluetoothEnabled();
  console.log(`蓝牙状态: ${enabled ? '已启用' : '未启用'}`);
  return enabled;
}

// 开始广播
async function startBroadcasting(command: string) {
  const { success, instanceId } = await BleAdvertiser.startAdvertising({
    // 广播模式: 0=平衡模式, 1=低延迟模式(默认), 2=低功耗模式
    mode: 1,
    // 制造商ID，用于标识广播数据
    manufacturerId: 255,
    // 要广播的数据，十六进制字符串
    data: command,
    // 可选: 广播实例ID，用于区分多个广播
    instanceId: 1
  });
  
  if (success) {
    console.log(`广播开始成功，实例ID: ${instanceId}`);
  } else {
    console.error('广播开始失败');
  }
  
  return instanceId;
}

// 停止广播
async function stopBroadcasting(instanceId: number) {
  const { success } = await BleAdvertiser.stopAdvertising({
    instanceId: instanceId
  });
  
  if (success) {
    console.log('广播已停止');
  } else {
    console.error('停止广播失败');
  }
}

// 停止所有广播
async function stopAllBroadcasting() {
  const { success } = await BleAdvertiser.stopAllAdvertising();
  
  if (success) {
    console.log('所有广播已停止');
  } else {
    console.error('停止所有广播失败');
  }
}
```

## API

### initialize()

初始化蓝牙广播服务。

**返回值：**
- `success`: 是否初始化成功

### isBluetoothEnabled()

检查蓝牙是否已启用。

**返回值：**
- `enabled`: 蓝牙是否已启用

### startAdvertising(options)

开始蓝牙广播。

**参数：**
- `options`: 广播选项
  - `mode?`: 广播模式 (0=平衡, 1=低延迟, 2=低功耗)
  - `manufacturerId?`: 制造商ID (默认: 255)
  - `data`: 要广播的数据，十六进制字符串或数字数组
  - `instanceId?`: 广播实例ID

**返回值：**
- `success`: 是否成功开始广播
- `instanceId`: 广播实例ID

### stopAdvertising(options)

停止指定的广播。

**参数：**
- `options`: 停止选项
  - `instanceId?`: 要停止的广播实例ID

**返回值：**
- `success`: 是否成功停止广播

### stopAllAdvertising()

停止所有广播。

**返回值：**
- `success`: 是否成功停止所有广播

## 示例项目

参见 [示例项目](https://github.com/rcapp/capacitor-ble-advertiser-example) 了解完整的使用示例。

## 注意事项

1. 在Android 12及以上版本，需要在运行时请求蓝牙权限
2. 在iOS上，蓝牙广播功能在应用进入后台后会有限制
3. 不同设备的广播能力可能有差异，请进行充分测试

## 许可证

MIT 
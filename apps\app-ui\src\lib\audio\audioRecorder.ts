/**
 * MediaRecorder 封装类
 * 提供音频录制功能和实时音量监测
 */

import {
  detectSupportedAudioFormat,
  SUPPORTED_AUDIO_FORMATS,
  AUDIO_QUALITY_PRESETS
} from './audioUtils'

export interface AudioRecorderOptions {
  quality?: 'high' | 'medium' | 'low'
  maxDuration?: number // 最大录制时长（秒）
  onVolumeChange?: (volume: number) => void // 音量变化回调
  onTimeUpdate?: (currentTime: number) => void // 时间更新回调
}

export interface AudioRecorderState {
  isRecording: boolean
  isPaused: boolean
  duration: number
  volume: number
  error?: string
}

export class AudioRecorder {
  private mediaRecorder: MediaRecorder | null = null
  private audioStream: MediaStream | null = null
  private audioChunks: Blob[] = []
  private startTime: number = 0
  private pausedDuration: number = 0
  private pauseStartTime: number = 0

  // 音量检测相关
  private audioContext: AudioContext | null = null
  private analyser: AnalyserNode | null = null
  private volumeCheckInterval: number | null = null

  // 时间更新定时器
  private timeUpdateInterval: number | null = null

  private options: Required<AudioRecorderOptions>
  private state: AudioRecorderState = {
    isRecording: false,
    isPaused: false,
    duration: 0,
    volume: 0
  }

  constructor(options: AudioRecorderOptions = {}) {
    this.options = {
      quality: 'medium',
      maxDuration: 60,
      onVolumeChange: () => {},
      onTimeUpdate: () => {},
      ...options
    }
  }

  /**
   * 初始化录制器
   */
  async initialize(): Promise<void> {
    try {
      // 请求麦克风权限
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: AUDIO_QUALITY_PRESETS[this.options.quality].sampleRate,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      })

      // 检测支持的音频格式
      const supportedFormat = detectSupportedAudioFormat()
      const mimeType = SUPPORTED_AUDIO_FORMATS[supportedFormat]

      // 创建MediaRecorder
      this.mediaRecorder = new MediaRecorder(this.audioStream, {
        mimeType,
        audioBitsPerSecond: AUDIO_QUALITY_PRESETS[this.options.quality].bitRate
      })

      // 设置事件监听器
      this.setupEventListeners()

      // 初始化音量检测
      await this.initVolumeDetection()
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '初始化录制器失败'
      this.setState({ error: errorMessage })
      throw error
    }
  }

  /**
   * 设置MediaRecorder事件监听器
   */
  private setupEventListeners(): void {
    if (!this.mediaRecorder) return

    this.mediaRecorder.ondataavailable = event => {
      if (event.data.size > 0) {
        this.audioChunks.push(event.data)
      }
    }

    this.mediaRecorder.onstart = () => {
      this.startTime = Date.now()
      this.pausedDuration = 0
      this.audioChunks = []
      this.setState({
        isRecording: true,
        isPaused: false,
        duration: 0,
        error: undefined
      })
      this.startVolumeDetection()
      this.startTimeUpdate()
    }

    this.mediaRecorder.onpause = () => {
      this.pauseStartTime = Date.now()
      this.setState({ isPaused: true })
      this.stopVolumeDetection()
      this.stopTimeUpdate()
    }

    this.mediaRecorder.onresume = () => {
      this.pausedDuration += Date.now() - this.pauseStartTime
      this.setState({ isPaused: false })
      this.startVolumeDetection()
      this.startTimeUpdate()
    }

    this.mediaRecorder.onstop = () => {
      this.setState({
        isRecording: false,
        isPaused: false
      })
      this.stopVolumeDetection()
      this.stopTimeUpdate()
    }

    this.mediaRecorder.onerror = event => {
      const error = event.error || new Error('录制过程中发生错误')
      this.setState({ error: error.message })
      this.stop()
    }
  }

  /**
   * 初始化音量检测
   */
  private async initVolumeDetection(): Promise<void> {
    if (!this.audioStream) return

    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()

      if (this.audioContext.state === 'suspended') {
        try {
          await this.audioContext.resume()
        } catch (error) {
          console.warn('AudioContext恢复失败:', error)
        }
      }

      const source = this.audioContext.createMediaStreamSource(this.audioStream)

      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = 512
      this.analyser.smoothingTimeConstant = 0.3

      source.connect(this.analyser)
    } catch (error) {
      console.warn('初始化音量检测失败:', error)
    }
  }

  /**
   * 开始音量检测
   */
  private startVolumeDetection(): void {
    if (!this.analyser || !this.audioContext) return

    if (this.audioContext.state === 'suspended') {
      this.audioContext.resume().catch(console.warn)
    }

    this.volumeCheckInterval = window.setInterval(() => {
      if (!this.analyser) return

      const dataArray = new Uint8Array(this.analyser.frequencyBinCount)
      this.analyser.getByteTimeDomainData(dataArray)

      let sum = 0
      for (let i = 0; i < dataArray.length; i++) {
        const sample = (dataArray[i] - 128) / 128
        sum += sample * sample
      }
      const rms = Math.sqrt(sum / dataArray.length)

      const volume = Math.min(1, rms * 50)

      this.setState({ volume })
      this.options.onVolumeChange(volume)
    }, 50)
  }

  /**
   * 停止音量检测
   */
  private stopVolumeDetection(): void {
    if (this.volumeCheckInterval) {
      clearInterval(this.volumeCheckInterval)
      this.volumeCheckInterval = null
    }
    this.setState({ volume: 0 })
  }

  /**
   * 开始时间更新
   */
  private startTimeUpdate(): void {
    this.timeUpdateInterval = window.setInterval(() => {
      if (this.state.isRecording && !this.state.isPaused) {
        const currentDuration = (Date.now() - this.startTime - this.pausedDuration) / 1000
        this.setState({ duration: currentDuration })
        this.options.onTimeUpdate(currentDuration)

        if (currentDuration >= this.options.maxDuration) {
          this.stop()
        }
      }
    }, 100)
  }

  /**
   * 停止时间更新
   */
  private stopTimeUpdate(): void {
    if (this.timeUpdateInterval) {
      clearInterval(this.timeUpdateInterval)
      this.timeUpdateInterval = null
    }
  }

  /**
   * 开始录制
   */
  async start(): Promise<void> {
    if (!this.mediaRecorder) {
      const error = '录制器未初始化'
      throw new Error(error)
    }

    if (this.state.isRecording) {
      const error = '已在录制中'
      throw new Error(error)
    }

    try {
      this.mediaRecorder.start(1000)
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '开始录制失败'
      this.setState({ error: errorMessage })
      throw error
    }
  }

  /**
   * 暂停录制
   */
  pause(): void {
    if (!this.mediaRecorder || !this.state.isRecording || this.state.isPaused) {
      return
    }

    try {
      this.mediaRecorder.pause()
    } catch (error) {
      console.error('暂停录制失败:', error)
    }
  }

  /**
   * 恢复录制
   */
  resume(): void {
    if (!this.mediaRecorder || !this.state.isRecording || !this.state.isPaused) {
      return
    }

    try {
      this.mediaRecorder.resume()
    } catch (error) {
      console.error('恢复录制失败:', error)
    }
  }

  /**
   * 停止录制并返回音频数据
   */
  async stop(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || !this.state.isRecording) {
        reject(new Error('当前未在录制'))
        return
      }

      const handleStop = () => {
        this.mediaRecorder!.removeEventListener('stop', handleStop)

        if (this.audioChunks.length === 0) {
          reject(new Error('没有录制到音频数据'))
          return
        }

        const mimeType = this.mediaRecorder!.mimeType
        const audioBlob = new Blob(this.audioChunks, { type: mimeType })

        resolve(audioBlob)
      }

      this.mediaRecorder.addEventListener('stop', handleStop)
      this.mediaRecorder.stop()
    })
  }

  /**
   * 获取当前状态
   */
  getState(): AudioRecorderState {
    return { ...this.state }
  }

  /**
   * 检查是否正在录制
   */
  isRecording(): boolean {
    return this.state.isRecording
  }

  /**
   * 检查是否已暂停
   */
  isPaused(): boolean {
    return this.state.isPaused
  }

  /**
   * 获取当前录制时长
   */
  getCurrentDuration(): number {
    return this.state.duration
  }

  /**
   * 获取当前音量
   */
  getCurrentVolume(): number {
    return this.state.volume
  }

  /**
   * 销毁录制器，释放资源
   */
  destroy(): void {
    if (this.mediaRecorder && this.state.isRecording) {
      this.mediaRecorder.stop()
    }

    this.stopVolumeDetection()
    this.stopTimeUpdate()

    if (this.audioStream) {
      this.audioStream.getTracks().forEach(track => track.stop())
      this.audioStream = null
    }

    if (this.audioContext) {
      this.audioContext.close()
      this.audioContext = null
    }

    this.mediaRecorder = null
    this.analyser = null
    this.audioChunks = []

    this.state = {
      isRecording: false,
      isPaused: false,
      duration: 0,
      volume: 0
    }
  }

  /**
   * 更新状态
   */
  private setState(updates: Partial<AudioRecorderState>): void {
    this.state = { ...this.state, ...updates }
  }
}

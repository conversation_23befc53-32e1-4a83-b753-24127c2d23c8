// 强度设置
export interface Intensity {
  thrust: number; // 推力强度 (-1表示停止, 1-3表示强度等级)
  suction: number; // 吸力强度
  vibrate: number; // 震动强度
}

// 对话内容
export interface Dialogue {
  role: string; // 角色名称
  time: string; // 时间点 (格式: "00:01:30")
  dialogue: string; // 对话内容
  intensity: Intensity; // 设备强度设置
}

// 图片资源
export interface ScriptImage {
  name: string; // 图片名称/描述
  pic: string; // 图片URL
}

// 剧本阶段
export interface ScriptStage {
  stage: number; // 阶段编号
  stageTitle: string; // 阶段标题
  duration?: number; // 阶段时长（秒）
  pics: ScriptImage[]; // 阶段图片
  dialogues: Dialogue[]; // 阶段对话
}

// 完整的剧本内容
export interface ScriptContent {
  audioUrl: string; // 音频文件URL
  totalDuration: number; // 总时长（秒）
  stageCount: number; // 阶段数量
  stages: ScriptStage[]; // 剧本阶段列表
  metadata?: {
    // 可选的元数据
    language?: string; // 语言
    version?: string; // 版本
    author?: string; // 作者
    [key: string]: any; // 其他自定义字段
  };
}

// 时间点转换工具
export class TimeUtils {
  /**
   * 将时间字符串转换为秒数
   * @param timeStr 时间字符串，格式: "00:01:30"
   * @returns 秒数
   */
  static timeStringToSeconds(timeStr: string): number {
    const parts = timeStr.split(':').map(Number);
    if (parts.length === 3) {
      return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else if (parts.length === 2) {
      return parts[0] * 60 + parts[1];
    }
    return 0;
  }

  /**
   * 将秒数转换为时间字符串
   * @param seconds 秒数
   * @returns 时间字符串，格式: "00:01:30"
   */
  static secondsToTimeString(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;

    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs
        .toString()
        .padStart(2, '0')}`;
    } else {
      return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
  }

  /**
   * 计算剧本总时长
   * @param stages 剧本阶段列表
   * @returns 总时长（秒）
   */
  static calculateTotalDuration(stages: ScriptStage[]): number {
    let maxTime = 0;

    stages.forEach((stage) => {
      stage.dialogues.forEach((dialogue) => {
        const timeInSeconds = this.timeStringToSeconds(dialogue.time);
        if (timeInSeconds > maxTime) {
          maxTime = timeInSeconds;
        }
      });
    });

    return maxTime;
  }
}

// 剧本内容验证器
export class ScriptValidator {
  /**
   * 验证剧本内容是否有效
   * @param content 剧本内容
   * @returns 验证结果
   */
  static validateScriptContent(content: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!content) {
      errors.push('剧本内容不能为空');
      return { valid: false, errors };
    }

    if (!content.stages || !Array.isArray(content.stages)) {
      errors.push('剧本必须包含stages数组');
      return { valid: false, errors };
    }

    if (content.stages.length === 0) {
      errors.push('剧本至少需要一个阶段');
      return { valid: false, errors };
    }

    // 验证每个阶段
    content.stages.forEach((stage: any, index: number) => {
      if (typeof stage.stage !== 'number') {
        errors.push(`阶段${index + 1}: stage字段必须是数字`);
      }

      if (!stage.stageTitle || typeof stage.stageTitle !== 'string') {
        errors.push(`阶段${index + 1}: stageTitle字段必须是非空字符串`);
      }

      if (!stage.pics || !Array.isArray(stage.pics)) {
        errors.push(`阶段${index + 1}: pics字段必须是数组`);
      }

      if (!stage.dialogues || !Array.isArray(stage.dialogues)) {
        errors.push(`阶段${index + 1}: dialogues字段必须是数组`);
      } else {
        // 验证对话
        stage.dialogues.forEach((dialogue: any, dialogueIndex: number) => {
          if (!dialogue.role || typeof dialogue.role !== 'string') {
            errors.push(`阶段${index + 1}对话${dialogueIndex + 1}: role字段必须是非空字符串`);
          }

          if (!dialogue.time || typeof dialogue.time !== 'string') {
            errors.push(`阶段${index + 1}对话${dialogueIndex + 1}: time字段必须是字符串`);
          }

          if (!dialogue.dialogue || typeof dialogue.dialogue !== 'string') {
            errors.push(`阶段${index + 1}对话${dialogueIndex + 1}: dialogue字段必须是非空字符串`);
          }

          if (!dialogue.intensity || typeof dialogue.intensity !== 'object') {
            errors.push(`阶段${index + 1}对话${dialogueIndex + 1}: intensity字段必须是对象`);
          }
        });
      }
    });

    return { valid: errors.length === 0, errors };
  }
}

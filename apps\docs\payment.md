# 支付系统架构设计文档

## 概述

基于现有的会员订阅系统，集成支付宝和微信支付，实现完整的支付闭环。

## 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                          前端生态                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐           ┌─────────────────────────────┐   │
│  │   APP (React)   │           │   支付页面 (pay-web)        │   │
│  │                 │           │                            │   │
│  │ • 会员中心       │           │ • pay.example.com          │   │
│  │ • 支付入口       │ ────────► │ • /pay/start?orderId=xxx   │   │
│  │ • 状态轮询       │           │ • /pay/result              │   │
│  │ • 结果展示       │ ◄──────── │ • 自动跳转支付平台          │   │
│  │                 │           │ • "回到APP"按钮             │   │
│  └─────────────────┘           └─────────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                        支付平台                                 │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐           ┌─────────────────────────────┐   │
│  │    支付宝支付    │           │         微信支付            │   │
│  │                 │           │                            │   │
│  │ • 网页支付       │           │ • H5支付                   │   │
│  │ • 手机网站支付   │           │ • 公众号支付                │   │
│  │ • 异步回调       │           │ • 异步回调                 │   │
│  │                 │           │                            │   │
│  └─────────────────┘           └─────────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                    后端 API 服务                                │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │               Cloudflare Workers                        │   │
│  │                                                         │   │
│  │ • POST /api/payment/create-order     (创建订单)         │   │
│  │ • POST /api/payment/callback/alipay  (支付宝回调)       │   │
│  │ • POST /api/payment/callback/wechat  (微信回调)         │   │
│  │ • GET  /api/payment/status/:id       (查询状态)         │   │
│  │ • GET  /api/payment/order/:id/info   (订单信息)         │   │
│  │                                                         │   │
│  │ 现有API (已实现):                                        │   │
│  │ ✅ /api/membership/*          (会员管理)               │   │
│  │ ✅ /api/payment/create-order  (订单创建 - Mock版本)      │   │
│  │ ✅ /api/payment/callback      (支付回调 - Mock版本)      │   │
│  │ ✅ /api/payment/status/*      (状态查询 - Mock版本)      │   │
│  │                                                         │   │
│  └─────────────────────────────────────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 支付流程设计

### 完整支付流程

```
1. 用户发起支付
   [APP] → 点击"升级Pro会员" → 选择支付方式(支付宝/微信)

2. 创建支付订单
   [APP] → POST /api/payment/create-order → [Worker API]
   参数: { planId, paymentMethod: 'alipay'|'wechat' }
   返回: { orderId, redirectUrl: 'pay.example.com/pay/start?orderId=xxx' }

3. 打开支付页面
   [APP] → 系统浏览器打开 redirectUrl → [pay-web]

4. 自动跳转支付平台
   [pay-web] → 获取订单信息 → 自动跳转到支付宝/微信

5. 用户完成支付
   [支付宝/微信] → 用户扫码/输入密码支付

6. 支付结果处理 (双重保障)
   方案A (主要): [支付平台] → 异步回调 → POST /api/payment/callback/{platform}
   方案B (备用): [APP] → 轮询状态 → GET /api/payment/status/:paymentId

7. 激活会员服务
   [Worker API] → 激活会员 → 发放积分 → 更新数据库

8. 用户返回APP
   [pay-web/result] → 显示支付结果 → 点击"回到APP" → Deep Link返回
```

### 状态更新机制

```
主要机制: 支付平台异步回调
┌─────────────┐    支付完成     ┌─────────────────┐    HTTP回调    ┌─────────────┐
│  支付宝/微信  │ ────────────► │  支付平台服务器   │ ───────────► │  我们的API   │
└─────────────┘                └─────────────────┘              └─────────────┘
                                                                      │
                                                                      ▼
                                                               更新订单状态
                                                               激活会员服务
                                                               发放积分

备用机制: APP主动轮询 (防止回调失败或延迟)
┌─────────────┐    定时轮询     ┌─────────────────┐
│     APP     │ ◄────────────► │    Worker API   │
└─────────────┘   每3秒一次     └─────────────────┘
                 最多轮询5分钟          │
                                      ▼
                              查询支付平台订单状态
                              返回最新状态给APP
```

## 技术实现方案

### 数据库设计

基于现有schema，新增支付订单表：

```sql
-- 支付订单表 (新增)
CREATE TABLE PaymentOrder (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES "User"(id),
  plan_id UUID NOT NULL REFERENCES "MembershipPlan"(id),
  order_no VARCHAR(64) NOT NULL UNIQUE,     -- 我们的订单号
  external_order_id VARCHAR(128),           -- 支付平台订单号
  
  -- 支付信息
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'CNY',
  payment_method VARCHAR(20),               -- 'alipay', 'wechat'
  
  -- 订单状态
  status VARCHAR(20) DEFAULT 'pending',     -- pending, paid, failed, cancelled, expired
  
  -- 业务信息
  description TEXT NOT NULL,
  is_upgrade BOOLEAN DEFAULT FALSE,         -- 是否升级订单
  original_amount DECIMAL(10,2),            -- 原价(升级时显示节省金额)
  current_subscription_id UUID,             -- 当前订阅ID(升级时用于取消)
  
  -- 回调信息
  callback_url TEXT,
  return_url TEXT,
  notify_url TEXT,
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT NOW(),
  paid_at TIMESTAMP,
  expires_at TIMESTAMP,                     -- 订单过期时间(30分钟)
  
  -- 元数据
  metadata JSONB DEFAULT '{}'
);

-- 索引
CREATE INDEX idx_payment_order_user_id ON PaymentOrder(user_id);
CREATE INDEX idx_payment_order_status ON PaymentOrder(status);
CREATE INDEX idx_payment_order_external_id ON PaymentOrder(external_order_id);
CREATE INDEX idx_payment_order_expires_at ON PaymentOrder(expires_at);
```

### 支付服务接口设计

基于现有的 `MockPaymentService`，创建真实支付服务：

```typescript
// 支付服务接口 (已存在于 mock-payment.ts)
export interface PaymentService {
  createOrder(orderInfo: CreateOrderRequest): Promise<PaymentResult>
  verifyPayment(paymentId: string): Promise<PaymentVerification>
  processCallback(callbackData: any): Promise<CallbackResult>
}

// 真实支付服务实现 (新增)
export class RealPaymentService implements PaymentService {
  private alipayService: AlipayService
  private wechatService: WechatPayService
  
  async createOrder(orderInfo: CreateOrderRequest): Promise<PaymentResult> {
    // 根据 paymentMethod 选择对应的支付服务
    if (orderInfo.paymentMethod === 'alipay') {
      return this.alipayService.createOrder(orderInfo)
    } else if (orderInfo.paymentMethod === 'wechat') {
      return this.wechatService.createOrder(orderInfo)
    }
  }
  
  // ... 其他方法实现
}
```

## 项目结构规划

### 现有项目结构
```
apps/
├── app-ui/              # React Native APP (已存在)
├── backend-service/     # Cloudflare Workers API (已存在)
└── pay-web/             # 支付页面 Web项目 (新增)
```

### 新增项目: apps/pay-web

```
apps/pay-web/
├── package.json
├── vite.config.ts
├── src/
│   ├── main.tsx
│   ├── App.tsx
│   ├── pages/
│   │   ├── PaymentStart.tsx    # /pay/start?orderId=xxx
│   │   └── PaymentResult.tsx   # /pay/result
│   ├── api/
│   │   └── payment.ts          # API调用封装
│   ├── utils/
│   │   ├── deeplink.ts         # APP深链接处理
│   │   └── payment-redirect.ts # 支付平台跳转逻辑
│   └── types/
│       └── payment.ts          # 类型定义
├── public/
│   └── favicon.ico
└── dist/                       # 构建产物，部署到 pay.example.com
```

## 开发计划和TODO

### 阶段一：后端支付服务升级 (优先级: P0)

**位置**: `apps/backend-service/src/lib/payment/`

#### TODO 1.1: 创建真实支付服务
- [x] **文件**: `real-payment-service.ts` (新建) ✅
  - 实现 `RealPaymentService` 类
  - 集成支付宝和微信支付SDK
  - 统一支付接口封装

- [x] **文件**: `alipay-service.ts` (新建) ✅
  - 支付宝网页支付集成
  - 订单创建、查询、回调处理
  - 签名验证逻辑

- [ ] **文件**: `wechat-service.ts` (新建)
  - 微信H5支付集成
  - 订单创建、查询、回调处理
  - 签名验证逻辑

- [x] **文件**: `payment-config.ts` (新建) ✅
  - 支付配置管理
  - 环境变量定义
  - 回调URL配置

#### TODO 1.2: 修改现有支付API
- [x] **文件**: `mock-payment.ts` (修改) ✅
  - 修改 `createPaymentService()` 工厂函数
  - 根据环境变量返回 Mock 或 Real 服务

- [x] **文件**: `../routes/payment.ts` (修改) ✅
  - 扩展 `create-order` API 支持支付方式选择
  - 添加支付平台特定的回调路由
  - 添加订单信息查询API

#### TODO 1.3: 数据库扩展
- [x] **文件**: `../db/schema.ts` (修改) ✅
  - 添加 `PaymentOrder` 表定义
  - 添加相关索引

- [x] **文件**: `../db/queries/payment.ts` (新建) ✅
  - 支付订单CRUD操作
  - 订单状态更新
  - 支付记录查询

- [x] **文件**: `../db/migrations/` (新建迁移文件) ✅
  - 创建 PaymentOrder 表的迁移脚本

#### TODO 1.4: 环境变量配置
- [ ] **文件**: `wrangler.toml` (修改)
  - 添加支付宝配置 (APP_ID, PRIVATE_KEY, PUBLIC_KEY)
  - 添加微信支付配置 (MCHID, API_KEY, CERT_PATH)
  - 添加支付回调域名配置

### 阶段二：支付页面Web项目 (优先级: P0)

**位置**: `apps/pay-web/` (整个项目新建)

#### TODO 2.1: 项目初始化
- [x] **文件**: `package.json` (新建) ✅
  - 初始化Vite + React项目
  - 安装必要依赖(react, typescript, tailwind, heroui)

- [x] **文件**: `vite.config.ts` (新建) ✅
  - 配置开发服务器
  - 配置构建输出
  - 配置环境变量和API代理

#### TODO 2.2: 核心页面开发
- [x] **文件**: `src/pages/PaymentStart.tsx` (新建) ✅
  - 获取订单信息 (GET /api/payment/order/:orderId/info)
  - 显示支付信息(金额、套餐名称)
  - 自动跳转到支付宝/微信
  - Loading状态和错误处理

- [x] **文件**: `src/pages/PaymentResult.tsx` (新建) ✅
  - 显示支付结果(成功/失败)
  - "回到APP"按钮 (Deep Link)
  - 支付失败重试选项
  - 支付状态轮询机制

#### TODO 2.3: API和工具封装
- [x] **文件**: `src/api/payment.ts` (新建) ✅
  - 封装支付相关API调用
  - 订单信息获取
  - 支付状态查询和轮询

- [x] **文件**: `src/utils/deeplink.ts` (新建) ✅
  - APP深链接生成
  - 平台检测(iOS/Android)
  - 打开APP逻辑

- [x] **文件**: `src/utils/payment-redirect.ts` (新建) ✅
  - 支付宝跳转逻辑
  - 微信支付跳转逻辑
  - URL参数构建

#### TODO 2.4: 部署配置
- [ ] **配置域名**: `pay.example.com`
  - DNS解析配置
  - SSL证书配置
  - CDN缓存策略

### 阶段三：APP端支付集成 (优先级: P1)

**位置**: `apps/app-ui/src/`

#### TODO 3.1: 支付入口开发
- [ ] **文件**: `pages/membership/MembershipPage.tsx` (修改)
  - 添加支付方式选择(支付宝/微信)
  - 调用 `/api/payment/create-order`
  - 打开系统浏览器跳转支付页面

#### TODO 3.2: 支付状态轮询
- [ ] **文件**: `hooks/use-payment-polling.ts` (新建)
  - 实现支付状态轮询逻辑
  - 3秒间隔，最多5分钟
  - 状态变化通知

- [ ] **文件**: `components/payment/PaymentStatusModal.tsx` (新建)
  - 支付进度显示
  - 取消支付选项
  - 支付结果展示

#### TODO 3.3: Deep Link处理
- [ ] **文件**: `src/App.tsx` (修改)
  - 添加支付结果Deep Link处理
  - 路由到支付结果页面
  - 更新会员状态

### 阶段四：测试和监控 (优先级: P2)

#### TODO 4.1: 测试环境配置
- [ ] 支付宝沙箱环境配置
- [ ] 微信支付测试环境配置
- [ ] 端到端测试流程验证

#### TODO 4.2: 生产环境部署
- [ ] 支付宝生产环境配置
- [ ] 微信支付生产环境配置
- [ ] 监控和日志配置

#### TODO 4.3: 异常处理和监控
- [ ] 支付失败重试机制
- [ ] 订单过期处理
- [ ] 异常订单监控

## 安全考虑

### 签名验证
- 支付宝: RSA2签名验证
- 微信: MD5/HMAC-SHA256签名验证
- 回调数据完整性校验

### 金额校验
- 订单金额与实际支付金额一致性检查
- 防止金额篡改攻击

### 重复支付防护
- 订单状态幂等性保证
- 重复回调过滤

### 订单安全
- 订单过期机制(30分钟)
- 订单状态机严格控制

## 部署和配置

### 环境变量
```bash
# 支付宝配置
ALIPAY_APP_ID=your_app_id
ALIPAY_PRIVATE_KEY=your_private_key  
ALIPAY_PUBLIC_KEY=alipay_public_key
ALIPAY_GATEWAY=https://openapi.alipay.com/gateway.do

# 微信支付配置  
WECHAT_MCHID=your_mch_id
WECHAT_API_KEY=your_api_key
WECHAT_APPID=your_app_id
WECHAT_GATEWAY=https://api.mch.weixin.qq.com

# 支付页面配置
PAY_WEB_DOMAIN=pay.example.com
PAY_CALLBACK_DOMAIN=your-worker-domain.workers.dev
```

### 域名配置
- 支付页面: `pay.example.com` → `apps/pay-web/dist/`  
- API服务: `api.example.com` → `apps/backend-service` (Cloudflare Workers)
- 主应用: `app.example.com` → `apps/app-ui/dist/`

### 支付平台配置
- 支付宝: 配置支付回调URL为 `https://api.example.com/api/payment/callback/alipay`
- 微信: 配置支付回调URL为 `https://api.example.com/api/payment/callback/wechat`

## 开发时间估算

- **阶段一**: 2-3天 (后端支付服务)
- **阶段二**: 2-3天 (支付页面开发)  
- **阶段三**: 1-2天 (APP端集成)
- **阶段四**: 1-2天 (测试和部署)

**总计**: 约1-2周完成完整支付系统。

## 风险评估

### 技术风险
- 支付平台API文档理解偏差
- 回调机制可靠性依赖
- 跨域和安全策略限制

### 业务风险  
- 支付转化率影响用户体验
- 支付失败处理用户感知
- 退款和争议处理流程

### 缓解措施
- 充分的沙箱测试
- 完善的日志和监控
- 清晰的用户反馈机制
/* Android 兼容性 CSS - 精确定位修复 */

/*
 * 新的兼容性策略：
 * 1. 只对 Android 12 及以下设备应用修复（通过 .android-legacy 类）
 * 2. 使用 CSS 特性检测确保只在需要时应用
 * 3. 避免硬编码，使用动态 CSS 变量系统
 * 4. Android 13+ 设备完全不受影响
 */

/* 基础 CSS 变量 fallback - 确保在所有设备上都有基础颜色定义 */
:root {
  /* HeroUI 主题颜色 - 使用 RGB 格式确保兼容性 */
  --background-rgb: 253, 251, 252;
  --foreground-rgb: 26, 26, 26;
  --content1-rgb: 255, 255, 255;
  --content2-rgb: 249, 250, 251;
  --content3-rgb: 243, 244, 246;
  --content4-rgb: 229, 231, 235;
  --primary-rgb: 233, 30, 99;
  --secondary-rgb: 156, 39, 176;
}

.dark {
  --background-rgb: 18, 21, 33;
  --foreground-rgb: 255, 255, 255;
  --content1-rgb: 61, 42, 66;
  --content2-rgb: 80, 58, 86;
  --content3-rgb: 99, 74, 106;
  --content4-rgb: 118, 90, 126;
  --primary-rgb: 226, 63, 157;
  --secondary-rgb: 186, 104, 200;
}

/* 只对 Android 12 及以下 + 不支持现代 CSS 特性的设备应用修复 */
@supports not (color: color-mix(in srgb, red 50%, blue)) {
  .android-legacy {
    /* 确保 CSS 变量正确映射 */
    --background: rgb(var(--background-rgb));
    --foreground: rgb(var(--foreground-rgb));
    --content1: rgb(var(--content1-rgb));
    --content2: rgb(var(--content2-rgb));
    --content3: rgb(var(--content3-rgb));
    --content4: rgb(var(--content4-rgb));
    --primary: rgb(var(--primary-rgb));
    --secondary: rgb(var(--secondary-rgb));
  }

  .dark.android-legacy {
    --background: rgb(var(--background-rgb));
    --foreground: rgb(var(--foreground-rgb));
    --content1: rgb(var(--content1-rgb));
    --content2: rgb(var(--content2-rgb));
    --content3: rgb(var(--content3-rgb));
    --content4: rgb(var(--content4-rgb));
    --primary: rgb(var(--primary-rgb));
    --secondary: rgb(var(--secondary-rgb));
  }

  /* 渐变背景修复 - 使用通用选择器 */
  .android-legacy .bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(left, var(--tw-gradient-stops)) !important;
  }

  .android-legacy .bg-gradient-to-l {
    background-image: linear-gradient(to left, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(right, var(--tw-gradient-stops)) !important;
  }

  .android-legacy .bg-gradient-to-t {
    background-image: linear-gradient(to top, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(bottom, var(--tw-gradient-stops)) !important;
  }

  .android-legacy .bg-gradient-to-b {
    background-image: linear-gradient(to bottom, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(top, var(--tw-gradient-stops)) !important;
  }

  .android-legacy .bg-gradient-to-tr {
    background-image: linear-gradient(to top right, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(bottom left, var(--tw-gradient-stops)) !important;
  }

  .android-legacy .bg-gradient-to-tl {
    background-image: linear-gradient(to top left, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(bottom right, var(--tw-gradient-stops)) !important;
  }

  .android-legacy .bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(top left, var(--tw-gradient-stops)) !important;
  }

  .android-legacy .bg-gradient-to-bl {
    background-image: linear-gradient(to bottom left, var(--tw-gradient-stops)) !important;
    background-image: -webkit-linear-gradient(top right, var(--tw-gradient-stops)) !important;
  }

  /* 渐变色彩停止点 - 动态处理主题颜色 */
  .android-legacy .from-background {
    --tw-gradient-from: var(--background) !important;
    --tw-gradient-to: rgba(var(--background-rgb), 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-content1 {
    --tw-gradient-from: var(--content1) !important;
    --tw-gradient-to: rgba(var(--content1-rgb), 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-content2 {
    --tw-gradient-from: var(--content2) !important;
    --tw-gradient-to: rgba(var(--content2-rgb), 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-content3 {
    --tw-gradient-from: var(--content3) !important;
    --tw-gradient-to: rgba(var(--content3-rgb), 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-content4 {
    --tw-gradient-from: var(--content4) !important;
    --tw-gradient-to: rgba(var(--content4-rgb), 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-primary {
    --tw-gradient-from: var(--primary) !important;
    --tw-gradient-to: rgba(var(--primary-rgb), 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-secondary {
    --tw-gradient-from: var(--secondary) !important;
    --tw-gradient-to: rgba(var(--secondary-rgb), 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  /* via- 颜色处理 */
  .android-legacy .via-background {
    --tw-gradient-stops: var(--tw-gradient-from), var(--background), var(--tw-gradient-to) !important;
  }

  .android-legacy .via-content1 {
    --tw-gradient-stops: var(--tw-gradient-from), var(--content1), var(--tw-gradient-to) !important;
  }

  .android-legacy .via-content2 {
    --tw-gradient-stops: var(--tw-gradient-from), var(--content2), var(--tw-gradient-to) !important;
  }

  .android-legacy .via-content3 {
    --tw-gradient-stops: var(--tw-gradient-from), var(--content3), var(--tw-gradient-to) !important;
  }

  .android-legacy .via-content4 {
    --tw-gradient-stops: var(--tw-gradient-from), var(--content4), var(--tw-gradient-to) !important;
  }

  .android-legacy .via-primary {
    --tw-gradient-stops: var(--tw-gradient-from), var(--primary), var(--tw-gradient-to) !important;
  }

  .android-legacy .via-secondary {
    --tw-gradient-stops: var(--tw-gradient-from), var(--secondary), var(--tw-gradient-to) !important;
  }

  /* to- 颜色处理 */
  .android-legacy .to-background {
    --tw-gradient-to: var(--background) !important;
  }

  .android-legacy .to-content1 {
    --tw-gradient-to: var(--content1) !important;
  }

  .android-legacy .to-content2 {
    --tw-gradient-to: var(--content2) !important;
  }

  .android-legacy .to-content3 {
    --tw-gradient-to: var(--content3) !important;
  }

  .android-legacy .to-content4 {
    --tw-gradient-to: var(--content4) !important;
  }

  .android-legacy .to-primary {
    --tw-gradient-to: var(--primary) !important;
  }

  .android-legacy .to-secondary {
    --tw-gradient-to: var(--secondary) !important;
  }

  /* 标准 Tailwind 颜色支持 */
  .android-legacy .from-blue-500 {
    --tw-gradient-from: #3b82f6 !important;
    --tw-gradient-to: rgba(59, 130, 246, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-green-500 {
    --tw-gradient-from: #10b981 !important;
    --tw-gradient-to: rgba(16, 185, 129, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-red-500 {
    --tw-gradient-from: #ef4444 !important;
    --tw-gradient-to: rgba(239, 68, 68, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-purple-500 {
    --tw-gradient-from: #a855f7 !important;
    --tw-gradient-to: rgba(168, 85, 247, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-pink-500 {
    --tw-gradient-from: #ec4899 !important;
    --tw-gradient-to: rgba(236, 72, 153, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-yellow-500 {
    --tw-gradient-from: #eab308 !important;
    --tw-gradient-to: rgba(234, 179, 8, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  .android-legacy .from-indigo-500 {
    --tw-gradient-from: #6366f1 !important;
    --tw-gradient-to: rgba(99, 102, 241, 0) !important;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to) !important;
  }

  /* 对应的 to- 颜色 */
  .android-legacy .to-blue-500 {
    --tw-gradient-to: #3b82f6 !important;
  }

  .android-legacy .to-green-500 {
    --tw-gradient-to: #10b981 !important;
  }

  .android-legacy .to-red-500 {
    --tw-gradient-to: #ef4444 !important;
  }

  .android-legacy .to-purple-500 {
    --tw-gradient-to: #a855f7 !important;
  }

  .android-legacy .to-purple-600 {
    --tw-gradient-to: #9333ea !important;
  }

  .android-legacy .to-pink-500 {
    --tw-gradient-to: #ec4899 !important;
  }

  .android-legacy .to-yellow-500 {
    --tw-gradient-to: #eab308 !important;
  }

  .android-legacy .to-indigo-500 {
    --tw-gradient-to: #6366f1 !important;
  }

  .android-legacy .to-indigo-600 {
    --tw-gradient-to: #4f46e5 !important;
  }

  /* via- 颜色支持 */
  .android-legacy .via-purple-500 {
    --tw-gradient-stops: var(--tw-gradient-from), #a855f7, var(--tw-gradient-to) !important;
  }

  .android-legacy .via-blue-500 {
    --tw-gradient-stops: var(--tw-gradient-from), #3b82f6, var(--tw-gradient-to) !important;
  }

  .android-legacy .via-green-500 {
    --tw-gradient-stops: var(--tw-gradient-from), #10b981, var(--tw-gradient-to) !important;
  }

  .android-legacy .via-red-500 {
    --tw-gradient-stops: var(--tw-gradient-from), #ef4444, var(--tw-gradient-to) !important;
  }

  .android-legacy .via-pink-500 {
    --tw-gradient-stops: var(--tw-gradient-from), #ec4899, var(--tw-gradient-to) !important;
  }

  .android-legacy .via-yellow-500 {
    --tw-gradient-stops: var(--tw-gradient-from), #eab308, var(--tw-gradient-to) !important;
  }

  /* 基础布局修复 */
  .android-legacy .flex {
    display: -webkit-box !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  .android-legacy .items-center {
    -webkit-box-align: center !important;
    -ms-flex-align: center !important;
    align-items: center !important;
  }

  .android-legacy .justify-center {
    -webkit-box-pack: center !important;
    -ms-flex-pack: center !important;
    justify-content: center !important;
  }

  .android-legacy .min-h-screen {
    min-height: 100vh !important;
    min-height: -webkit-fill-available !important;
  }
}

/* 更老的 WebView 版本额外修复 */
.webview-legacy {
  /* 确保基础 flexbox 工作 */
  .flex {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
  }

  /* 确保渐变有最基础的 fallback */
  .bg-gradient-to-b {
    background: linear-gradient(to bottom, #fdfbfc, #ffffff, #f9fafb) !important;
  }
}

/* 完全不支持 CSS 变量的极端情况 */
.no-css-vars {
  /* 硬编码颜色作为最后的 fallback */
  .bg-gradient-to-b {
    background: linear-gradient(to bottom, #fdfbfc, #ffffff, #f9fafb) !important;
  }

  .from-background {
    background-color: #fdfbfc !important;
  }

  .from-content1 {
    background-color: #ffffff !important;
  }

  .from-content2 {
    background-color: #f9fafb !important;
  }
}

/*
 * 性能优化说明：
 * 1. 所有修复都包装在 @supports 查询中，现代浏览器不会加载这些代码
 * 2. 使用 .android-legacy 类精确定位需要修复的设备
 * 3. Android 13+ 设备完全不会匹配任何规则，零性能影响
 * 4. 使用 CSS 变量系统，避免硬编码维护问题
 */

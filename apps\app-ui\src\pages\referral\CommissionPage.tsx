import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'
import { useAuth } from '@/contexts/auth-context'
import { apiService } from '@/api'
import { useTranslation } from 'react-i18next'

// HeroUI 组件
import { 
  Card, 
  CardBody, 
  Button, 
  Chip, 
  addToast,
  Modal,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  useDisclosure,
  Input,
  Tabs,
  Tab
} from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

// 类型定义
import type { CommissionAccount, CommissionRecord, WithdrawConfig } from '@/api'

export default function CommissionPage() {
  const navigate = useNavigate()
  const { status } = useAuth()
  const { t } = useTranslation(['referral'])
  const { isOpen, onOpen, onClose } = useDisclosure()

  // 状态管理
  const [isLoading, setIsLoading] = useState(true)
  const [account, setAccount] = useState<CommissionAccount | null>(null)
  const [records, setRecords] = useState<CommissionRecord[]>([])
  const [withdrawConfig, setWithdrawConfig] = useState<WithdrawConfig | null>(null)
  const [activeTab, setActiveTab] = useState('overview')

  // 提现表单状态
  const [withdrawAmount, setWithdrawAmount] = useState('')
  const [bankName, setBankName] = useState('')
  const [accountName, setAccountName] = useState('')
  const [accountNumber, setAccountNumber] = useState('')
  const [phone, setPhone] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    // 检查认证状态
    if (status === 'unauthenticated') {
      addToast({
        title: '请先登录',
        color: 'warning'
      })
      navigate('/login')
      return
    }

    if (status === 'authenticated') {
      loadCommissionData()
    }
  }, [status, navigate])

  const loadCommissionData = async () => {
    try {
      setIsLoading(true)
      
      // 并行加载数据
      const [accountResponse, recordsResponse, configResponse] = await Promise.all([
        apiService.referral.getCommissionAccount(),
        apiService.referral.getCommissionRecords(1, 20),
        apiService.referral.getWithdrawConfig()
      ])
      
      if (accountResponse.success) {
        setAccount(accountResponse.data.account)
      }
      
      if (recordsResponse.success) {
        setRecords(recordsResponse.data.list)
      }
      
      if (configResponse.success) {
        setWithdrawConfig(configResponse.data)
      }
    } catch (error) {
      console.error('获取佣金数据失败:', error)
      addToast({
        title: t('referral:commission.get_info_failed'),
        color: 'danger'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleWithdraw = async () => {
    if (!withdrawAmount || !bankName || !accountName || !accountNumber) {
      addToast({
        title: t('referral:commission.withdraw.incomplete_info'),
        color: 'warning'
      })
      return
    }

    const amount = parseFloat(withdrawAmount)
    if (isNaN(amount) || amount <= 0) {
      addToast({
        title: t('referral:commission.withdraw.invalid_amount'),
        color: 'warning'
      })
      return
    }

    if (withdrawConfig && amount < parseFloat(withdrawConfig.minWithdrawAmount)) {
      addToast({
        title: t('referral:commission.withdraw.amount_too_small', { minAmount: withdrawConfig.minWithdrawAmount }),
        color: 'warning'
      })
      return
    }

    if (account && amount > parseFloat(account.availableBalance)) {
      addToast({
        title: t('referral:commission.withdraw.amount_too_large'),
        color: 'warning'
      })
      return
    }

    try {
      setIsSubmitting(true)
      
      const response = await apiService.referral.applyWithdraw({
        amount,
        bankInfo: {
          bankName,
          accountName,
          accountNumber,
          phone
        }
      })

      if (response.success) {
        addToast({
          title: t('referral:commission.withdraw.success'),
          description: t('referral:commission.withdraw.success_desc'),
          color: 'success'
        })
        onClose()
        // 重新加载数据
        loadCommissionData()
        // 清空表单
        setWithdrawAmount('')
        setBankName('')
        setAccountName('')
        setAccountNumber('')
        setPhone('')
      } else {
        addToast({
          title: t('referral:commission.withdraw.failed'),
          description: response.error,
          color: 'danger'
        })
      }
    } catch (error) {
      console.error('提现申请失败:', error)
      addToast({
        title: t('referral:commission.withdraw.failed'),
        color: 'danger'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'settled':
        return 'success'
      case 'pending':
        return 'warning'
      case 'cancelled':
        return 'danger'
      default:
        return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'settled':
        return t('referral:commission.status.settled')
      case 'pending':
        return t('referral:commission.status.pending')
      case 'cancelled':
        return t('referral:commission.status.cancelled')
      default:
        return status
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <Icon icon="lucide:loader-2" className="w-8 h-8 animate-spin text-primary" />
          <p className="text-default-500">{t('referral:common.loading')}</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 safe-area-top pb-20">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Button
          isIconOnly
          variant="light"
          className="text-default-500 hover:text-foreground"
          onPress={() => navigate('/referral')}
        >
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-foreground">{t('referral:commission.title')}</h1>
        <div className="w-10" /> {/* 占位符保持居中 */}
      </div>

      {/* 佣金账户概览 */}
      <div className="px-6 mb-6">
        <Card className="bg-gradient-to-r from-success-50 to-success-100 border-success-200">
          <CardBody className="p-6">
            <div className="text-center mb-4">
              <Icon icon="lucide:wallet" className="w-12 h-12 text-success mx-auto mb-2" />
              <h2 className="text-xl font-semibold text-success-700">{t('referral:commission.commission_account')}</h2>
            </div>
            
            {account && (
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-success-800">
                    ¥{account.availableBalance}
                  </p>
                  <p className="text-sm text-success-600">{t('referral:commission.available_balance')}</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-success-800">
                    ¥{account.totalEarned}
                  </p>
                  <p className="text-sm text-success-600">{t('referral:commission.total_income')}</p>
                </div>
              </div>
            )}

            {account && parseFloat(account.availableBalance) > 0 && (
              <div className="mt-4 text-center">
                <Button
                  color="success"
                  variant="solid"
                  onPress={onOpen}
                  startContent={<Icon icon="lucide:banknote" className="w-4 h-4" />}
                >
                  {t('referral:commission.apply_withdraw')}
                </Button>
              </div>
            )}
          </CardBody>
        </Card>
      </div>

      {/* 标签页 */}
      <div className="px-6">
        <Tabs
          selectedKey={activeTab}
          onSelectionChange={(key) => setActiveTab(key as string)}
          variant="underlined"
          classNames={{
            tabList: "gap-6 w-full relative rounded-none p-0 border-b border-divider",
            cursor: "w-full bg-primary",
            tab: "max-w-fit px-0 h-12",
            tabContent: "group-data-[selected=true]:text-primary"
          }}
        >
          <Tab key="overview" title={t('referral:commission.account_overview')}>
            <div className="mt-6 space-y-4">
              {account && (
                <>
                  <Card className="bg-content1/50 backdrop-blur-sm">
                    <CardBody className="p-4">
                      <div className="flex justify-between items-center">
                        <span className="text-default-600">{t('referral:commission.frozen_balance')}</span>
                        <span className="font-medium">¥{account.frozenBalance}</span>
                      </div>
                    </CardBody>
                  </Card>
                  
                  <Card className="bg-content1/50 backdrop-blur-sm">
                    <CardBody className="p-4">
                      <div className="flex justify-between items-center">
                        <span className="text-default-600">{t('referral:commission.withdrawn_amount')}</span>
                        <span className="font-medium">¥{account.totalWithdrawn}</span>
                      </div>
                    </CardBody>
                  </Card>
                </>
              )}
            </div>
          </Tab>
          
          <Tab key="records" title={t('referral:commission.commission_records')}>
            <div className="mt-6 space-y-3">
              {records.length === 0 ? (
                <Card className="bg-content1/50 backdrop-blur-sm">
                  <CardBody className="p-8 text-center">
                    <Icon icon="lucide:receipt" className="w-12 h-12 text-default-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-foreground mb-2">{t('referral:commission.no_records')}</h3>
                    <p className="text-default-500">{t('referral:commission.no_records_desc')}</p>
                  </CardBody>
                </Card>
              ) : (
                records.map((record) => (
                  <Card key={record.id} className="bg-content1/50 backdrop-blur-sm">
                    <CardBody className="p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <p className="font-medium text-foreground">
                            {record.orderType === 'membership' ? t('referral:commission.membership_order') : t('referral:commission.points_order')}
                          </p>
                          <p className="text-sm text-default-500">
                            {record.referredUserEmail?.replace(/(.{3}).*(@.*)/, '$1***$2')}
                          </p>
                          <p className="text-xs text-default-400">
                            {formatDate(record.createdAt)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-success">
                            +¥{record.commissionAmount}
                          </p>
                          <Chip
                            size="sm"
                            color={getStatusColor(record.status)}
                            variant="flat"
                          >
                            {getStatusText(record.status)}
                          </Chip>
                        </div>
                      </div>
                    </CardBody>
                  </Card>
                ))
              )}
            </div>
          </Tab>
        </Tabs>
      </div>

      {/* 提现申请模态框 */}
      <Modal isOpen={isOpen} onClose={onClose} placement="center">
        <ModalContent>
          <ModalHeader>{t('referral:commission.withdraw.title')}</ModalHeader>
          <ModalBody>
            <div className="space-y-4">
              <Input
                label={t('referral:commission.withdraw.amount')}
                placeholder={t('referral:commission.withdraw.amount_placeholder')}
                value={withdrawAmount}
                onChange={(e) => setWithdrawAmount(e.target.value)}
                type="number"
                startContent="¥"
                description={
                  withdrawConfig 
                    ? t('referral:commission.withdraw.min_amount', { amount: withdrawConfig.minWithdrawAmount }) + ', ' + 
                      t('referral:commission.withdraw.fee_description', { description: withdrawConfig.feeDescription })
                    : undefined
                }
              />
              
              <Input
                label={t('referral:commission.withdraw.bank_name')}
                placeholder={t('referral:commission.withdraw.bank_name_placeholder')}
                value={bankName}
                onChange={(e) => setBankName(e.target.value)}
              />
              
              <Input
                label={t('referral:commission.withdraw.account_name')}
                placeholder={t('referral:commission.withdraw.account_name_placeholder')}
                value={accountName}
                onChange={(e) => setAccountName(e.target.value)}
              />
              
              <Input
                label={t('referral:commission.withdraw.account_number')}
                placeholder={t('referral:commission.withdraw.account_number_placeholder')}
                value={accountNumber}
                onChange={(e) => setAccountNumber(e.target.value)}
              />
              
              <Input
                label={t('referral:commission.withdraw.phone')}
                placeholder={t('referral:commission.withdraw.phone_placeholder')}
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
              />
            </div>
          </ModalBody>
          <ModalFooter>
            <Button variant="light" onPress={onClose}>
              {t('referral:commission.withdraw.cancel')}
            </Button>
            <Button
              color="primary"
              onPress={handleWithdraw}
              isLoading={isSubmitting}
            >
              {t('referral:commission.withdraw.submit')}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* 底部空间 */}
      <div className="h-16" />
    </div>
  )
}

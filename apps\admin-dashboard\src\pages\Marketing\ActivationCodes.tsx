import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  Select,
  message,
  Modal,
  Form,
  InputNumber,
  DatePicker,
  Typography,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Badge,
  Radio,
  Descriptions
} from 'antd'
import {
  PlusOutlined,
  EyeOutlined,
  StopOutlined,
  Check<PERSON>ircleOutlined,
  GiftOutlined,
  CrownOutlined,
  SearchOutlined,
  ExportOutlined,
  CopyOutlined,
  FireOutlined,
  HistoryOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { ActivationCode, ActivationCodeListParams, ActivationCodeParams } from '@/services/activation'
import { activationService } from '@/services/activation'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

const { Title } = Typography
const { TextArea } = Input

const ActivationCodes: React.FC = () => {
  const [codes, setCodes] = useState<ActivationCode[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [modalVisible, setModalVisible] = useState(false)
  const [form] = Form.useForm()
  
  // 搜索条件
  const [searchParams, setSearchParams] = useState<ActivationCodeListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState({
    totalCodes: 0,
    membershipCodes: 0,
    pointsCodes: 0,
    usedCodes: 0,
    activeCodes: 0,
    todayUsed: 0,
    monthlyUsed: 0
  })

  // 套餐数据
  const [membershipPlans, setMembershipPlans] = useState<any[]>([])
  const [pointsPackages, setPointsPackages] = useState<any[]>([])

  useEffect(() => {
    loadActivationCodes()
    loadStats()
    loadPlansAndPackages()
  }, [currentPage, pageSize])
  
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1)
    } else {
      loadActivationCodes()
    }
  }, [searchParams])

  const loadActivationCodes = async () => {
    try {
      setLoading(true)
      
      const params = {
        page: currentPage,
        pageSize,
        ...searchParams
      }
      
      const response = await activationService.getActivationCodes(params)
      
      if (response.success && response.data) {
        setCodes(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取激活码列表失败')
      }
      setLoading(false)
    } catch (error) {
      console.error('获取激活码列表失败:', error)
      message.error('获取激活码列表失败')
      setLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await activationService.getStats()
      
      if (response.success && response.data) {
        setStats(response.data)
      } else {
        console.error('获取统计数据失败:', response.message)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const loadPlansAndPackages = async () => {
    try {
      const [plansResponse, packagesResponse] = await Promise.all([
        activationService.getMembershipPlans(),
        activationService.getPointsPackages()
      ])
      
      if (plansResponse.success && plansResponse.data) {
        setMembershipPlans(plansResponse.data)
      } else {
        console.error('获取会员套餐失败:', plansResponse.message)
      }
      
      if (packagesResponse.success && packagesResponse.data) {
        setPointsPackages(packagesResponse.data)
      } else {
        console.error('获取积分包失败:', packagesResponse.message)
      }
    } catch (error) {
      console.error('获取套餐数据失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    setSearchParams({
      ...searchParams,
      page: 1
    })
    loadActivationCodes()
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
    loadActivationCodes()
  }

  const handleCreate = () => {
    form.resetFields()
    setModalVisible(true)
  }

  const handleCreateSubmit = async (values: ActivationCodeParams) => {
    try {
      const params = {
        ...values,
        expiresAt: values.expiresAt ? dayjs(values.expiresAt).toISOString() : undefined
      }
      
      let response
      if (values.type === 'membership') {
        response = await activationService.createMembershipCode({
          membershipPlanId: values.membershipPlanId!,
          description: values.description,
          expiresAt: params.expiresAt,
          count: values.count
        })
      } else {
        response = await activationService.createPointsCode({
          pointsPackageId: values.pointsPackageId!,
          description: values.description,
          expiresAt: params.expiresAt,
          count: values.count
        })
      }
      
      if (response.success) {
        const count = values.count || 1
        message.success(`成功创建${count}个激活码`)
        setModalVisible(false)
        loadActivationCodes()
        loadStats()
      } else {
        message.error(response.message || '创建失败')
      }
    } catch (error) {
      console.error('创建激活码失败:', error)
      message.error('创建失败')
    }
  }

  const handleToggleStatus = async (id: string, isActive: boolean) => {
    try {
      const response = isActive 
        ? await activationService.enableCode(id)
        : await activationService.disableCode(id)
      
      if (response.success) {
        message.success(isActive ? '激活码已启用' : '激活码已禁用')
        loadActivationCodes()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('切换状态失败:', error)
      message.error('操作失败')
    }
  }

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code)
    message.success('激活码已复制到剪贴板')
  }

  const handleViewDetail = (activationCode: ActivationCode) => {
    Modal.info({
      title: '激活码详情',
      width: 700,
      content: (
        <div style={{ marginTop: 16 }}>
          <Descriptions column={2} bordered size="small">
            <Descriptions.Item label="激活码">{activationCode.code}</Descriptions.Item>
            <Descriptions.Item label="类型">
              <Tag color={activationCode.type === 'membership' ? 'gold' : 'blue'}>
                {activationCode.type === 'membership' ? '会员激活码' : '积分激活码'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="描述" span={2}>
              {activationCode.description || '无'}
            </Descriptions.Item>
            
            {activationCode.membershipPlan && (
              <>
                <Descriptions.Item label="会员套餐">{activationCode.membershipPlan.name}</Descriptions.Item>
                <Descriptions.Item label="有效期">{activationCode.membershipPlan.durationDays}天</Descriptions.Item>
                <Descriptions.Item label="包含积分" span={2}>
                  {activationCode.membershipPlan.pointsIncluded}积分
                </Descriptions.Item>
              </>
            )}

            {activationCode.pointsPackage && (
              <>
                <Descriptions.Item label="积分包">{activationCode.pointsPackage.name}</Descriptions.Item>
                <Descriptions.Item label="积分数量">{activationCode.pointsPackage.points}</Descriptions.Item>
                <Descriptions.Item label="奖励积分" span={2}>
                  {activationCode.pointsPackage.bonusPoints}积分
                </Descriptions.Item>
              </>
            )}

            <Descriptions.Item label="批次ID">
              {activationCode.batchId || '单独生成'}
            </Descriptions.Item>
            <Descriptions.Item label="使用状态">
              <Tag color={activationCode.isUsed ? 'green' : 'default'}>
                {activationCode.isUsed ? '已使用' : '未使用'}
              </Tag>
            </Descriptions.Item>

            {activationCode.usedBy && (
              <>
                <Descriptions.Item label="使用者">{activationCode.usedBy}</Descriptions.Item>
                <Descriptions.Item label="使用时间">
                  {dayjs(activationCode.usedAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              </>
            )}

            <Descriptions.Item label="过期时间" span={2}>
              {activationCode.expiresAt ? 
                dayjs(activationCode.expiresAt).format('YYYY-MM-DD HH:mm:ss') : 
                <Tag color="blue">永不过期</Tag>
              }
            </Descriptions.Item>

            <Descriptions.Item label="创建时间" span={2}>
              {dayjs(activationCode.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </Descriptions.Item>
          </Descriptions>
        </div>
      )
    })
  }

  const getTypeColor = (type: string) => {
    return type === 'membership' ? 'gold' : 'blue'
  }

  const getTypeText = (type: string) => {
    return type === 'membership' ? '会员' : '积分'
  }

  const columns: ColumnsType<ActivationCode> = [
    {
      title: '激活码',
      dataIndex: 'code',
      render: (code) => (
        <Space>
          <span style={{ fontFamily: 'monospace', fontWeight: 500 }}>{code}</span>
          <Tooltip title="复制激活码">
            <Button 
              type="link" 
              size="small"
              icon={<CopyOutlined />}
              onClick={() => handleCopyCode(code)}
            />
          </Tooltip>
        </Space>
      ),
    },
    {
      title: '类型',
      dataIndex: 'type',
      render: (type, record) => (
        <div>
          <Tag color={getTypeColor(type)} icon={type === 'membership' ? <CrownOutlined /> : <GiftOutlined />}>
            {getTypeText(type)}
          </Tag>
          <div style={{ color: '#999', fontSize: '12px', marginTop: 4 }}>
            {record.membershipPlan?.name || record.pointsPackage?.name}
          </div>
        </div>
      ),
    },
    {
      title: '批次',
      dataIndex: 'batchId',
      render: (batchId) => (
        batchId ? (
          <Tag color="purple">{batchId}</Tag>
        ) : (
          <span style={{ color: '#999' }}>单独生成</span>
        )
      ),
    },
    {
      title: '使用状态',
      key: 'usageStatus',
      render: (_, record) => (
        <div>
          <Badge 
            status={record.isUsed ? 'success' : 'default'} 
            text={record.isUsed ? '已使用' : '未使用'}
          />
          {record.isUsed && record.usedAt && (
            <div style={{ color: '#999', fontSize: '12px' }}>
              {dayjs(record.usedAt).fromNow()}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '过期时间',
      dataIndex: 'expiresAt',
      render: (expiresAt) => {
        if (!expiresAt) return <Tag color="blue">永不过期</Tag>
        
        const expireDate = dayjs(expiresAt)
        const now = dayjs()
        const isExpired = expireDate.isBefore(now)
        const daysLeft = expireDate.diff(now, 'day')
        
        return (
          <div>
            <div>{expireDate.format('YYYY-MM-DD')}</div>
            {!isExpired && (
              <div style={{ 
                color: daysLeft <= 3 ? '#f50' : daysLeft <= 7 ? '#fa8c16' : '#52c41a',
                fontSize: '12px'
              }}>
                {daysLeft}天后过期
              </div>
            )}
            {isExpired && (
              <Tag color="red">已过期</Tag>
            )}
          </div>
        )
      },
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: (date) => dayjs(date).format('MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.isActive ? (
            <Popconfirm
              title="确定禁用这个激活码吗？"
              onConfirm={() => handleToggleStatus(record.id, false)}
            >
              <Tooltip title="禁用">
                <Button 
                  type="link" 
                  danger 
                  icon={<StopOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          ) : (
            <Tooltip title="启用">
              <Button 
                type="link" 
                icon={<CheckCircleOutlined />}
                onClick={() => handleToggleStatus(record.id, true)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        激活码管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="总激活码数"
              value={stats.totalCodes}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="会员激活码"
              value={stats.membershipCodes}
              prefix={<CrownOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="积分激活码"
              value={stats.pointsCodes}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="已使用"
              value={stats.usedCodes}
              prefix={<FireOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="今日使用"
              value={stats.todayUsed}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="本月使用"
              value={stats.monthlyUsed}
              prefix={<HistoryOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索激活码"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={(e) => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />
          
          <Select
            placeholder="类型"
            style={{ width: 120 }}
            allowClear
            value={searchParams.type}
            onChange={(value) => setSearchParams({ ...searchParams, type: value })}
          >
            <Select.Option value="membership">会员</Select.Option>
            <Select.Option value="points">积分</Select.Option>
          </Select>

          <Select
            placeholder="使用状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isUsed}
            onChange={(value) => setSearchParams({ ...searchParams, isUsed: value })}
          >
            <Select.Option value={false}>未使用</Select.Option>
            <Select.Option value={true}>已使用</Select.Option>
          </Select>

          <Select
            placeholder="启用状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.isActive}
            onChange={(value) => setSearchParams({ ...searchParams, isActive: value })}
          >
            <Select.Option value={true}>启用</Select.Option>
            <Select.Option value={false}>禁用</Select.Option>
          </Select>

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>
          
          <Button onClick={handleReset}>
            重置
          </Button>
          
          <Button icon={<ExportOutlined />}>
            导出
          </Button>

          <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
            生成激活码
          </Button>
        </Space>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={codes}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 生成激活码模态框 */}
      <Modal
        title="生成激活码"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateSubmit}
          initialValues={{ count: 1 }}
        >
          <Form.Item
            name="type"
            label="激活码类型"
            rules={[{ required: true, message: '请选择激活码类型' }]}
          >
            <Radio.Group>
              <Radio.Button value="membership">
                <CrownOutlined /> 会员激活码
              </Radio.Button>
              <Radio.Button value="points">
                <GiftOutlined /> 积分激活码
              </Radio.Button>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type')
              
              if (type === 'membership') {
                return (
                  <Form.Item
                    name="membershipPlanId"
                    label="选择会员套餐"
                    rules={[{ required: true, message: '请选择会员套餐' }]}
                  >
                    <Select placeholder="选择会员套餐">
                      {membershipPlans.map(plan => (
                        <Select.Option key={plan.id} value={plan.id}>
                          {plan.name} - {plan.durationDays}天 - {plan.pointsIncluded}积分
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                )
              }

              if (type === 'points') {
                return (
                  <Form.Item
                    name="pointsPackageId"
                    label="选择积分包"
                    rules={[{ required: true, message: '请选择积分包' }]}
                  >
                    <Select placeholder="选择积分包">
                      {pointsPackages.map(pkg => (
                        <Select.Option key={pkg.id} value={pkg.id}>
                          {pkg.name} - {pkg.points}积分 + {pkg.bonusPoints}奖励
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>
                )
              }

              return null
            }}
          </Form.Item>

          <Form.Item
            name="count"
            label="生成数量"
            rules={[{ required: true, message: '请输入生成数量' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              max={1000}
              placeholder="1"
            />
          </Form.Item>

          <Form.Item
            name="expiresAt"
            label="过期时间"
            extra="留空则永不过期"
          >
            <DatePicker
              style={{ width: '100%' }}
              showTime
              placeholder="选择过期时间"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea 
              rows={3}
              placeholder="激活码用途说明..."
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                生成
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ActivationCodes
import React, { useState, useEffect } from 'react';
import { BleService } from './BleService';

interface BleCommandButtonProps {
  command: string;
  label: string;
  intensity?: number;
  className?: string;
}

/**
 * 蓝牙命令按钮组件
 * 用于发送蓝牙广播命令
 */
const BleCommandButton: React.FC<BleCommandButtonProps> = ({
  command,
  label,
  intensity = 1,
  className = '',
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isBluetoothEnabled, setIsBluetoothEnabled] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  // 获取蓝牙服务实例
  const bleService = BleService.getInstance();

  // 初始化蓝牙服务
  useEffect(() => {
    const initBluetooth = async () => {
      const initialized = await bleService.initialize();
      setIsInitialized(initialized);

      if (initialized) {
        const enabled = await bleService.isBluetoothEnabled();
        setIsBluetoothEnabled(enabled);
      }
    };

    initBluetooth();
  }, []);

  // 发送命令
  const handleSendCommand = async () => {
    if (!isInitialized || !isBluetoothEnabled) {
      console.error('蓝牙未初始化或未启用');
      return;
    }

    setIsLoading(true);

    try {
      // 根据强度选择广播模式
      const mode = intensity === 3 ? 1 : intensity === 2 ? 0 : 2;

      // 发送命令
      await bleService.sendCommand(command, mode);

      // 短暂延迟以显示加载状态
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
    } catch (error) {
      console.error('发送命令失败:', error);
      setIsLoading(false);
    }
  };

  return (
    <button
      onClick={handleSendCommand}
      disabled={isLoading || !isInitialized || !isBluetoothEnabled}
      className={`px-4 py-2 rounded-lg transition-all ${
        isLoading
          ? 'bg-gray-400 cursor-not-allowed'
          : !isInitialized || !isBluetoothEnabled
            ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
            : 'bg-pink-600 hover:bg-pink-700 text-white'
      } ${className}`}
    >
      {isLoading ? (
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full size-4 border-y-2 border-white mr-2" />
          <span>发送中...</span>
        </div>
      ) : (
        <span>{label}</span>
      )}
    </button>
  );
};

export default BleCommandButton;

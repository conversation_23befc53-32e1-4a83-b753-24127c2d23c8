import type { Env } from '@/types/env';
import { uploadToR2, type UploadOptions, getR2ConfigFromEnv } from '@/lib/utils/r2-upload';

// 在 Cloudflare Workers 环境中创建 Buffer 兼容对象
function createBuffer(data: ArrayBuffer | Uint8Array | string, encoding?: string): Uint8Array {
  if (data instanceof ArrayBuffer) {
    return new Uint8Array(data);
  }
  if (data instanceof Uint8Array) {
    return data;
  }
  if (typeof data === 'string') {
    if (encoding === 'base64') {
      // 处理 base64 字符串
      const binaryString = atob(data);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes;
    } else {
      // 处理普通字符串
      const encoder = new TextEncoder();
      return encoder.encode(data);
    }
  }
  throw new Error('不支持的数据类型');
}

/**
 * 下载图片URL并上传到Cloudflare R2 (使用S3 SDK)
 */
export async function downloadAndUploadImage(
  env: Env,
  imageUrl: string,
  folder = 'characters'
): Promise<string> {
  try {
    console.log('开始使用S3 SDK方式上传到R2...');

    // 获取R2配置
    const r2Config = getR2ConfigFromEnv(env);
    if (!r2Config) {
      throw new Error('R2配置未找到，请检查环境变量');
    }

    console.log('R2配置获取成功:', {
      accountId: r2Config.accountId,
      bucketName: r2Config.bucketName,
      publicBaseUrl: r2Config.publicBaseUrl,
    });

    // 下载图片
    console.log('开始下载图片:', imageUrl);
    const response = await fetch(imageUrl);
    if (!response.ok) {
      throw new Error(`下载图片失败: ${response.statusText}`);
    }

    // 获取图片数据和内容类型
    const imageData = await response.arrayBuffer();
    const contentType = response.headers.get('content-type') || 'image/png';
    console.log('图片下载完成，大小:', imageData.byteLength, '字节，类型:', contentType);

    // 转换为 Uint8Array（uploadToR2 期望的格式，在 Workers 环境中兼容 Buffer）
    const imageBuffer = createBuffer(imageData);

    // 设置上传选项 - 包含更宽泛的文件类型支持
    const uploadOptions: UploadOptions = {
      folder: folder,
      fileName: `generated_${Date.now()}.png`,
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/octet-stream', // 添加这个类型以支持某些图片服务器返回的通用类型
      ],
      makePublic: true,
    };

    // 使用 uploadToR2 函数上传，传入正确的内容类型
    const result = await uploadToR2(imageBuffer as any, r2Config, {
      ...uploadOptions,
      // 如果检测到的是 application/octet-stream，强制设置为 image/png
      fileName:
        contentType === 'application/octet-stream'
          ? `generated_${Date.now()}.png`
          : `generated_${Date.now()}.${getFileExtensionFromContentType(contentType)}`,
    });

    if (!result.success) {
      throw new Error(result.error || '上传失败');
    }

    console.log('上传成功:', {
      url: result.url,
      key: result.key,
      size: result.size,
    });

    return result.url!;
  } catch (error) {
    console.error('S3 SDK上传失败:', error);
    throw new Error(`S3 SDK上传失败: ${error instanceof Error ? error.message : '未知错误'}`);
  }
}

// 根据 Content-Type 获取文件扩展名的辅助函数
function getFileExtensionFromContentType(contentType: string): string {
  switch (contentType) {
    case 'image/jpeg':
      return 'jpg';
    case 'image/png':
      return 'png';
    case 'image/gif':
      return 'gif';
    case 'image/webp':
      return 'webp';
    default:
      return 'png'; // 默认使用 png
  }
}

/**
 * 将base64图片数据上传到Cloudflare R2 (使用S3 SDK)
 */
export async function uploadBase64Image(
  env: Env,
  base64Data: string,
  folder = 'characters'
): Promise<string> {
  try {
    // 获取R2配置
    const r2Config = getR2ConfigFromEnv(env);
    if (!r2Config) {
      throw new Error('R2配置未找到，请检查环境变量');
    }

    // 移除base64前缀（如果有）
    const base64Content = base64Data.includes(',') ? base64Data.split(',')[1] : base64Data;

    // 将base64转换为Uint8Array（在 Workers 环境中兼容 Buffer）
    const imageBuffer = createBuffer(base64Content, 'base64');

    // 设置上传选项
    const uploadOptions: UploadOptions = {
      folder: folder,
      fileName: `base64_${Date.now()}.png`,
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['image/png'],
      makePublic: true,
    };

    // 使用 uploadToR2 函数上传
    const result = await uploadToR2(imageBuffer as any, r2Config, uploadOptions);

    if (!result.success) {
      throw new Error(result.error || '上传失败');
    }

    return result.url!;
  } catch (error) {
    console.error('上传base64图片失败:', error);
    throw new Error('上传base64图片失败');
  }
}

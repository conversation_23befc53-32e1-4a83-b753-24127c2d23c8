import React, {
  useState,
  useRef,
  useCallback,
  useEffect,
  useMemo,
  forwardRef,
  useImperativeHandle
} from 'react'
import {
  Button,
  Progress,
  Chip,
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerBody,
  useDisclosure
} from '@heroui/react'
import { Icon } from '@iconify/react'
import cx from 'classnames'
import { useVoiceRecorder } from '@/hooks/useVoiceRecorder'
import { useAudioPermission } from '@/hooks/useAudioPermission'
import { useSpeechToText } from '@/hooks/useSpeechToText'
import { AudioWaveform } from './AudioWaveform'
import { useTranslation } from 'react-i18next'

interface VoiceInlineRecorderProps {
  /** 提交回调 */
  onSubmit: (audioBlob: Blob) => void
  /** 取消录制回调 */
  onCancel: () => void
  /** 状态变化回调 */
  onStateChange?: (state: ComponentState) => void
  /** 最大录制时长(秒) */
  maxDuration?: number
  /** 是否禁用 */
  disabled?: boolean
}

// 组件状态类型定义
type ComponentState =
  | 'idle'
  | 'click-recording'
  | 'click-recorded'
  | 'longpress-recording'
  | 'processing'

export interface VoiceInlineRecorderRef {
  getRecordedAudio: () => Blob | null
  hasRecordedAudio: () => boolean
  getDuration: () => number
  getCurrentState: () => ComponentState
  cancelRecording: () => void
}

/**
 * 内联语音录制组件
 * 支持点击和长按两种交互模式
 */
export const VoiceInlineRecorder = forwardRef<VoiceInlineRecorderRef, VoiceInlineRecorderProps>(
  ({ onSubmit, onCancel, onStateChange, maxDuration = 60, disabled = false }, ref) => {
    // 国际化
    const { t } = useTranslation('voice')
    
    // 权限管理
    const { state: permissionStatus, requestPermission } = useAudioPermission()

    // 录制管理
    const voiceRecorderOptions = useMemo(() => ({ maxDuration }), [maxDuration])
    const {
      status: recordingStatus,
      duration,
      volume,
      audioBlob,
      startRecording,
      stopRecording,
      cancelRecording,
      reset: resetRecording
    } = useVoiceRecorder(voiceRecorderOptions)

    // 语音转文本
    const {
      convert: convertToText,
      status: convertStatus,
      progress,
      error: convertError,
      reset: resetConverter
    } = useSpeechToText()

    // 统一的组件状态管理
    const [interactionMode, setInteractionMode] = useState<
      'idle' | 'click-recording' | 'click-recorded' | 'longpress-recording'
    >('idle')
    const [isInCancelZone, setIsInCancelZone] = useState(false)
    const [showError, setShowError] = useState(false)
    const [pendingSubmit, setPendingSubmit] = useState(false) // 标记长按录制完成待提交
    const [hasSubmitted, setHasSubmitted] = useState(false) // 防重复提交标记

    // 权限 Drawer 控制
    const {
      isOpen: isPermissionDrawerOpen,
      onOpen: openPermissionDrawer,
      onOpenChange: onPermissionDrawerChange
    } = useDisclosure()

    // 交互管理
    const longPressTimer = useRef<ReturnType<typeof setTimeout> | null>(null)
    const touchStartY = useRef<number>(0)
    const cancelZoneRef = useRef<HTMLDivElement>(null)
    const errorTimer = useRef<ReturnType<typeof setTimeout> | null>(null)

    // 计算当前组件状态
    const currentState = useMemo((): ComponentState => {
      // 只有录制状态需要显示处理中，转换在后台进行
      if (recordingStatus === 'processing') return 'processing'
      return interactionMode as ComponentState
    }, [recordingStatus, interactionMode])

    // 格式化录制时长显示
    const formatDuration = useCallback((seconds: number): string => {
      const mins = Math.floor(seconds / 60)
      const secs = Math.floor(seconds % 60)
      return `${mins}:${secs.toString().padStart(2, '0')}`
    }, [])

    // 显示错误信息
    const showErrorMessage = useCallback((message: string) => {
      setShowError(true)
      if (errorTimer.current) {
        clearTimeout(errorTimer.current)
      }
      errorTimer.current = setTimeout(() => {
        setShowError(false)
      }, 3000)
    }, [])

    // 处理权限请求
    const handlePermissionRequest = useCallback(async () => {
      try {
        const granted = await requestPermission()
        if (granted) {
          onPermissionDrawerChange() // 关闭权限抽屉
        }
        return granted
      } catch (error) {
        console.error(`${t('voice_recorder.get_microphone_permission_failed')}:`, error)
        return false
      }
    }, [requestPermission, onPermissionDrawerChange, t])

    // 检查权限并显示抽屉
    const checkPermissionAndProceed = useCallback(async () => {
      if (permissionStatus === 'granted') {
        return true
      }

      // 只有明确被拒绝时才显示抽屉
      if (permissionStatus === 'denied') {
        openPermissionDrawer()
        return false
      }

      // 权限状态未知，直接尝试请求
      try {
        const granted = await requestPermission()
        if (!granted) {
          openPermissionDrawer()
        }
        return granted
      } catch (error) {
        openPermissionDrawer()
        return false
      }
    }, [permissionStatus, requestPermission, openPermissionDrawer])

    // 开始录制
    const handleStartRecording = useCallback(async () => {
      // 检查权限
      if (permissionStatus !== 'granted') {
        // 如果权限状态未知，先尝试检查权限
        if (permissionStatus === 'unknown') {
          try {
            const granted = await requestPermission()
            if (!granted) {
              openPermissionDrawer()
              return false
            }
            // 权限获取成功，继续录制
          } catch (error) {
            openPermissionDrawer()
            return false
          }
        } else {
          // 权限明确被拒绝，显示抽屉
          openPermissionDrawer()
          return false
        }
      }

      try {
        // 手动激活AudioContext（移动端兼容性）
        if (window.AudioContext || (window as any).webkitAudioContext) {
          try {
            const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)()
            if (audioContext.state === 'suspended') {
              await audioContext.resume()
            }
            audioContext.close() // 只是为了激活，立即关闭
          } catch (error) {
            console.warn('AudioContext activation failed:', error)
          }
        }

        const success = await startRecording()
        return success
      } catch (error) {
          console.error(`${t('voice_recorder.start_recording_failed')}:`, error)
          showErrorMessage(error instanceof Error ? error.message : t('voice_recorder.recording_failed'))
        return false
      }
    }, [
      permissionStatus,
      requestPermission,
      openPermissionDrawer,
      startRecording,
      showErrorMessage,
      t
    ])

    // 停止录制
    const handleStopRecording = useCallback(() => {
      if (recordingStatus === 'recording') {
        stopRecording()
      }
    }, [recordingStatus, stopRecording])

    // 重置所有状态
    const resetAllStates = useCallback(() => {
      // 使用 cancelRecording 而不是 reset，确保完全清理录制器状态
      if (recordingStatus === 'recording' || recordingStatus === 'processing') {
        cancelRecording()
      } else {
        resetRecording()
      }
      resetConverter()
      setInteractionMode('idle')
      setIsInCancelZone(false)
      setShowError(false)
      setPendingSubmit(false)
      if (errorTimer.current) {
        clearTimeout(errorTimer.current)
      }
    }, [recordingStatus, cancelRecording, resetRecording, resetConverter])

    // 取消录制
    const handleCancelRecording = useCallback(() => {
      resetAllStates()
      // 状态重置后会触发 useEffect 通知父组件
    }, [resetAllStates])

    // 点击处理
    const handleClick = useCallback(() => {
      if (disabled || currentState === 'processing') return

      switch (interactionMode) {
        case 'idle':
          // 开始点击录制
          setInteractionMode('click-recording')
          handleStartRecording()
          break
        case 'click-recording':
          // 停止录制，显示操作按钮
          handleStopRecording()
          setInteractionMode('click-recorded')
          break
        case 'click-recorded':
          if (audioBlob && !hasSubmitted) {
            setHasSubmitted(true) // 标记已提交，防重复
            onSubmit(audioBlob)
            setTimeout(() => {
              setInteractionMode('idle')
              setHasSubmitted(false) // 重置提交标记
            }, 50)
          }
          break
      }
    }, [disabled, currentState, interactionMode, handleStartRecording, handleStopRecording])

    // 长按开始
    const handleTouchStart = useCallback(
      (e: React.TouchEvent | React.MouseEvent) => {
        if (disabled || currentState === 'processing' || interactionMode !== 'idle') return

        const startY = 'touches' in e ? e.touches[0].clientY : (e as React.MouseEvent).clientY
        touchStartY.current = startY

        longPressTimer.current = setTimeout(() => {
          setInteractionMode('longpress-recording')
          handleStartRecording()
          // 触觉反馈
          if ('vibrate' in navigator) {
            navigator.vibrate(50)
          }
        }, 200) // 增加长按延迟，避免误触
      },
      [disabled, currentState, interactionMode, handleStartRecording]
    )

    // 长按结束
    const handleTouchEnd = useCallback(() => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
        longPressTimer.current = null
      }

      if (interactionMode === 'longpress-recording') {
        if (isInCancelZone) {
          // 在取消区域松开 - 取消录制
          handleCancelRecording()
        } else if (duration >= 0.5) {
          // 在其他区域松开且时长足够 - 停止录制并标记待提交
          handleStopRecording()
          setPendingSubmit(true) // 标记为待提交
          setInteractionMode('idle')
        } else {
          // 时长不足 - 取消
          showErrorMessage(t('voice_recorder.recording_too_short'))
          handleCancelRecording()
        }
        setIsInCancelZone(false)
      }
    }, [
      interactionMode,
      isInCancelZone,
      duration,
      handleCancelRecording,
      handleStopRecording,
      showErrorMessage,
      t
    ])

    // 长按移动
    const handleTouchMove = useCallback(
      (e: React.TouchEvent | React.MouseEvent) => {
        if (interactionMode !== 'longpress-recording') return

        const currentY = 'touches' in e ? e.touches[0].clientY : (e as React.MouseEvent).clientY
        const currentX = 'touches' in e ? e.touches[0].clientX : (e as React.MouseEvent).clientX

        // 检查是否在取消区域
        if (cancelZoneRef.current) {
          const rect = cancelZoneRef.current.getBoundingClientRect()
          const inCancelZone =
            currentX >= rect.left &&
            currentX <= rect.right &&
            currentY >= rect.top &&
            currentY <= rect.bottom
          setIsInCancelZone(inCancelZone)
        }
      },
      [interactionMode]
    )

    // 清理定时器
    useEffect(() => {
      return () => {
        if (longPressTimer.current) {
          clearTimeout(longPressTimer.current)
        }
        if (errorTimer.current) {
          clearTimeout(errorTimer.current)
        }
      }
    }, [])

    // 处理转换错误
    useEffect(() => {
      if (convertError) {
        showErrorMessage(convertError)
      }
    }, [convertError, showErrorMessage])

    // 长按录制完成后自动提交
    useEffect(() => {
      if (audioBlob && recordingStatus === 'completed' && pendingSubmit && !hasSubmitted) {
        // 长按录制刚完成，立即提交
        setHasSubmitted(true) // 标记已提交，防重复
        onSubmit(audioBlob)
        // 重置状态
        setPendingSubmit(false)
        // 延迟重置其他状态，避免立即重新触发
        setTimeout(() => {
          resetAllStates()
          setHasSubmitted(false) // 重置提交标记
        }, 50)
      }
    }, [audioBlob, recordingStatus, pendingSubmit, hasSubmitted, onSubmit])

    // 状态变化时通知父组件
    useEffect(() => {
      if (onStateChange) {
        onStateChange(currentState)
      }
    }, [currentState, onStateChange])

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        getCurrentState: () => currentState,
        getRecordedAudio: () => audioBlob || null,
        hasRecordedAudio: () => !!audioBlob,
        getDuration: () => duration,
        cancelRecording: () => handleCancelRecording()
      }),
      [audioBlob, duration]
    )

    // 状态指示器
    const stateIndicator = useMemo(() => {
      switch (currentState) {
        case 'idle':
          return {
            text: permissionStatus === 'granted' ? t('voice_recorder.click_to_record') : t('voice_recorder.click_to_start_recording'),
            color: 'text-default-400'
          }
        case 'click-recording':
          return {
            text: t('voice_recorder.click_to_stop'),
            color: 'text-primary-600' // 统一使用主题色
          }
        case 'longpress-recording':
          return {
            text: isInCancelZone ? t('voice_recorder.release_to_cancel') : t('voice_recorder.release_to_send'),
            color: isInCancelZone ? 'text-warning-600' : 'text-primary-600' // 统一使用主题色
          }
        case 'processing':
          return {
            text: t('voice_recorder.processing_audio'),
            color: 'text-primary-600'
          }
        case 'click-recorded':
          return {
            text: '', // 移除"录制完成"文案
            color: 'text-primary-600' // 统一使用主题色
          }
        default:
          return {
            text: t('voice_recorder.ready'),
            color: 'text-default-400'
          }
      }
    }, [currentState, permissionStatus, isInCancelZone, t])

    return (
      <div className="relative flex-1 min-w-0 select-none">
        {/* 长按模式的悬浮取消按钮 */}
        {interactionMode === 'longpress-recording' && (
          <div className="absolute -top-16 left-1/2 transform -translate-x-1/2 z-[10000]">
            <div
              ref={cancelZoneRef}
              className={cx(
                'flex items-center justify-center bg-background rounded-full shadow-lg border px-4 py-2 transition-all duration-200',
                isInCancelZone
                  ? 'border-danger-200 bg-danger-50 scale-110'
                  : 'border-divider scale-100'
              )}
            >
              <Icon
                icon="material-symbols:close"
                width={16}
                className={cx(
                  'transition-colors',
                  isInCancelZone ? 'text-danger-600' : 'text-default-500'
                )}
              />
              <span
                className={cx(
                  'ml-2 text-sm font-medium transition-colors',
                  isInCancelZone ? 'text-danger-600' : 'text-default-600'
                )}
              >
                {t('voice_recorder.cancel')}
              </span>
            </div>
          </div>
        )}

        {/* 主录制区域 */}
        <div
          className={cx(
            'flex items-center gap-3 rounded-xl border border-divider bg-background p-2 min-h-[38px] cursor-pointer transition-all duration-200',
            'hover:bg-default-50 active:scale-[0.98]',
            currentState === 'click-recording' && 'bg-primary-50 border-primary-200', // 统一使用主题色
            currentState === 'click-recorded' && 'bg-primary-50 border-primary-200',
            currentState === 'longpress-recording' &&
              (isInCancelZone
                ? 'bg-warning-50 border-warning-200'
                : 'bg-primary-50 border-primary-200'), // 统一使用主题色
            currentState === 'processing' && 'bg-primary-50 border-primary-200',
            (disabled || currentState === 'processing') && 'cursor-not-allowed opacity-60'
          )}
          onClick={handleClick}
          onTouchStart={handleTouchStart}
          onTouchEnd={handleTouchEnd}
          onTouchMove={handleTouchMove}
          onMouseDown={handleTouchStart}
          onMouseUp={handleTouchEnd}
          onMouseMove={handleTouchMove}
          onMouseLeave={handleTouchEnd}
        >
          {/* 状态显示区域 */}
          <div
            className={cx(
              'flex-1 flex items-center justify-between',
              (!stateIndicator.text || currentState === 'idle') && 'justify-center'
            )}
          >
            {/* 左侧状态 */}
            {stateIndicator.text && (
              <div className="flex items-center gap-3">
                {/* 状态文字 */}
                <p className={cx('text-xs font-medium transition-colors', stateIndicator.color)}>
                  {stateIndicator.text}
                </p>

                {/* 时长显示 */}
                {(currentState === 'click-recording' || currentState === 'longpress-recording') && (
                  <Chip
                    size="sm"
                    variant="flat"
                    color={duration > maxDuration * 0.8 ? 'warning' : 'default'}
                    className="text-xs"
                  >
                    {formatDuration(duration)}
                  </Chip>
                )}
              </div>
            )}

            {/* 右侧指示 */}
            {currentState !== 'idle' && (
              <div className="flex items-center gap-2">
                {/* 内联操作按钮 - 点击录制完成后只显示发送按钮 */}
                {currentState === 'click-recorded' && (
                  <Icon
                    className="text-primary-foreground"
                    icon="solar:arrow-up-linear"
                    width={18}
                  />
                )}

                {/* 波形 */}
                {(currentState === 'click-recording' || currentState === 'longpress-recording') && (
                  <AudioWaveform
                    volume={volume}
                    isActive={true}
                    color={isInCancelZone ? 'warning' : 'primary'} // 统一使用主题色
                    bars={4}
                    testMode={volume < 0.01} // 如果音量太小，启用测试模式
                  />
                )}

                {/* 处理中动画 */}
                {currentState === 'processing' && (
                  <div className="flex items-center gap-1">
                    {[0, 1, 2].map(i => (
                      <div
                        key={i}
                        className="w-1 h-3 bg-primary-400 rounded-full animate-pulse"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* 错误提示 */}
        {showError && (convertError || showError) && (
          <div className="absolute -top-12 left-0 right-0 bg-danger/10 border border-danger/20 rounded-lg p-2 text-center animate-in slide-in-from-top duration-200 z-[10000]">
            <p className="text-xs text-danger font-medium">{convertError || t('voice_recorder.operation_failed')}</p>
          </div>
        )}

        {/* 权限请求 Drawer */}
        <Drawer
          isOpen={isPermissionDrawerOpen}
          onOpenChange={onPermissionDrawerChange}
          placement="bottom"
          size="sm"
        >
          <DrawerContent>
            {onClose => (
              <>
                <DrawerHeader className="flex flex-col gap-1">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-warning/10 rounded-full flex items-center justify-center">
                      <Icon icon="solar:microphone-off-bold" className="text-warning text-xl" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{t('voice_recorder.need_microphone_permission')}</h3>
                      <p className="text-sm text-default-500">{t('voice_recorder.voice_recording_needs_mic')}</p>
                    </div>
                  </div>
                </DrawerHeader>
                <DrawerBody className="pb-6">
                  <div className="space-y-4">
                    <div className="bg-default-50 rounded-lg p-4">
                      <div className="flex items-start gap-3">
                        <Icon
                          icon="solar:shield-check-linear"
                          className="text-success text-xl mt-0.5"
                        />
                        <div>
                          <p className="text-sm font-medium text-default-700">{t('voice_recorder.privacy_protection')}</p>
                          <p className="text-xs text-default-500 mt-1">
                            {t('voice_recorder.privacy_description')}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="flex gap-3">
                      <Button
                        variant="light"
                        onPress={() => {
                          onClose()
                          onCancel() // 调用取消回调，退出录音状态
                        }}
                        className="flex-1"
                      >
                        {t('voice_recorder.cancel')}
                      </Button>
                      <Button
                        color="primary"
                        onPress={handlePermissionRequest}
                        className="flex-1"
                        startContent={<Icon icon="solar:microphone-bold" width={18} />}
                      >
                        {t('voice_recorder.allow_microphone_access')}
                      </Button>
                    </div>
                  </div>
                </DrawerBody>
              </>
            )}
          </DrawerContent>
        </Drawer>
      </div>
    )
  }
)

/**
 * 支付结果页面
 * 显示支付结果并提供返回APP的选项
 */

import { useEffect, useState } from 'react'
import { useSearchParams } from 'react-router'
import { Card, CardBody, Button, Spinner, Chip, Progress } from '@heroui/react'
import { Check, X, Minus, Clock, HelpCircle, FileText } from 'lucide-react'
import { paymentAPI } from '@/api/payment'
import { openAppDeepLink } from '@/utils/deeplink'
import {
  isFromPaymentReturn,
  detectPaymentMethod,
  getPaymentResultFromUrl
} from '@/utils/payment-redirect'
import type { PaymentStatus, PaymentOrderInfo } from '@/types/payment'

export default function PaymentResult() {
  const [searchParams] = useSearchParams()
  const [loading, setLoading] = useState(true)
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null)
  const [orderInfo, setOrderInfo] = useState<PaymentOrderInfo | null>(null)
  const [error, setError] = useState<string>('')
  const [polling, setPolling] = useState(false)
  const [pollingProgress, setPollingProgress] = useState(0)

  const orderId = searchParams.get('orderId')
  const urlStatus = searchParams.get('status')

  useEffect(() => {
    if (!orderId) {
      setError('订单ID不存在')
      setLoading(false)
      return
    }

    initializePage()
  }, [orderId])

  const initializePage = async () => {
    try {
      setLoading(true)
      setError('')

      console.log('📋 [PAY-RESULT] 初始化支付结果页面:', {
        orderId,
        urlStatus,
        isFromPayment: isFromPaymentReturn(),
        paymentMethod: detectPaymentMethod()
      })

      // 先获取订单信息
      try {
        const info = await paymentAPI.getOrderInfo(orderId!)
        setOrderInfo(info)
      } catch (error) {
        console.warn('⚠️ [PAY-RESULT] 获取订单信息失败，继续查询支付状态', error)
      }

      // 获取支付结果参数
      const paymentResult = getPaymentResultFromUrl()
      console.log('🔍 [PAY-RESULT] URL参数解析结果:', paymentResult)

      // 查询支付状态
      await checkPaymentStatus()
    } catch (error) {
      console.error('❌ [PAY-RESULT] 初始化失败:', error)
      setError(error instanceof Error ? error.message : '页面初始化失败')
    } finally {
      setLoading(false)
    }
  }

  const checkPaymentStatus = async () => {
    try {
      console.log('🔍 [PAY-RESULT] 检查支付状态:', orderId)

      const status = await paymentAPI.getPaymentStatus(orderId!)
      setPaymentStatus(status)

      // 如果支付状态未确定，开始轮询
      if (status.status === 'pending') {
        startPolling()
      }
    } catch (error) {
      console.error('❌ [PAY-RESULT] 查询支付状态失败:', error)
      setError('查询支付状态失败')
    }
  }

  const startPolling = async () => {
    if (polling || !orderId) return

    try {
      setPolling(true)
      setPollingProgress(0)

      console.log('⏰ [PAY-RESULT] 开始轮询支付状态')

      let attempts = 0
      const maxAttempts = 60 // 3分钟

      const result = await paymentAPI.pollPaymentStatus(orderId, {
        interval: 3000,
        maxAttempts,
        onStatusChange: status => {
          attempts++
          setPollingProgress((attempts / maxAttempts) * 100)
          setPaymentStatus(status)

          console.log('📊 [PAY-RESULT] 轮询状态更新:', {
            attempts,
            status: status.status,
            progress: (attempts / maxAttempts) * 100
          })
        }
      })

      setPaymentStatus(result)
      console.log('✅ [PAY-RESULT] 轮询结束，最终状态:', result.status)
    } catch (error) {
      console.error('❌ [PAY-RESULT] 轮询失败:', error)
      setError('支付状态查询超时')
    } finally {
      setPolling(false)
      setPollingProgress(100)
    }
  }

  const handleReturnToApp = async () => {
    console.log('📱 [PAY-RESULT] 准备返回APP:', {
      orderId,
      status: paymentStatus?.status
    })

    try {
      // 直接尝试打开深度链接
      const success = await openAppDeepLink({
        orderId: orderId || undefined,
        status: paymentStatus?.status === 'completed' ? 'success' : 'failed',
        amount: paymentStatus?.amount,
        planName: orderInfo?.planInfo?.name
      })

      if (!success) {
        console.log('⚠️ [PAY-RESULT] 无法打开APP，可能未安装')
        // 可以显示提示信息，但不跳转到下载页面
      }
    } catch (error) {
      console.error('❌ [PAY-RESULT] 返回APP失败:', error)
    }
  }

  const handleRefresh = () => {
    setError('')
    checkPaymentStatus()
  }

  const getStatusDisplay = () => {
    if (!paymentStatus) return null

    switch (paymentStatus.status) {
      case 'completed':
        return {
          title: '支付成功',
          icon: Check,
          color: 'success' as const,
          bgColor: 'from-green-900/30 to-emerald-900/30',
          borderColor: 'border-green-700/50',
          iconBg: 'bg-green-900/50',
          iconColor: 'text-green-400'
        }
      case 'failed':
        return {
          title: '支付失败',
          icon: X,
          color: 'danger' as const,
          bgColor: 'from-red-900/30 to-rose-900/30',
          borderColor: 'border-red-700/50',
          iconBg: 'bg-red-900/50',
          iconColor: 'text-red-400'
        }
      case 'cancelled':
        return {
          title: '支付取消',
          icon: Minus,
          color: 'warning' as const,
          bgColor: 'from-orange-900/30 to-amber-900/30',
          borderColor: 'border-orange-700/50',
          iconBg: 'bg-orange-900/50',
          iconColor: 'text-orange-400'
        }
      case 'expired':
        return {
          title: '订单过期',
          icon: Clock,
          color: 'danger' as const,
          bgColor: 'from-gray-900/30 to-slate-900/30',
          borderColor: 'border-gray-700/50',
          iconBg: 'bg-gray-900/50',
          iconColor: 'text-gray-400'
        }
      case 'pending':
        return {
          title: '处理中',
          icon: Clock,
          color: 'warning' as const,
          bgColor: 'from-blue-900/30 to-indigo-900/30',
          borderColor: 'border-blue-700/50',
          iconBg: 'bg-blue-900/50',
          iconColor: 'text-blue-400'
        }
      default:
        return {
          title: '状态未知',
          icon: HelpCircle,
          color: 'default' as const,
          bgColor: 'from-gray-900/30 to-slate-900/30',
          borderColor: 'border-gray-700/50',
          iconBg: 'bg-gray-900/50',
          iconColor: 'text-gray-400'
        }
    }
  }

  const formatAmount = (amount?: number) => {
    if (!amount) return '--'
    return `¥${amount.toFixed(2)}`
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800">
        <Card className="w-full max-w-sm mx-4 bg-gray-900/90 backdrop-blur-xl border-gray-700">
          <CardBody className="text-center py-8">
            <Spinner size="md" color="primary" className="mb-3" />
            <p className="text-white text-sm">查询支付状态</p>
            <p className="text-gray-400 text-xs mt-1">请稍候...</p>
          </CardBody>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-900 via-black to-gray-800 p-4">
        <Card className="w-full max-w-sm bg-gray-900/90 backdrop-blur-xl border-gray-700">
          <CardBody className="text-center py-6">
            <div className="w-12 h-12 mx-auto mb-4 rounded-full bg-red-900/50 flex items-center justify-center">
              <X className="w-6 h-6 text-red-400" />
            </div>
            <h2 className="text-lg font-bold text-red-400 mb-2">查询失败</h2>
            <p className="text-gray-300 text-sm mb-6 leading-relaxed">{error}</p>
            <div className="flex gap-2 justify-center">
              <Button color="primary" size="sm" onPress={handleRefresh} className="px-6">
                重新查询
              </Button>
              <Button
                variant="bordered"
                size="sm"
                onPress={handleReturnToApp}
                className="px-6 border-gray-600 text-gray-300"
              >
                返回APP
              </Button>
            </div>
          </CardBody>
        </Card>
      </div>
    )
  }

  const statusDisplay = getStatusDisplay()

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 p-3">
      <div className="max-w-sm mx-auto space-y-4">
        {/* 支付结果卡片 */}
        <Card className="bg-gray-900/90 backdrop-blur-xl border-gray-700">
          <CardBody className="text-center py-4">
            {statusDisplay && (
              <>
                <div className="flex items-center justify-center gap-3 mb-3">
                  <div
                    className={`w-10 h-10 ${statusDisplay.iconBg} rounded-full flex items-center justify-center`}
                  >
                    <statusDisplay.icon className={`w-5 h-5 ${statusDisplay.iconColor}`} />
                  </div>
                  <div className="text-left">
                    <h1 className="text-lg font-bold text-white">{statusDisplay.title}</h1>
                  </div>
                </div>

                <div className="flex justify-center">
                  <Chip color={statusDisplay.color} size="sm" className="text-xs">
                    {paymentStatus?.status.toUpperCase()}
                  </Chip>
                </div>
              </>
            )}

            {/* 轮询进度 - 紧凑版 */}
            {polling && (
              <div className="mt-4 p-3 rounded-lg bg-blue-900/20 border border-blue-700/50">
                <div className="flex items-center gap-2 mb-2">
                  <Spinner size="sm" color="primary" />
                  <span className="text-white text-xs">正在确认支付结果</span>
                </div>
                <Progress
                  value={pollingProgress}
                  className="mb-1"
                  color="primary"
                  size="sm"
                  classNames={{
                    track: 'bg-gray-700',
                    indicator: 'bg-gradient-to-r from-primary-500 to-secondary-500'
                  }}
                />
                <p className="text-xs text-gray-400">{Math.round(pollingProgress)}%</p>
              </div>
            )}
          </CardBody>
        </Card>

        {/* 订单详情卡片 */}
        {(paymentStatus || orderInfo) && (
          <Card className="bg-gray-900/90 backdrop-blur-xl border-gray-700">
            <CardBody className="p-4">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-primary-900/50 rounded-full flex items-center justify-center">
                  <FileText className="w-3 h-3 text-primary-400" />
                </div>
                <h3 className="text-lg font-bold text-white">订单详情</h3>
              </div>

              <div className="space-y-3">
                {orderId && (
                  <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/50 border border-gray-700">
                    <span className="text-gray-400 text-sm">订单号</span>
                    <span className="font-mono text-xs text-white bg-gray-700/50 px-2 py-1 rounded max-w-[180px] truncate">
                      {orderId}
                    </span>
                  </div>
                )}

                {orderInfo?.description && (
                  <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/50 border border-gray-700">
                    <span className="text-gray-400 text-sm">商品</span>
                    <span className="text-white text-sm font-medium text-right flex-1 ml-3 truncate">
                      {orderInfo.description}
                    </span>
                  </div>
                )}

                {orderInfo?.planInfo && (
                  <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/50 border border-gray-700">
                    <span className="text-gray-400 text-sm">套餐</span>
                    <span className="text-white text-sm font-medium text-right flex-1 ml-3 truncate">
                      {orderInfo.planInfo.name}
                    </span>
                  </div>
                )}

                {paymentStatus?.amount && (
                  <div className="flex justify-between items-center p-4 rounded-xl bg-gradient-to-r from-primary-900/30 to-secondary-900/30 border border-primary-700/50">
                    <span className="text-white text-sm font-medium">支付金额</span>
                    <span className="text-xl font-bold text-white">
                      {formatAmount(paymentStatus.amount)}
                    </span>
                  </div>
                )}

                {paymentStatus?.paidAt && (
                  <div className="flex justify-between items-center p-3 rounded-lg bg-gray-800/50 border border-gray-700">
                    <span className="text-gray-400 text-sm">支付时间</span>
                    <span className="text-white text-xs">
                      {new Date(paymentStatus.paidAt).toLocaleString()}
                    </span>
                  </div>
                )}
              </div>
            </CardBody>
          </Card>
        )}

        {/* 操作按钮 */}
        <div className="space-y-3">
          <Button
            color="primary"
            size="lg"
            className="w-full font-semibold bg-gradient-to-r from-primary-500 to-secondary-500"
            onPress={handleReturnToApp}
          >
            {paymentStatus?.status === 'completed' ? '返回应用' : '返回应用'}
          </Button>

          {paymentStatus?.status === 'pending' && !polling && (
            <Button
              variant="bordered"
              size="md"
              className="w-full border-gray-600 text-gray-300"
              onPress={handleRefresh}
            >
              刷新状态
            </Button>
          )}

          {['failed', 'cancelled', 'expired'].includes(paymentStatus?.status || '') && (
            <Button
              variant="bordered"
              size="md"
              className="w-full border-orange-600 text-orange-300"
              onPress={() => window.history.back()}
            >
              重新支付
            </Button>
          )}
        </div>

        {/* 客服联系卡片 */}
        {['failed', 'expired'].includes(paymentStatus?.status || '') && (
          <Card className="bg-gray-900/70 backdrop-blur-xl border-gray-700">
            <CardBody className="p-3">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-900/50 rounded-full flex items-center justify-center flex-shrink-0">
                  <HelpCircle className="w-4 h-4 text-blue-400" />
                </div>
                <div>
                  <h3 className="text-white text-sm font-medium mb-0.5">需要帮助？</h3>
                  <p className="text-gray-400 text-xs leading-relaxed">如遇支付问题，请联系客服</p>
                </div>
              </div>
            </CardBody>
          </Card>
        )}
      </div>
    </div>
  )
}

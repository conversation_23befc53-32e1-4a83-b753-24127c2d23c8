import type { Script, ScriptId, Stage } from '@/types/dialogue';

// 导入剧本数据
import homestayData from '@/lib/scripts/homestay.json';
import dialogueWithMultiIntensityData from '@/lib/scripts/dialogue_with_multi_intensity.json';
import airportEncounterData from '@/lib/scripts/airport_encounter.json';

// 样本1：民宿激情
const dialogue1: Script = {
  mp3: 'https://asset.pleasurehub.app/play/homestay/homestay-CsX18dwmgCVVtxOH0wxfarWtmtnGRT.MP3',
  stages: homestayData as Stage[],
};

// 样本2：教授
const dialogue2: Script = {
  mp3: 'https://asset.pleasurehub.app/play/dialogue_with_multi_intensity/professor-2dtxzTbZMdPo8EveiHueCJM43uZ9Tt.MP3',
  stages: dialogueWithMultiIntensityData as Stage[],
};

// 样本3：机场商务
const dialogue3: Script = {
  mp3: 'https://asset.pleasurehub.app/play/airport_encounter/lesbian-GwRPZ44IZ9WUlf708V0MvKurV2r8Lx.MP3',
  stages: airportEncounterData as Stage[],
};

// 剧本数据映射
const scriptMap: Record<ScriptId, Script> = {
  '1': dialogue1,
  '2': dialogue2,
  '3': dialogue3,
};

/**
 * 获取指定ID的剧本
 */
export function getScriptById(scriptId: ScriptId): Script | null {
  return scriptMap[scriptId] || null;
}

/**
 * 获取默认剧本（民宿激情）
 */
export function getDefaultScript(): Script {
  return dialogue1;
}

/**
 * 获取所有可用的剧本ID
 */
export function getAvailableScriptIds(): ScriptId[] {
  return Object.keys(scriptMap) as ScriptId[];
}

/**
 * 检查剧本ID是否有效
 */
export function isValidScriptId(id: string): id is ScriptId {
  return id in scriptMap;
}

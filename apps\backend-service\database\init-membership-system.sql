-- ==========================================
-- 会员/积分系统初始化 SQL
-- 执行环境: Supabase Dashboard > SQL Editor
-- 创建时间: 2024-06-21
-- ==========================================

-- 清理现有数据（可选，谨慎使用）
-- DELETE FROM "PointsTransaction";
-- DELETE FROM "UserPoints";
-- DELETE FROM "SubscriptionHistory";
-- DELETE FROM "UserSubscription";
-- DELETE FROM "PointsPackage";
-- DELETE FROM "MembershipPlan";
-- DELETE FROM "SystemConfig";

-- ==========================================
-- 1. 会员套餐初始化
-- ==========================================

-- 注意：Free 用户不是会员，不需要 Free 会员套餐
-- Free 用户的权限和限制通过系统配置管理，不通过会员套餐

INSERT INTO "MembershipPlan" (
    "id",
    "name", 
    "description",
    "price",
    "duration_days",
    "points_included",
    "features",
    "is_active",
    "sort_order"
) VALUES
(
    gen_random_uuid(),
    'pro',
    'Pro 会员 - 专业版，解锁更多创作可能',
    38.00,
    30,
    400,
    '["5个角色", "无限文本对话", "图片生成", "语音生成"]'::json,
    true,
    1
),
(
    gen_random_uuid(),
    'elite',
    'Elite 会员 - 精英版，享受高级功能',
    98.00,
    30,
    1200,
    '["20个角色", "Pro所有功能", "写真集生成", "优先支持"]'::json,
    true,
    2
),
(
    gen_random_uuid(),
    'ultra',
    'Ultra 会员 - 至尊版，体验全部功能',
    188.00,
    30,
    3000,
    '["无限角色", "Elite所有功能", "专属客服", "最新功能抢先体验"]'::json,
    true,
    3
);

-- ==========================================
-- 2. 积分包初始化
-- ==========================================

INSERT INTO "PointsPackage" (
    "id",
    "name",
    "description",
    "points",
    "price",
    "bonus_points",
    "is_active",
    "sort_order"
) VALUES 
(
    gen_random_uuid(),
    '基础积分包',
    '适合轻度使用',
    100,
    9.90,
    0,
    true,
    1
),
(
    gen_random_uuid(),
    '标准积分包',
    '性价比推荐',
    300,
    25.00,
    30,
    true,
    2
),
(
    gen_random_uuid(),
    '高级积分包',
    '重度用户首选',
    600,
    45.00,
    100,
    true,
    3
),
(
    gen_random_uuid(),
    '超值积分包',
    '最优惠选择',
    1200,
    80.00,
    300,
    true,
    4
);

-- ==========================================
-- 3. 系统配置初始化
-- ==========================================

INSERT INTO "SystemConfig" (
    "key",
    "value",
    "description",
    "is_public"
) VALUES 
-- 积分消费配置
(
    'IMAGE_GENERATION',
    '10',
    '图片生成积分消费',
    true
),
(
    'VOICE_GENERATION',
    '5',
    '语音生成积分消费',
    true
),
-- 注意：剧本购买每个剧本价格不同，不设置统一价格
-- 剧本价格在剧本表中单独设置
(
    'GALLERY_GENERATION',
    '15',
    '写真集生成积分消费',
    true
),
(
    'VIDEO_GENERATION',
    '20',
    '视频生成积分消费',
    true
),

-- 会员权限配置
(
    'FREE_DAILY_CHAT_LIMIT',
    '100',
    'Free每日对话次数限制',
    false
),
(
    'FREE_CHARACTER_LIMIT',
    '1',
    'Free角色数量限制',
    false
),

-- 系统设置
(
    'POINTS_EXPIRY_DAYS',
    '30',
    '积分有效期（天）',
    false
),
(
    'MIN_POINTS_BALANCE',
    '0',
    '最小积分余额要求',
    false
),
(
    'MAX_SINGLE_CONSUMPTION',
    '1000',
    '单次最大积分消费限制',
    false
),

-- 业务配置
(
    'AUTO_RENEWAL_ENABLED',
    'true',
    '是否启用自动续费',
    false
),
(
    'REFUND_POLICY_DAYS',
    '7',
    '退款政策天数',
    false
);

-- ==========================================
-- 4. 创建系统管理员用户（可选）
-- ==========================================

-- 注意：需要替换为实际的用户ID
-- INSERT INTO "UserPoints" (
--     "id",
--     "user_id",
--     "total_points",
--     "used_points",
--     "available_points"
-- ) VALUES (
--     gen_random_uuid(),
--     '替换为实际用户ID',
--     10000,
--     0,
--     10000
-- );

-- ==========================================
-- 5. 验证数据插入
-- ==========================================

-- 查看插入的会员套餐
SELECT 
    "name",
    "description",
    "price",
    "points_included",
    "is_active"
FROM "MembershipPlan"
ORDER BY "sort_order";

-- 查看插入的积分包
SELECT 
    "name",
    "points",
    "price",
    "bonus_points",
    "is_active"
FROM "PointsPackage"
ORDER BY "sort_order";

-- 查看插入的系统配置
SELECT 
    "key",
    "value",
    "description",
    "is_public"
FROM "SystemConfig"
ORDER BY "key";

-- ==========================================
-- 6. 创建有用的查询视图（可选）
-- ==========================================

-- 会员套餐概览视图
CREATE OR REPLACE VIEW "membership_overview" AS
SELECT 
    mp."name" as plan_name,
    mp."description",
    mp."price",
    mp."points_included",
    COUNT(us."id") as active_subscribers
FROM "MembershipPlan" mp
LEFT JOIN "UserSubscription" us ON mp."id" = us."plan_id" 
    AND us."status" = 'active' 
    AND us."end_date" > NOW()
WHERE mp."is_active" = true
GROUP BY mp."id", mp."name", mp."description", mp."price", mp."points_included", mp."sort_order"
ORDER BY mp."sort_order";

-- 积分配置快查视图
CREATE OR REPLACE VIEW "points_config_view" AS
SELECT 
    "key" as feature,
    CAST("value"::text AS INTEGER) as points_cost,
    "description"
FROM "SystemConfig"
WHERE "key" IN ('IMAGE_GENERATION', 'VOICE_GENERATION', 'GALLERY_GENERATION', 'VIDEO_GENERATION')
ORDER BY "key";

-- ==========================================
-- 执行完成提示
-- ==========================================

SELECT 
    '✅ 会员/积分系统初始化完成！' as status,
    '数据已成功插入到数据库中' as message,
    NOW() as executed_at;

-- ==========================================
-- 后续步骤提醒
-- ==========================================

/*
🎯 后续步骤：
1. 在 Supabase Dashboard 中执行此 SQL 文件
2. 验证数据是否正确插入
3. 启动应用测试前端界面
4. 根据需要调整积分价格和套餐配置

🔧 配置调整示例：
-- 修改图片生成积分消费
UPDATE "SystemConfig" 
SET "value" = '15' 
WHERE "key" = 'IMAGE_GENERATION';

-- 修改Pro会员价格
UPDATE "MembershipPlan" 
SET "price" = 45.00 
WHERE "name" = 'pro';
*/
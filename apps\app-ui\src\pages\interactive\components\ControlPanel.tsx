import React, { useState, useEffect, useRef } from 'react'
import { Card, CardBody, But<PERSON>, Slide<PERSON>, <PERSON>, Spinner, addToast } from '@heroui/react'
import { Icon } from '@iconify/react'
import { Capacitor } from '@capacitor/core'
import { useTranslation } from 'react-i18next'
import { PlayerState } from '../types'
import { secondsToTime } from '../utils/timeUtils'
import { usePlayer } from '../context/PlayerContext'
import { StageSelector } from './StageSelector'
import { DeviceControlPanel } from './DeviceControlPanel'
import { CommandPanel } from './CommandPanel'
import { StageProgressBar } from './StageProgressBar'
import { useDeviceStore } from '../../../stores/device-store'
import type { DeviceFunction, ControlMode } from '../types'

interface ControlPanelProps {
  currentTime: number
  duration: number
  playerState: PlayerState
  currentStageIndex: number
  stageCount: number
  stageTitles: string[]
  onPlay: () => void
  onPause: () => void
  onSeek: (time: number) => void
  onStageChange: (index: number) => void
  onForward: (seconds: number) => void
  hideStageControls?: boolean
}

/**
 * 控制面板组件
 * 包含播放控制和阶段切换
 */
export const ControlPanel: React.FC<ControlPanelProps> = ({
  currentTime,
  duration,
  playerState,
  currentStageIndex,
  stageCount,
  stageTitles,
  onPlay,
  onPause,
  onSeek,
  onStageChange,
  onForward,
  hideStageControls
}) => {
  const [isProcessingClick, setIsProcessingClick] = useState(false)
  const [playAttempts, setPlayAttempts] = useState(0)
  const [audioLoadingStatus, setAudioLoadingStatus] = useState<'loading' | 'ready' | 'error'>(
    'loading'
  )

  // 新增状态：控制抽屉显示
  const [showStageSelector, setShowStageSelector] = useState(false)
  const [showDevicePanel, setShowDevicePanel] = useState(false)
  const [showCommandPanel, setShowCommandPanel] = useState(false)

  // 调试信息：监控状态变化
  useEffect(() => {
    console.log('🔍 ControlPanel - showDevicePanel 状态变化:', showDevicePanel)
  }, [showDevicePanel])

  // 设备控制相关状态
  const [currentIntensity, setCurrentIntensity] = useState<{ [key: string]: number }>({})
  const [controlMode, setControlMode] = useState<ControlMode>('auto' as ControlMode)

  const maxPlayAttempts = 3
  const playTimeoutRef = useRef<number | null>(null)

  // Capacitor 环境检测
  const isCapacitor = Capacitor.isNativePlatform()
  const platform = Capacitor.getPlatform()

  // 音频元素引用
  const audioElementRef = useRef<HTMLAudioElement | null>(null)

  // 获取设备控制相关函数和指令数据
  const { device, sendCommand, commands, script } = usePlayer()

  // 使用设备状态管理
  const { connectedDevice } = useDeviceStore()

  // 保存最近的设备命令状态
  const lastCommandsRef = useRef<{ [key: string]: number }>({})

  // 防抖定时器
  const debounceTimerRef = useRef<number | null>(null)

  // 获取音频元素的函数
  const getAudioElement = (): HTMLAudioElement | null => {
    if (audioElementRef.current) {
      return audioElementRef.current
    }

    const audioElement = document.querySelector('audio') as HTMLAudioElement
    if (audioElement) {
      audioElementRef.current = audioElement
    }

    return audioElement
  }

  // Capacitor 环境下的音频权限检查
  const checkAudioPermissions = async (): Promise<boolean> => {
    if (!isCapacitor) return true

    try {
      // 在 Capacitor 环境下，尝试获取音频权限
      if (platform === 'android') {
        // Android 权限检查
        const permissions = await (window as any).Capacitor?.Plugins?.Permissions?.query({
          name: 'microphone'
        })

        if (permissions?.state !== 'granted') {
          console.warn('音频权限未授予')
          return false
        }
      }

      return true
    } catch (error) {
      console.warn('权限检查失败:', error)
      return true // 如果检查失败，假设有权限
    }
  }

  // 检查音频加载状态
  useEffect(() => {
    const initAudio = async () => {
      // 检查权限
      const hasPermission = await checkAudioPermissions()
      if (!hasPermission) {
        setAudioLoadingStatus('error')
        return
      }

      const audioElement = getAudioElement()

      if (!audioElement) {
        console.error('未找到音频元素')
        setAudioLoadingStatus('error')
        return
      }

      // 初始状态设为加载中
      setAudioLoadingStatus('loading')

      // 监听加载事件
      const handleCanPlay = () => {
        console.log('✅ 音频已准备就绪，可以播放')
        setAudioLoadingStatus('ready')
      }

      const handleCanPlayThrough = () => {
        console.log('✅ 音频可以完整播放')
        setAudioLoadingStatus('ready')
      }

      const handleError = (e: Event) => {
        console.error('❌ 音频加载失败:', e)
        const target = e.target as HTMLAudioElement
        if (target?.error) {
          console.error('音频错误详情:', {
            code: target.error.code,
            message: target.error.message
          })

          // 在 Capacitor 环境下，某些错误可能是 CORS 相关的，但音频实际可以播放
          if (isCapacitor && target.error.code === 4) {
            console.log('🔧 Capacitor 环境下忽略 CORS 相关错误，尝试移除 crossOrigin')
            target.removeAttribute('crossorigin')
            target.load()
            return // 不设置为错误状态
          }
        }
        setAudioLoadingStatus('error')
      }

      const handleLoadStart = () => {
        console.log('🔄 音频开始加载')
        setAudioLoadingStatus('loading')
      }

      const handleLoadedData = () => {
        console.log('📊 音频数据已加载')
        // 在 Capacitor 环境下，loadeddata 事件可能比 canplay 更可靠
        if (isCapacitor) {
          setAudioLoadingStatus('ready')
        }
      }

      const handleLoadedMetadata = () => {
        console.log('📋 音频元数据已加载')
        // 检查是否有有效的时长
        const audioElement = getAudioElement()
        if (audioElement && audioElement.duration > 0) {
          console.log('✅ 音频时长有效:', audioElement.duration)
          // 在某些情况下，元数据加载完成就可以认为准备就绪
          if (isCapacitor && audioElement.readyState >= 1) {
            setAudioLoadingStatus('ready')
          }
        }
      }

      // Capacitor 环境下的特殊处理
      if (isCapacitor) {
        // 🔧 关键修复：在 Capacitor 环境下立即移除 crossOrigin 属性
        console.log('🔧 Capacitor 环境下移除 crossOrigin 属性，避免 CORS 问题')
        audioElement.removeAttribute('crossorigin')

        // 设置音频元素属性
        audioElement.preload = 'auto'
        audioElement.muted = false
        audioElement.volume = 1.0

        // 移动设备音频会话管理
        if (platform === 'ios') {
          // iOS 特殊处理
          audioElement.setAttribute('webkit-playsinline', 'true')
          audioElement.setAttribute('playsinline', 'true')
        }

        // 在 Capacitor 环境下，如果音频已经有基本信息就认为可以播放
        if (audioElement.duration > 0 || audioElement.readyState >= 1) {
          console.log('✅ Capacitor 环境下音频已有基本信息，设置为就绪状态')
          setAudioLoadingStatus('ready')
        }
      } else {
        // Web 环境下才设置 crossOrigin
        audioElement.crossOrigin = 'anonymous'
      }

      // 检查当前音频状态
      console.log('🔍 当前音频状态:', {
        readyState: audioElement.readyState,
        networkState: audioElement.networkState,
        duration: audioElement.duration,
        currentSrc: audioElement.currentSrc,
        crossOrigin: audioElement.crossOrigin,
        error: audioElement.error
      })

      // 在 Capacitor 环境下使用更宽松的就绪条件
      if (isCapacitor) {
        // Capacitor 环境下，只要有音频源就尝试设置为就绪
        if (audioElement.currentSrc && !audioElement.error) {
          console.log('✅ Capacitor 环境下音频源存在且无错误，设置为就绪状态')
          setAudioLoadingStatus('ready')
        } else if (audioElement.readyState >= 1) {
          console.log('✅ Capacitor 环境下音频元数据已加载，设置为就绪状态')
          setAudioLoadingStatus('ready')
        } else {
          console.log('🔄 Capacitor 环境下音频仍在加载中')
          setAudioLoadingStatus('loading')
        }
      } else {
        // Web 环境下使用原来的逻辑
        if (audioElement.readyState >= 3) {
          console.log('✅ Web 环境下音频已经可以播放 (readyState >= 3)')
          setAudioLoadingStatus('ready')
        } else if (audioElement.readyState >= 1 && audioElement.duration > 0) {
          console.log('✅ Web 环境下音频元数据已加载且时长有效')
          setAudioLoadingStatus('loading')
        } else {
          console.log('🔄 Web 环境下音频仍在加载中')
          setAudioLoadingStatus('loading')
        }
      }

      // 添加事件监听器
      audioElement.addEventListener('canplay', handleCanPlay)
      audioElement.addEventListener('canplaythrough', handleCanPlayThrough)
      audioElement.addEventListener('error', handleError)
      audioElement.addEventListener('loadstart', handleLoadStart)
      audioElement.addEventListener('loadeddata', handleLoadedData)
      audioElement.addEventListener('loadedmetadata', handleLoadedMetadata)

      // 在 Capacitor 环境下添加延迟检查，给音频更多时间加载
      if (isCapacitor) {
        const delayedCheck = setTimeout(() => {
          const currentElement = getAudioElement()
          if (currentElement && currentElement.currentSrc && audioLoadingStatus === 'loading') {
            console.log('🔧 Capacitor 延迟检查：强制设置音频为就绪状态')
            currentElement.removeAttribute('crossorigin')
            setAudioLoadingStatus('ready')
          }
        }, 2000) // 2秒后检查

        // 清理函数
        return () => {
          clearTimeout(delayedCheck)
          audioElement.removeEventListener('canplay', handleCanPlay)
          audioElement.removeEventListener('canplaythrough', handleCanPlayThrough)
          audioElement.removeEventListener('error', handleError)
          audioElement.removeEventListener('loadstart', handleLoadStart)
          audioElement.removeEventListener('loadeddata', handleLoadedData)
          audioElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
        }
      } else {
        // 清理函数
        return () => {
          audioElement.removeEventListener('canplay', handleCanPlay)
          audioElement.removeEventListener('canplaythrough', handleCanPlayThrough)
          audioElement.removeEventListener('error', handleError)
          audioElement.removeEventListener('loadstart', handleLoadStart)
          audioElement.removeEventListener('loadeddata', handleLoadedData)
          audioElement.removeEventListener('loadedmetadata', handleLoadedMetadata)
        }
      }
    }

    initAudio()
  }, [])

  // 发送停止命令到所有设备功能
  const sendStopCommands = () => {
    if (!device) return

    // 清除之前的防抖定时器
    if (debounceTimerRef.current) {
      window.clearTimeout(debounceTimerRef.current)
      debounceTimerRef.current = null
    }

    // 使用防抖，延迟200ms执行，避免频繁操作
    debounceTimerRef.current = window.setTimeout(() => {
      console.log('发送所有功能停止命令')
      device.func.forEach(func => {
        // 记录当前命令状态，以便恢复
        if (func.key in lastCommandsRef.current) {
          console.log(`记录功能 ${func.key} 的当前强度: ${lastCommandsRef.current[func.key]}`)
        }

        // 发送停止命令 (intensity = -1)
        sendCommand(func.key, -1)
      })
    }, 200)
  }

  // 恢复之前的命令状态
  const restoreCommands = () => {
    if (!device) return

    // 清除之前的防抖定时器
    if (debounceTimerRef.current) {
      window.clearTimeout(debounceTimerRef.current)
      debounceTimerRef.current = null
    }

    // 使用防抖，延迟200ms执行，避免频繁操作
    debounceTimerRef.current = window.setTimeout(() => {
      const savedCommands = lastCommandsRef.current
      if (Object.keys(savedCommands).length > 0) {
        console.log('恢复之前的命令状态:', savedCommands)

        // 按顺序恢复命令，避免同时发送多个命令
        Object.entries(savedCommands).forEach(([key, intensity], index) => {
          // 如果强度大于0，才需要恢复
          if (intensity > 0) {
            // 添加延迟，避免命令同时发送
            setTimeout(() => {
              console.log(`恢复功能 ${key} 的强度为 ${intensity}`)
              sendCommand(key, intensity)
            }, index * 300) // 每个命令间隔300ms
          }
        })
      }
    }, 200)
  }

  // 更新命令状态记录
  useEffect(() => {
    if (device && playerState === PlayerState.PLAYING) {
      // 当播放状态下，记录当前活跃的命令
      device.func.forEach(func => {
        const key = func.key
        // 通过DOM自定义数据属性获取当前强度
        const currentIntensityElement = document.querySelector(`[data-device-function="${key}"]`)
        if (currentIntensityElement) {
          const intensity = Number.parseInt(
            currentIntensityElement.getAttribute('data-intensity') || '0',
            10
          )
          if (intensity !== 0) {
            lastCommandsRef.current[key] = intensity
          }
        }
      })
    }
  }, [device, playerState])

  // 处理播放/暂停
  const handlePlayPause = async () => {
    // 防止频繁点击
    if (isProcessingClick) return

    // 如果音频未准备好，显示提示
    if (audioLoadingStatus === 'loading') {
      return
    } else if (audioLoadingStatus === 'error' && !isCapacitor) {
      return
    }

    setIsProcessingClick(true)

    try {
      if (playerState === PlayerState.PLAYING) {
        // 暂停音频 - 直接调用context的pause方法
        onPause()
        setIsProcessingClick(false)
        setPlayAttempts(0)
      } else {
        // 播放音频 - 直接调用context的play方法
        onPlay()

        // 设置一个合理的超时，如果播放没有成功则重置状态
        setTimeout(() => {
          setIsProcessingClick(false)
        }, 1000)

        setPlayAttempts(0)
      }
    } catch (error) {
      console.error('播放/暂停失败:', error)
      setIsProcessingClick(false)

      const { t } = useTranslation('interactive')
      addToast({
        title: t('toast.playbackFailed.title'),
        description: t('toast.playbackFailed.description'),
        color: 'danger'
      })
    }
  }

  // 处理后退
  const handleBackward = () => {
    onForward(-15) // 负数表示后退
  }

  // 处理快进
  const handleForward = () => {
    onForward(15)
  }

  // 处理设备强度变化
  const handleIntensityChange = (key: string, intensity: number) => {
    setCurrentIntensity(prev => ({
      ...prev,
      [key]: intensity
    }))

    // 发送命令到设备
    if (device) {
      sendCommand(key, intensity)
    }
  }

  // 处理控制模式变化
  const handleControlModeChange = (mode: ControlMode) => {
    setControlMode(mode)
  }

  // 处理指令跳转
  const handleCommandJump = (timeStr: string) => {
    // 将时间字符串转换为秒数
    const timeToSeconds = (timeStr: string): number => {
      const parts = timeStr.split(':')
      if (parts.length === 3) {
        const hours = parseInt(parts[0], 10)
        const minutes = parseInt(parts[1], 10)
        const seconds = parseInt(parts[2], 10)
        return hours * 3600 + minutes * 60 + seconds
      }
      return 0
    }

    const seconds = timeToSeconds(timeStr)
    onSeek(seconds)
  }

  // 返回到剧本选择页面
  const handleBackToScriptSelect = () => {
    console.log('返回到剧本选择页面')
  }

  // 检查是否有可用的指令
  const hasCommands = commands && commands.length > 0

  // 重置处理状态当播放器状态发生变化
  useEffect(() => {
    if (playerState === PlayerState.PLAYING || playerState === PlayerState.PAUSED) {
      setIsProcessingClick(false)
      setPlayAttempts(0)
    }
  }, [playerState])

  // 音频准备状态检查
  useEffect(() => {
    const audioElement = getAudioElement()
    if (audioElement) {
      const handleCanPlay = () => setAudioLoadingStatus('ready')
      const handleError = () => setAudioLoadingStatus('error')

      audioElement.addEventListener('canplay', handleCanPlay)
      audioElement.addEventListener('error', handleError)

      if (audioElement.readyState >= 3) {
        setAudioLoadingStatus('ready')
      }

      return () => {
        audioElement.removeEventListener('canplay', handleCanPlay)
        audioElement.removeEventListener('error', handleError)
      }
    }
  }, [])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (playTimeoutRef.current) {
        window.clearTimeout(playTimeoutRef.current)
      }
      if (debounceTimerRef.current) {
        window.clearTimeout(debounceTimerRef.current)
      }
    }
  }, [])

  return (
    <>
      {/* 指令快速访问按钮 - 贴合控制面板 */}
      {hasCommands && (
        <div className="relative z-40">
          <div
            className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-[-2px]"
            style={{ marginBottom: '0px' }}
          >
            <Button
              isIconOnly
              variant="flat"
              size="sm"
              onPress={() => setShowCommandPanel(true)}
              className="bg-white/10 backdrop-blur-sm text-white hover:bg-white/20 border border-white/20 w-8 h-8 min-w-8 rounded-full shadow-lg"
              style={{
                background:
                  'linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)',
                backdropFilter: 'blur(10px)',
                WebkitBackdropFilter: 'blur(10px)'
              }}
              aria-label="显示剧本指令"
            >
              <Icon icon="solar:double-alt-arrow-up-linear" width={16} className="text-[#ff2d97]" />
            </Button>
          </div>
        </div>
      )}

      {/* 控制面板 - 按照设计稿实现 */}
      <div className="relative w-full bg-[#13151E] px-5 py-12">
        <img
          src="/images/decorate/decorate-7.svg"
          alt=""
          className="w-full h-full absolute left-0 bottom-0"
        />
        <img
          src="/images/decorate/decorate-8.svg"
          alt=""
          className="w-full h-full absolute -right-20 -bottom-10"
        />

        {/* 阶段进度条 */}
        <div className="mb-6">
          <StageProgressBar
            currentTime={currentTime}
            duration={duration}
            script={script}
            currentStageIndex={currentStageIndex}
            onSeek={onSeek}
            className="w-full"
          />
        </div>

        {/* 控制按钮区域 */}
        <div className="flex items-center justify-between relative z-10">
          {/* 左侧：粉色圆形按钮 */}
          <div
            className="p-3"
            onClick={() => {
              setShowDevicePanel(true)
            }}
          >
            <div className="w-6 h-6 bg-[#ff2d97] hover:bg-[#ff2d97]/80 rounded-full cursor-pointer flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full" />
            </div>
          </div>

          {/* 中间：播放控制按钮组 */}
          <div className="flex items-center gap-4">
            {/* 后退15秒 */}
            <Button
              isIconOnly
              variant="light"
              size="lg"
              onPress={handleBackward}
              className="text-white hover:bg-white/10 w-12 h-12"
              aria-label="后退15秒"
            >
              <div className="flex items-center justify-center">
                <Icon icon="solar:rewind-15-seconds-back-linear" width={24} />
              </div>
            </Button>

            {/* 播放/暂停按钮 - 白色大圆形 */}
            <Button
              isIconOnly
              size="lg"
              isDisabled={isProcessingClick}
              onPress={handlePlayPause}
              className="w-16 h-16 bg-white hover:bg-white/90 text-black min-w-16 rounded-full"
              aria-label={playerState === PlayerState.PLAYING ? '暂停播放' : '开始播放'}
            >
              {isProcessingClick ? (
                <Spinner size="sm" color="default" />
              ) : playerState === PlayerState.PLAYING ? (
                <Icon icon="solar:pause-bold" width={24} className="text-black" />
              ) : (
                <Icon icon="solar:play-bold" width={24} className="text-black ml-1" />
              )}
            </Button>

            {/* 快进15秒 */}
            <Button
              isIconOnly
              variant="light"
              size="lg"
              onPress={handleForward}
              className="text-white hover:bg-white/10 w-12 h-12"
              aria-label="快进15秒"
            >
              <div className="flex items-center justify-center">
                <Icon icon="solar:rewind-15-seconds-back-linear" width={24} />
              </div>
            </Button>
          </div>

          {/* 右侧：三横线菜单按钮 */}
          <Button
            isIconOnly
            variant="light"
            size="lg"
            onPress={() => setShowStageSelector(true)}
            className="text-white hover:bg-white/10 w-12 h-12"
            aria-label="选择阶段"
          >
            <Icon icon="solar:hamburger-menu-linear" width={24} />
          </Button>
        </div>
      </div>

      {/* 阶段选择抽屉 */}
      <StageSelector
        isOpen={showStageSelector}
        onClose={() => setShowStageSelector(false)}
        stageTitles={stageTitles}
        currentStageIndex={currentStageIndex}
        onStageChange={onStageChange}
      />

      {/* 设备控制面板 */}
      <DeviceControlPanel
        isOpen={showDevicePanel}
        onClose={() => setShowDevicePanel(false)}
        deviceFunctions={device?.func || []}
        currentIntensity={currentIntensity}
        controlMode={controlMode}
        onIntensityChange={handleIntensityChange}
        onControlModeChange={handleControlModeChange}
        onBackToScriptSelect={handleBackToScriptSelect}
      />

      {/* 指令面板 */}
      <CommandPanel
        isOpen={showCommandPanel}
        onClose={() => setShowCommandPanel(false)}
        commands={commands || []}
        onCommandClick={handleCommandJump}
      />
    </>
  )
}

// 自定义CSS动画
const styles = `
@keyframes slideInLeft {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.animate-slide-in-left {
  animation: slideInLeft 0.2s ease-out forwards;
}
`

// 添加样式到文档头部
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style')
  styleElement.innerHTML = styles
  document.head.appendChild(styleElement)
}

/**
 * 存储系统初始化器
 * 统一管理各种存储系统的初始化顺序和依赖关系
 */

// 导入数据库初始化
import { initDatabase } from './database'

// 导入聊天系统初始化
import { initializeChatSystem } from './chat-database'
import { getGlobalChatDatabase } from '@/lib/chat-database'
import { BackgroundStorage } from '@/lib/media/background-storage'
import { AudioStorage } from '@/lib/media/audio-storage'
import { ImageStorage } from './media/image-storage'
import { VideoStorage } from './media/video-storage'
import { chatDatabase } from './chat-database/chat-database'
// import { backgroundStorageManager } from './background/background-storage'
// import { mediaDownloadManager } from './background/media-download-manager'

/**
 * 存储系统初始化器
 * 统一管理各种存储系统的初始化顺序和依赖关系
 */
export class StorageInitializer {
  private static instance: StorageInitializer
  private isInitialized = false

  static getInstance(): StorageInitializer {
    if (!StorageInitializer.instance) {
      StorageInitializer.instance = new StorageInitializer()
    }
    return StorageInitializer.instance
  }

  /**
   * 初始化所有存储系统
   */
  async initializeAll(): Promise<boolean> {
    if (this.isInitialized) {
      console.log('📦 [StorageInit] 存储系统已初始化，跳过')
      return true
    }

    try {
      console.log('🚀 [StorageInit] 开始初始化存储系统...')

      // 1. 初始化基础数据库
      console.log('📊 [StorageInit] 初始化聊天数据库...')
      await chatDatabase.initialize()

      // 2. 初始化聊天系统（依赖基础数据库）
      console.log('💬 [StorageInit] 初始化聊天系统...')
      // 聊天系统已在数据库初始化时完成

      // 3. 初始化增强聊天系统（依赖聊天系统）
      console.log('⚡ [StorageInit] 初始化增强聊天系统...')
      // 增强聊天系统已集成到主数据库中

      // 4. 初始化背景图存储（依赖数据库）
      console.log('🖼️ [StorageInit] 初始化背景图存储...')
      const backgroundStorage = BackgroundStorage.getInstance()
      await backgroundStorage.initialize()

      // 3. 图片存储初始化
      await ImageStorage.getInstance().initialize()
      console.log('✅ 图片存储初始化完成')

      // 4. 视频存储初始化
      await VideoStorage.getInstance().initialize()
      console.log('✅ 视频存储初始化完成')

      // 5. 音频存储初始化
      await AudioStorage.getInstance().initialize()
      console.log('✅ 音频存储初始化完成')

      // 6. 媒体下载管理器暂时跳过
      console.log('📥 [StorageInit] 媒体下载管理器初始化跳过...')

      this.isInitialized = true
      console.log('✅ [StorageInit] 所有存储系统初始化完成')
      return true
    } catch (error) {
      console.error('❌ [StorageInit] 存储系统初始化失败:', error)
      return false
    }
  }

  /**
   * 检查是否已初始化
   */
  isStorageInitialized(): boolean {
    return this.isInitialized
  }

  /**
   * 重置初始化状态（测试用）
   */
  reset(): void {
    this.isInitialized = false
  }
}

// 导出单例实例
export const storageInitializer = StorageInitializer.getInstance()

/**
 * 清理存储系统资源
 */
export async function cleanupStorageSystems(): Promise<void> {
  console.log('🧹 [Storage] 开始清理存储系统...')

  try {
    // 清理聊天数据库连接
    const { chatDatabase } = await import('@/lib/chat-database')
    await chatDatabase.close()

    console.log('✅ [Storage] 存储系统清理完成')
  } catch (error) {
    console.warn('⚠️ [Storage] 存储系统清理失败:', error)
  }
}

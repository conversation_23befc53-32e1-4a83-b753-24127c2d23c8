import { useState } from 'react'
import { useTranslation } from 'react-i18next'
import { ImageDrawer } from '@/components/common/image-drawer'

interface ImageDisplayProps {
  imageUrl: string
  alt?: string
  className?: string
}

/**
 * 纯UI组件：图片显示和预览
 * 职责：展示图片、点击预览、缩放效果
 */
export const ImageDisplay = ({
  imageUrl,
  alt = 'AI Generated Image',
  className = ''
}: ImageDisplayProps) => {
  const { t } = useTranslation('chat-v2')
  const [showPreview, setShowPreview] = useState(false)

  return (
    <>
      <div className={`relative w-full max-w-xs mx-auto ${className}`}>
        <div
          className="aspect-[3/4] rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-lg transition-shadow"
          onClick={() => setShowPreview(true)}
        >
          <img
            src={imageUrl}
            alt={alt}
            className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
            loading="lazy"
          />

          <div className="mt-2 text-xs text-gray-500 dark:text-gray-400 text-center absolute bottom-3 left-0 right-0">
            {t('image.click_to_view')}
          </div>
        </div>
      </div>

      {/* 使用ImageDrawer预览 */}
      <ImageDrawer
        isOpen={showPreview}
        onClose={() => setShowPreview(false)}
        imageUrl={imageUrl}
        title={alt}
      />
    </>
  )
}

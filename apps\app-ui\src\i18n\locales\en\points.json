{"store": {"title": "Points Store", "loading": "Loading...", "loading_description": "Getting points package information", "current_points": "Current Points", "available_points": "Available for various functions", "select_package": "Select Points Package", "purchase_now": "Purchase Now", "no_packages": "No packages available", "no_packages_description": "Please check back later", "load_failed": "Load failed", "reload": "Reload", "purchase_failed": "Purchase failed", "purchase_success": "Order created successfully", "redirecting": "Redirecting to payment page...", "retry": "Please try again", "validation_failed": "Parameter validation failed", "confirm_purchase": "Confirm Purchase", "payment_amount": "Payment Amount", "base_points": "Base Points", "bonus_points": "Bonus Points", "total_points": "Total Points", "original_price": "Original Price", "savings": "You Save", "cancel": "Cancel", "alipay_payment": "Alipay Payment", "points_unit": "points"}, "detail": {"title": "Points Details", "remaining_points": "Remaining Points", "transaction_history": "Transaction History", "no_transactions": "No transaction records", "no_transactions_description": "Records will appear after subscribing or using features", "refresh_success": "Data refreshed", "refresh_failed": "Refresh failed", "loading": "Loading...", "transaction_after_balance": "Balance after transaction: {{balance}}"}, "transaction": {"types": {"earn": "Earn Points", "spend": "Spend Points", "upgrade_bonus": "Upgrade Bonus", "cycle_grant": "Cycle Grant", "cycle_reset": "Cycle Reset", "points_change": "Points Change"}, "sources": {"subscription": "Membership Subscription", "purchase": "Purchase Points", "generation": "Media Generation", "refund": "Refund", "bonus": "Bonus", "admin": "System Adjustment", "upgrade": "Membership Upgrade", "default": "Points Change"}, "descriptions": {"earn": "Earn Points", "spend": "Spend Points", "upgrade_bonus": "Upgrade Bonus Points", "cycle_grant": "Membership Cycle Points", "cycle_reset": "Points Cycle Reset", "default": "Points Record"}}, "time": {"today": "Today", "yesterday": "Yesterday", "days_ago": "{{days}} days ago"}, "common": {"loading": "Loading...", "login_required": "Please login first", "error": "Error occurred", "retry": "Retry", "back": "Back"}}
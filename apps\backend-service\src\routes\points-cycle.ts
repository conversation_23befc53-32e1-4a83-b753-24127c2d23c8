import { Hono } from 'hono';
import { zValidator } from '@hono/zod-validator';
import { z } from 'zod';
import type { Env } from '@/types/env';
import { authMiddleware } from '@/middleware/auth';
import { createPointsCycleManager } from '@/lib/membership/points-cycle';
import type { MembershipLevel } from '@/lib/membership/cycle-utils';

/**
 * 积分周期管理API
 * 提供积分周期查询、重置、升级等功能
 */

const app = new Hono<{ Bindings: Env }>();

// 初始化积分周期验证schema
const initializeCycleSchema = z.object({
  membershipLevel: z.enum(['FREE', 'PRO', 'ELITE', 'ULTRA']),
  startDate: z.string().datetime().optional(),
});

// 会员升级验证schema
const upgradeSchema = z.object({
  fromLevel: z.enum(['FREE', 'PRO', 'ELITE', 'ULTRA']),
  toLevel: z.enum(['FREE', 'PRO', 'ELITE', 'ULTRA']),
  remainingDays: z.number().min(0).max(30),
});

/**
 * GET /api/points-cycle/status
 * 获取用户积分周期状态
 */
app.get('/status', authMiddleware, async (c) => {
  try {
    const user = c.get('user');
    const cycleManager = createPointsCycleManager(c.env);

    const cycleInfo = await cycleManager.getUserPointsCycle(user.id);

    return c.json({
      success: true,
      data: {
        userId: cycleInfo.userId,
        availablePoints: cycleInfo.availablePoints,
        cycleStartDate: cycleInfo.cycleStartDate,
        cycleEndDate: cycleInfo.cycleEndDate,
        membershipLevel: cycleInfo.membershipLevel,
        monthlyAllocation: cycleInfo.monthlyAllocation,
        cycleConsumed: cycleInfo.cycleConsumed,
        daysRemaining: cycleInfo.daysRemaining,
        isExpired: cycleInfo.isExpired,
        needsReset: cycleInfo.needsReset,
        progressPercentage:
          cycleInfo.monthlyAllocation > 0
            ? Math.round((cycleInfo.cycleConsumed / cycleInfo.monthlyAllocation) * 100)
            : 0,
      },
    });
  } catch (error) {
    console.error('获取积分周期状态失败:', error);
    return c.json(
      {
        success: false,
        error: '获取积分周期状态失败',
      },
      500
    );
  }
});

/**
 * POST /api/points-cycle/initialize
 * 初始化用户积分周期
 */
app.post('/initialize', authMiddleware, zValidator('json', initializeCycleSchema), async (c) => {
  try {
    const user = c.get('user');
    const { membershipLevel, startDate } = c.req.valid('json');

    const cycleManager = createPointsCycleManager(c.env);
    const startDateTime = startDate ? new Date(startDate) : new Date();

    await cycleManager.initializeUserPointsCycle(
      user.id,
      membershipLevel as MembershipLevel,
      startDateTime
    );

    // 获取初始化后的状态
    const cycleInfo = await cycleManager.getUserPointsCycle(user.id);

    return c.json({
      success: true,
      data: cycleInfo,
      message: `${membershipLevel}会员积分周期初始化成功`,
    });
  } catch (error) {
    console.error('初始化积分周期失败:', error);
    return c.json(
      {
        success: false,
        error: '初始化积分周期失败',
      },
      500
    );
  }
});

/**
 * POST /api/points-cycle/check-and-reset
 * 手动检查并重置过期积分
 */
app.post('/check-and-reset', authMiddleware, async (c) => {
  try {
    const user = c.get('user');
    const cycleManager = createPointsCycleManager(c.env);

    const result = await cycleManager.checkAndHandleExpiredPoints(user.id);

    return c.json({
      success: true,
      data: {
        wasExpired: result.wasExpired,
        newCycle: result.newCycle,
        message: result.wasExpired ? '积分周期已重置' : '积分周期正常',
      },
    });
  } catch (error) {
    console.error('检查重置积分周期失败:', error);
    return c.json(
      {
        success: false,
        error: '检查重置积分周期失败',
      },
      500
    );
  }
});

/**
 * POST /api/points-cycle/upgrade
 * 处理会员升级积分补差
 */
app.post('/upgrade', authMiddleware, zValidator('json', upgradeSchema), async (c) => {
  try {
    const user = c.get('user');
    const { fromLevel, toLevel, remainingDays } = c.req.valid('json');

    const cycleManager = createPointsCycleManager(c.env);

    const result = await cycleManager.handleMembershipUpgrade(
      user.id,
      fromLevel as MembershipLevel,
      toLevel as MembershipLevel,
      remainingDays
    );

    // 获取升级后的状态
    const cycleInfo = await cycleManager.getUserPointsCycle(user.id);

    return c.json({
      success: true,
      data: {
        bonusPoints: result.bonusPoints,
        newCycle: cycleInfo,
      },
      message: `会员升级成功，补发${result.bonusPoints}积分`,
    });
  } catch (error) {
    console.error('处理会员升级失败:', error);
    return c.json(
      {
        success: false,
        error: '处理会员升级失败',
      },
      500
    );
  }
});

/**
 * POST /api/points-cycle/batch-process
 * 批量处理过期积分（管理员接口）
 */
app.post('/batch-process', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限检查
    const user = c.get('user');

    const cycleManager = createPointsCycleManager(c.env);
    const result = await cycleManager.batchProcessExpiredPoints();

    return c.json({
      success: true,
      data: {
        processedCount: result.processedCount,
        errors: result.errors,
        executedBy: user.id,
        executedAt: new Date().toISOString(),
      },
      message: `批量处理完成，成功处理${result.processedCount}个用户`,
    });
  } catch (error) {
    console.error('批量处理过期积分失败:', error);
    return c.json(
      {
        success: false,
        error: '批量处理过期积分失败',
      },
      500
    );
  }
});

/**
 * GET /api/points-cycle/health
 * 积分周期系统健康检查
 */
app.get('/health', async (c) => {
  try {
    const cycleManager = createPointsCycleManager(c.env);

    // 简单的健康检查，获取系统状态
    const healthData = {
      service: 'points-cycle',
      status: 'healthy',
      timestamp: new Date().toISOString(),
      features: {
        cycleManagement: true,
        automaticReset: true,
        upgradeHandling: true,
        batchProcessing: true,
      },
    };

    return c.json({
      success: true,
      data: healthData,
    });
  } catch (error) {
    console.error('积分周期健康检查失败:', error);
    return c.json(
      {
        success: false,
        error: '积分周期健康检查失败',
      },
      500
    );
  }
});

export default app;

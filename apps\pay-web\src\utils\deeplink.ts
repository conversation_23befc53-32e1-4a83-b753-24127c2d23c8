/**
 * APP深链接处理工具
 */

export interface DeepLinkOptions {
  orderId?: string
  status?: 'success' | 'failed' | 'cancelled'
  amount?: number
  planName?: string
}

/**
 * 检测用户设备类型
 */
export function detectPlatform(): 'ios' | 'android' | 'unknown' {
  const userAgent = navigator.userAgent.toLowerCase()

  if (/iphone|ipad|ipod/.test(userAgent)) {
    return 'ios'
  } else if (/android/.test(userAgent)) {
    return 'android'
  } else {
    return 'unknown'
  }
}

/**
 * 生成APP深链接URL
 */
export function generateDeepLink(options: DeepLinkOptions = {}): string {
  const { orderId, status, amount, planName } = options

  // 构建深链接参数
  const params = new URLSearchParams()

  if (orderId) params.set('orderId', orderId)
  if (status) params.set('status', status)
  if (amount) params.set('amount', amount.toString())
  if (planName) params.set('planName', planName)

  // 应用的深链接scheme - 使用正式包名
  const appScheme = 'com.pleasurehub.app'
  const path = status ? `payment/result` : 'payment'

  const deepLink = `${appScheme}://${path}?${params.toString()}`

  console.log('🔗 [DEEPLINK] 生成深度链接:', deepLink)
  return deepLink
}

/**
 * 尝试打开APP深链接
 */
export function openAppDeepLink(options: DeepLinkOptions = {}): Promise<boolean> {
  return new Promise(resolve => {
    const deepLink = generateDeepLink(options)
    const platform = detectPlatform()

    console.log('📱 [DEEPLINK] 尝试打开APP:', { deepLink, platform })

    // 创建隐藏的iframe或链接来触发深链接
    let element: HTMLIFrameElement | HTMLAnchorElement

    if (platform === 'ios') {
      // iOS使用iframe
      element = document.createElement('iframe')
      element.style.display = 'none'
      element.src = deepLink
    } else {
      // Android使用链接
      element = document.createElement('a')
      element.style.display = 'none'
      element.href = deepLink
      element.target = '_self' // 确保在当前窗口打开
    }

    document.body.appendChild(element)

    // 设置超时检测
    let timeout: NodeJS.Timeout
    let opened = false

    const cleanup = () => {
      if (timeout) clearTimeout(timeout)
      if (element && element.parentNode) {
        document.body.removeChild(element)
      }
    }

    // 检测页面是否失去焦点(表示APP被打开)
    const handleVisibilityChange = () => {
      if (document.hidden) {
        opened = true
        console.log('✅ [DEEPLINK] 检测到APP被打开')
        cleanup()
        resolve(true)
      }
    }

    const handlePageHide = () => {
      opened = true
      console.log('✅ [DEEPLINK] 页面被隐藏，APP可能被打开')
      cleanup()
      resolve(true)
    }

    const handleBlur = () => {
      // 页面失去焦点，可能是应用被打开
      setTimeout(() => {
        if (document.hidden || !document.hasFocus()) {
          opened = true
          console.log('✅ [DEEPLINK] 页面失去焦点，APP可能被打开')
          cleanup()
          resolve(true)
        }
      }, 100)
    }

    // 添加事件监听
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('pagehide', handlePageHide)
    window.addEventListener('blur', handleBlur)

    // 触发深链接
    if (platform === 'android' && element instanceof HTMLAnchorElement) {
      element.click()
    }

    // 3秒后检查是否成功打开
    timeout = setTimeout(() => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('pagehide', handlePageHide)
      window.removeEventListener('blur', handleBlur)
      cleanup()

      if (!opened) {
        console.log('⏰ [DEEPLINK] 超时，可能未安装APP')
        resolve(false)
      }
    }, 3000)
  })
}

/**
 * 打开APP，如果失败则提供下载链接
 */
export async function openAppWithFallback(
  options: DeepLinkOptions = {},
  downloadUrls?: {
    ios?: string
    android?: string
    web?: string
  }
): Promise<void> {
  console.log('🚀 [DEEPLINK] 尝试打开APP...')

  const success = await openAppDeepLink(options)

  if (!success && downloadUrls) {
    const platform = detectPlatform()

    console.log('📥 [DEEPLINK] APP未打开，显示下载选项')

    // 如果未成功打开APP，显示下载选项
    const downloadUrl = downloadUrls[platform] || downloadUrls.web

    if (downloadUrl) {
      const userConfirmed = confirm('检测到您可能未安装APP，是否前往下载？')

      if (userConfirmed) {
        window.open(downloadUrl, '_blank')
      }
    }
  }
}

/**
 * 获取URL参数
 */
export function getUrlParams(): URLSearchParams {
  return new URLSearchParams(window.location.search)
}

/**
 * 获取订单ID从URL参数
 */
export function getOrderIdFromUrl(): string | null {
  const params = getUrlParams()
  return params.get('orderId')
}

/**
 * 从URL获取支付结果参数
 */
export function getPaymentResultFromUrl(): {
  orderId?: string
  status?: string
  amount?: string
  [key: string]: string | undefined
} {
  const urlParams = new URLSearchParams(window.location.search)
  const result: { [key: string]: string | undefined } = {}

  // 支付宝常见返回参数
  const alipayParams = [
    'out_trade_no',
    'trade_no',
    'total_amount',
    'trade_status',
    'gmt_create',
    'gmt_payment',
    'seller_id',
    'buyer_id'
  ]

  // 微信常见返回参数
  const wechatParams = [
    'orderId',
    'transaction_id',
    'total_fee',
    'result_code',
    'time_end',
    'mch_id',
    'openid'
  ]

  // 通用参数
  const commonParams = ['orderId', 'status', 'amount']

  const allParams = [...new Set([...alipayParams, ...wechatParams, ...commonParams])]

  allParams.forEach(param => {
    const value = urlParams.get(param)
    if (value) {
      result[param] = value
    }
  })

  // 标准化订单号
  if (result.out_trade_no && !result.orderId) {
    result.orderId = result.out_trade_no
  }

  console.log('📋 [PAYMENT-RESULT] 从URL解析支付结果:', result)
  return result
}

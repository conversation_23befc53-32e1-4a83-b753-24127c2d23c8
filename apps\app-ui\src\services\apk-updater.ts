import { Capacitor } from '@capacitor/core'
import { Filesystem, Directory, Encoding } from '@capacitor/filesystem'
import { Device } from '@capacitor/device'

export interface ApkDownloadProgress {
  loaded: number
  total: number
  percentage: number
}

export type ApkDownloadProgressCallback = (progress: ApkDownloadProgress) => void

export interface ApkDownloadResult {
  success: boolean
  filePath?: string
  error?: string
}

/**
 * APK下载和安装管理器
 */
export class ApkUpdater {
  private downloadController?: AbortController

  /**
   * 下载APK文件
   */
  async downloadApk(
    url: string,
    fileName: string,
    onProgress?: ApkDownloadProgressCallback
  ): Promise<ApkDownloadResult> {
    try {
      // 检查是否在原生环境
      if (!Capacitor.isNativePlatform()) {
        return {
          success: false,
          error: 'APK下载仅在原生Android环境中支持'
        }
      }

      // 检查是否是Android平台
      const deviceInfo = await Device.getInfo()
      if (deviceInfo.platform !== 'android') {
        return {
          success: false,
          error: 'APK下载仅支持Android平台'
        }
      }

      console.log('开始下载APK:', { url, fileName })

      // 创建下载控制器
      this.downloadController = new AbortController()

      // 开始下载
      const response = await fetch(url, {
        signal: this.downloadController.signal
      })

      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`)
      }

      const contentLength = response.headers.get('content-length')
      const totalSize = contentLength ? parseInt(contentLength, 10) : 0

      if (!response.body) {
        throw new Error('响应体为空')
      }

      const reader = response.body.getReader()
      const chunks: Uint8Array[] = []
      let loaded = 0

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        chunks.push(value)
        loaded += value.length

        // 报告进度
        if (onProgress && totalSize > 0) {
          onProgress({
            loaded,
            total: totalSize,
            percentage: Math.round((loaded / totalSize) * 100)
          })
        }
      }

      // 合并所有chunks
      const totalLength = chunks.reduce((acc, chunk) => acc + chunk.length, 0)
      const uint8Array = new Uint8Array(totalLength)
      let offset = 0
      for (const chunk of chunks) {
        uint8Array.set(chunk, offset)
        offset += chunk.length
      }

      // 转换为base64以便保存到文件系统
      const base64Data = this.arrayBufferToBase64(uint8Array.buffer)

      // 保存到应用程序的Documents目录
      const filePath = `downloads/${fileName}`

      await Filesystem.writeFile({
        path: filePath,
        data: base64Data,
        directory: Directory.Documents,
        encoding: Encoding.UTF8 // 使用UTF8编码保存base64字符串
      })

      console.log('APK下载完成:', filePath)

      return {
        success: true,
        filePath
      }
    } catch (error) {
      console.error('APK下载失败:', error)

      // 如果是用户取消，不视为错误
      if (error instanceof Error && error.name === 'AbortError') {
        return {
          success: false,
          error: '下载已取消'
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : '下载过程中发生未知错误'
      }
    }
  }

  /**
   * 取消当前下载
   */
  cancelDownload(): void {
    if (this.downloadController) {
      this.downloadController.abort()
      this.downloadController = undefined
    }
  }

  /**
   * 安装APK文件
   */
  async installApk(filePath: string): Promise<{ success: boolean; error?: string }> {
    try {
      if (!Capacitor.isNativePlatform()) {
        // 在Web环境中，打开下载链接
        return this.fallbackToWebDownload(filePath)
      }

      const deviceInfo = await Device.getInfo()
      if (deviceInfo.platform !== 'android') {
        return {
          success: false,
          error: 'APK安装仅支持Android平台'
        }
      }

      // 获取文件的完整路径
      const fileUri = await Filesystem.getUri({
        directory: Directory.Documents,
        path: filePath
      })

      console.log('准备安装APK:', fileUri.uri)

      // 在Android中，我们需要使用Intent来安装APK
      // 这需要一个自定义的Capacitor插件或者使用现有的文件打开插件

      // 暂时使用系统的文件管理器打开（用户需要手动安装）
      const result = await this.openApkWithSystem(fileUri.uri)

      return result
    } catch (error) {
      console.error('APK安装失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '安装过程中发生未知错误'
      }
    }
  }

  /**
   * 使用系统应用打开APK文件
   */
  private async openApkWithSystem(fileUri: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 这里需要使用Capacitor的插件来打开文件
      // 由于没有直接的API，我们使用一个workaround

      // 尝试使用浏览器打开文件URI（某些Android设备支持）
      window.open(fileUri, '_system')

      return {
        success: true
      }
    } catch (error) {
      console.error('使用系统应用打开APK失败:', error)
      return {
        success: false,
        error: '无法打开APK文件，请手动安装'
      }
    }
  }

  /**
   * Web环境的备用方案
   */
  private async fallbackToWebDownload(url: string): Promise<{ success: boolean; error?: string }> {
    try {
      // 在Web环境中，直接打开下载链接
      const link = document.createElement('a')
      link.href = url
      link.download = url.split('/').pop() || 'app.apk'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      return {
        success: true
      }
    } catch (error) {
      return {
        success: false,
        error: '无法下载文件'
      }
    }
  }

  /**
   * 检查是否有安装权限
   */
  async checkInstallPermission(): Promise<boolean> {
    try {
      if (!Capacitor.isNativePlatform()) {
        return false
      }

      const deviceInfo = await Device.getInfo()
      if (deviceInfo.platform !== 'android') {
        return false
      }

      // 在实际实现中，这里应该检查INSTALL_PACKAGES权限
      // 目前返回true，假设用户已经授予了权限
      return true
    } catch (error) {
      console.error('检查安装权限失败:', error)
      return false
    }
  }

  /**
   * 清理下载的临时文件
   */
  async cleanupDownloadedFiles(): Promise<void> {
    try {
      const downloadsDir = 'downloads'

      // 列出downloads目录中的所有文件
      const result = await Filesystem.readdir({
        path: downloadsDir,
        directory: Directory.Documents
      })

      // 删除所有APK文件
      for (const file of result.files) {
        if (file.name.endsWith('.apk')) {
          await Filesystem.deleteFile({
            path: `${downloadsDir}/${file.name}`,
            directory: Directory.Documents
          })
          console.log('已删除临时文件:', file.name)
        }
      }
    } catch (error) {
      console.warn('清理临时文件失败:', error)
      // 不抛出错误，因为这不是关键操作
    }
  }

  /**
   * 工具方法：将ArrayBuffer转换为Base64
   */
  private arrayBufferToBase64(buffer: ArrayBuffer): string {
    const bytes = new Uint8Array(buffer)
    const len = bytes.byteLength
    let binary = ''

    for (let i = 0; i < len; i++) {
      binary += String.fromCharCode(bytes[i])
    }

    return btoa(binary)
  }

  /**
   * 获取下载进度状态
   */
  isDownloading(): boolean {
    return this.downloadController !== undefined
  }

  /**
   * 验证APK文件完整性（基于文件大小）
   */
  async verifyApkFile(filePath: string, expectedSize?: number): Promise<boolean> {
    try {
      const stat = await Filesystem.stat({
        path: filePath,
        directory: Directory.Documents
      })

      if (expectedSize && stat.size !== expectedSize) {
        console.warn('APK文件大小不匹配:', {
          actual: stat.size,
          expected: expectedSize
        })
        return false
      }

      return true
    } catch (error) {
      console.error('验证APK文件失败:', error)
      return false
    }
  }
}

// 单例导出
export const apkUpdater = new ApkUpdater()

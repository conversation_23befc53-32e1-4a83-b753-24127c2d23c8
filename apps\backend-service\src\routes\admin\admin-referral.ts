import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import { authMiddleware } from '@/middleware/auth';
import type { Env } from '@/types/env';
import {
  getPendingWithdrawRequests,
  getAllWithdrawRequests,
  reviewWithdrawRequest,
  completeWithdrawRequest,
  getWithdrawStats,
} from '@/lib/db/queries/admin-referral';

const app = new Hono<{ Bindings: Env; Variables: { userId: string } }>();

// ==================== 管理员提现审核 ====================

// 获取待审核的提现申请列表
app.get('/withdraw/pending', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限验证
    const page = Number.parseInt(c.req.query('page') || '1');
    const limit = Number.parseInt(c.req.query('limit') || '20');

    const result = await getPendingWithdrawRequests(c.env, page, limit);

    return c.json({
      success: true,
      data: {
        list: result.list,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Get pending withdraw requests error:', error);
    return c.json(
      {
        success: false,
        error: '获取待审核提现申请失败',
      },
      500
    );
  }
});

// 获取所有提现申请列表
app.get('/withdraw/all', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限验证
    const page = Number.parseInt(c.req.query('page') || '1');
    const limit = Number.parseInt(c.req.query('limit') || '20');
    const status = c.req.query('status') as
      | 'pending'
      | 'approved'
      | 'rejected'
      | 'completed'
      | undefined;

    const result = await getAllWithdrawRequests(c.env, page, limit, status);

    return c.json({
      success: true,
      data: {
        list: result.list,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit),
        },
      },
    });
  } catch (error) {
    console.error('Get all withdraw requests error:', error);
    return c.json(
      {
        success: false,
        error: '获取提现申请列表失败',
      },
      500
    );
  }
});

// 审核提现申请
const reviewWithdrawSchema = z.object({
  requestId: z.string().uuid('无效的申请ID'),
  action: z.enum(['approve', 'reject'], {
    errorMap: () => ({ message: '操作类型必须是 approve 或 reject' }),
  }),
  adminNote: z.string().optional(),
});

app.post(
  '/withdraw/review',
  authMiddleware,
  zValidator('json', reviewWithdrawSchema),
  async (c) => {
    try {
      // TODO: 添加管理员权限验证
      const adminUserId = c.get('userId');
      const { requestId, action, adminNote } = c.req.valid('json');

      await reviewWithdrawRequest(c.env, requestId, action, adminUserId, adminNote);

      return c.json({
        success: true,
        message: action === 'approve' ? '提现申请已批准' : '提现申请已拒绝',
        data: {
          requestId,
          status: action === 'approve' ? 'approved' : 'rejected',
          processedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Review withdraw request error:', error);
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : '审核提现申请失败',
        },
        500
      );
    }
  }
);

// 标记提现完成
const completeWithdrawSchema = z.object({
  requestId: z.string().uuid('无效的申请ID'),
  adminNote: z.string().optional(),
});

app.post(
  '/withdraw/complete',
  authMiddleware,
  zValidator('json', completeWithdrawSchema),
  async (c) => {
    try {
      // TODO: 添加管理员权限验证
      const adminUserId = c.get('userId');
      const { requestId, adminNote } = c.req.valid('json');

      await completeWithdrawRequest(c.env, requestId, adminUserId, adminNote);

      return c.json({
        success: true,
        message: '提现已完成',
        data: {
          requestId,
          status: 'completed',
          completedAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Complete withdraw request error:', error);
      return c.json(
        {
          success: false,
          error: error instanceof Error ? error.message : '完成提现失败',
        },
        500
      );
    }
  }
);

// 获取提现统计数据
app.get('/withdraw/stats', authMiddleware, async (c) => {
  try {
    // TODO: 添加管理员权限验证
    const stats = await getWithdrawStats(c.env);

    return c.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Get withdraw stats error:', error);
    return c.json(
      {
        success: false,
        error: '获取提现统计失败',
      },
      500
    );
  }
});

export default app;

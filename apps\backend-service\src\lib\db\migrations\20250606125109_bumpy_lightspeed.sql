CREATE TABLE IF NOT EXISTS "Script" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" varchar(100) NOT NULL,
	"description" text NOT NULL,
	"cover_image" text NOT NULL,
	"duration" varchar(20) NOT NULL,
	"tags" json DEFAULT '[]' NOT NULL,
	"category" varchar(50),
	"content" json,
	"is_public" boolean DEFAULT true NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_premium" boolean DEFAULT false NOT NULL,
	"usage_count" integer DEFAULT 0 NOT NULL,
	"rating" numeric(3, 2) DEFAULT '0',
	"rating_count" integer DEFAULT 0 NOT NULL,
	"created_by" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "ScriptUsage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"script_id" uuid NOT NULL,
	"chat_id" uuid,
	"duration" integer,
	"rating" integer,
	"feedback" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Script" ADD CONSTRAINT "Script_created_by_User_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ScriptUsage" ADD CONSTRAINT "ScriptUsage_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ScriptUsage" ADD CONSTRAINT "ScriptUsage_script_id_Script_id_fk" FOREIGN KEY ("script_id") REFERENCES "public"."Script"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "ScriptUsage" ADD CONSTRAINT "ScriptUsage_chat_id_Chat_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."Chat"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_public" ON "Script" USING btree ("is_public") WHERE "Script"."is_public" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_active" ON "Script" USING btree ("is_active") WHERE "Script"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_category" ON "Script" USING btree ("category");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_premium" ON "Script" USING btree ("is_premium");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_rating" ON "Script" USING btree ("rating" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_usage" ON "Script" USING btree ("usage_count" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_created_at" ON "Script" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_public_category" ON "Script" USING btree ("is_public","category") WHERE "Script"."is_public" = true AND "Script"."is_active" = true;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_usage_user_id" ON "ScriptUsage" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_usage_script_id" ON "ScriptUsage" USING btree ("script_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_usage_chat_id" ON "ScriptUsage" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_usage_created_at" ON "ScriptUsage" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_script_usage_user_script" ON "ScriptUsage" USING btree ("user_id","script_id");
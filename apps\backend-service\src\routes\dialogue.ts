import { Hono } from 'hono';
import { z } from 'zod';
import { zValidator } from '@hono/zod-validator';
import type { Env } from '@/types/env';
import {
  getScriptById,
  getDefaultScript,
  getAvailableScriptIds,
  isValidScriptId,
} from '@/lib/dialogue/scripts';

const dialogue = new Hono<{ Bindings: Env }>();

// 查询参数验证模式
const getDialogueSchema = z.object({
  id: z.string().optional(),
});

// ==================== 获取剧本数据 ====================

dialogue.get('/', zValidator('query', getDialogueSchema), async (c) => {
  try {
    const { id } = c.req.valid('query');

    // 如果提供了ID，验证并返回特定剧本
    if (id) {
      if (!isValidScriptId(id)) {
        return c.json(
          {
            success: false,
            message: `无效的剧本ID: ${id}。可用ID: ${getAvailableScriptIds().join(', ')}`,
          },
          400
        );
      }

      const script = getScriptById(id);
      if (!script) {
        return c.json(
          {
            success: false,
            message: '剧本不存在',
          },
          404
        );
      }

      return c.json({
        success: true,
        data: script,
      });
    }

    // 没有ID，返回默认剧本（民宿激情）
    const defaultScript = getDefaultScript();
    return c.json({
      success: true,
      data: defaultScript,
    });
  } catch (error) {
    console.error('获取剧本数据失败:', error);
    return c.json(
      {
        success: false,
        message: '获取剧本数据失败',
      },
      500
    );
  }
});

// ==================== 获取可用剧本列表 ====================

dialogue.get('/list', async (c) => {
  try {
    const availableIds = getAvailableScriptIds();

    return c.json({
      success: true,
      data: {
        availableScripts: availableIds,
        total: availableIds.length,
        descriptions: {
          '1': '民宿激情',
          '2': '教授诱惑',
          '3': '机场邂逅',
        },
      },
    });
  } catch (error) {
    console.error('获取剧本列表失败:', error);
    return c.json(
      {
        success: false,
        message: '获取剧本列表失败',
      },
      500
    );
  }
});

export default dialogue;

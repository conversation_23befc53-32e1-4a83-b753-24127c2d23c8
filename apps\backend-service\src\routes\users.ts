import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import {
  getUserProfile,
  createUserProfile,
  updateUserProfile,
  getUserBySupabaseId,
  getUserActiveSubscription,
  getUserSubscriptionHistory,
  getUserPoints,
  getUserPointsTransactions
} from '@/lib/db/queries'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import type { SupportedLanguage } from '@/i18n/config'

const users = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// ==================== 验证模式 ====================

const getProfileUpdateSchema = (t: (key: string) => string) =>
  z.object({
    nickname: z
      .string()
      .min(2, t('validation.nickname_min'))
      .max(50, t('validation.nickname_max'))
      .optional(),
    gender: z
      .enum(['male', 'female', 'other'], {
        errorMap: () => ({ message: t('validation.gender_invalid') })
      })
      .optional(),
    avatarUrl: z
      .union([
        z.string().url(t('validation.avatar_url_invalid')),
        z.string().length(0),
        z.undefined()
      ])
      .optional()
  })

// ==================== 获取用户资料 ====================

users.get('/profile', authMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t') // 获取翻译函数

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('unauthorized')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('user.not_found')
        },
        404
      )
    }

    // 获取用户配置文件
    let userProfile = await getUserProfile(env, dbUser.id)

    if (!userProfile) {
      // 如果用户配置文件不存在，创建一个默认的
      const t = c.get('t') // 获取翻译函数
      const defaultName = user.email?.split('@')[0] || t('user.default_name')
      const [newProfile] = await createUserProfile(env, {
        userId: dbUser.id,
        nickname: defaultName,
        gender: 'other'
      })
      userProfile = newProfile
    }

    return c.json(
      {
        success: true,
        userProfile: {
          id: userProfile.id,
          userId: userProfile.userId,
          nickname: userProfile.nickname,
          gender: userProfile.gender,
          avatarUrl: userProfile.avatarUrl,
          createdAt: userProfile.createdAt,
          updatedAt: userProfile.updatedAt
        }
      },
      200
    )
  } catch (error) {
    console.error('获取用户资料失败:', error)
    const t = c.get('t') // 获取翻译函数
    return c.json(
      {
        success: false,
        message: t('internal_error')
      },
      500
    )
  }
})

// ==================== 更新用户资料 ====================

users.put('/profile', authMiddleware, async c => {
  const t = c.get('t') // 获取翻译函数
  const profileUpdateSchema = getProfileUpdateSchema(t)

  // 验证请求数据
  const validationResult = profileUpdateSchema.safeParse(c.req.json())
  if (!validationResult.success) {
    return c.json(
      {
        success: false,
        message: validationResult.error.errors[0].message,
        errors: validationResult.error.errors
      },
      400
    )
  }

  const data = validationResult.data
  try {
    const user = c.get('user')
    const env = c.env

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('unauthorized')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('user.not_found')
        },
        404
      )
    }

    // 检查用户配置文件是否存在
    let userProfile = await getUserProfile(env, dbUser.id)

    if (!userProfile) {
      // 如果不存在，创建新的配置文件
      const [newProfile] = await createUserProfile(env, {
        userId: dbUser.id,
        nickname: data.nickname || user.email?.split('@')[0] || t('user.default_name'),
        gender: data.gender || 'other',
        avatarUrl: data.avatarUrl
      })
      userProfile = newProfile
    } else {
      // 如果存在，更新配置文件
      const [updatedProfile] = await updateUserProfile(env, dbUser.id, data)
      userProfile = updatedProfile
    }

    return c.json(
      {
        success: true,
        userProfile: {
          id: userProfile.id,
          userId: userProfile.userId,
          nickname: userProfile.nickname,
          gender: userProfile.gender,
          avatarUrl: userProfile.avatarUrl,
          createdAt: userProfile.createdAt,
          updatedAt: userProfile.updatedAt
        }
      },
      200
    )
  } catch (error) {
    console.error('更新用户资料失败:', error)
    const t = c.get('t') // 获取翻译函数
    return c.json(
      {
        success: false,
        message: t('internal_error')
      },
      500
    )
  }
})

// ==================== 获取用户订阅信息 ====================

users.get('/subscription', authMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t') // 获取翻译函数

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('unauthorized')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('user.not_found')
        },
        404
      )
    }

    // 获取用户当前活跃订阅
    const subscription = await getUserActiveSubscription(env, dbUser.id)

    if (!subscription) {
      return c.json(
        {
          success: true,
          data: null,
          message: t('membership.not_found')
        },
        200
      )
    }

    // 计算剩余天数
    const daysRemaining = Math.ceil(
      (new Date(subscription.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    )

    return c.json(
      {
        success: true,
        data: {
          id: subscription.id,
          planId: subscription.planId,
          startDate: subscription.startDate,
          endDate: subscription.endDate,
          status: subscription.status,
          autoRenew: subscription.autoRenew,
          daysRemaining: Math.max(0, daysRemaining),
          isActive: subscription.status === 'active' && daysRemaining > 0
        }
      },
      200
    )
  } catch (error) {
    console.error('获取用户订阅信息失败:', error)
    const t = c.get('t') // 获取翻译函数
    return c.json(
      {
        success: false,
        message: t('internal_error')
      },
      500
    )
  }
})

// ==================== 获取用户订阅历史 ====================

users.get('/subscription/history', authMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t') // 获取翻译函数

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('unauthorized')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('user.not_found')
        },
        404
      )
    }

    // 获取查询参数
    const limit = Number.parseInt(c.req.query('limit') || '20')

    // 获取用户订阅历史
    const history = await getUserSubscriptionHistory(env, dbUser.id)

    return c.json(
      {
        success: true,
        data: history.slice(0, limit)
      },
      200
    )
  } catch (error) {
    console.error('获取用户订阅历史失败:', error)
    const t = c.get('t') // 获取翻译函数
    return c.json(
      {
        success: false,
        message: t('internal_error')
      },
      500
    )
  }
})

// ==================== 获取用户积分信息 ====================

users.get('/points', authMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t') // 获取翻译函数

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('unauthorized')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('user.not_found')
        },
        404
      )
    }

    // 获取用户积分信息
    const userPointsData = await getUserPoints(env, dbUser.id)

    return c.json(
      {
        success: true,
        data: {
          totalPoints: userPointsData.totalPoints,
          usedPoints: userPointsData.usedPoints,
          availablePoints: userPointsData.availablePoints,
          lastUpdated: userPointsData.lastUpdated
        }
      },
      200
    )
  } catch (error) {
    console.error('获取用户积分信息失败:', error)
    const t = c.get('t') // 获取翻译函数
    return c.json(
      {
        success: false,
        message: t('internal_error')
      },
      500
    )
  }
})

// ==================== 获取用户积分交易记录 ====================

users.get('/points/transactions', authMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t') // 获取翻译函数

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('unauthorized')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('user.not_found')
        },
        404
      )
    }

    // 获取查询参数
    const limit = Number.parseInt(c.req.query('limit') || '50')

    // 获取用户积分交易记录
    const transactions = await getUserPointsTransactions(env, dbUser.id, limit)

    return c.json(
      {
        success: true,
        data: transactions
      },
      200
    )
  } catch (error) {
    console.error('获取用户积分交易记录失败:', error)
    const t = c.get('t') // 获取翻译函数
    return c.json(
      {
        success: false,
        message: t('user.points_transactions_get_failed')
      },
      500
    )
  }
})

// ==================== 获取用户完整状态信息 ====================

users.get('/status', authMiddleware, async c => {
  try {
    const user = c.get('user')
    const env = c.env
    const t = c.get('t') // 获取翻译函数

    if (!user) {
      return c.json(
        {
          success: false,
          message: t('unauthorized')
        },
        401
      )
    }

    // 获取数据库用户信息
    const dbUser = await getUserBySupabaseId(env, user.id)
    if (!dbUser) {
      return c.json(
        {
          success: false,
          message: t('user.not_found')
        },
        404
      )
    }

    // 并行获取用户的各种信息
    const [userProfile, activeSubscription, userPointsData] = await Promise.all([
      getUserProfile(env, dbUser.id),
      getUserActiveSubscription(env, dbUser.id),
      getUserPoints(env, dbUser.id)
    ])

    // 计算会员状态
    const isMember =
      activeSubscription &&
      activeSubscription.status === 'active' &&
      new Date(activeSubscription.endDate) > new Date()

    const daysRemaining = activeSubscription
      ? Math.ceil(
          (new Date(activeSubscription.endDate).getTime() - new Date().getTime()) /
            (1000 * 60 * 60 * 24)
        )
      : 0

    return c.json(
      {
        success: true,
        data: {
          // 用户基本信息
          user: {
            id: user.id,
            email: user.email,
            emailConfirmed: user.email_confirmed_at !== null,
            dbUserId: dbUser.id
          },
          // 用户配置文件
          profile: userProfile
            ? {
                nickname: userProfile.nickname,
                gender: userProfile.gender,
                avatarUrl: userProfile.avatarUrl
              }
            : null,
          // 会员状态
          membership: {
            isMember,
            subscription: activeSubscription
              ? {
                  id: activeSubscription.id,
                  planId: activeSubscription.planId,
                  startDate: activeSubscription.startDate,
                  endDate: activeSubscription.endDate,
                  status: activeSubscription.status,
                  daysRemaining: Math.max(0, daysRemaining)
                }
              : null
          },
          // 积分信息
          points: {
            totalPoints: userPointsData.totalPoints,
            usedPoints: userPointsData.usedPoints,
            availablePoints: userPointsData.availablePoints,
            lastUpdated: userPointsData.lastUpdated
          },
          // 权限信息
          permissions: {
            canAccessPremiumTemplates: isMember,
            canUseAdvancedFeatures: isMember,
            hasUnlimitedGenerations: false
          }
        }
      },
      200
    )
  } catch (error) {
    console.error('获取用户状态信息失败:', error)
    const t = c.get('t') // 获取翻译函数
    return c.json(
      {
        success: false,
        message: t('user.status_get_failed')
      },
      500
    )
  }
})

export default users

import React, { useState, useEffect } from 'react'
import {
  Card,
  Button,
  Space,
  Input,
  InputNumber,
  Upload,
  Modal,
  Form,
  Typography,
  Row,
  Col,
  List,
  Collapse,
  Tag,
  message,
  Tabs,
  Image,
  Tooltip,
  Popconfirm
} from 'antd'
import {
  PlusOutlined,
  DeleteOutlined,
  EditOutlined,
  UploadOutlined,
  ClockCircleOutlined,
  PictureOutlined,
  SoundOutlined,
  SaveOutlined,
  EyeOutlined
} from '@ant-design/icons'

const { Title, Text } = Typography
const { Panel } = Collapse

// 剧本内容数据结构
interface Command {
  command: string
  time: string
}

interface Picture {
  name: string
  pic: string
}

interface Intensity {
  thrust: number
  suction: number
  vibrate: number
}

interface Stage {
  stage: number
  stageTitle: string
  pics: Picture[]
  intensity: Record<string, Intensity>
}

interface ScriptContent {
  commands: Command[]
  stages: Stage[]
}

interface ScriptEditorProps {
  scriptId?: string
  initialContent?: ScriptContent
  onSave?: (content: ScriptContent) => void
  onCancel?: () => void
}

const ScriptEditor: React.FC<ScriptEditorProps> = ({
  scriptId,
  initialContent,
  onSave,
  onCancel
}) => {
  const [content, setContent] = useState<ScriptContent>(
    initialContent || { commands: [], stages: [] }
  )
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('commands')
  
  // 指令相关状态
  const [commandModalVisible, setCommandModalVisible] = useState(false)
  const [editingCommand, setEditingCommand] = useState<{ index: number; command: Command } | null>(null)
  const [commandForm] = Form.useForm()
  
  // 阶段相关状态
  const [stageModalVisible, setStageModalVisible] = useState(false)
  const [editingStage, setEditingStage] = useState<{ index: number; stage: Stage } | null>(null)
  const [stageForm] = Form.useForm()
  
  // 图片相关状态
  const [picModalVisible, setPicModalVisible] = useState(false)
  const [currentStageIndex, setCurrentStageIndex] = useState<number>(0)
  const [editingPic, setEditingPic] = useState<{ index: number; pic: Picture } | null>(null)
  const [picForm] = Form.useForm()
  
  // 强度设置相关状态
  const [intensityModalVisible, setIntensityModalVisible] = useState(false)
  const [intensityTime, setIntensityTime] = useState('')
  const [intensityForm] = Form.useForm()

  useEffect(() => {
    if (scriptId) {
      loadScriptContent()
    }
  }, [scriptId])

  const loadScriptContent = async () => {
    try {
      setLoading(true)
      // TODO: 从API加载剧本内容
      // const content = await scriptService.getScriptContent(scriptId)
      // setContent(content)
      setLoading(false)
    } catch (error) {
      console.error('加载剧本内容失败:', error)
      message.error('加载剧本内容失败')
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setLoading(true)
      if (onSave) {
        onSave(content)
      }
      message.success('保存成功')
      setLoading(false)
    } catch (error) {
      console.error('保存失败:', error)
      message.error('保存失败')
      setLoading(false)
    }
  }

  // 指令管理
  const handleAddCommand = () => {
    setEditingCommand(null)
    commandForm.resetFields()
    setCommandModalVisible(true)
  }

  const handleEditCommand = (index: number) => {
    const command = content.commands[index]
    setEditingCommand({ index, command })
    commandForm.setFieldsValue(command)
    setCommandModalVisible(true)
  }

  const handleDeleteCommand = (index: number) => {
    const newCommands = [...content.commands]
    newCommands.splice(index, 1)
    setContent({ ...content, commands: newCommands })
  }

  const handleCommandSubmit = (values: Command) => {
    const newCommands = [...content.commands]
    if (editingCommand) {
      newCommands[editingCommand.index] = values
    } else {
      newCommands.push(values)
    }
    // 按时间排序
    newCommands.sort((a, b) => {
      const timeA = a.time.split(':').reduce((acc, time) => (60 * acc) + +time, 0)
      const timeB = b.time.split(':').reduce((acc, time) => (60 * acc) + +time, 0)
      return timeA - timeB
    })
    setContent({ ...content, commands: newCommands })
    setCommandModalVisible(false)
  }

  // 阶段管理
  const handleAddStage = () => {
    setEditingStage(null)
    stageForm.resetFields()
    setStageModalVisible(true)
  }

  const handleEditStage = (index: number) => {
    const stage = content.stages[index]
    setEditingStage({ index, stage })
    stageForm.setFieldsValue({
      stage: stage.stage,
      stageTitle: stage.stageTitle
    })
    setStageModalVisible(true)
  }

  const handleDeleteStage = (index: number) => {
    const newStages = [...content.stages]
    newStages.splice(index, 1)
    // 重新编号
    newStages.forEach((stage, i) => {
      stage.stage = i + 1
    })
    setContent({ ...content, stages: newStages })
  }

  const handleStageSubmit = (values: { stage: number; stageTitle: string }) => {
    const newStages = [...content.stages]
    if (editingStage) {
      newStages[editingStage.index] = {
        ...newStages[editingStage.index],
        ...values
      }
    } else {
      newStages.push({
        ...values,
        stage: newStages.length + 1,
        pics: [],
        intensity: {}
      })
    }
    setContent({ ...content, stages: newStages })
    setStageModalVisible(false)
  }

  // 图片管理
  const handleAddPicture = (stageIndex: number) => {
    setCurrentStageIndex(stageIndex)
    setEditingPic(null)
    picForm.resetFields()
    setPicModalVisible(true)
  }

  const handleEditPicture = (stageIndex: number, picIndex: number) => {
    const pic = content.stages[stageIndex].pics[picIndex]
    setCurrentStageIndex(stageIndex)
    setEditingPic({ index: picIndex, pic })
    picForm.setFieldsValue(pic)
    setPicModalVisible(true)
  }

  const handleDeletePicture = (stageIndex: number, picIndex: number) => {
    const newStages = [...content.stages]
    newStages[stageIndex].pics.splice(picIndex, 1)
    setContent({ ...content, stages: newStages })
  }

  const handlePictureSubmit = (values: Picture) => {
    const newStages = [...content.stages]
    if (editingPic) {
      newStages[currentStageIndex].pics[editingPic.index] = values
    } else {
      newStages[currentStageIndex].pics.push(values)
    }
    setContent({ ...content, stages: newStages })
    setPicModalVisible(false)
  }

  // 强度设置
  const handleAddIntensity = (stageIndex: number) => {
    setCurrentStageIndex(stageIndex)
    setIntensityTime('')
    intensityForm.resetFields()
    setIntensityModalVisible(true)
  }

  const handleEditIntensity = (stageIndex: number, time: string) => {
    const intensity = content.stages[stageIndex].intensity[time]
    setCurrentStageIndex(stageIndex)
    setIntensityTime(time)
    intensityForm.setFieldsValue({ time, ...intensity })
    setIntensityModalVisible(true)
  }

  const handleDeleteIntensity = (stageIndex: number, time: string) => {
    const newStages = [...content.stages]
    delete newStages[stageIndex].intensity[time]
    setContent({ ...content, stages: newStages })
  }

  const handleIntensitySubmit = (values: any) => {
    const { time, thrust, suction, vibrate } = values
    const newStages = [...content.stages]
    if (intensityTime && intensityTime !== time) {
      // 如果时间改变了，删除旧的
      delete newStages[currentStageIndex].intensity[intensityTime]
    }
    newStages[currentStageIndex].intensity[time] = { thrust, suction, vibrate }
    setContent({ ...content, stages: newStages })
    setIntensityModalVisible(false)
  }

  const parseTime = (timeStr: string): number => {
    const parts = timeStr.split(':')
    return parseInt(parts[0]) * 60 + parseInt(parts[1])
  }

  return (
    <div style={{ padding: 24 }}>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Title level={3}>剧本内容编辑器</Title>
        <Space>
          <Button icon={<EyeOutlined />}>预览</Button>
          <Button type="primary" icon={<SaveOutlined />} loading={loading} onClick={handleSave}>
            保存
          </Button>
          {onCancel && (
            <Button onClick={onCancel}>
              取消
            </Button>
          )}
        </Space>
      </div>

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        items={[
          {
            key: 'commands',
            label: `指令管理 (${content.commands.length})`,
            children: (
              <Card>
                <div style={{ marginBottom: 16 }}>
                  <Button type="primary" icon={<PlusOutlined />} onClick={handleAddCommand}>
                    添加指令
                  </Button>
                </div>
                <List
                  dataSource={content.commands}
                  renderItem={(command, index) => (
                    <List.Item
                      actions={[
                        <Tooltip title="编辑">
                          <Button 
                            type="link" 
                            icon={<EditOutlined />} 
                            onClick={() => handleEditCommand(index)}
                          />
                        </Tooltip>,
                        <Popconfirm
                          title="确定删除这个指令吗？"
                          onConfirm={() => handleDeleteCommand(index)}
                        >
                          <Tooltip title="删除">
                            <Button type="link" danger icon={<DeleteOutlined />} />
                          </Tooltip>
                        </Popconfirm>
                      ]}
                    >
                      <List.Item.Meta
                        avatar={<Tag color="blue" icon={<ClockCircleOutlined />}>{command.time}</Tag>}
                        title={command.command}
                      />
                    </List.Item>
                  )}
                />
              </Card>
            )
          },
          {
            key: 'stages',
            label: `阶段管理 (${content.stages.length})`,
            children: (
          <Card>
            <div style={{ marginBottom: 16 }}>
              <Button type="primary" icon={<PlusOutlined />} onClick={handleAddStage}>
                添加阶段
              </Button>
            </div>
            <Collapse>
              {content.stages.map((stage, stageIndex) => (
                <Panel
                  header={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>
                        阶段 {stage.stage}: {stage.stageTitle}
                      </span>
                      <Space>
                        <Tag color="green">{stage.pics.length} 图片</Tag>
                        <Tag color="orange">{Object.keys(stage.intensity).length} 强度点</Tag>
                      </Space>
                    </div>
                  }
                  key={stageIndex}
                  extra={
                    <Space onClick={(e) => e.stopPropagation()}>
                      <Tooltip title="编辑阶段">
                        <Button 
                          type="link" 
                          size="small" 
                          icon={<EditOutlined />}
                          onClick={() => handleEditStage(stageIndex)}
                        />
                      </Tooltip>
                      <Popconfirm
                        title="确定删除这个阶段吗？"
                        onConfirm={() => handleDeleteStage(stageIndex)}
                      >
                        <Tooltip title="删除阶段">
                          <Button type="link" size="small" danger icon={<DeleteOutlined />} />
                        </Tooltip>
                      </Popconfirm>
                    </Space>
                  }
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Card size="small" title={<><PictureOutlined /> 图片管理</>}>
                        <div style={{ marginBottom: 8 }}>
                          <Button 
                            size="small" 
                            icon={<PlusOutlined />}
                            onClick={() => handleAddPicture(stageIndex)}
                          >
                            添加图片
                          </Button>
                        </div>
                        <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                          {stage.pics.map((pic, picIndex) => (
                            <div key={picIndex} style={{ position: 'relative' }}>
                              <Image
                                src={pic.pic}
                                width={60}
                                height={60}
                                style={{ objectFit: 'cover', borderRadius: 4 }}
                                fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6U"
                              />
                              <div style={{ position: 'absolute', top: 2, right: 2 }}>
                                <Space size="small">
                                  <Button 
                                    type="primary" 
                                    size="small" 
                                    icon={<EditOutlined />}
                                    style={{ fontSize: '10px', padding: '0 4px' }}
                                    onClick={() => handleEditPicture(stageIndex, picIndex)}
                                  />
                                  <Button 
                                    danger 
                                    size="small" 
                                    icon={<DeleteOutlined />}
                                    style={{ fontSize: '10px', padding: '0 4px' }}
                                    onClick={() => handleDeletePicture(stageIndex, picIndex)}
                                  />
                                </Space>
                              </div>
                              <Text style={{ fontSize: '10px', display: 'block', textAlign: 'center' }}>
                                {pic.name}
                              </Text>
                            </div>
                          ))}
                        </div>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card size="small" title={<><SoundOutlined /> 强度设置</>}>
                        <div style={{ marginBottom: 8 }}>
                          <Button 
                            size="small" 
                            icon={<PlusOutlined />}
                            onClick={() => handleAddIntensity(stageIndex)}
                          >
                            添加强度点
                          </Button>
                        </div>
                        {Object.entries(stage.intensity)
                          .sort(([a], [b]) => parseTime(a) - parseTime(b))
                          .map(([time, intensity]) => (
                            <div key={time} style={{ marginBottom: 8, padding: 8, border: '1px solid #f0f0f0', borderRadius: 4 }}>
                              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                <Space>
                                  <Tag color="blue">{time}</Tag>
                                  <Text style={{ fontSize: '12px' }}>
                                    推力:{intensity.thrust} 吸力:{intensity.suction} 震动:{intensity.vibrate}
                                  </Text>
                                </Space>
                                <Space>
                                  <Button 
                                    type="link" 
                                    size="small" 
                                    icon={<EditOutlined />}
                                    onClick={() => handleEditIntensity(stageIndex, time)}
                                  />
                                  <Button 
                                    type="link" 
                                    size="small" 
                                    danger 
                                    icon={<DeleteOutlined />}
                                    onClick={() => handleDeleteIntensity(stageIndex, time)}
                                  />
                                </Space>
                              </div>
                            </div>
                          ))}
                      </Card>
                    </Col>
                  </Row>
                </Panel>
              ))}
            </Collapse>
          </Card>
            )
          }
        ]}
      />

      {/* 指令编辑模态框 */}
      <Modal
        title={editingCommand ? '编辑指令' : '添加指令'}
        open={commandModalVisible}
        onCancel={() => setCommandModalVisible(false)}
        footer={null}
      >
        <Form
          form={commandForm}
          layout="vertical"
          onFinish={handleCommandSubmit}
        >
          <Form.Item
            name="time"
            label="时间点"
            rules={[{ required: true, message: '请输入时间点' }]}
          >
            <Input placeholder="格式: 00:01:30" />
          </Form.Item>
          <Form.Item
            name="command"
            label="指令内容"
            rules={[{ required: true, message: '请输入指令内容' }]}
          >
            <Input placeholder="请输入指令内容" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setCommandModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">确定</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 阶段编辑模态框 */}
      <Modal
        title={editingStage ? '编辑阶段' : '添加阶段'}
        open={stageModalVisible}
        onCancel={() => setStageModalVisible(false)}
        footer={null}
      >
        <Form
          form={stageForm}
          layout="vertical"
          onFinish={handleStageSubmit}
        >
          <Form.Item
            name="stage"
            label="阶段编号"
            rules={[{ required: true, message: '请输入阶段编号' }]}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="stageTitle"
            label="阶段标题"
            rules={[{ required: true, message: '请输入阶段标题' }]}
          >
            <Input placeholder="请输入阶段标题" />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setStageModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">确定</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 图片编辑模态框 */}
      <Modal
        title={editingPic ? '编辑图片' : '添加图片'}
        open={picModalVisible}
        onCancel={() => setPicModalVisible(false)}
        footer={null}
      >
        <Form
          form={picForm}
          layout="vertical"
          onFinish={handlePictureSubmit}
        >
          <Form.Item
            name="name"
            label="图片名称"
            rules={[{ required: true, message: '请输入图片名称' }]}
          >
            <Input placeholder="请输入图片名称" />
          </Form.Item>
          <Form.Item
            name="pic"
            label="图片URL"
            rules={[{ required: true, message: '请输入图片URL' }]}
          >
            <Input placeholder="请输入图片URL" />
          </Form.Item>
          <Form.Item label="上传图片">
            <Upload
              listType="picture-card"
              maxCount={1}
              beforeUpload={() => false}
            >
              <div>
                <UploadOutlined />
                <div style={{ marginTop: 8 }}>上传</div>
              </div>
            </Upload>
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setPicModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">确定</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 强度设置模态框 */}
      <Modal
        title="设置强度"
        open={intensityModalVisible}
        onCancel={() => setIntensityModalVisible(false)}
        footer={null}
      >
        <Form
          form={intensityForm}
          layout="vertical"
          onFinish={handleIntensitySubmit}
        >
          <Form.Item
            name="time"
            label="时间点"
            rules={[{ required: true, message: '请输入时间点' }]}
          >
            <Input placeholder="格式: 00:01:30" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="thrust"
                label="推力强度"
                rules={[{ required: true, message: '请设置推力强度' }]}
              >
                <InputNumber min={-1} max={3} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="suction"
                label="吸力强度"
                rules={[{ required: true, message: '请设置吸力强度' }]}
              >
                <InputNumber min={-1} max={3} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="vibrate"
                label="震动强度"
                rules={[{ required: true, message: '请设置震动强度' }]}
              >
                <InputNumber min={-1} max={3} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <div style={{ fontSize: '12px', color: '#999', marginBottom: 16 }}>
            强度范围: -1(停止) 0(最低) 1(低) 2(中) 3(高)
          </div>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setIntensityModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">确定</Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ScriptEditor
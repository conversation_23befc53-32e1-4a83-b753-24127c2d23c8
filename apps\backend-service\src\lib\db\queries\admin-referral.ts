import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { WithdrawRequest } from '../schema';
import type { Env } from '@/types/env';
import { unfreezeCommissionBalance, completeWithdraw } from './commission';

// ==================== 管理员提现审核 ====================

/**
 * 获取待审核的提现申请列表
 */
export async function getPendingWithdrawRequests(
  env: Env,
  page = 1,
  limit = 20
): Promise<{
  list: Array<WithdrawRequest & { userEmail: string }>;
  total: number;
}> {
  const supabase = getSupabase(env);
  const offset = (page - 1) * limit;

  // 获取待审核的提现申请
  const listResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select(`
      *,
      user:user_id (
        email
      )
    `)
    .eq('status', 'pending')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  const { data: listData } = handleSupabaseResult(listResult);

  // 获取总数
  const countResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select('id', { count: 'exact' })
    .eq('status', 'pending');

  const { count } = countResult;

  const list = (listData || []).map((item: any) => ({
    ...item,
    userEmail: item.user?.email || '',
  })) as Array<WithdrawRequest & { userEmail: string }>;

  return {
    list,
    total: count || 0,
  };
}

/**
 * 获取所有提现申请列表
 */
export async function getAllWithdrawRequests(
  env: Env,
  page = 1,
  limit = 20,
  status?: 'pending' | 'approved' | 'rejected' | 'completed'
): Promise<{
  list: Array<WithdrawRequest & { userEmail: string }>;
  total: number;
}> {
  const supabase = getSupabase(env);
  const offset = (page - 1) * limit;

  let query = supabase.from(TABLE_NAMES.withdrawRequest).select(`
      *,
      user:user_id (
        email
      )
    `);

  // 如果指定了状态，添加筛选条件
  if (status) {
    query = query.eq('status', status);
  }

  const listResult = await query
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  const { data: listData } = handleSupabaseResult(listResult);

  // 获取总数
  let countQuery = supabase.from(TABLE_NAMES.withdrawRequest).select('id', { count: 'exact' });

  if (status) {
    countQuery = countQuery.eq('status', status);
  }

  const { count } = await countQuery;

  const list = (listData || []).map((item: any) => ({
    ...item,
    userEmail: item.user?.email || '',
  })) as Array<WithdrawRequest & { userEmail: string }>;

  return {
    list,
    total: count || 0,
  };
}

/**
 * 获取提现申请详情
 */
export async function getWithdrawRequestById(
  env: Env,
  requestId: string
): Promise<WithdrawRequest | null> {
  const supabase = getSupabase(env);

  const result = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select('*')
    .eq('id', requestId)
    .single();

  const { data, error } = handleSupabaseSingleResult(result);
  if (error) return null;
  return data as WithdrawRequest;
}

/**
 * 审核提现申请（批准或拒绝）
 */
export async function reviewWithdrawRequest(
  env: Env,
  requestId: string,
  action: 'approve' | 'reject',
  adminUserId: string,
  adminNote?: string
): Promise<void> {
  const supabase = getSupabase(env);

  // 获取提现申请信息
  const request = await getWithdrawRequestById(env, requestId);
  if (!request) {
    throw new Error('提现申请不存在');
  }

  if (request.status !== 'pending') {
    throw new Error('该申请已被处理');
  }

  const newStatus = action === 'approve' ? 'approved' : 'rejected';

  // 更新提现申请状态
  const updateResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .update({
      status: newStatus,
      admin_note: adminNote || '',
      processed_by: adminUserId,
      processed_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', requestId);

  const { error: updateError } = handleSupabaseResult(updateResult);
  if (updateError) throw updateError;

  // 如果是拒绝，需要解冻用户的佣金
  if (action === 'reject') {
    const amount = Number.parseFloat(request.amount);
    await unfreezeCommissionBalance(env, request.userId, amount);
  }
}

/**
 * 标记提现完成
 */
export async function completeWithdrawRequest(
  env: Env,
  requestId: string,
  adminUserId: string,
  adminNote?: string
): Promise<void> {
  const supabase = getSupabase(env);

  // 获取提现申请信息
  const request = await getWithdrawRequestById(env, requestId);
  if (!request) {
    throw new Error('提现申请不存在');
  }

  if (request.status !== 'approved') {
    throw new Error('只能完成已批准的提现申请');
  }

  // 更新提现申请状态
  const updateResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .update({
      status: 'completed',
      admin_note: adminNote || request.adminNote,
      processed_by: adminUserId,
      processed_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })
    .eq('id', requestId);

  const { error: updateError } = handleSupabaseResult(updateResult);
  if (updateError) throw updateError;

  // 更新用户佣金账户（从冻结余额转为已提现）
  const amount = Number.parseFloat(request.amount);
  await completeWithdraw(env, request.userId, amount);
}

/**
 * 获取提现统计数据
 */
export async function getWithdrawStats(env: Env): Promise<{
  pendingCount: number;
  pendingAmount: number;
  completedCount: number;
  completedAmount: number;
  rejectedCount: number;
  totalAmount: number;
}> {
  const supabase = getSupabase(env);

  // 获取待审核统计
  const pendingResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select('amount')
    .eq('status', 'pending');

  const { data: pendingData } = handleSupabaseResult(pendingResult);
  const pendingCount = pendingData?.length || 0;
  const pendingAmount = (pendingData || []).reduce(
    (sum: number, item: any) => sum + Number.parseFloat(item.amount),
    0
  );

  // 获取已完成统计
  const completedResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select('amount')
    .eq('status', 'completed');

  const { data: completedData } = handleSupabaseResult(completedResult);
  const completedCount = completedData?.length || 0;
  const completedAmount = (completedData || []).reduce(
    (sum: number, item: any) => sum + Number.parseFloat(item.amount),
    0
  );

  // 获取已拒绝统计
  const rejectedResult = await supabase
    .from(TABLE_NAMES.withdrawRequest)
    .select('id', { count: 'exact' })
    .eq('status', 'rejected');

  const { count: rejectedCount } = rejectedResult;

  // 获取总金额统计
  const totalResult = await supabase.from(TABLE_NAMES.withdrawRequest).select('amount');

  const { data: totalData } = handleSupabaseResult(totalResult);
  const totalAmount = (totalData || []).reduce(
    (sum: number, item: any) => sum + Number.parseFloat(item.amount),
    0
  );

  return {
    pendingCount,
    pendingAmount: Math.round(pendingAmount * 100) / 100,
    completedCount,
    completedAmount: Math.round(completedAmount * 100) / 100,
    rejectedCount: rejectedCount || 0,
    totalAmount: Math.round(totalAmount * 100) / 100,
  };
}

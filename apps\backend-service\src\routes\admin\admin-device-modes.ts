import { Hono } from 'hono'
import { z } from 'zod'
import { zValidator } from '@hono/zod-validator'
import { authMiddleware } from '@/middleware/auth'
import type { Env } from '@/types/env'
import { getSupabase } from '@/lib/db/queries/base'
import { handleSupabaseResult, TABLE_NAMES } from '@/lib/db/supabase-types'
import { getUserBySupabaseId } from '@/lib/db/queries/user'

const adminDeviceModes = new Hono<{ Bindings: Env }>()

// 检查管理员权限
async function checkAdminPermission(c: any): Promise<boolean> {
  try {
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return false
    }

    // 检查用户的 user_metadata 中是否有管理员标识
    const userMetadata =
      supabaseUser.user_metadata || (supabaseUser as any).raw_user_meta_data || {}
    const isAdmin = userMetadata.role === 'admin' || userMetadata.isAdmin === true

    if (isAdmin) {
      return true
    }

    // 备用检查：检查特定的管理员邮箱
    const adminEmails = [
      '<EMAIL>'
      // 在这里添加其他管理员邮箱
    ]

    if (adminEmails.includes(supabaseUser.email)) {
      return true
    }

    return false
  } catch (error) {
    console.error('检查管理员权限失败:', error)
    return false
  }
}

// ==================== 设备模式列表管理 ====================

// 查询参数验证
const modeListSchema = z.object({
  page: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('1'),
  pageSize: z
    .string()
    .transform(val => Number.parseInt(val))
    .default('20'),
  keyword: z.string().optional(),
  isActive: z
    .string()
    .transform(val => val === 'true')
    .optional()
})

// 获取设备模式列表
adminDeviceModes.get('/', authMiddleware, zValidator('query', modeListSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const { page, pageSize, keyword, isActive } = c.req.valid('query')
    const env = c.env

    console.log('📋 [ADMIN-DEVICE-MODES] 获取设备模式列表:', {
      page,
      pageSize,
      keyword,
      isActive
    })

    // 从数据库查询设备模式列表
    const supabase = getSupabase(env)

    let query = supabase.from('DeviceMode').select(`
      *,
      commandSet:DeviceCommandSet(
        id,
        name,
        command,
        description,
        is_active
      )
    `)

    // 构建筛选条件
    if (keyword) {
      query = query.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`)
    }

    if (isActive !== undefined) {
      query = query.eq('is_active', isActive)
    }

    // 添加排序
    query = query.order('created_at', { ascending: false })

    // 计算总数
    let countQuery = supabase.from('DeviceMode').select('*', { count: 'exact', head: true })

    if (keyword) {
      countQuery = countQuery.or(`name.ilike.%${keyword}%,description.ilike.%${keyword}%`)
    }
    if (isActive !== undefined) {
      countQuery = countQuery.eq('is_active', isActive)
    }

    const countResult = await countQuery
    const total = countResult.count || 0

    // 分页查询
    const offset = (page - 1) * pageSize
    const result = await query.range(offset, offset + pageSize - 1)
    const { data: modes, error } = handleSupabaseResult(result)

    if (error) throw error

    console.log(`📋 [ADMIN-DEVICE-MODES] 查询到 ${modes.length} 个设备模式，共 ${total} 个`)

    // 转换数据格式 (snake_case -> camelCase)
    const formattedModes = (modes || []).map((mode: any) => ({
      id: mode.id,
      name: mode.name,
      description: mode.description,
      commandSetId: mode.command_set_id,
      isActive: mode.is_active,
      createdAt: mode.created_at,
      updatedAt: mode.updated_at,
      commandSet: mode.commandSet ? {
        id: mode.commandSet.id,
        name: mode.commandSet.name,
        command: mode.commandSet.command,
        description: mode.commandSet.description,
        isActive: mode.commandSet.is_active
      } : null
    }))

    return c.json({
      success: true,
      data: {
        data: formattedModes,
        total,
        page,
        pageSize,
        totalPages: Math.ceil(total / pageSize)
      }
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-MODES] 获取设备模式列表失败:', error)
    return c.json(
      {
        success: false,
        message: '获取设备模式列表失败'
      },
      500
    )
  }
})

// ==================== 设备模式详情管理 ====================

// 获取设备模式详情
adminDeviceModes.get('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🔍 [ADMIN-DEVICE-MODES] 获取设备模式详情:', id)

    // 查询设备模式详情
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceMode')
      .select('*')
      .eq('id', id)
      .single()

    const { data: mode, error } = handleSupabaseResult(result)

    if (error || !mode) {
      return c.json(
        {
          success: false,
          message: '设备模式不存在'
        },
        404
      )
    }

    // 格式化数据
    const formattedMode = {
      id: mode.id,
      name: mode.name,
      description: mode.description,
      commandSetId: mode.command_set_id,
      isActive: mode.is_active,
      createdAt: mode.created_at,
      updatedAt: mode.updated_at
    }

    return c.json({
      success: true,
      data: formattedMode
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-MODES] 获取设备模式详情失败:', error)
    return c.json(
      {
        success: false,
        message: '获取设备模式详情失败'
      },
      500
    )
  }
})

// ==================== 设备模式创建和编辑 ====================

// 模式规律验证模式
const patternStepSchema = z.object({
  duration: z.number().min(100, '持续时间不能小于100ms'), // 持续时间（毫秒）
  intensity: z.number().min(0, '强度不能小于0'), // 强度等级，0表示停止
  description: z.string().optional() // 该步骤的描述
})

// 创建设备模式验证模式
const createModeSchema = z.object({
  name: z.string().min(1, '模式名称不能为空'),
  description: z.string().optional(),
  commandSetId: z.string().uuid('指令集ID格式不正确'),
  isActive: z.boolean().default(true)
})

// 创建设备模式
adminDeviceModes.post('/', authMiddleware, zValidator('json', createModeSchema), async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const modeData = c.req.valid('json')
    const env = c.env

    console.log('➕ [ADMIN-DEVICE-MODES] 创建设备模式:', modeData.name)

    // 获取数据库连接
    const supabase = getSupabase(env)

    // 验证指令集是否存在
    const commandSetResult = await supabase
      .from('DeviceCommandSet')
      .select('id')
      .eq('id', modeData.commandSetId)
      .single()

    if (!commandSetResult.data) {
      return c.json({ success: false, message: '指令集不存在' }, 400)
    }

    // 创建设备模式
    const result = await supabase
      .from('DeviceMode')
      .insert([{
        name: modeData.name,
        description: modeData.description,
        command_set_id: modeData.commandSetId,
        is_active: modeData.isActive
      }])
      .select()

    const { data: newModes, error } = handleSupabaseResult(result)

    if (error || !newModes || newModes.length === 0) {
      console.error('❌ [ADMIN-DEVICE-MODES] 数据库错误详情:', error)
      console.error('❌ [ADMIN-DEVICE-MODES] 插入数据:', {
        name: modeData.name,
        description: modeData.description,
        command_set_id: modeData.commandSetId,
        is_active: modeData.isActive
      })
      return c.json({ 
        success: false, 
        message: '创建设备模式失败', 
        error: error ? error.message : 'No data returned'
      }, 500)
    }

    const newMode = newModes[0]

    console.log('✅ [ADMIN-DEVICE-MODES] 设备模式创建成功:', newMode.id)

    return c.json({
      success: true,
      data: {
        id: newMode.id,
        name: newMode.name,
        description: newMode.description,
        commandSetId: newMode.command_set_id,
        isActive: newMode.is_active,
        createdAt: newMode.created_at,
        updatedAt: newMode.updated_at
      },
      message: '设备模式创建成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-MODES] 创建设备模式失败:', error)
    return c.json(
      {
        success: false,
        message: '创建设备模式失败'
      },
      500
    )
  }
})

// 更新设备模式
adminDeviceModes.put(
  '/:id',
  authMiddleware,
  zValidator('json', createModeSchema.partial()),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const updateData = c.req.valid('json')
      const env = c.env

      console.log('📝 [ADMIN-DEVICE-MODES] 更新设备模式:', id)

      // 获取数据库连接
      const supabase = getSupabase(env)

      // 验证指令集是否存在
      if (updateData.commandSetId) {
        const commandSetResult = await supabase
          .from('DeviceCommandSet')
          .select('id')
          .eq('id', updateData.commandSetId)
          .single()

        if (!commandSetResult.data) {
          return c.json({ success: false, message: '指令集不存在' }, 400)
        }
      }

      // 更新设备模式
      const updatePayload: any = {}

      if (updateData.name !== undefined) updatePayload.name = updateData.name
      if (updateData.description !== undefined) updatePayload.description = updateData.description
      if (updateData.commandSetId !== undefined) updatePayload.command_set_id = updateData.commandSetId
      if (updateData.isActive !== undefined) updatePayload.is_active = updateData.isActive

      updatePayload.updated_at = new Date().toISOString()

      const result = await supabase
        .from('DeviceMode')
        .update(updatePayload)
        .eq('id', id)
        .select()

      const { data: updatedModes, error } = handleSupabaseResult(result)

      if (error || !updatedModes || updatedModes.length === 0) {
        return c.json({ success: false, message: '设备模式不存在' }, 404)
      }

      const updatedMode = updatedModes[0]

      console.log('✅ [ADMIN-DEVICE-MODES] 设备模式更新成功:', id)

      return c.json({
        success: true,
        data: {
          id: updatedMode.id,
          name: updatedMode.name,
          description: updatedMode.description,
          commandSetId: updatedMode.command_set_id,
          isActive: updatedMode.is_active,
          createdAt: updatedMode.created_at,
          updatedAt: updatedMode.updated_at
        },
        message: '设备模式更新成功'
      })
    } catch (error) {
      console.error('❌ [ADMIN-DEVICE-MODES] 更新设备模式失败:', error)
      return c.json(
        {
          success: false,
          message: '更新设备模式失败'
        },
        500
      )
    }
  }
)

// 删除设备模式
adminDeviceModes.delete('/:id', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('🗑️ [ADMIN-DEVICE-MODES] 删除设备模式:', id)

    // 删除设备模式
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceMode')
      .delete()
      .eq('id', id)
      .select()

    const { data: deletedModes, error } = handleSupabaseResult(result)

    if (error || !deletedModes || deletedModes.length === 0) {
      return c.json({ success: false, message: '设备模式不存在' }, 404)
    }

    console.log('✅ [ADMIN-DEVICE-MODES] 设备模式删除成功:', id)

    return c.json({
      success: true,
      message: '设备模式删除成功'
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-MODES] 删除设备模式失败:', error)
    return c.json(
      {
        success: false,
        message: '删除设备模式失败'
      },
      500
    )
  }
})

// ==================== 设备模式状态管理 ====================

// 切换设备模式启用状态
adminDeviceModes.post(
  '/:id/toggle-status',
  authMiddleware,
  zValidator(
    'json',
    z.object({
      isActive: z.boolean()
    })
  ),
  async c => {
    try {
      // 检查管理员权限
      const hasPermission = await checkAdminPermission(c)
      if (!hasPermission) {
        return c.json({ success: false, message: '权限不足' }, 403)
      }

      const id = c.req.param('id')
      const { isActive } = c.req.valid('json')
      const env = c.env

      console.log('🔄 [ADMIN-DEVICE-MODES] 切换设备模式状态:', id, isActive)

      // 更新状态
      const supabase = getSupabase(env)
      const result = await supabase
        .from('DeviceMode')
        .update({
          is_active: isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()

      const { data: updatedModes, error } = handleSupabaseResult(result)

      if (error || !updatedModes || updatedModes.length === 0) {
        return c.json({ success: false, message: '设备模式不存在' }, 404)
      }

      console.log('✅ [ADMIN-DEVICE-MODES] 设备模式状态更新成功:', id)

      return c.json({
        success: true,
        message: isActive ? '设备模式已启用' : '设备模式已禁用'
      })
    } catch (error) {
      console.error('❌ [ADMIN-DEVICE-MODES] 切换设备模式状态失败:', error)
      return c.json(
        {
          success: false,
          message: '状态更新失败'
        },
        500
      )
    }
  }
)

// ==================== 模式预览和测试 ====================

// 获取模式预览信息
adminDeviceModes.get('/:id/preview', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    const id = c.req.param('id')
    const env = c.env

    console.log('👁️ [ADMIN-DEVICE-MODES] 获取模式预览:', id)

    // 查询设备模式
    const supabase = getSupabase(env)
    const result = await supabase
      .from('DeviceMode')
      .select('*')
      .eq('id', id)
      .single()

    const { data: mode, error } = handleSupabaseResult(result)

    if (error || !mode) {
      return c.json(
        {
          success: false,
          message: '设备模式不存在'
        },
        404
      )
    }

    // 计算模式统计信息
    const pattern = typeof mode.pattern === 'string' ? JSON.parse(mode.pattern) : mode.pattern
    const totalDuration = pattern.reduce((sum: number, step: any) => sum + step.duration, 0)
    const maxIntensity = Math.max(...pattern.map((step: any) => step.intensity))
    const minIntensity = Math.min(...pattern.map((step: any) => step.intensity))
    const avgIntensity = pattern.reduce((sum: number, step: any) => sum + step.intensity, 0) / pattern.length

    const preview = {
      id: mode.id,
      name: mode.name,
      description: mode.description,
      pattern: pattern,
      statistics: {
        totalDuration: totalDuration,
        totalSteps: pattern.length,
        maxIntensity: maxIntensity,
        minIntensity: minIntensity,
        avgIntensity: Math.round(avgIntensity * 100) / 100
      },
      isActive: mode.is_active
    }

    return c.json({
      success: true,
      data: preview
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-MODES] 获取模式预览失败:', error)
    return c.json(
      {
        success: false,
        message: '获取模式预览失败'
      },
      500
    )
  }
})

// 模式模板接口 - 提供常用的模式模板
adminDeviceModes.get('/templates/list', authMiddleware, async c => {
  try {
    // 检查管理员权限
    const hasPermission = await checkAdminPermission(c)
    if (!hasPermission) {
      return c.json({ success: false, message: '权限不足' }, 403)
    }

    console.log('📋 [ADMIN-DEVICE-MODES] 获取模式模板列表')

    // 预设的模式模板
    const templates = [
      {
        id: 'continuous',
        name: '连续模式',
        description: '持续稳定的强度输出',
        pattern: [
          { duration: 30000, intensity: 2, description: '持续中等强度' }
        ]
      },
      {
        id: 'intermittent',
        name: '间歇模式',
        description: '1秒开启，1秒暂停的交替模式',
        pattern: [
          { duration: 1000, intensity: 2, description: '开启' },
          { duration: 1000, intensity: 0, description: '暂停' }
        ]
      },
      {
        id: 'wave',
        name: '波浪模式',
        description: '强度逐渐增强然后减弱的波浪式变化',
        pattern: [
          { duration: 2000, intensity: 1, description: '低强度' },
          { duration: 2000, intensity: 2, description: '中强度' },
          { duration: 2000, intensity: 3, description: '高强度' },
          { duration: 2000, intensity: 2, description: '中强度' },
          { duration: 2000, intensity: 1, description: '低强度' }
        ]
      },
      {
        id: 'pulse',
        name: '脉冲模式',
        description: '短暂的高强度脉冲',
        pattern: [
          { duration: 500, intensity: 3, description: '高强度脉冲' },
          { duration: 1500, intensity: 0, description: '间隔' }
        ]
      },
      {
        id: 'escalation',
        name: '递增模式',
        description: '强度逐步递增',
        pattern: [
          { duration: 5000, intensity: 1, description: '第一阶段' },
          { duration: 5000, intensity: 2, description: '第二阶段' },
          { duration: 5000, intensity: 3, description: '第三阶段' }
        ]
      }
    ]

    return c.json({
      success: true,
      data: templates
    })
  } catch (error) {
    console.error('❌ [ADMIN-DEVICE-MODES] 获取模式模板失败:', error)
    return c.json(
      {
        success: false,
        message: '获取模式模板失败'
      },
      500
    )
  }
})

export default adminDeviceModes
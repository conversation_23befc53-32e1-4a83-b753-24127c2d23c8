-- ==========================================
-- 为指定邮箱设置会员身份 SQL 脚本
-- 执行环境: Supabase Dashboard > SQL Editor
-- 创建时间: 2025-01-22
-- ==========================================

-- 使用说明：
-- 1. 修改下面的变量值为实际需要的邮箱和会员计划
-- 2. 在 Supabase SQL Editor 中执行此脚本
-- 3. 脚本会自动查找用户并创建会员订阅

-- ==========================================
-- 配置变量 (请修改这些值)
-- ==========================================

DO $$
DECLARE
    -- 🔧 请在这里修改配置参数 🔧
    target_email TEXT := '<EMAIL>';  -- 目标用户邮箱
    membership_plan TEXT := 'pro';            -- 会员计划名称 ('pro', 'elite', 'ultra')
    subscription_days INTEGER := 30;          -- 订阅天数 (默认30天，可自定义)
    
    -- 内部变量
    user_record RECORD;
    plan_record RECORD;
    subscription_id UUID;
    subscription_start_date TIMESTAMP;
    subscription_end_date TIMESTAMP;
BEGIN
    -- 1. 查找目标用户
    SELECT * INTO user_record
    FROM "User" 
    WHERE email = target_email;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION '用户不存在: %', target_email;
    END IF;
    
    RAISE NOTICE '找到用户: % (ID: %)', user_record.email, user_record.id;
    
    -- 2. 查找会员计划
    SELECT * INTO plan_record
    FROM "MembershipPlan" 
    WHERE name = membership_plan AND is_active = true;
    
    IF NOT FOUND THEN
        RAISE EXCEPTION '会员计划不存在或已停用: %', membership_plan;
    END IF;
    
    RAISE NOTICE '使用会员计划: % (价格: %元, 包含积分: %)', 
        plan_record.name, plan_record.price, plan_record.points_included;
    
    -- 3. 设置订阅时间
    subscription_start_date := NOW();
    subscription_end_date := subscription_start_date + INTERVAL '1 day' * subscription_days;
    
    -- 4. 检查是否已有活跃订阅
    IF EXISTS (
        SELECT 1 FROM "UserSubscription" us
        WHERE us.user_id = user_record.id 
        AND us.status = 'active' 
        AND us.end_date > NOW()
    ) THEN
        -- 如果有活跃订阅，先将其设为过期
        UPDATE "UserSubscription" 
        SET status = 'expired', updated_at = NOW()
        WHERE user_id = user_record.id 
        AND status = 'active' 
        AND "UserSubscription".end_date > NOW();
        
        RAISE NOTICE '已将用户现有活跃订阅设为过期';
    END IF;
    
    -- 5. 创建新的订阅记录
    subscription_id := gen_random_uuid();
    
    INSERT INTO "UserSubscription" (
        id,
        user_id,
        plan_id,
        start_date,
        end_date,
        status,
        auto_renew,
        payment_id,
        created_at,
        updated_at
    ) VALUES (
        subscription_id,
        user_record.id,
        plan_record.id,
        subscription_start_date,
        subscription_end_date,
        'active',
        false, -- 手动设置的订阅默认不自动续费
        'admin_grant_' || EXTRACT(EPOCH FROM NOW())::bigint, -- 生成管理员授权的支付ID
        NOW(),
        NOW()
    );
    
    RAISE NOTICE '创建订阅成功: % (有效期至: %)', subscription_id, subscription_end_date;
    
    -- 6. 创建订阅历史记录
    INSERT INTO "SubscriptionHistory" (
        id,
        user_id,
        plan_id,
        subscription_id,
        action,
        amount,
        points_granted,
        payment_id,
        metadata,
        created_at
    ) VALUES (
        gen_random_uuid(),
        user_record.id,
        plan_record.id,
        subscription_id,
        'subscribe',
        plan_record.price,
        plan_record.points_included,
        'admin_grant_' || EXTRACT(EPOCH FROM NOW())::bigint,
        jsonb_build_object(
            'source', 'admin_script',
            'granted_by', 'database_admin',
            'note', '通过管理员脚本手动授权'
        ),
        NOW()
    );
    
    -- 7. 确保用户积分记录存在
    INSERT INTO "UserPoints" (
        id,
        user_id,
        total_points,
        used_points,
        available_points,
        membership_level,
        last_updated
    ) 
    VALUES (
        gen_random_uuid(),
        user_record.id,
        0,
        0,
        0,
        plan_record.name,
        NOW()
    )
    ON CONFLICT (user_id) DO UPDATE SET
        membership_level = plan_record.name,
        last_updated = NOW();
    
    -- 8. 添加会员套餐包含的积分
    IF plan_record.points_included > 0 THEN
        -- 更新用户积分
        UPDATE "UserPoints" 
        SET 
            total_points = total_points + plan_record.points_included,
            available_points = available_points + plan_record.points_included,
            last_updated = NOW()
        WHERE user_id = user_record.id;
        
        -- 创建积分交易记录
        INSERT INTO "PointsTransaction" (
            id,
            user_id,
            transaction_type,
            amount,
            source,
            source_id,
            description,
            balance_after,
            metadata,
            created_at
        ) VALUES (
            gen_random_uuid(),
            user_record.id,
            'earn',
            plan_record.points_included,
            'subscription',
            subscription_id,
            '购买' || plan_record.name || '会员套餐获得' || plan_record.points_included || '积分',
            (SELECT available_points FROM "UserPoints" WHERE user_id = user_record.id),
            jsonb_build_object(
                'plan_name', plan_record.name,
                'subscription_id', subscription_id,
                'granted_by', 'admin_script'
            ),
            NOW()
        );
        
        RAISE NOTICE '已添加积分: %', plan_record.points_included;
    END IF;
    
    -- 9. 输出结果摘要
    RAISE NOTICE '========================================';
    RAISE NOTICE '会员设置完成！';
    RAISE NOTICE '用户邮箱: %', user_record.email;
    RAISE NOTICE '会员计划: %', plan_record.name;
    RAISE NOTICE '订阅开始: %', subscription_start_date;
    RAISE NOTICE '订阅结束: %', subscription_end_date;
    RAISE NOTICE '包含积分: %', plan_record.points_included;
    RAISE NOTICE '订阅ID: %', subscription_id;
    RAISE NOTICE '========================================';
    
END $$;

-- ==========================================
-- 验证结果
-- ==========================================

-- 查询设置结果 (请修改邮箱地址)
SELECT 
    u.email as "用户邮箱",
    mp.name as "会员计划",
    us.status as "订阅状态",
    us.start_date as "开始时间",
    us.end_date as "结束时间",
    up.available_points as "可用积分",
    up.total_points as "总积分"
FROM "User" u
JOIN "UserSubscription" us ON u.id = us.user_id
JOIN "MembershipPlan" mp ON us.plan_id = mp.id
LEFT JOIN "UserPoints" up ON u.id = up.user_id
WHERE u.email = '<EMAIL>'  -- 🔧 请修改为实际邮箱
AND us.status = 'active'
ORDER BY us.created_at DESC
LIMIT 1;

-- ==========================================
-- 使用示例
-- ==========================================

/*
-- 使用示例：直接修改上面DO块中的变量值

-- 示例1: 设置 Pro 会员
target_email TEXT := '<EMAIL>';
membership_plan TEXT := 'pro';
subscription_days INTEGER := 30;

-- 示例2: 设置 Elite 会员
target_email TEXT := '<EMAIL>';
membership_plan TEXT := 'elite';
subscription_days INTEGER := 30;

-- 示例3: 设置 Ultra 会员（90天）
target_email TEXT := '<EMAIL>';
membership_plan TEXT := 'ultra';
subscription_days INTEGER := 90;
*/ 
import { config } from 'dotenv';
import { defineConfig } from 'drizzle-kit';

config({
  path: '.env.local',
});

export default defineConfig({
  // 数据库连接配置
  dialect: 'postgresql',
  dbCredentials: {
    url: 'postgresql://postgres.cqvzpxjidnwezcldzjpi:<EMAIL>:6543/postgres',
  },

  // Schema 文件路径
  schema: './src/lib/db/schema.ts',

  // 迁移文件输出目录
  out: './src/lib/db/migrations',

  // 开发配置
  verbose: true,
  strict: true,

  // 表前缀（如果需要）
  // tablesFilter: ['pleasurehub_*'],

  // 迁移配置
  migrations: {
    prefix: 'timestamp',
    table: '__drizzle_migrations__',
    schema: 'public',
  },
});

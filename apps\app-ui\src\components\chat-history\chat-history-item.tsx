import { useState, useRef, useEffect } from 'react'
import { Card, CardBody, Button, Avatar } from '@heroui/react'
import { Icon } from '@iconify/react'
import { useTranslation } from 'react-i18next'
import { cn } from '@/lib/utils'

// 聊天记录项接口
export interface ChatHistoryItemProps {
  chat: {
    id: string
    roleId: string
    title: string
    lastMessage: string
    createdAt: string
    updatedAt: string
  }
  formatTime: (dateStr: string) => string
  onSelect: (chatId: string, roleId: string) => void
  onDelete: () => void
  roleCache: Record<string, { name: string; avatar: string }>
}

export function ChatHistoryItem({
  chat,
  formatTime,
  onSelect,
  onDelete,
  roleCache
}: ChatHistoryItemProps) {
  const { t } = useTranslation('chat-history')
  
  // 从缓存中获取角色信息
  const roleInfo = chat.roleId ? roleCache[chat.roleId] : null
  const roleName = roleInfo?.name || t('item.unknown_role')
  const roleAvatar = roleInfo?.avatar || '/images/roles/default.jpg'

  // 使用状态管理滑动
  const [isSlideOpen, setIsSlideOpen] = useState(false)
  // 添加引用来标记整个项目容器
  const itemRef = useRef<HTMLDivElement>(null)

  // 滑动处理
  const toggleSlide = () => {
    setIsSlideOpen(!isSlideOpen)
  }

  // 处理卡片点击
  const handleCardClick = () => {
    console.log('handleCardClick', chat.id, chat.roleId)
    if (isSlideOpen) {
      setIsSlideOpen(false)
    } else {
      onSelect(chat.id, chat.roleId)
    }
  }

  // 处理删除点击
  const handleDeleteClick = () => {
    onDelete()
  }

  // 点击文档其他区域时关闭滑动菜单
  useEffect(() => {
    if (!isSlideOpen) return

    const handleOutsideClick = (e: MouseEvent) => {
      // 检查点击是否发生在当前聊天项内部
      if (itemRef.current && !itemRef.current.contains(e.target as Node)) {
        setIsSlideOpen(false)
      }
    }

    // 使用setTimeout确保事件监听器在当前事件循环结束后才添加
    setTimeout(() => {
      document.addEventListener('click', handleOutsideClick)
    }, 0)

    return () => {
      document.removeEventListener('click', handleOutsideClick)
    }
  }, [isSlideOpen])

  return (
    <div ref={itemRef} className="relative overflow-hidden rounded-lg">
      {/* 滑动容器 */}
      <div
        className={cn(
          'relative w-full transition-transform duration-300 ease-out',
          isSlideOpen ? 'translate-x-[-70px]' : ''
        )}
        onClick={handleCardClick}
      >
        {/* 内容区域 */}
        <Card className="w-full cursor-pointer hover:bg-default-50 transition-colors">
          <CardBody className="p-4">
            <div className="flex items-center gap-4">
              <Avatar
                src={roleAvatar}
                alt={roleName}
                size="md"
                className="flex-shrink-0"
                fallback={<Icon icon="solar:user-bold" className="text-default-500" />}
              />

              <div className="flex-1 overflow-hidden">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className="font-medium text-foreground truncate">{roleName}</h3>
                </div>
                <p className="text-sm text-default-500 truncate">
                  {chat.title || chat.lastMessage || t('item.no_content')}
                </p>
              </div>

              <div className="flex flex-col items-end gap-1">
                <span className="text-xs text-default-400 whitespace-nowrap">
                  {formatTime(chat.updatedAt || chat.createdAt)}
                </span>
                <div onClick={e => e.stopPropagation()}>
                  <Button
                    isIconOnly
                    size="sm"
                    variant="light"
                    className="text-default-400 hover:text-default-600"
                    onPress={toggleSlide}
                  >
                    <Icon icon="solar:menu-dots-bold" width={16} />
                  </Button>
                </div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      {/* 删除按钮 - 绝对定位在右侧，且只在滑动状态显示 */}
      <div
        className={cn(
          'absolute top-0 right-0 h-full flex items-center transition-opacity duration-300',
          isSlideOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        )}
      >
        <Button
          isIconOnly
          color="danger"
          variant="solid"
          className="h-full w-[70px] rounded-none rounded-r-lg"
          onPress={handleDeleteClick}
          isDisabled={!isSlideOpen}
        >
          <Icon icon="solar:trash-bin-minimalistic-bold" width={20} />
        </Button>
      </div>
    </div>
  )
}

// 标签解析器 - 解析 AI 输出的标签格式

import type { ParsedTag, TagType } from '../types/multimodal';

export class TagParser {
  /**
   * 解析文本中的所有标签
   */
  parseAllTags(text: string): ParsedTag[] {
    const tags: ParsedTag[] = [];

    // 解析各种标签
    tags.push(...this.parseSceneTags(text));
    tags.push(...this.parseActionTags(text));
    tags.push(...this.parseDialogueTags(text));
    tags.push(...this.parseImagePromptTags(text));
    tags.push(...this.parseAudioTags(text));

    return tags;
  }

  /**
   * 解析场景标签 <scene:内容>
   */
  parseSceneTags(text: string): ParsedTag[] {
    const regex = /<scene:([^>]+)>/g;
    const matches = Array.from(text.matchAll(regex));

    return matches.map((match) => ({
      type: 'scene' as TagType,
      content: match[1].trim(),
    }));
  }

  /**
   * 解析动作标签 <action:内容>
   */
  parseActionTags(text: string): ParsedTag[] {
    const regex = /<action:([^>]+)>/g;
    const matches = Array.from(text.matchAll(regex));

    return matches.map((match) => ({
      type: 'action' as TagType,
      content: match[1].trim(),
    }));
  }

  /**
   * 解析对话标签 <dialogue>内容</dialogue>
   */
  parseDialogueTags(text: string): ParsedTag[] {
    const regex = /<dialogue>([^<]+)<\/dialogue>/g;
    const matches = Array.from(text.matchAll(regex));

    return matches.map((match) => ({
      type: 'dialogue' as TagType,
      content: match[1].trim(),
    }));
  }

  /**
   * 解析图片提示标签 <imagePrompt>内容</imagePrompt>
   */
  parseImagePromptTags(text: string): ParsedTag[] {
    const regex = /<imagePrompt>([^<]+)<\/imagePrompt>/g;
    const matches = Array.from(text.matchAll(regex));

    return matches.map((match) => ({
      type: 'imagePrompt' as TagType,
      content: match[1].trim(),
    }));
  }

  /**
   * 解析音频标签 <audioTags>标签1,标签2</audioTags>
   */
  parseAudioTags(text: string): ParsedTag[] {
    const regex = /<audioTags>([^<]+)<\/audioTags>/g;
    const matches = Array.from(text.matchAll(regex));

    return matches.map((match) => ({
      type: 'audioTags' as TagType,
      content: match[1].trim(),
    }));
  }

  /**
   * 移除文本中的所有标签，返回纯文本
   */
  removeAllTags(text: string): string {
    return text
      .replace(/<scene:[^>]*>/g, '')
      .replace(/<action:[^>]*>/g, '')
      .replace(/<dialogue>([^<]*)<\/dialogue>/g, '$1')
      .replace(/<imagePrompt>[^<]*<\/imagePrompt>/g, '')
      .replace(/<audioTags>[^<]*<\/audioTags>/g, '')
      .trim();
  }

  /**
   * 验证标签格式是否正确
   */
  validateTagFormat(text: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 检查是否有未闭合的标签
    const openTags = text.match(/<(dialogue|imagePrompt|audioTags)>/g) || [];
    const closeTags = text.match(/<\/(dialogue|imagePrompt|audioTags)>/g) || [];

    if (openTags.length !== closeTags.length) {
      errors.push('存在未闭合的标签');
    }

    // 检查自闭合标签格式
    const sceneMatches = text.match(/<scene:[^>]*>/g) || [];
    const actionMatches = text.match(/<action:[^>]*>/g) || [];

    sceneMatches.forEach((match) => {
      if (!match.includes(':')) {
        errors.push(`场景标签格式错误: ${match}`);
      }
    });

    actionMatches.forEach((match) => {
      if (!match.includes(':')) {
        errors.push(`动作标签格式错误: ${match}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取特定类型的标签内容
   */
  getTagContent(text: string, tagType: TagType): string[] {
    const tags = this.parseAllTags(text);
    return tags.filter((tag) => tag.type === tagType).map((tag) => tag.content);
  }

  /**
   * 检查文本是否包含指定类型的标签
   */
  hasTag(text: string, tagType: TagType): boolean {
    return this.getTagContent(text, tagType).length > 0;
  }
}

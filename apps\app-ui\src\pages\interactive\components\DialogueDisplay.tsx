import React, { useEffect, useState, useRef } from 'react'
import { Card, CardBody, Avatar, Chip } from '@heroui/react'
import { Icon } from '@iconify/react'
import type { Dialogue } from '../types'

interface DialogueDisplayProps {
  dialogues: Dialogue[]
}

/**
 * 对话展示组件
 * 使用气泡样式显示对话内容
 */
export const DialogueDisplay: React.FC<DialogueDisplayProps> = ({ dialogues }) => {
  // 状态用来跟踪对话变化，确保正确触发动画
  const [animateKey, setAnimateKey] = useState<number>(0)
  const lastDialogueRef = useRef<string>('')
  const lastTimeRef = useRef<string>('')

  // 显示最新的对话
  const latestDialogue = dialogues.length > 0 ? dialogues[dialogues.length - 1] : null

  // 当对话变化时，更新动画key
  useEffect(() => {
    if (latestDialogue) {
      // 检查对话内容或时间是否变化
      const dialogueChanged = latestDialogue.dialogue !== lastDialogueRef.current
      const timeChanged = latestDialogue.time !== lastTimeRef.current

      if (dialogueChanged || timeChanged) {
        setAnimateKey(prev => prev + 1)
        lastDialogueRef.current = latestDialogue.dialogue
        lastTimeRef.current = latestDialogue.time
      }
    } else {
      // 当没有对话时，清空上一次对话引用
      if (lastDialogueRef.current || lastTimeRef.current) {
        lastDialogueRef.current = ''
        lastTimeRef.current = ''
        setAnimateKey(prev => prev + 1)
      }
    }
  }, [latestDialogue])

  // 监听对话重置事件
  useEffect(() => {
    const handleDialogueReset = () => {
      // 强制更新动画key，确保在下一个对话出现时有新的动画
      setAnimateKey(Date.now())
      lastDialogueRef.current = ''
      lastTimeRef.current = ''
    }

    document.addEventListener('dialogueReset', handleDialogueReset)
    return () => {
      document.removeEventListener('dialogueReset', handleDialogueReset)
    }
  }, [])

  if (!latestDialogue) {
    return null
  }

  // 根据角色名生成头像颜色
  const getAvatarColor = (role: string) => {
    const colors = ['primary', 'secondary', 'success', 'warning', 'danger']
    const index = role.length % colors.length
    return colors[index] as any
  }

  // 获取角色首字母
  const getRoleInitial = (role: string) => {
    return role.charAt(0).toUpperCase()
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      <Card
        key={`dialogue-${animateKey}-${latestDialogue.time}`}
        className="bg-black/70 backdrop-blur-sm border-gray-800 shadow-lg animate-fade-in"
      >
        <CardBody className="p-4">
          <div className="flex items-start gap-3">
            {/* 角色头像 */}
            <Avatar
              size="sm"
              name={getRoleInitial(latestDialogue.role)}
              color={getAvatarColor(latestDialogue.role)}
              className="flex-shrink-0"
            />

            <div className="flex-1 min-w-0">
              {/* 角色名和时间 */}
              <div className="flex items-center gap-2 mb-2">
                <Chip
                  size="sm"
                  variant="flat"
                  color="primary"
                  startContent={<Icon icon="solar:user-speak-rounded-bold" width={14} />}
                >
                  {latestDialogue.role}
                </Chip>
                <Chip
                  size="sm"
                  variant="flat"
                  color="default"
                  startContent={<Icon icon="solar:clock-circle-linear" width={14} />}
                >
                  {latestDialogue.time}
                </Chip>
              </div>

              {/* 对话内容 */}
              <p className="text-white leading-relaxed text-sm">{latestDialogue.dialogue}</p>
            </div>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}

// 添加自定义动画样式
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.innerHTML = `
    @keyframes fadeIn {
      from { 
        opacity: 0; 
        transform: translateY(10px) scale(0.95); 
      }
      to { 
        opacity: 1; 
        transform: translateY(0) scale(1); 
      }
    }
    .animate-fade-in {
      animation: fadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    }
  `
  document.head.appendChild(style)
}

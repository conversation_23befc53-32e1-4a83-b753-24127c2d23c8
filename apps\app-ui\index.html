<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/logo.svg" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover"
    />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="format-detection" content="telephone=no" />
    <title>AI Assistant</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Geist:wght@300;400;500;600;700&family=Geist+Mono:wght@300;400;500;600&display=swap"
      rel="stylesheet"
    />
    <!-- <script>
      ;(function () {
        var html = document.documentElement
        var meta = document.querySelector('meta[name="theme-color"]')
        if (!meta) {
          meta = document.createElement('meta')
          meta.setAttribute('name', 'theme-color')
          document.head.appendChild(meta)
        }
        function updateThemeColor() {
          var isDark = html.classList.contains('dark')
          meta.setAttribute('content', isDark ? 'hsl(240deg 10% 3.92%)' : 'hsl(0 0% 100%)')
        }
        var observer = new MutationObserver(updateThemeColor)
        observer.observe(html, { attributes: true, attributeFilter: ['class'] })
        updateThemeColor()
      })()
    </script> -->
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>

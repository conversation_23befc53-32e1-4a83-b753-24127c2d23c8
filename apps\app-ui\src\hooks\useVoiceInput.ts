import { useState, useCallback } from 'react'

/**
 * 语音输入状态管理Hook
 * 用于管理语音覆盖层的开关状态
 */
export function useVoiceInput() {
  const [isOpen, setIsOpen] = useState(false)

  const openVoiceInput = useCallback(() => {
    setIsOpen(true)
  }, [])

  const closeVoiceInput = useCallback(() => {
    setIsOpen(false)
  }, [])

  return {
    isVoiceInputOpen: isOpen,
    openVoiceInput,
    closeVoiceInput
  }
}

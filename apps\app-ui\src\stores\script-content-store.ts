import { create } from 'zustand'
import { persist, createJSONStorage } from 'zustand/middleware'
import { scriptPurchaseService } from '@/api/services/script-purchase'
import type { ScriptContentResponse } from '@/api/services/script-purchase'

// 剧本内容数据结构
interface ScriptContentData {
  id: string
  title: string
  content: any
  audioUrl: string | null
  totalDuration: number | null
  stageCount: number | null
  downloadedAt: number // 下载时间戳
}

// Store 状态接口
interface ScriptContentStoreState {
  contents: Record<string, ScriptContentData> // scriptId -> content
  downloadingScripts: Set<string> // 正在下载的剧本ID
}

// Store 方法接口
interface ScriptContentStoreActions {
  // 获取剧本内容（优先从本地获取）
  getScriptContent: (scriptId: string) => Promise<ScriptContentData | null>

  // 下载剧本内容到本地
  downloadScriptContent: (scriptId: string) => Promise<ScriptContentData>

  // 检查剧本是否已下载
  isScriptDownloaded: (scriptId: string) => boolean

  // 清除过期的剧本内容（超过一年）
  clearExpiredContent: () => void

  // 获取下载状态
  getDownloadingStatus: (scriptId: string) => boolean

  // 清除指定剧本内容
  clearScriptContent: (scriptId: string) => void

  // 清除所有内容
  clearAllContent: () => void
}

// 缓存配置
const CACHE_CONFIG = {
  // 内容过期时间（一年）
  maxAge: 365 * 24 * 60 * 60 * 1000,
  // 存储键名
  storageKey: 'script-content-cache',
  // 版本号
  version: 1
}

type ScriptContentStore = ScriptContentStoreState & ScriptContentStoreActions

export const useScriptContentStore = create<ScriptContentStore>()(
  persist(
    (set, get) => ({
      // 初始状态
      contents: {},
      downloadingScripts: new Set(),

      // 获取剧本内容
      getScriptContent: async (scriptId: string) => {
        const { contents } = get()
        const content = contents[scriptId]

        if (content) {
          // 检查是否过期
          const isExpired = Date.now() - content.downloadedAt > CACHE_CONFIG.maxAge
          if (!isExpired) {
            return content
          } else {
            // 内容已过期，清除
            get().clearScriptContent(scriptId)
          }
        }

        return null
      },

      // 下载剧本内容
      downloadScriptContent: async (scriptId: string) => {
        const { downloadingScripts } = get()

        // 防止重复下载
        if (downloadingScripts.has(scriptId)) {
          throw new Error('剧本正在下载中，请稍候')
        }

        // 检查是否已有缓存
        const existingContent = await get().getScriptContent(scriptId)
        if (existingContent) {
          return existingContent
        }

        try {
          // 标记为下载中
          set(state => ({
            downloadingScripts: new Set([...state.downloadingScripts, scriptId])
          }))

          // 从服务器下载
          const response = await scriptPurchaseService.downloadScriptContent(scriptId)

          if (!response.success) {
            throw new Error('下载失败')
          }

          const contentData: ScriptContentData = {
            ...response.data,
            downloadedAt: Date.now()
          }

          // 保存到本地存储
          set(state => ({
            contents: {
              ...state.contents,
              [scriptId]: contentData
            }
          }))

          return contentData
        } catch (error) {
          console.error('下载剧本内容失败:', error)
          throw error
        } finally {
          // 移除下载中标记
          set(state => {
            const newDownloadingScripts = new Set(state.downloadingScripts)
            newDownloadingScripts.delete(scriptId)
            return { downloadingScripts: newDownloadingScripts }
          })
        }
      },

      // 检查是否已下载
      isScriptDownloaded: (scriptId: string) => {
        const { contents } = get()
        const content = contents[scriptId]

        if (!content) return false

        // 检查是否过期
        const isExpired = Date.now() - content.downloadedAt > CACHE_CONFIG.maxAge
        return !isExpired
      },

      // 清除过期内容
      clearExpiredContent: () => {
        const { contents } = get()
        const now = Date.now()
        const validContents: Record<string, ScriptContentData> = {}

        Object.entries(contents).forEach(([scriptId, content]) => {
          const isExpired = now - content.downloadedAt > CACHE_CONFIG.maxAge
          if (!isExpired) {
            validContents[scriptId] = content
          }
        })

        set({ contents: validContents })
      },

      // 获取下载状态
      getDownloadingStatus: (scriptId: string) => {
        const { downloadingScripts } = get()
        return downloadingScripts.has(scriptId)
      },

      // 清除指定剧本内容
      clearScriptContent: (scriptId: string) => {
        set(state => {
          const { [scriptId]: removed, ...restContents } = state.contents
          return { contents: restContents }
        })
      },

      // 清除所有内容
      clearAllContent: () => {
        set({ contents: {}, downloadingScripts: new Set() })
      }
    }),
    {
      name: CACHE_CONFIG.storageKey,
      version: CACHE_CONFIG.version,
      storage: createJSONStorage(() => localStorage),
      partialize: state => ({
        // 只持久化 contents，不持久化 downloadingScripts
        contents: state.contents
      }),
      onRehydrateStorage: () => state => {
        if (state) {
          // 重新初始化 downloadingScripts
          state.downloadingScripts = new Set()
          // 清理过期内容
          state.clearExpiredContent()
        }
      }
    }
  )
)

import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { VoiceModel, VoiceSample } from '../schema';
import type { Env } from '@/types/env';

// ==================== 声音模型相关操作 ====================

/**
 * 获取所有可用的声音模型
 */
export async function getActiveVoiceModels(env: Env): Promise<VoiceModel[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.voiceModel)
      .select('*')
      .eq('is_active', true)
      .order('sort_order')
      .order('name');

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('获取声音模型失败', error);
    throw error;
  }
}

/**
 * 根据性别获取声音模型
 */
export async function getVoiceModelsByGender(
  env: Env,
  gender: 'male' | 'female' | 'neutral'
): Promise<VoiceModel[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.voiceModel)
      .select('*')
      .eq('is_active', true)
      .eq('gender', gender)
      .order('sort_order')
      .order('name');

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('根据性别获取声音模型失败', error);
    throw error;
  }
}

/**
 * 根据ID获取声音模型详情
 */
export async function getVoiceModelById(env: Env, id: string): Promise<VoiceModel | null> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase.from(TABLE_NAMES.voiceModel).select('*').eq('id', id).single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) return null;
    return data;
  } catch (error) {
    console.error('获取声音模型详情失败', error);
    throw error;
  }
}

/**
 * 根据modelId获取声音模型
 */
export async function getVoiceModelByModelId(
  env: Env,
  modelId: string
): Promise<VoiceModel | null> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.voiceModel)
      .select('*')
      .eq('model_id', modelId)
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) return null;
    return data;
  } catch (error) {
    console.error('根据modelId获取声音模型失败', error);
    throw error;
  }
}

/**
 * 创建声音模型
 */
export async function createVoiceModel(
  env: Env,
  data: {
    modelId: string;
    name: string;
    displayName: string;
    description?: string;
    gender: 'male' | 'female' | 'neutral';
    language?: string;
    supportedLanguages?: string[];
    category?: string;
    tags?: string[];
    isPremium?: boolean;
    sortOrder?: number;
  }
): Promise<VoiceModel[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.voiceModel)
      .insert({
        model_id: data.modelId,
        name: data.name,
        display_name: data.displayName,
        description: data.description,
        gender: data.gender,
        language: data.language || 'zh-CN',
        supported_languages: data.supportedLanguages,
        category: data.category,
        tags: data.tags,
        is_premium: data.isPremium || false,
        sort_order: data.sortOrder || 0,
      })
      .select();

    const { data: voiceModels, error } = handleSupabaseResult(result);
    if (error) throw error;
    return voiceModels || [];
  } catch (error) {
    console.error('创建声音模型失败', error);
    throw error;
  }
}

/**
 * 更新声音模型
 */
export async function updateVoiceModel(
  env: Env,
  id: string,
  data: Partial<{
    name: string;
    displayName: string;
    description: string;
    gender: 'male' | 'female' | 'neutral';
    language: string;
    supportedLanguages: string[];
    category: string;
    tags: string[];
    isActive: boolean;
    isPremium: boolean;
    sortOrder: number;
  }>
): Promise<VoiceModel[]> {
  try {
    const supabase = getSupabase(env);

    // 转换字段名为 snake_case
    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.displayName !== undefined) updateData.display_name = data.displayName;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.gender !== undefined) updateData.gender = data.gender;
    if (data.language !== undefined) updateData.language = data.language;
    if (data.supportedLanguages !== undefined)
      updateData.supported_languages = data.supportedLanguages;
    if (data.category !== undefined) updateData.category = data.category;
    if (data.tags !== undefined) updateData.tags = data.tags;
    if (data.isActive !== undefined) updateData.is_active = data.isActive;
    if (data.isPremium !== undefined) updateData.is_premium = data.isPremium;
    if (data.sortOrder !== undefined) updateData.sort_order = data.sortOrder;

    updateData.updated_at = new Date().toISOString();

    const result = await supabase
      .from(TABLE_NAMES.voiceModel)
      .update(updateData)
      .eq('id', id)
      .select();

    const { data: voiceModels, error } = handleSupabaseResult(result);
    if (error) throw error;
    return voiceModels || [];
  } catch (error) {
    console.error('更新声音模型失败', error);
    throw error;
  }
}

// ==================== 声音示例相关操作 ====================

/**
 * 获取声音模型的所有示例
 */
export async function getVoiceSamplesByModelId(
  env: Env,
  voiceModelId: string
): Promise<VoiceSample[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.voiceSample)
      .select('*')
      .eq('voice_model_id', voiceModelId)
      .order('is_default', { ascending: false })
      .order('language');

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('获取声音示例失败', error);
    throw error;
  }
}

/**
 * 获取特定语言的声音示例
 */
export async function getVoiceSampleByLanguage(
  env: Env,
  voiceModelId: string,
  language: string
): Promise<VoiceSample | null> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.voiceSample)
      .select('*')
      .eq('voice_model_id', voiceModelId)
      .eq('language', language)
      .single();

    const { data, error } = handleSupabaseSingleResult(result);
    if (error) return null;
    return data;
  } catch (error) {
    console.error('获取特定语言声音示例失败', error);
    throw error;
  }
}

/**
 * 创建声音示例
 */
export async function createVoiceSample(
  env: Env,
  data: {
    voiceModelId: string;
    language: string;
    sampleText: string;
    audioUrl?: string;
    duration?: number;
    fileSize?: number;
    isDefault?: boolean;
  }
): Promise<VoiceSample[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase
      .from(TABLE_NAMES.voiceSample)
      .insert({
        voice_model_id: data.voiceModelId,
        language: data.language,
        sample_text: data.sampleText,
        audio_url: data.audioUrl,
        duration: data.duration?.toString(),
        file_size: data.fileSize,
        is_default: data.isDefault || false,
      })
      .select();

    const { data: voiceSamples, error } = handleSupabaseResult(result);
    if (error) throw error;
    return voiceSamples || [];
  } catch (error) {
    console.error('创建声音示例失败', error);
    throw error;
  }
}

/**
 * 更新声音示例
 */
export async function updateVoiceSample(
  env: Env,
  id: string,
  data: Partial<{
    sampleText: string;
    audioUrl: string;
    duration: number;
    fileSize: number;
    isDefault: boolean;
  }>
): Promise<VoiceSample[]> {
  try {
    const supabase = getSupabase(env);

    // 转换字段名为 snake_case
    const updateData: any = {};
    if (data.sampleText !== undefined) updateData.sample_text = data.sampleText;
    if (data.audioUrl !== undefined) updateData.audio_url = data.audioUrl;
    if (data.duration !== undefined) updateData.duration = data.duration.toString();
    if (data.fileSize !== undefined) updateData.file_size = data.fileSize;
    if (data.isDefault !== undefined) updateData.is_default = data.isDefault;

    const result = await supabase
      .from(TABLE_NAMES.voiceSample)
      .update(updateData)
      .eq('id', id)
      .select();

    const { data: voiceSamples, error } = handleSupabaseResult(result);
    if (error) throw error;
    return voiceSamples || [];
  } catch (error) {
    console.error('更新声音示例失败', error);
    throw error;
  }
}

/**
 * 删除声音示例
 */
export async function deleteVoiceSample(env: Env, id: string): Promise<VoiceSample[]> {
  try {
    const supabase = getSupabase(env);
    const result = await supabase.from(TABLE_NAMES.voiceSample).delete().eq('id', id).select();

    const { data, error } = handleSupabaseResult(result);
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('删除声音示例失败', error);
    throw error;
  }
}

/**
 * 获取声音模型及其示例（联合查询）
 */
export async function getVoiceModelWithSamples(
  env: Env,
  id: string
): Promise<(VoiceModel & { samples: VoiceSample[] }) | null> {
  try {
    // 获取声音模型
    const model = await getVoiceModelById(env, id);
    if (!model) return null;

    // 获取示例
    const samples = await getVoiceSamplesByModelId(env, id);

    return {
      ...model,
      samples,
    };
  } catch (error) {
    console.error('获取声音模型及示例失败', error);
    throw error;
  }
}

/**
 * 获取所有声音模型及其默认示例
 */
export async function getAllVoiceModelsWithDefaultSamples(
  env: Env
): Promise<(VoiceModel & { defaultSample: VoiceSample | null })[]> {
  try {
    const supabase = getSupabase(env);

    // 获取所有活跃的声音模型
    const models = await getActiveVoiceModels(env);

    // 为每个模型获取默认示例
    const modelsWithSamples = await Promise.all(
      models.map(async (model) => {
        const result = await supabase
          .from(TABLE_NAMES.voiceSample)
          .select('*')
          .eq('voice_model_id', model.id)
          .eq('is_default', true)
          .limit(1);

        const { data: samples, error } = handleSupabaseResult(result);
        if (error) console.warn('获取默认示例失败', error);

        return {
          ...model,
          defaultSample: samples && samples.length > 0 ? samples[0] : null,
        };
      })
    );

    return modelsWithSamples;
  } catch (error) {
    console.error('获取所有声音模型及默认示例失败', error);
    throw error;
  }
}

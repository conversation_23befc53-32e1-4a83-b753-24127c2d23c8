import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';

interface ExitConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  onCancel: () => void;
}

/**
 * 退出确认弹窗组件
 * 当用户试图在没有上一页面的情况下左滑退出时显示
 */
export function ExitConfirmationDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
}: ExitConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[300px]">
        <DialogHeader>
          <DialogTitle className="text-center">确认退出应用？</DialogTitle>
          <DialogDescription className="text-center">
            您确定要退出当前应用吗？
          </DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex justify-between sm:justify-between">
          <button
            type="button"
            className="px-5 py-2 rounded-md border border-gray-200 bg-gray-100 text-sm font-medium text-gray-700 hover:bg-gray-200 focus:outline-none"
            onClick={onCancel}
          >
            取消
          </button>
          <button
            type="button"
            className="px-5 py-2 rounded-md bg-red-500 text-sm font-medium text-white hover:bg-red-600 focus:outline-none"
            onClick={onConfirm}
          >
            退出
          </button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}

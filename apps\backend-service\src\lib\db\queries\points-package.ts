/**
 * 积分包数据库查询函数
 */

import { getSupabase } from './base';
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types';
import type { PointsPackage } from '../schema';
import type { Env } from '@/types/env';

export const pointsPackageQueries = {
  /**
   * 获取所有活跃的积分包
   */
  async getActivePackages(env: Env): Promise<PointsPackage[]> {
    console.log('🔍 [POINTS-QUERY] 查询活跃积分包');

    try {
      const supabase = getSupabase(env);
      const result = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .select('*')
        .eq('is_active', true)
        .order('sort_order')
        .order('price');

      const { data: packages, error } = handleSupabaseResult(result);
      if (error) throw error;

      console.log('✅ [POINTS-QUERY] 查询到积分包数量:', (packages || []).length);
      return packages || [];
    } catch (error) {
      console.error('❌ [POINTS-QUERY] 查询积分包失败:', error);
      throw error;
    }
  },

  /**
   * 根据ID获取积分包
   */
  async getPackageById(env: Env, packageId: string): Promise<PointsPackage | null> {
    console.log('🔍 [POINTS-QUERY] 查询积分包:', packageId);

    try {
      const supabase = getSupabase(env);
      const result = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .select('*')
        .eq('id', packageId)
        .single();

      const { data: pkg, error } = handleSupabaseSingleResult(result);
      if (error) {
        console.log('⚠️ [POINTS-QUERY] 积分包不存在:', packageId);
        return null;
      }

      if (pkg) {
        console.log('✅ [POINTS-QUERY] 积分包查询成功:', pkg.name);
      }

      return pkg;
    } catch (error) {
      console.error('❌ [POINTS-QUERY] 查询积分包失败:', error);
      throw error;
    }
  },

  /**
   * 获取所有积分包（包括非活跃的）
   */
  async getAllPackages(env: Env): Promise<PointsPackage[]> {
    console.log('🔍 [POINTS-QUERY] 查询所有积分包');

    try {
      const supabase = getSupabase(env);
      const result = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .select('*')
        .order('sort_order')
        .order('price');

      const { data: packages, error } = handleSupabaseResult(result);
      if (error) throw error;

      console.log('✅ [POINTS-QUERY] 查询到所有积分包数量:', (packages || []).length);
      return packages || [];
    } catch (error) {
      console.error('❌ [POINTS-QUERY] 查询所有积分包失败:', error);
      throw error;
    }
  },

  /**
   * 创建积分包
   */
  async createPackage(
    env: Env,
    data: {
      name: string;
      description?: string;
      points: number;
      price: string;
      bonusPoints?: number;
      sortOrder?: number;
    }
  ): Promise<PointsPackage | null> {
    console.log('➕ [POINTS-QUERY] 创建积分包:', data.name);

    try {
      const supabase = getSupabase(env);
      const result = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .insert({
          name: data.name,
          description: data.description,
          points: data.points,
          price: data.price,
          bonus_points: data.bonusPoints || 0,
          sort_order: data.sortOrder || 0,
          is_active: true,
        })
        .select()
        .single();

      const { data: createdPackage, error } = handleSupabaseSingleResult(result);
      if (error) throw error;

      console.log('✅ [POINTS-QUERY] 积分包创建成功:', createdPackage?.id);
      return createdPackage;
    } catch (error) {
      console.error('❌ [POINTS-QUERY] 创建积分包失败:', error);
      throw error;
    }
  },

  /**
   * 更新积分包
   */
  async updatePackage(
    env: Env,
    packageId: string,
    data: Partial<{
      name: string;
      description: string;
      points: number;
      price: string;
      bonusPoints: number;
      isActive: boolean;
      sortOrder: number;
    }>
  ): Promise<PointsPackage | null> {
    console.log('📝 [POINTS-QUERY] 更新积分包:', packageId);

    try {
      const supabase = getSupabase(env);

      // 转换字段名为 snake_case
      const updateData: any = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.points !== undefined) updateData.points = data.points;
      if (data.price !== undefined) updateData.price = data.price;
      if (data.bonusPoints !== undefined) updateData.bonus_points = data.bonusPoints;
      if (data.isActive !== undefined) updateData.is_active = data.isActive;
      if (data.sortOrder !== undefined) updateData.sort_order = data.sortOrder;

      updateData.updated_at = new Date().toISOString();

      const result = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .update(updateData)
        .eq('id', packageId)
        .select()
        .single();

      const { data: updatedPackage, error } = handleSupabaseSingleResult(result);
      if (error) throw error;

      console.log('✅ [POINTS-QUERY] 积分包更新成功:', updatedPackage?.id);
      return updatedPackage;
    } catch (error) {
      console.error('❌ [POINTS-QUERY] 更新积分包失败:', error);
      throw error;
    }
  },

  /**
   * 删除积分包（软删除，设置为非活跃）
   */
  async deactivatePackage(env: Env, packageId: string): Promise<PointsPackage | null> {
    console.log('🗑️ [POINTS-QUERY] 停用积分包:', packageId);

    try {
      const supabase = getSupabase(env);
      const result = await supabase
        .from(TABLE_NAMES.pointsPackage)
        .update({
          is_active: false,
          updated_at: new Date().toISOString(),
        })
        .eq('id', packageId)
        .select()
        .single();

      const { data: deactivatedPackage, error } = handleSupabaseSingleResult(result);
      if (error) throw error;

      console.log('✅ [POINTS-QUERY] 积分包停用成功:', deactivatedPackage?.id);
      return deactivatedPackage;
    } catch (error) {
      console.error('❌ [POINTS-QUERY] 停用积分包失败:', error);
      throw error;
    }
  },
};

// 为了兼容性，导出个别函数
export const {
  getActivePackages,
  getPackageById,
  getAllPackages,
  createPackage,
  updatePackage,
  deactivatePackage,
} = pointsPackageQueries;

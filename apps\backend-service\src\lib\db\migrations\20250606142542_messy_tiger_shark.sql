CREATE TABLE IF NOT EXISTS "Device" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"device_code" varchar(20) NOT NULL,
	"name" varchar(100) NOT NULL,
	"pic" text,
	"brand" varchar(50),
	"model" varchar(50),
	"category" varchar(30),
	"description" text,
	"is_active" boolean DEFAULT true NOT NULL,
	"last_connected_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "Device_device_code_unique" UNIQUE("device_code")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DeviceCommand" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"function_id" uuid NOT NULL,
	"intensity" integer NOT NULL,
	"command" varchar(100) NOT NULL,
	"description" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DeviceConnection" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"device_id" uuid NOT NULL,
	"session_id" varchar(100),
	"status" varchar DEFAULT 'connected' NOT NULL,
	"connected_at" timestamp DEFAULT now() NOT NULL,
	"disconnected_at" timestamp,
	"error_message" text,
	"metadata" json DEFAULT '{}'
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DeviceFunction" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"device_id" uuid NOT NULL,
	"name" varchar(50) NOT NULL,
	"key" varchar(30) NOT NULL,
	"description" text,
	"max_intensity" integer DEFAULT 3 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "DeviceUsage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"device_id" uuid NOT NULL,
	"script_id" uuid,
	"session_id" varchar(100),
	"function_key" varchar(30) NOT NULL,
	"intensity" integer NOT NULL,
	"duration" integer,
	"started_at" timestamp DEFAULT now() NOT NULL,
	"ended_at" timestamp,
	"metadata" json DEFAULT '{}'
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "Device" ADD CONSTRAINT "Device_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceCommand" ADD CONSTRAINT "DeviceCommand_function_id_DeviceFunction_id_fk" FOREIGN KEY ("function_id") REFERENCES "public"."DeviceFunction"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceConnection" ADD CONSTRAINT "DeviceConnection_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceConnection" ADD CONSTRAINT "DeviceConnection_device_id_Device_id_fk" FOREIGN KEY ("device_id") REFERENCES "public"."Device"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceFunction" ADD CONSTRAINT "DeviceFunction_device_id_Device_id_fk" FOREIGN KEY ("device_id") REFERENCES "public"."Device"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceUsage" ADD CONSTRAINT "DeviceUsage_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceUsage" ADD CONSTRAINT "DeviceUsage_device_id_Device_id_fk" FOREIGN KEY ("device_id") REFERENCES "public"."Device"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "DeviceUsage" ADD CONSTRAINT "DeviceUsage_script_id_Script_id_fk" FOREIGN KEY ("script_id") REFERENCES "public"."Script"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_user_id" ON "Device" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_code" ON "Device" USING btree ("device_code");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_category" ON "Device" USING btree ("category");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_active" ON "Device" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_user_active" ON "Device" USING btree ("user_id","is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_command_function_id" ON "DeviceCommand" USING btree ("function_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_command_intensity" ON "DeviceCommand" USING btree ("intensity");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_command_function_intensity" ON "DeviceCommand" USING btree ("function_id","intensity");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_connection_user_id" ON "DeviceConnection" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_connection_device_id" ON "DeviceConnection" USING btree ("device_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_connection_status" ON "DeviceConnection" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_connection_connected_at" ON "DeviceConnection" USING btree ("connected_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_connection_user_device" ON "DeviceConnection" USING btree ("user_id","device_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_function_device_id" ON "DeviceFunction" USING btree ("device_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_function_key" ON "DeviceFunction" USING btree ("key");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_function_active" ON "DeviceFunction" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_function_device_key" ON "DeviceFunction" USING btree ("device_id","key");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_usage_user_id" ON "DeviceUsage" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_usage_device_id" ON "DeviceUsage" USING btree ("device_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_usage_script_id" ON "DeviceUsage" USING btree ("script_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_usage_started_at" ON "DeviceUsage" USING btree ("started_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_device_usage_user_device" ON "DeviceUsage" USING btree ("user_id","device_id");
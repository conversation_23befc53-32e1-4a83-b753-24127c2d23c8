import { imageService } from './image'
import { supabase } from '@/lib/supabase'

/**
 * 图片生成服务 - 带降级机制
 * 优先使用新的 API v2，失败时降级到 Edge Function
 */
export const imageGenerationService = {
  /**
   * 生成图片（带降级机制）
   */
  async generateImage(params: {
    messageId: string
    chatId: string
    prompt: string
    characterAvatar?: string
    characterId?: string // 添加 characterId 参数
    metadata?: {
      width?: number
      height?: number
      steps?: number
      cfg?: number
      negativePrompt?: string
    }
  }) {
    const { messageId, chatId, prompt, characterAvatar, characterId, metadata } = params

    console.log('🎨 [START] 开始图片生成:', {
      messageId,
      chatId,
      promptLength: prompt.length,
      hasCharacterAvatar: !!characterAvatar
    })

    // 优先使用新的 API v2
    try {
      console.log('🚀 [API_V2] 尝试调用 API v2...')

      const apiV2Response = await imageService.generateImageV2Queue({
        messageId,
        chatId,
        prompt,
        characterAvatar,
        characterId, // 传递 characterId
        metadata: metadata || {
          width: 720,
          height: 1280,
          steps: 30,
          cfg: 7
        }
      })

      if (apiV2Response.success) {
        console.log('✅ [API_V2] API v2 调用成功:', {
          taskId: apiV2Response.taskId,
          pointsConsumed: apiV2Response.data?.pointsConsumed,
          remainingPoints: apiV2Response.data?.remainingPoints
        })
        return {
          success: true,
          source: 'api_v2' as const,
          data: apiV2Response
        }
      } else {
        throw new Error(apiV2Response.message || '图片生成失败')
      }
    } catch (apiV2Error) {
      console.warn('⚠️ [FALLBACK] API v2 失败，降级到 Edge Function:', apiV2Error)

      // 降级到原来的 Edge Function
      try {
        console.log('🔄 [EDGE_FUNCTION] 调用 Edge Function...')

        const { data, error } = await supabase.functions.invoke('generate-image', {
          body: {
            messageId,
            chatId,
            prompt,
            characterAvatar
          }
        })

        if (error) {
          console.error('❌ [EDGE_FUNCTION] Edge Function 调用失败:', error)
          throw new Error('图片生成失败，请重试')
        } else {
          console.log('✅ [EDGE_FUNCTION] Edge Function 调用成功:', data)
          return {
            success: true,
            source: 'edge_function' as const,
            data
          }
        }
      } catch (edgeFunctionError) {
        console.error('❌ [FALLBACK] 所有图片生成方法都失败了:', edgeFunctionError)
        throw new Error('图片生成服务暂时不可用，请稍后重试')
      }
    }
  },

  /**
   * 检查 API v2 服务健康状态
   */
  async checkApiV2Health() {
    try {
      const healthResponse = await imageService.checkImageGenerationV2Health()
      return {
        available: healthResponse.success,
        status: healthResponse.data?.status || 'unknown',
        details: healthResponse.data
      }
    } catch (error) {
      console.warn('⚠️ [HEALTH_CHECK] API v2 健康检查失败:', error)
      return {
        available: false,
        status: 'error',
        error: error instanceof Error ? error.message : String(error)
      }
    }
  },

  /**
   * 查询任务状态（API v2）
   */
  async getTaskStatus(taskId: string) {
    try {
      return await imageService.getImageGenerationV2Status(taskId)
    } catch (error) {
      console.error('❌ [STATUS] 查询任务状态失败:', error)
      throw error
    }
  },

  /**
   * 查询图片生成状态（通过 messageId）
   */
  async getGenerationStatus(messageId: string): Promise<{
    success: boolean
    data?: {
      status: 'idle' | 'processing' | 'completed' | 'failed'
      progress: number
      stage: 'image_generation'
      message: string
      finalImageUrl?: string
      error?: string
    }
    error?: string
  }> {
    try {
      console.log('🔍 [API] 查询图片生成状态:', messageId)

      const { apiClient } = await import('../client')
      const response = await apiClient.get<{
        success: boolean
        data?: {
          status: 'idle' | 'processing' | 'completed' | 'failed'
          progress: number
          stage: 'image_generation'
          message: string
          finalImageUrl?: string
          error?: string
        }
        error?: string
      }>(`/api/image-generation/status/${messageId}`)

      console.log('📊 [API] 状态查询结果:', response)
      return response
    } catch (error) {
      console.error('❌ [API] 查询状态失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '查询状态失败'
      }
    }
  }
}

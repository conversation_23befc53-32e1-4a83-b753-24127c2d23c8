import type { Env } from '@/types/env'
import {
  CachePrefix,
  CacheTTL,
  type CacheResult,
  type CacheStats,
  type CachedUserMapping,
  type CachedVoiceModel,
  type CachedUserPoints,
  type CachedMembership,
  type CachedVoiceModelsList,
  type CachedTemplate
} from './types'

/**
 * 通用缓存管理器
 * 基于 Cloudflare KV 存储实现
 */
export class CacheManager {
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    errors: 0,
    hitRate: 0
  }

  constructor(private kv: KVNamespace) {}

  /**
   * 生成缓存键
   */
  private generateCacheKey(prefix: CachePrefix, identifier: string): string {
    return `${prefix}:${identifier}`
  }

  /**
   * 通用的缓存获取方法
   */
  private async getFromCache<T>(key: string, ttl: number): Promise<CacheResult<T>> {
    try {
      const cached = await this.kv.get(key, 'json')

      if (!cached) {
        this.stats.misses++
        this.updateHitRate()
        return {
          success: true,
          cached: false
        }
      }

      const data = cached as T & { timestamp: number }

      // 检查是否过期（双重保险，KV 的 TTL 是主要的过期机制）
      if (data.timestamp && Date.now() - data.timestamp > ttl * 1000) {
        console.log(`缓存已过期，删除: ${key}`)
        await this.kv.delete(key)
        this.stats.misses++
        this.updateHitRate()
        return {
          success: true,
          cached: false
        }
      }

      this.stats.hits++
      this.updateHitRate()

      return {
        success: true,
        data,
        cached: true
      }
    } catch (error) {
      this.stats.errors++
      console.error(`缓存获取失败 [${key}]:`, error)
      return {
        success: false,
        cached: false,
        error: error instanceof Error ? error.message : String(error)
      }
    }
  }

  /**
   * 通用的缓存设置方法
   */
  private async setToCache<T>(key: string, data: T, ttl: number): Promise<boolean> {
    try {
      const cacheData = {
        ...data,
        timestamp: Date.now()
      }

      await this.kv.put(key, JSON.stringify(cacheData), {
        expirationTtl: ttl
      })

      console.log(`缓存设置成功 [${key}], TTL: ${ttl}s`)
      return true
    } catch (error) {
      this.stats.errors++
      console.error(`缓存设置失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 删除缓存
   */
  private async deleteFromCache(key: string): Promise<boolean> {
    try {
      await this.kv.delete(key)
      console.log(`缓存删除成功: ${key}`)
      return true
    } catch (error) {
      console.error(`缓存删除失败 [${key}]:`, error)
      return false
    }
  }

  /**
   * 更新命中率统计
   */
  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses
    this.stats.hitRate = total > 0 ? this.stats.hits / total : 0
  }

  /**
   * 批量删除缓存（通过前缀）
   */
  async clearCacheByPrefix(prefix: CachePrefix): Promise<number> {
    try {
      // 注意：KV 不支持按前缀批量删除，这里只是提供接口
      // 实际项目中如果需要可以维护一个键列表
      console.log(`请求清除缓存前缀: ${prefix}`)
      return 0
    } catch (error) {
      console.error(`清除缓存失败 [${prefix}]:`, error)
      return 0
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats(): CacheStats {
    return { ...this.stats }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      hits: 0,
      misses: 0,
      errors: 0,
      hitRate: 0
    }
  }

  // ==================== 具体缓存方法 ====================

  /**
   * 获取缓存的用户ID映射
   */
  async getCachedDbUserId(supabaseUserId: string): Promise<CacheResult<string>> {
    const key = this.generateCacheKey(CachePrefix.USER_MAPPING, supabaseUserId)
    const result = await this.getFromCache<CachedUserMapping>(key, CacheTTL.USER_MAPPING)

    if (result.success && result.data) {
      return {
        success: true,
        data: result.data.dbUserId,
        cached: true
      }
    }

    return {
      success: result.success,
      cached: false,
      error: result.error
    }
  }

  /**
   * 设置用户ID映射缓存
   */
  async setCachedDbUserId(supabaseUserId: string, dbUserId: string): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.USER_MAPPING, supabaseUserId)
    const data: Omit<CachedUserMapping, 'timestamp'> = { dbUserId }
    return await this.setToCache(key, data, CacheTTL.USER_MAPPING)
  }

  /**
   * 获取缓存的声音模型ID
   */
  async getCachedRealVoiceId(voiceModelId: string): Promise<CacheResult<string>> {
    const key = this.generateCacheKey(CachePrefix.VOICE_MODEL, voiceModelId)
    const result = await this.getFromCache<CachedVoiceModel>(key, CacheTTL.VOICE_MODEL)

    if (result.success && result.data) {
      return {
        success: true,
        data: result.data.realVoiceId,
        cached: true
      }
    }

    return {
      success: result.success,
      cached: false,
      error: result.error
    }
  }

  /**
   * 设置声音模型ID缓存
   */
  async setCachedRealVoiceId(
    voiceModelId: string,
    realVoiceId: string,
    displayName?: string
  ): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.VOICE_MODEL, voiceModelId)
    const data: Omit<CachedVoiceModel, 'timestamp'> = {
      realVoiceId,
      displayName
    }
    return await this.setToCache(key, data, CacheTTL.VOICE_MODEL)
  }

  /**
   * 获取缓存的用户积分
   */
  async getCachedUserPoints(dbUserId: string): Promise<CacheResult<CachedUserPoints>> {
    const key = this.generateCacheKey(CachePrefix.USER_POINTS, dbUserId)
    return await this.getFromCache<CachedUserPoints>(key, CacheTTL.USER_POINTS)
  }

  /**
   * 设置用户积分缓存
   */
  async setCachedUserPoints(
    dbUserId: string,
    pointsData: Omit<CachedUserPoints, 'timestamp'>
  ): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.USER_POINTS, dbUserId)
    return await this.setToCache(key, pointsData, CacheTTL.USER_POINTS)
  }

  /**
   * 清除用户积分缓存（积分变化时调用）
   */
  async clearUserPointsCache(dbUserId: string): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.USER_POINTS, dbUserId)
    return await this.deleteFromCache(key)
  }

  /**
   * 清除用户映射缓存（用户信息变化时调用）
   */
  async clearUserMappingCache(supabaseUserId: string): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.USER_MAPPING, supabaseUserId)
    return await this.deleteFromCache(key)
  }

  /**
   * 清除声音模型缓存（模型更新时调用）
   */
  async clearVoiceModelCache(voiceModelId: string): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.VOICE_MODEL, voiceModelId)
    return await this.deleteFromCache(key)
  }

  /**
   * 获取缓存的会员状态
   */
  async getCachedMembership(dbUserId: string): Promise<CacheResult<CachedMembership>> {
    const key = this.generateCacheKey(CachePrefix.MEMBERSHIP, dbUserId)
    return await this.getFromCache<CachedMembership>(key, CacheTTL.MEMBERSHIP)
  }

  /**
   * 设置会员状态缓存
   */
  async setCachedMembership(
    dbUserId: string,
    membershipData: Omit<CachedMembership, 'timestamp'>
  ): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.MEMBERSHIP, dbUserId)
    return await this.setToCache(key, membershipData, CacheTTL.MEMBERSHIP)
  }

  /**
   * 清除会员状态缓存（订阅状态变化时调用）
   */
  async clearMembershipCache(dbUserId: string): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.MEMBERSHIP, dbUserId)
    return await this.deleteFromCache(key)
  }

  /**
   * 获取缓存的声音模型列表
   */
  async getCachedVoiceModelsList(cacheKey: string): Promise<CacheResult<CachedVoiceModelsList>> {
    return await this.getFromCache<CachedVoiceModelsList>(cacheKey, CacheTTL.VOICE_MODELS_LIST)
  }

  /**
   * 设置声音模型列表缓存
   */
  async setCachedVoiceModelsList(
    cacheKey: string,
    modelsData: Omit<CachedVoiceModelsList, 'timestamp'>
  ): Promise<boolean> {
    return await this.setToCache(cacheKey, modelsData, CacheTTL.VOICE_MODELS_LIST)
  }

  /**
   * 清除所有声音模型列表缓存
   * 当声音模型发生变化时调用
   */
  async clearAllVoiceModelsListCache(): Promise<void> {
    try {
      // 注意：这里只是记录日志，实际清除需要维护缓存键列表
      // 或者等待TTL自然过期
      console.log('🗑️ 请求清除所有声音模型列表缓存')
    } catch (error) {
      console.error('清除声音模型列表缓存失败:', error)
    }
  }

  /**
   * 生成声音模型列表的缓存键
   */
  static generateVoiceModelsListCacheKey(type: string, filter?: string): string {
    const baseKey = CachePrefix.VOICE_MODELS_LIST
    if (filter) {
      return `${baseKey}:${type}:${filter}`
    }
    return `${baseKey}:${type}`
  }

  /**
   * 获取缓存的模板数据
   */
  async getCachedTemplate(templateId: string): Promise<CacheResult<CachedTemplate>> {
    const key = this.generateCacheKey(CachePrefix.TEMPLATE, templateId)
    return await this.getFromCache<CachedTemplate>(key, CacheTTL.TEMPLATE)
  }

  /**
   * 设置模板数据缓存
   */
  async setCachedTemplate(
    templateId: string,
    templateData: Omit<CachedTemplate, 'timestamp'>
  ): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.TEMPLATE, templateId)
    return await this.setToCache(key, templateData, CacheTTL.TEMPLATE)
  }

  /**
   * 清除模板缓存（模板更新时调用）
   */
  async clearTemplateCache(templateId: string): Promise<boolean> {
    const key = this.generateCacheKey(CachePrefix.TEMPLATE, templateId)
    return await this.deleteFromCache(key)
  }
}

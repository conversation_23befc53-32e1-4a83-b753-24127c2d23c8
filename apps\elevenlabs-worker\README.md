# ElevenLabs V3 逆向 API 独立 Worker

## 概述

这是一个基于逆向工程的 ElevenLabs V3 TTS API 服务，专为 Cloudflare Workers 环境优化。通过模拟用户登录流程，获取访问令牌，然后调用 V3 模型进行文本转语音。

## 架构设计

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   用户请求       │ -> │  ElevenLabsV3    │ -> │   账号管理器     │
│   (TTS 生成)    │    │  Service         │    │ AccountManager  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                       │
                                v                       │
                       ┌──────────────────┐             │
                       │   会话管理器      │ <-----------┘
                       │ SessionManager   │
                       └──────────────────┘
                                │
                                v
                       ┌──────────────────┐
                       │   API 客户端     │
                       │   V3ApiClient    │
                       └──────────────────┘
                                │
                                v
                       ┌──────────────────┐
                       │  Firebase Auth   │
                       │ + ElevenLabs V3  │
                       └──────────────────┘
```

ElevenLabs V3 逆向 API 的独立 Cloudflare Worker 服务，提供完善的账号轮询和 token 管理机制，**支持真正的流式音频传输**。

## 🚀 功能特性

- **硬编码账号配置**: 在代码中直接配置账号，部署即用
- **完善的账号轮询机制**: 自动轮询多个 ElevenLabs 账号，确保服务可用性
- **智能 Token 管理**: 自动刷新和管理会话 token，处理过期和失效情况
- **真正的流式传输**: 直接转发 ElevenLabs API 的音频流，无中间缓存，超低延迟 ⭐
- **双模式支持**: 支持传统 Base64 返回和流式返回两种模式
- **健康检查系统**: 实时监控账号状态和服务健康状况
- **重试机制**: 内置重试逻辑，提高 API 调用成功率
- **RESTful API**: 简洁明了的 HTTP API 接口
- **高性能**: 基于 Cloudflare Worker，全球部署，低延迟

## 📋 API 接口

### 根路径

```http
GET /
```

返回 API 信息和可用端点列表。

### 生成语音（Base64 返回）

```http
POST /tts
Content-Type: application/json

{
  "text": "要转换的文本",
  "voice_id": "声音ID（可选）",
  "model_id": "模型ID（可选，默认 eleven_v3）",
  "stability": 0.5,
  "use_speaker_boost": true
}
```

**响应格式**：

```json
{
  "success": true,
  "data": {
    "audioBase64": "base64编码的音频数据"
  },
  "message": "音频生成成功"
}
```

### 生成语音（流式返回）⭐

```http
POST /tts/stream
Content-Type: application/json

{
  "text": "要转换的文本",
  "voice_id": "声音ID（可选）",
  "model_id": "模型ID（可选，默认 eleven_v3）",
  "stability": 0.5,
  "use_speaker_boost": true
}
```

**响应格式**：直接返回音频流

- **Content-Type**: `audio/mpeg`
- **Transfer-Encoding**: `chunked`
- **X-Source**: `elevenlabs-v3-stream`

### 健康检查

```http
GET /health
```

返回服务健康状态和账号统计信息。

### 获取声音列表

```http
GET /voices
```

获取可用的声音列表。

## 🎯 流式传输优势

### **传统 Base64 模式**

```mermaid
graph TD
    A[ElevenLabs API] --> B[Worker 完整接收]
    B --> C[转换为 Base64]
    C --> D[客户端一次性接收]

    style B fill:#ffcdd2
    style C fill:#ffcdd2
```

### **流式传输模式** ⭐

```mermaid
graph TD
    A[ElevenLabs API] --> B[Worker 实时转发]
    B --> C[客户端实时接收]

    style A fill:#c8e6c9
    style B fill:#c8e6c9
    style C fill:#c8e6c9
```

| 对比项         | Base64 模式            | 流式模式         |
| -------------- | ---------------------- | ---------------- |
| **首字节延迟** | 需等待完整生成         | ~100ms           |
| **内存使用**   | 完整音频 + Base64 编码 | 仅少量缓冲       |
| **用户体验**   | 等待后播放             | 边生成边播放     |
| **适用场景**   | 离线处理、小文本       | 实时应用、长文本 |

## 🛠️ 部署指南

### 1. 配置账号

在 `src/account-manager.ts` 文件中配置你的 ElevenLabs 账号：

```typescript
const HARDCODED_ACCOUNTS = [
  {
    email: '<EMAIL>',
    password: 'your-password1'
  },
  {
    email: '<EMAIL>',
    password: 'your-password2'
  }
  // 添加更多账号...
]
```

### 2. 安装依赖

```bash
npm install
```

### 3. 配置环境

编辑 `wrangler.toml` 文件，配置你的 Cloudflare 账号信息：

```toml
name = "elevenlabs-worker"
main = "src/index.ts"
compatibility_date = "2024-12-20"

# KV 存储配置
[[kv_namespaces]]
binding = "ELEVENLABS_CACHE"
preview_id = "your_preview_kv_id"
id = "your_production_kv_id"
```

### 4. 创建 KV 命名空间

```bash
# 创建生产环境 KV
wrangler kv:namespace create "ELEVENLABS_CACHE"

# 创建预览环境 KV
wrangler kv:namespace create "ELEVENLABS_CACHE" --preview
```

### 5. 本地开发

```bash
npm run dev
```

### 6. 部署到生产环境

```bash
npm run deploy
```

## 🔧 使用示例

### 1. 生成语音

```bash
curl -X POST "https://your-worker.workers.dev/tts" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, this is a test message",
    "voice_id": "JBFqnCBsd6RMkjVDRZzb"
  }'
```

### 2. 检查服务状态

```bash
curl "https://your-worker.workers.dev/health"
```

### 3. 获取可用声音

```bash
curl "https://your-worker.workers.dev/voices"
```

## 📊 响应格式

所有 API 端点都返回标准的 JSON 格式：

```json
{
  "success": true,
  "data": "响应数据",
  "message": "操作描述"
}
```

错误响应：

```json
{
  "success": false,
  "error": "错误信息"
}
```

## 🔒 安全考虑

- **账号安全**: 账号信息硬编码在代码中，部署前请确保代码安全
- **访问控制**: 建议在生产环境中添加身份验证机制
- **频率限制**: 根据需要实施请求频率限制

## 🚨 监控和告警

### 健康检查指标

- `total_accounts`: 总账号数
- `active_accounts`: 活跃账号数
- `failed_accounts`: 失败账号数
- `healthy`: 服务整体健康状态

### 日志监控

- 查看 Cloudflare Workers 日志
- 监控账号失效情况
- 跟踪 API 调用成功率

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙋‍♂️ 支持

如有问题，请创建 Issue 或联系维护者。

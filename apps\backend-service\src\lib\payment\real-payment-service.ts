/**
 * 真实支付服务实现
 * 整合支付宝和微信支付服务
 */

import { AlipayService } from './alipay-service';
import { PaymentMethod, OrderStatus } from './payment-config';
import { createPaymentOrder } from '../db/queries/payment';
import { ensureUserExists } from '../db/queries/membership';
import type { PaymentResult, PaymentVerification } from './mock-payment';

export interface CreateOrderRequest {
  amount: number;
  currency?: string;
  description: string;
  userId: string;
  planId?: string;
  pointsPackageId?: string;
  paymentMethod: PaymentMethod;
  metadata?: Record<string, any>;
}

export interface CallbackResult {
  success: boolean;
  message: string;
  orderData?: any;
}

/**
 * 真实支付服务类
 */
export class RealPaymentService {
  private alipayService: AlipayService;
  private env: any;

  constructor(env: any) {
    this.env = env;
    this.alipayService = new AlipayService(env);
  }

  /**
   * 创建支付订单
   */
  async createOrder(orderInfo: CreateOrderRequest): Promise<PaymentResult> {
    try {
      console.log('💰 [REAL-PAYMENT] 创建真实支付订单:', {
        amount: orderInfo.amount,
        paymentMethod: orderInfo.paymentMethod,
        description: orderInfo.description,
      });

      // 生成订单号
      const orderId = this.generateOrderId();
      const outTradeNo = orderId;

      // 计算订单过期时间 (2小时后，给用户足够时间完成支付)
      // 支付宝时间格式：yyyy-MM-dd HH:mm（不包含秒）
      const expireTime = new Date(Date.now() + 2 * 60 * 60 * 1000);

      // 构建回调URL
      const callbackDomain = this.env.PAY_CALLBACK_DOMAIN || 'https://your-worker.workers.dev';
      const webDomain = this.env.PAY_WEB_DOMAIN || 'http://***********:3001';

      const notifyUrl = `${callbackDomain}/api/payment/callback/${orderInfo.paymentMethod}`;
      const returnUrl = `${webDomain}/pay/result?orderId=${orderId}`;

      let result: PaymentResult;

      // 根据支付方式分发到不同的服务
      switch (orderInfo.paymentMethod) {
        case PaymentMethod.ALIPAY:
          result = await this.alipayService.createOrder({
            outTradeNo,
            totalAmount: orderInfo.amount.toFixed(2),
            subject: orderInfo.description,
            body: orderInfo.description,
            notifyUrl,
            returnUrl,
          });
          break;

        case PaymentMethod.WECHAT:
          // 微信支付暂未实现
          result = {
            success: false,
            orderId,
            error: '微信支付暂未实现',
          };
          break;

        default:
          result = {
            success: false,
            orderId,
            error: '不支持的支付方式',
          };
      }

      if (result.success) {
        console.log('✅ [REAL-PAYMENT] 支付订单创建成功:', {
          orderId: result.orderId,
          paymentMethod: orderInfo.paymentMethod,
          redirectUrl: result.redirectUrl?.substring(0, 100) + '...',
        });

        // 保存订单到数据库
        try {
          await createPaymentOrder(this.env, {
            userId: orderInfo.userId,
            planId: orderInfo.planId,
            pointsPackageId: orderInfo.pointsPackageId,
            orderNo: orderId,
            amount: orderInfo.amount,
            currency: orderInfo.currency || 'CNY',
            paymentMethod: orderInfo.paymentMethod,
            description: orderInfo.description,
            isUpgrade: false,
            returnUrl: returnUrl,
            notifyUrl: notifyUrl,
            expiresAt: expireTime,
            metadata: orderInfo.metadata,
          });
          console.log('✅ [REAL-PAYMENT] 订单已保存到数据库:', orderId);
        } catch (dbError) {
          console.error('❌ [REAL-PAYMENT] 保存订单到数据库失败:', dbError);
          // 不阻断支付流程，但记录错误
        }
      } else {
        console.error('❌ [REAL-PAYMENT] 支付订单创建失败:', result.error);
      }

      return result;
    } catch (error) {
      console.error('❌ [REAL-PAYMENT] 创建订单异常:', error);
      return {
        success: false,
        orderId: this.generateOrderId(),
        error: '创建支付订单失败',
      };
    }
  }

  /**
   * 验证支付状态
   */
  async verifyPayment(paymentId: string): Promise<PaymentVerification> {
    try {
      console.log('🔍 [REAL-PAYMENT] 验证支付状态:', paymentId);

      // 目前主要支持支付宝查询，后续可扩展微信
      const verification = await this.alipayService.queryOrder(paymentId);

      console.log('📊 [REAL-PAYMENT] 支付状态查询结果:', {
        paymentId,
        success: verification.success,
        status: verification.status,
      });

      return verification;
    } catch (error) {
      console.error('❌ [REAL-PAYMENT] 验证支付状态失败:', error);
      return {
        success: false,
        orderId: '',
        paymentId,
        status: 'failed',
        amount: 0,
      };
    }
  }

  /**
   * 处理支付回调
   */
  async processCallback(callbackData: any, paymentMethod: PaymentMethod): Promise<CallbackResult> {
    try {
      console.log('📨 [REAL-PAYMENT] 处理支付回调:', {
        paymentMethod,
        orderId: callbackData.out_trade_no || callbackData.orderId,
      });

      let result: CallbackResult;

      switch (paymentMethod) {
        case PaymentMethod.ALIPAY:
          result = this.alipayService.processNotify(callbackData);
          break;

        case PaymentMethod.WECHAT:
          // 微信支付回调处理 (待实现)
          result = {
            success: false,
            message: '微信支付回调处理暂未实现',
          };
          break;

        default:
          result = {
            success: false,
            message: '不支持的支付方式',
          };
      }

      console.log('📋 [REAL-PAYMENT] 回调处理结果:', {
        paymentMethod,
        success: result.success,
        message: result.message,
      });

      return result;
    } catch (error) {
      console.error('❌ [REAL-PAYMENT] 处理回调异常:', error);
      return {
        success: false,
        message: '处理支付回调失败',
      };
    }
  }

  /**
   * 获取调试信息
   */
  getDebugInfo() {
    return {
      service: 'RealPaymentService',
      supportedMethods: [PaymentMethod.ALIPAY],
      features: ['真实支付', '订单查询', '回调处理'],
      note: '生产环境支付服务',
    };
  }

  /**
   * 生成订单号
   */
  private generateOrderId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORDER_${timestamp}_${random}`;
  }
}

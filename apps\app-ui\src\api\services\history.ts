import { apiClient } from '../client'

// 聊天历史类型
export interface ChatHistory {
  id: string
  title: string
  roleId: string
  userId: string
  createdAt: string
  updatedAt: string
  lastMessageAt: string
  messageCount: number
  visibility: 'public' | 'private'
  lastMessage: string
}

// 历史记录 API 服务
export const historyService = {
  // 获取聊天历史列表
  async getList(params?: { limit?: number; startingAfter?: string; endingBefore?: string }) {
    const queryParams = new URLSearchParams()

    if (params?.limit) {
      queryParams.append('limit', params.limit.toString())
    }

    if (params?.startingAfter) {
      queryParams.append('starting_after', params.startingAfter)
    }

    if (params?.endingBefore) {
      queryParams.append('ending_before', params.endingBefore)
    }

    const endpoint = `/api/history${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    return apiClient.get<{ chats: ChatHistory[]; hasMore: boolean }>(endpoint)
  },

  // 获取特定角色的聊天历史
  async getByRole(roleId: string, limit?: number) {
    const queryParams = new URLSearchParams()
    queryParams.append('role', roleId)

    if (limit) {
      queryParams.append('limit', limit.toString())
    }

    return apiClient.get<{ chats: ChatHistory[] }>(`/api/history?${queryParams.toString()}`)
  },

  // 删除聊天记录
  async delete(chatId: string) {
    return apiClient.delete<{ success: boolean }>(`/api/chat/conversations/${chatId}`)
  },

  // 批量删除聊天记录
  async batchDelete(chatIds: string[]) {
    return apiClient.post<{ success: boolean; deletedCount: number }>('/api/history/batch-delete', {
      chatIds
    })
  },

  // 搜索聊天历史
  async search(params: {
    query: string
    limit?: number
    offset?: number
    roleId?: string
    dateFrom?: string
    dateTo?: string
  }) {
    const queryParams = new URLSearchParams()

    queryParams.append('q', params.query)

    if (params.limit) {
      queryParams.append('limit', params.limit.toString())
    }
    if (params.offset) {
      queryParams.append('offset', params.offset.toString())
    }
    if (params.roleId) {
      queryParams.append('roleId', params.roleId)
    }
    if (params.dateFrom) {
      queryParams.append('dateFrom', params.dateFrom)
    }
    if (params.dateTo) {
      queryParams.append('dateTo', params.dateTo)
    }

    return apiClient.get<{
      success: boolean
      data: {
        chats: ChatHistory[]
        total: number
        hasMore: boolean
      }
    }>(`/api/history/search?${queryParams.toString()}`)
  },

  // 获取聊天统计信息
  async getStats(params?: { dateFrom?: string; dateTo?: string; roleId?: string }) {
    const queryParams = new URLSearchParams()

    if (params?.dateFrom) {
      queryParams.append('dateFrom', params.dateFrom)
    }
    if (params?.dateTo) {
      queryParams.append('dateTo', params.dateTo)
    }
    if (params?.roleId) {
      queryParams.append('roleId', params.roleId)
    }

    const endpoint = `/api/history/stats${
      queryParams.toString() ? `?${queryParams.toString()}` : ''
    }`
    return apiClient.get<{
      success: boolean
      data: {
        totalChats: number
        totalMessages: number
        averageMessagesPerChat: number
        mostActiveRole: string
        dailyStats: Array<{
          date: string
          chatCount: number
          messageCount: number
        }>
      }
    }>(endpoint)
  }
}

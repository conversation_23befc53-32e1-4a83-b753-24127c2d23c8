<svg width="375" height="265" viewBox="0 0 375 265" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_752_2378" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="375" height="265">
<rect width="375" height="265" fill="#D9D9D9"/>
</mask>
<g mask="url(#mask0_752_2378)">
<g filter="url(#filter0_f_752_2378)">
<path d="M321 2.44077C321 66.7492 276.009 84.8964 211.556 84.8964C147.103 84.8964 58 90.1488 58 25.8404C58 -38.4681 139.844 -114 204.297 -114C268.75 -114 321 -61.8677 321 2.44077Z" fill="#66E8FF" fill-opacity="0.3"/>
</g>
<g opacity="0.4" filter="url(#filter1_f_752_2378)">
<path d="M92 9.00012C172.5 78.0002 59.3199 169 -18 169C-95.3199 169 -158 106.32 -158 29C-158 -48.3199 -95.3199 -111 -18 -111C59.3199 -111 11.5 -59.9999 92 9.00012Z" fill="#EA2DA9" fill-opacity="0.5"/>
</g>
<g filter="url(#filter2_f_752_2378)">
<path d="M321 -66.5C413.158 -74.7212 475.386 -43.94 483.607 48.2177C491.828 140.375 542.361 242.026 450.203 250.248C358.046 258.469 276.672 190.425 268.451 98.2671C260.23 6.10938 228.842 -58.2788 321 -66.5Z" fill="#892FFF" fill-opacity="0.3"/>
</g>
</g>
<defs>
<filter id="filter0_f_752_2378" x="-14" y="-186" width="407" height="343" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_752_2378"/>
</filter>
<filter id="filter1_f_752_2378" x="-230" y="-183" width="421.668" height="424" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_752_2378"/>
</filter>
<filter id="filter2_f_752_2378" x="184.641" y="-139.775" width="392.167" height="462.695" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="36" result="effect1_foregroundBlur_752_2378"/>
</filter>
</defs>
</svg>

{"api": {"request_failed": "API Request Failed", "unknown_error": "Unknown API Error", "unauthorized": "Login Expired", "unauthorized_description": "Please log in again to continue", "forbidden": "Access Denied", "forbidden_description": "You don't have permission to perform this action", "not_found": "Resource Not Found", "not_found_description": "The requested resource was not found", "server_error": "Server Error", "server_error_description": "Server is temporarily unavailable, please try again later", "network_error": "Network Connection Failed", "network_error_description": "Please check your network connection and try again", "timeout_error": "Request Timeout", "timeout_error_description": "Request took too long to process, please try again", "validation_error": "Data Validation Failed", "validation_error_description": "Please check your input data format", "rate_limit": "Too Many Requests", "rate_limit_description": "Please try again later"}, "avatar": {"select_image": "Please select an image file", "size_limit": "Image size cannot exceed 1MB", "uploading": "Uploading...", "upload_avatar": "Upload Avatar", "support_format": "Supports JPG, PNG formats,", "size_limit_note": "size not exceeding 1MB"}, "permission": {"check_failed": "Permission Check Failed", "check_failed_description": "The system is temporarily unable to verify your permissions, please try again later", "points_insufficient": "Insufficient Points", "points_required": "Using {{featureName}} requires {{points}} points", "member_only": "Member-Only Feature", "member_only_description": "{{featureName}} is a member-only feature", "usage_limit": "Usage Limit Reached", "insufficient": "Insufficient Permission"}, "user": {"profile_updated": "Profile updated successfully", "profile_update_failed": "Failed to update profile", "info_load_failed": "Failed to load user information", "status_load_failed": "Failed to load user status"}, "membership": {"subscription_created": "Subscription created successfully", "subscription_failed": "Failed to create subscription", "points_consumed": "Points consumed successfully", "points_consume_failed": "Failed to consume points", "insufficient_points": "Insufficient points", "member_required": "Membership required"}, "points": {"package_not_found": "Points package not found", "package_inactive": "Points package is no longer available", "order_create_failed": "Failed to create order"}}
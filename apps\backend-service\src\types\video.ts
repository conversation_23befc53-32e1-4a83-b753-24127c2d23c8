type MessageId = string

/**
 * 视频生成任务
 */
export interface VideoGenerationTask {
  taskId: string
  userId: string
  messageId: MessageId
  prompt: string
  characterAvatar?: string
  // 新增：任务类型标识
  taskType?: 'direct_video' | 'multimodal_video' // 直接视频生成 vs 多模态视频生成
  metadata?: {
    width?: number
    height?: number
    duration?: number // 视频时长(秒)
    fps?: number // 帧率
    steps?: number
    cfg?: number
    negativePrompt?: string
    mediaGenerationId?: string
    pointsUsed?: number
  }
}

/**
 * 视频生成进度
 */
export interface VideoGenerationProgress {
  messageId: MessageId
  status: 'starting' | 'processing' | 'completed' | 'failed'
  progress: number // 0-100
  message: string
  timestamp: string
  // 多模态视频生成的额外字段
  stage?: 'image_generation' | 'video_generation'
  intermediateImageUrl?: string
}

/**
 * Insa3D 视频任务请求
 */
export interface Insa3DVideoTaskRequest {
  inputs: {
    // 提示词
    [key: string]: {
      title: string
      value: string | number
    }
  }
}

/**
 * Insa3D 视频任务响应
 */
export interface Insa3DVideoTaskResponse {
  task_id: string
  status: string
  message?: string
}

/**
 * Insa3D 视频任务状态
 */
export interface Insa3DVideoTaskStatus {
  task_id: string
  status: 'PENDING' | 'IN_PROGRESS' | 'EXECUTING' | 'COMPLETED' | 'FAILED'
  progress?: number
  video_urls?: string[]
  error_message?: string
  created_at: string
  updated_at: string
}

/**
 * 视频附件基础类型
 */
export interface VideoAttachment {
  url: string
  name: string
  contentType: string
  metadata?: Record<string, any>
}

/**
 * 正在生成的视频状态附件
 */
export interface GeneratingVideoStatusAttachment extends VideoAttachment {
  contentType: 'video/generating' | 'video/multimodal-generating'
  metadata: {
    status: 'starting' | 'processing' | 'completed' | 'failed'
    progress: number
    timestamp: string
    taskId: string
    stage?: string // 多模态视频生成阶段
    intermediateImageUrl?: string // 中间生成的图片URL
  }
}

/**
 * 已完成的视频附件
 */
export interface CompletedVideoAttachment extends VideoAttachment {
  contentType: 'video/mp4' | 'video/webm' | 'video/mov'
  metadata: {
    taskId: string
    generatedAt: string
    duration?: number // 视频时长(秒)
    resolution?: {
      width: number
      height: number
    }
    fileSize?: number // 文件大小(字节)
  }
}

/**
 * 视频生成API请求
 */
export interface VideoGenerationRequest {
  prompt: string
  characterAvatar?: string
  chatId?: string
  messageId?: string
  metadata?: {
    width?: number
    height?: number
    duration?: number
    fps?: number
    steps?: number
    cfg?: number
    negativePrompt?: string
  }
}

/**
 * 视频生成API响应
 */
export interface VideoGenerationResponse {
  success: boolean
  data: {
    taskId: string
    messageId: string
    status: 'queued'
    pointsUsed: number
    remainingPoints: number
    estimatedTime?: number // 预计完成时间(秒)
  }
  message: string
}

/**
 * 视频生成错误响应
 */
export interface VideoGenerationErrorResponse {
  success: false
  error: string
  data?: {
    required?: number
    available?: number
  }
}

/**
 * 视频播放器配置
 */
export interface VideoPlayerConfig {
  autoplay?: boolean
  controls?: boolean
  loop?: boolean
  muted?: boolean
  poster?: string // 封面图
  preload?: 'none' | 'metadata' | 'auto'
}

/**
 * 视频元数据
 */
export interface VideoMetadata {
  duration: number
  width: number
  height: number
  fps: number
  format: string
  fileSize: number
  bitrate?: number
  codec?: string
}

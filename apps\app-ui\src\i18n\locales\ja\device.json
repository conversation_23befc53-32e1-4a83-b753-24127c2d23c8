{"connection": {"title": "デバイス接続", "failed": "デバイス接続失敗", "disconnect_failed": "デバイス切断失敗", "bluetooth_device": "Bluetoothデバイス"}, "connection_form": {"input_placeholder": "デバイスコードを入力してください", "invalid_code": "無効なデバイスコードです。確認して再試行してください", "connect_failed": "デバイス接続に失敗しました。後でもう一度お試しください", "qr_not_supported": "現在のデバイスはQRコードスキャンをサポートしていません", "invalid_qr_code": "スキャンしたデバイスコードが無効です", "qr_unrecognized": "QRコードの内容を認識できませんでした", "camera_permission_denied": "カメラ権限が拒否されました。設定でカメラアクセスを有効にしてください", "scan_not_supported": "現在のデバイスはQRコードスキャン機能をサポートしていません", "scan_failed": "スキャンに失敗しました。再試行してください", "input_label": "デバイスコードを入力", "input_example": "サンプルコード: 1583, 6842, 2137", "connecting": "接続中...", "connect_button": "デバイス接続", "or": "または", "scanning": "スキャン中...", "scan_button": "デバイスQRコードをスキャン", "scan_instruction": "デバイスのQRコードをカメラの前に置いてスキャンしてください"}, "control": {"title": "デバイスコントロールパネル", "connected": "接続済み", "bluetooth_error": "Bluetoothエラー", "bluetooth_ready": "Bluetooth準備完了", "bluetooth_initializing": "Bluetooth初期化中...", "bluetooth_not_initialized": "Bluetooth未初期化", "wait_bluetooth_init": "Bluetooth初期化完了まで待機してください", "bluetooth_connection_error": "Bluetooth接続エラー", "mode_not_found": "モードIDが見つかりません", "select_classic_mode": "クラシックモードを選択", "send_classic_mode_failed": "クラシックモードコマンド送信に失敗", "device_function_set": "デバイス機能{{functionKey}}の強度を{{statusText}}に設定 (値: {{intensity}})", "debounce_delay": "デバウンス遅延後にBluetoothコマンドを送信", "intensity_zero": "強度が0、停止指令を送信", "stop_command_sent": "停止コマンドを送信しました", "stop_command_not_found": "機能{{functionKey}}の停止コマンドが見つかりません", "intensity": "強度", "off": "オフ", "level": "レベル{{level}}", "classic_mode": "クラシックモード", "disconnect_failed": "切断に失敗", "disconnect_button": "切断"}}
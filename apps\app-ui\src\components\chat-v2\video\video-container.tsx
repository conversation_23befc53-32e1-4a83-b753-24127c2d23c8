import { memo, useEffect, useState } from 'react'
import { MultimodalVideoGeneration } from './multimodal-video-generation'
import { VideoStorage } from '@/lib/media/video-storage'
import { getGlobalChatDatabase } from '@/lib/chat-database'
import { VideoDisplay } from './video-display'
import { useTranslation } from 'react-i18next'

interface VideoContainerProps {
  // 生成相关
  prompt?: string
  characterAvatar?: string | null
  messageId?: string
  chatId?: string

  // 已有视频相关
  existingVideoUrl?: string
  existingAttachments?: Array<{
    url: string
    name?: string
    contentType?: string
    metadata?: any
  }>

  // 控制逻辑
  mode: 'display' | 'generate' | 'auto' // auto会自动判断

  // 回调
  onVideoGenerated?: (videoUrl: string) => void
  onError?: (error: string) => void
}

interface VideoState {
  videoUrl: string | null
  isLocalizing: boolean
  shouldGenerate: boolean
  error: string | null
}

const PureVideoContainer = ({
  prompt,
  characterAvatar,
  messageId,
  chatId,
  existingVideoUrl,
  existingAttachments,
  mode = 'auto',
  onVideoGenerated,
  onError
}: VideoContainerProps) => {
  const { t } = useTranslation('chat-v2')
  const [state, setState] = useState<VideoState>({
    videoUrl: null,
    isLocalizing: false,
    shouldGenerate: false,
    error: null
  })

  // 统一的本地化逻辑
  const localizeVideo = async (remoteUrl: string, messageId: string): Promise<string | null> => {
    if (!messageId) return null

    try {
      setState(prev => ({ ...prev, isLocalizing: true }))

      const videoStorage = VideoStorage.getInstance()
      const chatDatabase = getGlobalChatDatabase()

      // 1. 检查数据库记录
      if (chatDatabase) {
        const attachments = await chatDatabase.getAttachmentsByMessage(messageId)
        const videoAttachment = attachments.find(
          att => att.type === 'video' && att.status === 'completed' && att.localPath
        )

        if (videoAttachment?.localPath) {
          const fileExists = await videoStorage.fileExists(videoAttachment.localPath)
          if (fileExists) {
            const localUrl = await videoStorage.getVideoUrl(videoAttachment.localPath)
            if (localUrl) {
              return localUrl
            }
          } else {
            // 清理失效记录
            await chatDatabase.deleteAttachment(videoAttachment.id)
          }
        }
      }

      // 2. 检查文件系统
      const localPath = await videoStorage.findVideoByMessageId(messageId)
      if (localPath) {
        const localUrl = await videoStorage.getVideoUrl(localPath)
        if (localUrl) {
          return localUrl
        }
      }

      // 3. 下载并本地化
      const savedPath = await videoStorage.saveVideoFromUrl(
        remoteUrl,
        messageId,
        'container_localize'
      )

      if (savedPath && chatDatabase) {
        // 保存到数据库
        try {
          const existingAttachments = await chatDatabase.getAttachmentsByMessage(messageId)
          const existingVideoAttachment = existingAttachments.find(att => att.type === 'video')

          if (existingVideoAttachment) {
            await chatDatabase.updateAttachment(existingVideoAttachment.id, {
              localPath: savedPath,
              status: 'completed',
              originalUrl: remoteUrl
            })
          } else {
            const attachmentId = `video_${messageId}_localized_${Date.now()}`

            await chatDatabase.createAttachment({
              id: attachmentId,
              messageId,
              type: 'video',
              name: savedPath.split('/').pop() || 'localized_video.mp4',
              contentType: 'video/mp4',
              originalUrl: remoteUrl,
              localPath: savedPath,
              status: 'completed',
              metadata: JSON.stringify({
                source: 'container_localize',
                timestamp: Date.now()
              })
            })
          }
        } catch (dbError) {
          console.warn(`⚠️ [VideoContainer] ${t('video.database_save_failed')}:`, dbError)
        }
      }

      const localUrl = await videoStorage.getVideoUrl(savedPath)
      return localUrl || remoteUrl
    } catch (error) {
      console.warn(`⚠️ [VideoContainer] ${t('video.localization_failed')}:`, error)
      return remoteUrl // 降级到远程URL
    } finally {
      setState(prev => ({ ...prev, isLocalizing: false }))
    }
  }

  // 主要初始化逻辑
  useEffect(() => {
    const initialize = async () => {
      // 重置状态
      setState({
        videoUrl: null,
        isLocalizing: false,
        shouldGenerate: false,
        error: null
      })

      // 1. 处理已有视频 (来自existingVideoUrl或attachments)
      let remoteVideoUrl: string | null = null

      if (existingVideoUrl) {
        remoteVideoUrl = existingVideoUrl
      } else if (existingAttachments && existingAttachments.length > 0) {
        const videoAttachment = existingAttachments.find(
          att =>
            (att.contentType === 'video/mp4' ||
              att.contentType === 'video/webm' ||
              att.contentType?.startsWith('video/') ||
              (!att.contentType && att.url.includes('.mp4')) ||
              (!att.contentType && att.url.includes('.webm'))) &&
            !att.contentType?.includes('generating')
        )
        if (videoAttachment) {
          remoteVideoUrl = videoAttachment.url
        }
      }

      // 2. 如果有已存在的视频，进行本地化
      if (remoteVideoUrl && messageId) {
        const localizedUrl = await localizeVideo(remoteVideoUrl, messageId)
        setState(prev => ({
          ...prev,
          videoUrl: localizedUrl,
          shouldGenerate: false
        }))
        return
      }

      // 3. 判断是否需要生成
      if (mode === 'generate' || (mode === 'auto' && prompt && !remoteVideoUrl)) {
        setState(prev => ({
          ...prev,
          shouldGenerate: true
        }))
        return
      }

      // 4. 显示模式但无视频
      if (mode === 'display' && !remoteVideoUrl) {
        setState(prev => ({
          ...prev,
          error: t('video.no_video')
        }))
      }
    }

    initialize()
  }, [existingVideoUrl, existingAttachments, prompt, messageId, mode])

  // 渲染逻辑
  if (state.error) {
    return <div className="text-sm text-gray-500 text-center p-4">{state.error}</div>
  }

  // 显示已有视频
  if (state.videoUrl) {
    return (
      <div className="relative">
        <VideoDisplay videoUrl={state.videoUrl} title={t('video.ai_generated_video')} />
      </div>
    )
  }

  // 生成新视频
  if (state.shouldGenerate && prompt && characterAvatar && messageId && chatId) {
    return (
      <MultimodalVideoGeneration
        prompt={prompt}
        characterAvatar={characterAvatar}
        messageId={messageId}
        chatId={chatId}
        existingAttachments={existingAttachments}
        onVideoGenerated={onVideoGenerated}
        onError={onError}
      />
    )
  }

  // 加载状态
  return <div className="text-sm text-gray-500 text-center p-4">{t('video.processing')}</div>
}

export const VideoContainer = memo(PureVideoContainer, (prevProps, nextProps) => {
  return (
    prevProps.prompt === nextProps.prompt &&
    prevProps.characterAvatar === nextProps.characterAvatar &&
    prevProps.messageId === nextProps.messageId &&
    prevProps.chatId === nextProps.chatId &&
    prevProps.existingVideoUrl === nextProps.existingVideoUrl &&
    prevProps.mode === nextProps.mode &&
    JSON.stringify(prevProps.existingAttachments) === JSON.stringify(nextProps.existingAttachments)
  )
})

// 核心对话引擎

import { ChatOpenAI } from '@langchain/openai'
import { HumanMessage, SystemMessage, AIMessage } from '@langchain/core/messages'
import type { LangChainEnv, CharacterType } from '../types'
import type { LangChainChatOptions, ChatContext, LangChainMessage } from '../types/chat'
import { PromptManager } from '../prompts/prompt-manager'
import { StructuredOutputParser } from '../parsers/output-parser'
import { createXaiProviderConfig } from './providers'
import { getMessage } from '@/i18n/messages'
import type { SupportedLanguage } from '@/i18n/config'
export class LangChainChatEngine {
  private promptManager: PromptManager
  private outputParser: StructuredOutputParser
  private env: LangChainEnv

  constructor(env: LangChainEnv) {
    this.env = env
    this.promptManager = new PromptManager()
    this.outputParser = new StructuredOutputParser()
  }

  /**
   * 创建聊天模型实例
   */
  private createChatModel(): ChatOpenAI {
    const config = createXaiProviderConfig(this.env)

    console.log('🔍 [DEBUG] 创建聊天模型配置:', config)

    return new ChatOpenAI({
      apiKey: config.apiKey,
      modelName: config.modelName,
      configuration: {
        baseURL: config.baseURL
      },
      streaming: true,
      temperature: config.temperature,
      topP: config.topP,
      frequencyPenalty: config.frequency_penalty,
      presencePenalty: config.presence_penalty,
      stop: undefined,
      timeout: 60000,
      maxRetries: 3,
      verbose: process.env.NODE_ENV === 'development'
    })
  }

  /**
   * 准备聊天上下文
   */
  private async prepareChatContext(
    options: LangChainChatOptions,
    character?: CharacterType
  ): Promise<ChatContext> {
    // 生成系统提示词
    const systemPrompt = await this.promptManager.generateFullSystemPrompt({
      username: options.username,
      userGender: options.userGender,
      character: character,
      conversationHistory: [], // 历史消息会在调用时单独处理
      memories: [] // 预留记忆功能
    })

    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 [DEBUG] 系统提示词生成完成，角色信息:', character?.name || '无角色')
    }

    return {
      messages: [], // 会在调用时填充
      character: character,
      systemPrompt
    }
  }

  /**
   * 转换消息格式为 LangChain 格式
   */
  private convertToLangChainMessages(messages: LangChainMessage[]) {
    const isDev = process.env.NODE_ENV === 'development'

    if (isDev) console.log('🔍 [DEBUG] 开始转换消息格式，消息数量:', messages.length)

    const convertedMessages = messages.map((msg, index) => {
      if (isDev)
        console.log(
          `🔍 [DEBUG] 转换消息 ${index + 1}: ${msg.role} - ${msg.content.substring(0, 50)}...`
        )

      switch (msg.role) {
        case 'user':
          return new HumanMessage(msg.content)
        case 'assistant':
          return new AIMessage(msg.content)
        case 'system':
          return new SystemMessage(msg.content)
        default:
          if (isDev) console.warn(`🔍 [DEBUG] 未知消息角色: ${msg.role}，默认作为用户消息处理`)
          return new HumanMessage(msg.content)
      }
    })

    if (isDev) console.log('🔍 [DEBUG] 消息转换完成，转换后数量:', convertedMessages.length)
    return convertedMessages
  }

  /**
   * 生成单次回复（非流式）
   */
  async generateResponse(
    options: LangChainChatOptions,
    messages: LangChainMessage[],
    character?: CharacterType
  ): Promise<string> {
    // 创建聊天模型
    const chatModel = this.createChatModel()

    // 准备上下文
    const context = await this.prepareChatContext(options, character)

    // 转换消息格式
    const langchainMessages = this.convertToLangChainMessages(messages)

    // 添加系统消息
    const allMessages = [
      new SystemMessage(context.systemPrompt),
      ...langchainMessages,
      new HumanMessage(options.message.content)
    ]

    // 调用模型生成回复
    const response = await chatModel.invoke(allMessages)

    return response.content as string
  }

  /**
   * 生成流式回复
   */
  async *generateStreamResponse(
    options: LangChainChatOptions,
    messages: LangChainMessage[],
    character?: CharacterType
  ) {
    const startTime = Date.now()
    const isDev = process.env.NODE_ENV === 'development'

    if (isDev) {
      console.log('🔍 [DEBUG] 开始生成流式回复')
      console.log('🔍 [DEBUG] 输入参数:', {
        chatId: options.chatId,
        messagesCount: messages.length,
        hasCharacter: !!character,
        characterName: character?.name
      })
    }

    try {
      // 创建聊天模型
      const chatModel = this.createChatModel()

      // 准备上下文
      const context = await this.prepareChatContext(options, character)

      // 转换消息格式
      const langchainMessages = this.convertToLangChainMessages(messages)

      // 构建完整的消息列表
      const allMessages = [
        new SystemMessage(context.systemPrompt),
        ...langchainMessages,
        new HumanMessage(options.message.content)
      ]

      if (isDev) {
        console.log('🔍 [DEBUG] 最终消息列表构建完成，总数量:', allMessages.length)
        console.log('🔍 [DEBUG] 系统提示词长度:', context.systemPrompt.length)
        console.log('🔍 [DEBUG] 用户当前消息:', options.message.content.substring(0, 100) + '...')
      }

      // 流式调用模型
      const stream = await chatModel.stream(allMessages)

      let chunkCount = 0
      let totalLength = 0

      for await (const chunk of stream) {
        if (chunk.content) {
          chunkCount++
          totalLength += chunk.content.length

          // 每100个chunk输出一次统计（仅开发环境）
          if (isDev && chunkCount % 100 === 0) {
            console.log(`🔍 [DEBUG] 已处理 ${chunkCount} 个chunk，总长度: ${totalLength}`)
          }

          yield chunk.content as string
        }
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      if (isDev) {
        console.log('🔍 [DEBUG] 流式回复生成完成')
        console.log('🔍 [DEBUG] 性能统计:', {
          duration: `${duration}ms`,
          chunkCount,
          totalLength,
          avgChunkSize: Math.round(totalLength / chunkCount),
          tokensPerSecond: Math.round((totalLength / duration) * 1000)
        })
      }
    } catch (error) {
      const endTime = Date.now()
      const duration = endTime - startTime

      // 🚀 检查是否为403错误（内容违规）
      if ((error as Error).message.includes('403')) {
        console.warn('⚠️ [WARNING] 检测到内容违规 (403):', {
          chatId: options.chatId,
          duration: `${duration}ms`,
          userMessage: options.message.content.substring(0, 50) + '...',
          language: options.language
        })

        // 根据用户语言返回对应的错误消息
        const language = (options.language as SupportedLanguage) || 'zh'
        const errorMessage = getMessage('content_violation', language)
        yield `<error>${errorMessage}</error>`
        return
      }

      console.error('❌ [ERROR] 流式回复生成失败:', {
        error: (error as Error).message,
        duration: `${duration}ms`,
        chatId: options.chatId
      })

      throw error
    }
  }

  /**
   * 解析AI输出为多模态响应
   */
  parseResponse(aiOutput: string) {
    return this.outputParser.parse(aiOutput)
  }

  /**
   * 验证AI输出格式
   */
  validateResponse(aiOutput: string) {
    return this.outputParser.validateOutput(aiOutput)
  }

  /**
   * 提取纯文本内容
   */
  extractPlainText(aiOutput: string): string {
    return this.outputParser.extractPlainText(aiOutput)
  }

  /**
   * 检查是否包含特定模态
   */
  hasModality(aiOutput: string, modality: 'text' | 'image' | 'audio'): boolean {
    return this.outputParser.hasModality(aiOutput, modality)
  }
}

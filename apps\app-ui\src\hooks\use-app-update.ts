import { useState, useEffect, useCallback, useRef } from 'react'
import {
  updateManager,
  type UpdateCheckResult,
  type UpdateProgress
} from '@/services/update-manager'

export interface UseAppUpdateOptions {
  autoCheck?: boolean // 是否自动检查更新
  autoCheckInterval?: number // 自动检查间隔（毫秒）
  onUpdateAvailable?: (updateInfo: UpdateCheckResult) => void
  onUpdateProgress?: (progress: UpdateProgress) => void
  onUpdateComplete?: () => void
  onUpdateError?: (error: Error) => void
}

export function useAppUpdate(options: UseAppUpdateOptions = {}) {
  const {
    autoCheck = true,
    autoCheckInterval = 60 * 60 * 1000, // 默认1小时检查一次
    onUpdateAvailable,
    onUpdateProgress,
    onUpdateComplete,
    onUpdateError
  } = options

  // 状态管理
  const [isChecking, setIsChecking] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [updateInfo, setUpdateInfo] = useState<UpdateCheckResult | null>(null)
  const [updateProgress, setUpdateProgress] = useState<UpdateProgress | null>(null)
  const [error, setError] = useState<Error | null>(null)

  // 引用管理
  const intervalRef = useRef<NodeJS.Timeout>()
  const unsubscribeProgressRef = useRef<(() => void) | null>(null)

  // 清理函数
  const cleanup = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = undefined
    }
    if (unsubscribeProgressRef.current) {
      unsubscribeProgressRef.current()
      unsubscribeProgressRef.current = null
    }
  }, [])

  // 处理更新进度
  const handleUpdateProgress = useCallback(
    (progress: UpdateProgress) => {
      setUpdateProgress(progress)
      onUpdateProgress?.(progress)

      // 更新完成
      if (progress.status === 'completed') {
        setIsUpdating(false)
        setUpdateProgress(null)
        setUpdateInfo(null)
        onUpdateComplete?.()
      }

      // 更新失败
      if (progress.status === 'failed') {
        setIsUpdating(false)
        setUpdateProgress(null)
        const error = new Error(progress.message)
        setError(error)
        onUpdateError?.(error)
      }
    },
    [onUpdateProgress, onUpdateComplete, onUpdateError]
  )

  // 检查更新
  const checkForUpdates = useCallback(
    async (force = false) => {
      if (isChecking && !force) {
        return null
      }

      setIsChecking(true)
      setError(null)

      try {
        const result = await updateManager.checkForUpdates({ force })

        if (result && (result.hasApkUpdate || result.hasHotfixUpdate)) {
          setUpdateInfo(result)
          onUpdateAvailable?.(result)
          return result
        }

        setUpdateInfo(null)
        return null
      } catch (err) {
        const error = err instanceof Error ? err : new Error('检查更新失败')
        setError(error)
        onUpdateError?.(error)
        return null
      } finally {
        setIsChecking(false)
      }
    },
    [isChecking, onUpdateAvailable, onUpdateError]
  )

  // 执行更新
  const performUpdate = useCallback(
    async (updateInfo?: UpdateCheckResult) => {
      const targetUpdate = updateInfo || updateInfo
      if (!targetUpdate) {
        throw new Error('没有可用的更新信息')
      }

      setIsUpdating(true)
      setUpdateProgress(null)
      setError(null)

      // 注册进度监听
      if (unsubscribeProgressRef.current) {
        unsubscribeProgressRef.current()
      }
      unsubscribeProgressRef.current = updateManager.onUpdateProgress(handleUpdateProgress)

      try {
        let success = false

        // 优先执行APK更新
        if (targetUpdate.hasApkUpdate && targetUpdate.apkUpdate) {
          success = await updateManager.performApkUpdate(targetUpdate.apkUpdate)
        }
        // 否则执行热更新
        else if (targetUpdate.hasHotfixUpdate && targetUpdate.hotfixUpdate) {
          success = await updateManager.performHotfixUpdate(targetUpdate.hotfixUpdate)
        }

        if (!success) {
          throw new Error('更新执行失败')
        }

        return success
      } catch (err) {
        setIsUpdating(false)
        setUpdateProgress(null)
        const error = err instanceof Error ? err : new Error('更新失败')
        setError(error)
        onUpdateError?.(error)
        throw error
      }
    },
    [handleUpdateProgress, onUpdateError]
  )

  // 检查是否应该显示更新提示
  const shouldShowUpdatePrompt = useCallback(
    (updateResult?: UpdateCheckResult) => {
      const target = updateResult || updateInfo
      return target ? updateManager.shouldShowUpdatePrompt(target) : false
    },
    [updateInfo]
  )

  // 检查是否为强制更新
  const isForcedUpdate = useCallback(
    (updateResult?: UpdateCheckResult) => {
      const target = updateResult || updateInfo
      return target ? updateManager.isForcedUpdate(target) : false
    },
    [updateInfo]
  )

  // 获取更新优先级
  const getUpdatePriority = useCallback(
    (updateResult?: UpdateCheckResult) => {
      const target = updateResult || updateInfo
      return target ? updateManager.getUpdatePriority(target) : null
    },
    [updateInfo]
  )

  // 重置到基础版本
  const resetToBaseVersion = useCallback(async () => {
    try {
      const success = await updateManager.resetToBaseVersion()
      if (success) {
        setUpdateInfo(null)
        setUpdateProgress(null)
        setError(null)
      }
      return success
    } catch (err) {
      const error = err instanceof Error ? err : new Error('重置失败')
      setError(error)
      onUpdateError?.(error)
      return false
    }
  }, [onUpdateError])

  // 获取当前bundle信息
  const getCurrentBundleInfo = useCallback(async () => {
    try {
      return await updateManager.getCurrentBundleInfo()
    } catch (err) {
      const error = err instanceof Error ? err : new Error('获取bundle信息失败')
      setError(error)
      onUpdateError?.(error)
      return null
    }
  }, [onUpdateError])

  // 启动时检查更新
  const checkOnStartup = useCallback(async () => {
    try {
      const result = await updateManager.checkOnStartup()
      if (result && (result.hasApkUpdate || result.hasHotfixUpdate)) {
        setUpdateInfo(result)
        onUpdateAvailable?.(result)
        return result
      }
      return null
    } catch (err) {
      const error = err instanceof Error ? err : new Error('启动检查更新失败')
      setError(error)
      onUpdateError?.(error)
      return null
    }
  }, [onUpdateAvailable, onUpdateError])

  // 清除错误
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // 清除更新信息
  const clearUpdateInfo = useCallback(() => {
    setUpdateInfo(null)
  }, [])

  // 初始化和清理
  useEffect(() => {
    // 启动时检查更新
    if (autoCheck) {
      checkOnStartup()
    }

    // 设置定时检查
    if (autoCheck && autoCheckInterval > 0) {
      intervalRef.current = setInterval(() => {
        checkForUpdates()
      }, autoCheckInterval)
    }

    // 清理函数
    return () => {
      cleanup()
    }
  }, [autoCheck, autoCheckInterval, checkOnStartup, checkForUpdates, cleanup])

  return {
    // 状态
    isChecking,
    isUpdating,
    updateInfo,
    updateProgress,
    error,

    // 方法
    checkForUpdates,
    performUpdate,
    shouldShowUpdatePrompt,
    isForcedUpdate,
    getUpdatePriority,
    resetToBaseVersion,
    getCurrentBundleInfo,
    checkOnStartup,
    clearError,
    clearUpdateInfo,

    // 便利方法
    hasUpdate: updateInfo !== null,
    hasApkUpdate: updateInfo?.hasApkUpdate || false,
    hasHotfixUpdate: updateInfo?.hasHotfixUpdate || false,
    isProgressActive: updateProgress !== null,
    progressValue: updateProgress?.progress || 0,
    progressMessage: updateProgress?.message || ''
  }
}

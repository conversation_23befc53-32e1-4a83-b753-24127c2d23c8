// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_21
      targetCompatibility JavaVersion.VERSION_21
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-community-bluetooth-le')
    implementation project(':capacitor-community-media')
    implementation project(':capacitor-community-sqlite')
    implementation project(':capacitor-app')
    implementation project(':capacitor-barcode-scanner')
    implementation project(':capacitor-device')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-network')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-privacy-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capawesome-capacitor-live-update')
    implementation project(':capacitor-ble-advertiser')

}


if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}

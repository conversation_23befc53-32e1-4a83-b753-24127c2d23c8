{"api": {"request_failed": "APIリクエスト失敗", "unknown_error": "不明なAPIエラー", "unauthorized": "ログイン期限切れ", "unauthorized_description": "再度ログインして続行してください", "forbidden": "権限不足", "forbidden_description": "この操作を実行する権限がありません", "not_found": "リソースが存在しません", "not_found_description": "リクエストされたリソースが見つかりません", "server_error": "サーバーエラー", "server_error_description": "サーバーが一時的に利用できません。後でもう一度お試しください", "network_error": "ネットワーク接続失敗", "network_error_description": "ネットワーク接続を確認して再試行してください", "timeout_error": "リクエストタイムアウト", "timeout_error_description": "リクエスト処理時間が長すぎます。再試行してください", "validation_error": "データ検証失敗", "validation_error_description": "入力データの形式を確認してください", "rate_limit": "リクエストが頻繁すぎます", "rate_limit_description": "後でもう一度お試しください"}, "avatar": {"select_image": "画像ファイルを選択してください", "size_limit": "画像サイズは1MBを超えることはできません", "uploading": "アップロード中...", "upload_avatar": "アバターアップロード", "support_format": "JPG、PNG形式をサポート、", "size_limit_note": "サイズは1MB以下"}, "permission": {"check_failed": "権限確認失敗", "check_failed_description": "システムが一時的に権限を確認できません。後でもう一度お試しください", "points_insufficient": "ポイント不足", "points_required": "{{featureName}}の使用には{{points}}ポイントが必要です", "member_only": "メンバー専用機能", "member_only_description": "{{featureName}}はメンバー専用機能です", "usage_limit": "使用回数上限に達しました", "insufficient": "権限不足"}, "user": {"profile_updated": "個人プロフィール更新成功", "profile_update_failed": "個人プロフィール更新失敗", "info_load_failed": "ユーザー情報読み込み失敗", "status_load_failed": "ユーザーステータス読み込み失敗"}, "membership": {"subscription_created": "サブスクリプション作成成功", "subscription_failed": "サブスクリプション作成失敗", "points_consumed": "ポイント消費成功", "points_consume_failed": "ポイント消費失敗", "insufficient_points": "ポイント不足", "member_required": "メンバーシップが必要です"}, "points": {"package_not_found": "ポイントパッケージが存在しません", "package_inactive": "ポイントパッケージは販売終了しました", "order_create_failed": "注文作成失敗"}}
import { apiService } from './api'
import type { ApiResponse, PaginatedResponse } from '@/types/api'

// 应用版本接口
export interface AppVersion {
  id: string
  versionName: string
  versionCode: number
  versionType: 'apk' | 'hotfix'
  fileUrl: string
  fileSize?: number
  fileHash?: string
  minCompatibleVersion?: string
  releaseNotes?: string
  createdAt: string
  updatedAt: string
}

// 应用更新策略接口
export interface AppUpdatePolicy {
  id: string
  versionId: string
  channel: string
  updateStrategy: 'force' | 'optional' | 'silent'
  targetVersionMin?: string
  targetVersionMax?: string
  rolloutPercentage: number
  isActive: boolean
  createdAt: string
}

// 版本列表查询参数
export interface VersionListParams {
  page?: number
  pageSize?: number
  versionType?: 'apk' | 'hotfix'
}

// 创建版本参数
export interface CreateVersionParams {
  versionName: string
  versionCode: number
  versionType: 'apk' | 'hotfix'
  releaseNotes?: string
  minCompatibleVersion?: string
}

// 创建策略参数
export interface CreatePolicyParams {
  versionId: string
  channel?: string
  updateStrategy: 'force' | 'optional' | 'silent'
  targetVersionMin?: string
  targetVersionMax?: string
  rolloutPercentage?: number
}

// 上传结果
export interface UploadResult {
  version: AppVersion
  uploadInfo: {
    key: string
    url: string
    size: number
  }
}

// 更新统计
export interface UpdateStats {
  totalUpdates: number
  successfulUpdates: number
  failedUpdates: number
  statusBreakdown: Record<string, number>
}

// 应用管理服务
export class AppManagementService {
  // 获取版本列表
  async getVersions(
    params: VersionListParams = {}
  ): Promise<ApiResponse<PaginatedResponse<AppVersion>>> {
    return await apiService.get<PaginatedResponse<AppVersion>>('/app-update/admin/versions', {
      params
    })
  }

  // 获取版本详情
  async getVersionDetail(versionId: string): Promise<ApiResponse<AppVersion>> {
    return await apiService.get<AppVersion>(`/app-update/admin/versions/${versionId}`)
  }

  // 上传新版本文件
  async uploadVersion(formData: FormData): Promise<ApiResponse<UploadResult>> {
    return await apiService.post<UploadResult>('/app-update/admin/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      timeout: 300000 // 5分钟超时，用于大文件上传
    })
  }

  // 创建版本记录
  async createVersion(
    data: CreateVersionParams & { fileUrl: string; fileSize?: number; fileHash?: string }
  ): Promise<ApiResponse<AppVersion>> {
    return await apiService.post<AppVersion>('/app-update/admin/versions', data)
  }

  // 删除版本
  async deleteVersion(versionId: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/app-update/admin/versions/${versionId}`)
  }

  // 获取更新策略列表
  async getPolicies(versionId?: string): Promise<ApiResponse<AppUpdatePolicy[]>> {
    const params = versionId ? { versionId } : {}
    return await apiService.get<AppUpdatePolicy[]>('/app-update/admin/policies', { params })
  }

  // 创建更新策略
  async createPolicy(data: CreatePolicyParams): Promise<ApiResponse<AppUpdatePolicy>> {
    return await apiService.post<AppUpdatePolicy>('/app-update/admin/policies', data)
  }

  // 更新策略
  async updatePolicy(
    policyId: string,
    data: Partial<CreatePolicyParams>
  ): Promise<ApiResponse<AppUpdatePolicy>> {
    return await apiService.put<AppUpdatePolicy>(`/app-update/admin/policies/${policyId}`, data)
  }

  // 删除策略
  async deletePolicy(policyId: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/app-update/admin/policies/${policyId}`)
  }

  // 激活/停用策略
  async togglePolicy(policyId: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.put<void>(`/app-update/admin/policies/${policyId}/status`, { isActive })
  }

  // 获取更新统计
  async getUpdateStats(
    params: {
      dateFrom?: string
      dateTo?: string
      updateType?: 'apk' | 'hotfix'
    } = {}
  ): Promise<ApiResponse<UpdateStats>> {
    return await apiService.get<UpdateStats>('/app-update/admin/stats', { params })
  }

  // 获取版本使用统计
  async getVersionUsage(versionId: string): Promise<
    ApiResponse<{
      totalDevices: number
      successfulUpdates: number
      failedUpdates: number
      updateLogs: Array<{
        deviceId: string
        status: string
        updatedAt: string
        errorMessage?: string
      }>
    }>
  > {
    return await apiService.get(`/app-update/admin/versions/${versionId}/usage`)
  }

  // 强制推送更新到指定设备
  async pushUpdateToDevice(deviceId: string, versionId: string): Promise<ApiResponse<void>> {
    return await apiService.post<void>('/app-update/admin/push-update', {
      deviceId,
      versionId
    })
  }

  // 批量强制推送更新
  async batchPushUpdate(
    deviceIds: string[],
    versionId: string
  ): Promise<
    ApiResponse<{
      success: number
      failed: number
      errors: Array<{ deviceId: string; error: string }>
    }>
  > {
    return await apiService.post('/app-update/admin/batch-push-update', {
      deviceIds,
      versionId
    })
  }

  // 回滚版本
  async rollbackVersion(versionId: string, targetVersionId: string): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/app-update/admin/versions/${versionId}/rollback`, {
      targetVersionId
    })
  }

  // 获取设备列表
  async getDevices(
    params: {
      page?: number
      pageSize?: number
      currentVersion?: string
      platform?: string
    } = {}
  ): Promise<
    ApiResponse<
      PaginatedResponse<{
        deviceId: string
        platform: string
        currentVersion: string
        lastCheckTime: string
        updateStatus: string
      }>
    >
  > {
    return await apiService.get('/app-update/admin/devices', { params })
  }

  // 测试更新配置
  async testUpdateConfig(config: {
    platform: string
    currentVersion: string
    channel: string
  }): Promise<
    ApiResponse<{
      hasApkUpdate: boolean
      hasHotfixUpdate: boolean
      apkUpdate?: AppVersion
      hotfixUpdate?: AppVersion
    }>
  > {
    return await apiService.post('/app-update/admin/test-config', config)
  }
}

export const appManagementService = new AppManagementService()

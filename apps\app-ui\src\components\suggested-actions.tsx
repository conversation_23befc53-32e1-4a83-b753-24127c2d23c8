'use client';

import { motion } from 'framer-motion';
import { Button } from './ui/button';
import { memo } from 'react';
import type { UseChatHelpers } from '@ai-sdk/react';
import { roles } from '@/lib/roles/system';
import { suggestedActions as SUGGESTED_ACTIONS } from '@/lib/roles/suggestedActions';
interface SuggestedActionsProps {
  chatId: string;
  role: string;
  append: UseChatHelpers['append'];
}

function PureSuggestedActions({ chatId, append, role }: SuggestedActionsProps) {
  const suggestedActions =
    SUGGESTED_ACTIONS[
      (role as keyof typeof SUGGESTED_ACTIONS) || roles[0].role
    ];

  if (!suggestedActions || suggestedActions.length === 0) {
    return null;
  }

  return (
    <div
      data-testid="suggested-actions"
      className="grid grid-cols-1 sm:grid-cols-2 gap-1.5 sm:gap-2 w-full max-h-[70vh] sm:max-h-none overflow-y-auto p-0.5"
    >
      {suggestedActions.map((suggestedAction, index) => (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
          transition={{ delay: 0.05 * index, duration: 0.2 }}
          key={`suggested-action-${suggestedAction.title}-${index}`}
          className="block"
        >
          <Button
            variant="ghost"
            onClick={async () => {
              window.history.replaceState(
                {},
                '',
                `/chat/${chatId}?role=${role}`,
              );

              append({
                role: 'user',
                content: suggestedAction.action,
              });
            }}
            className="flex flex-col text-left border border-border rounded-xl px-3 sm:px-4 py-2.5 sm:py-3.5 text-xs sm:text-sm flex-1 gap-1 w-full h-auto justify-start items-start hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/80 dark:border-accent/20 dark:text-foreground dark:hover:text-accent-foreground transition-colors"
          >
            <span className="font-medium break-words whitespace-normal w-full text-xs sm:text-sm">
              {suggestedAction.title}
            </span>
            <span className="text-muted-foreground break-words whitespace-normal w-full text-xs dark:text-muted-foreground/80">
              {suggestedAction.label}
            </span>
          </Button>
        </motion.div>
      ))}
    </div>
  );
}

export const SuggestedActions = memo(PureSuggestedActions, () => true);

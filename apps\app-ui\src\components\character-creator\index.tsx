import { useState, useEffect } from 'react'
import { useTranslation } from 'react-i18next'
import { <PERSON><PERSON>, Card, CardBody } from '@heroui/react'
import { addToast } from '@heroui/react'
import StepIndicator from './step-indicator'
import BasicInfo from './steps/basic-info'
import GenderAge from './steps/gender-age'
import Ethnicity from './steps/ethnicity'
import HairStyle from './steps/hair-style'
import BodyType from './steps/body-type'
import Personality from './steps/personality'
import ClothingVoice from './steps/clothing-voice'
import Preview from './steps/preview'

// 定义步骤数据
const getSteps = (t: any) => [
  { id: 1, title: t('steps.basic_info.title'), description: t('steps.basic_info.description') },
  { id: 2, title: t('steps.gender_age.title'), description: t('steps.gender_age.description') },
  { id: 3, title: t('steps.ethnicity.title'), description: t('steps.ethnicity.description') },
  { id: 4, title: t('steps.hair_style.title'), description: t('steps.hair_style.description') },
  { id: 5, title: t('steps.body_type.title'), description: t('steps.body_type.description') },
  { id: 6, title: t('steps.personality.title'), description: t('steps.personality.description') },
  {
    id: 7,
    title: t('steps.clothing_voice.title'),
    description: t('steps.clothing_voice.description')
  },
  { id: 8, title: t('steps.preview.title'), description: t('steps.preview.description') }
]

// 定义角色数据类型
export type CharacterData = {
  name: string
  gender: string
  relationship: string
  ethnicity: string
  age: string
  eyeColor: string
  hairStyle: string
  hairColor: string
  bodyType: string
  breastSize?: string
  buttSize?: string
  personality: string
  clothing: string
  voice: string
  voiceModelId?: string // 声音模型ID
}

export default function CharacterCreator() {
  const { t } = useTranslation('customRole')
  const [currentStep, setCurrentStep] = useState(1)
  const [highestStepVisited, setHighestStepVisited] = useState(1)
  const [characterData, setCharacterData] = useState<CharacterData>({
    name: '',
    gender: 'female', // 默认选择女性
    relationship: '',
    ethnicity: 'female-caucasian', // 默认选择
    age: 'teen', // 默认选择
    eyeColor: 'brown', // 默认选择
    hairStyle: '',
    hairColor: 'black', // 默认选择
    bodyType: 'slim', // 默认选择
    personality: 'caregiver', // 默认选择
    clothing: '',
    voice: '', // 需要用户主动选择
    voiceModelId: '' // 需要用户主动选择
  })

  // 处理数据更新
  const updateCharacterData = (data: Partial<CharacterData>) => {
    setCharacterData(prev => ({ ...prev, ...data }))
  }

  // 设置默认选项
  useEffect(() => {
    // 根据当前步骤设置默认值
    if (currentStep === 4 && !characterData.hairStyle) {
      // 根据性别设置默认发型
      const isMale = characterData.gender === 'male'
      updateCharacterData({
        hairStyle: isMale ? 'buzz-cut' : 'straight'
      })
    }

    if (currentStep === 5) {
      // 为女性设置胸部和臀部默认尺寸（如果未设置）
      const isMale = characterData.ethnicity?.startsWith('male-')
      if (!isMale && !characterData.breastSize) {
        updateCharacterData({
          breastSize: 'moderate'
        })
      }
      if (!isMale && !characterData.buttSize) {
        updateCharacterData({
          buttSize: 'medium'
        })
      }
    }

    if (currentStep === 7 && !characterData.clothing) {
      // 根据性别设置默认服装
      const isMale = characterData.ethnicity?.startsWith('male-')
      updateCharacterData({
        clothing: isMale ? 'casual' : 'bikini'
      })
    }

    // 当完成一个步骤时，记录最高访问步骤
    if (currentStep > highestStepVisited) {
      setHighestStepVisited(currentStep)
    }
  }, [currentStep, characterData.ethnicity, highestStepVisited])

  // 验证当前步骤是否已完成
  const validateCurrentStep = (): boolean => {
    switch (currentStep) {
      case 1:
        if (!characterData.name || !characterData.relationship) {
          addToast({
            title: t('validation.fill_name_relationship'),
            color: 'danger'
          })
          return false
        }
        return true
      case 2:
        if (!characterData.gender || !characterData.age) {
          addToast({
            title: t('validation.select_gender_age'),
            color: 'danger'
          })
          return false
        }
        return true
      case 3:
        if (!characterData.ethnicity || !characterData.eyeColor) {
          addToast({
            title: t('validation.select_ethnicity_eye'),
            color: 'danger'
          })
          return false
        }
        return true
      case 4:
        if (!characterData.hairStyle || !characterData.hairColor) {
          addToast({
            title: t('validation.select_hair_style_color'),
            color: 'danger'
          })
          return false
        }
        return true
      case 5:
        if (!characterData.bodyType) {
          addToast({
            title: t('validation.select_body_type'),
            color: 'danger'
          })
          return false
        }
        return true
      case 6:
        if (!characterData.personality) {
          addToast({
            title: t('validation.select_personality'),
            color: 'danger'
          })
          return false
        }
        return true
      case 7:
        if (!characterData.clothing || !characterData.voiceModelId) {
          addToast({
            title: t('validation.select_clothing_voice'),
            color: 'danger'
          })
          return false
        }
        return true
      default:
        return true
    }
  }

  const steps = getSteps(t)

  // 下一步
  const nextStep = () => {
    if (currentStep < steps.length) {
      // 验证当前步骤
      if (!validateCurrentStep()) {
        return
      }

      // 更新最高访问步骤
      if (currentStep + 1 > highestStepVisited) {
        setHighestStepVisited(currentStep + 1)
      }

      setCurrentStep(currentStep + 1)
      window.scrollTo(0, 0)
    }
  }

  // 上一步
  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      window.scrollTo(0, 0)
    }
  }

  // 处理步骤点击
  const handleStepClick = (step: number) => {
    // 只允许跳转到已经访问过的步骤
    if (step <= highestStepVisited) {
      setCurrentStep(step)
      window.scrollTo(0, 0)
    } else {
      // 提示用户需要一步一步完成
      addToast({
        title: t('validation.complete_current_step'),
        color: 'danger'
      })
    }
  }

  // 检查每个步骤是否已完成
  const isStepCompleted = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        return !!characterData.name && !!characterData.relationship
      case 2:
        return !!characterData.gender && !!characterData.age
      case 3:
        return !!characterData.ethnicity && !!characterData.eyeColor
      case 4:
        return !!characterData.hairStyle && !!characterData.hairColor
      case 5:
        return !!characterData.bodyType
      case 6:
        return !!characterData.personality
      case 7:
        return !!characterData.clothing && !!characterData.voiceModelId
      default:
        return false
    }
  }

  // 根据当前步骤渲染对应组件
  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return <BasicInfo data={characterData} onUpdate={updateCharacterData} />
      case 2:
        return <GenderAge data={characterData} onUpdate={updateCharacterData} />
      case 3:
        return <Ethnicity data={characterData} onUpdate={updateCharacterData} />
      case 4:
        return <HairStyle data={characterData} onUpdate={updateCharacterData} />
      case 5:
        return <BodyType data={characterData} onUpdate={updateCharacterData} />
      case 6:
        return <Personality data={characterData} onUpdate={updateCharacterData} />
      case 7:
        return <ClothingVoice data={characterData} onUpdate={updateCharacterData} />
      case 8:
        return <Preview data={characterData} />
      default:
        return null
    }
  }

  return (
    <div className="flex flex-col space-y-4">
      {/* 步骤指示器 */}
      <StepIndicator
        steps={steps}
        currentStep={currentStep}
        onStepClick={handleStepClick}
        highestStepVisited={highestStepVisited}
        completedSteps={steps.map(step => isStepCompleted(step.id))}
      />

      {/* 步骤内容 */}
      {renderStep()}

      {/* 导航按钮 */}
      <div className="flex justify-between">
        <Button
          variant="bordered"
          onPress={prevStep}
          isDisabled={currentStep === 1}
          size="sm"
          color="default"
        >
          {t('navigation.prev_step')}
        </Button>

        {currentStep < steps.length && (
          <Button
            onPress={nextStep}
            size="sm"
            color="primary"
            isDisabled={!isStepCompleted(currentStep)}
          >
            {t('navigation.next_step')}
            {!isStepCompleted(currentStep) && (
              <span className="ml-1 text-xs opacity-70">
                ({t('navigation.complete_selection')})
              </span>
            )}
          </Button>
        )}
      </div>
    </div>
  )
}

// ElevenLabs V3 逆向 API 独立 Cloudflare Worker 入口
import type { Env, TTSRequest, APIResponse } from './types'
import { ElevenLabsV3Service } from './service'

// CORS 头部
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  'Access-Control-Max-Age': '86400'
}

export default {
  async fetch(request: Request, env: Env, ctx: any): Promise<Response> {
    // 处理 CORS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: CORS_HEADERS
      })
    }

    // 初始化服务
    const service = new ElevenLabsV3Service(env)

    try {
      const url = new URL(request.url)
      const { pathname, searchParams } = url
      const method = request.method

      // 路由处理
      console.log(`${method} ${pathname}`)

      // 根路径 - API 信息
      if (pathname === '/' && method === 'GET') {
        return jsonResponse({
          success: true,
          data: {
            name: 'ElevenLabs V3 逆向 API Worker',
            version: '1.0.0',
            endpoints: {
              'POST /tts': '生成语音（Base64 返回）',
              'POST /tts/stream': '生成语音（流式返回）',
              'GET /health': '健康检查',
              'GET /voices': '获取声音列表'
            }
          },
          message: 'ElevenLabs V3 逆向 API 服务运行中'
        })
      }

      // 生成语音
      if (pathname === '/tts' && method === 'POST') {
        const body = (await request.json()) as TTSRequest

        // 验证必要参数
        if (!body.text) {
          return jsonResponse(
            {
              success: false,
              error: '缺少必要参数: text'
            },
            400
          )
        }

        if (body.text.length > 5000) {
          return jsonResponse(
            {
              success: false,
              error: '文本长度不能超过 5000 字符'
            },
            400
          )
        }

        const result = await service.generateTTS(body)
        return jsonResponse(result, result.success ? 200 : 500)
      }

      // 生成语音流（新增流式接口）
      if (pathname === '/tts/stream' && method === 'POST') {
        const body = (await request.json()) as TTSRequest

        // 验证必要参数
        if (!body.text) {
          return jsonResponse(
            {
              success: false,
              error: '缺少必要参数: text'
            },
            400
          )
        }

        if (body.text.length > 5000) {
          return jsonResponse(
            {
              success: false,
              error: '文本长度不能超过 5000 字符'
            },
            400
          )
        }

        try {
          // 直接返回流式响应
          const streamResponse = await service.generateTTSStream(body)

          // 添加 CORS 头部到流式响应
          const headers = new Headers(streamResponse.headers)
          Object.entries(CORS_HEADERS).forEach(([key, value]) => {
            headers.set(key, value)
          })

          return new Response(streamResponse.body, {
            status: streamResponse.status,
            headers
          })
        } catch (error) {
          console.error('流式音频生成失败:', error)
          return jsonResponse(
            {
              success: false,
              error: error instanceof Error ? error.message : '流式音频生成失败'
            },
            500
          )
        }
      }

      // 健康检查
      if (pathname === '/health' && method === 'GET') {
        const result = await service.healthCheck()
        return jsonResponse(result, result.success ? 200 : 500)
      }

      // 获取声音列表
      if (pathname === '/voices' && method === 'GET') {
        const result = await service.getAvailableVoices()
        return jsonResponse(result, result.success ? 200 : 500)
      }

      // 404 - 路由不匹配
      return jsonResponse(
        {
          success: false,
          error: '路由不存在',
          message: `${method} ${pathname} 未找到`
        },
        404
      )
    } catch (error) {
      console.error('Worker 处理请求时发生错误:', error)

      return jsonResponse(
        {
          success: false,
          error: error instanceof Error ? error.message : '内部服务器错误'
        },
        500
      )
    }
  }
}

/**
 * 创建 JSON 响应
 */
function jsonResponse(data: APIResponse<any>, status: number = 200): Response {
  return new Response(JSON.stringify(data, null, 2), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...CORS_HEADERS
    }
  })
}

/**
 * 创建错误响应
 */
function errorResponse(message: string, status: number = 500): Response {
  return jsonResponse(
    {
      success: false,
      error: message
    },
    status
  )
}

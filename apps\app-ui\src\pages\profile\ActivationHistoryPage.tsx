import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router'

// HeroUI 组件
import { 
  Card, 
  CardBody, 
  CardHeader,
  Button, 
  Chip,
  Divider,
  Spinner
} from '@heroui/react'

// Iconify 图标
import { Icon } from '@iconify/react'

// API 服务
import { apiService } from '@/api'
import type { ActivationCodeUsage } from '@/api/services/activation-code'

export default function ActivationHistoryPage() {
  const navigate = useNavigate()
  const [history, setHistory] = useState<ActivationCodeUsage[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [hasMore, setHasMore] = useState(true)
  const [offset, setOffset] = useState(0)
  const limit = 20

  // 加载激活历史
  const loadHistory = async (reset = false) => {
    try {
      const currentOffset = reset ? 0 : offset
      const response = await apiService.activationCode.getActivationHistory(limit, currentOffset)

      if (response.success) {
        const newHistory = response.data
        
        if (reset) {
          setHistory(newHistory)
        } else {
          setHistory(prev => [...prev, ...newHistory])
        }

        setHasMore(newHistory.length === limit)
        setOffset(currentOffset + newHistory.length)
      }
    } catch (error) {
      console.error('加载激活历史失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    loadHistory(true)
  }, [])

  // 加载更多
  const handleLoadMore = () => {
    if (!isLoading && hasMore) {
      setIsLoading(true)
      loadHistory(false)
    }
  }

  // 获取结果类型的显示文本和颜色
  const getResultTypeDisplay = (resultType: string) => {
    switch (resultType) {
      case 'membership_created':
        return { text: '会员激活', color: 'success' as const }
      case 'membership_extended':
        return { text: '会员延长', color: 'primary' as const }
      case 'membership_upgraded':
        return { text: '会员升级', color: 'secondary' as const }
      case 'points_added':
        return { text: '积分获得', color: 'warning' as const }
      default:
        return { text: '未知', color: 'default' as const }
    }
  }

  // 格式化日期
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background via-content1 to-content2 safe-area-top">
      {/* 顶部导航 */}
      <div className="flex items-center justify-between p-4 pt-12">
        <Button
          isIconOnly
          variant="light"
          onPress={() => navigate(-1)}
          className="text-foreground"
        >
          <Icon icon="lucide:arrow-left" className="w-5 h-5" />
        </Button>
        <h1 className="text-lg font-semibold text-foreground">激活历史</h1>
        <div className="w-10" />
      </div>

      {/* 主要内容 */}
      <div className="px-4 pb-20">
        {isLoading && history.length === 0 ? (
          // 初始加载状态
          <div className="flex items-center justify-center py-20">
            <div className="flex flex-col items-center gap-4">
              <Spinner size="lg" color="primary" />
              <p className="text-default-500">加载中...</p>
            </div>
          </div>
        ) : history.length === 0 ? (
          // 空状态
          <Card className="bg-content1/50 backdrop-blur-sm">
            <CardBody className="py-20">
              <div className="flex flex-col items-center gap-4">
                <div className="w-16 h-16 bg-default-100 rounded-full flex items-center justify-center">
                  <Icon icon="lucide:ticket" className="w-8 h-8 text-default-400" />
                </div>
                <div className="text-center">
                  <h3 className="text-lg font-medium text-foreground mb-2">暂无激活记录</h3>
                  <p className="text-default-500 text-sm">您还没有使用过激活码</p>
                </div>
                <Button
                  color="primary"
                  variant="flat"
                  onPress={() => navigate('/profile/activation-code')}
                  startContent={<Icon icon="lucide:plus" className="w-4 h-4" />}
                >
                  立即激活
                </Button>
              </div>
            </CardBody>
          </Card>
        ) : (
          // 历史记录列表
          <div className="space-y-4">
            {history.map((record, index) => {
              const resultDisplay = getResultTypeDisplay(record.resultType)
              
              return (
                <Card key={record.id} className="bg-content1/50 backdrop-blur-sm">
                  <CardBody className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                          <Icon 
                            icon={record.activationCode?.type === 'membership' ? 'lucide:crown' : 'lucide:coins'} 
                            className="w-5 h-5 text-primary" 
                          />
                        </div>
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <span className="font-medium text-foreground">
                              {record.activationCode?.code}
                            </span>
                            <Chip
                              size="sm"
                              color={resultDisplay.color}
                              variant="flat"
                            >
                              {resultDisplay.text}
                            </Chip>
                          </div>
                          <p className="text-sm text-default-500">
                            {formatDate(record.usedAt)}
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* 激活内容详情 */}
                    <div className="space-y-2">
                      {record.activationCode?.membershipPlan && (
                        <div className="flex items-center gap-2 text-sm">
                          <Icon icon="lucide:crown" className="w-4 h-4 text-warning" />
                          <span className="text-foreground">
                            {record.activationCode.membershipPlan.name}
                          </span>
                        </div>
                      )}

                      {record.activationCode?.pointsPackage && (
                        <div className="flex items-center gap-2 text-sm">
                          <Icon icon="lucide:coins" className="w-4 h-4 text-primary" />
                          <span className="text-foreground">
                            {record.activationCode.pointsPackage.name} - {record.activationCode.pointsPackage.points} 积分
                          </span>
                        </div>
                      )}

                      {record.activationCode?.description && (
                        <div className="flex items-start gap-2 text-sm">
                          <Icon icon="lucide:info" className="w-4 h-4 text-default-400 mt-0.5" />
                          <span className="text-default-600">
                            {record.activationCode.description}
                          </span>
                        </div>
                      )}

                      {/* 冲突处理信息 */}
                      {record.conflictResolution !== 'no_conflict' && (
                        <div className="flex items-center gap-2 text-sm">
                          <Icon icon="lucide:alert-circle" className="w-4 h-4 text-warning" />
                          <span className="text-warning-600">
                            {record.conflictResolution === 'replaced' && '替换了原有会员'}
                            {record.conflictResolution === 'upgraded' && '升级了会员等级'}
                            {record.conflictResolution === 'extended' && '延长了会员有效期'}
                          </span>
                        </div>
                      )}
                    </div>

                    {index < history.length - 1 && <Divider className="mt-4" />}
                  </CardBody>
                </Card>
              )
            })}

            {/* 加载更多按钮 */}
            {hasMore && (
              <div className="flex justify-center pt-4">
                <Button
                  variant="flat"
                  onPress={handleLoadMore}
                  isLoading={isLoading}
                  disabled={isLoading}
                >
                  {isLoading ? '加载中...' : '加载更多'}
                </Button>
              </div>
            )}

            {/* 没有更多数据提示 */}
            {!hasMore && history.length > 0 && (
              <div className="text-center py-4">
                <p className="text-default-400 text-sm">没有更多记录了</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

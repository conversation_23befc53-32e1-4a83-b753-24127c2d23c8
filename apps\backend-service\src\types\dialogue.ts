// 剧本类型定义
export interface ScenePic {
  name: string;
  pic: string;
}

export interface Intensity {
  [key: string]: number; // 键是功能名称，值是强度
}

export interface Dialogue {
  role: string;
  time: string;
  dialogue: string;
  pics?: ScenePic[];
  intensity: Intensity;
}

export interface Stage {
  stage: number;
  stageTitle: string;
  pics?: ScenePic[];
  dialogues: Dialogue[];
}

export interface Script {
  mp3: string;
  stages: Stage[];
}

// 剧本ID类型
export type ScriptId = '1' | '2' | '3';

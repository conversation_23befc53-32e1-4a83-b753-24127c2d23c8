/**
 * 写真集功能错误处理工具
 * 提供统一的错误处理和用户友好的错误信息
 */

// 错误类型定义
export enum PhotoAlbumErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  INSUFFICIENT_POINTS = 'INSUFFICIENT_POINTS',
  MEMBERSHIP_REQUIRED = 'MEMBERSHIP_REQUIRED',
  TEMPLATE_NOT_FOUND = 'TEMPLATE_NOT_FOUND',
  GENERATION_FAILED = 'GENERATION_FAILED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  SERVER_ERROR = 'SERVER_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

// 错误信息接口
export interface PhotoAlbumError {
  type: PhotoAlbumErrorType
  message: string
  userMessage: string
  code?: string
  details?: any
  retryable: boolean
  actionRequired?: 'login' | 'upgrade' | 'purchase_points' | 'contact_support'
}

// 错误映射表
const ERROR_MAPPING: Record<string, Partial<PhotoAlbumError>> = {
  // 网络相关错误
  'Failed to fetch': {
    type: PhotoAlbumErrorType.NETWORK_ERROR,
    userMessage: '网络连接失败，请检查网络后重试',
    retryable: true
  },
  'NetworkError': {
    type: PhotoAlbumErrorType.NETWORK_ERROR,
    userMessage: '网络连接异常，请稍后重试',
    retryable: true
  },
  
  // 认证相关错误
  'Unauthorized': {
    type: PhotoAlbumErrorType.AUTHENTICATION_ERROR,
    userMessage: '登录已过期，请重新登录',
    retryable: false,
    actionRequired: 'login'
  },
  '401': {
    type: PhotoAlbumErrorType.AUTHENTICATION_ERROR,
    userMessage: '请先登录后再使用此功能',
    retryable: false,
    actionRequired: 'login'
  },
  
  // 权限相关错误
  'MEMBERSHIP_REQUIRED': {
    type: PhotoAlbumErrorType.MEMBERSHIP_REQUIRED,
    userMessage: '此功能需要会员权限，请升级会员',
    retryable: false,
    actionRequired: 'upgrade'
  },
  'INSUFFICIENT_POINTS': {
    type: PhotoAlbumErrorType.INSUFFICIENT_POINTS,
    userMessage: '积分不足，请购买积分后重试',
    retryable: false,
    actionRequired: 'purchase_points'
  },
  
  // 模板相关错误
  '模板不存在': {
    type: PhotoAlbumErrorType.TEMPLATE_NOT_FOUND,
    userMessage: '所选模板不存在，请重新选择',
    retryable: false
  },
  'Template not found': {
    type: PhotoAlbumErrorType.TEMPLATE_NOT_FOUND,
    userMessage: '所选模板不存在，请重新选择',
    retryable: false
  },
  
  // 生成相关错误
  '生成失败': {
    type: PhotoAlbumErrorType.GENERATION_FAILED,
    userMessage: '写真生成失败，请重试',
    retryable: true
  },
  'Generation failed': {
    type: PhotoAlbumErrorType.GENERATION_FAILED,
    userMessage: '写真生成失败，请重试',
    retryable: true
  },
  
  // 配额相关错误
  'Quota exceeded': {
    type: PhotoAlbumErrorType.QUOTA_EXCEEDED,
    userMessage: '今日生成次数已达上限，请明天再试',
    retryable: false
  },
  
  // 服务器错误
  '500': {
    type: PhotoAlbumErrorType.SERVER_ERROR,
    userMessage: '服务器暂时不可用，请稍后重试',
    retryable: true
  },
  'Internal Server Error': {
    type: PhotoAlbumErrorType.SERVER_ERROR,
    userMessage: '服务器内部错误，请稍后重试',
    retryable: true
  }
}

/**
 * 错误处理器类
 */
export class PhotoAlbumErrorHandler {
  /**
   * 解析错误并返回标准化的错误对象
   */
  static parseError(error: any): PhotoAlbumError {
    let errorMessage = ''
    let errorCode = ''
    
    // 提取错误信息
    if (error instanceof Error) {
      errorMessage = error.message
    } else if (typeof error === 'string') {
      errorMessage = error
    } else if (error?.message) {
      errorMessage = error.message
    } else if (error?.error) {
      errorMessage = error.error
    } else {
      errorMessage = '未知错误'
    }
    
    // 提取错误代码
    if (error?.code) {
      errorCode = error.code
    } else if (error?.status) {
      errorCode = error.status.toString()
    }
    
    // 查找匹配的错误映射
    let mappedError: Partial<PhotoAlbumError> | undefined
    
    // 优先匹配错误代码
    if (errorCode && ERROR_MAPPING[errorCode]) {
      mappedError = ERROR_MAPPING[errorCode]
    } else {
      // 匹配错误信息
      for (const [key, value] of Object.entries(ERROR_MAPPING)) {
        if (errorMessage.includes(key)) {
          mappedError = value
          break
        }
      }
    }
    
    // 构建最终错误对象
    const finalError: PhotoAlbumError = {
      type: mappedError?.type || PhotoAlbumErrorType.UNKNOWN_ERROR,
      message: errorMessage,
      userMessage: mappedError?.userMessage || '操作失败，请重试',
      code: errorCode,
      details: error,
      retryable: mappedError?.retryable ?? true,
      actionRequired: mappedError?.actionRequired
    }
    
    return finalError
  }
  
  /**
   * 处理错误并返回用户友好的提示
   */
  static handleError(error: any): {
    error: PhotoAlbumError
    shouldRetry: boolean
    actionButton?: {
      text: string
      action: string
    }
  } {
    const parsedError = this.parseError(error)
    
    // 确定是否应该重试
    const shouldRetry = parsedError.retryable && !parsedError.actionRequired
    
    // 确定操作按钮
    let actionButton: { text: string; action: string } | undefined
    
    switch (parsedError.actionRequired) {
      case 'login':
        actionButton = { text: '去登录', action: 'login' }
        break
      case 'upgrade':
        actionButton = { text: '升级会员', action: 'upgrade' }
        break
      case 'purchase_points':
        actionButton = { text: '购买积分', action: 'purchase_points' }
        break
      case 'contact_support':
        actionButton = { text: '联系客服', action: 'contact_support' }
        break
    }
    
    return {
      error: parsedError,
      shouldRetry,
      actionButton
    }
  }
  
  /**
   * 记录错误日志
   */
  static logError(error: PhotoAlbumError, context?: any) {
    const logData = {
      type: error.type,
      message: error.message,
      code: error.code,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }
    
    // 发送到错误监控服务
    console.error('PhotoAlbum Error:', logData)
    
    // TODO: 集成实际的错误监控服务
    // 例如：Sentry, LogRocket, 或自定义错误收集服务
  }
  
  /**
   * 显示用户友好的错误提示
   */
  static showUserError(error: any, context?: any): PhotoAlbumError {
    const { error: parsedError, shouldRetry, actionButton } = this.handleError(error)
    
    // 记录错误
    this.logError(parsedError, context)
    
    // TODO: 集成 Toast 或 Modal 组件显示错误
    console.warn('User Error:', parsedError.userMessage)
    
    return parsedError
  }
}

/**
 * 便捷的错误处理函数
 */
export function handlePhotoAlbumError(error: any, context?: any) {
  return PhotoAlbumErrorHandler.showUserError(error, context)
}

/**
 * 重试装饰器
 * 为异步函数添加自动重试功能
 */
export function withRetry<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  maxRetries: number = 3,
  delay: number = 1000
): T {
  return (async (...args: any[]) => {
    let lastError: any
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn(...args)
      } catch (error) {
        lastError = error
        
        const parsedError = PhotoAlbumErrorHandler.parseError(error)
        
        // 如果错误不可重试，直接抛出
        if (!parsedError.retryable) {
          throw error
        }
        
        // 如果是最后一次尝试，抛出错误
        if (attempt === maxRetries) {
          throw error
        }
        
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, delay * attempt))
      }
    }
    
    throw lastError
  }) as T
}

/**
 * 网络请求错误处理装饰器
 */
export function withNetworkErrorHandling<T extends (...args: any[]) => Promise<Response>>(
  fetchFn: T
): T {
  return (async (...args: any[]) => {
    try {
      const response = await fetchFn(...args)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw {
          status: response.status,
          message: errorData.error || response.statusText,
          code: response.status.toString()
        }
      }
      
      return response
    } catch (error) {
      // 网络错误或其他异常
      if (error instanceof TypeError && error.message === 'Failed to fetch') {
        throw {
          type: 'NetworkError',
          message: 'Failed to fetch',
          code: 'NETWORK_ERROR'
        }
      }
      
      throw error
    }
  }) as T
}

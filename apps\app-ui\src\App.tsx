import { Outlet, useLocation } from 'react-router'
import { MobileViewportManager } from './components/mobile-viewport-manager'
import { useSwipeBack } from './hooks/use-swipe-back'
import { ExitConfirmationDialog } from './components/exit-confirmation-dialog'
import { PrivacyScreenStatus } from './components/privacy-screen-status'
import { useGlobalAudioStore } from './stores/global-audio-store'
import { useEffect } from 'react'

/**
 * 应用根组件
 * 简化为只包含Outlet，布局逻辑已移至专用布局组件
 * 使用抽离的组件处理移动端视口适配、状态栏适配和左滑返回功能
 * 角色状态管理已移至 AuthedLayout 中
 */
function App() {
  // 使用左滑返回钩子获取退出确认弹窗的状态和方法
  const { showExitConfirm, setShowExitConfirm, handleExitApp, closeExitConfirm } = useSwipeBack()

  // 页面退出清理
  const globalAudio = useGlobalAudioStore()
  const location = useLocation()

  useEffect(() => {
    const handleBeforeUnload = () => {
      console.log('🎵 beforeunload - 清理音频')
      globalAudio.cleanup()
    }

    const handleVisibilityChange = () => {
      if (document.hidden) {
        console.log('🎵 页面隐藏 - 停止音频')
        globalAudio.stopCurrent()
      }
    }

    const handlePageHide = () => {
      console.log('🎵 pagehide - 清理音频')
      globalAudio.cleanup()
    }

    // 添加多种事件监听
    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('pagehide', handlePageHide)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('pagehide', handlePageHide)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [])

  // 路由变化时清理音频
  useEffect(() => {
    console.log('🎵 路由变化 - 停止音频')
    globalAudio.stopCurrent()
  }, [location.pathname])

  return (
    <MobileViewportManager>
      <Outlet />

      {/* 退出确认弹窗 */}
      <ExitConfirmationDialog
        open={showExitConfirm}
        onOpenChange={setShowExitConfirm}
        onConfirm={handleExitApp}
        onCancel={closeExitConfirm}
      />

      {/* 隐私屏幕状态显示 */}
      <PrivacyScreenStatus />
    </MobileViewportManager>
  )
}

export default App

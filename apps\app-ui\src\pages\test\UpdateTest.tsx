import { useState } from 'react'
import { <PERSON><PERSON>, Card, CardBody, CardHeader } from '@heroui/react'
import CheckUpdate from '@/components/CheckUpdate'
import { useAppUpdate } from '@/hooks/use-app-update'
import { updateManager } from '@/services/update-manager'

export default function UpdateTest() {
  const [testResult, setTestResult] = useState<string>('')
  const [isLoading, setIsLoading] = useState(false)

  const {
    isChecking,
    updateInfo,
    updateProgress,
    error,
    checkForUpdates,
    performUpdate
  } = useAppUpdate({
    autoCheck: false,
    onUpdateAvailable: (info) => {
      setTestResult(`发现更新: ${JSON.stringify(info, null, 2)}`)
    },
    onUpdateError: (error) => {
      setTestResult(`更新错误: ${error.message}`)
    }
  })

  const handleTestCheck = async () => {
    setIsLoading(true)
    setTestResult('开始检查更新...')
    
    try {
      const result = await checkForUpdates(true)
      if (result) {
        setTestResult(`检查结果: ${JSON.stringify(result, null, 2)}`)
      } else {
        setTestResult('没有可用更新')
      }
    } catch (error) {
      setTestResult(`检查失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsLoading(false)
    }
  }

  const handleTestAPI = async () => {
    setIsLoading(true)
    setTestResult('测试 API 连接...')
    
    try {
      // 直接测试 updateManager
      await updateManager.initialize()
      const result = await updateManager.checkForUpdates({ force: true })
      setTestResult(`API 测试结果: ${JSON.stringify(result, null, 2)}`)
    } catch (error) {
      setTestResult(`API 测试失败: ${error instanceof Error ? error.message : '未知错误'}`)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <h1 className="text-3xl font-bold text-white text-center mb-8">
          更新功能测试页面
        </h1>

        {/* CheckUpdate 组件测试 */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">CheckUpdate 组件测试</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <p className="text-default-600">
                点击下面的组件来测试检查更新功能：
              </p>
              <CheckUpdate />
            </div>
          </CardBody>
        </Card>

        {/* 手动测试 */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">手动测试</h2>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex gap-4">
                <Button 
                  color="primary" 
                  onPress={handleTestCheck}
                  isLoading={isLoading}
                  isDisabled={isChecking}
                >
                  测试检查更新
                </Button>
                <Button 
                  color="secondary" 
                  onPress={handleTestAPI}
                  isLoading={isLoading}
                >
                  测试 API 连接
                </Button>
              </div>
              
              {/* 状态显示 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="p-3 bg-default-100 rounded-lg">
                  <div className="text-sm text-default-600">检查状态</div>
                  <div className="font-medium">
                    {isChecking ? '检查中...' : '空闲'}
                  </div>
                </div>
                <div className="p-3 bg-default-100 rounded-lg">
                  <div className="text-sm text-default-600">更新信息</div>
                  <div className="font-medium">
                    {updateInfo ? '有更新' : '无更新'}
                  </div>
                </div>
                <div className="p-3 bg-default-100 rounded-lg">
                  <div className="text-sm text-default-600">错误状态</div>
                  <div className="font-medium text-danger">
                    {error ? error.message : '无错误'}
                  </div>
                </div>
              </div>

              {/* 更新进度 */}
              {updateProgress && (
                <div className="p-4 bg-primary-50 rounded-lg">
                  <div className="text-sm text-primary-600 mb-2">更新进度</div>
                  <div className="font-medium">{updateProgress.message}</div>
                  <div className="text-sm text-default-500">
                    {updateProgress.progress}% - {updateProgress.status}
                  </div>
                </div>
              )}
            </div>
          </CardBody>
        </Card>

        {/* 测试结果 */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">测试结果</h2>
          </CardHeader>
          <CardBody>
            <pre className="bg-default-100 p-4 rounded-lg text-sm overflow-auto max-h-96">
              {testResult || '暂无测试结果'}
            </pre>
          </CardBody>
        </Card>

        {/* 应用信息 */}
        <Card>
          <CardHeader>
            <h2 className="text-xl font-semibold">应用信息</h2>
          </CardHeader>
          <CardBody>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <div className="text-sm text-default-600">当前版本</div>
                <div className="font-medium">0.0.1</div>
              </div>
              <div>
                <div className="text-sm text-default-600">版本代码</div>
                <div className="font-medium">1</div>
              </div>
              <div>
                <div className="text-sm text-default-600">平台</div>
                <div className="font-medium">Web (测试)</div>
              </div>
              <div>
                <div className="text-sm text-default-600">渠道</div>
                <div className="font-medium">production</div>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

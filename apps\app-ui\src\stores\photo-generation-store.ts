import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import {
  photoAlbumService,
  type PhotoTemplate,
  type PhotoHistory,
  type UserMembership
} from '@/api/services/photo-album'

// 重新导出类型以便其他文件使用
export type { PhotoTemplate, PhotoHistory, UserMembership }

// 生成状态类型
export type GenerationStatus = 'idle' | 'selecting' | 'generating' | 'completed' | 'failed'

// 生成任务类型
export interface GenerationTask {
  taskId: string
  templateId: string
  characterId: string
  characterName: string
  characterImageUrl: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  estimatedSteps?: number
  completedSteps?: number
  imageUrl?: string
  errorMessage?: string
  pointsConsumed?: number
  remainingPoints?: number
  generationId?: string
}

// Store 状态接口
interface PhotoGenerationState {
  // 当前选择的模板
  selectedTemplate: PhotoTemplate | null

  // 生成状态
  generationStatus: GenerationStatus

  // 当前生成任务
  currentTask: GenerationTask | null

  // 模板列表缓存
  templates: PhotoTemplate[]
  templatesLoading: boolean
  templatesError: string | null

  // 历史记录缓存
  history: PhotoHistory[]
  historyLoading: boolean
  historyError: string | null

  // 用户信息
  userMembership: {
    isMember: boolean
    canAccessPremium: boolean
  }

  // Actions
  setSelectedTemplate: (template: PhotoTemplate | null) => void
  setGenerationStatus: (status: GenerationStatus) => void
  setCurrentTask: (task: GenerationTask | null) => void
  updateTaskProgress: (progress: number, status?: GenerationTask['status']) => void

  // 模板相关操作
  fetchTemplates: (params?: { characterId?: string; gender?: 'male' | 'female' }) => Promise<void>
  clearTemplates: () => void

  // 历史记录相关操作
  fetchHistory: (characterId: string) => Promise<void>
  addHistoryItem: (item: PhotoHistory) => void
  updateHistoryItem: (id: string, updates: Partial<PhotoHistory>) => void
  clearHistory: () => void

  // 生成相关操作
  startGeneration: (
    templateId: string,
    characterId: string,
    characterName: string,
    characterImageUrl: string,
    characterInfo?: {
      gender?: 'male' | 'female'
      ethnicity?: string
      age?: string | number
      eyeColor?: string
      hairStyle?: string
      hairColor?: string
      bodyType?: string
      breastSize?: string
      buttSize?: string
    }
  ) => Promise<void>
  pollTaskStatus: (taskId: string) => Promise<void>
  stopPolling: () => void

  // 重置状态
  reset: () => void
}

// 轮询定时器
let pollTimer: NodeJS.Timeout | null = null

// 从缓存加载初始数据
const loadInitialData = () => {
  try {
    const cachedTemplates = localStorage.getItem('photo-templates-cache')
    const cacheTime = localStorage.getItem('photo-templates-cache-time')

    // 检查缓存是否有效（5分钟内）
    if (cachedTemplates && cacheTime && Date.now() - parseInt(cacheTime) < 5 * 60 * 1000) {
      return {
        templates: JSON.parse(cachedTemplates),
        templatesError: null
      }
    }
  } catch (e) {
    console.warn('加载缓存数据失败:', e)
  }

  return {
    templates: [],
    templatesError: null
  }
}

// 创建 store
export const usePhotoGenerationStore = create<PhotoGenerationState>()(
  devtools(
    (set, get) => {
      const initialData = loadInitialData()

      return {
        // 初始状态
        selectedTemplate: null,
        generationStatus: 'idle',
        currentTask: null,
        templates: initialData.templates,
        templatesLoading: false,
        templatesError: initialData.templatesError,
        history: [],
        historyLoading: false,
        historyError: null,
        userMembership: {
          isMember: false,
          canAccessPremium: false
        },

        // 基础设置操作
        setSelectedTemplate: template => {
          set({ selectedTemplate: template })
        },

        setGenerationStatus: status => {
          set({ generationStatus: status })
        },

        setCurrentTask: task => {
          set({ currentTask: task })
        },

        updateTaskProgress: (progress, status) => {
          set(state => ({
            currentTask: state.currentTask
              ? {
                  ...state.currentTask,
                  progress,
                  ...(status && { status })
                }
              : null
          }))
        },

        // 获取模板列表
        fetchTemplates: async (params?: { characterId?: string; gender?: 'male' | 'female' }) => {
          // 生成缓存键，包含性别信息
          const cacheKey = `photo-templates-cache-${params?.gender || 'all'}`
          const cacheTimeKey = `photo-templates-cache-time-${params?.gender || 'all'}`

          set({ templatesLoading: true, templatesError: null })

          try {
            console.log('🔄 正在获取模板列表...', params)
            const data = await photoAlbumService.getTemplates(params)
            console.log('✅ 获取到模板数据:', data.templates.length, '个模板')

            set({
              templates: data.templates,
              userMembership: data.userMembership,
              templatesLoading: false
            })

            // 缓存到 localStorage（按性别分别缓存）
            try {
              localStorage.setItem(cacheKey, JSON.stringify(data.templates))
              localStorage.setItem(cacheTimeKey, Date.now().toString())
            } catch (e) {
              console.warn('缓存模板数据失败:', e)
            }
          } catch (error) {
            console.error('获取模板列表失败:', error)

            // 尝试从缓存加载
            try {
              const cachedTemplates = localStorage.getItem('photo-templates-cache')
              if (cachedTemplates) {
                set({
                  templates: JSON.parse(cachedTemplates),
                  templatesLoading: false,
                  templatesError: '网络错误，显示缓存数据'
                })
                return
              }
            } catch (e) {
              console.warn('加载缓存数据失败:', e)
            }

            set({
              templates: [], // 确保在错误时也设置为空数组
              templatesError: error instanceof Error ? error.message : '获取模板列表失败',
              templatesLoading: false
            })
          }
        },

        clearTemplates: () => {
          set({ templates: [], templatesError: null })
        },

        // 获取历史记录
        fetchHistory: async (characterId: string) => {
          set({ historyLoading: true, historyError: null })

          try {
            console.log('🔄 正在获取历史记录...', characterId)
            const history = await photoAlbumService.getHistory(characterId)
            console.log(
              '✅ 获取到历史记录:',
              history,
              '类型:',
              typeof history,
              '是否为数组:',
              Array.isArray(history)
            )

            // 确保 history 是数组
            const historyArray = Array.isArray(history) ? history : []

            set({
              history: historyArray,
              historyLoading: false
            })
          } catch (error) {
            console.error('获取历史记录失败:', error)
            set({
              history: [], // 确保在错误时也设置为空数组
              historyError: error instanceof Error ? error.message : '获取历史记录失败',
              historyLoading: false
            })
          }
        },

        addHistoryItem: item => {
          set(state => ({
            history: [item, ...state.history]
          }))
        },

        updateHistoryItem: (id, updates) => {
          set(state => ({
            history: state.history.map(item => (item.id === id ? { ...item, ...updates } : item))
          }))
        },

        clearHistory: () => {
          set({ history: [], historyError: null })
        },

        // 开始生成
        startGeneration: async (
          templateId,
          characterId,
          characterName,
          characterImageUrl,
          characterInfo
        ) => {
          const { selectedTemplate } = get()
          if (!selectedTemplate) {
            throw new Error('未选择模板')
          }

          set({ generationStatus: 'generating' })

          try {
            const data = await photoAlbumService.startGeneration({
              templateId,
              characterId,
              characterName,
              characterImageUrl,
              waitForCompletion: false,
              ...characterInfo // 展开角色信息
            })

            console.log('🎯 [DEBUG] startGeneration 返回数据:', data)

            // 解包 API 响应中的 data 字段
            const responseData = (data as any).data || data

            const task: GenerationTask = {
              taskId: responseData.taskId,
              templateId,
              characterId,
              characterName,
              characterImageUrl,
              status: responseData.status,
              progress: 0, // 强制初始进度为 0
              estimatedSteps: responseData.estimatedSteps,
              pointsConsumed: responseData.pointsConsumed,
              remainingPoints: responseData.remainingPoints,
              generationId: responseData.generationId
            }

            console.log('🎯 [DEBUG] 创建的 task 对象:', task)
            console.log('🎯 [DEBUG] task.taskId:', task.taskId)

            set({ currentTask: task })

            // 开始轮询状态
            get().pollTaskStatus(task.taskId)
          } catch (error) {
            console.error('启动生成失败:', error)
            set({ generationStatus: 'failed' })
            throw error
          }
        },

        // 轮询任务状态
        pollTaskStatus: async taskId => {
          console.log('🔄 [DEBUG] 开始轮询任务状态, taskId:', taskId)

          const poll = async () => {
            try {
              console.log('🔄 [DEBUG] 正在查询任务状态, taskId:', taskId)
              const response = await photoAlbumService.getGenerationStatus(taskId)

              // 解包 API 响应中的 data 字段
              const data = (response as any).data || response
              console.log('🔄 [DEBUG] 解包后的状态数据:', data)

              set(state => ({
                currentTask: state.currentTask
                  ? {
                      ...state.currentTask,
                      status: data.status,
                      progress: data.progress || 0,
                      imageUrl: data.imageUrl,
                      errorMessage: data.errorMessage
                    }
                  : null
              }))

              if (data.status === 'completed') {
                set({ generationStatus: 'completed' })
                get().stopPolling()
              } else if (data.status === 'failed') {
                set({ generationStatus: 'failed' })
                get().stopPolling()
              } else {
                // 继续轮询
                pollTimer = setTimeout(poll, 3000)
              }
            } catch (error) {
              console.error('轮询状态失败:', error)
              // 继续轮询，避免因网络问题中断
              pollTimer = setTimeout(poll, 5000)
            }
          }

          // 立即执行一次
          poll()
        },

        stopPolling: () => {
          if (pollTimer) {
            clearTimeout(pollTimer)
            pollTimer = null
          }
        },

        // 重置状态
        reset: () => {
          get().stopPolling()
          set({
            selectedTemplate: null,
            generationStatus: 'idle',
            currentTask: null,
            templatesError: null,
            historyError: null
          })
        }
      }
    },
    {
      name: 'photo-generation-store'
    }
  )
)

// 导出便捷的 hooks
export const usePhotoTemplates = () => {
  const store = usePhotoGenerationStore()
  return {
    templates: store.templates,
    loading: store.templatesLoading,
    error: store.templatesError,
    userMembership: store.userMembership,
    fetchTemplates: store.fetchTemplates,
    clearTemplates: store.clearTemplates
  }
}

export const usePhotoHistory = () => {
  const store = usePhotoGenerationStore()
  return {
    history: store.history,
    loading: store.historyLoading,
    error: store.historyError,
    fetchHistory: store.fetchHistory,
    addHistoryItem: store.addHistoryItem,
    updateHistoryItem: store.updateHistoryItem,
    clearHistory: store.clearHistory
  }
}

export const usePhotoGeneration = () => {
  const store = usePhotoGenerationStore()
  return {
    selectedTemplate: store.selectedTemplate,
    generationStatus: store.generationStatus,
    currentTask: store.currentTask,
    setSelectedTemplate: store.setSelectedTemplate,
    setGenerationStatus: store.setGenerationStatus,
    setCurrentTask: store.setCurrentTask,
    updateTaskProgress: store.updateTaskProgress,
    startGeneration: store.startGeneration,
    pollTaskStatus: store.pollTaskStatus,
    stopPolling: store.stopPolling,
    reset: store.reset
  }
}

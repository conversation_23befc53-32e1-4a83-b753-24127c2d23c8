CREATE TABLE IF NOT EXISTS "MediaGeneration" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"character_id" uuid,
	"chat_id" uuid,
	"message_id" uuid,
	"media_type" varchar NOT NULL,
	"generation_type" varchar NOT NULL,
	"prompt" text,
	"negative_prompt" text,
	"input_image_url" text,
	"output_urls" json,
	"status" varchar DEFAULT 'pending' NOT NULL,
	"error_message" text,
	"points_used" integer DEFAULT 0 NOT NULL,
	"generation_time" integer,
	"completed_at" timestamp,
	"metadata" json,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
DROP TABLE "GenerationHistory";--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MediaGeneration" ADD CONSTRAINT "MediaGeneration_user_id_User_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MediaGeneration" ADD CONSTRAINT "MediaGeneration_character_id_Character_id_fk" FOREIGN KEY ("character_id") REFERENCES "public"."Character"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "MediaGeneration" ADD CONSTRAINT "MediaGeneration_chat_id_Chat_id_fk" FOREIGN KEY ("chat_id") REFERENCES "public"."Chat"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_user_id" ON "MediaGeneration" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_character_id" ON "MediaGeneration" USING btree ("character_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_chat_id" ON "MediaGeneration" USING btree ("chat_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_media_type" ON "MediaGeneration" USING btree ("media_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_generation_type" ON "MediaGeneration" USING btree ("generation_type");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_status" ON "MediaGeneration" USING btree ("status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_created_at" ON "MediaGeneration" USING btree ("created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_user_character" ON "MediaGeneration" USING btree ("user_id","character_id","created_at" DESC NULLS LAST);--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "idx_media_gen_user_media_type" ON "MediaGeneration" USING btree ("user_id","media_type","created_at" DESC NULLS LAST);
import React, { useState, useEffect } from 'react';
import { BleService } from './BleService';
import BleCommandButton from './BleCommandButton';
import type { Device } from '../types';

/**
 * 设备控制组件
 * 用于控制设备功能
 */
const DeviceControl: React.FC<{ device: Device }> = ({ device }) => {
  const [activeFunction, setActiveFunction] = useState<string | null>(
    device.func.length > 0 ? device.func[0].key : null,
  );
  const [isBluetoothReady, setIsBluetoothReady] = useState(false);

  // 获取蓝牙服务实例
  const bleService = BleService.getInstance();

  // 初始化蓝牙
  useEffect(() => {
    const initBluetooth = async () => {
      const initialized = await bleService.initialize();
      if (initialized) {
        const enabled = await bleService.isBluetoothEnabled();
        setIsBluetoothReady(enabled);
      }
    };

    initBluetooth();

    // 组件卸载时停止所有广播
    return () => {
      bleService.stopAllCommands();
    };
  }, []);

  // 获取当前选中的功能
  const currentFunction = device.func.find((f) => f.key === activeFunction);

  return (
    <div className="flex flex-col space-y-6">
      {/* 设备信息 */}
      <div className="flex items-center space-x-4">
        <img
          src={device.pic}
          alt={device.name}
          className="w-16 h-16 rounded-full object-cover"
        />
        <div>
          <h2 className="text-xl font-bold text-white">{device.name}</h2>
          <p className="text-gray-400 text-sm">
            {isBluetoothReady ? '蓝牙已连接' : '蓝牙未连接'}
          </p>
        </div>
      </div>

      {/* 功能选择 */}
      <div className="flex flex-wrap gap-2">
        {device.func.map((func) => (
          <button
            key={func.key}
            type="button"
            onClick={() => setActiveFunction(func.key)}
            className={`px-4 py-2 rounded-lg ${
              activeFunction === func.key
                ? 'bg-pink-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            {func.name}
          </button>
        ))}
      </div>

      {/* 强度控制 */}
      {currentFunction && (
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-white">
            {currentFunction.name}强度
          </h3>
          <div className="grid grid-cols-4 gap-2">
            {currentFunction.commands.map((cmd) => {
              // 强度为-1表示停止
              const isStop = cmd.intensity === -1;

              return (
                <BleCommandButton
                  key={cmd.intensity}
                  command={cmd.command}
                  label={isStop ? '停止' : `强度${cmd.intensity}`}
                  intensity={cmd.intensity}
                  className={isStop ? 'col-span-4 mt-2' : ''}
                />
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

export default DeviceControl;

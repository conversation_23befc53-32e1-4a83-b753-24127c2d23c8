// Cloudflare Workers 环境变量类型定义
export interface Env {
  // KV 存储 (通过 wrangler.toml 配置)
  CACHE: KVNamespace

  // R2 存储 (通过 wrangler.toml 配置)
  BUCKET: R2Bucket

  // Cloudflare Workers AI 绑定
  AI: Ai

  // Queue 绑定 - 用于后台任务处理
  AUDIO_PROCESSING_QUEUE: Queue
  IMAGE_GENERATION_QUEUE: Queue
  VIDEO_GENERATION_QUEUE: Queue
  PHOTO_ALBUM_GENERATION_QUEUE: Queue
  FACE_SWAP_QUEUE: Queue

  // 敏感环境变量 (通过 wrangler secret 设置)
  DATABASE_URL: string
  SUPABASE_URL: string
  SUPABASE_ANON_KEY: string
  SUPABASE_SERVICE_ROLE_KEY: string
  XAI_API_KEY: string
  YUNWU_API_KEY?: string
  INSA3D_API_TOKEN?: string
  INSA3D_API_ENDPOINT?: string
  INSA3D_BASE_API_TOKEN?: string
  INSA3D_BASE_API_ENDPOINT?: string
  REPLICATE_API_TOKEN?: string

  // Fish Audio TTS 配置
  FISH_AUDIO_API_KEY?: string

  // ElevenLabs TTS 配置
  ELEVENLABS_API_KEY?: string

  // R2 S3 API 配置 (用于直接使用 S3 SDK)
  CLOUDFLARE_ACCOUNT_ID?: string
  R2_ACCESS_KEY_ID?: string
  R2_SECRET_ACCESS_KEY?: string
  R2_BUCKET_NAME?: string
  R2_PUBLIC_BASE_URL?: string

  // Resend 邮件服务
  RESEND_API_KEY: string
  RESEND_FROM_EMAIL: string

  // 公开环境变量 (通过 wrangler.toml [vars] 设置)
  NODE_ENV: string
  WORKER_ENV: string
  R2_PUBLIC_URL: string

  PAY_CALLBACK_DOMAIN: string
  PAY_WEB_DOMAIN: string
}

import type { ChatSession } from './types'
import { ChatDatabaseError } from './types'

/**
 * 会话仓储类
 * 负责会话相关的数据库操作
 */
export class SessionRepository {
  private db: any

  constructor(database: any) {
    this.db = database
  }

  /**
   * 创建会话
   */
  async createSession(session: Omit<ChatSession, 'createdAt' | 'updatedAt'>): Promise<ChatSession> {
    const now = new Date().toISOString()
    const newSession: ChatSession = {
      ...session,
      createdAt: now,
      updatedAt: now
    }

    try {
      await this.db.run(
        `INSERT INTO chat_sessions (id, roleId, title, messageCount, createdAt, updatedAt, lastMessageAt)
         VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          newSession.id,
          newSession.roleId,
          newSession.title,
          newSession.messageCount,
          newSession.createdAt,
          newSession.updatedAt,
          newSession.lastMessageAt
        ]
      )

      console.log(`✅ [SessionRepo] 会话已创建: ${newSession.id}`)
      return newSession
    } catch (error) {
      throw new ChatDatabaseError(
        `创建会话失败: ${newSession.id}`,
        'CREATE_SESSION_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取会话
   */
  async getSession(id: string): Promise<ChatSession | null> {
    try {
      const result = await this.db.query('SELECT * FROM chat_sessions WHERE id = ?', [id])
      return result.values?.length > 0 ? result.values[0] : null
    } catch (error) {
      throw new ChatDatabaseError(`获取会话失败: ${id}`, 'GET_SESSION_FAILED', error as Error)
    }
  }

  /**
   * 获取所有会话
   */
  async getAllSessions(): Promise<ChatSession[]> {
    try {
      const result = await this.db.query('SELECT * FROM chat_sessions ORDER BY updatedAt DESC')
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError('获取所有会话失败', 'GET_ALL_SESSIONS_FAILED', error as Error)
    }
  }

  /**
   * 根据角色ID获取会话
   */
  async getSessionsByRole(roleId: string): Promise<ChatSession[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM chat_sessions WHERE roleId = ? ORDER BY updatedAt DESC',
        [roleId]
      )
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        `获取角色会话失败: ${roleId}`,
        'GET_SESSIONS_BY_ROLE_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取角色的最新聊天会话
   */
  async getLatestSessionByRole(roleId: string): Promise<ChatSession | null> {
    try {
      const result = await this.db.query(
        'SELECT * FROM chat_sessions WHERE roleId = ? ORDER BY lastMessageAt DESC, updatedAt DESC LIMIT 1',
        [roleId]
      )
      return result.values?.length > 0 ? result.values[0] : null
    } catch (error) {
      throw new ChatDatabaseError(
        `获取角色最新会话失败: ${roleId}`,
        'GET_LATEST_SESSION_BY_ROLE_FAILED',
        error as Error
      )
    }
  }

  /**
   * 更新会话
   */
  async updateSession(id: string, updates: Partial<ChatSession>): Promise<boolean> {
    try {
      const setClause = Object.keys(updates)
        .filter(key => key !== 'id')
        .map(key => `${key} = ?`)
        .join(', ')

      if (!setClause) return false

      const values = Object.entries(updates)
        .filter(([key]) => key !== 'id')
        .map(([_, value]) => value)

      values.push(new Date().toISOString()) // updatedAt
      values.push(id) // WHERE 条件

      const result = await this.db.run(
        `UPDATE chat_sessions SET ${setClause}, updatedAt = ? WHERE id = ?`,
        values
      )

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`✅ [SessionRepo] 会话已更新: ${id}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(`更新会话失败: ${id}`, 'UPDATE_SESSION_FAILED', error as Error)
    }
  }

  /**
   * 删除会话
   */
  async deleteSession(id: string): Promise<boolean> {
    try {
      const result = await this.db.run('DELETE FROM chat_sessions WHERE id = ?', [id])
      const success = result.changes?.changes > 0
      if (success) {
        console.log(`🗑️ [SessionRepo] 会话已删除: ${id}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(`删除会话失败: ${id}`, 'DELETE_SESSION_FAILED', error as Error)
    }
  }

  /**
   * 更新会话的最后消息时间和消息数量
   */
  async updateSessionMetrics(
    sessionId: string,
    incrementMessageCount: boolean = true
  ): Promise<boolean> {
    try {
      const now = new Date().toISOString()
      const updateSql = incrementMessageCount
        ? `UPDATE chat_sessions 
           SET lastMessageAt = ?, 
               messageCount = messageCount + 1, 
               updatedAt = ? 
           WHERE id = ?`
        : `UPDATE chat_sessions 
           SET lastMessageAt = ?, 
               updatedAt = ? 
           WHERE id = ?`

      const params = incrementMessageCount ? [now, now, sessionId] : [now, now, sessionId]

      const result = await this.db.run(updateSql, params)

      const success = result.changes?.changes > 0
      if (success) {
        console.log(`📊 [SessionRepo] 会话指标已更新: ${sessionId}`)
      }
      return success
    } catch (error) {
      throw new ChatDatabaseError(
        `更新会话指标失败: ${sessionId}`,
        'UPDATE_SESSION_METRICS_FAILED',
        error as Error
      )
    }
  }

  /**
   * 获取会话统计信息
   */
  async getSessionStats(): Promise<{
    totalSessions: number
    totalMessages: number
    averageMessagesPerSession: number
    mostActiveSession: { id: string; messageCount: number } | null
  }> {
    try {
      // 获取总会话数
      const sessionCountResult = await this.db.query('SELECT COUNT(*) as count FROM chat_sessions')
      const totalSessions = sessionCountResult.values?.[0]?.count || 0

      // 获取总消息数
      const messageCountResult = await this.db.query(
        'SELECT SUM(messageCount) as total FROM chat_sessions'
      )
      const totalMessages = messageCountResult.values?.[0]?.total || 0

      // 获取最活跃的会话
      const mostActiveResult = await this.db.query(
        'SELECT id, messageCount FROM chat_sessions ORDER BY messageCount DESC LIMIT 1'
      )
      const mostActiveSession = mostActiveResult.values?.[0] || null

      return {
        totalSessions,
        totalMessages,
        averageMessagesPerSession: totalSessions > 0 ? totalMessages / totalSessions : 0,
        mostActiveSession
      }
    } catch (error) {
      throw new ChatDatabaseError('获取会话统计失败', 'GET_SESSION_STATS_FAILED', error as Error)
    }
  }

  /**
   * 搜索会话
   */
  async searchSessions(keyword: string, limit: number = 50): Promise<ChatSession[]> {
    try {
      const result = await this.db.query(
        'SELECT * FROM chat_sessions WHERE title LIKE ? ORDER BY updatedAt DESC LIMIT ?',
        [`%${keyword}%`, limit]
      )
      return result.values || []
    } catch (error) {
      throw new ChatDatabaseError(
        `搜索会话失败: ${keyword}`,
        'SEARCH_SESSIONS_FAILED',
        error as Error
      )
    }
  }
}

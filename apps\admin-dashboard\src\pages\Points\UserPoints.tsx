import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  Select,
  InputNumber,
  message,
  Modal,
  Form,
  Typography,
  Avatar,
  Tooltip,
  Row,
  Col,
  Statistic,
  Tabs,
  Tag
} from 'antd'
import {
  SearchOutlined,
  ExportOutlined,
  UserOutlined,
  EyeOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined,
  HistoryOutlined,
  GiftOutlined,
  ToolOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UserPoints } from '@/types/api'
import type { UserPointsListParams } from '@/services/points'
import { pointsService } from '@/services/points'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { Title } = Typography

const UserPointsPage: React.FC = () => {
  const [userPoints, setUserPoints] = useState<UserPoints[]>([])
  const [transactions, setTransactions] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [transactionLoading, setTransactionLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [transactionTotal, setTransactionTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [adjustModalVisible, setAdjustModalVisible] = useState(false)
  const [adjustingUser, setAdjustingUser] = useState<UserPoints | null>(null)
  const [form] = Form.useForm()

  // 搜索条件
  const [searchParams, setSearchParams] = useState<UserPointsListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalPoints: 0,
    averagePoints: 0,
    todayTransactions: 0
  })

  useEffect(() => {
    loadUserPoints()
    loadStats()
  }, [currentPage, pageSize, searchParams])

  useEffect(() => {
    loadTransactions()
  }, [])

  const loadUserPoints = async () => {
    try {
      setLoading(true)

      const params: UserPointsListParams = {
        page: currentPage,
        pageSize,
        ...searchParams
      }

      const response = await pointsService.getUserPoints(params)

      if (response.success && response.data) {
        setUserPoints(response.data.data || [])
        setTotal(response.data.total || 0)
      } else {
        message.error(response.message || '获取用户积分列表失败')
      }
    } catch (error) {
      console.error('获取用户积分列表失败:', error)
      message.error('获取用户积分列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadTransactions = async () => {
    try {
      setTransactionLoading(true)

      const response = await pointsService.getTransactions({
        page: 1,
        pageSize: 50
      })

      if (response.success && response.data) {
        setTransactions(response.data.data || [])
        setTransactionTotal(response.data.total || 0)
      } else {
        message.error(response.message || '获取交易记录失败')
      }
    } catch (error) {
      console.error('获取交易记录失败:', error)
      message.error('获取交易记录失败')
    } finally {
      setTransactionLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await pointsService.getPointsStats()

      if (response.success && response.data) {
        setStats({
          totalUsers: response.data.totalUsers,
          totalPoints: response.data.totalPoints,
          averagePoints:
            response.data.totalUsers > 0
              ? Math.round(response.data.totalPoints / response.data.totalUsers)
              : 0,
          todayTransactions: response.data.todayTransactions
        })
      } else {
        console.error('获取统计数据失败:', response.message)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
      // 如果出错，尝试快速模式
      try {
        const quickResponse = await pointsService.getPointsStats(true)
        if (quickResponse.success && quickResponse.data) {
          setStats({
            totalUsers: quickResponse.data.totalUsers,
            totalPoints: quickResponse.data.totalPoints,
            averagePoints:
              quickResponse.data.totalUsers > 0
                ? Math.round(quickResponse.data.totalPoints / quickResponse.data.totalUsers)
                : 0,
            todayTransactions: quickResponse.data.todayTransactions
          })
        }
      } catch (quickError) {
        console.error('快速模式也失败:', quickError)
      }
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
    // loadUserPoints will be called automatically due to useEffect dependency
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
    // loadUserPoints will be called automatically due to useEffect dependency
  }

  const handleAdjustPoints = (user: UserPoints) => {
    setAdjustingUser(user)
    form.resetFields()
    setAdjustModalVisible(true)
  }

  const handleAdjustSubmit = async (values: {
    type: 'add' | 'deduct'
    points: number
    reason: string
  }) => {
    if (!adjustingUser) return

    try {
      const response = await pointsService.adjustUserPoints({
        userId: adjustingUser.userId,
        points: values.points,
        type: values.type,
        reason: values.reason
      })

      if (response.success) {
        message.success(`积分${values.type === 'add' ? '增加' : '扣除'}成功`)
        setAdjustModalVisible(false)
        loadUserPoints()
        loadTransactions()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('调整积分失败:', error)
      message.error('操作失败')
    }
  }

  const handleFixCycleDates = async () => {
    try {
      const response = await pointsService.fixCycleDates()

      if (response.success && response.data) {
        message.success(
          `成功修复 ${response.data.fixed} 条记录（共检查 ${response.data.total} 条）`
        )
        loadUserPoints() // 重新加载数据
      } else {
        message.error(response.message || '修复失败')
      }
    } catch (error) {
      console.error('修复周期信息失败:', error)
      message.error('修复失败')
    }
  }

  const handleViewDetail = (user: UserPoints) => {
    Modal.info({
      title: '用户积分详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <strong>用户ID:</strong> {user.userId}
            </div>
            <div>
              <strong>当前余额:</strong> {user.balance.toLocaleString()}积分
            </div>
            <div>
              <strong>周期开始:</strong>{' '}
              {user.cycleStartDate ? dayjs(user.cycleStartDate).format('YYYY-MM-DD') : '未设置'}
            </div>
            <div>
              <strong>周期结束:</strong>{' '}
              {user.cycleEndDate ? dayjs(user.cycleEndDate).format('YYYY-MM-DD') : '未设置'}
            </div>
            <div>
              <strong>账户创建:</strong> {dayjs(user.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </Space>
        </div>
      )
    })
  }

  const userPointsColumns: ColumnsType<UserPoints> = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <div style={{ fontWeight: 500 }}>
              {(record as any).userEmail ||
                `用户${record.userId ? record.userId.slice(-8) : '未知'}`}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>
              ID: {record.userId ? record.userId.slice(0, 8) + '...' : '未知'}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '积分余额',
      dataIndex: 'balance',
      render: balance => (
        <span
          style={{
            color: balance > 1000 ? '#52c41a' : balance > 500 ? '#faad14' : '#f50',
            fontWeight: 500
          }}
        >
          {balance.toLocaleString()}
        </span>
      )
    },
    {
      title: '周期时间',
      key: 'cycle',
      render: (_, record) => (
        <div>
          {record.cycleStartDate && record.cycleEndDate ? (
            <>
              <div>{dayjs(record.cycleStartDate).format('MM-DD')}</div>
              <div style={{ color: '#999', fontSize: '12px' }}>
                至 {dayjs(record.cycleEndDate).format('MM-DD')}
              </div>
            </>
          ) : (
            <Tag color="orange">未设置</Tag>
          )}
        </div>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)} />
          </Tooltip>
          <Tooltip title="调整积分">
            <Button
              type="link"
              icon={<PlusCircleOutlined />}
              onClick={() => handleAdjustPoints(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const transactionColumns: ColumnsType<any> = [
    {
      title: '用户',
      dataIndex: 'userId',
      render: (userId, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <span>{(record as any).userEmail || `用户${userId ? userId.slice(-8) : '未知'}`}</span>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      render: type => (
        <Tag color={type === 'add' ? 'green' : 'red'}>{type === 'add' ? '增加' : '扣除'}</Tag>
      )
    },
    {
      title: '积分数量',
      dataIndex: 'points',
      render: (points, record) => (
        <span
          style={{
            color: record.type === 'add' ? '#52c41a' : '#f50',
            fontWeight: 500
          }}
        >
          {record.type === 'add' ? '+' : '-'}
          {points.toLocaleString()}
        </span>
      )
    },
    {
      title: '原因',
      dataIndex: 'reason'
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('MM-DD HH:mm')
    }
  ]

  const tabItems = [
    {
      key: '1',
      label: '用户积分',
      children: (
        <div>
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Input
                placeholder="搜索用户ID"
                style={{ width: 200 }}
                value={searchParams.keyword}
                onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
                onPressEnter={handleSearch}
              />

              <InputNumber
                placeholder="最小余额"
                style={{ width: 120 }}
                value={searchParams.minBalance}
                onChange={value =>
                  setSearchParams({ ...searchParams, minBalance: value || undefined })
                }
              />

              <InputNumber
                placeholder="最大余额"
                style={{ width: 120 }}
                value={searchParams.maxBalance}
                onChange={value =>
                  setSearchParams({ ...searchParams, maxBalance: value || undefined })
                }
              />

              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>

              <Button onClick={handleReset}>重置</Button>

              <Button icon={<ExportOutlined />}>导出</Button>

              <Button
                icon={<ToolOutlined />}
                onClick={handleFixCycleDates}
                title="修复周期信息为空的用户积分记录"
              >
                修复周期信息
              </Button>
            </Space>
          </Card>

          <Card>
            <Table
              columns={userPointsColumns}
              dataSource={userPoints}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize,
                total,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size)
                },
                ...TABLE_CONFIG
              }}
            />
          </Card>
        </div>
      )
    },
    {
      key: '2',
      label: '交易记录',
      children: (
        <Card>
          <Table
            columns={transactionColumns}
            dataSource={transactions}
            rowKey="id"
            loading={transactionLoading}
            pagination={{
              total: transactionTotal,
              ...TABLE_CONFIG
            }}
          />
        </Card>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        用户积分管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={stats.totalUsers}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总积分数"
              value={stats.totalPoints}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#1890ff' }}
              formatter={value => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均积分"
              value={stats.averagePoints}
              valueStyle={{ color: '#722ed1' }}
              formatter={value => `${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日交易"
              value={stats.todayTransactions}
              prefix={<HistoryOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs items={tabItems} />

      {/* 调整积分模态框 */}
      <Modal
        title="调整用户积分"
        open={adjustModalVisible}
        onCancel={() => setAdjustModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form form={form} layout="vertical" onFinish={handleAdjustSubmit}>
          <Form.Item
            name="type"
            label="操作类型"
            rules={[{ required: true, message: '请选择操作类型' }]}
          >
            <Select placeholder="选择操作类型">
              <Select.Option value="add">
                <Space>
                  <PlusCircleOutlined style={{ color: '#52c41a' }} />
                  增加积分
                </Space>
              </Select.Option>
              <Select.Option value="deduct">
                <Space>
                  <MinusCircleOutlined style={{ color: '#f50' }} />
                  扣除积分
                </Space>
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="points"
            label="积分数量"
            rules={[{ required: true, message: '请输入积分数量' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              max={100000}
              placeholder="100"
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => Number(value!.replace(/\$\s?|(,*)/g, '')) as 1}
            />
          </Form.Item>

          <Form.Item
            name="reason"
            label="操作原因"
            rules={[{ required: true, message: '请输入操作原因' }]}
          >
            <Input.TextArea rows={3} placeholder="请详细说明调整积分的原因..." />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setAdjustModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确定调整
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default UserPointsPage

/**
 * 统一缓存清理工具
 * 用于在用户登出或切换账号时清理所有相关缓存
 */

// 导入所有需要清理的 stores
import { useMembershipCacheStore } from '@/stores/membership-cache-store'
import { useVoiceModelsStore } from '@/stores/voice-models-store'
import { useUserProfileStore } from '@/stores/user-profile-store'
import { useUserCharactersStore } from '@/stores/user-characters-store'
import { usePhotoGenerationStore } from '@/stores/photo-generation-store'
import { useGlobalAudioStore } from '@/stores/global-audio-store'

// 导入其他需要清理的服务
import { voicesService } from '@/api/services/voices'
import { resourceManager } from '@/services/resource-manager'
import { ResourceLoader } from '@/utils/networkUtils'

/**
 * 清理所有用户相关缓存
 * 在用户登出或切换账号时调用
 */
export async function clearAllUserCaches(): Promise<void> {
  console.log('🧹 [CacheCleaner] 开始清理所有用户缓存...')

  try {
    // 1. 清理 Zustand stores
    console.log('🗂️ [CacheCleaner] 清理 Zustand stores...')

    // 会员状态和积分缓存
    useMembershipCacheStore.getState().clearCache()

    // 声音模型缓存
    useVoiceModelsStore.getState().clearVoiceModels()

    // 用户资料缓存
    useUserProfileStore.getState().clearUserProfile()

    // 用户角色缓存
    useUserCharactersStore.getState().clearUserCharacters()

    // 照片生成缓存
    const photoStore = usePhotoGenerationStore.getState()
    if ('clearCache' in photoStore) {
      ;(photoStore as any).clearCache()
    }

    // 全局音频状态
    useGlobalAudioStore.getState().cleanup()

    // 2. 清理 API 服务缓存
    console.log('🌐 [CacheCleaner] 清理 API 服务缓存...')

    // 声音服务缓存
    voicesService.clearCache()

    // 资源加载器缓存
    ResourceLoader.clearCache()

    // 3. 清理资源管理器缓存
    console.log('🗃️ [CacheCleaner] 清理资源管理器缓存...')
    resourceManager.clearBlobUrlCache()

    // 4. 清理 localStorage 中的其他缓存
    console.log('🗄️ [CacheCleaner] 清理 localStorage 缓存...')
    clearLocalStorageCaches()

    // 5. 清理 sessionStorage
    console.log('📝 [CacheCleaner] 清理 sessionStorage...')
    clearSessionStorageCaches()

    console.log('✅ [CacheCleaner] 所有用户缓存清理完成')
  } catch (error) {
    console.error('❌ [CacheCleaner] 清理缓存时出错:', error)
    // 即使出错也要尽量清理基础缓存
    try {
      clearLocalStorageCaches()
      clearSessionStorageCaches()
    } catch (fallbackError) {
      console.error('❌ [CacheCleaner] 备用清理也失败:', fallbackError)
    }
  }
}

/**
 * 清理 localStorage 中的缓存项
 */
function clearLocalStorageCaches(): void {
  const cacheKeys = [
    // Zustand persist stores
    'chat-cache-storage',
    'background-cache-storage',
    'membership-cache-storage',
    'voice-models-storage',
    'user-profile-storage',
    'user-characters-storage',
    'photo-generation-storage',

    // 其他可能的缓存键
    'role-cache',
    'chat-history-cache',
    'audio-cache',
    'image-cache',
    'video-cache',
    'background-cache',

    // 旧版本或临时缓存键
    'app-cache',
    'user-cache',
    'temp-cache'
  ]

  cacheKeys.forEach(key => {
    try {
      localStorage.removeItem(key)
      console.log(`🗑️ [CacheCleaner] 已清理 localStorage: ${key}`)
    } catch (error) {
      console.warn(`⚠️ [CacheCleaner] 清理 localStorage ${key} 失败:`, error)
    }
  })
}

/**
 * 清理 sessionStorage 中的缓存项
 */
function clearSessionStorageCaches(): void {
  const sessionKeys = [
    'temp-role-data',
    'temp-chat-data',
    'temp-user-data',
    'navigation-state',
    'form-cache'
  ]

  sessionKeys.forEach(key => {
    try {
      sessionStorage.removeItem(key)
      console.log(`🗑️ [CacheCleaner] 已清理 sessionStorage: ${key}`)
    } catch (error) {
      console.warn(`⚠️ [CacheCleaner] 清理 sessionStorage ${key} 失败:`, error)
    }
  })
}

/**
 * 快速清理关键缓存（用于紧急情况）
 */
export function clearCriticalCaches(): void {
  console.log('🚨 [CacheCleaner] 快速清理关键缓存...')

  try {
    // 清理最重要的用户数据缓存
    useUserProfileStore.getState().clearUserProfile()
    useMembershipCacheStore.getState().clearCache()

    // 清理认证相关的 localStorage
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('token_expires_at')
    localStorage.removeItem('auth-storage')
    localStorage.removeItem('user-session')

    console.log('✅ [CacheCleaner] 关键缓存清理完成')
  } catch (error) {
    console.error('❌ [CacheCleaner] 关键缓存清理失败:', error)
  }
}

/**
 * 检查缓存清理状态
 */
export function getCacheCleanupStatus(): {
  totalStores: number
  clearedStores: number
  remainingCaches: string[]
} {
  const remainingCaches: string[] = []
  let clearedStores = 0
  const totalStores = 7 // 总共需要清理的 store 数量

  // 检查各个 store 是否已清理
  const membershipCache = useMembershipCacheStore.getState().cache
  if (!membershipCache.membershipStatus && !membershipCache.userPoints) clearedStores++
  else remainingCaches.push('membership-cache')

  // ... 可以继续检查其他 stores

  return {
    totalStores,
    clearedStores,
    remainingCaches
  }
}

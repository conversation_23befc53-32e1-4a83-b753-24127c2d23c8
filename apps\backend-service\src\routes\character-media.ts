import { Hono } from 'hono'
import { zValidator } from '@hono/zod-validator'
import { z } from 'zod'
import type { Env } from '@/types/env'
import { authMiddleware } from '@/middleware/auth'
import { getCachedDbUserId } from '@/lib/cache/cache-utils'
import { getUserMediaGenerations } from '@/lib/db/queries/media-generation'
import type { Context } from 'hono'
import type { SupportedLanguage } from '@/i18n/messages'

const app = new Hono<{
  Bindings: Env
  Variables: {
    language: SupportedLanguage
    t: (key: string, params?: Record<string, string | number>) => string
  }
}>()

// 查询参数验证
const getCharacterMediaSchema = z.object({
  characterId: z.string().min(1, 'character-media.character_id_required'),
  mediaType: z.enum(['image', 'video', 'audio']).optional(),
  generationType: z.enum(['multimodal_chat', 'standalone', 'template_based']).optional(),
  status: z.enum(['pending', 'processing', 'completed', 'failed', 'cancelled']).optional(),
  limit: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 20)),
  offset: z
    .string()
    .optional()
    .transform(val => (val ? Number.parseInt(val) : 0))
})

/**
 * GET /api/character-media/:characterId
 * 获取角色的媒体生成记录
 */
app.get(
  '/:characterId',
  authMiddleware,
  zValidator('query', getCharacterMediaSchema.omit({ characterId: true })),
  async c => {
    const t = c.get('t')

    try {
      const characterId = c.req.param('characterId')
      const { mediaType, generationType, status, limit, offset } = c.req.valid('query')

      // 获取用户信息
      const supabaseUser = c.get('user')
      if (!supabaseUser) {
        return c.json({ success: false, message: t('character-media.user_not_found') }, 401)
      }

      // 获取数据库用户ID
      const dbUserId = await getCachedDbUserId(c.env, supabaseUser.id)
      if (!dbUserId) {
        return c.json({ success: false, message: t('character-media.user_not_exist') }, 404)
      }

      console.log('🔍 [CHARACTER-MEDIA] 查询角色媒体记录:', {
        characterId,
        userId: dbUserId,
        mediaType,
        generationType,
        status,
        limit,
        offset
      })

      // 查询媒体生成记录
      const mediaGenerations = await getUserMediaGenerations(c.env, dbUserId, {
        characterId,
        mediaType,
        generationType,
        status: status || 'completed', // 默认只返回已完成的记录
        limit,
        offset
      })

      // 转换为前端需要的格式
      const formattedData = mediaGenerations.map(mg => ({
        id: mg.id,
        characterId: mg.characterId,
        chatId: mg.chatId,
        messageId: mg.messageId,
        mediaType: mg.mediaType,
        generationType: mg.generationType,
        prompt: mg.prompt,
        negativePrompt: mg.negativePrompt,
        inputImageUrl: mg.inputImageUrl,
        outputUrls: mg.outputUrls || [],
        status: mg.status,
        errorMessage: mg.errorMessage,
        pointsUsed: mg.pointsUsed,
        generationTime: mg.generationTime,
        completedAt: mg.completedAt,
        createdAt: mg.createdAt,
        updatedAt: mg.updatedAt,
        metadata: mg.metadata
      }))

      return c.json({
        success: true,
        data: {
          mediaGenerations: formattedData,
          total: formattedData.length,
          hasMore: formattedData.length === limit
        }
      })
    } catch (error) {
      console.error('❌ [CHARACTER-MEDIA] 查询角色媒体记录失败:', error)
      return c.json(
        {
          success: false,
          message: t('character-media.query_media_failed'),
          error: error instanceof Error ? error.message : String(error)
        },
        500
      )
    }
  }
)

/**
 * GET /api/character-media/:characterId/images
 * 获取角色的图片生成记录
 */
app.get('/:characterId/images', authMiddleware, async c => {
  const t = c.get('t')

  try {
    const characterId = c.req.param('characterId')
    const limit = Number.parseInt(c.req.query('limit') || '20')
    const offset = Number.parseInt(c.req.query('offset') || '0')

    // 获取用户信息
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return c.json({ success: false, message: t('character-media.user_not_found') }, 401)
    }

    // 获取数据库用户ID
    const dbUserId = await getCachedDbUserId(c.env, supabaseUser.id)
    if (!dbUserId) {
      return c.json({ success: false, message: t('character-media.user_not_exist') }, 404)
    }

    // 查询图片生成记录
    const mediaGenerations = await getUserMediaGenerations(c.env, dbUserId, {
      characterId,
      mediaType: 'image',
      status: 'completed', // 只返回已完成的
      limit,
      offset
    })

    // 提取图片URL
    const images = mediaGenerations
      .filter(mg => mg.outputUrls && Array.isArray(mg.outputUrls) && mg.outputUrls.length > 0)
      .flatMap(mg =>
        (mg.outputUrls as string[]).map(url => ({
          id: mg.id,
          url,
          prompt: mg.prompt,
          createdAt: mg.createdAt,
          generationType: mg.generationType,
          metadata: mg.metadata
        }))
      )

    return c.json({
      success: true,
      data: {
        images,
        total: images.length,
        hasMore: mediaGenerations.length === limit
      }
    })
  } catch (error) {
    console.error('❌ [CHARACTER-MEDIA] 查询角色图片失败:', error)
    return c.json(
      {
        success: false,
        message: t('character-media.query_images_failed'),
        error: error instanceof Error ? error.message : String(error)
      },
      500
    )
  }
})

/**
 * GET /api/character-media/:characterId/videos
 * 获取角色的视频生成记录
 */
app.get('/:characterId/videos', authMiddleware, async c => {
  const t = c.get('t')

  try {
    const characterId = c.req.param('characterId')
    const limit = Number.parseInt(c.req.query('limit') || '20')
    const offset = Number.parseInt(c.req.query('offset') || '0')

    // 获取用户信息
    const supabaseUser = c.get('user')
    if (!supabaseUser) {
      return c.json({ success: false, message: t('character-media.user_not_found') }, 401)
    }

    // 获取数据库用户ID
    const dbUserId = await getCachedDbUserId(c.env, supabaseUser.id)
    if (!dbUserId) {
      return c.json({ success: false, message: t('character-media.user_not_exist') }, 404)
    }

    // 查询视频生成记录
    const mediaGenerations = await getUserMediaGenerations(c.env, dbUserId, {
      characterId,
      mediaType: 'video',
      status: 'completed', // 只返回已完成的
      limit,
      offset
    })

    // 提取视频URL
    const videos = mediaGenerations
      .filter(mg => mg.outputUrls && Array.isArray(mg.outputUrls) && mg.outputUrls.length > 0)
      .flatMap(mg =>
        (mg.outputUrls as string[]).map(url => ({
          id: mg.id,
          url,
          prompt: mg.prompt,
          createdAt: mg.createdAt,
          generationType: mg.generationType,
          metadata: mg.metadata
        }))
      )

    return c.json({
      success: true,
      data: {
        videos,
        total: videos.length,
        hasMore: mediaGenerations.length === limit
      }
    })
  } catch (error) {
    console.error('❌ [CHARACTER-MEDIA] 查询角色视频失败:', error)
    return c.json(
      {
        success: false,
        message: t('character-media.query_videos_failed'),
        error: error instanceof Error ? error.message : String(error)
      },
      500
    )
  }
})

export default app

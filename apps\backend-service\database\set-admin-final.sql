-- 为 <EMAIL> 设置管理员权限
-- 在 Supabase SQL Editor 中执行

-- 1. 设置管理员权限（保留现有的 email_verified）
UPDATE auth.users 
SET raw_user_meta_data = raw_user_meta_data || '{"role": "admin", "isAdmin": true}'::jsonb
WHERE email = '<EMAIL>';

-- 2. 验证设置结果
SELECT 
    id,
    email,
    raw_user_meta_data,
    raw_user_meta_data->>'role' as role,
    (raw_user_meta_data->>'isAdmin')::boolean as is_admin,
    (raw_user_meta_data->>'email_verified')::boolean as email_verified
FROM auth.users 
WHERE email = '<EMAIL>';

-- 3. 查看所有管理员（确认设置成功）
SELECT 
    email,
    raw_user_meta_data->>'role' as role,
    created_at
FROM auth.users 
WHERE raw_user_meta_data->>'role' = 'admin';
import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  Select,
  DatePicker,
  message,
  Modal,
  Form,
  InputNumber,
  Typography,
  Avatar,
  Tooltip,
  Row,
  Col,
  Statistic,
  Tabs,
  Tag
} from 'antd'
import {
  SearchOutlined,
  ExportOutlined,
  UserOutlined,
  EyeOutlined,
  PlusCircleOutlined,
  MinusCircleOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { CommissionAccount } from '@/types/api'
import type { CommissionListParams } from '@/services/marketing'
import { marketingService } from '@/services/marketing'
import { TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { Title } = Typography

const CommissionManagement: React.FC = () => {
  const [accounts, setAccounts] = useState<CommissionAccount[]>([])
  const [records, setRecords] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [recordLoading, setRecordLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [recordTotal, setRecordTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [adjustModalVisible, setAdjustModalVisible] = useState(false)
  const [adjustingAccount, setAdjustingAccount] = useState<CommissionAccount | null>(null)
  const [form] = Form.useForm()

  // 搜索条件
  const [searchParams, setSearchParams] = useState<CommissionListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState({
    totalAccounts: 0,
    totalBalance: 0,
    totalEarned: 0,
    totalWithdrawn: 0,
    monthlyCommission: 0
  })

  useEffect(() => {
    loadAccounts()
    loadRecords()
    loadStats()
  }, [currentPage, pageSize, searchParams])

  const loadAccounts = async () => {
    try {
      setLoading(true)

      const params = {
        page: currentPage,
        pageSize,
        ...searchParams
      }

      const response = await marketingService.getCommissionAccounts(params)

      if (response.success && response.data) {
        setAccounts(response.data.data)
        setTotal(response.data.total)
      } else {
        message.error(response.message || '获取佣金账户列表失败')
      }
    } catch (error) {
      console.error('获取佣金账户列表失败:', error)
      message.error('获取佣金账户列表失败')
    } finally {
      setLoading(false)
    }
  }

  const loadRecords = async () => {
    try {
      setRecordLoading(true)

      const response = await marketingService.getCommissionRecords({
        page: 1,
        pageSize: 20
      })

      if (response.success && response.data) {
        setRecords(response.data.data)
        setRecordTotal(response.data.total)
      } else {
        message.error(response.message || '获取佣金记录失败')
      }
    } catch (error) {
      console.error('获取佣金记录失败:', error)
      message.error('获取佣金记录失败')
    } finally {
      setRecordLoading(false)
    }
  }

  const loadStats = async () => {
    try {
      const response = await marketingService.getCommissionStats()

      if (response.success && response.data) {
        setStats(response.data)
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  const handleSearch = () => {
    setCurrentPage(1)
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
  }

  const handleAdjustCommission = (account: CommissionAccount) => {
    setAdjustingAccount(account)
    form.resetFields()
    setAdjustModalVisible(true)
  }

  const handleAdjustSubmit = async (values: {
    type: 'add' | 'deduct'
    amount: number
    reason: string
  }) => {
    if (!adjustingAccount) return

    try {
      const response = await marketingService.adjustCommission({
        userId: adjustingAccount.userId,
        amount: values.amount,
        type: values.type,
        reason: values.reason
      })

      if (response.success) {
        message.success(`佣金${values.type === 'add' ? '增加' : '扣除'}成功`)
        setAdjustModalVisible(false)
        loadAccounts()
        loadRecords()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('调整佣金失败:', error)
      message.error('操作失败')
    }
  }

  const handleViewDetail = (account: CommissionAccount) => {
    Modal.info({
      title: '佣金账户详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <strong>用户ID:</strong> {account.userId}
            </div>
            <div>
              <strong>当前余额:</strong> ¥{account.balance.toFixed(2)}
            </div>
            <div>
              <strong>累计收入:</strong> ¥{account.totalEarned.toFixed(2)}
            </div>
            <div>
              <strong>累计提现:</strong> ¥{account.totalWithdrawn.toFixed(2)}
            </div>
            <div>
              <strong>收益率:</strong>{' '}
              {account.totalEarned > 0
                ? ((account.totalWithdrawn / account.totalEarned) * 100).toFixed(1) + '%'
                : '0%'}
            </div>
            <div>
              <strong>账户创建:</strong> {dayjs(account.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </Space>
        </div>
      )
    })
  }

  const accountColumns: ColumnsType<CommissionAccount> = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <div style={{ fontWeight: 500 }}>
              用户{record.userId ? record.userId.slice(-3) : '未知'}
            </div>
            <div style={{ color: '#999', fontSize: '12px' }}>ID: {record.userId}</div>
          </div>
        </Space>
      )
    },
    {
      title: '当前余额',
      dataIndex: 'balance',
      render: balance => (
        <span
          style={{
            color: balance > 1000 ? '#52c41a' : balance > 500 ? '#faad14' : '#666',
            fontWeight: 500
          }}
        >
          ¥{balance.toFixed(2)}
        </span>
      ),
      sorter: (a, b) => a.balance - b.balance
    },
    {
      title: '累计收入',
      dataIndex: 'totalEarned',
      render: totalEarned => <span style={{ color: '#1890ff' }}>¥{totalEarned.toFixed(2)}</span>,
      sorter: (a, b) => a.totalEarned - b.totalEarned
    },
    {
      title: '累计提现',
      dataIndex: 'totalWithdrawn',
      render: totalWithdrawn => (
        <span style={{ color: '#722ed1' }}>¥{totalWithdrawn.toFixed(2)}</span>
      ),
      sorter: (a, b) => a.totalWithdrawn - b.totalWithdrawn
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)} />
          </Tooltip>
          <Tooltip title="调整佣金">
            <Button
              type="link"
              icon={<PlusCircleOutlined />}
              onClick={() => handleAdjustCommission(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  const recordColumns: ColumnsType<any> = [
    {
      title: '用户',
      dataIndex: 'userId',
      render: userId => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <span>用户{userId.slice(-3)}</span>
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'type',
      render: type => {
        const typeMap = {
          invite_membership: { label: '会员邀请', color: 'gold' },
          invite_points: { label: '积分邀请', color: 'blue' },
          adjustment: { label: '手动调整', color: 'purple' }
        }
        const config = typeMap[type as keyof typeof typeMap] || { label: type, color: 'default' }
        return <Tag color={config.color}>{config.label}</Tag>
      }
    },
    {
      title: '佣金金额',
      dataIndex: 'amount',
      render: amount => (
        <span style={{ color: '#52c41a', fontWeight: 500 }}>+¥{amount.toFixed(2)}</span>
      )
    },
    {
      title: '订单金额',
      dataIndex: 'orderAmount',
      render: orderAmount => (orderAmount ? `¥${orderAmount.toFixed(2)}` : '-')
    },
    {
      title: '佣金率',
      dataIndex: 'rate',
      render: rate => (rate > 0 ? `${rate}%` : '-')
    },
    {
      title: '描述',
      dataIndex: 'description'
    },
    {
      title: '时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('MM-DD HH:mm')
    }
  ]

  const tabItems = [
    {
      key: '1',
      label: '佣金账户',
      children: (
        <div>
          <Card style={{ marginBottom: 16 }}>
            <Space wrap>
              <Input
                placeholder="搜索用户ID"
                style={{ width: 200 }}
                value={searchParams.keyword}
                onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
                onPressEnter={handleSearch}
              />

              <RangePicker
                placeholder={['开始日期', '结束日期']}
                onChange={dates => {
                  if (dates) {
                    setSearchParams({
                      ...searchParams,
                      startDate: dates[0]?.toISOString(),
                      endDate: dates[1]?.toISOString()
                    })
                  } else {
                    setSearchParams({
                      ...searchParams,
                      startDate: undefined,
                      endDate: undefined
                    })
                  }
                }}
              />

              <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                搜索
              </Button>

              <Button onClick={handleReset}>重置</Button>

              <Button icon={<ExportOutlined />}>导出</Button>
            </Space>
          </Card>

          <Card>
            <Table
              columns={accountColumns}
              dataSource={accounts}
              rowKey="id"
              loading={loading}
              pagination={{
                current: currentPage,
                pageSize,
                total,
                onChange: (page, size) => {
                  setCurrentPage(page)
                  setPageSize(size)
                },
                ...TABLE_CONFIG
              }}
            />
          </Card>
        </div>
      )
    },
    {
      key: '2',
      label: '佣金记录',
      children: (
        <Card>
          <Table
            columns={recordColumns}
            dataSource={records}
            rowKey="id"
            loading={recordLoading}
            pagination={{
              total: recordTotal,
              ...TABLE_CONFIG
            }}
          />
        </Card>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        佣金管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={5}>
          <Card>
            <Statistic
              title="佣金账户数"
              value={stats.totalAccounts}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="总余额"
              value={stats.totalBalance}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="累计收入"
              value={stats.totalEarned}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={5}>
          <Card>
            <Statistic
              title="累计提现"
              value={stats.totalWithdrawn}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="本月佣金"
              value={stats.monthlyCommission}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs items={tabItems} />

      {/* 调整佣金模态框 */}
      <Modal
        title="调整用户佣金"
        open={adjustModalVisible}
        onCancel={() => setAdjustModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form form={form} layout="vertical" onFinish={handleAdjustSubmit}>
          <Form.Item
            name="type"
            label="操作类型"
            rules={[{ required: true, message: '请选择操作类型' }]}
          >
            <Select placeholder="选择操作类型">
              <Select.Option value="add">
                <Space>
                  <PlusCircleOutlined style={{ color: '#52c41a' }} />
                  增加佣金
                </Space>
              </Select.Option>
              <Select.Option value="deduct">
                <Space>
                  <MinusCircleOutlined style={{ color: '#f50' }} />
                  扣除佣金
                </Space>
              </Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="amount"
            label="调整金额"
            rules={[{ required: true, message: '请输入调整金额' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0.01}
              max={100000}
              precision={2}
              placeholder="100.00"
              addonBefore="¥"
            />
          </Form.Item>

          <Form.Item
            name="reason"
            label="调整原因"
            rules={[{ required: true, message: '请输入调整原因' }]}
          >
            <Input.TextArea rows={3} placeholder="请详细说明调整佣金的原因..." />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setAdjustModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确定调整
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default CommissionManagement

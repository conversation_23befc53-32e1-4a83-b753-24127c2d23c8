import React, { useState, useEffect } from 'react'
import {
  Table,
  Card,
  Input,
  Button,
  Space,
  Tag,
  Select,
  DatePicker,
  message,
  Typography,
  Avatar,
  Tooltip,
  Modal,
  Form,
  InputNumber,
  Row,
  Col,
  Statistic
} from 'antd'
import {
  SearchOutlined,
  ExportOutlined,
  UserOutlined,
  EyeOutlined,
  StopOutlined,
  Clock<PERSON>ircleOutlined,
  CrownOutlined
} from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import type { UserSubscription } from '@/types/api'
import type { SubscriptionListParams } from '@/services/membership'
import { membershipService } from '@/services/membership'
import { SUBSCRIPTION_STATUS_OPTIONS, TABLE_CONFIG, DEFAULT_PAGE_SIZE } from '@/constants'
import dayjs from 'dayjs'

const { RangePicker } = DatePicker
const { Title } = Typography

const MembershipSubscriptions: React.FC = () => {
  const [subscriptions, setSubscriptions] = useState<UserSubscription[]>([])
  const [loading, setLoading] = useState(false)
  const [total, setTotal] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(DEFAULT_PAGE_SIZE)
  const [extendModalVisible, setExtendModalVisible] = useState(false)
  const [extendingSubscription, setExtendingSubscription] = useState<UserSubscription | null>(null)
  const [form] = Form.useForm()

  // 搜索条件
  const [searchParams, setSearchParams] = useState<SubscriptionListParams>({
    page: 1,
    pageSize: DEFAULT_PAGE_SIZE
  })

  // 统计数据
  const [stats, setStats] = useState({
    totalSubscriptions: 0,
    activeSubscriptions: 0,
    expiringSoon: 0,
    todayRevenue: 0
  })

  const loadSubscriptions = React.useCallback(async () => {
    try {
      setLoading(true)

      const params: SubscriptionListParams = {
        page: currentPage,
        pageSize,
        ...searchParams
      }

      const response = await membershipService.getSubscriptions(params)

      if (response.success && response.data) {
        setSubscriptions(response.data.data || [])
        setTotal(response.data.total || 0)
      } else {
        message.error(response.message || '获取订阅列表失败')
      }
    } catch (error) {
      console.error('获取订阅列表失败:', error)
      message.error('获取订阅列表失败')
    } finally {
      setLoading(false)
    }
  }, [currentPage, pageSize, searchParams])

  const loadStats = async () => {
    try {
      const response = await membershipService.getMembershipStats()
      if (response.success && response.data) {
        setStats({
          totalSubscriptions: response.data.totalSubscriptions,
          activeSubscriptions: response.data.activeSubscriptions,
          expiringSoon: 0, // TODO: 后端添加即将到期统计
          todayRevenue: response.data.todayRevenue
        })
      }
    } catch (error) {
      console.error('获取统计数据失败:', error)
    }
  }

  useEffect(() => {
    loadSubscriptions()
  }, [loadSubscriptions])

  useEffect(() => {
    loadStats()
  }, [])

  const handleSearch = () => {
    setCurrentPage(1)
    // loadSubscriptions will be called automatically due to useEffect dependency
  }

  const handleReset = () => {
    setSearchParams({
      page: 1,
      pageSize: DEFAULT_PAGE_SIZE
    })
    setCurrentPage(1)
    // loadSubscriptions will be called automatically due to useEffect dependency
  }

  const handleCancelSubscription = async (id: string) => {
    try {
      const response = await membershipService.cancelSubscription(id)
      if (response.success) {
        message.success('订阅已取消')
        loadSubscriptions()
        loadStats()
      } else {
        message.error(response.message || '操作失败')
      }
    } catch (error) {
      console.error('取消订阅失败:', error)
      message.error('操作失败')
    }
  }

  const handleExtendSubscription = (subscription: UserSubscription) => {
    setExtendingSubscription(subscription)
    form.resetFields()
    setExtendModalVisible(true)
  }

  const handleExtendSubmit = async (values: { days: number }) => {
    if (!extendingSubscription) return

    try {
      const response = await membershipService.extendSubscription(
        extendingSubscription.id,
        values.days
      )
      if (response.success) {
        message.success(`订阅已延长${values.days}天`)
        setExtendModalVisible(false)
        loadSubscriptions()
        loadStats()
      } else {
        message.error(response.message || '延长失败')
      }
    } catch (error) {
      console.error('延长订阅失败:', error)
      message.error('延长失败')
    }
  }

  const handleViewDetail = (subscription: UserSubscription) => {
    Modal.info({
      title: '订阅详情',
      width: 600,
      content: (
        <div style={{ marginTop: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <strong>订阅ID:</strong> {subscription.id}
            </div>
            <div>
              <strong>用户ID:</strong> {subscription.userId}
            </div>
            <div>
              <strong>套餐ID:</strong> {subscription.planId}
            </div>
            <div>
              <strong>状态:</strong>{' '}
              {SUBSCRIPTION_STATUS_OPTIONS.find(s => s.value === subscription.status)?.label}
            </div>
            <div>
              <strong>开始时间:</strong>{' '}
              {dayjs(subscription.startDate).format('YYYY-MM-DD HH:mm:ss')}
            </div>
            <div>
              <strong>结束时间:</strong> {dayjs(subscription.endDate).format('YYYY-MM-DD HH:mm:ss')}
            </div>
            <div>
              <strong>剩余天数:</strong> {dayjs(subscription.endDate).diff(dayjs(), 'day')}天
            </div>
            <div>
              <strong>创建时间:</strong>{' '}
              {dayjs(subscription.createdAt).format('YYYY-MM-DD HH:mm:ss')}
            </div>
          </Space>
        </div>
      )
    })
  }

  const getStatusColor = (status: string) => {
    const option = SUBSCRIPTION_STATUS_OPTIONS.find(s => s.value === status)
    return option?.color || 'default'
  }

  const getStatusText = (status: string) => {
    const option = SUBSCRIPTION_STATUS_OPTIONS.find(s => s.value === status)
    return option?.label || status
  }

  const columns: ColumnsType<UserSubscription> = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      render: (_, record) => (
        <Space>
          <Avatar icon={<UserOutlined />} size="small" />
          <div>
            <div style={{ fontWeight: 500, fontSize: '12px' }}>
              {record.userEmail || '未知用户'}
            </div>
            <div style={{ color: '#999', fontSize: '11px' }}>
              ID: {record.userId ? record.userId.slice(0, 8) + '...' : '未知'}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '套餐',
      key: 'plan',
      width: 150,
      render: (_, record) => (
        <Space>
          <CrownOutlined style={{ color: '#faad14' }} />
          <div>
            <div style={{ fontWeight: 500, fontSize: '12px' }}>{record.planName || '未知套餐'}</div>
            <div style={{ color: '#999', fontSize: '11px' }}>
              ¥{record.planPrice || '0'} / {record.planDuration || '0'}天
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: status => <Tag color={getStatusColor(status)}>{getStatusText(status)}</Tag>
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      render: date => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '结束时间',
      dataIndex: 'endDate',
      render: (date, record) => {
        const endDate = dayjs(date)
        const now = dayjs()
        const daysLeft = endDate.diff(now, 'day')

        return (
          <div>
            <div>{endDate.format('YYYY-MM-DD')}</div>
            {record.status === 'active' && (
              <div
                style={{
                  color: daysLeft <= 7 ? '#f50' : daysLeft <= 15 ? '#fa8c16' : '#52c41a',
                  fontSize: '12px'
                }}
              >
                剩余{daysLeft}天
              </div>
            )}
          </div>
        )
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      render: date => dayjs(date).format('YYYY-MM-DD')
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button type="link" icon={<EyeOutlined />} onClick={() => handleViewDetail(record)} />
          </Tooltip>
          {record.status === 'active' && (
            <>
              <Tooltip title="延长订阅">
                <Button
                  type="link"
                  icon={<ClockCircleOutlined />}
                  onClick={() => handleExtendSubscription(record)}
                />
              </Tooltip>
              <Tooltip title="取消订阅">
                <Button
                  type="link"
                  danger
                  icon={<StopOutlined />}
                  onClick={() => handleCancelSubscription(record.id)}
                />
              </Tooltip>
            </>
          )}
        </Space>
      )
    }
  ]

  return (
    <div>
      <Title level={2} style={{ marginBottom: 24 }}>
        用户订阅管理
      </Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订阅数"
              value={stats.totalSubscriptions}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃订阅"
              value={stats.activeSubscriptions}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="即将到期"
              value={stats.expiringSoon}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="今日收入"
              value={stats.todayRevenue}
              prefix="¥"
              precision={2}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Card style={{ marginBottom: 16 }}>
        <Space wrap>
          <Input
            placeholder="搜索用户ID或邮箱"
            style={{ width: 200 }}
            value={searchParams.keyword}
            onChange={e => setSearchParams({ ...searchParams, keyword: e.target.value })}
            onPressEnter={handleSearch}
          />

          <Select
            placeholder="订阅状态"
            style={{ width: 120 }}
            allowClear
            value={searchParams.status}
            onChange={value => setSearchParams({ ...searchParams, status: value })}
          >
            {SUBSCRIPTION_STATUS_OPTIONS.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>

          <RangePicker
            placeholder={['开始日期', '结束日期']}
            onChange={dates => {
              if (dates) {
                setSearchParams({
                  ...searchParams,
                  startDate: dates[0]?.toISOString(),
                  endDate: dates[1]?.toISOString()
                })
              } else {
                setSearchParams({
                  ...searchParams,
                  startDate: undefined,
                  endDate: undefined
                })
              }
            }}
          />

          <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
            搜索
          </Button>

          <Button onClick={handleReset}>重置</Button>

          <Button icon={<ExportOutlined />}>导出</Button>
        </Space>
      </Card>

      <Card>
        <Table
          columns={columns}
          dataSource={subscriptions}
          rowKey="id"
          loading={loading}
          pagination={{
            current: currentPage,
            pageSize,
            total,
            onChange: (page, size) => {
              setCurrentPage(page)
              setPageSize(size)
            },
            ...TABLE_CONFIG
          }}
        />
      </Card>

      {/* 延长订阅模态框 */}
      <Modal
        title="延长订阅"
        open={extendModalVisible}
        onCancel={() => setExtendModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form form={form} layout="vertical" onFinish={handleExtendSubmit}>
          <Form.Item
            name="days"
            label="延长天数"
            rules={[{ required: true, message: '请输入延长天数' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              max={365}
              placeholder="30"
              addonAfter="天"
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setExtendModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                确定延长
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default MembershipSubscriptions

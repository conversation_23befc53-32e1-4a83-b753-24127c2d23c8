// 重新导出新的分离的服务，保持向后兼容
export { apiService, referralService } from './services/index'

// 重新导出所有类型，保持向后兼容
export type {
  // 聊天相关类型
  Message,
  TextUIPart,
  UIMessage,
  ChatConversation,
  ChatHistory,
  LangChainModel,
  LangChainStatus,

  // 用户相关类型
  UserProfile,

  // 角色相关类型
  CharacterData,
  Role,

  // 图像相关类型
  ImageGenerationTask,
  ImageGenerationResponse,

  // 上传相关类型
  UploadResponse,
  UploadType,
  UploadProgressCallback,

  // 邀请码相关类型
  InviteCode,
  InviteStats,
  CommissionAccount,
  CommissionRecord,
  InvitedUser,
  WithdrawRequest,
  WithdrawConfig,
  ReferralApiResponse,
  PaginatedResponse
} from './services/index'

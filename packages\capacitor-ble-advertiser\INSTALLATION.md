# 蓝牙广播插件安装和使用指南

## 安装步骤

### 1. 安装插件

```bash
# 从本地安装
npm install ./capacitor-ble-advertiser

# 或者从npm安装（发布后）
# npm install capacitor-ble-advertiser
```

### 2. 同步Capacitor项目

```bash
npx cap sync
```

## Android平台配置

### 1. 添加权限

在`android/app/src/main/AndroidManifest.xml`文件中添加以下权限：

```xml
<!-- 基本蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH" />
<uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />

<!-- Android 12及以上需要的蓝牙权限 -->
<uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
<uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
<uses-permission android:name="android.permission.BLUETOOTH_SCAN" />

<!-- 位置权限（Android 11以下需要） -->
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

<!-- 声明设备需要蓝牙LE功能 -->
<uses-feature android:name="android.hardware.bluetooth_le" android:required="true" />
```

### 2. 添加运行时权限请求

在应用启动时请求必要的权限：

```typescript
import { Capacitor } from '@capacitor/core';
import { PermissionsAndroid, Platform } from 'react-native';

// 请求Android蓝牙权限
async function requestBluetoothPermissions() {
  if (Capacitor.getPlatform() === 'android') {
    if (parseInt(Platform.Version.toString()) >= 31) { // Android 12+
      const permissions = [
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_SCAN,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_ADVERTISE,
        PermissionsAndroid.PERMISSIONS.BLUETOOTH_CONNECT,
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
      ];
      
      await PermissionsAndroid.requestMultiple(permissions);
    } else {
      await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION
      );
    }
  }
}
```

## iOS平台配置

### 1. 添加权限描述

在`ios/App/App/Info.plist`文件中添加以下权限描述：

```xml
<!-- 蓝牙使用权限描述 -->
<key>NSBluetoothAlwaysUsageDescription</key>
<string>应用需要使用蓝牙功能来发送设备控制命令</string>
<key>NSBluetoothPeripheralUsageDescription</key>
<string>应用需要使用蓝牙功能来发送设备控制命令</string>

<!-- 蓝牙广播权限 -->
<key>UIBackgroundModes</key>
<array>
    <string>bluetooth-peripheral</string>
</array>
```

## 使用方法

### 基本使用

```typescript
import { BleAdvertiser } from 'capacitor-ble-advertiser';

// 初始化蓝牙服务
async function initBluetooth() {
  const { success } = await BleAdvertiser.initialize();
  if (success) {
    console.log('蓝牙服务初始化成功');
  } else {
    console.error('蓝牙服务初始化失败');
  }
}

// 检查蓝牙是否启用
async function checkBluetoothEnabled() {
  const { enabled } = await BleAdvertiser.isBluetoothEnabled();
  console.log(`蓝牙状态: ${enabled ? '已启用' : '未启用'}`);
  return enabled;
}

// 发送命令
async function sendCommand(command: string) {
  const { success, instanceId } = await BleAdvertiser.startAdvertising({
    // 广播模式: 0=平衡模式, 1=低延迟模式(默认), 2=低功耗模式
    mode: 1,
    // 制造商ID，用于标识广播数据
    manufacturerId: 255,
    // 要广播的数据，十六进制字符串
    data: command
  });
  
  if (success) {
    console.log(`广播开始成功，实例ID: ${instanceId}`);
    
    // 3秒后停止广播
    setTimeout(async () => {
      await BleAdvertiser.stopAdvertising({ instanceId });
    }, 3000);
  }
}
```

### 使用服务类封装

可以使用示例中的`BleService`类来简化使用：

```typescript
import { BleService } from './path/to/BleService';

// 获取服务实例
const bleService = BleService.getInstance();

// 初始化
await bleService.initialize();

// 发送命令
await bleService.sendCommand('6db643ce97fe427ce49c6c');

// 停止所有广播
await bleService.stopAllCommands();
```

### 在React组件中使用

可以参考示例中的`BleCommandButton`和`DeviceControl`组件，将蓝牙广播功能集成到React组件中。

## 故障排除

### 常见问题

1. **蓝牙未启用**
   - 确保设备的蓝牙已开启
   - 使用`isBluetoothEnabled()`方法检查蓝牙状态

2. **权限问题**
   - 确保已添加所有必要的权限
   - 在Android 12+上确保运行时请求了蓝牙权限

3. **广播不工作**
   - 检查设备是否支持BLE广播功能
   - 确认数据格式正确（十六进制字符串或数字数组）
   - 尝试不同的广播模式

4. **iOS后台限制**
   - iOS在应用进入后台后会限制蓝牙广播功能
   - 添加`bluetooth-peripheral`后台模式可以延长广播时间

### 调试技巧

1. 使用BLE扫描应用（如nRF Connect）验证广播是否正常发送
2. 检查控制台日志中的错误信息
3. 在不同设备上测试，因为不同设备的蓝牙实现可能有差异 
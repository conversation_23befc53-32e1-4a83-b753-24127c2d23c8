import type { Env } from '@/types/env';
import { getUserInviter } from '@/lib/db/queries/referral';
import { createCommissionRecord, updateCommissionBalance } from '@/lib/db/queries/commission';
import { getCommissionRates, calculateCommissionAmount } from '@/lib/db/queries/system-config';
import { getSupabase } from '@/lib/db/queries/base';
import { handleSupabaseSingleResult, TABLE_NAMES } from '@/lib/db/supabase-types';

// ==================== 佣金计算和结算服务 ====================

/**
 * 计算并创建佣金记录
 */
export async function calculateAndCreateCommission(
  env: Env,
  orderId: string
): Promise<{
  success: boolean;
  commissionAmount?: number;
  inviterId?: string;
  error?: string;
}> {
  try {
    const supabase = getSupabase(env);

    // 1. 获取订单信息
    const orderResult = await supabase
      .from(TABLE_NAMES.paymentOrder)
      .select('*')
      .eq('id', orderId)
      .single();

    const { data: order, error: orderError } = handleSupabaseSingleResult(orderResult);
    if (orderError || !order) {
      return {
        success: false,
        error: '订单不存在',
      };
    }

    // 2. 检查用户是否有邀请人
    const inviterInfo = await getUserInviter(env, order.user_id);
    if (!inviterInfo) {
      // 用户没有邀请人，不产生佣金
      return {
        success: true,
        commissionAmount: 0,
      };
    }

    // 3. 检查是否已经创建过佣金记录
    const existingResult = await supabase
      .from(TABLE_NAMES.commissionRecord)
      .select('*')
      .eq('order_id', orderId)
      .limit(1);

    const { data: existing } = handleSupabaseSingleResult(existingResult);
    if (existing) {
      return {
        success: true,
        commissionAmount: Number.parseFloat(existing.commission_amount),
        inviterId: existing.inviter_id,
      };
    }

    // 4. 确定佣金来源类型和金额
    let sourceType: 'membership' | 'points_package';
    let sourceAmount: number;

    if (order.plan_id) {
      sourceType = 'membership';
      sourceAmount = Number.parseFloat(order.amount);
    } else if (order.points_package_id) {
      sourceType = 'points_package';
      sourceAmount = Number.parseFloat(order.amount);
    } else {
      return {
        success: false,
        error: '无法确定订单类型',
      };
    }

    // 5. 获取佣金比例并计算佣金金额
    const rates = await getCommissionRates(env);
    const commissionAmount = calculateCommissionAmount(sourceAmount, sourceType, rates);

    if (commissionAmount <= 0) {
      return {
        success: true,
        commissionAmount: 0,
      };
    }

    // 6. 创建佣金记录
    await createCommissionRecord(env, {
      inviterId: inviterInfo.inviterId,
      inviteeId: order.user_id,
      orderId,
      commissionAmount,
      sourceType,
      sourceAmount,
      commissionRate: sourceType === 'membership' ? rates.membershipRate : rates.pointsRate,
    });

    // 7. 更新邀请人的佣金账户余额
    await updateCommissionBalance(env, inviterInfo.inviterId, commissionAmount);

    console.log(`✅ [COMMISSION] 佣金结算成功:`, {
      orderId,
      inviterId: inviterInfo.inviterId,
      inviteeId: order.user_id,
      sourceType,
      sourceAmount,
      commissionAmount,
    });

    return {
      success: true,
      commissionAmount,
      inviterId: inviterInfo.inviterId,
    };
  } catch (error) {
    console.error('Calculate and create commission error:', error);
    return {
      success: false,
      error: '佣金计算失败',
    };
  }
}

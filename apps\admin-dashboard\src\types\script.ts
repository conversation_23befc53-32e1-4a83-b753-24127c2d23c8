// 剧本相关类型定义

export interface Command {
  command: string
  time: string
}

export interface Picture {
  name: string
  pic: string
}

export interface Intensity {
  thrust: number
  suction: number
  vibrate: number
}

export interface Stage {
  stage: number
  stageTitle: string
  pics: Picture[]
  intensity: Record<string, Intensity>
}

export interface ScriptContent {
  commands: Command[]
  stages: Stage[]
}

export interface Script {
  id: string
  title: string
  description: string
  coverImage: string
  duration: string
  tags: string[]
  category: string
  content: ScriptContent
  audioUrl?: string
  totalDuration?: number
  stageCount?: number
  isPublic: boolean
  isActive: boolean
  isPremium: boolean
  pointsCost: number
  usageCount: number
  rating: number
  ratingCount: number
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface ScriptListParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  isPublic?: boolean
  isActive?: boolean
  isPremium?: boolean
}

export interface ScriptCreateParams {
  title: string
  description: string
  coverImage: string
  duration: string
  tags: string[]
  category: string
  content?: ScriptContent
  audioUrl?: string
  totalDuration?: number
  stageCount?: number
  isPublic?: boolean
  isActive?: boolean
  isPremium?: boolean
  pointsCost?: number
}

export interface ScriptUpdateParams extends Partial<ScriptCreateParams> {
  id: string
}

export interface ScriptStats {
  totalScripts: number
  publicScripts: number
  privateScripts: number
  activeScripts: number
  premiumScripts: number
  totalUsage: number
  averageRating: number
}
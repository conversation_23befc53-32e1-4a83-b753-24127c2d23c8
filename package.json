{"name": "<PERSON><PERSON><PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "engines": {"node": ">=22"}, "scripts": {"test": "turbo run test", "lint": "turbo run lint", "build:web": "turbo run build --filter=app-ui", "dev:web": "turbo run dev --filter=app-ui", "deploy:edge-functions": "turbo run deploy --filter=edge-functions", "dev:edge-functions": "turbo run dev --filter=edge-functions", "build:elevenlabs-worker": "turbo run build --filter=elevenlabs-worker", "dev:elevenlabs-worker": "turbo run dev --filter=elevenlabs-worker", "deploy:elevenlabs-worker": "turbo run deploy --filter=elevenlabs-worker"}, "keywords": [], "author": "", "license": "ISC", "packageManager": "pnpm@10.5.2", "devDependencies": {"turbo": "^2.5.3"}}
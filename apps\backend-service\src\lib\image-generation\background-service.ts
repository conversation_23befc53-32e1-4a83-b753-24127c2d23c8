import type { Env } from '@/types/env';
import { generateScenePrompt } from '../ai/actions';
import { BackgroundImageGenerator } from './generators/background';
import { getChatById } from '../db/queries/chat';
import { getSupabase } from '../db/queries/base';
import { TABLE_NAMES } from '../db/supabase-types';

/**
 * 背景图生成服务
 * 负责场景描述优化和背景图生成的完整流程
 */
export class BackgroundImageService {
  private backgroundGenerator: BackgroundImageGenerator;

  constructor(private env: Env) {
    this.backgroundGenerator = new BackgroundImageGenerator(env);
  }

  /**
   * 为聊天生成背景图
   * @param chatId 聊天ID
   * @param sceneDescription 场景描述
   * @param userId 用户ID（用于权限验证）
   * @returns 背景图URL
   */
  async generateChatBackground(
    chatId: string,
    sceneDescription: string,
    userId: string
  ): Promise<string> {
    try {
      // 1. 验证聊天是否属于该用户
      const chatRecord = await getChatById(this.env, { id: chatId });

      if (!chatRecord) {
        throw new Error('聊天记录不存在');
      }

      if (chatRecord.userId !== userId) {
        throw new Error('无权限访问该聊天');
      }

      // 2. 优化场景描述，生成适合背景图的提示词
      console.log('开始优化场景描述:', sceneDescription);
      const optimizedPrompt = await generateScenePrompt(this.env, { sceneDescription });
      console.log('优化后的提示词:', optimizedPrompt);

      // 3. 生成背景图
      console.log('开始生成背景图...');
      const backgroundImageUrl =
        await this.backgroundGenerator.generateBackgroundImage(optimizedPrompt);
      console.log('背景图生成完成:', backgroundImageUrl);

      // 4. 更新聊天记录的背景图URL和场景描述
      const supabase = getSupabase(this.env);
      await supabase
        .from(TABLE_NAMES.chat)
        .update({
          background_image_url: backgroundImageUrl,
          background_scene_description: sceneDescription, // 存储原始场景描述
          updated_at: new Date().toISOString(),
        })
        .eq('id', chatId);

      return backgroundImageUrl;
    } catch (error) {
      console.error('生成聊天背景图失败:', error);
      throw error;
    }
  }

  /**
   * 获取聊天的背景图URL和场景描述
   * @param chatId 聊天ID
   * @param userId 用户ID（用于权限验证）
   * @returns 背景图信息或null
   */
  async getChatBackground(
    chatId: string,
    userId: string
  ): Promise<{
    backgroundImageUrl: string | null;
    backgroundSceneDescription: string | null;
  } | null> {
    try {
      // 一次查询同时获取背景图URL、场景描述和验证权限
      const supabase = getSupabase(this.env);
      const result = await supabase
        .from(TABLE_NAMES.chat)
        .select('background_image_url, background_scene_description, user_id')
        .eq('id', chatId)
        .single();

      const { data: chatRecord, error } = result;
      if (error || !chatRecord) {
        throw new Error('聊天记录不存在');
      }

      // 权限验证
      if (chatRecord.user_id !== userId) {
        throw new Error('无权限访问该聊天');
      }

      return {
        backgroundImageUrl: chatRecord.background_image_url,
        backgroundSceneDescription: chatRecord.background_scene_description,
      };
    } catch (error) {
      console.error('获取聊天背景图失败:', error);
      throw error;
    }
  }
}

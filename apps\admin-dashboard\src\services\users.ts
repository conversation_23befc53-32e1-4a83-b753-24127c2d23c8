import { apiService } from './api'
import type { ApiResponse, PaginatedResponse, User, UserProfile } from '@/types/api'

export interface UserListParams {
  page?: number
  pageSize?: number
  keyword?: string
  gender?: string
  isEmailVerified?: boolean
  startDate?: string
  endDate?: string
}

export interface UserDetailResponse extends User {
  profile?: UserProfile
  subscription?: {
    status: string
    planName?: string
    endDate?: string
  }
  points?: {
    balance: number
    totalUsed: number
  }
  stats?: {
    chatCount: number
    messageCount: number
    totalSpent: number
  }
}

export interface CreateUserRequest {
  email: string
  password?: string
  nickname?: string
  generatePassword?: boolean
}

export interface CreateUserResponse {
  id: string
  email: string
  nickname: string
  tempPassword?: string
}

// 用户管理服务
export class UserService {
  // 获取用户列表
  async getUsers(params: UserListParams): Promise<ApiResponse<PaginatedResponse<User>>> {
    return await apiService.get<PaginatedResponse<User>>('/admin/users', { params })
  }

  // 获取用户详情
  async getUserDetail(userId: string): Promise<ApiResponse<UserDetailResponse>> {
    return await apiService.get<UserDetailResponse>(`/admin/users/${userId}`)
  }

  // 创建用户
  async createUser(data: CreateUserRequest): Promise<ApiResponse<CreateUserResponse>> {
    return await apiService.post<CreateUserResponse>('/admin/users', data)
  }

  // 禁用/启用用户
  async toggleUserStatus(userId: string, isActive: boolean): Promise<ApiResponse<void>> {
    return await apiService.put<void>(`/admin/users/${userId}/status`, { isActive })
  }

  // 重置用户密码
  async resetUserPassword(userId: string): Promise<ApiResponse<{ tempPassword: string }>> {
    return await apiService.post<{ tempPassword: string }>(`/admin/users/${userId}/reset-password`)
  }

  // 删除用户
  async deleteUser(userId: string): Promise<ApiResponse<void>> {
    return await apiService.delete<void>(`/admin/users/${userId}`)
  }

  // 调整用户积分
  async adjustUserPoints(
    userId: string,
    points: number,
    reason: string
  ): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/users/${userId}/points`, {
      points,
      reason,
      type: points > 0 ? 'add' : 'deduct'
    })
  }

  // 获取用户统计数据
  async getUserStats(): Promise<
    ApiResponse<{
      totalUsers: number
      activeUsers: number
      newUsersToday: number
      verifiedUsers: number
    }>
  > {
    return await apiService.get<any>('/admin/users/stats')
  }

  // 导出用户数据
  async exportUsers(params: UserListParams): Promise<Blob> {
    const response = await apiService.get('/admin/users/export', {
      params,
      responseType: 'blob'
    })
    return response as any
  }

  // 设为会员
  async setUserMembership(
    userId: string,
    planId: string,
    duration: number
  ): Promise<ApiResponse<void>> {
    return await apiService.post<void>(`/admin/users/${userId}/membership`, {
      planId,
      duration
    })
  }
}

export const userService = new UserService()

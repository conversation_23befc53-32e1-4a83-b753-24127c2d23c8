{"name": "pay-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroui/react": "2.8.0-beta.7", "@tailwindcss/vite": "^4.1.8", "framer-motion": "^12.11.3", "lucide-react": "^0.523.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router": "^7.6.2", "tw-animate-css": "^1.3.4"}, "devDependencies": {"@eslint/js": "^9.29.0", "@tailwindcss/postcss": "^4.1.10", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^6.3.5"}}
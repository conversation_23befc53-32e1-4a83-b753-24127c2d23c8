import React, { useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import confetti from 'canvas-confetti'
import { useTranslation } from 'react-i18next'

interface PurchaseSuccessModalProps {
  isOpen: boolean
  onClose: () => void
  onStartScript: () => void
  scriptTitle: string
  pointsCost: number
}

/**
 * 购买成功弹窗组件
 * 包含碎纸屑特效和操作按钮
 */
export const PurchaseSuccessModal: React.FC<PurchaseSuccessModalProps> = ({
  isOpen,
  onClose,
  onStartScript,
  scriptTitle,
  pointsCost
}) => {
  const { t } = useTranslation('interactive')
  // 触发碎纸屑特效
  const triggerConfetti = () => {
    // 第一波：从左侧发射
    confetti({
      particleCount: 100,
      spread: 70,
      origin: { x: 0.2, y: 0.6 },
      colors: ['#ff2d97', '#892fff', '#00d4ff', '#ffeb3b']
    })

    // 第二波：从右侧发射
    setTimeout(() => {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { x: 0.8, y: 0.6 },
        colors: ['#ff2d97', '#892fff', '#00d4ff', '#ffeb3b']
      })
    }, 250)

    // 第三波：从中间发射
    setTimeout(() => {
      confetti({
        particleCount: 150,
        spread: 100,
        origin: { x: 0.5, y: 0.4 },
        colors: ['#ff2d97', '#892fff', '#00d4ff', '#ffeb3b']
      })
    }, 500)
  }

  // 当弹窗打开时触发特效
  useEffect(() => {
    if (isOpen) {
      // 延迟一点触发，让弹窗动画先完成
      const timer = setTimeout(triggerConfetti, 300)
      return () => clearTimeout(timer)
    }
  }, [isOpen])

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* 弹窗内容 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="fixed inset-0 flex items-center justify-center z-50 p-4"
            onClick={e => e.stopPropagation()}
          >
            <div className="bg-[#1a1e2e] rounded-2xl p-6 max-w-sm w-full mx-4 shadow-2xl border border-[#33395c]">
              {/* 成功图标 */}
              <div className="text-center mb-6">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring', damping: 15 }}
                  className="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-[#ff2d97] to-[#892fff] mobile-gradient-primary rounded-full flex items-center justify-center"
                >
                  <svg
                    className="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={3}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </motion.div>

                <motion.h2
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="text-xl font-bold text-white mb-2"
                >
                  {t('purchaseSuccessModal.title')}
                </motion.h2>

                <motion.p
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="text-[#7c85b6] text-sm"
                >
                  {t('purchaseSuccessModal.congratulations', { scriptTitle })}
                </motion.p>
              </div>

              {/* 购买信息 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                className="bg-[#121521] rounded-xl p-4 mb-6"
              >
                <div className="flex justify-between items-center text-sm">
                  <span className="text-[#7c85b6]">{t('purchaseSuccessModal.pointsSpent')}</span>
                  <span className="text-[#ff2d97] font-semibold">{t('purchaseSuccessModal.pointsAmount', { pointsCost })}</span>
                </div>
                <div className="flex justify-between items-center text-sm mt-2">
                  <span className="text-[#7c85b6]">{t('purchaseSuccessModal.validityPeriod')}</span>
                  <span className="text-white">{t('purchaseSuccessModal.oneYear')}</span>
                </div>
              </motion.div>

              {/* 操作按钮 */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="flex gap-3"
              >
                <button
                  onClick={onClose}
                  className="flex-1 py-3 px-4 bg-[#33395c] text-[#7c85b6] rounded-xl font-medium transition-colors hover:bg-[#3d4466]"
                >
                  {t('purchaseSuccessModal.experienceLater')}
                </button>
                <button
                  onClick={onStartScript}
                  className="flex-1 py-3 px-4 bg-gradient-to-r from-[#ff2d97] to-[#892fff] mobile-gradient-primary text-white rounded-xl font-medium transition-transform hover:scale-105"
                >
                  {t('purchaseSuccessModal.startScript')}
                </button>
              </motion.div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}

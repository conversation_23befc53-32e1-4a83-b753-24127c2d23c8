import React from 'react'
import { Card, CardBody } from '@heroui/react'
import type { CharacterData } from '..'
import { personalityOptions } from '../mapping'

// 个性选项

interface PersonalityProps {
  data: CharacterData
  onUpdate: (data: Partial<CharacterData>) => void
}

// 对应每个section的组件
const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-6">
    <h3 className="font-medium text-center text-lg mb-4 text-primary">{title}</h3>
    {children}
  </div>
)

export default function Personality({ data, onUpdate }: PersonalityProps) {
  // 每页显示12个选项（全部）
  const currentOptions = personalityOptions

  return (
    <div className="space-y-6">
      <Section title="选择角色性格">
        <div className="flex justify-center mb-4">
          <p className="text-default-500 text-sm text-center">选择一种最能代表角色的性格特点</p>
        </div>

        <div className="grid grid-cols-2 gap-3">
          {currentOptions.map(option => {
            const isSelected = data.personality === option.value

            return (
              <Card
                key={option.value}
                isPressable
                isHoverable
                onPress={() => onUpdate({ personality: option.value })}
                className={`
                  cursor-pointer transition-all duration-200
                  ${
                    isSelected
                      ? 'ring-2 ring-primary ring-offset-2 ring-offset-background bg-primary/5'
                      : ''
                  }
                `}
              >
                <CardBody className="p-3">
                  <div className="flex items-start gap-3">
                    <div className="text-xl bg-gradient-to-br from-primary/20 to-primary/5 w-10 h-10 flex items-center justify-center rounded-full flex-shrink-0">
                      {option.emoji}
                    </div>
                    <div className="min-w-0 flex-1">
                      <div className="text-sm font-medium text-foreground mb-1">{option.label}</div>
                      <div className="text-xs text-default-500 line-clamp-2 leading-relaxed">
                        {option.description}
                      </div>
                    </div>
                  </div>
                </CardBody>
              </Card>
            )
          })}
        </div>
      </Section>
    </div>
  )
}

# 在 Cloudflare Dashboard 获取
CLOUDFLARE_ACCOUNT_ID=123        # 账户 ID

# 在 Supabase Dashboard 获取
SUPABASE_URL=123

SUPABASE_ANON_KEY=123

SUPABASE_SERVICE_ROLE_KEY=123

# 数据库直连 URL
DATABASE_URL=123

XAI_API_KEY=123

BUCKET=pleasurehub
R2_PUBLIC_URL=https://assets.pleasurehub.app

CLOUDFLARE_ACCOUNT_ID=123
R2_ACCESS_KEY_ID=123
R2_SECRET_ACCESS_KEY=123
R2_BUCKET_NAME=pleasurehub
R2_PUBLIC_BASE_URL=https://assets.pleasurehub.app

YUNWU_API_KEY=123

# version 1
# INSA3D_API_ENDPOINT=10w7eacxxhxna7
# INSA3D_API_TOKEN=6Ar45yqs1x-kFjPuwIpCvw

# version2
INSA3D_API_ENDPOINT=123
INSA3D_API_TOKEN=123


# 通用
INSA3D_BASE_API_ENDPOINT=123
INSA3D_BASE_API_TOKEN=123


# 线上环境支付密钥
NOWPAYMENTS_API_KEY=123
NOWPAYMENTS_IPN_SECRET=123
# 沙箱环境API密钥和IPN密钥
NOWPAYMENTS_SANDBOX_API_KEY=123
NOWPAYMENTS_SANDBOX_IPN_SECRET=123
NOWPAYMENTS_USE_SANDBOX=true

# Resend 邮件服务配置
RESEND_API_KEY=123

# 选择 B: 使用自己的域名（需要在 Resend 中验证域名）
RESEND_FROM_EMAIL=<EMAIL>
RESEND_FROM_NAME=PleasureHub


FISH_AUDIO_API_KEY=123
FISH_AUDIO_MODEL_ID=123

ELEVENLABS_API_KEY=123
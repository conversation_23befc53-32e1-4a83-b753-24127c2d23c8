import { registerPlugin } from '@capacitor/core';
import type { BleAdvertiserPlugin } from './definitions';

/**
 * 蓝牙广播插件，用于发送BLE广播数据
 *
 * 使用示例:
 * ```typescript
 * import { BleAdvertiser } from 'capacitor-ble-advertiser';
 *
 * // 初始化
 * await BleAdvertiser.initialize();
 *
 * // 检查蓝牙是否开启
 * const { enabled } = await BleAdvertiser.isBluetoothEnabled();
 *
 * // 发送广播
 * const { success, instanceId } = await BleAdvertiser.startAdvertising({
 *   data: '6db643ce97fe427ce49c6c', // 十六进制字符串
 *   manufacturerId: 255
 * });
 *
 * // 停止广播
 * await BleAdvertiser.stopAdvertising({ instanceId });
 * ```
 */
const BleAdvertiserImpl = registerPlugin<BleAdvertiserPlugin>('BleAdvertiser', {
  web: () => import('./web').then((m) => new m.BleAdvertiserWeb()),
});

// 包装插件方法，确保正确处理Promise
export const BleAdvertiser = {
  // 初始化蓝牙服务
  initialize: async (): Promise<{ success: boolean; message?: string }> => {
    try {
      return await BleAdvertiserImpl.initialize();
    } catch (error) {
      console.error('初始化蓝牙服务失败:', error);
      return { success: false, message: String(error) };
    }
  },

  // 检查蓝牙是否启用
  isBluetoothEnabled: async (): Promise<{ enabled: boolean }> => {
    try {
      return await BleAdvertiserImpl.isBluetoothEnabled();
    } catch (error) {
      console.error('检查蓝牙状态失败:', error);
      return { enabled: false };
    }
  },

  // 开始广播
  startAdvertising: async (
    options: any,
  ): Promise<{ success: boolean; instanceId?: number; message?: string }> => {
    try {
      return await BleAdvertiserImpl.startAdvertising(options);
    } catch (error) {
      console.error('开始广播失败:', error);
      return { success: false, message: String(error) };
    }
  },

  // 停止广播
  stopAdvertising: async (
    options: any,
  ): Promise<{ success: boolean; message?: string }> => {
    try {
      return await BleAdvertiserImpl.stopAdvertising(options);
    } catch (error) {
      console.error('停止广播失败:', error);
      return { success: false, message: String(error) };
    }
  },

  // 停止所有广播
  stopAllAdvertising: async (): Promise<{
    success: boolean;
    message?: string;
  }> => {
    try {
      return await BleAdvertiserImpl.stopAllAdvertising();
    } catch (error) {
      console.error('停止所有广播失败:', error);
      return { success: false, message: String(error) };
    }
  },
};

// 添加错误处理，确保插件方法能够正确识别并处理错误
try {
  // 验证插件是否正确注册
  if (typeof BleAdvertiser !== 'undefined') {
    console.log('BleAdvertiser插件已注册');
  }
} catch (error) {
  console.error('BleAdvertiser插件注册错误:', error);
}

export * from './definitions';

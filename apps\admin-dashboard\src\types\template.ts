// 写真集模板相关类型定义

export interface Template {
  id: string
  name: string
  description?: string
  category: string
  previewImage?: string
  prompt: string
  negativePrompt?: string
  pointsCost: number
  isPremium: boolean
  isPublic: boolean
  isActive: boolean
  tags: string[]
  settings: Record<string, any>
  createdBy?: string
  usageCount: number
  createdAt: string
  updatedAt: string
}

export interface TemplateListParams {
  page?: number
  pageSize?: number
  keyword?: string
  category?: string
  isPublic?: boolean
  isActive?: boolean
  isPremium?: boolean
}

export interface TemplateCreateParams {
  name: string
  description?: string
  category: string
  previewImage?: string
  prompt: string
  negativePrompt?: string
  pointsCost?: number
  isPremium?: boolean
  isPublic?: boolean
  isActive?: boolean
  tags?: string[]
  settings?: Record<string, any>
}

export interface TemplateUpdateParams extends TemplateCreateParams {
  id: string
}

export interface TemplateStats {
  totalTemplates: number
  publicTemplates: number
  privateTemplates: number
  activeTemplates: number
  premiumTemplates: number
  totalUsage: number
}
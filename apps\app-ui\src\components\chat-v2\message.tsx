import type { LangChainMessage } from './type'
import { AnimatePresence, motion } from 'framer-motion'
import { memo, useEffect } from 'react'
import { Icon } from '@iconify/react'
import { MarkdownV2, StoryMarkdownV2 } from './markdown'
import equal from 'fast-deep-equal'
import { useRoleInfo } from '@/contexts/role-context'
import { useStreamingContent } from '../data-stream-handler'
import { commandQueueManager } from '../../pages/interactive/utils/bluetoothUtils'
import { CustomAvatar } from '@/components/ui/custom-avatar'
import { useTranslation } from 'react-i18next'

// 创建一个状态引用，避免在memo比较函数中使用hooks
let streamingMessageIdV2: string | null = null
const updateStreamingMessageIdV2 = (id: string | null) => {
  streamingMessageIdV2 = id
}

// 添加一个函数来检查消息中是否包含完整的设备命令
const hasCompleteDeviceCommand = (text: string): boolean => {
  return /<device>\[.*?\]<\/device>/s.test(text)
}

const PurePreviewMessageV2 = ({
  message,
  isLoading,
  chatId,
  onScene
}: {
  message: LangChainMessage
  isLoading: boolean
  chatId?: string
  onScene?: (sceneDescription: string) => void
}) => {
  // 使用角色上下文获取头像
  const { character } = useRoleInfo()
  const characterAvatar = character?.imageUrl || null

  // 使用i18n翻译
  const { t } = useTranslation('chat-v2')

  // 使用流式内容
  const { streamingMessageId, streamingContent, isStreaming } = useStreamingContent()

  // 更新全局引用，用于memo比较
  updateStreamingMessageIdV2(streamingMessageId)

  // 判断当前消息是否是正在流式输出的消息
  const isCurrentMessageStreaming = streamingMessageId === message.id && isStreaming

  // 处理图片提示（由ImageGeneration组件处理）
  const handleImagePrompt = (imagePrompt: string) => {
    // 图片提示由ImageGeneration组件自动处理
  }

  // 检查消息是否完成，并处理设备命令
  useEffect(() => {
    // 只处理来自助手的消息
    if (message.role !== 'assistant' || !message.parts || !message.id) return

    // 检查消息是否仍在流式传输中
    if (message.id === streamingMessageId && isStreaming) {
      // 消息仍在流式传输中，检查当前内容是否包含完整的设备命令
      const currentFullContent = message.parts
        .filter(part => part.type === 'text')
        .map(part => part.text)
        .join('')

      // 如果包含完整设备命令并且没有流式传输中，可以处理
      if (hasCompleteDeviceCommand(currentFullContent)) {
        console.log(`检测到流式传输中的完整设备命令，消息ID: ${message.id}`)
        commandQueueManager.handleDeviceCommands(currentFullContent, message)
      }
    } else {
      // 消息已完成，处理所有文本部分
      let fullContent = ''
      message.parts.forEach(part => {
        if (part.type === 'text') {
          fullContent += part.text
        }
      })

      // 处理可能包含的设备命令
      if (hasCompleteDeviceCommand(fullContent)) {
        console.log(`处理完整消息中的设备命令，消息ID: ${message.id}`)
        commandQueueManager.handleDeviceCommands(fullContent, message)
      }
    }
  }, [message, streamingMessageId, isStreaming])

  return (
    <AnimatePresence>
      <motion.div
        data-testid={`message-${message.role}`}
        className="w-full mx-auto max-w-3xl px-4 group/message"
        initial={{
          y: message.role === 'user' ? 20 : 5,
          opacity: 0,
          scale: message.role === 'user' ? 0.95 : 1
        }}
        animate={{
          y: 0,
          opacity: 1,
          scale: 1
        }}
        transition={{
          duration: message.role === 'user' ? 0.4 : 0.3,
          ease: message.role === 'user' ? 'easeOut' : 'easeInOut'
        }}
        data-role={message.role}
      >
        {message.role === 'user' ? (
          // 用户消息 - 右对齐气泡
          <div className="flex justify-end">
            <div className="max-w-[80%] bg-[#892FFF] text-primary-foreground rounded-2xl rounded-tr-md px-4 py-3 shadow-sm">
              {message.parts?.map((part, index) => {
                if (part.type === 'text') {
                  return (
                    <div key={`user-${message.id}-${index}`}>
                      <MarkdownV2>{part.text}</MarkdownV2>
                    </div>
                  )
                }
                return null
              })}
            </div>
          </div>
        ) : (
          // AI消息 - 左对齐，包含头像和角色名
          <div className="flex gap-2">
            {/* 头像 */}
            <CustomAvatar
              src={characterAvatar || undefined}
              alt={character?.name || t('assistant.aiAssistant')}
              size="md"
              className="shrink-0"
              fallback={
                <div className="w-full h-full bg-gradient-to-br from-purple-400 to-blue-500 flex items-center justify-center">
                  <Icon icon="solar:magic-stick-3-linear" className="text-white" width={20} />
                </div>
              }
            />

            {/* 消息内容区域 */}
            <div className="flex-1">
              {/* 消息内容 */}
              <div className="w-full">
                {message.parts?.map((part, index) => {
                  const key = `message-${message.id}-part-${index}`

                  if (part.type === 'text') {
                    // 如果是当前正在流式输出的消息，使用流式内容代替原始内容
                    const textContent =
                      isCurrentMessageStreaming && index === message.parts.length - 1
                        ? streamingContent
                        : part.text

                    // 只有在当前消息是正在流式输出的最后一部分时才显示光标
                    const shouldShowCursor =
                      isCurrentMessageStreaming && index === message.parts.length - 1

                    return (
                      <div key={key} className="w-full">
                        <StoryMarkdownV2
                          content={textContent}
                          showCursor={shouldShowCursor}
                          onImagePrompt={handleImagePrompt}
                          onScene={onScene}
                          characterAvatar={characterAvatar}
                          messageId={message.id}
                          chatId={chatId}
                          messageAttachments={message.attachments}
                        />
                      </div>
                    )
                  }

                  return null
                })}
              </div>
            </div>
          </div>
        )}
      </motion.div>
    </AnimatePresence>
  )
}

export const MessageV2 = memo(PurePreviewMessageV2, (prevProps, nextProps) => {
  // 基本属性比较
  if (prevProps.isLoading !== nextProps.isLoading) return false
  if (prevProps.message.id !== nextProps.message.id) return false
  if (prevProps.chatId !== nextProps.chatId) return false

  // 深度比较消息内容，但排除可能频繁变化的属性
  if (!equal(prevProps.message.parts, nextProps.message.parts)) return false

  // 对于正在流式输出的消息，始终返回false以触发重新渲染
  // 不要在这里调用useStreamingContent Hook，而是使用外部状态引用
  if (
    streamingMessageIdV2 === prevProps.message.id ||
    streamingMessageIdV2 === nextProps.message.id
  ) {
    return false
  }

  // 其他情况下，认为组件相同，避免不必要的重新渲染
  return true
})

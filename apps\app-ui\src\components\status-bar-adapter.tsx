import { useEffect, useState, type ReactNode } from 'react'
import { Capacitor } from '@capacitor/core'
import { StatusBar, Style } from '@capacitor/status-bar'

interface StatusBarAdapterProps {
  children: ReactNode
  /** 状态栏样式，默认为深色 */
  style?: Style
  /** 状态栏背景色，仅安卓有效 */
  backgroundColor?: string
}

/**
 * 状态栏适配组件
 * 简单直接：检测状态栏是否需要手动填充高度
 */
export function StatusBarAdapter({
  children,
  style = Style.Dark,
  backgroundColor = '#0B0A1E'
}: StatusBarAdapterProps) {
  const [needsPadding, setNeedsPadding] = useState(false)
  const [paddingHeight, setPaddingHeight] = useState(0)

  useEffect(() => {
    const checkStatusBar = async () => {
      if (!Capacitor.isNativePlatform()) {
        setNeedsPadding(false)
        return
      }

      try {
        // 配置状态栏
        const platform = Capacitor.getPlatform()

        if (platform === 'android') {
          // Android：原生层已设置全屏，这里只需配置样式
          await StatusBar.setOverlaysWebView({ overlay: true })
          await StatusBar.setStyle({ style })
          await StatusBar.setBackgroundColor({ color: '#00000000' }) // 透明
          console.log('Android状态栏：原生全屏 + Capacitor透明覆盖')
        } else if (platform === 'ios') {
          // iOS：设置样式
          await StatusBar.setStyle({ style })
        }

        // 获取状态栏信息
        const info = await StatusBar.getInfo()
        console.log('状态栏信息:', JSON.stringify(info))

        // 统一设置为覆盖模式，状态栏悬浮不占空间，无需任何填充
        setNeedsPadding(false)
        setPaddingHeight(0)
        console.log('状态栏设置为覆盖模式，悬浮不占空间')
      } catch (error) {
        console.error('状态栏检测失败:', error)
        setNeedsPadding(false)
      }
    }

    checkStatusBar()
  }, [style, backgroundColor])

  // 获取状态栏高度估算值
  const getStatusBarHeight = (platform: string): number => {
    if (platform === 'ios') {
      // iOS设备状态栏高度
      const isIPhoneWithNotch = window.screen.height >= 812 || window.screen.width >= 812
      return isIPhoneWithNotch ? 47 : 20
    } else if (platform === 'android') {
      // Android设备状态栏高度
      const density = window.devicePixelRatio || 1
      return Math.round(24 * density)
    }
    return 0
  }

  return (
    <div
      className="status-bar-adapter w-full h-full"
      style={{
        paddingTop: needsPadding ? `${paddingHeight}px` : '0px'
      }}
    >
      {children}
    </div>
  )
}

// 简化的工具函数
export function useStatusBarHeight() {
  const [height, setHeight] = useState(0)

  useEffect(() => {
    const getHeight = async () => {
      if (Capacitor.isNativePlatform()) {
        const platform = Capacitor.getPlatform()
        if (platform === 'ios') {
          const isIPhoneWithNotch = window.screen.height >= 812 || window.screen.width >= 812
          setHeight(isIPhoneWithNotch ? 47 : 20)
        } else if (platform === 'android') {
          const density = window.devicePixelRatio || 1
          setHeight(Math.round(24 * density))
        }
      }
    }
    getHeight()
  }, [])

  return height
}

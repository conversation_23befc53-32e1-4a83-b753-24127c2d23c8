import { apiClient } from '../client'

// 剧本购买记录类型
export interface ScriptPurchase {
  id: string
  userId: string
  scriptId: string
  pointsCost: number
  transactionId: string | null
  status: 'completed' | 'refunded'
  expiresAt: string
  isDownloaded: boolean
  downloadedAt: string | null
  createdAt: string
  updatedAt: string
}

// 购买响应类型
export interface PurchaseResponse {
  success: boolean
  data: {
    purchase: ScriptPurchase
    script: {
      id: string
      title: string
      description: string
      coverImage: string
    }
  }
  message: string
}

// 购买状态响应类型
export interface PurchaseStatusResponse {
  success: boolean
  data: {
    isPurchased: boolean
    purchase: ScriptPurchase | null
  }
}

// 已购买剧本列表响应类型
export interface PurchasedScriptsResponse {
  success: boolean
  data: {
    scriptIds: string[]
  }
}

// 剧本内容下载响应类型
export interface ScriptContentResponse {
  success: boolean
  data: {
    id: string
    title: string
    content: any
    audioUrl: string | null
    totalDuration: number | null
    stageCount: number | null
  }
  message: string
}

// 剧本购买API服务类
export class ScriptPurchaseService {
  /**
   * 购买剧本
   */
  static async purchaseScript(scriptId: string): Promise<PurchaseResponse> {
    return apiClient.post<PurchaseResponse>(`/api/scripts/${scriptId}/purchase`)
  }

  /**
   * 检查剧本购买状态
   */
  static async checkPurchaseStatus(scriptId: string): Promise<PurchaseStatusResponse> {
    return apiClient.get<PurchaseStatusResponse>(`/api/scripts/${scriptId}/purchase-status`)
  }

  /**
   * 获取用户已购买的剧本ID列表
   */
  static async getPurchasedScriptIds(): Promise<PurchasedScriptsResponse> {
    return apiClient.get<PurchasedScriptsResponse>('/api/scripts/purchased')
  }

  /**
   * 下载剧本内容
   */
  static async downloadScriptContent(scriptId: string): Promise<ScriptContentResponse> {
    return apiClient.post<ScriptContentResponse>(`/api/scripts/${scriptId}/download`)
  }
}

// 导出默认实例
export const scriptPurchaseService = ScriptPurchaseService

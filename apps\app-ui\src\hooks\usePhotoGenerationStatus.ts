import { useEffect, useRef, useCallback } from 'react'
import { usePhotoGeneration } from '@/stores/photo-generation-store'
import { photoAlbumService } from '@/api/services/photo-album'

// 状态监听配置
interface StatusListenerConfig {
  pollInterval?: number // 轮询间隔（毫秒）
  maxRetries?: number // 最大重试次数
  enableRealtime?: boolean // 是否启用实时监听（WebSocket）
}

// 默认配置
const DEFAULT_CONFIG: StatusListenerConfig = {
  pollInterval: 3000, // 3秒
  maxRetries: 10,
  enableRealtime: false // 暂时使用轮询，后续可以升级为 WebSocket
}

/**
 * 写真集生成状态监听 Hook
 * 支持轮询和实时监听两种模式
 */
export function usePhotoGenerationStatus(config: StatusListenerConfig = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const { currentTask, generationStatus, updateTaskProgress, stopPolling } = usePhotoGeneration()

  const pollTimerRef = useRef<NodeJS.Timeout | null>(null)
  const retryCountRef = useRef(0)
  const isPollingRef = useRef(false)

  // 轮询状态查询
  const pollTaskStatus = useCallback(
    async (taskId: string) => {
      if (isPollingRef.current) return // 防止重复轮询

      try {
        const data = await photoAlbumService.getGenerationStatus(taskId)

        // 更新任务进度
        updateTaskProgress(data.progress || 0, data.status)

        // 重置重试计数
        retryCountRef.current = 0

        // 检查是否完成
        if (data.status === 'completed' || data.status === 'failed') {
          stopPolling()
          isPollingRef.current = false
          return
        }

        // 继续轮询
        if (isPollingRef.current) {
          pollTimerRef.current = setTimeout(() => {
            pollTaskStatus(taskId)
          }, finalConfig.pollInterval)
        }
      } catch (error) {
        console.error('轮询状态失败:', error)

        retryCountRef.current++

        // 检查是否超过最大重试次数
        if (retryCountRef.current >= (finalConfig.maxRetries || 10)) {
          console.error('轮询重试次数超限，停止轮询')
          stopPolling()
          isPollingRef.current = false
          return
        }

        // 延长轮询间隔后重试
        if (isPollingRef.current) {
          const retryInterval = Math.min(
            (finalConfig.pollInterval || 3000) * Math.pow(2, retryCountRef.current),
            30000 // 最大30秒
          )

          pollTimerRef.current = setTimeout(() => {
            pollTaskStatus(taskId)
          }, retryInterval)
        }
      }
    },
    [finalConfig.pollInterval, finalConfig.maxRetries, updateTaskProgress, stopPolling]
  )

  // 开始监听
  const startListening = useCallback(
    (taskId: string) => {
      if (isPollingRef.current) {
        stopListening() // 停止之前的监听
      }

      isPollingRef.current = true
      retryCountRef.current = 0

      if (finalConfig.enableRealtime) {
        // TODO: 实现 WebSocket 实时监听
        console.log('WebSocket 实时监听暂未实现，使用轮询模式')
        pollTaskStatus(taskId)
      } else {
        // 使用轮询模式
        pollTaskStatus(taskId)
      }
    },
    [finalConfig.enableRealtime, pollTaskStatus]
  )

  // 停止监听
  const stopListening = useCallback(() => {
    isPollingRef.current = false

    if (pollTimerRef.current) {
      clearTimeout(pollTimerRef.current)
      pollTimerRef.current = null
    }

    retryCountRef.current = 0
  }, [])

  // 自动监听当前任务
  useEffect(() => {
    if (currentTask?.taskId && generationStatus === 'generating') {
      startListening(currentTask.taskId)
    } else {
      stopListening()
    }

    // 清理函数
    return () => {
      stopListening()
    }
  }, [currentTask?.taskId, generationStatus, startListening, stopListening])

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopListening()
    }
  }, [stopListening])

  return {
    isListening: isPollingRef.current,
    retryCount: retryCountRef.current,
    startListening,
    stopListening
  }
}

/**
 * WebSocket 实时监听实现（预留）
 * 可以在后续版本中实现
 */
export function useRealtimePhotoGeneration() {
  const { updateTaskProgress } = usePhotoGeneration()
  const wsRef = useRef<WebSocket | null>(null)

  const connect = useCallback(
    (taskId: string) => {
      // TODO: 实现 WebSocket 连接
      // const ws = new WebSocket(`wss://api.example.com/photo-generation/${taskId}`)
      //
      // ws.onmessage = (event) => {
      //   const data = JSON.parse(event.data)
      //   updateTaskProgress(data.progress, data.status)
      // }
      //
      // ws.onerror = (error) => {
      //   console.error('WebSocket 错误:', error)
      // }
      //
      // wsRef.current = ws

      console.log('WebSocket 实时监听暂未实现')
    },
    [updateTaskProgress]
  )

  const disconnect = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close()
      wsRef.current = null
    }
  }, [])

  useEffect(() => {
    return () => {
      disconnect()
    }
  }, [disconnect])

  return {
    connect,
    disconnect,
    isConnected: wsRef.current?.readyState === WebSocket.OPEN
  }
}

/**
 * 生成状态变化监听 Hook
 * 用于监听生成状态的变化并执行相应的回调
 */
export function useGenerationStatusChange(onStatusChange?: (status: string, task: any) => void) {
  const { generationStatus, currentTask } = usePhotoGeneration()
  const prevStatusRef = useRef(generationStatus)

  useEffect(() => {
    if (prevStatusRef.current !== generationStatus && onStatusChange) {
      onStatusChange(generationStatus, currentTask)
      prevStatusRef.current = generationStatus
    }
  }, [generationStatus, currentTask, onStatusChange])

  return {
    currentStatus: generationStatus,
    previousStatus: prevStatusRef.current
  }
}

/**
 * 生成进度监听 Hook
 * 用于监听生成进度的变化
 */
export function useGenerationProgress(onProgressChange?: (progress: number, task: any) => void) {
  const { currentTask } = usePhotoGeneration()
  const prevProgressRef = useRef(currentTask?.progress || 0)

  useEffect(() => {
    const currentProgress = currentTask?.progress || 0
    if (prevProgressRef.current !== currentProgress && onProgressChange) {
      onProgressChange(currentProgress, currentTask)
      prevProgressRef.current = currentProgress
    }
  }, [currentTask?.progress, currentTask, onProgressChange])

  return {
    currentProgress: currentTask?.progress || 0,
    previousProgress: prevProgressRef.current,
    estimatedSteps: currentTask?.estimatedSteps,
    completedSteps: currentTask?.completedSteps
  }
}

/**
 * 组合 Hook：完整的生成监听
 * 包含状态监听、进度监听和错误处理
 */
export function useCompleteGenerationMonitor(
  config?: StatusListenerConfig & {
    onComplete?: (task: any) => void
    onFailed?: (task: any) => void
  }
) {
  const statusListener = usePhotoGenerationStatus(config)

  const statusChange = useGenerationStatusChange((status, task) => {
    console.log(`生成状态变化: ${status}`, task)

    // 调用回调函数
    if (status === 'completed' && config?.onComplete) {
      config.onComplete(task)
    } else if (status === 'failed' && config?.onFailed) {
      config.onFailed(task)
    }
  })

  const progressChange = useGenerationProgress((progress, task) => {
    console.log(`生成进度更新: ${progress}%`, task)
  })

  return {
    ...statusListener,
    ...statusChange,
    ...progressChange
  }
}

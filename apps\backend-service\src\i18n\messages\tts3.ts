// TTS3 相关的国际化消息
export const tts3Messages = {
  zh: {
    // 错误消息
    'tts3.user_not_found': '未找到用户信息',
    'tts3.user_not_exist': '用户不存在',
    'tts3.r2_config_incomplete': 'R2配置不完整',
    'tts3.upload_temp_audio_failed': '上传临时音频文件失败',
    'tts3.tts_generation_failed': 'TTS3 生成音频失败',
    'tts3.both_services_failed': '主要 TTS 服务和 Fish Audio 降级服务都失败了',
    'tts3.health_check_failed': '健康检查失败',
    'tts3.service_status_failed': '获取集成服务状态失败',
    'tts3.cache_stats_failed': '获取缓存统计失败',
    'tts3.service_stats_failed': '获取服务统计失败',
    'tts3.fallback_audio_failed': '无法获取降级音频文件',

    // 日志和状态消息
    'tts3.audio_stream_completed': '🎵 音频流传输完成',
    'tts3.audio_stats': '📊 总计: {chunks} 个块, {bytes} bytes',
    'tts3.submit_audio_start': '🔄 开始提交音频数据到队列...',
    'tts3.audio_data_size': '📊 音频数据大小: {size} bytes',
    'tts3.audio_uploaded': '📤 音频已上传到临时存储: {url}',
    'tts3.task_submitted': '✅ 音频任务已提交到队列: {taskId}',
    'tts3.submit_task_failed': '❌ 提交音频任务到队列失败: {error}',
    'tts3.tts_generation_start': '🚀 开始极速 TTS 生成，文本长度: {length}',
    'tts3.message_chat_info': '📝 消息ID: {messageId} 💬 聊天ID: {chatId}',
    'tts3.using_integrated_service': '⚡ 使用集成 TTS 服务...',
    'tts3.integrated_service_success': '✅ 集成服务响应成功',
    'tts3.fallback_to_fish_audio': '🔄 集成 TTS 服务失败，切换到 Fish Audio 降级: {error}',
    'tts3.using_fish_audio': '使用 Fish Audio 降级服务生成音频...',
    'tts3.fish_audio_completed': 'Fish Audio 生成完成，音频URL: {url}',
    'tts3.fish_audio_failed': 'Fish Audio 降级也失败: {error}',
    'tts3.checking_health': '检查集成 TTS 服务健康状态...',
    'tts3.getting_service_status': '获取集成 TTS 服务详细状态...'
  },
  'zh-TW': {
    // 錯誤訊息
    'tts3.user_not_found': '未找到使用者資訊',
    'tts3.user_not_exist': '使用者不存在',
    'tts3.r2_config_incomplete': 'R2配置不完整',
    'tts3.upload_temp_audio_failed': '上傳臨時音訊檔案失敗',
    'tts3.tts_generation_failed': 'TTS3 生成音訊失敗',
    'tts3.both_services_failed': '主要 TTS 服務和 Fish Audio 降級服務都失敗了',
    'tts3.health_check_failed': '健康檢查失敗',
    'tts3.service_status_failed': '取得整合服務狀態失敗',
    'tts3.cache_stats_failed': '取得快取統計失敗',
    'tts3.service_stats_failed': '取得服務統計失敗',
    'tts3.fallback_audio_failed': '無法取得降級音訊檔案',

    // 日誌和狀態訊息
    'tts3.audio_stream_completed': '🎵 音訊流傳輸完成',
    'tts3.audio_stats': '📊 總計: {chunks} 個塊, {bytes} bytes',
    'tts3.submit_audio_start': '🔄 開始提交音訊資料到佇列...',
    'tts3.audio_data_size': '📊 音訊資料大小: {size} bytes',
    'tts3.audio_uploaded': '📤 音訊已上傳到臨時儲存: {url}',
    'tts3.task_submitted': '✅ 音訊任務已提交到佇列: {taskId}',
    'tts3.submit_task_failed': '❌ 提交音訊任務到佇列失敗: {error}',
    'tts3.tts_generation_start': '🚀 開始極速 TTS 生成，文字長度: {length}',
    'tts3.message_chat_info': '📝 訊息ID: {messageId} 💬 聊天ID: {chatId}',
    'tts3.using_integrated_service': '⚡ 使用整合 TTS 服務...',
    'tts3.integrated_service_success': '✅ 整合服務回應成功',
    'tts3.fallback_to_fish_audio': '🔄 整合 TTS 服務失敗，切換到 Fish Audio 降級: {error}',
    'tts3.using_fish_audio': '使用 Fish Audio 降級服務生成音訊...',
    'tts3.fish_audio_completed': 'Fish Audio 生成完成，音訊URL: {url}',
    'tts3.fish_audio_failed': 'Fish Audio 降級也失敗: {error}',
    'tts3.checking_health': '檢查整合 TTS 服務健康狀態...',
    'tts3.getting_service_status': '取得整合 TTS 服務詳細狀態...'
  },
  ja: {
    // エラーメッセージ
    'tts3.user_not_found': 'ユーザー情報が見つかりません',
    'tts3.user_not_exist': 'ユーザーが存在しません',
    'tts3.r2_config_incomplete': 'R2設定が不完全です',
    'tts3.upload_temp_audio_failed': '一時音声ファイルのアップロードに失敗しました',
    'tts3.tts_generation_failed': 'TTS3 音声生成に失敗しました',
    'tts3.both_services_failed':
      'メインTTSサービスとFish Audioフォールバックサービスの両方が失敗しました',
    'tts3.health_check_failed': 'ヘルスチェックに失敗しました',
    'tts3.service_status_failed': '統合サービスステータスの取得に失敗しました',
    'tts3.cache_stats_failed': 'キャッシュ統計の取得に失敗しました',
    'tts3.service_stats_failed': 'サービス統計の取得に失敗しました',
    'tts3.fallback_audio_failed': 'フォールバック音声ファイルを取得できません',

    // ログとステータスメッセージ
    'tts3.audio_stream_completed': '🎵 音声ストリーム転送完了',
    'tts3.audio_stats': '📊 合計: {chunks} チャンク, {bytes} bytes',
    'tts3.submit_audio_start': '🔄 音声データのキューへの送信を開始...',
    'tts3.audio_data_size': '📊 音声データサイズ: {size} bytes',
    'tts3.audio_uploaded': '📤 音声が一時ストレージにアップロードされました: {url}',
    'tts3.task_submitted': '✅ 音声タスクがキューに送信されました: {taskId}',
    'tts3.submit_task_failed': '❌ 音声タスクのキューへの送信に失敗しました: {error}',
    'tts3.tts_generation_start': '🚀 高速TTS生成を開始、テキスト長: {length}',
    'tts3.message_chat_info': '📝 メッセージID: {messageId} 💬 チャットID: {chatId}',
    'tts3.using_integrated_service': '⚡ 統合TTSサービスを使用中...',
    'tts3.integrated_service_success': '✅ 統合サービスの応答が成功しました',
    'tts3.fallback_to_fish_audio':
      '🔄 統合TTSサービスが失敗、Fish Audioフォールバックに切り替え: {error}',
    'tts3.using_fish_audio': 'Fish Audioフォールバックサービスを使用して音声を生成中...',
    'tts3.fish_audio_completed': 'Fish Audio生成完了、音声URL: {url}',
    'tts3.fish_audio_failed': 'Fish Audioフォールバックも失敗: {error}',
    'tts3.checking_health': '統合TTSサービスの健康状態を確認中...',
    'tts3.getting_service_status': '統合TTSサービスの詳細ステータスを取得中...'
  },
  es: {
    // Mensajes de error
    'tts3.user_not_found': 'No se encontró la información del usuario',
    'tts3.user_not_exist': 'El usuario no existe',
    'tts3.r2_config_incomplete': 'Configuración R2 incompleta',
    'tts3.upload_temp_audio_failed': 'Error al subir archivo de audio temporal',
    'tts3.tts_generation_failed': 'Error en la generación de audio TTS3',
    'tts3.both_services_failed':
      'Tanto el servicio TTS principal como el servicio de respaldo Fish Audio fallaron',
    'tts3.health_check_failed': 'Error en la verificación de salud',
    'tts3.service_status_failed': 'Error al obtener el estado del servicio integrado',
    'tts3.cache_stats_failed': 'Error al obtener estadísticas de caché',
    'tts3.service_stats_failed': 'Error al obtener estadísticas del servicio',
    'tts3.fallback_audio_failed': 'No se puede obtener el archivo de audio de respaldo',

    // Mensajes de registro y estado
    'tts3.audio_stream_completed': '🎵 Transmisión de flujo de audio completada',
    'tts3.audio_stats': '📊 Total: {chunks} fragmentos, {bytes} bytes',
    'tts3.submit_audio_start': '🔄 Comenzando a enviar datos de audio a la cola...',
    'tts3.audio_data_size': '📊 Tamaño de datos de audio: {size} bytes',
    'tts3.audio_uploaded': '📤 Audio subido al almacenamiento temporal: {url}',
    'tts3.task_submitted': '✅ Tarea de audio enviada a la cola: {taskId}',
    'tts3.submit_task_failed': '❌ Error al enviar tarea de audio a la cola: {error}',
    'tts3.tts_generation_start': '🚀 Iniciando generación TTS rápida, longitud de texto: {length}',
    'tts3.message_chat_info': '📝 ID de mensaje: {messageId} 💬 ID de chat: {chatId}',
    'tts3.using_integrated_service': '⚡ Usando servicio TTS integrado...',
    'tts3.integrated_service_success': '✅ Respuesta del servicio integrado exitosa',
    'tts3.fallback_to_fish_audio':
      '🔄 Servicio TTS integrado falló, cambiando a respaldo Fish Audio: {error}',
    'tts3.using_fish_audio': 'Usando servicio de respaldo Fish Audio para generar audio...',
    'tts3.fish_audio_completed': 'Generación Fish Audio completada, URL de audio: {url}',
    'tts3.fish_audio_failed': 'El respaldo Fish Audio también falló: {error}',
    'tts3.checking_health': 'Verificando estado de salud del servicio TTS integrado...',
    'tts3.getting_service_status': 'Obteniendo estado detallado del servicio TTS integrado...'
  },
  en: {
    // Error messages
    'tts3.user_not_found': 'User information not found',
    'tts3.user_not_exist': 'User does not exist',
    'tts3.r2_config_incomplete': 'R2 configuration incomplete',
    'tts3.upload_temp_audio_failed': 'Failed to upload temporary audio file',
    'tts3.tts_generation_failed': 'TTS3 audio generation failed',
    'tts3.both_services_failed': 'Both primary TTS service and Fish Audio fallback service failed',
    'tts3.health_check_failed': 'Health check failed',
    'tts3.service_status_failed': 'Failed to get integrated service status',
    'tts3.cache_stats_failed': 'Failed to get cache statistics',
    'tts3.service_stats_failed': 'Failed to get service statistics',
    'tts3.fallback_audio_failed': 'Unable to get fallback audio file',

    // Log and status messages
    'tts3.audio_stream_completed': '🎵 Audio stream transmission completed',
    'tts3.audio_stats': '📊 Total: {chunks} chunks, {bytes} bytes',
    'tts3.submit_audio_start': '🔄 Starting to submit audio data to queue...',
    'tts3.audio_data_size': '📊 Audio data size: {size} bytes',
    'tts3.audio_uploaded': '📤 Audio uploaded to temporary storage: {url}',
    'tts3.task_submitted': '✅ Audio task submitted to queue: {taskId}',
    'tts3.submit_task_failed': '❌ Failed to submit audio task to queue: {error}',
    'tts3.tts_generation_start': '🚀 Starting fast TTS generation, text length: {length}',
    'tts3.message_chat_info': '📝 Message ID: {messageId} 💬 Chat ID: {chatId}',
    'tts3.using_integrated_service': '⚡ Using integrated TTS service...',
    'tts3.integrated_service_success': '✅ Integrated service response successful',
    'tts3.fallback_to_fish_audio':
      '🔄 Integrated TTS service failed, switching to Fish Audio fallback: {error}',
    'tts3.using_fish_audio': 'Using Fish Audio fallback service to generate audio...',
    'tts3.fish_audio_completed': 'Fish Audio generation completed, audio URL: {url}',
    'tts3.fish_audio_failed': 'Fish Audio fallback also failed: {error}',
    'tts3.checking_health': 'Checking integrated TTS service health status...',
    'tts3.getting_service_status': 'Getting detailed status of integrated TTS service...'
  }
}

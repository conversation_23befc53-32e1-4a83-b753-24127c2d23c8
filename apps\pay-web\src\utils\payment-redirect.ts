/**
 * 支付平台跳转逻辑
 */

import type { PaymentOrderInfo } from '@/types/payment'

/**
 * 支付宝跳转参数
 */
interface AlipayParams {
  orderNo: string
  amount: string
  subject: string
  body?: string
  returnUrl?: string
}

/**
 * 微信支付跳转参数
 */
interface WechatParams {
  orderNo: string
  amount: string
  description: string
  returnUrl?: string
}

/**
 * 构建支付宝跳转URL
 */
export function buildAlipayUrl(params: AlipayParams): string {
  // 在实际环境中，这里应该调用后端API获取真正的支付宝支付URL
  // 目前返回模拟的支付宝支付页面
  const queryParams = new URLSearchParams({
    orderNo: params.orderNo,
    amount: params.amount,
    subject: params.subject,
    body: params.body || params.subject,
    returnUrl: params.returnUrl || window.location.origin + '/pay/result'
  })

  // 注意：这里是模拟URL，实际应该是从后端API获取的真实支付宝URL
  return `https://openapi.alipay.com/gateway.do?${queryParams.toString()}`
}

/**
 * 构建微信支付跳转URL
 */
export function buildWechatUrl(params: WechatParams): string {
  // 在实际环境中，这里应该调用后端API获取真正的微信支付URL
  const queryParams = new URLSearchParams({
    orderNo: params.orderNo,
    amount: params.amount,
    description: params.description,
    returnUrl: params.returnUrl || window.location.origin + '/pay/result'
  })

  // 注意：这里是模拟URL，实际应该是从后端API获取的真实微信支付URL
  return `https://pay.weixin.qq.com/index.php/public/wechatpay_h5?${queryParams.toString()}`
}

/**
 * 根据订单信息跳转到对应的支付平台
 */
export function redirectToPayment(orderInfo: PaymentOrderInfo): void {
  console.log('💳 [PAYMENT-REDIRECT] 准备跳转到支付平台:', {
    paymentMethod: orderInfo.paymentMethod,
    orderId: orderInfo.orderId,
    amount: orderInfo.amount
  })

  const returnUrl = `${window.location.origin}/pay/result?orderId=${orderInfo.orderId}`

  try {
    switch (orderInfo.paymentMethod) {
      case 'alipay': {
        const alipayUrl = buildAlipayUrl({
          orderNo: orderInfo.orderId,
          amount: orderInfo.amount.toFixed(2),
          subject: orderInfo.description,
          body: orderInfo.planInfo?.description || orderInfo.description,
          returnUrl
        })
        
        console.log('🏦 [ALIPAY] 跳转到支付宝:', alipayUrl.substring(0, 100) + '...')
        window.location.href = alipayUrl
        break
      }

      case 'wechat': {
        const wechatUrl = buildWechatUrl({
          orderNo: orderInfo.orderId,
          amount: orderInfo.amount.toFixed(2),
          description: orderInfo.description,
          returnUrl
        })
        
        console.log('💚 [WECHAT] 跳转到微信支付:', wechatUrl.substring(0, 100) + '...')
        window.location.href = wechatUrl
        break
      }

      default:
        throw new Error(`不支持的支付方式: ${orderInfo.paymentMethod}`)
    }
  } catch (error) {
    console.error('❌ [PAYMENT-REDIRECT] 跳转失败:', error)
    throw error
  }
}

/**
 * 检查是否从支付平台返回
 */
export function isFromPaymentReturn(): boolean {
  const urlParams = new URLSearchParams(window.location.search)
  const referrer = document.referrer.toLowerCase()
  
  // 检查URL参数或来源页面
  return (
    urlParams.has('orderId') ||
    referrer.includes('alipay.com') ||
    referrer.includes('tenpay.com') ||
    referrer.includes('weixin.qq.com')
  )
}

/**
 * 从URL获取支付结果参数
 */
export function getPaymentResultFromUrl(): {
  orderId?: string
  status?: string
  amount?: string
  [key: string]: string | undefined
} {
  const urlParams = new URLSearchParams(window.location.search)
  const result: { [key: string]: string | undefined } = {}
  
  // 支付宝常见返回参数
  const alipayParams = [
    'out_trade_no', 'trade_no', 'total_amount', 'trade_status',
    'gmt_create', 'gmt_payment', 'seller_id', 'buyer_id'
  ]
  
  // 微信常见返回参数
  const wechatParams = [
    'orderId', 'transaction_id', 'total_fee', 'result_code',
    'time_end', 'mch_id', 'openid'
  ]
  
  // 通用参数
  const commonParams = ['orderId', 'status', 'amount']
  
  const allParams = [...new Set([...alipayParams, ...wechatParams, ...commonParams])]
  
  allParams.forEach(param => {
    const value = urlParams.get(param)
    if (value) {
      result[param] = value
    }
  })
  
  // 标准化订单号
  if (result.out_trade_no && !result.orderId) {
    result.orderId = result.out_trade_no
  }
  
  console.log('📋 [PAYMENT-RESULT] 从URL解析支付结果:', result)
  return result
}

/**
 * 检测支付方式
 */
export function detectPaymentMethod(): 'alipay' | 'wechat' | 'unknown' {
  const referrer = document.referrer.toLowerCase()
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (referrer.includes('alipay.com')) {
    return 'alipay'
  } else if (referrer.includes('tenpay.com') || referrer.includes('weixin.qq.com')) {
    return 'wechat'
  } else if (userAgent.includes('alipay')) {
    return 'alipay'
  } else if (userAgent.includes('micromessenger')) {
    return 'wechat'
  } else {
    return 'unknown'
  }
}
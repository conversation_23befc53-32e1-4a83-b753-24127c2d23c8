import { getSupabase } from './base'
import { handleSupabaseResult, handleSupabaseSingleResult, TABLE_NAMES } from '../supabase-types'
import type { AppVersion, AppUpdatePolicy, AppUpdateLog } from '../schema'
import type { Env } from '@/types/env'

// ==================== 应用版本管理 ====================

/**
 * 获取最新的应用版本（按版本类型）
 */
export async function getLatestAppVersion(
  env: Env,
  versionType: 'apk' | 'hotfix'
): Promise<AppVersion | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.appVersion)
      .select('*')
      .eq('version_type', versionType)
      .eq('is_active', true)
      .order('version_code', { ascending: false })
      .limit(1)
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) {
      console.log(`获取最新${versionType}版本失败:`, error)
      return null
    }
    return data
  } catch (error) {
    console.error(`获取最新${versionType}版本异常:`, error)
    return null
  }
}

/**
 * 根据版本号获取应用版本
 */
export async function getAppVersionByVersionCode(
  env: Env,
  versionCode: number,
  versionType: 'apk' | 'hotfix'
): Promise<AppVersion | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.appVersion)
      .select('*')
      .eq('version_code', versionCode)
      .eq('version_type', versionType)
      .eq('is_active', true)
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) return null
    return data
  } catch (error) {
    console.error('根据版本号获取应用版本失败:', error)
    return null
  }
}

/**
 * 创建新的应用版本
 */
export async function createAppVersion(
  env: Env,
  versionData: {
    versionName: string
    versionCode: number
    versionType: 'apk' | 'hotfix'
    fileUrl: string
    fileSize?: number
    fileHash?: string
    minCompatibleVersion?: string
    releaseNotes?: string
  }
): Promise<AppVersion> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.appVersion)
      .insert({
        version_name: versionData.versionName,
        version_code: versionData.versionCode,
        version_type: versionData.versionType,
        file_url: versionData.fileUrl,
        file_size: versionData.fileSize || null,
        file_hash: versionData.fileHash || null,
        min_compatible_version: versionData.minCompatibleVersion || null,
        release_notes: versionData.releaseNotes || null
      })
      .select('*')
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) throw error
    return data
  } catch (error) {
    console.error('创建应用版本失败:', error)
    throw error
  }
}

/**
 * 获取应用版本列表（用于管理后台）
 */
export async function getAppVersions(
  env: Env,
  options: {
    versionType?: 'apk' | 'hotfix'
    limit?: number
    offset?: number
  } = {}
): Promise<AppVersion[]> {
  try {
    const supabase = getSupabase(env)
    const { versionType, limit = 20, offset = 0 } = options

    let query = supabase
      .from(TABLE_NAMES.appVersion)
      .select('*')
      .order('created_at', { ascending: false })

    if (versionType) {
      query = query.eq('version_type', versionType)
    }

    const result = await query.range(offset, offset + limit - 1)

    const { data, error } = handleSupabaseResult(result)
    if (error) throw error
    return data || []
  } catch (error) {
    console.error('获取应用版本列表失败:', error)
    throw error
  }
}

// ==================== 更新策略管理 ====================

/**
 * 获取版本的更新策略
 */
export async function getUpdatePolicy(
  env: Env,
  versionId: string,
  channel = 'production'
): Promise<AppUpdatePolicy | null> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.appUpdatePolicy)
      .select('*')
      .eq('version_id', versionId)
      .eq('channel', channel)
      .eq('is_active', true)
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) return null
    return data
  } catch (error) {
    console.error('获取更新策略失败:', error)
    return null
  }
}

/**
 * 创建更新策略
 */
export async function createUpdatePolicy(
  env: Env,
  policyData: {
    versionId: string
    channel?: string
    updateStrategy: 'force' | 'optional' | 'silent'
    targetVersionMin?: string
    targetVersionMax?: string
    rolloutPercentage?: number
  }
): Promise<AppUpdatePolicy> {
  try {
    const supabase = getSupabase(env)
    const result = await supabase
      .from(TABLE_NAMES.appUpdatePolicy)
      .insert({
        version_id: policyData.versionId,
        channel: policyData.channel || 'production',
        update_strategy: policyData.updateStrategy,
        target_version_min: policyData.targetVersionMin || null,
        target_version_max: policyData.targetVersionMax || null,
        rollout_percentage: policyData.rolloutPercentage || 100
      })
      .select('*')
      .single()

    const { data, error } = handleSupabaseSingleResult(result)
    if (error) throw error
    return data
  } catch (error) {
    console.error('创建更新策略失败:', error)
    throw error
  }
}

/**
 * 检查版本是否需要更新
 */
export async function checkForUpdates(
  env: Env,
  currentVersion: string,
  versionType: 'apk' | 'hotfix',
  channel = 'production'
): Promise<{
  hasUpdate: boolean
  version?: AppVersion
  policy?: AppUpdatePolicy
}> {
  try {
    // 获取最新版本
    const latestVersion = await getLatestAppVersion(env, versionType)
    if (!latestVersion) {
      return { hasUpdate: false }
    }

    // 比较版本
    const currentVersionCode = Number.parseInt(currentVersion.split('.').join(''))
    const latestVersionCode = latestVersion.versionCode

    if (latestVersionCode <= currentVersionCode) {
      return { hasUpdate: false }
    }

    // 获取更新策略
    const policy = await getUpdatePolicy(env, latestVersion.id, channel)

    return {
      hasUpdate: true,
      version: latestVersion,
      policy: policy || undefined
    }
  } catch (error) {
    console.error('检查更新失败:', error)
    return { hasUpdate: false }
  }
}

// ==================== 更新日志管理 ====================

/**
 * 记录更新日志
 */
export async function logUpdateEvent(
  env: Env,
  logData: {
    userId?: string
    deviceId?: string
    currentVersion?: string
    targetVersion?: string
    updateType: 'apk' | 'hotfix'
    updateStatus:
      | 'started'
      | 'downloading'
      | 'downloaded'
      | 'installing'
      | 'installed'
      | 'failed'
      | 'cancelled'
    errorMessage?: string
    metadata?: any
  }
): Promise<void> {
  try {
    const supabase = getSupabase(env)
    await supabase.from(TABLE_NAMES.appUpdateLog).insert({
      user_id: logData.userId || null,
      device_id: logData.deviceId || null,
      current_version: logData.currentVersion || null,
      target_version: logData.targetVersion || null,
      update_type: logData.updateType,
      update_status: logData.updateStatus,
      error_message: logData.errorMessage || null,
      metadata: logData.metadata || {}
    })
  } catch (error) {
    console.error('记录更新日志失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}

/**
 * 获取更新统计
 */
export async function getUpdateStats(
  env: Env,
  options: {
    versionId?: string
    updateType?: 'apk' | 'hotfix'
    dateFrom?: string
    dateTo?: string
  } = {}
): Promise<{
  totalUpdates: number
  successfulUpdates: number
  failedUpdates: number
  statusBreakdown: Record<string, number>
}> {
  try {
    const supabase = getSupabase(env)
    let query = supabase.from(TABLE_NAMES.appUpdateLog).select('update_status')

    if (options.updateType) {
      query = query.eq('update_type', options.updateType)
    }

    if (options.dateFrom) {
      query = query.gte('created_at', options.dateFrom)
    }

    if (options.dateTo) {
      query = query.lte('created_at', options.dateTo)
    }

    const result = await query
    const { data, error } = handleSupabaseResult(result)
    if (error) throw error

    const logs = data || []
    const statusBreakdown: Record<string, number> = {}
    let successfulUpdates = 0
    let failedUpdates = 0

    for (const log of logs) {
      const status = log.updateStatus
      statusBreakdown[status] = (statusBreakdown[status] || 0) + 1

      if (status === 'installed') {
        successfulUpdates++
      } else if (status === 'failed' || status === 'cancelled') {
        failedUpdates++
      }
    }

    return {
      totalUpdates: logs.length,
      successfulUpdates,
      failedUpdates,
      statusBreakdown
    }
  } catch (error) {
    console.error('获取更新统计失败:', error)
    return {
      totalUpdates: 0,
      successfulUpdates: 0,
      failedUpdates: 0,
      statusBreakdown: {}
    }
  }
}

import type { Env } from '@/types/env';
import type { YunwuApiResponse } from '@/types/image-generation';
import { downloadAndUploadImage, uploadBase64Image } from '../upload';

/**
 * 背景图生成器
 * 基于云雾AI，专门用于生成聊天背景图
 * 尺寸：9:16 (1080x1920)
 */
export class BackgroundImageGenerator {
  constructor(private env: Env) {}

  /**
   * 生成背景图
   */
  async generateBackgroundImage(prompt: string): Promise<string> {
    if (!this.env.YUNWU_API_KEY) {
      throw new Error('云雾AI API密钥未配置');
    }

    try {
      // 调用云雾AI API，使用9:16尺寸
      const response = await fetch('https://ark.cn-beijing.volces.com/api/v3/images/generations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.env.YUNWU_API_KEY}`,
        },
        body: JSON.stringify({
          prompt: prompt,
          n: 1,
          size: '1080x1920', // 9:16 背景图尺寸
          model: 'doubao-seedream-3-0-t2i-250415',
          response_format: 'url',
          watermark: false,
        }),
      });

      if (!response.ok) {
        throw new Error(`云雾AI API请求失败: ${response.statusText}`);
      }

      const data: YunwuApiResponse = await response.json();

      // 处理返回的数据
      if (data.data?.[0]?.url) {
        // 如果返回的是URL，下载并上传到R2
        return await downloadAndUploadImage(this.env, data.data[0].url);
      } else if (data.data?.[0]?.b64_json) {
        // 如果返回的是base64数据，直接上传到R2
        return await uploadBase64Image(this.env, data.data[0].b64_json);
      } else {
        throw new Error('云雾AI返回数据格式错误');
      }
    } catch (error) {
      console.error('背景图生成失败:', error);
      throw new Error('生成背景图失败');
    }
  }
}

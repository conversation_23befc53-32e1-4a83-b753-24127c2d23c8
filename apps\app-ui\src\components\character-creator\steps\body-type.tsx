import React, { useMemo } from 'react'
import { Chip, Card, CardBody, Image } from '@heroui/react'
import type { CharacterData } from '..'
import { characterMapping } from '../mapping'

// 男性身体类型选项
const maleBodyTypes = Object.entries(characterMapping.bodyType.male).map(
  ([value, label], index) => ({
    value,
    label,
    pos: index + 1
  })
)

// 女性身体类型选项
const femaleBodyTypes = Object.entries(characterMapping.bodyType.female).map(
  ([value, label], index) => ({
    value,
    label,
    pos: index + 1
  })
)

// 女性胸部尺寸选项
const breastSizeOptions = Object.entries(characterMapping.breastSize).map(
  ([value, label], index) => ({
    value,
    label,
    pos: index + 1
  })
)

// 女性臀部尺寸选项
const buttSizeOptions = Object.entries(characterMapping.buttSize).map(([value, label], index) => ({
  value,
  label,
  pos: index + 1
}))

interface BodyTypeProps {
  data: CharacterData
  onUpdate: (data: Partial<CharacterData>) => void
}

// 对应每个section的组件
const Section: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => (
  <div className="mb-4">
    <h3 className="font-medium text-center text-md mb-3 text-primary">{title}</h3>
    {children}
  </div>
)

// 创建一个分布式布局组件
const DistributedGrid: React.FC<{
  items: any[]
  renderItem: (item: any, index: number) => React.ReactNode
}> = ({ items, renderItem }) => {
  if (items.length <= 3) {
    // 如果项目数量≤3，则单行显示
    return <div className="grid grid-cols-3 gap-3">{items.map(renderItem)}</div>
  } else if (items.length === 4) {
    // 如果是4项，则2行2列
    return <div className="grid grid-cols-2 gap-3">{items.map(renderItem)}</div>
  } else if (items.length === 5) {
    // 如果是5项，则第一行2项，第二行3项
    const firstRow = items.slice(0, 2)
    const secondRow = items.slice(2, 5)

    return (
      <>
        <div className="grid grid-cols-2 gap-3 mb-3">
          {firstRow.map((item, index) => renderItem(item, index))}
        </div>
        <div className="grid grid-cols-3 gap-3">
          {secondRow.map((item, index) => renderItem(item, index + 2))}
        </div>
      </>
    )
  } else {
    // 更多项目，使用网格自动排列
    return <div className="grid grid-cols-3 gap-3">{items.map(renderItem)}</div>
  }
}

export default function BodyType({ data, onUpdate }: BodyTypeProps) {
  // 直接使用gender字段，如果没有则默认为female
  const gender = useMemo(() => {
    const currentGender = (data.gender as 'male' | 'female') || 'female'
    console.log(`身体特征组件检测到性别: ${currentGender}, gender字段: ${data.gender}`)
    return currentGender
  }, [data.gender])

  const bodyTypeOptions = gender === 'female' ? femaleBodyTypes : maleBodyTypes

  return (
    <div className="space-y-4">
      {/* 身体类型选择 */}
      <Section title="选择体型">
        <div role="radiogroup" aria-label="选择身体类型">
          <DistributedGrid
            items={bodyTypeOptions}
            renderItem={option => {
              const fileNumber = option.pos < 10 ? `0${option.pos}` : option.pos
              const imageFile = `${gender === 'female' ? 'women' : 'male'}-body_${fileNumber}`

              return (
                <Card
                  key={option.value}
                  isPressable
                  isHoverable
                  onPress={() => onUpdate({ bodyType: option.value })}
                  className={`
                    cursor-pointer transition-all duration-200
                    ${
                      data.bodyType === option.value
                        ? 'ring-2 ring-primary ring-offset-2 ring-offset-background'
                        : ''
                    }
                  `}
                  role="radio"
                  aria-checked={data.bodyType === option.value}
                  aria-label={`选择${option.label}体型`}
                >
                  <CardBody className="p-0 relative overflow-hidden">
                    <div className="aspect-square bg-gradient-to-b from-default-100/30 to-transparent">
                      <Image
                        src={`/images/custom/${imageFile}.png`}
                        alt={option.label}
                        className="w-full h-full object-cover transition-transform hover:scale-105"
                        classNames={{
                          wrapper: 'w-full h-full'
                        }}
                      />
                    </div>
                    <Chip
                      color={data.bodyType === option.value ? 'primary' : 'default'}
                      variant={data.bodyType === option.value ? 'solid' : 'flat'}
                      className="absolute bottom-2 right-2 z-10"
                      size="sm"
                      classNames={{
                        content:
                          data.bodyType === option.value
                            ? 'text-white font-medium'
                            : 'text-foreground'
                      }}
                    >
                      {option.label}
                    </Chip>
                  </CardBody>
                </Card>
              )
            }}
          />
        </div>
      </Section>

      {/* 女性特有的选项 */}
      {gender === 'female' && (
        <>
          {/* 胸部尺寸选择 */}
          <Section title="胸部尺寸">
            <div role="radiogroup" aria-label="选择胸部尺寸">
              <DistributedGrid
                items={breastSizeOptions}
                renderItem={option => {
                  const fileNumber = option.pos < 10 ? `0${option.pos}` : option.pos
                  const imageFile = `women-breast_${fileNumber}`

                  return (
                    <Card
                      key={option.value}
                      isPressable
                      isHoverable
                      onPress={() => onUpdate({ breastSize: option.value })}
                      className={`
                        cursor-pointer transition-all duration-200
                        ${
                          data.breastSize === option.value
                            ? 'ring-2 ring-primary ring-offset-2 ring-offset-background'
                            : ''
                        }
                      `}
                      role="radio"
                      aria-checked={data.breastSize === option.value}
                      aria-label={`选择${option.label}胸部尺寸`}
                    >
                      <CardBody className="p-0 relative overflow-hidden">
                        <div className="aspect-square bg-gradient-to-b from-default-100/30 to-transparent">
                          <Image
                            src={`/images/custom/${imageFile}.png`}
                            alt={option.label}
                            className="w-full h-full object-cover transition-transform hover:scale-105"
                            classNames={{
                              wrapper: 'w-full h-full'
                            }}
                          />
                        </div>
                        <Chip
                          color={data.breastSize === option.value ? 'primary' : 'default'}
                          variant={data.breastSize === option.value ? 'solid' : 'flat'}
                          className="absolute bottom-2 right-2 z-10"
                          size="sm"
                          classNames={{
                            content:
                              data.breastSize === option.value
                                ? 'text-white font-medium'
                                : 'text-foreground'
                          }}
                        >
                          {option.label}
                        </Chip>
                      </CardBody>
                    </Card>
                  )
                }}
              />
            </div>
          </Section>

          {/* 臀部尺寸选择 */}
          <Section title="臀部尺寸">
            <div role="radiogroup" aria-label="选择臀部尺寸">
              <DistributedGrid
                items={buttSizeOptions}
                renderItem={option => {
                  const fileNumber = option.pos < 10 ? `0${option.pos}` : option.pos
                  const imageFile = `women-butt_${fileNumber}`

                  return (
                    <Card
                      key={option.value}
                      isPressable
                      isHoverable
                      onPress={() => onUpdate({ buttSize: option.value })}
                      className={`
                        cursor-pointer transition-all duration-200
                        ${
                          data.buttSize === option.value
                            ? 'ring-2 ring-primary ring-offset-2 ring-offset-background'
                            : ''
                        }
                      `}
                      role="radio"
                      aria-checked={data.buttSize === option.value}
                      aria-label={`选择${option.label}臀部尺寸`}
                    >
                      <CardBody className="p-0 relative overflow-hidden">
                        <div className="aspect-square bg-gradient-to-b from-default-100/30 to-transparent">
                          <Image
                            src={`/images/custom/${imageFile}.png`}
                            alt={option.label}
                            className="w-full h-full object-cover transition-transform hover:scale-105"
                            classNames={{
                              wrapper: 'w-full h-full'
                            }}
                          />
                        </div>
                        <Chip
                          color={data.buttSize === option.value ? 'primary' : 'default'}
                          variant={data.buttSize === option.value ? 'solid' : 'flat'}
                          className="absolute bottom-2 right-2 z-10"
                          size="sm"
                          classNames={{
                            content:
                              data.buttSize === option.value
                                ? 'text-white font-medium'
                                : 'text-foreground'
                          }}
                        >
                          {option.label}
                        </Chip>
                      </CardBody>
                    </Card>
                  )
                }}
              />
            </div>
          </Section>
        </>
      )}
    </div>
  )
}

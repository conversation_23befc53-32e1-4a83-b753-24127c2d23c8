import React, { useRef, useState } from 'react'
import { UploadCloud, User, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { apiService } from '@/api'
import { addToast } from '@heroui/react'
import { useTranslation } from 'react-i18next'

interface AvatarUploadProps {
  defaultAvatarUrl?: string
  onAvatarChange: (url: string) => void
}

export function AvatarUpload({ defaultAvatarUrl, onAvatarChange }: AvatarUploadProps) {
  const { t } = useTranslation('toast')
  const [avatarUrl, setAvatarUrl] = useState(defaultAvatarUrl || '')
  const [isUploading, setIsUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  // 处理文件选择
  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.startsWith('image/')) {
      addToast({
        title: t('avatar.select_image'),
        color: 'danger'
      })
      return
    }

    // 检查文件大小
    if (file.size > 1024 * 1024) {
      addToast({
        title: t('avatar.size_limit'),
        color: 'danger'
      })
      return
    }

    try {
      setIsUploading(true)

      // 使用API服务上传头像
      const data = await apiService.upload.uploadAvatar(file)

      setAvatarUrl(data.avatarUrl)
      onAvatarChange(data.avatarUrl)
    } catch (error) {
      console.error('上传头像失败:', error)
      // 如果上传失败，重置为原来的头像
      setAvatarUrl(defaultAvatarUrl || '')
    } finally {
      setIsUploading(false)
      // 重置文件输入，允许选择相同的文件
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  // 触发文件选择器
  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="flex flex-col items-center">
      <div className="relative mb-4 group">
        <div
          className="size-24 rounded-full overflow-hidden bg-gray-100 dark:bg-gray-800 flex items-center justify-center cursor-pointer"
          onClick={handleUploadClick}
        >
          {isUploading ? (
            <div className="flex flex-col items-center justify-center">
              <Loader2 className="size-10 text-primary animate-spin" />
            </div>
          ) : avatarUrl ? (
            <img src={avatarUrl} alt="头像" className="object-cover size-full" />
          ) : (
            <User className="size-12 text-gray-400" />
          )}
          {!isUploading && (
            <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-full">
              <UploadCloud className="size-8 text-white" />
            </div>
          )}
        </div>
      </div>

      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="image/*"
        className="hidden"
      />

      <Button
        type="button"
        variant="outline"
        onClick={handleUploadClick}
        disabled={isUploading}
        className="mb-2"
      >
        {isUploading ? (
          <span className="flex items-center">
            <Loader2 className="size-4 mr-2 animate-spin" />
            {t('avatar.uploading')}
          </span>
        ) : (
          t('avatar.upload_avatar')
        )}
      </Button>

      <p className="text-xs text-muted-foreground text-center">
        {t('avatar.support_format')}
        <br />
        {t('avatar.size_limit_note')}
      </p>
    </div>
  )
}
